import 'package:test/test.dart';
import 'package:shared/src/models/security/user_mfa_settings.dart';
import 'package:shared/src/models/security/trusted_device_models.dart';

void main() {
  group('UserMFASettings Simple Tests', () {
    test('UserMFASettings can be created with required parameters', () {
      final testDevice = TrustedDevice(
        id: 'device_123',
        userId: 'user_456',
        deviceFingerprint: 'fingerprint_123',
        deviceName: 'MacBook Pro',
        deviceType: DeviceType.desktop,
        operatingSystem: 'macOS',
        browserInfo: 'Chrome',
        ipAddress: '*************',
        status: DeviceStatus.active,
        trustLevel: DeviceTrustLevel.high,
        firstSeen: DateTime.parse('2025-01-15T10:00:00.000Z'),
        lastSeen: DateTime.parse('2025-01-15T12:00:00.000Z'),
        trustedAt: DateTime.parse('2025-01-15T10:00:00.000Z'),
        accessCount: 5,
        metadata: {},
      );
      
      final testMFASettings = UserMFASettings(
        userId: 'user_456',
        isEnabled: true,
        enabledMethods: [],
        trustedDevices: [testDevice],
        hasTOTP: true,
        hasSMS: true,
        hasRecoveryEmail: true,
        hasBackupCodes: true,
        backupCodesRemaining: 3,
        phoneNumber: '+1234567890',
        recoveryEmail: '<EMAIL>',
      );

      expect(testMFASettings.userId, equals('user_456'));
      expect(testMFASettings.isEnabled, isTrue);
      expect(testMFASettings.hasTOTP, isTrue);
      expect(testMFASettings.hasSMS, isTrue);
      expect(testMFASettings.hasRecoveryEmail, isTrue);
      expect(testMFASettings.hasBackupCodes, isTrue);
      expect(testMFASettings.backupCodesRemaining, equals(3));
      expect(testMFASettings.trustedDevices.length, equals(1));
    });
  });
}
