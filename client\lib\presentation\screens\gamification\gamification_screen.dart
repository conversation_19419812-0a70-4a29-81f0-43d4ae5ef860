import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/gamification/gamification_bloc.dart';
import '../../widgets/gamification/points_widget.dart';
import '../../widgets/gamification/achievement_widget.dart';
import '../../widgets/common/responsive_builder.dart';

class GamificationScreen extends StatefulWidget {
  const GamificationScreen({super.key});

  @override
  State<GamificationScreen> createState() => _GamificationScreenState();
}

class _GamificationScreenState extends State<GamificationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load initial data
    _loadGamificationData();
  }

  void _loadGamificationData() {
    final bloc = context.read<GamificationBloc>();
    bloc.add(const LoadUserPoints());
    bloc.add(const LoadUserStats());
    bloc.add(const LoadUserAchievements());
    bloc.add(const LoadLeaderboard());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gamification'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.emoji_events), text: 'Achievements'),
            Tab(icon: Icon(Icons.leaderboard), text: 'Leaderboard'),
            Tab(icon: Icon(Icons.redeem), text: 'Rewards'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadGamificationData,
          ),
        ],
      ),
      body: BlocConsumer<GamificationBloc, GamificationState>(
        listener: (context, state) {
          if (state is GamificationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is GamificationLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildDashboard(state),
              _buildAchievements(state),
              _buildLeaderboard(state),
              _buildRewards(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDashboard(GamificationState state) {
    return ResponsiveBuilder(
      mobile: (context) => _buildMobileDashboard(context, state),
      tablet: (context) => _buildTabletDashboard(context, state),
      desktop: (context) => _buildDesktopDashboard(context, state),
    );
  }

  Widget _buildMobileDashboard(BuildContext context, GamificationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced Points Widget
          const PointsWidget(
            showDetails: true,
            showLevelProgress: true,
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Recent Achievements
          _buildSectionHeader(context, 'Recent Achievements'),
          const SizedBox(height: AppConstants.smallPadding),
          const AchievementWidget(
            showAll: false,
            maxCount: 4,
            layoutStyle: AchievementLayoutStyle.grid,
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Quick Stats
          _buildQuickStats(context, state),
          const SizedBox(height: AppConstants.largePadding),

          // Action Buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildTabletDashboard(BuildContext context, GamificationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.largePadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top row with points and quick stats
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: const PointsWidget(
                  showDetails: true,
                  showLevelProgress: true,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildQuickStats(context, state),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Achievements section
          _buildSectionHeader(context, 'Achievements'),
          const SizedBox(height: AppConstants.smallPadding),
          const AchievementWidget(
            showAll: false,
            maxCount: 6,
            layoutStyle: AchievementLayoutStyle.grid,
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Action Buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildDesktopDashboard(BuildContext context, GamificationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.extraLargePadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top section with points, stats, and recent achievements
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    const PointsWidget(
                      showDetails: true,
                      showLevelProgress: true,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    _buildQuickStats(context, state),
                  ],
                ),
              ),
              const SizedBox(width: AppConstants.largePadding),
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionHeader(context, 'Recent Achievements'),
                    const SizedBox(height: AppConstants.smallPadding),
                    const AchievementWidget(
                      showAll: false,
                      maxCount: 8,
                      layoutStyle: AchievementLayoutStyle.grid,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Action Buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, GamificationState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            if (state is GamificationLoaded && state.userStats != null) ...[
              _buildStatRow('Current Streak', '${state.userStats!['current_streak'] ?? 0} days'),
              _buildStatRow('Longest Streak', '${state.userStats!['longest_streak'] ?? 0} days'),
              _buildStatRow('Total Achievements', '${state.userStats!['total_achievements'] ?? 0}'),
            ] else
              const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ResponsiveWrap(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _earnTestPoints(),
                  icon: const Icon(Icons.add),
                  label: const Text('Earn Points'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _unlockTestAchievement(),
                  icon: const Icon(Icons.emoji_events),
                  label: const Text('Unlock Achievement'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _viewAllAchievements(),
                  icon: const Icon(Icons.list),
                  label: const Text('View All'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _viewLeaderboard(),
                  icon: const Icon(Icons.leaderboard),
                  label: const Text('Leaderboard'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievements(GamificationState state) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Your Achievements',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: state is GamificationLoaded && state.achievements != null
                ? ListView.builder(
                    itemCount: state.achievements!.length,
                    itemBuilder: (context, index) {
                      final achievement = state.achievements![index];
                      return Card(
                        child: ListTile(
                          leading: Icon(
                            Icons.emoji_events,
                            color: _getAchievementColor(achievement.rarity.name),
                            size: 32,
                          ),
                          title: Text(achievement.name),
                          subtitle: Text(achievement.description),
                          trailing: Text(
                            '+${achievement.pointsAwarded} pts',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ),
                      );
                    },
                  )
                : const Center(child: Text('No achievements yet. Start completing tasks!')),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboard(GamificationState state) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Leaderboard',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: state is GamificationLoaded && state.leaderboard != null
                ? ListView.builder(
                    itemCount: state.leaderboard!.length,
                    itemBuilder: (context, index) {
                      final user = state.leaderboard![index];
                      return Card(
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: _getRankColor(index),
                            child: Text(
                              '${index + 1}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          title: Text(user['name'] ?? 'User ${index + 1}'),
                          subtitle: Text('Level ${user['level'] ?? 1}'),
                          trailing: Text(
                            '${user['points'] ?? 0} pts',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      );
                    },
                  )
                : const Center(child: Text('Loading leaderboard...')),
          ),
        ],
      ),
    );
  }

  Widget _buildRewards(GamificationState state) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Available Rewards',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              itemCount: 8, // Mock rewards
              itemBuilder: (context, index) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Icon(
                          _getRewardIcon(index),
                          size: 48,
                          color: Theme.of(context).primaryColor,
                        ),
                        Text(
                          'Reward ${index + 1}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${(index + 1) * 100} points',
                          style: const TextStyle(color: Colors.grey),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Reward system coming soon!')),
                            );
                          },
                          child: const Text('Redeem'),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointsStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(label, style: const TextStyle(color: Colors.grey)),
      ],
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onTap) {
    return Column(
      children: [
        IconButton(
          onPressed: onTap,
          icon: Icon(icon, size: 32),
          style: IconButton.styleFrom(
            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getAchievementColor(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'common': return Colors.grey;
      case 'uncommon': return Colors.green;
      case 'rare': return Colors.blue;
      case 'epic': return Colors.purple;
      case 'legendary': return Colors.orange;
      default: return Colors.grey;
    }
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 0: return Colors.amber;
      case 1: return Colors.grey;
      case 2: return Colors.brown;
      default: return Theme.of(context).primaryColor;
    }
  }

  IconData _getRewardIcon(int index) {
    final icons = [
      Icons.star,
      Icons.diamond,
      Icons.emoji_events,
      Icons.workspace_premium,
      Icons.card_giftcard,
      Icons.redeem,
      Icons.local_fire_department,
      Icons.military_tech,
    ];
    return icons[index % icons.length];
  }

  void _earnTestPoints() {
    // Add test points for demonstration
    context.read<GamificationBloc>().add(const EarnPoints(points: 50, reason: 'Test points'));
  }

  void _unlockTestAchievement() {
    context.read<GamificationBloc>().add(const UnlockAchievement(achievementId: 'test_achievement'));
  }

  void _viewAllAchievements() {
    Navigator.of(context).pushNamed('/achievements');
  }

  void _viewLeaderboard() {
    Navigator.of(context).pushNamed('/leaderboard');
  }
}