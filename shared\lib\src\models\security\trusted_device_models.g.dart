// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trusted_device_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TrustedDevice _$TrustedDeviceFromJson(Map<String, dynamic> json) =>
    TrustedDevice(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      deviceFingerprint: json['device_fingerprint'] as String,
      deviceName: json['device_name'] as String,
      deviceType: $enumDecode(_$DeviceTypeEnumMap, json['device_type']),
      deviceModel: json['device_model'] as String?,
      operatingSystem: json['operating_system'] as String?,
      browserInfo: json['browser_info'] as String?,
      ipAddress: json['ip_address'] as String,
      userAgent: json['user_agent'] as String?,
      status:
          $enumDecodeNullable(_$DeviceStatusEnumMap, json['status']) ??
          DeviceStatus.active,
      trustLevel:
          $enumDecodeNullable(_$DeviceTrustLevelEnumMap, json['trust_level']) ??
          DeviceTrustLevel.low,
      firstSeen: DateTime.parse(json['first_seen'] as String),
      lastSeen: DateTime.parse(json['last_seen'] as String),
      trustedAt: json['trusted_at'] == null
          ? null
          : DateTime.parse(json['trusted_at'] as String),
      expiresAt: json['expires_at'] == null
          ? null
          : DateTime.parse(json['expires_at'] as String),
      accessCount: (json['access_count'] as num?)?.toInt() ?? 1,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      revokedBy: json['revoked_by'] as String?,
      revocationReason: json['revocation_reason'] as String?,
      revokedAt: json['revoked_at'] == null
          ? null
          : DateTime.parse(json['revoked_at'] as String),
    );

Map<String, dynamic> _$TrustedDeviceToJson(TrustedDevice instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'device_fingerprint': instance.deviceFingerprint,
      'device_name': instance.deviceName,
      'device_type': _$DeviceTypeEnumMap[instance.deviceType]!,
      'device_model': instance.deviceModel,
      'operating_system': instance.operatingSystem,
      'browser_info': instance.browserInfo,
      'ip_address': instance.ipAddress,
      'user_agent': instance.userAgent,
      'status': _$DeviceStatusEnumMap[instance.status]!,
      'trust_level': _$DeviceTrustLevelEnumMap[instance.trustLevel]!,
      'first_seen': instance.firstSeen.toIso8601String(),
      'last_seen': instance.lastSeen.toIso8601String(),
      'trusted_at': instance.trustedAt?.toIso8601String(),
      'expires_at': instance.expiresAt?.toIso8601String(),
      'access_count': instance.accessCount,
      'metadata': instance.metadata,
      'revoked_by': instance.revokedBy,
      'revocation_reason': instance.revocationReason,
      'revoked_at': instance.revokedAt?.toIso8601String(),
    };

const _$DeviceTypeEnumMap = {
  DeviceType.mobile: 'mobile',
  DeviceType.desktop: 'desktop',
  DeviceType.tablet: 'tablet',
  DeviceType.unknown: 'unknown',
};

const _$DeviceStatusEnumMap = {
  DeviceStatus.active: 'active',
  DeviceStatus.revoked: 'revoked',
  DeviceStatus.expired: 'expired',
  DeviceStatus.suspicious: 'suspicious',
};

const _$DeviceTrustLevelEnumMap = {
  DeviceTrustLevel.low: 'low',
  DeviceTrustLevel.medium: 'medium',
  DeviceTrustLevel.high: 'high',
  DeviceTrustLevel.verified: 'verified',
};

DeviceRegistrationRequest _$DeviceRegistrationRequestFromJson(
  Map<String, dynamic> json,
) => DeviceRegistrationRequest(
  deviceFingerprint: json['device_fingerprint'] as String,
  deviceName: json['device_name'] as String,
  deviceType: $enumDecode(_$DeviceTypeEnumMap, json['device_type']),
  deviceModel: json['device_model'] as String?,
  operatingSystem: json['operating_system'] as String?,
  browserInfo: json['browser_info'] as String?,
  ipAddress: json['ip_address'] as String,
  userAgent: json['user_agent'] as String?,
  trustImmediately: json['trust_immediately'] as bool? ?? false,
  trustDurationHours: (json['trust_duration_hours'] as num?)?.toInt(),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$DeviceRegistrationRequestToJson(
  DeviceRegistrationRequest instance,
) => <String, dynamic>{
  'device_fingerprint': instance.deviceFingerprint,
  'device_name': instance.deviceName,
  'device_type': _$DeviceTypeEnumMap[instance.deviceType]!,
  'device_model': instance.deviceModel,
  'operating_system': instance.operatingSystem,
  'browser_info': instance.browserInfo,
  'ip_address': instance.ipAddress,
  'user_agent': instance.userAgent,
  'trust_immediately': instance.trustImmediately,
  'trust_duration_hours': instance.trustDurationHours,
  'metadata': instance.metadata,
};

DeviceVerificationResult _$DeviceVerificationResultFromJson(
  Map<String, dynamic> json,
) => DeviceVerificationResult(
  isTrusted: json['is_trusted'] as bool,
  requiresMFA: json['requires_mfa'] as bool,
  isNewDevice: json['is_new_device'] as bool,
  isSuspicious: json['is_suspicious'] as bool? ?? false,
  device: json['device'] == null
      ? null
      : TrustedDevice.fromJson(json['device'] as Map<String, dynamic>),
  warningMessage: json['warning_message'] as String?,
  riskFactors:
      (json['risk_factors'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  verificationTimestamp: json['verification_timestamp'] == null
      ? null
      : DateTime.parse(json['verification_timestamp'] as String),
);

Map<String, dynamic> _$DeviceVerificationResultToJson(
  DeviceVerificationResult instance,
) => <String, dynamic>{
  'is_trusted': instance.isTrusted,
  'requires_mfa': instance.requiresMFA,
  'is_new_device': instance.isNewDevice,
  'is_suspicious': instance.isSuspicious,
  'device': instance.device,
  'warning_message': instance.warningMessage,
  'risk_factors': instance.riskFactors,
  'metadata': instance.metadata,
  'verification_timestamp': instance.verificationTimestamp.toIso8601String(),
};

TrustedDeviceConfig _$TrustedDeviceConfigFromJson(
  Map<String, dynamic> json,
) => TrustedDeviceConfig(
  defaultTrustDurationDays:
      (json['default_trust_duration_days'] as num?)?.toInt() ?? 30,
  maxTrustedDevices: (json['max_trusted_devices'] as num?)?.toInt() ?? 10,
  requireMFAForNewDevices: json['require_mfa_for_new_devices'] as bool? ?? true,
  autoTrustAfterMFA: json['auto_trust_after_mfa'] as bool? ?? true,
  suspiciousDeviceTimeoutHours:
      (json['suspicious_device_timeout_hours'] as num?)?.toInt() ?? 24,
  maxFailedAttempts: (json['max_failed_attempts'] as num?)?.toInt() ?? 5,
  allowedCountries:
      (json['allowed_countries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  enableLocationTracking: json['enable_location_tracking'] as bool? ?? false,
);

Map<String, dynamic> _$TrustedDeviceConfigToJson(
  TrustedDeviceConfig instance,
) => <String, dynamic>{
  'default_trust_duration_days': instance.defaultTrustDurationDays,
  'max_trusted_devices': instance.maxTrustedDevices,
  'require_mfa_for_new_devices': instance.requireMFAForNewDevices,
  'auto_trust_after_mfa': instance.autoTrustAfterMFA,
  'suspicious_device_timeout_hours': instance.suspiciousDeviceTimeoutHours,
  'max_failed_attempts': instance.maxFailedAttempts,
  'allowed_countries': instance.allowedCountries,
  'enable_location_tracking': instance.enableLocationTracking,
};

DeviceStats _$DeviceStatsFromJson(Map<String, dynamic> json) => DeviceStats(
  totalDevices: (json['total_devices'] as num).toInt(),
  activeDevices: (json['active_devices'] as num).toInt(),
  trustedDevices: (json['trusted_devices'] as num).toInt(),
  suspiciousDevices: (json['suspicious_devices'] as num).toInt(),
  revokedDevices: (json['revoked_devices'] as num).toInt(),
  expiredDevices: (json['expired_devices'] as num).toInt(),
  lastActivity: json['last_activity'] == null
      ? null
      : DateTime.parse(json['last_activity'] as String),
  securityScore: (json['security_score'] as num).toDouble(),
  needsCleanup: json['needs_cleanup'] as bool,
);

Map<String, dynamic> _$DeviceStatsToJson(DeviceStats instance) =>
    <String, dynamic>{
      'total_devices': instance.totalDevices,
      'active_devices': instance.activeDevices,
      'trusted_devices': instance.trustedDevices,
      'suspicious_devices': instance.suspiciousDevices,
      'revoked_devices': instance.revokedDevices,
      'expired_devices': instance.expiredDevices,
      'last_activity': instance.lastActivity?.toIso8601String(),
      'security_score': instance.securityScore,
      'needs_cleanup': instance.needsCleanup,
    };

TrustDeviceRequest _$TrustDeviceRequestFromJson(Map<String, dynamic> json) =>
    TrustDeviceRequest(
      trustDurationHours: (json['trust_duration_hours'] as num?)?.toInt(),
      reason: json['reason'] as String? ?? 'Manual trust',
      trustedBy: json['trusted_by'] as String?,
    );

Map<String, dynamic> _$TrustDeviceRequestToJson(TrustDeviceRequest instance) =>
    <String, dynamic>{
      'trust_duration_hours': instance.trustDurationHours,
      'reason': instance.reason,
      'trusted_by': instance.trustedBy,
    };

RevokeDeviceRequest _$RevokeDeviceRequestFromJson(Map<String, dynamic> json) =>
    RevokeDeviceRequest(
      reason: json['reason'] as String? ?? 'Manual revocation',
      revokedBy: json['revoked_by'] as String?,
    );

Map<String, dynamic> _$RevokeDeviceRequestToJson(
  RevokeDeviceRequest instance,
) => <String, dynamic>{
  'reason': instance.reason,
  'revoked_by': instance.revokedBy,
};

DeviceVerificationRequest _$DeviceVerificationRequestFromJson(
  Map<String, dynamic> json,
) => DeviceVerificationRequest(
  deviceFingerprint: json['device_fingerprint'] as String,
  ipAddress: json['ip_address'] as String?,
  userAgent: json['user_agent'] as String?,
);

Map<String, dynamic> _$DeviceVerificationRequestToJson(
  DeviceVerificationRequest instance,
) => <String, dynamic>{
  'device_fingerprint': instance.deviceFingerprint,
  'ip_address': instance.ipAddress,
  'user_agent': instance.userAgent,
};

TrustedDeviceResponse _$TrustedDeviceResponseFromJson(
  Map<String, dynamic> json,
) => TrustedDeviceResponse(
  data: TrustedDevice.fromJson(json['data'] as Map<String, dynamic>),
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$TrustedDeviceResponseToJson(
  TrustedDeviceResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

DeviceVerificationResponse _$DeviceVerificationResponseFromJson(
  Map<String, dynamic> json,
) => DeviceVerificationResponse(
  data: DeviceVerificationResult.fromJson(json['data'] as Map<String, dynamic>),
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$DeviceVerificationResponseToJson(
  DeviceVerificationResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

DeviceListResponse _$DeviceListResponseFromJson(Map<String, dynamic> json) =>
    DeviceListResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => TrustedDevice.fromJson(e as Map<String, dynamic>))
          .toList(),
      message: json['message'] as String,
      success: json['success'] as bool? ?? true,
      stats: json['stats'] == null
          ? null
          : DeviceStats.fromJson(json['stats'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DeviceListResponseToJson(DeviceListResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'message': instance.message,
      'success': instance.success,
      'stats': instance.stats,
    };

DeviceStatsResponse _$DeviceStatsResponseFromJson(Map<String, dynamic> json) =>
    DeviceStatsResponse(
      data: DeviceStats.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String,
      success: json['success'] as bool? ?? true,
    );

Map<String, dynamic> _$DeviceStatsResponseToJson(
  DeviceStatsResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};
