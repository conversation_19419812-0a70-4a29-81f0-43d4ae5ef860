import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('OrganizationSecurityPolicy Tests', () {
    late OrganizationSecurityPolicy testPolicy;
    
    setUp(() {
      testPolicy = OrganizationSecurityPolicy(
        id: 'policy_123',
        organizationId: 'org_456',
        passwordPolicy: const PasswordPolicy(
          minLength: 12,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSymbols: true,
          maxAgeDays: 60,
          historyCount: 8,
        ),
        mfaPolicy: const MFAPolicy(
          required: true,
          requiredForAdmins: true,
          allowedMethods: ['totp', 'sms', 'email'],
          gracePeriodDays: 5,
        ),
        sessionPolicy: const SessionPolicy(
          idleTimeoutMinutes: 20,
          absoluteTimeoutHours: 6,
          concurrentSessionsLimit: 3,
          requireDeviceTrust: true,
        ),
        accessPolicy: const AccessPolicy(
          ipWhitelistEnabled: true,
          ipWhitelist: ['***********/24', '10.0.0.0/8'],
          geoRestrictionsEnabled: true,
          allowedCountries: ['US', 'CA', 'GB'],
          loginAttemptLimit: 3,
          lockoutDurationMinutes: 30,
        ),
        auditPolicy: const AuditPolicy(
          retentionDays: 365,
          logAllActions: true,
          alertOnSuspicious: true,
          exportEnabled: true,
        ),
        complianceSettings: {
          'gdpr_enabled': true,
          'soc2_compliance': true,
          'data_residency': 'US',
        },
        isActive: true,
        createdAt: DateTime.parse('2025-01-10T09:00:00.000Z'),
        updatedAt: DateTime.parse('2025-01-15T14:00:00.000Z'),
        createdBy: 'admin_123',
      );
    });

    test('should create valid OrganizationSecurityPolicy instance', () {
      expect(testPolicy.id, equals('policy_123'));
      expect(testPolicy.organizationId, equals('org_456'));
      expect(testPolicy.isActive, isTrue);
      expect(testPolicy.passwordPolicy.minLength, equals(12));
      expect(testPolicy.mfaPolicy.required, isTrue);
      expect(testPolicy.sessionPolicy.requireDeviceTrust, isTrue);
      expect(testPolicy.accessPolicy.ipWhitelistEnabled, isTrue);
      expect(testPolicy.auditPolicy.retentionDays, equals(365));
    });

    test('should create empty OrganizationSecurityPolicy for testing', () {
      final empty = OrganizationSecurityPolicy.empty();
      expect(empty.id, isEmpty);
      expect(empty.organizationId, isEmpty);
      expect(empty.isActive, isTrue);
      expect(empty.passwordPolicy, equals(PasswordPolicy.defaultPolicy));
      expect(empty.mfaPolicy, equals(MFAPolicy.defaultPolicy));
      expect(empty.sessionPolicy, equals(SessionPolicy.defaultPolicy));
      expect(empty.accessPolicy, equals(AccessPolicy.defaultPolicy));
      expect(empty.auditPolicy, equals(AuditPolicy.defaultPolicy));
      expect(empty.complianceSettings, isEmpty);
    });

    group('PasswordPolicy Tests', () {
      test('should create valid PasswordPolicy instance', () {
        final policy = testPolicy.passwordPolicy;
        expect(policy.minLength, equals(12));
        expect(policy.requireUppercase, isTrue);
        expect(policy.requireLowercase, isTrue);
        expect(policy.requireNumbers, isTrue);
        expect(policy.requireSymbols, isTrue);
        expect(policy.maxAgeDays, equals(60));
        expect(policy.historyCount, equals(8));
      });

      test('should have correct default policy', () {
        final defaultPolicy = PasswordPolicy.defaultPolicy;
        expect(defaultPolicy.minLength, equals(8));
        expect(defaultPolicy.requireUppercase, isTrue);
        expect(defaultPolicy.requireLowercase, isTrue);
        expect(defaultPolicy.requireNumbers, isTrue);
        expect(defaultPolicy.requireSymbols, isFalse);
        expect(defaultPolicy.maxAgeDays, equals(90));
        expect(defaultPolicy.historyCount, equals(5));
      });

      test('should serialize and deserialize correctly', () {
        final policy = testPolicy.passwordPolicy;
        final json = policy.toJson();
        final deserialized = PasswordPolicy.fromJson(json);
        expect(deserialized, equals(policy));
      });

      test('should handle missing JSON fields with defaults', () {
        final json = <String, dynamic>{};
        final policy = PasswordPolicy.fromJson(json);
        expect(policy.minLength, equals(8));
        expect(policy.requireUppercase, isTrue);
        expect(policy.maxAgeDays, equals(90));
      });
    });

    group('MFAPolicy Tests', () {
      test('should create valid MFAPolicy instance', () {
        final policy = testPolicy.mfaPolicy;
        expect(policy.required, isTrue);
        expect(policy.requiredForAdmins, isTrue);
        expect(policy.allowedMethods, equals(['totp', 'sms', 'email']));
        expect(policy.gracePeriodDays, equals(5));
      });

      test('should have correct default policy', () {
        final defaultPolicy = MFAPolicy.defaultPolicy;
        expect(defaultPolicy.required, isFalse);
        expect(defaultPolicy.requiredForAdmins, isTrue);
        expect(defaultPolicy.allowedMethods, equals(['totp', 'sms', 'backup_codes']));
        expect(defaultPolicy.gracePeriodDays, equals(7));
      });

      test('should serialize and deserialize correctly', () {
        final policy = testPolicy.mfaPolicy;
        final json = policy.toJson();
        final deserialized = MFAPolicy.fromJson(json);
        expect(deserialized, equals(policy));
      });
    });

    group('SessionPolicy Tests', () {
      test('should create valid SessionPolicy instance', () {
        final policy = testPolicy.sessionPolicy;
        expect(policy.idleTimeoutMinutes, equals(20));
        expect(policy.absoluteTimeoutHours, equals(6));
        expect(policy.concurrentSessionsLimit, equals(3));
        expect(policy.requireDeviceTrust, isTrue);
      });

      test('should have correct default policy', () {
        final defaultPolicy = SessionPolicy.defaultPolicy;
        expect(defaultPolicy.idleTimeoutMinutes, equals(30));
        expect(defaultPolicy.absoluteTimeoutHours, equals(8));
        expect(defaultPolicy.concurrentSessionsLimit, equals(5));
        expect(defaultPolicy.requireDeviceTrust, isFalse);
      });

      test('should serialize and deserialize correctly', () {
        final policy = testPolicy.sessionPolicy;
        final json = policy.toJson();
        final deserialized = SessionPolicy.fromJson(json);
        expect(deserialized, equals(policy));
      });
    });

    group('AccessPolicy Tests', () {
      test('should create valid AccessPolicy instance', () {
        final policy = testPolicy.accessPolicy;
        expect(policy.ipWhitelistEnabled, isTrue);
        expect(policy.ipWhitelist, equals(['***********/24', '10.0.0.0/8']));
        expect(policy.geoRestrictionsEnabled, isTrue);
        expect(policy.allowedCountries, equals(['US', 'CA', 'GB']));
        expect(policy.loginAttemptLimit, equals(3));
        expect(policy.lockoutDurationMinutes, equals(30));
      });

      test('should have correct default policy', () {
        final defaultPolicy = AccessPolicy.defaultPolicy;
        expect(defaultPolicy.ipWhitelistEnabled, isFalse);
        expect(defaultPolicy.ipWhitelist, isEmpty);
        expect(defaultPolicy.geoRestrictionsEnabled, isFalse);
        expect(defaultPolicy.allowedCountries, isEmpty);
        expect(defaultPolicy.loginAttemptLimit, equals(5));
        expect(defaultPolicy.lockoutDurationMinutes, equals(15));
      });

      test('should serialize and deserialize correctly', () {
        final policy = testPolicy.accessPolicy;
        final json = policy.toJson();
        final deserialized = AccessPolicy.fromJson(json);
        expect(deserialized, equals(policy));
      });
    });

    group('AuditPolicy Tests', () {
      test('should create valid AuditPolicy instance', () {
        final policy = testPolicy.auditPolicy;
        expect(policy.retentionDays, equals(365));
        expect(policy.logAllActions, isTrue);
        expect(policy.alertOnSuspicious, isTrue);
        expect(policy.exportEnabled, isTrue);
      });

      test('should have correct default policy', () {
        final defaultPolicy = AuditPolicy.defaultPolicy;
        expect(defaultPolicy.retentionDays, equals(90));
        expect(defaultPolicy.logAllActions, isTrue);
        expect(defaultPolicy.alertOnSuspicious, isTrue);
        expect(defaultPolicy.exportEnabled, isTrue);
      });

      test('should serialize and deserialize correctly', () {
        final policy = testPolicy.auditPolicy;
        final json = policy.toJson();
        final deserialized = AuditPolicy.fromJson(json);
        expect(deserialized, equals(policy));
      });
    });

    group('Policy Validation Methods', () {
      test('should validate MFA requirements correctly', () {
        expect(testPolicy.isMFARequired('user'), isTrue); // MFA required for all
        expect(testPolicy.isMFARequired('admin'), isTrue);
        expect(testPolicy.isMFARequired('owner'), isTrue);

        final adminOnlyPolicy = testPolicy.copyWith(
          mfaPolicy: const MFAPolicy(
            required: false,
            requiredForAdmins: true,
            allowedMethods: ['totp'],
            gracePeriodDays: 7,
          ),
        );

        expect(adminOnlyPolicy.isMFARequired('user'), isFalse);
        expect(adminOnlyPolicy.isMFARequired('admin'), isTrue);
        expect(adminOnlyPolicy.isMFARequired('owner'), isTrue);
      });

      test('should validate IP whitelist correctly', () {
        expect(testPolicy.isIPAllowed('*************'), isTrue); // In whitelist
        expect(testPolicy.isIPAllowed('***********'), isFalse); // Not in whitelist

        final noWhitelistPolicy = testPolicy.copyWith(
          accessPolicy: testPolicy.accessPolicy.copyWith(ipWhitelistEnabled: false),
        );
        expect(noWhitelistPolicy.isIPAllowed('***********'), isTrue); // Whitelist disabled
      });

      test('should validate country restrictions correctly', () {
        expect(testPolicy.isCountryAllowed('US'), isTrue); // Allowed country
        expect(testPolicy.isCountryAllowed('CN'), isFalse); // Not allowed

        final noGeoRestrictionsPolicy = testPolicy.copyWith(
          accessPolicy: testPolicy.accessPolicy.copyWith(geoRestrictionsEnabled: false),
        );
        expect(noGeoRestrictionsPolicy.isCountryAllowed('CN'), isTrue); // Restrictions disabled
      });
    });

    group('Timeout Calculations', () {
      test('should calculate session timeout correctly', () {
        expect(testPolicy.sessionTimeoutMs, equals(20 * 60 * 1000)); // 20 minutes in ms
      });

      test('should calculate absolute session timeout correctly', () {
        expect(testPolicy.absoluteSessionTimeoutMs, equals(6 * 60 * 60 * 1000)); // 6 hours in ms
      });
    });

    test('should serialize to JSON correctly', () {
      final json = testPolicy.toJson();
      expect(json['id'], equals('policy_123'));
      expect(json['organization_id'], equals('org_456'));
      expect(json['is_active'], isTrue);
      expect(json['password_policy'], isA<Map<String, dynamic>>());
      expect(json['mfa_policy'], isA<Map<String, dynamic>>());
      expect(json['session_policy'], isA<Map<String, dynamic>>());
      expect(json['access_policy'], isA<Map<String, dynamic>>());
      expect(json['audit_policy'], isA<Map<String, dynamic>>());
      expect(json['compliance_settings'], isA<Map<String, dynamic>>());
      expect(json['created_at'], isA<String>());
      expect(json['updated_at'], isA<String>());
      expect(json['created_by'], equals('admin_123'));
    });

    test('should deserialize from JSON correctly', () {
      final json = testPolicy.toJson();
      final deserialized = OrganizationSecurityPolicy.fromJson(json);
      
      expect(deserialized.id, equals(testPolicy.id));
      expect(deserialized.organizationId, equals(testPolicy.organizationId));
      expect(deserialized.isActive, equals(testPolicy.isActive));
      expect(deserialized.passwordPolicy, equals(testPolicy.passwordPolicy));
      expect(deserialized.mfaPolicy, equals(testPolicy.mfaPolicy));
      expect(deserialized.sessionPolicy, equals(testPolicy.sessionPolicy));
      expect(deserialized.accessPolicy, equals(testPolicy.accessPolicy));
      expect(deserialized.auditPolicy, equals(testPolicy.auditPolicy));
      expect(deserialized.complianceSettings, equals(testPolicy.complianceSettings));
    });

    test('should handle JSON deserialization with missing policy objects', () {
      final json = {
        'id': 'test_id',
        'organization_id': 'org_123',
        'password_policy': null,
        'mfa_policy': null,
        'session_policy': null,
        'access_policy': null,
        'audit_policy': null,
        'compliance_settings': null,
        'is_active': true,
        'created_at': '2025-01-15T10:00:00.000Z',
        'updated_at': '2025-01-15T10:00:00.000Z',
        'created_by': null,
      };
      
      final policy = OrganizationSecurityPolicy.fromJson(json);
      expect(policy.passwordPolicy, equals(PasswordPolicy.defaultPolicy));
      expect(policy.mfaPolicy, equals(MFAPolicy.defaultPolicy));
      expect(policy.sessionPolicy, equals(SessionPolicy.defaultPolicy));
      expect(policy.accessPolicy, equals(AccessPolicy.defaultPolicy));
      expect(policy.auditPolicy, equals(AuditPolicy.defaultPolicy));
      expect(policy.complianceSettings, isEmpty);
      expect(policy.createdBy, isNull);
    });

    test('should create copy with updated fields', () {
      final updatedPasswordPolicy = const PasswordPolicy(
        minLength: 16,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSymbols: true,
        maxAgeDays: 30,
        historyCount: 12,
      );
      
      final updated = testPolicy.copyWith(
        isActive: false,
        passwordPolicy: updatedPasswordPolicy,
        complianceSettings: {'gdpr_enabled': false},
      );
      
      expect(updated.isActive, isFalse);
      expect(updated.passwordPolicy.minLength, equals(16));
      expect(updated.passwordPolicy.maxAgeDays, equals(30));
      expect(updated.complianceSettings['gdpr_enabled'], isFalse);
      expect(updated.id, equals(testPolicy.id)); // Unchanged
      expect(updated.organizationId, equals(testPolicy.organizationId)); // Unchanged
    });

    test('should maintain equality for identical instances', () {
      final json = testPolicy.toJson();
      final identical = OrganizationSecurityPolicy.fromJson(json);
      expect(testPolicy, equals(identical));
    });

    test('should not be equal for different instances', () {
      final different = testPolicy.copyWith(id: 'different_id');
      expect(testPolicy, isNot(equals(different)));
    });

    group('Edge Cases', () {
      test('should handle empty IP whitelist', () {
        final emptyWhitelistPolicy = testPolicy.copyWith(
          accessPolicy: testPolicy.accessPolicy.copyWith(ipWhitelist: []),
        );
        expect(emptyWhitelistPolicy.isIPAllowed('*************'), isFalse);
      });

      test('should handle empty allowed countries', () {
        final noCountriesPolicy = testPolicy.copyWith(
          accessPolicy: testPolicy.accessPolicy.copyWith(allowedCountries: []),
        );
        expect(noCountriesPolicy.isCountryAllowed('US'), isFalse);
      });

      test('should handle complex compliance settings', () {
        final complexSettings = {
          'gdpr': {
            'enabled': true,
            'data_retention_days': 365,
            'right_to_erasure': true,
          },
          'soc2': {
            'type': 'Type II',
            'certification_date': '2025-01-15',
          },
          'custom_requirements': ['encryption_at_rest', 'audit_logging'],
        };
        
        final policy = testPolicy.copyWith(complianceSettings: complexSettings);
        final json = policy.toJson();
        final deserialized = OrganizationSecurityPolicy.fromJson(json);
        
        expect(deserialized.complianceSettings, equals(complexSettings));
      });

      test('should handle extreme timeout values', () {
        final extremePolicy = testPolicy.copyWith(
          sessionPolicy: const SessionPolicy(
            idleTimeoutMinutes: 1,
            absoluteTimeoutHours: 24,
            concurrentSessionsLimit: 1,
            requireDeviceTrust: true,
          ),
        );
        
        expect(extremePolicy.sessionTimeoutMs, equals(60 * 1000)); // 1 minute
        expect(extremePolicy.absoluteSessionTimeoutMs, equals(24 * 60 * 60 * 1000)); // 24 hours
      });
    });

    group('AccessPolicy copyWith method', () {
      test('should create copy of AccessPolicy with updated fields', () {
        final original = testPolicy.accessPolicy;
        final updated = original.copyWith(
          ipWhitelistEnabled: false,
          loginAttemptLimit: 10,
        );
        
        expect(updated.ipWhitelistEnabled, isFalse);
        expect(updated.loginAttemptLimit, equals(10));
        expect(updated.ipWhitelist, equals(original.ipWhitelist)); // Unchanged
        expect(updated.geoRestrictionsEnabled, equals(original.geoRestrictionsEnabled)); // Unchanged
      });
    });
  });
}