/// Generic API response wrapper
class ApiResponse<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  ApiResponse._({
    this.data,
    this.error,
    required this.isSuccess,
  });

  /// Create successful response
  factory ApiResponse.success(T data) => ApiResponse._(
        data: data,
        isSuccess: true,
      );

  /// Create error response
  factory ApiResponse.error(String error) => ApiResponse._(
        error: error,
        isSuccess: false,
      );

  /// Check if response has data
  bool get hasData => data != null;

  /// Check if response has error
  bool get hasError => error != null;

  @override
  String toString() {
    if (isSuccess) {
      return 'ApiResponse.success(data: $data)';
    } else {
      return 'ApiResponse.error(error: $error)';
    }
  }
}