#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// Production configuration validation script
/// Validates all required environment variables and service connections
void main(List<String> args) async {
  print('🔍 Validating production configuration...\n');
  
  final validator = ProductionConfigValidator();
  
  try {
    await validator.validateAll();
    print('\n✅ All production configuration checks passed!');
    print('🚀 Ready for deployment.');
    exit(0);
  } catch (e) {
    print('\n❌ Production configuration validation failed:');
    print('   $e');
    print('\n🔧 Please fix the configuration issues before deploying.');
    exit(1);
  }
}

class ProductionConfigValidator {
  final Map<String, String> _env = Platform.environment;
  final List<String> _errors = [];
  final List<String> _warnings = [];

  /// Validate all configuration aspects
  Future<void> validateAll() async {
    print('📋 Checking environment variables...');
    _validateEnvironmentVariables();
    
    print('🔐 Validating security configuration...');
    _validateSecurityConfig();
    
    print('📧 Validating SendGrid configuration...');
    await _validateSendGridConfig();
    
    print('📱 Validating Twilio configuration...');
    await _validateTwilioConfig();
    
    print('🗄️ Validating database configuration...');
    _validateDatabaseConfig();
    
    print('⚡ Validating Redis configuration...');
    _validateRedisConfig();
    
    print('📊 Validating monitoring configuration...');
    _validateMonitoringConfig();
    
    // Print warnings
    if (_warnings.isNotEmpty) {
      print('\n⚠️ Warnings:');
      for (final warning in _warnings) {
        print('   • $warning');
      }
    }
    
    // Throw error if any critical issues found
    if (_errors.isNotEmpty) {
      throw Exception(_errors.join('\n   '));
    }
  }

  /// Validate required environment variables
  void _validateEnvironmentVariables() {
    final required = [
      'DART_ENV',
      'API_BASE_URL',
      'WEB_BASE_URL',
      'DATABASE_URL',
      'JWT_SECRET',
      'ENCRYPTION_KEY',
    ];
    
    for (final key in required) {
      if (!_env.containsKey(key) || _env[key]!.isEmpty) {
        _errors.add('Missing required environment variable: $key');
      }
    }
    
    // Check environment is set to production
    if (_env['DART_ENV'] != 'production') {
      _warnings.add('DART_ENV is not set to "production"');
    }
    
    print('   ✓ Environment variables checked');
  }

  /// Validate security configuration
  void _validateSecurityConfig() {
    final jwtSecret = _env['JWT_SECRET'] ?? '';
    final encryptionKey = _env['ENCRYPTION_KEY'] ?? '';
    final apiKey = _env['API_KEY'] ?? '';
    
    // Check secret lengths
    if (jwtSecret.length < 32) {
      _errors.add('JWT_SECRET must be at least 32 characters long');
    }
    
    if (encryptionKey.length < 32) {
      _errors.add('ENCRYPTION_KEY must be at least 32 characters long');
    }
    
    if (apiKey.length < 32) {
      _errors.add('API_KEY must be at least 32 characters long');
    }
    
    // Check for common weak secrets
    final weakSecrets = ['password', '123456', 'secret', 'admin'];
    for (final weak in weakSecrets) {
      if (jwtSecret.toLowerCase().contains(weak) ||
          encryptionKey.toLowerCase().contains(weak)) {
        _errors.add('Security secrets contain weak patterns');
        break;
      }
    }
    
    // Check SSL configuration
    if (_env['SSL_ENABLED'] != 'true') {
      _warnings.add('SSL is not enabled - recommended for production');
    }
    
    print('   ✓ Security configuration validated');
  }

  /// Validate SendGrid configuration
  Future<void> _validateSendGridConfig() async {
    final emailEnabled = _env['EMAIL_ENABLED'] == 'true';
    
    if (!emailEnabled) {
      _warnings.add('Email service is disabled');
      print('   ⚠️ Email service disabled - skipping SendGrid validation');
      return;
    }
    
    final apiKey = _env['SENDGRID_API_KEY'] ?? '';
    final fromEmail = _env['SENDGRID_FROM_EMAIL'] ?? '';
    
    if (apiKey.isEmpty) {
      _errors.add('SENDGRID_API_KEY is required when email is enabled');
      return;
    }
    
    if (!apiKey.startsWith('SG.')) {
      _errors.add('Invalid SendGrid API key format (should start with "SG.")');
      return;
    }
    
    if (fromEmail.isEmpty || !_isValidEmail(fromEmail)) {
      _errors.add('Valid SENDGRID_FROM_EMAIL is required');
    }
    
    // Test SendGrid connection
    try {
      final response = await http.get(
        Uri.parse('https://api.sendgrid.com/v3/user/profile'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        print('   ✓ SendGrid connection successful');
      } else {
        _errors.add('SendGrid API key validation failed (HTTP ${response.statusCode})');
      }
    } catch (e) {
      _errors.add('Failed to connect to SendGrid: $e');
    }
    
    // Check template configuration
    final templates = [
      'SENDGRID_TEMPLATE_WELCOME',
      'SENDGRID_TEMPLATE_VERIFICATION',
      'SENDGRID_TEMPLATE_PASSWORD_RESET',
      'SENDGRID_TEMPLATE_MFA_CODE',
    ];
    
    for (final template in templates) {
      final value = _env[template] ?? '';
      if (value.isEmpty) {
        _warnings.add('$template is not configured');
      } else if (!value.startsWith('d-')) {
        _warnings.add('$template should start with "d-"');
      }
    }
  }

  /// Validate Twilio configuration
  Future<void> _validateTwilioConfig() async {
    final smsEnabled = _env['SMS_ENABLED'] == 'true';
    
    if (!smsEnabled) {
      _warnings.add('SMS service is disabled');
      print('   ⚠️ SMS service disabled - skipping Twilio validation');
      return;
    }
    
    final accountSid = _env['TWILIO_ACCOUNT_SID'] ?? '';
    final authToken = _env['TWILIO_AUTH_TOKEN'] ?? '';
    final fromPhone = _env['TWILIO_FROM_PHONE'] ?? '';
    
    if (accountSid.isEmpty) {
      _errors.add('TWILIO_ACCOUNT_SID is required when SMS is enabled');
      return;
    }
    
    if (!accountSid.startsWith('AC')) {
      _errors.add('Invalid Twilio Account SID format (should start with "AC")');
      return;
    }
    
    if (authToken.isEmpty) {
      _errors.add('TWILIO_AUTH_TOKEN is required when SMS is enabled');
      return;
    }
    
    if (fromPhone.isEmpty || !_isValidPhoneNumber(fromPhone)) {
      _errors.add('Valid TWILIO_FROM_PHONE is required (format: +**********)');
    }
    
    // Test Twilio connection
    try {
      final credentials = base64Encode(utf8.encode('$accountSid:$authToken'));
      final response = await http.get(
        Uri.parse('https://api.twilio.com/2010-04-01/Accounts/$accountSid.json'),
        headers: {
          'Authorization': 'Basic $credentials',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        print('   ✓ Twilio connection successful');
      } else {
        _errors.add('Twilio credentials validation failed (HTTP ${response.statusCode})');
      }
    } catch (e) {
      _errors.add('Failed to connect to Twilio: $e');
    }
  }

  /// Validate database configuration
  void _validateDatabaseConfig() {
    final databaseUrl = _env['DATABASE_URL'] ?? '';
    
    if (databaseUrl.isEmpty) {
      _errors.add('DATABASE_URL is required');
      return;
    }
    
    if (!databaseUrl.startsWith('postgresql://')) {
      _errors.add('DATABASE_URL must be a PostgreSQL connection string');
    }
    
    // Check SSL mode for production
    final sslMode = _env['POSTGRES_SSL_MODE'] ?? '';
    if (sslMode != 'require' && sslMode != 'verify-full') {
      _warnings.add('Consider using SSL for database connections in production');
    }
    
    // Check connection pool settings
    final maxConnections = int.tryParse(_env['DB_POOL_MAX_CONNECTIONS'] ?? '20') ?? 20;
    if (maxConnections < 10) {
      _warnings.add('Consider increasing DB_POOL_MAX_CONNECTIONS for production');
    }
    
    print('   ✓ Database configuration validated');
  }

  /// Validate Redis configuration
  void _validateRedisConfig() {
    final redisUrl = _env['REDIS_URL'] ?? '';
    
    if (redisUrl.isEmpty) {
      _warnings.add('REDIS_URL is not configured - caching will be disabled');
      return;
    }
    
    if (!redisUrl.startsWith('redis://') && !redisUrl.startsWith('rediss://')) {
      _errors.add('REDIS_URL must be a valid Redis connection string');
    }
    
    // Check TLS for production
    if (_env['REDIS_TLS'] != 'true' && redisUrl.startsWith('redis://')) {
      _warnings.add('Consider using TLS for Redis connections in production');
    }
    
    print('   ✓ Redis configuration validated');
  }

  /// Validate monitoring configuration
  void _validateMonitoringConfig() {
    final sentryDsn = _env['SENTRY_DSN'] ?? '';
    final datadogKey = _env['DATADOG_API_KEY'] ?? '';
    
    if (sentryDsn.isEmpty && datadogKey.isEmpty) {
      _warnings.add('No error monitoring service configured (Sentry, Datadog, etc.)');
    }
    
    if (_env['PERFORMANCE_MONITORING_ENABLED'] != 'true') {
      _warnings.add('Performance monitoring is disabled');
    }
    
    if (_env['ANALYTICS_ENABLED'] != 'true') {
      _warnings.add('Analytics tracking is disabled');
    }
    
    print('   ✓ Monitoring configuration validated');
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email);
  }

  /// Validate phone number format
  bool _isValidPhoneNumber(String phone) {
    return RegExp(r'^\+[1-9]\d{1,14}$').hasMatch(phone);
  }
}

/// Helper class for colored console output
class ConsoleColors {
  static const String red = '\x1B[31m';
  static const String green = '\x1B[32m';
  static const String yellow = '\x1B[33m';
  static const String blue = '\x1B[34m';
  static const String reset = '\x1B[0m';
  
  static String error(String text) => '$red$text$reset';
  static String success(String text) => '$green$text$reset';
  static String warning(String text) => '$yellow$text$reset';
  static String info(String text) => '$blue$text$reset';
}
