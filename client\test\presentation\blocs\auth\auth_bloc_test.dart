import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared/shared.dart';
import 'package:client/presentation/blocs/auth/auth_bloc.dart';
import 'package:client/data/repositories/api_repository.dart';

class MockApiRepository extends Mock implements ApiRepository {}

void main() {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockApiRepository mockRepository;

    setUp(() {
      mockRepository = MockApiRepository();
      authBloc = AuthBloc(repository: mockRepository);
    });

    tearDown(() {
      authBloc.close();
    });

    test('initial state is AuthInitial', () {
      expect(authBloc.state, equals(const AuthInitial()));
    });

    group('LoginRequested', () {
      const email = '<EMAIL>';
      const password = 'password123';
      const loginRequest = LoginRequest(email: email, password: password);
      const user = User(
        id: '1',
        email: email,
        displayName: 'Test User',
      );
      const loginResponse = LoginResponse(
        user: user,
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthAuthenticated] when login succeeds',
        build: () {
          when(() => mockRepository.login(loginRequest))
              .thenAnswer((_) async => loginResponse);
          return authBloc;
        },
        act: (bloc) => bloc.add(const LoginRequested(
          email: email,
          password: password,
        )),
        expect: () => [
          const AuthLoading(),
          const AuthAuthenticated(
            user: user,
            accessToken: 'access_token',
            refreshToken: 'refresh_token',
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.login(loginRequest)).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthError] when login fails',
        build: () {
          when(() => mockRepository.login(loginRequest))
              .thenThrow(Exception('Invalid credentials'));
          return authBloc;
        },
        act: (bloc) => bloc.add(const LoginRequested(
          email: email,
          password: password,
        )),
        expect: () => [
          const AuthLoading(),
          const AuthError(message: 'Exception: Invalid credentials'),
        ],
        verify: (_) {
          verify(() => mockRepository.login(loginRequest)).called(1);
        },
      );
    });

    group('RegisterRequested', () {
      const email = '<EMAIL>';
      const password = 'password123';
      const displayName = 'Test User';
      const registerRequest = RegisterRequest(
        email: email,
        password: password,
        displayName: displayName,
      );
      const user = User(
        id: '1',
        email: email,
        displayName: displayName,
      );
      const registerResponse = RegisterResponse(
        user: user,
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthAuthenticated] when registration succeeds',
        build: () {
          when(() => mockRepository.register(registerRequest))
              .thenAnswer((_) async => registerResponse);
          return authBloc;
        },
        act: (bloc) => bloc.add(const RegisterRequested(
          email: email,
          password: password,
          displayName: displayName,
        )),
        expect: () => [
          const AuthLoading(),
          const AuthAuthenticated(
            user: user,
            accessToken: 'access_token',
            refreshToken: 'refresh_token',
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.register(registerRequest)).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthError] when registration fails',
        build: () {
          when(() => mockRepository.register(registerRequest))
              .thenThrow(Exception('Email already exists'));
          return authBloc;
        },
        act: (bloc) => bloc.add(const RegisterRequested(
          email: email,
          password: password,
          displayName: displayName,
        )),
        expect: () => [
          const AuthLoading(),
          const AuthError(message: 'Exception: Email already exists'),
        ],
        verify: (_) {
          verify(() => mockRepository.register(registerRequest)).called(1);
        },
      );
    });

    group('LogoutRequested', () {
      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthUnauthenticated] when logout succeeds',
        build: () {
          when(() => mockRepository.logout())
              .thenAnswer((_) async => {});
          return authBloc;
        },
        act: (bloc) => bloc.add(const LogoutRequested()),
        expect: () => [
          const AuthLoading(),
          const AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockRepository.logout()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthError] when logout fails',
        build: () {
          when(() => mockRepository.logout())
              .thenThrow(Exception('Logout failed'));
          return authBloc;
        },
        act: (bloc) => bloc.add(const LogoutRequested()),
        expect: () => [
          const AuthLoading(),
          const AuthError(message: 'Exception: Logout failed'),
        ],
        verify: (_) {
          verify(() => mockRepository.logout()).called(1);
        },
      );
    });

    group('CheckAuthStatus', () {
      const user = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'Test User',
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthAuthenticated] when user is authenticated',
        build: () {
          when(() => mockRepository.getCurrentUser())
              .thenAnswer((_) async => user);
          when(() => mockRepository.getStoredToken())
              .thenAnswer((_) async => 'stored_token');
          return authBloc;
        },
        act: (bloc) => bloc.add(const CheckAuthStatus()),
        expect: () => [
          const AuthLoading(),
          const AuthAuthenticated(
            user: user,
            accessToken: 'stored_token',
            refreshToken: null,
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.getCurrentUser()).called(1);
          verify(() => mockRepository.getStoredToken()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthUnauthenticated] when user is not authenticated',
        build: () {
          when(() => mockRepository.getCurrentUser())
              .thenThrow(Exception('No user found'));
          return authBloc;
        },
        act: (bloc) => bloc.add(const CheckAuthStatus()),
        expect: () => [
          const AuthLoading(),
          const AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockRepository.getCurrentUser()).called(1);
        },
      );
    });

    group('RefreshToken', () {
      const refreshTokenResponse = RefreshTokenResponse(
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthAuthenticated] when token refresh succeeds',
        build: () {
          when(() => mockRepository.refreshToken('old_refresh_token'))
              .thenAnswer((_) async => refreshTokenResponse);
          when(() => mockRepository.getCurrentUser())
              .thenAnswer((_) async => const User(
                id: '1',
                email: '<EMAIL>',
                displayName: 'Test User',
              ));
          return authBloc;
        },
        act: (bloc) => bloc.add(const RefreshToken(refreshToken: 'old_refresh_token')),
        expect: () => [
          const AuthLoading(),
          const AuthAuthenticated(
            user: User(
              id: '1',
              email: '<EMAIL>',
              displayName: 'Test User',
            ),
            accessToken: 'new_access_token',
            refreshToken: 'new_refresh_token',
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.refreshToken('old_refresh_token')).called(1);
          verify(() => mockRepository.getCurrentUser()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthUnauthenticated] when token refresh fails',
        build: () {
          when(() => mockRepository.refreshToken('old_refresh_token'))
              .thenThrow(Exception('Token expired'));
          return authBloc;
        },
        act: (bloc) => bloc.add(const RefreshToken(refreshToken: 'old_refresh_token')),
        expect: () => [
          const AuthLoading(),
          const AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockRepository.refreshToken('old_refresh_token')).called(1);
        },
      );
    });

    group('UpdateProfile', () {
      const updateRequest = UpdateProfileRequest(
        displayName: 'Updated Name',
        avatarUrl: 'https://example.com/avatar.jpg',
      );
      const updatedUser = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'Updated Name',
        avatarUrl: 'https://example.com/avatar.jpg',
      );

      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading, AuthAuthenticated] when profile update succeeds',
        build: () {
          when(() => mockRepository.updateProfile(updateRequest))
              .thenAnswer((_) async => updatedUser);
          return authBloc;
        },
        seed: () => const AuthAuthenticated(
          user: User(
            id: '1',
            email: '<EMAIL>',
            displayName: 'Test User',
          ),
          accessToken: 'access_token',
          refreshToken: 'refresh_token',
        ),
        act: (bloc) => bloc.add(const UpdateProfile(
          displayName: 'Updated Name',
          avatarUrl: 'https://example.com/avatar.jpg',
        )),
        expect: () => [
          const AuthLoading(),
          const AuthAuthenticated(
            user: updatedUser,
            accessToken: 'access_token',
            refreshToken: 'refresh_token',
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.updateProfile(updateRequest)).called(1);
        },
      );
    });
  });
}
