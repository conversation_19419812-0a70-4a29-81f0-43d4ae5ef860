import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/repositories/api_repository.dart';

// Events
abstract class LearningEvent extends Equatable {
  const LearningEvent();
  
  @override
  List<Object?> get props => [];
}

class LoadCourses extends LearningEvent {
  final Map<String, String>? filters;
  
  const LoadCourses({this.filters});
  
  @override
  List<Object?> get props => [filters];
}

class LoadCourse extends LearningEvent {
  final String courseId;
  
  const LoadCourse({required this.courseId});
  
  @override
  List<Object?> get props => [courseId];
}

class EnrollInCourse extends LearningEvent {
  final String courseId;
  
  const EnrollInCourse({required this.courseId});
  
  @override
  List<Object?> get props => [courseId];
}

class LoadUserEnrollments extends LearningEvent {
  const LoadUserEnrollments();
}

class LoadUserCertificates extends LearningEvent {
  const LoadUserCertificates();
}

// States
abstract class LearningState extends Equatable {
  const LearningState();
  
  @override
  List<Object?> get props => [];
}

class LearningInitial extends LearningState {
  const LearningInitial();
}

class LearningLoading extends LearningState {
  const LearningLoading();
}

class LearningLoaded extends LearningState {
  final List<Map<String, dynamic>>? courses;
  final Map<String, dynamic>? selectedCourse;
  final List<Map<String, dynamic>>? enrollments;
  final List<Map<String, dynamic>>? certificates;
  
  const LearningLoaded({
    this.courses,
    this.selectedCourse,
    this.enrollments,
    this.certificates,
  });
  
  @override
  List<Object?> get props => [courses, selectedCourse, enrollments, certificates];
  
  LearningLoaded copyWith({
    List<Map<String, dynamic>>? courses,
    Map<String, dynamic>? selectedCourse,
    List<Map<String, dynamic>>? enrollments,
    List<Map<String, dynamic>>? certificates,
  }) {
    return LearningLoaded(
      courses: courses ?? this.courses,
      selectedCourse: selectedCourse ?? this.selectedCourse,
      enrollments: enrollments ?? this.enrollments,
      certificates: certificates ?? this.certificates,
    );
  }
}

class LearningError extends LearningState {
  final String message;
  
  const LearningError({required this.message});
  
  @override
  List<Object?> get props => [message];
}

class CourseEnrolled extends LearningState {
  final String courseId;
  
  const CourseEnrolled({required this.courseId});
  
  @override
  List<Object?> get props => [courseId];
}

// BLoC
class LearningBloc extends Bloc<LearningEvent, LearningState> {
  final ApiRepository repository;
  
  LearningBloc({required this.repository}) : super(const LearningInitial()) {
    on<LoadCourses>(_onLoadCourses);
    on<LoadCourse>(_onLoadCourse);
    on<EnrollInCourse>(_onEnrollInCourse);
    on<LoadUserEnrollments>(_onLoadUserEnrollments);
    on<LoadUserCertificates>(_onLoadUserCertificates);
  }

  Future<void> _onLoadCourses(LoadCourses event, Emitter<LearningState> emit) async {
    emit(const LearningLoading());
    
    final response = await repository.getCourses(filters: event.filters);
    
    if (response.isSuccess && response.data != null) {
      if (state is LearningLoaded) {
        emit((state as LearningLoaded).copyWith(courses: response.data));
      } else {
        emit(LearningLoaded(courses: response.data));
      }
    } else {
      emit(LearningError(message: response.error ?? 'Failed to load courses'));
    }
  }

  Future<void> _onLoadCourse(LoadCourse event, Emitter<LearningState> emit) async {
    if (state is! LearningLoading) {
      emit(const LearningLoading());
    }
    
    final response = await repository.getCourse(event.courseId);
    
    if (response.isSuccess && response.data != null) {
      if (state is LearningLoaded) {
        emit((state as LearningLoaded).copyWith(selectedCourse: response.data));
      } else {
        emit(LearningLoaded(selectedCourse: response.data));
      }
    } else {
      emit(LearningError(message: response.error ?? 'Failed to load course'));
    }
  }

  Future<void> _onEnrollInCourse(EnrollInCourse event, Emitter<LearningState> emit) async {
    emit(const LearningLoading());
    
    final response = await repository.enrollInCourse(event.courseId);
    
    if (response.isSuccess) {
      emit(CourseEnrolled(courseId: event.courseId));
    } else {
      emit(LearningError(message: response.error ?? 'Failed to enroll in course'));
    }
  }

  Future<void> _onLoadUserEnrollments(LoadUserEnrollments event, Emitter<LearningState> emit) async {
    if (state is! LearningLoading) {
      emit(const LearningLoading());
    }
    
    final response = await repository.getUserEnrollments();
    
    if (response.isSuccess && response.data != null) {
      if (state is LearningLoaded) {
        emit((state as LearningLoaded).copyWith(enrollments: response.data));
      } else {
        emit(LearningLoaded(enrollments: response.data));
      }
    } else {
      emit(LearningError(message: response.error ?? 'Failed to load enrollments'));
    }
  }

  Future<void> _onLoadUserCertificates(LoadUserCertificates event, Emitter<LearningState> emit) async {
    if (state is! LearningLoading) {
      emit(const LearningLoading());
    }
    
    // Using enrollments as placeholder since getUserCertificates isn't implemented yet
    final response = await repository.getUserEnrollments();
    
    if (response.isSuccess && response.data != null) {
      if (state is LearningLoaded) {
        emit((state as LearningLoaded).copyWith(certificates: response.data));
      } else {
        emit(LearningLoaded(certificates: response.data));
      }
    } else {
      emit(LearningError(message: response.error ?? 'Failed to load certificates'));
    }
  }
}