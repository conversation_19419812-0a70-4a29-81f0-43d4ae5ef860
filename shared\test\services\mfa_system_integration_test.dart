import 'package:test/test.dart';
import 'package:shared/shared.dart';
import '../test_helpers.dart';

void main() {
  group('MFA System Integration Tests', () {
    late TestDatabase testDatabase;
    late MockServices mockServices;

    setUpAll(() async {
      testDatabase = await TestDatabase.create();
      mockServices = MockServices();
    });

    tearDownAll(() async {
      await testDatabase.cleanup();
    });

    group('Complete MFA Setup Flow', () {
      test('should complete full MFA setup for new user', () async {
        final userId = 'test_user_${DateTime.now().millisecondsSinceEpoch}';
        
        // Step 1: Check initial MFA status
        final initialStatus = await mockServices.mfaService.getMFASetupStatus(userId);
        expect(initialStatus.totpEnabled, false);
        expect(initialStatus.setupComplete, false);
        expect(initialStatus.backupCodesCount, 0);

        // Step 2: Generate TOTP setup
        final totpSetup = await mockServices.totpService.generateTOTPSetup(userId);
        expect(totpSetup.secret, isNotEmpty);
        expect(totpSetup.qrCode, contains('otpauth://totp/'));

        // Step 3: Verify TOTP setup with mock code
        final mockTotpCode = TestHelpers.generateMockTOTPCode(totpSetup.secret);
        final totpVerification = await mockServices.totpService.verifyTOTPSetup(userId, mockTotpCode);
        expect(totpVerification.isValid, true);
        expect(totpVerification.isValid, true);

        // Step 4: Generate backup codes
        const config = BackupCodeGenerationConfig(codeCount: 10);
        final backupCodes = await mockServices.backupCodesService.generateBackupCodes(
          userId,
          config: config,
        );
        expect(backupCodes.codes.length, 10);
        expect(backupCodes.remainingCodes, 10);

        // Step 5: Verify final MFA status
        final finalStatus = await mockServices.mfaService.getMFASetupStatus(userId);
        expect(finalStatus.totpEnabled, true);
        expect(finalStatus.totpVerified, true);
        expect(finalStatus.backupCodesCount, 10);
        expect(finalStatus.setupComplete, true);
        expect(finalStatus.completionScore, greaterThan(0.7));
      });

      test('should handle MFA setup with trusted device registration', () async {
        final userId = 'test_user_trusted_${DateTime.now().millisecondsSinceEpoch}';
        
        // Setup MFA first
        await _setupBasicMFA(userId, mockServices);

        // Register a trusted device
        final deviceRequest = DeviceRegistrationRequest(
          deviceFingerprint: 'test_device_fingerprint_001',
          deviceName: 'Test Chrome Browser',
          deviceType: DeviceType.desktop,
          operatingSystem: 'Windows 11',
          browserInfo: 'Chrome 120.0',
          ipAddress: '*************',
          trustImmediately: true,
          trustDurationHours: 720, // 30 days
        );

        final trustedDevice = await mockServices.trustedDeviceService.registerDevice(
          userId,
          deviceRequest,
        );

        expect(trustedDevice.deviceName, 'Test Chrome Browser');
        expect(trustedDevice.deviceType, DeviceType.desktop);
        expect(trustedDevice.isActive, true);
        expect(trustedDevice.isTrusted, true);

        // Verify device verification works
        final deviceVerification = await mockServices.trustedDeviceService.verifyDevice(
          userId,
          'test_device_fingerprint_001',
        );

        expect(deviceVerification.isTrusted, true);
        expect(deviceVerification.requiresMFA, false);
        expect(deviceVerification.isNewDevice, false);
      });
    });

    group('MFA Authentication Flow Tests', () {
      test('should enforce MFA for untrusted device', () async {
        final userId = 'test_user_auth_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Evaluate MFA requirement for new device
        final mfaRequirement = await mockServices.mfaPolicyEngine.evaluateMFARequirement(
          userId,
          deviceFingerprint: 'unknown_device_001',
          ipAddress: '*************',
        );

        expect(mfaRequirement.isMFARequired, true);
        expect(mfaRequirement.canBypassMFA, false);
        expect(mfaRequirement.availableMethods, contains(MFAMethod.totp));
        expect(mfaRequirement.recommendedMethods, isNotEmpty);
      });

      test('should complete full TOTP MFA challenge flow', () async {
        final userId = 'test_user_totp_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Create TOTP challenge
        final challenge = await mockServices.mfaPolicyEngine.createMFAChallenge(
          userId,
          MFAMethod.totp,
        );

        expect(challenge.method, MFAMethod.totp);
        expect(challenge.destinationHint, 'Authenticator App');
        expect(challenge.isExpired, false);
        expect(challenge.hasAttemptsRemaining, true);

        // Generate mock TOTP code
        final totpSetup = await mockServices.totpService.getTOTPSetup(userId);
        final mockCode = TestHelpers.generateMockTOTPCode(totpSetup!.secret);

        // Verify challenge
        final verificationRequest = MFAVerificationRequest(
          challengeId: challenge.id,
          userId: userId,
          method: MFAMethod.totp,
          code: mockCode,
        );

        final verification = await mockServices.mfaPolicyEngine.verifyMFAChallenge(
          verificationRequest,
        );

        expect(verification.isValid, true);
        expect(verification.isCompleted, true);
        expect(verification.hasSessionToken, true);
        expect(verification.deviceTrusted, true);
      });

      test('should complete backup code MFA challenge flow', () async {
        final userId = 'test_user_backup_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Get a backup code
        const config = BackupCodeGenerationConfig(codeCount: 5);
        final backupCodes = await mockServices.backupCodesService.generateBackupCodes(
          userId,
          config: config,
        );
        final testCode = backupCodes.codes.first;

        // Create backup code challenge
        final challenge = await mockServices.mfaPolicyEngine.createMFAChallenge(
          userId,
          MFAMethod.backupCode,
        );

        expect(challenge.method, MFAMethod.backupCode);
        expect(challenge.destinationHint, 'Backup Code');

        // Verify with backup code
        final verificationRequest = MFAVerificationRequest(
          challengeId: challenge.id,
          userId: userId,
          method: MFAMethod.backupCode,
          code: testCode,
        );

        final verification = await mockServices.mfaPolicyEngine.verifyMFAChallenge(
          verificationRequest,
        );

        expect(verification.isValid, true);
        expect(verification.isCompleted, true);
        
        // Verify backup code was consumed
        final remainingCodes = await mockServices.backupCodesService.getActiveCodeCount(userId);
        expect(remainingCodes, 4); // One less than before
      });
    });

    group('MFA Policy Engine Tests', () {
      test('should apply risk-based MFA policies correctly', () async {
        final userId = 'test_user_policy_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Test with trusted device (should bypass MFA)
        final deviceRequest = DeviceRegistrationRequest(
          deviceFingerprint: 'trusted_device_001',
          deviceName: 'Trusted Work Laptop',
          deviceType: DeviceType.desktop,
          ipAddress: '*********',
          trustImmediately: true,
        );

        await mockServices.trustedDeviceService.registerDevice(userId, deviceRequest);

        final trustedResult = await mockServices.mfaPolicyEngine.evaluateMFARequirement(
          userId,
          deviceFingerprint: 'trusted_device_001',
          ipAddress: '*********',
        );

        expect(trustedResult.isMFARequired, false);
        expect(trustedResult.canBypassMFA, true);
        expect(trustedResult.bypassReason, MFABypassReason.trustedDevice);

        // Test with suspicious activity (should require MFA)
        final suspiciousResult = await mockServices.mfaPolicyEngine.evaluateMFARequirement(
          userId,
          deviceFingerprint: 'suspicious_device_001',
          ipAddress: '192.168.1.999', // Different IP
          context: {
            'suspicious_activity': true,
            'login_time': DateTime.now().subtract(Duration(hours: 25)).toIso8601String(), // Unusual time
          },
        );

        expect(suspiciousResult.isMFARequired, true);
        expect(suspiciousResult.canBypassMFA, false);
        expect(suspiciousResult.warnings, isNotEmpty);
      });

      test('should handle multiple policy types and priorities', () async {
        final userId = 'test_user_multi_policy_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Create multiple policies with different priorities
        final policies = await mockServices.mfaPolicyEngine.getUserMFAPolicies(userId);
        
        // Should have default policy at minimum
        expect(policies, isNotEmpty);
        
        // Test policy evaluation with different contexts
        final contexts = [
          {'login_source': 'mobile_app'},
          {'login_source': 'web_browser', 'location': 'office'},
          {'login_source': 'api', 'high_risk_operation': true},
        ];

        for (final context in contexts) {
          final result = await mockServices.mfaPolicyEngine.evaluateMFARequirement(
            userId,
            context: context,
          );
          
          expect(result.policyContext, isNotEmpty);
          expect(result.enforcedAt, isNotNull);
        }
      });
    });

    group('MFA Recovery Flow Tests', () {
      test('should complete email recovery flow', () async {
        final userId = 'test_user_recovery_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Simulate account lockout
        await mockServices.recoveryService.lockAccount(userId, 'Multiple failed MFA attempts');

        // Initiate recovery
        final recoveryResult = await mockServices.recoveryService.initiateRecovery(
          userId,
          method: RecoveryMethod.email,
          reason: UnlockReason.userRequest,
          recoveryDestination: '<EMAIL>',
        );

        expect(recoveryResult.success, true);
        expect(recoveryResult.recoveryId, isNotEmpty);
        expect(recoveryResult.method, RecoveryMethod.email);
        expect(recoveryResult.requiredSteps, isNotEmpty);

        // Mock email verification
        final mockCode = TestHelpers.extractVerificationCode(recoveryResult.recoveryId!);
        
        final verificationResult = await mockServices.recoveryService.verifyRecovery(
          recoveryResult.recoveryId!,
          mockCode,
        );

        expect(verificationResult.isValid, true);
        expect(verificationResult.nextStep, isNotEmpty);

        // Complete recovery
        final completionResult = await mockServices.recoveryService.completeRecovery(
          recoveryResult.recoveryId!,
          resetAllMFA: true,
        );

        expect(completionResult.success, true);
        expect(completionResult.actionsPerformed, contains('Account unlocked'));
        expect(completionResult.recommendations, isNotEmpty);
      });

      test('should handle admin approval recovery flow', () async {
        final userId = 'test_user_admin_recovery_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Initiate admin recovery
        final recoveryResult = await mockServices.recoveryService.initiateRecovery(
          userId,
          method: RecoveryMethod.email,
          reason: UnlockReason.adminUnlock,
        );

        expect(recoveryResult.success, true);
        expect(recoveryResult.destinationHint, 'Admin Review Required');

        // Get pending requests
        final pendingRequests = await mockServices.recoveryService.getPendingRecoveryRequests();
        expect(pendingRequests, isNotEmpty);
        
        final ourRequest = pendingRequests.firstWhere((r) => r.id == recoveryResult.recoveryId);
        expect(ourRequest.status, RecoveryStatus.pending);

        // Admin approves request
        await mockServices.recoveryService.approveRecoveryRequest(
          recoveryResult.recoveryId!,
          'admin_user_001',
          notes: 'Verified user identity through alternative means',
        );

        // Complete recovery
        final completionResult = await mockServices.recoveryService.completeRecovery(
          recoveryResult.recoveryId!,
          approvedBy: 'admin_user_001',
        );

        expect(completionResult.success, true);
        expect(completionResult.metadata['completed_by'], 'admin_user_001');
      });
    });

    group('Security and Error Handling Tests', () {
      test('should prevent MFA bypass attempts', () async {
        final userId = 'test_user_security_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Attempt to bypass with invalid session token
        final invalidToken = 'invalid_token_${DateTime.now().millisecondsSinceEpoch}';
        final isValidSession = await mockServices.sessionService.validateMFASession(invalidToken);
        expect(isValidSession, false);

        // Attempt multiple failed TOTP verifications
        final challenge = await mockServices.mfaPolicyEngine.createMFAChallenge(
          userId,
          MFAMethod.totp,
        );

        for (int i = 0; i < 4; i++) {
          final verificationRequest = MFAVerificationRequest(
            challengeId: challenge.id,
            userId: userId,
            method: MFAMethod.totp,
            code: 'invalid_code_$i',
          );

          final verification = await mockServices.mfaPolicyEngine.verifyMFAChallenge(
            verificationRequest,
          );

          if (i < 3) {
            expect(verification.isValid, false);
            expect(verification.attemptsRemaining, greaterThan(0));
          } else {
            expect(verification.attemptsRemaining, 0);
          }
        }
      });

      test('should handle service failures gracefully', () async {
        // Test service failure scenarios without userId dependency
        
        // Test TOTP service failure
        expect(
          () async => await mockServices.totpService.generateTOTPSetup('invalid_user_id'),
          throwsA(isA<Exception>()),
        );

        // Test backup codes service with invalid config
        expect(
          () async => await mockServices.backupCodesService.generateBackupCodes(
            'invalid_user_id',
            config: BackupCodeGenerationConfig(codeCount: -1),
          ),
          throwsA(isA<Exception>()),
        );

        // Test trusted device service with invalid data
        expect(
          () async => await mockServices.trustedDeviceService.registerDevice(
            'invalid_user_id',
            DeviceRegistrationRequest(
              deviceFingerprint: '',  // Invalid empty fingerprint
              deviceName: '',         // Invalid empty name
              deviceType: DeviceType.unknown,
              ipAddress: 'invalid_ip',
            ),
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should validate input data properly', () async {
        // Test TOTP validation
        final invalidTotpInputs = ['', '123', 'abcdef', '1234567890'];
        for (final input in invalidTotpInputs) {
          final result = await mockServices.totpService.validateTOTP('test_user', input);
          expect(result.isValid, false);
        }

        // Test backup code validation
        final invalidBackupCodes = ['', '123', 'invalid-format'];
        for (final code in invalidBackupCodes) {
          final result = await mockServices.backupCodesService.validateBackupCode('test_user', code);
          expect(result.isValid, false);
        }

        // Test device fingerprint validation
        final invalidFingerprints = ['', 'too_short', 'x' * 300]; // Too long
        for (final fingerprint in invalidFingerprints) {
          expect(
            () async => await mockServices.trustedDeviceService.verifyDevice(
              'test_user',
              fingerprint,
            ),
            throwsA(isA<Exception>()),
          );
        }
      });
    });

    group('Performance and Scale Tests', () {
      test('should handle concurrent MFA operations', () async {
        final userIds = List.generate(10, (i) => 'concurrent_user_$i');
        
        // Setup MFA for multiple users concurrently
        final futures = userIds.map((userId) => _setupBasicMFA(userId, mockServices)).toList();
        await Future.wait(futures);

        // Verify all setups completed successfully
        for (final userId in userIds) {
          final status = await mockServices.mfaService.getMFASetupStatus(userId);
          expect(status.setupComplete, true);
        }
      });

      test('should cleanup expired data efficiently', () async {
        final userId = 'test_user_cleanup_${DateTime.now().millisecondsSinceEpoch}';
        await _setupBasicMFA(userId, mockServices);

        // Create multiple expired challenges
        for (int i = 0; i < 5; i++) {
          await mockServices.mfaPolicyEngine.createMFAChallenge(
            userId,
            MFAMethod.totp,
            validDuration: Duration(microseconds: 1), // Immediately expired
          );
        }

        // Run cleanup
        await mockServices.mfaPolicyEngine.cleanupExpiredChallenges();
        await mockServices.backupCodesService.cleanupExpiredCodes();
        await mockServices.trustedDeviceService.cleanupExpiredDevices();
        await mockServices.recoveryService.cleanupExpiredRecoveryRequests();

        // Verify cleanup was effective (this would check actual database state in real implementation)
        expect(true, true); // Placeholder - would verify no expired records remain
      });
    });
  });
}

// Helper function to setup basic MFA for testing
Future<void> _setupBasicMFA(String userId, MockServices services) async {
  // Generate and verify TOTP
  final totpSetup = await services.totpService.generateTOTPSetup(userId);
  final mockCode = TestHelpers.generateMockTOTPCode(totpSetup.secret);
  await services.totpService.verifyTOTPSetup(userId, mockCode);

  // Generate backup codes
  await services.backupCodesService.generateBackupCodes(userId);
}

// Mock services for testing
class MockServices {
  late MockMFAService mfaService;
  late MockTOTPService totpService;
  late MockBackupCodesService backupCodesService;
  late MockTrustedDeviceService trustedDeviceService;
  late MockMFAPolicyEngine mfaPolicyEngine;
  late MockMFARecoveryService recoveryService;
  late MockSessionService sessionService;

  MockServices() {
    mfaService = MockMFAService();
    totpService = MockTOTPService();
    backupCodesService = MockBackupCodesService();
    trustedDeviceService = MockTrustedDeviceService();
    mfaPolicyEngine = MockMFAPolicyEngine();
    recoveryService = MockMFARecoveryService();
    sessionService = MockSessionService();
  }
}

// Mock service implementations would be defined here
// These would simulate the actual service behavior for testing

class MockMFAService {
  Future<MFASetupStatus> getMFASetupStatus(String userId) async {
    // Mock implementation
    return MFASetupStatus(
      userId: userId,
      totpEnabled: false,
      totpVerified: false,
      backupCodesCount: 0,
      trustedDevicesCount: 0,
      phoneConfigured: false,
      emailConfigured: true,
      setupComplete: false,
      lastUpdated: DateTime.now(),
    );
  }
}

class MockTOTPService {
  final Map<String, TOTPSetup> _setups = {};

  Future<TOTPSetup> generateTOTPSetup(String userId) async {
    final setup = TOTPSetup(
      secret: TestHelpers.generateMockSecret(),
      qrCode: 'otpauth://totp/TestApp:<EMAIL>?secret=TESTSECRET&issuer=TestApp',
      backupCodes: ['123456', '789012', '345678'],
    );
    _setups[userId] = setup;
    return setup;
  }

  Future<TOTPSetup?> getTOTPSetup(String userId) async {
    return _setups[userId];
  }

  Future<TOTPVerificationResult> verifyTOTPSetup(String userId, String code) async {
    final setup = _setups[userId];
    if (setup == null) {
      return TOTPVerificationResult(
        isValid: false,
      );
    }

    final expectedCode = TestHelpers.generateMockTOTPCode(setup.secret);
    if (code == expectedCode) {
      // Update verification status in a different way since copyWith doesn't exist
      return TOTPVerificationResult(
        isValid: true,
      );
    }

    return TOTPVerificationResult(
      isValid: false,
    );
  }

  Future<TOTPValidationResult> validateTOTP(String userId, String code) async {
    final setup = _setups[userId];
    if (setup == null) {
      return TOTPValidationResult(
        isValid: false,
      );
    }

    if (code.length != 6 || !RegExp(r'^\d{6}$').hasMatch(code)) {
      return TOTPValidationResult(
        isValid: false,
      );
    }

    final expectedCode = TestHelpers.generateMockTOTPCode(setup.secret);
    return TOTPValidationResult(
      isValid: code == expectedCode,
    );
  }
}

class MockBackupCodesService {
  final Map<String, List<String>> _userCodes = {};

  Future<BackupCodeSet> generateBackupCodes(String userId, {BackupCodeGenerationConfig? config}) async {
    config ??= BackupCodeGenerationConfig();
    
    final codes = List.generate(config.codeCount, (i) => 'TEST${i.toString().padLeft(4, '0')}');
    _userCodes[userId] = codes;
    
    return BackupCodeSet(
      codes: codes,
      generatedAt: DateTime.now(),
      userId: userId,
      remainingCodes: codes.length,
    );
  }

  Future<int> getActiveCodeCount(String userId) async {
    return _userCodes[userId]?.length ?? 0;
  }

  Future<BackupCodeValidationResult> validateBackupCode(String userId, String code) async {
    final codes = _userCodes[userId];
    if (codes == null || codes.isEmpty) {
      return BackupCodeValidationResult(
        isValid: false,
        errorMessage: 'No backup codes available',
        validatedAt: DateTime.now(),
      );
    }

    if (codes.contains(code)) {
      codes.remove(code);
      return BackupCodeValidationResult(
        isValid: true,
        remainingCodes: codes.length,
        codeConsumed: true,
        validatedAt: DateTime.now(),
      );
    }

    return BackupCodeValidationResult(
      isValid: false,
      errorMessage: 'Invalid backup code',
      validatedAt: DateTime.now(),
    );
  }

  Future<void> cleanupExpiredCodes() async {
    // Mock cleanup
  }
}

class MockTrustedDeviceService {
  final Map<String, List<TrustedDevice>> _userDevices = {};

  Future<TrustedDevice> registerDevice(String userId, DeviceRegistrationRequest request) async {
    if (request.deviceFingerprint.isEmpty || request.deviceName.isEmpty) {
      throw Exception('Invalid device registration data');
    }

    final device = TrustedDevice(
      id: 'device_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      deviceFingerprint: request.deviceFingerprint,
      deviceName: request.deviceName,
      deviceType: request.deviceType,
      ipAddress: request.ipAddress,
      firstSeen: DateTime.now(),
      lastSeen: DateTime.now(),
      trustedAt: request.trustImmediately ? DateTime.now() : null,
      expiresAt: request.trustDuration != null 
          ? DateTime.now().add(request.trustDuration!)
          : null,
    );

    _userDevices.putIfAbsent(userId, () => []).add(device);
    return device;
  }

  Future<DeviceVerificationResult> verifyDevice(String userId, String fingerprint, {String? ipAddress}) async {
    if (fingerprint.isEmpty || fingerprint.length < 10) {
      throw Exception('Invalid device fingerprint');
    }

    final devices = _userDevices[userId] ?? [];
    final device = devices.cast<TrustedDevice?>().firstWhere(
      (d) => d!.deviceFingerprint == fingerprint,
      orElse: () => null,
    );

    if (device == null) {
      return DeviceVerificationResult(
        isTrusted: false,
        requiresMFA: true,
        isNewDevice: true,
        warningMessage: 'Unknown device',
        riskFactors: ['unknown_device'],
      );
    }

    return DeviceVerificationResult(
      isTrusted: device.isTrusted,
      requiresMFA: !device.isTrusted,
      isNewDevice: false,
      device: device,
    );
  }

  Future<List<TrustedDevice>> getUserDevices(String userId, {bool activeOnly = false}) async {
    final devices = _userDevices[userId] ?? [];
    return activeOnly ? devices.where((d) => d.isActive).toList() : devices;
  }

  Future<void> cleanupExpiredDevices() async {
    // Mock cleanup
  }
}

class MockMFAPolicyEngine {
  Future<MFAEnforcementResult> evaluateMFARequirement(String userId, {String? deviceFingerprint, String? ipAddress, Map<String, dynamic>? context}) async {
    // Simple mock logic
    final isTrusted = deviceFingerprint?.contains('trusted') == true;
    
    return MFAEnforcementResult(
      isMFARequired: !isTrusted,
      canBypassMFA: isTrusted,
      availableMethods: [MFAMethod.totp, MFAMethod.sms, MFAMethod.backupCode],
      recommendedMethods: [MFAMethod.totp],
      bypassReason: isTrusted ? MFABypassReason.trustedDevice : null,
      policyContext: {'mock': true},
      enforcedAt: DateTime.now(),
    );
  }

  Future<MFAChallenge> createMFAChallenge(String userId, MFAMethod method, {Duration? validDuration}) async {
    return MFAChallenge(
      id: 'challenge_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      method: method,
      destinationHint: method == MFAMethod.totp ? 'Authenticator App' : 'Backup Code',
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(validDuration ?? Duration(minutes: 5)),
    );
  }

  Future<MFAVerificationResult> verifyMFAChallenge(MFAVerificationRequest request) async {
    // Mock successful verification
    return MFAVerificationResult(
      isValid: true,
      isCompleted: true,
      sessionToken: 'mock_session_${DateTime.now().millisecondsSinceEpoch}',
      attemptsRemaining: 2,
      deviceTrusted: false, // Default to false since trustDevice field doesn't exist
      verifiedAt: DateTime.now(),
    );
  }

  Future<List<dynamic>> getUserMFAPolicies(String userId) async {
    return [{'id': 'default', 'name': 'Default Policy'}];
  }

  Future<void> cleanupExpiredChallenges() async {
    // Mock cleanup
  }
}

class MockMFARecoveryService {
  Future<void> lockAccount(String userId, String reason) async {
    // Mock account lock
  }

  Future<dynamic> initiateRecovery(String userId, {required dynamic method, required dynamic reason, String? recoveryDestination}) async {
    return TestHelpers.createMockRecoveryResult(method, reason);
  }

  Future<dynamic> verifyRecovery(String recoveryId, String code) async {
    return TestHelpers.createMockVerificationResult(true);
  }

  Future<dynamic> completeRecovery(String recoveryId, {bool resetAllMFA = true, String? approvedBy}) async {
    return TestHelpers.createMockCompletionResult(true);
  }

  Future<List<dynamic>> getPendingRecoveryRequests() async {
    return [];
  }

  Future<void> approveRecoveryRequest(String recoveryId, String approvedBy, {String? notes}) async {
    // Mock approval
  }

  Future<void> cleanupExpiredRecoveryRequests() async {
    // Mock cleanup
  }
}

class MockSessionService {
  Future<bool> validateMFASession(String token) async {
    return token.startsWith('mock_session_');
  }
}