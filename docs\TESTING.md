# Testing Guide

This document outlines the testing strategy and implementation for the Quester platform.

## Testing Architecture

The Quester platform uses a comprehensive testing approach across all three packages:

- **Shared Package**: Unit tests for models, utilities, and DTOs
- **Server Package**: Unit tests for services, integration tests for APIs
- **Client Package**: Unit tests for BLoCs, widget tests for UI components

## Test Structure

```
├── shared/test/
│   ├── models/
│   ├── utilities/
│   └── shared_test.dart
├── server/test/
│   ├── integration/
│   ├── services/
│   └── server_test.dart
└── client/test/
    ├── features/
    ├── core/
    └── widget_test.dart
```

## Running Tests

### All Tests
```bash
# Run all tests across the monorepo
bash validate-project.sh --test

# Or run individually
cd shared && dart test
cd server && dart test
cd client && flutter test
```

### Specific Test Suites
```bash
# Shared package tests
cd shared && dart test

# Server API tests
cd server && dart test

# Client widget tests
cd client && flutter test

# Integration tests
cd server && dart test test/integration/

# End-to-end tests
cd client && flutter test integration_test/
```

## Test Categories

### 1. Unit Tests

**Shared Package**
- Model serialization/deserialization
- Utility function validation
- DTO transformation
- Constants and enums

**Server Package**
- Service layer logic
- Authentication and authorization
- Data validation
- Business rule enforcement

**Client Package**
- BLoC state management
- Form validation
- Utility functions
- Model transformations

### 2. Widget Tests (Client)

- UI component rendering
- User interaction handling
- State changes and updates
- Responsive behavior
- Accessibility compliance

### 3. Integration Tests (Server)

- API endpoint functionality
- Database operations
- Authentication flows
- Error handling
- Performance benchmarks

### 4. End-to-End Tests

- Complete user workflows
- Cross-platform compatibility
- Real-time features
- Data persistence
- Security validation

## Test Implementation Examples

### Unit Test Example (BLoC)
```dart
blocTest<QuestCreationBloc, QuestCreationState>(
  'emits updated state with new title',
  build: () => questCreationBloc,
  act: (bloc) => bloc.add(const UpdateTitle('New Quest Title')),
  expect: () => [
    isA<QuestCreationInProgress>()
        .having((state) => state.title, 'title', 'New Quest Title'),
  ],
);
```

### Widget Test Example
```dart
testWidgets('displays quest information correctly', (WidgetTester tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: QuestCard(quest: mockQuest),
    ),
  );

  expect(find.text('Test Quest'), findsOneWidget);
  expect(find.textContaining('150'), findsOneWidget); // Points
});
```

### Integration Test Example
```dart
test('POST /api/v1/gamification/quests creates new quest', () async {
  final response = await http.post(
    Uri.parse('$baseUrl/api/v1/gamification/quests'),
    body: jsonEncode(questData),
  );

  expect(response.statusCode, equals(201));
  expect(jsonDecode(response.body)['success'], isTrue);
});
```

## Testing Best Practices

### 1. Test Organization
- Group related tests using `group()`
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests focused and atomic

### 2. Mocking and Fixtures
- Use `mocktail` for mocking dependencies
- Create reusable test fixtures
- Mock external services and APIs
- Isolate units under test

### 3. Coverage Goals
- Aim for 90%+ code coverage
- Focus on critical business logic
- Test error scenarios and edge cases
- Validate user input handling

### 4. Performance Testing
- Benchmark critical operations
- Test with realistic data volumes
- Monitor memory usage
- Validate response times

## Test Data Management

### Mock Data
- Use consistent test data across tests
- Create factory methods for test objects
- Maintain realistic data relationships
- Version test data with code changes

### Test Database
- Use in-memory database for unit tests
- Seed test data for integration tests
- Clean up after each test
- Isolate test transactions

## Continuous Integration

### GitHub Actions
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: dart-lang/setup-dart@v1
      - run: bash validate-project.sh --test
```

### Test Reports
- Generate coverage reports
- Track test metrics over time
- Monitor test execution time
- Alert on test failures

## Debugging Tests

### Common Issues
- Async test timing issues
- Widget test pump cycles
- Mock setup problems
- Test isolation failures

### Debugging Tools
- Use `debugger()` for breakpoints
- Add `print()` statements for debugging
- Use `flutter test --verbose` for details
- Check test logs and stack traces

## Test Maintenance

### Regular Tasks
- Update tests with code changes
- Refactor duplicate test code
- Remove obsolete tests
- Update mock data

### Code Reviews
- Review test coverage in PRs
- Validate test quality
- Ensure proper test isolation
- Check for flaky tests

## Resources

- [Flutter Testing Guide](https://flutter.dev/docs/testing)
- [Dart Testing Guide](https://dart.dev/guides/testing)
- [BLoC Testing](https://bloclibrary.dev/#/testing)
- [Mocktail Documentation](https://pub.dev/packages/mocktail)
