import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../common/responsive_builder.dart';

/// Custom app bar widget with Quester branding and responsive design
class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  /// Title to display in the app bar
  final String? title;
  
  /// Whether to show back button
  final bool showBackButton;
  
  /// Custom leading widget
  final Widget? leading;
  
  /// List of action widgets
  final List<Widget>? actions;
  
  /// Background color of the app bar
  final Color? backgroundColor;
  
  /// Elevation of the app bar
  final double? elevation;
  
  /// Whether to center the title
  final bool centerTitle;

  const AppBarWidget({
    super.key,
    this.title,
    this.showBackButton = false,
    this.leading,
    this.actions,
    this.backgroundColor,
    this.elevation,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.surface,
      elevation: elevation ?? AppConstants.appBarElevation,
      surfaceTintColor: Colors.transparent,
      leading: _buildLeading(context),
      title: _buildTitle(context),
      centerTitle: centerTitle,
      actions: _buildActions(context),
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.surface,
              Theme.of(context).colorScheme.surface.withOpacity(0.8),
            ],
          ),
        ),
      ),
    );
  }

  /// Build leading widget
  Widget? _buildLeading(BuildContext context) {
    if (leading != null) {
      return leading;
    }
    
    if (showBackButton) {
      return IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      );
    }
    
    // Show Quester logo on desktop/tablet when no back button
    if (!context.isMobile && !showBackButton) {
      return _buildLogo(context);
    }
    
    return null;
  }

  /// Build title widget
  Widget? _buildTitle(BuildContext context) {
    if (title != null) {
      return ResponsiveText(
        title!,
        mobileStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        tabletStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        desktopStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      );
    }
    
    // Show Quester logo and title on mobile when no custom title
    if (context.isMobile) {
      return _buildLogoWithTitle(context);
    }
    
    return null;
  }

  /// Build actions list
  List<Widget>? _buildActions(BuildContext context) {
    if (actions == null || actions!.isEmpty) {
      return null;
    }
    
    return [
      ...actions!,
      const SizedBox(width: AppConstants.smallPadding),
    ];
  }

  /// Build Quester logo
  Widget _buildLogo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: context.responsive(
              mobile: 32,
              tablet: 36,
              desktop: 40,
            ),
            height: context.responsive(
              mobile: 32,
              tablet: 36,
              desktop: 40,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary,
                ],
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.explore,
              color: Theme.of(context).colorScheme.onPrimary,
              size: context.responsive(
                mobile: 20,
                tablet: 22,
                desktop: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build logo with title for mobile
  Widget _buildLogoWithTitle(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.secondary,
              ],
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.explore,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 20,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Text(
          AppConstants.appName,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    AppConstants.appBarHeight,
  );
}

/// Search app bar for search functionality
class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  /// Callback when search query changes
  final ValueChanged<String>? onSearchChanged;
  
  /// Callback when search is submitted
  final ValueChanged<String>? onSearchSubmitted;
  
  /// Callback when search is cancelled
  final VoidCallback? onSearchCancelled;
  
  /// Initial search query
  final String? initialQuery;
  
  /// Hint text for search field
  final String? hintText;

  const SearchAppBar({
    super.key,
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.onSearchCancelled,
    this.initialQuery,
    this.hintText,
  });

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(AppConstants.appBarHeight);
}

class _SearchAppBarState extends State<SearchAppBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
    _focusNode = FocusNode();
    
    // Auto focus when search bar is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Theme.of(context).colorScheme.surface,
      elevation: AppConstants.appBarElevation,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () {
          widget.onSearchCancelled?.call();
          Navigator.of(context).pop();
        },
      ),
      title: TextField(
        controller: _controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: widget.hintText ?? 'Search quests, tasks, users...',
          border: InputBorder.none,
          hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        style: Theme.of(context).textTheme.bodyLarge,
        onChanged: widget.onSearchChanged,
        onSubmitted: widget.onSearchSubmitted,
        textInputAction: TextInputAction.search,
      ),
      actions: [
        if (_controller.text.isNotEmpty)
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _controller.clear();
              widget.onSearchChanged?.call('');
            },
          ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            widget.onSearchSubmitted?.call(_controller.text);
          },
        ),
        const SizedBox(width: AppConstants.smallPadding),
      ],
    );
  }
}

/// Sliver app bar for scrollable content
class SliverAppBarWidget extends StatelessWidget {
  /// Title to display
  final String? title;
  
  /// Background image or widget
  final Widget? background;
  
  /// Whether the app bar should float
  final bool floating;
  
  /// Whether the app bar should pin when collapsed
  final bool pinned;
  
  /// Whether the app bar should snap
  final bool snap;
  
  /// Expanded height of the app bar
  final double? expandedHeight;
  
  /// Actions to display
  final List<Widget>? actions;

  const SliverAppBarWidget({
    super.key,
    this.title,
    this.background,
    this.floating = false,
    this.pinned = true,
    this.snap = false,
    this.expandedHeight,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      floating: floating,
      pinned: pinned,
      snap: snap,
      expandedHeight: expandedHeight ?? context.responsive(
        mobile: 200,
        tablet: 250,
        desktop: 300,
      ),
      backgroundColor: Theme.of(context).colorScheme.surface,
      actions: actions,
      flexibleSpace: FlexibleSpaceBar(
        title: title != null ? Text(
          title!,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ) : null,
        background: background ?? Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
                Theme.of(context).colorScheme.secondary.withOpacity(0.1),
              ],
            ),
          ),
        ),
        centerTitle: true,
        titlePadding: const EdgeInsets.only(
          left: AppConstants.defaultPadding,
          right: AppConstants.defaultPadding,
          bottom: AppConstants.defaultPadding,
        ),
      ),
    );
  }
}
