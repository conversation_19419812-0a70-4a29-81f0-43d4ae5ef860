// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomReport _$CustomReportFromJson(Map<String, dynamic> json) => CustomReport(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  reportType: json['reportType'] as String,
  queryConfig: json['queryConfig'] as Map<String, dynamic>,
  visualizationConfig: json['visualizationConfig'] as Map<String, dynamic>,
  layoutConfig: json['layoutConfig'] as Map<String, dynamic>,
  filterConfig: json['filterConfig'] as Map<String, dynamic>,
  scheduleConfig: json['scheduleConfig'] as Map<String, dynamic>?,
  permissions: json['permissions'] as Map<String, dynamic>,
  tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
  isTemplate: json['isTemplate'] as bool,
  isPublic: json['isPublic'] as bool,
  status: $enumDecode(_$ReportStatusEnumMap, json['status']),
  createdBy: json['createdBy'] as String,
  updatedBy: json['updatedBy'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$CustomReportToJson(CustomReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'name': instance.name,
      'description': instance.description,
      'reportType': instance.reportType,
      'queryConfig': instance.queryConfig,
      'visualizationConfig': instance.visualizationConfig,
      'layoutConfig': instance.layoutConfig,
      'filterConfig': instance.filterConfig,
      'scheduleConfig': instance.scheduleConfig,
      'permissions': instance.permissions,
      'tags': instance.tags,
      'isTemplate': instance.isTemplate,
      'isPublic': instance.isPublic,
      'status': _$ReportStatusEnumMap[instance.status]!,
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$ReportStatusEnumMap = {
  ReportStatus.draft: 'draft',
  ReportStatus.active: 'active',
  ReportStatus.archived: 'archived',
  ReportStatus.scheduled: 'scheduled',
  ReportStatus.generating: 'generating',
  ReportStatus.completed: 'completed',
  ReportStatus.failed: 'failed',
};

ReportHistory _$ReportHistoryFromJson(Map<String, dynamic> json) =>
    ReportHistory(
      id: json['id'] as String,
      reportId: json['reportId'] as String,
      organizationId: json['organizationId'] as String,
      generatedBy: json['generatedBy'] as String?,
      generationStartedAt: DateTime.parse(
        json['generationStartedAt'] as String,
      ),
      generationCompletedAt: json['generationCompletedAt'] == null
          ? null
          : DateTime.parse(json['generationCompletedAt'] as String),
      filePath: json['filePath'] as String?,
      fileName: json['fileName'] as String?,
      fileFormat: $enumDecode(_$ExportFormatEnumMap, json['fileFormat']),
      fileSizeBytes: (json['fileSizeBytes'] as num?)?.toInt(),
      parameters: json['parameters'] as Map<String, dynamic>,
      status: $enumDecode(_$ExportStatusEnumMap, json['status']),
      errorMessage: json['errorMessage'] as String?,
      downloadCount: (json['downloadCount'] as num).toInt(),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$ReportHistoryToJson(
  ReportHistory instance,
) => <String, dynamic>{
  'id': instance.id,
  'reportId': instance.reportId,
  'organizationId': instance.organizationId,
  'generatedBy': instance.generatedBy,
  'generationStartedAt': instance.generationStartedAt.toIso8601String(),
  'generationCompletedAt': instance.generationCompletedAt?.toIso8601String(),
  'filePath': instance.filePath,
  'fileName': instance.fileName,
  'fileFormat': _$ExportFormatEnumMap[instance.fileFormat]!,
  'fileSizeBytes': instance.fileSizeBytes,
  'parameters': instance.parameters,
  'status': _$ExportStatusEnumMap[instance.status]!,
  'errorMessage': instance.errorMessage,
  'downloadCount': instance.downloadCount,
  'expiresAt': instance.expiresAt?.toIso8601String(),
  'createdAt': instance.createdAt.toIso8601String(),
};

const _$ExportFormatEnumMap = {
  ExportFormat.pdf: 'pdf',
  ExportFormat.excel: 'excel',
  ExportFormat.csv: 'csv',
  ExportFormat.json: 'json',
  ExportFormat.powerpoint: 'powerpoint',
};

const _$ExportStatusEnumMap = {
  ExportStatus.pending: 'pending',
  ExportStatus.processing: 'processing',
  ExportStatus.completed: 'completed',
  ExportStatus.failed: 'failed',
  ExportStatus.cancelled: 'cancelled',
};

DataExport _$DataExportFromJson(Map<String, dynamic> json) => DataExport(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  requestedBy: json['requestedBy'] as String,
  exportName: json['exportName'] as String,
  exportType: json['exportType'] as String,
  dataQuery: json['dataQuery'] as Map<String, dynamic>,
  exportFormat: $enumDecode(_$ExportFormatEnumMap, json['exportFormat']),
  filters: json['filters'] as Map<String, dynamic>,
  dateRange: json['dateRange'] as Map<String, dynamic>,
  filePath: json['filePath'] as String?,
  fileName: json['fileName'] as String?,
  fileSizeBytes: (json['fileSizeBytes'] as num?)?.toInt(),
  recordCount: (json['recordCount'] as num?)?.toInt(),
  status: $enumDecode(_$ExportStatusEnumMap, json['status']),
  progressPercentage: (json['progressPercentage'] as num).toInt(),
  errorMessage: json['errorMessage'] as String?,
  downloadToken: json['downloadToken'] as String?,
  downloadCount: (json['downloadCount'] as num).toInt(),
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  createdAt: DateTime.parse(json['createdAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
);

Map<String, dynamic> _$DataExportToJson(DataExport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'requestedBy': instance.requestedBy,
      'exportName': instance.exportName,
      'exportType': instance.exportType,
      'dataQuery': instance.dataQuery,
      'exportFormat': _$ExportFormatEnumMap[instance.exportFormat]!,
      'filters': instance.filters,
      'dateRange': instance.dateRange,
      'filePath': instance.filePath,
      'fileName': instance.fileName,
      'fileSizeBytes': instance.fileSizeBytes,
      'recordCount': instance.recordCount,
      'status': _$ExportStatusEnumMap[instance.status]!,
      'progressPercentage': instance.progressPercentage,
      'errorMessage': instance.errorMessage,
      'downloadToken': instance.downloadToken,
      'downloadCount': instance.downloadCount,
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
    };

ReportTemplate _$ReportTemplateFromJson(Map<String, dynamic> json) =>
    ReportTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      templateConfig: json['templateConfig'] as Map<String, dynamic>,
      defaultParameters: json['defaultParameters'] as Map<String, dynamic>,
      previewImageUrl: json['previewImageUrl'] as String?,
      isSystemTemplate: json['isSystemTemplate'] as bool,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$ReportTemplateToJson(ReportTemplate instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': instance.category,
      'templateConfig': instance.templateConfig,
      'defaultParameters': instance.defaultParameters,
      'previewImageUrl': instance.previewImageUrl,
      'isSystemTemplate': instance.isSystemTemplate,
      'tags': instance.tags,
      'createdAt': instance.createdAt.toIso8601String(),
    };
