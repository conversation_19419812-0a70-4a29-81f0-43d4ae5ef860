import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/quest/quest_bloc.dart';
import '../../widgets/common/responsive_builder.dart';
import '../../widgets/quests/quest_card_widget.dart';
import '../../widgets/quests/quest_filter_widget.dart';

/// Screen displaying list of user's quests
class QuestListScreen extends StatefulWidget {
  const QuestListScreen({super.key});

  @override
  State<QuestListScreen> createState() => _QuestListScreenState();
}

class _QuestListScreenState extends State<QuestListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  QuestStatus _selectedStatus = QuestStatus.active;
  QuestPriority? _selectedPriority;
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadQuests();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: ResponsiveBuilder(
        mobile: _buildMobileLayout,
        tablet: _buildTabletLayout,
        desktop: _buildDesktopLayout,
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewQuest,
        tooltip: 'Create New Quest',
        child: const Icon(Icons.add),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    if (_isSearching) {
      return AppBar(
        leading: IconButton(
          onPressed: () {
            setState(() {
              _isSearching = false;
              _searchController.clear();
              _searchQuery = '';
            });
            _loadQuests();
          },
          icon: const Icon(Icons.arrow_back),
        ),
        title: TextField(
          controller: _searchController,
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Search quests...',
            border: InputBorder.none,
          ),
          onChanged: (query) {
            setState(() {
              _searchQuery = query;
            });
            _searchQuests(query);
          },
        ),
        actions: [
          IconButton(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
              });
              _searchQuests('');
            },
            icon: const Icon(Icons.clear),
          ),
        ],
      );
    }

    return AppBar(
      title: const Text('Quests'),
      bottom: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: 'Active', icon: Icon(Icons.play_arrow)),
          Tab(text: 'Completed', icon: Icon(Icons.check_circle)),
          Tab(text: 'Paused', icon: Icon(Icons.pause)),
          Tab(text: 'All', icon: Icon(Icons.list)),
        ],
        onTap: (index) {
          setState(() {
            _selectedStatus = [
              QuestStatus.active,
              QuestStatus.completed,
              QuestStatus.paused,
              QuestStatus.active, // All - will be handled differently
            ][index];
          });
          _loadQuests();
        },
      ),
      actions: [
        IconButton(
          onPressed: () {
            setState(() {
              _isSearching = true;
            });
          },
          icon: const Icon(Icons.search),
          tooltip: 'Search quests',
        ),
        IconButton(
          onPressed: _showFilterDialog,
          icon: const Icon(Icons.filter_list),
          tooltip: 'Filter quests',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuSelection,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'sort',
              child: ListTile(
                leading: Icon(Icons.sort),
                title: Text('Sort'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'templates',
              child: ListTile(
                leading: Icon(Icons.template_outlined),
                title: Text('Templates'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'archived',
              child: ListTile(
                leading: Icon(Icons.archive),
                title: Text('Archived'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        if (_selectedPriority != null) _buildActiveFilters(context),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildQuestList(context, QuestStatus.active),
              _buildQuestList(context, QuestStatus.completed),
              _buildQuestList(context, QuestStatus.paused),
              _buildQuestList(context, null), // All quests
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Column(
            children: [
              QuestFilterWidget(
                selectedPriority: _selectedPriority,
                onPriorityChanged: (priority) {
                  setState(() {
                    _selectedPriority = priority;
                  });
                  _loadQuests();
                },
              ),
              Expanded(
                child: _buildQuestStats(context),
              ),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              if (_selectedPriority != null) _buildActiveFilters(context),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildQuestList(context, QuestStatus.active),
                    _buildQuestList(context, QuestStatus.completed),
                    _buildQuestList(context, QuestStatus.paused),
                    _buildQuestList(context, null),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 280,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Column(
            children: [
              QuestFilterWidget(
                selectedPriority: _selectedPriority,
                onPriorityChanged: (priority) {
                  setState(() {
                    _selectedPriority = priority;
                  });
                  _loadQuests();
                },
              ),
              Expanded(
                child: _buildQuestStats(context),
              ),
            ],
          ),
        ),
        Expanded(
          flex: 3,
          child: Column(
            children: [
              if (_selectedPriority != null) _buildActiveFilters(context),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildQuestList(context, QuestStatus.active),
                    _buildQuestList(context, QuestStatus.completed),
                    _buildQuestList(context, QuestStatus.paused),
                    _buildQuestList(context, null),
                  ],
                ),
              ),
            ],
          ),
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildQuestDetails(context),
        ),
      ],
    );
  }

  Widget _buildActiveFilters(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            'Filters:',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          if (_selectedPriority != null)
            Chip(
              label: Text(_selectedPriority!.name.toUpperCase()),
              onDeleted: () {
                setState(() {
                  _selectedPriority = null;
                });
                _loadQuests();
              },
              backgroundColor: _getPriorityColor(_selectedPriority!).withOpacity(0.2),
            ),
          const Spacer(),
          TextButton(
            onPressed: () {
              setState(() {
                _selectedPriority = null;
              });
              _loadQuests();
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestList(BuildContext context, QuestStatus? status) {
    return BlocBuilder<QuestBloc, QuestState>(
      builder: (context, state) {
        if (state is QuestLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is QuestError) {
          return _buildErrorState(context, state.message);
        } else if (state is QuestsLoaded) {
          final filteredQuests = _filterQuests(state.quests, status);
          
          if (filteredQuests.isEmpty) {
            return _buildEmptyState(context, status);
          }
          
          return ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: filteredQuests.length,
            itemBuilder: (context, index) {
              final quest = filteredQuests[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: QuestCardWidget(
                  quest: quest,
                  onTap: () => _openQuest(quest),
                  onStatusChanged: (newStatus) => _updateQuestStatus(quest, newStatus),
                  onPriorityChanged: (newPriority) => _updateQuestPriority(quest, newPriority),
                ),
              );
            },
          );
        }

        return _buildEmptyState(context, status);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, QuestStatus? status) {
    String title;
    String subtitle;
    IconData icon;

    switch (status) {
      case QuestStatus.completed:
        title = 'No completed quests';
        subtitle = 'Complete some quests to see them here!';
        icon = Icons.check_circle_outline;
        break;
      case QuestStatus.paused:
        title = 'No paused quests';
        subtitle = 'Paused quests will appear here';
        icon = Icons.pause_circle_outline;
        break;
      default:
        title = 'No quests yet';
        subtitle = 'Create your first quest to get started!';
        icon = Icons.explore_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          if (status == null || status == QuestStatus.active) ...[
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton.icon(
              onPressed: _createNewQuest,
              icon: const Icon(Icons.add),
              label: const Text('Create Quest'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load quests',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: _loadQuests,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestStats(BuildContext context) {
    return BlocBuilder<QuestBloc, QuestState>(
      builder: (context, state) {
        if (state is QuestsLoaded) {
          final activeCount = state.quests.where((q) => q.status == QuestStatus.active).length;
          final completedCount = state.quests.where((q) => q.status == QuestStatus.completed).length;
          final pausedCount = state.quests.where((q) => q.status == QuestStatus.paused).length;
          final totalCount = state.quests.length;

          return Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quest Statistics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildStatCard(context, 'Active', activeCount, Colors.blue),
                _buildStatCard(context, 'Completed', completedCount, Colors.green),
                _buildStatCard(context, 'Paused', pausedCount, Colors.orange),
                _buildStatCard(context, 'Total', totalCount, Colors.grey),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildStatCard(BuildContext context, String label, int count, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            Text(
              count.toString(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestDetails(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quest Details',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          const Center(
            child: Text('Select a quest to view details'),
          ),
        ],
      ),
    );
  }

  List<Quest> _filterQuests(List<Quest> quests, QuestStatus? status) {
    var filtered = quests;

    // Filter by status
    if (status != null) {
      filtered = filtered.where((quest) => quest.status == status).toList();
    }

    // Filter by priority
    if (_selectedPriority != null) {
      filtered = filtered.where((quest) => quest.priority == _selectedPriority).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((quest) =>
        quest.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        quest.description.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }

    return filtered;
  }

  Color _getPriorityColor(QuestPriority priority) {
    switch (priority) {
      case QuestPriority.low:
        return Colors.green;
      case QuestPriority.medium:
        return Colors.orange;
      case QuestPriority.high:
        return Colors.red;
      case QuestPriority.urgent:
        return Colors.purple;
    }
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreQuests();
      }
    });
  }

  void _loadQuests() {
    context.read<QuestBloc>().add(const LoadQuests());
  }

  void _loadMoreQuests() {
    // TODO: Implement pagination
  }

  void _searchQuests(String query) {
    // Search is handled in _filterQuests method
    setState(() {});
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Quests'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<QuestPriority?>(
              value: _selectedPriority,
              decoration: const InputDecoration(
                labelText: 'Priority',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Priorities')),
                ...QuestPriority.values.map((priority) =>
                  DropdownMenuItem(
                    value: priority,
                    child: Text(priority.name.toUpperCase()),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadQuests();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'sort':
        _showSortDialog();
        break;
      case 'templates':
        _showTemplates();
        break;
      case 'archived':
        _showArchivedQuests();
        break;
    }
  }

  void _showSortDialog() {
    // TODO: Implement sort dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sort feature coming soon!')),
    );
  }

  void _showTemplates() {
    // TODO: Navigate to templates screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Templates feature coming soon!')),
    );
  }

  void _showArchivedQuests() {
    // TODO: Navigate to archived quests screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Archived quests feature coming soon!')),
    );
  }

  void _createNewQuest() {
    Navigator.of(context).pushNamed('/quest/create');
  }

  void _openQuest(Quest quest) {
    Navigator.of(context).pushNamed(
      '/quest/details',
      arguments: {'questId': quest.id},
    );
  }

  void _updateQuestStatus(Quest quest, QuestStatus newStatus) {
    context.read<QuestBloc>().add(UpdateQuestStatus(
      questId: quest.id,
      status: newStatus,
    ));
  }

  void _updateQuestPriority(Quest quest, QuestPriority newPriority) {
    context.read<QuestBloc>().add(UpdateQuestPriority(
      questId: quest.id,
      priority: newPriority,
    ));
  }
}
