// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'threat_detection.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SecurityThreat _$SecurityThreatFromJson(Map<String, dynamic> json) =>
    SecurityThreat(
      id: json['id'] as String,
      organizationId: json['organization_id'] as String,
      userId: json['user_id'] as String?,
      threatType: $enumDecode(_$ThreatTypeEnumMap, json['threat_type']),
      severity: $enumDecode(_$ThreatSeverityEnumMap, json['severity']),
      title: json['title'] as String,
      description: json['description'] as String,
      sourceIp: json['source_ip'] as String?,
      userAgent: json['user_agent'] as String?,
      geolocation: json['geolocation'] as Map<String, dynamic>?,
      indicators: (json['threat_indicators'] as List<dynamic>)
          .map((e) => ThreatIndicator.fromJson(e as Map<String, dynamic>))
          .toList(),
      riskScore: (json['risk_score'] as num).toDouble(),
      confidenceScore: (json['confidence_score'] as num).toDouble(),
      status:
          $enumDecodeNullable(_$ThreatStatusEnumMap, json['status']) ??
          ThreatStatus.detected,
      mitigationActions:
          (json['mitigation_actions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      assignedTo: json['assigned_to'] as String?,
      resolvedAt: json['resolved_at'] == null
          ? null
          : DateTime.parse(json['resolved_at'] as String),
      detectedAt: DateTime.parse(json['detected_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$SecurityThreatToJson(SecurityThreat instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organization_id': instance.organizationId,
      'user_id': instance.userId,
      'threat_type': _$ThreatTypeEnumMap[instance.threatType]!,
      'severity': _$ThreatSeverityEnumMap[instance.severity]!,
      'title': instance.title,
      'description': instance.description,
      'source_ip': instance.sourceIp,
      'user_agent': instance.userAgent,
      'geolocation': instance.geolocation,
      'threat_indicators': instance.indicators,
      'risk_score': instance.riskScore,
      'confidence_score': instance.confidenceScore,
      'status': _$ThreatStatusEnumMap[instance.status]!,
      'mitigation_actions': instance.mitigationActions,
      'assigned_to': instance.assignedTo,
      'resolved_at': instance.resolvedAt?.toIso8601String(),
      'detected_at': instance.detectedAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$ThreatTypeEnumMap = {
  ThreatType.suspiciousLogin: 'suspiciousLogin',
  ThreatType.bruteForce: 'bruteForce',
  ThreatType.anomalousAccess: 'anomalousAccess',
  ThreatType.dataExfiltration: 'dataExfiltration',
  ThreatType.privilegeEscalation: 'privilegeEscalation',
  ThreatType.malwareDetection: 'malwareDetection',
  ThreatType.geolocationAnomaly: 'geolocationAnomaly',
  ThreatType.deviceAnomaly: 'deviceAnomaly',
  ThreatType.behaviorAnomaly: 'behaviorAnomaly',
  ThreatType.apiAbuse: 'apiAbuse',
};

const _$ThreatSeverityEnumMap = {
  ThreatSeverity.low: 'low',
  ThreatSeverity.medium: 'medium',
  ThreatSeverity.high: 'high',
  ThreatSeverity.critical: 'critical',
};

const _$ThreatStatusEnumMap = {
  ThreatStatus.detected: 'detected',
  ThreatStatus.investigating: 'investigating',
  ThreatStatus.mitigated: 'mitigated',
  ThreatStatus.resolved: 'resolved',
  ThreatStatus.falsePositive: 'falsePositive',
};

ThreatIndicator _$ThreatIndicatorFromJson(Map<String, dynamic> json) =>
    ThreatIndicator(
      type: json['type'] as String,
      value: json['value'] as String,
      severity: $enumDecode(_$ThreatSeverityEnumMap, json['severity']),
      description: json['description'] as String,
      confidence: (json['confidence'] as num).toDouble(),
    );

Map<String, dynamic> _$ThreatIndicatorToJson(ThreatIndicator instance) =>
    <String, dynamic>{
      'type': instance.type,
      'value': instance.value,
      'severity': _$ThreatSeverityEnumMap[instance.severity]!,
      'description': instance.description,
      'confidence': instance.confidence,
    };

ThreatDetectionConfig _$ThreatDetectionConfigFromJson(
  Map<String, dynamic> json,
) => ThreatDetectionConfig(
  organizationId: json['organization_id'] as String,
  enabled: json['enabled'] as bool? ?? true,
  sensitivityLevel: (json['sensitivity_level'] as num?)?.toDouble() ?? 0.7,
  autoMitigationEnabled: json['auto_mitigation_enabled'] as bool? ?? false,
  notificationSettings: NotificationSettings.fromJson(
    json['notification_settings'] as Map<String, dynamic>,
  ),
  detectionRules:
      (json['detection_rules'] as List<dynamic>?)
          ?.map((e) => DetectionRule.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  whitelistIPs:
      (json['whitelist_ips'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  blacklistIPs:
      (json['blacklist_ips'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  geofencingEnabled: json['geofencing_enabled'] as bool? ?? false,
  allowedCountries:
      (json['allowed_countries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  behavioralAnalysisEnabled:
      json['behavioral_analysis_enabled'] as bool? ?? true,
  mlDetectionEnabled: json['ml_detection_enabled'] as bool? ?? false,
);

Map<String, dynamic> _$ThreatDetectionConfigToJson(
  ThreatDetectionConfig instance,
) => <String, dynamic>{
  'organization_id': instance.organizationId,
  'enabled': instance.enabled,
  'sensitivity_level': instance.sensitivityLevel,
  'auto_mitigation_enabled': instance.autoMitigationEnabled,
  'notification_settings': instance.notificationSettings,
  'detection_rules': instance.detectionRules,
  'whitelist_ips': instance.whitelistIPs,
  'blacklist_ips': instance.blacklistIPs,
  'geofencing_enabled': instance.geofencingEnabled,
  'allowed_countries': instance.allowedCountries,
  'behavioral_analysis_enabled': instance.behavioralAnalysisEnabled,
  'ml_detection_enabled': instance.mlDetectionEnabled,
};

NotificationSettings _$NotificationSettingsFromJson(
  Map<String, dynamic> json,
) => NotificationSettings(
  emailEnabled: json['email_enabled'] as bool? ?? true,
  emailRecipients:
      (json['email_recipients'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  slackEnabled: json['slack_enabled'] as bool? ?? false,
  slackWebhook: json['slack_webhook'] as String?,
  smsEnabled: json['sms_enabled'] as bool? ?? false,
  smsNumbers:
      (json['sms_numbers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  severityThreshold:
      $enumDecodeNullable(
        _$ThreatSeverityEnumMap,
        json['severity_threshold'],
      ) ??
      ThreatSeverity.medium,
);

Map<String, dynamic> _$NotificationSettingsToJson(
  NotificationSettings instance,
) => <String, dynamic>{
  'email_enabled': instance.emailEnabled,
  'email_recipients': instance.emailRecipients,
  'slack_enabled': instance.slackEnabled,
  'slack_webhook': instance.slackWebhook,
  'sms_enabled': instance.smsEnabled,
  'sms_numbers': instance.smsNumbers,
  'severity_threshold': _$ThreatSeverityEnumMap[instance.severityThreshold]!,
};

DetectionRule _$DetectionRuleFromJson(Map<String, dynamic> json) =>
    DetectionRule(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      ruleType: json['rule_type'] as String,
      conditions: json['conditions'] as Map<String, dynamic>,
      actions: (json['actions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      severity: $enumDecode(_$ThreatSeverityEnumMap, json['severity']),
      enabled: json['enabled'] as bool? ?? true,
    );

Map<String, dynamic> _$DetectionRuleToJson(DetectionRule instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'rule_type': instance.ruleType,
      'conditions': instance.conditions,
      'actions': instance.actions,
      'severity': _$ThreatSeverityEnumMap[instance.severity]!,
      'enabled': instance.enabled,
    };

ThreatDetectionStats _$ThreatDetectionStatsFromJson(
  Map<String, dynamic> json,
) => ThreatDetectionStats(
  organizationId: json['organization_id'] as String,
  totalThreats: (json['total_threats'] as num).toInt(),
  activeThreats: (json['active_threats'] as num).toInt(),
  criticalThreats: (json['critical_threats'] as num).toInt(),
  threatsByType: Map<String, int>.from(json['threats_by_type'] as Map),
  threatsBySeverity: Map<String, int>.from(json['threats_by_severity'] as Map),
  averageResponseTime: Duration(
    microseconds: (json['average_response_time'] as num).toInt(),
  ),
  falsePositiveRate: (json['false_positive_rate'] as num).toDouble(),
  detectionAccuracy: (json['detection_accuracy'] as num).toDouble(),
  generatedAt: DateTime.parse(json['generated_at'] as String),
);

Map<String, dynamic> _$ThreatDetectionStatsToJson(
  ThreatDetectionStats instance,
) => <String, dynamic>{
  'organization_id': instance.organizationId,
  'total_threats': instance.totalThreats,
  'active_threats': instance.activeThreats,
  'critical_threats': instance.criticalThreats,
  'threats_by_type': instance.threatsByType,
  'threats_by_severity': instance.threatsBySeverity,
  'average_response_time': instance.averageResponseTime.inMicroseconds,
  'false_positive_rate': instance.falsePositiveRate,
  'detection_accuracy': instance.detectionAccuracy,
  'generated_at': instance.generatedAt.toIso8601String(),
};
