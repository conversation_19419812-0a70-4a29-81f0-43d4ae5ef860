import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'custom_report.g.dart';

/// Report status enumeration
enum ReportStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('active')
  active,
  @JsonValue('archived')
  archived,
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('generating')
  generating,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
}

/// Export format enumeration
enum ExportFormat {
  @JsonValue('pdf')
  pdf,
  @JsonValue('excel')
  excel,
  @JsonValue('csv')
  csv,
  @JsonValue('json')
  json,
  @JsonValue('powerpoint')
  powerpoint,
}

/// Export status enumeration
enum ExportStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('processing')
  processing,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('cancelled')
  cancelled,
}

/// Custom report configuration model
@JsonSerializable()
class CustomReport extends Equatable {
  /// Unique report identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// Report name
  final String name;

  /// Report description
  final String? description;

  /// Report type (dashboard, table, chart, etc.)
  final String reportType;

  /// Query configuration for data retrieval
  final Map<String, dynamic> queryConfig;

  /// Visualization configuration
  final Map<String, dynamic> visualizationConfig;

  /// Layout configuration
  final Map<String, dynamic> layoutConfig;

  /// Filter configuration
  final Map<String, dynamic> filterConfig;

  /// Schedule configuration for automated reports
  final Map<String, dynamic>? scheduleConfig;

  /// Access permissions
  final Map<String, dynamic> permissions;

  /// Tags for categorization
  final List<String> tags;

  /// Whether this is a template
  final bool isTemplate;

  /// Whether this is public
  final bool isPublic;

  /// Current status
  final ReportStatus status;

  /// Creator user ID
  final String createdBy;

  /// Last updater user ID
  final String? updatedBy;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const CustomReport({
    required this.id,
    required this.organizationId,
    required this.name,
    this.description,
    required this.reportType,
    required this.queryConfig,
    required this.visualizationConfig,
    required this.layoutConfig,
    required this.filterConfig,
    this.scheduleConfig,
    required this.permissions,
    required this.tags,
    required this.isTemplate,
    required this.isPublic,
    required this.status,
    required this.createdBy,
    this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create CustomReport from JSON
  factory CustomReport.fromJson(Map<String, dynamic> json) =>
      _$CustomReportFromJson(json);

  /// Convert CustomReport to JSON
  Map<String, dynamic> toJson() => _$CustomReportToJson(this);

  /// Create a copy with updated fields
  CustomReport copyWith({
    String? id,
    String? organizationId,
    String? name,
    String? description,
    String? reportType,
    Map<String, dynamic>? queryConfig,
    Map<String, dynamic>? visualizationConfig,
    Map<String, dynamic>? layoutConfig,
    Map<String, dynamic>? filterConfig,
    Map<String, dynamic>? scheduleConfig,
    Map<String, dynamic>? permissions,
    List<String>? tags,
    bool? isTemplate,
    bool? isPublic,
    ReportStatus? status,
    String? createdBy,
    String? updatedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomReport(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      name: name ?? this.name,
      description: description ?? this.description,
      reportType: reportType ?? this.reportType,
      queryConfig: queryConfig ?? this.queryConfig,
      visualizationConfig: visualizationConfig ?? this.visualizationConfig,
      layoutConfig: layoutConfig ?? this.layoutConfig,
      filterConfig: filterConfig ?? this.filterConfig,
      scheduleConfig: scheduleConfig ?? this.scheduleConfig,
      permissions: permissions ?? this.permissions,
      tags: tags ?? this.tags,
      isTemplate: isTemplate ?? this.isTemplate,
      isPublic: isPublic ?? this.isPublic,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if report has scheduling enabled
  bool get isScheduled => scheduleConfig != null && scheduleConfig!.isNotEmpty;

  /// Get display name with type information
  String get displayName => '$name ($reportType)';

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        description,
        reportType,
        queryConfig,
        visualizationConfig,
        layoutConfig,
        filterConfig,
        scheduleConfig,
        permissions,
        tags,
        isTemplate,
        isPublic,
        status,
        createdBy,
        updatedBy,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Report generation history entry
@JsonSerializable()
class ReportHistory extends Equatable {
  /// Unique history entry identifier
  final String id;

  /// Associated report ID
  final String reportId;

  /// Organization identifier
  final String organizationId;

  /// User who generated the report
  final String? generatedBy;

  /// When generation started
  final DateTime generationStartedAt;

  /// When generation completed
  final DateTime? generationCompletedAt;

  /// File path in storage
  final String? filePath;

  /// Generated file name
  final String? fileName;

  /// Export format used
  final ExportFormat fileFormat;

  /// File size in bytes
  final int? fileSizeBytes;

  /// Parameters used for generation
  final Map<String, dynamic> parameters;

  /// Generation status
  final ExportStatus status;

  /// Error message if failed
  final String? errorMessage;

  /// Number of times downloaded
  final int downloadCount;

  /// When the file expires
  final DateTime? expiresAt;

  /// Creation timestamp
  final DateTime createdAt;

  const ReportHistory({
    required this.id,
    required this.reportId,
    required this.organizationId,
    this.generatedBy,
    required this.generationStartedAt,
    this.generationCompletedAt,
    this.filePath,
    this.fileName,
    required this.fileFormat,
    this.fileSizeBytes,
    required this.parameters,
    required this.status,
    this.errorMessage,
    required this.downloadCount,
    this.expiresAt,
    required this.createdAt,
  });

  /// Create ReportHistory from JSON
  factory ReportHistory.fromJson(Map<String, dynamic> json) =>
      _$ReportHistoryFromJson(json);

  /// Convert ReportHistory to JSON
  Map<String, dynamic> toJson() => _$ReportHistoryToJson(this);

  /// Get generation duration
  Duration? get generationDuration {
    if (generationCompletedAt == null) return null;
    return generationCompletedAt!.difference(generationStartedAt);
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSizeBytes == null) return 'Unknown';
    
    final bytes = fileSizeBytes!;
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Check if file has expired
  bool get hasExpired {
    if (expiresAt == null) return false;
    return DateTime.now().toUtc().isAfter(expiresAt!);
  }

  /// Check if report is still being generated
  bool get isGenerating => status == ExportStatus.processing;

  @override
  List<Object?> get props => [
        id,
        reportId,
        organizationId,
        generatedBy,
        generationStartedAt,
        generationCompletedAt,
        filePath,
        fileName,
        fileFormat,
        fileSizeBytes,
        parameters,
        status,
        errorMessage,
        downloadCount,
        expiresAt,
        createdAt,
      ];

  @override
  bool get stringify => true;
}

/// Data export request model
@JsonSerializable()
class DataExport extends Equatable {
  /// Unique export identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// User who requested the export
  final String requestedBy;

  /// Export name/title
  final String exportName;

  /// Type of export (analytics_events, user_data, etc.)
  final String exportType;

  /// Query for data selection
  final Map<String, dynamic> dataQuery;

  /// Export format
  final ExportFormat exportFormat;

  /// Applied filters
  final Map<String, dynamic> filters;

  /// Date range for export
  final Map<String, dynamic> dateRange;

  /// File path in storage
  final String? filePath;

  /// Generated file name
  final String? fileName;

  /// File size in bytes
  final int? fileSizeBytes;

  /// Number of records exported
  final int? recordCount;

  /// Export status
  final ExportStatus status;

  /// Progress percentage (0-100)
  final int progressPercentage;

  /// Error message if failed
  final String? errorMessage;

  /// Download token for secure access
  final String? downloadToken;

  /// Number of times downloaded
  final int downloadCount;

  /// When the export expires
  final DateTime? expiresAt;

  /// Creation timestamp
  final DateTime createdAt;

  /// Completion timestamp
  final DateTime? completedAt;

  const DataExport({
    required this.id,
    required this.organizationId,
    required this.requestedBy,
    required this.exportName,
    required this.exportType,
    required this.dataQuery,
    required this.exportFormat,
    required this.filters,
    required this.dateRange,
    this.filePath,
    this.fileName,
    this.fileSizeBytes,
    this.recordCount,
    required this.status,
    required this.progressPercentage,
    this.errorMessage,
    this.downloadToken,
    required this.downloadCount,
    this.expiresAt,
    required this.createdAt,
    this.completedAt,
  });

  /// Create DataExport from JSON
  factory DataExport.fromJson(Map<String, dynamic> json) =>
      _$DataExportFromJson(json);

  /// Convert DataExport to JSON
  Map<String, dynamic> toJson() => _$DataExportToJson(this);

  /// Check if export is complete
  bool get isComplete => status == ExportStatus.completed;

  /// Check if export is in progress
  bool get isInProgress => status == ExportStatus.processing;

  /// Check if export failed
  bool get hasFailed => status == ExportStatus.failed;

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSizeBytes == null) return 'Unknown';
    
    final bytes = fileSizeBytes!;
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Get processing duration
  Duration? get processingDuration {
    if (completedAt == null) return null;
    return completedAt!.difference(createdAt);
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        requestedBy,
        exportName,
        exportType,
        dataQuery,
        exportFormat,
        filters,
        dateRange,
        filePath,
        fileName,
        fileSizeBytes,
        recordCount,
        status,
        progressPercentage,
        errorMessage,
        downloadToken,
        downloadCount,
        expiresAt,
        createdAt,
        completedAt,
      ];

  @override
  bool get stringify => true;
}

/// Report template model
@JsonSerializable()
class ReportTemplate extends Equatable {
  /// Template identifier
  final String id;

  /// Template name
  final String name;

  /// Template description
  final String description;

  /// Template category
  final String category;

  /// Template configuration
  final Map<String, dynamic> templateConfig;

  /// Default parameters
  final Map<String, dynamic> defaultParameters;

  /// Preview image URL
  final String? previewImageUrl;

  /// Whether this is a system template
  final bool isSystemTemplate;

  /// Template tags
  final List<String> tags;

  /// Creation timestamp
  final DateTime createdAt;

  const ReportTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.templateConfig,
    required this.defaultParameters,
    this.previewImageUrl,
    required this.isSystemTemplate,
    required this.tags,
    required this.createdAt,
  });

  /// Create ReportTemplate from JSON
  factory ReportTemplate.fromJson(Map<String, dynamic> json) =>
      _$ReportTemplateFromJson(json);

  /// Convert ReportTemplate to JSON
  Map<String, dynamic> toJson() => _$ReportTemplateToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        templateConfig,
        defaultParameters,
        previewImageUrl,
        isSystemTemplate,
        tags,
        createdAt,
      ];

  @override
  bool get stringify => true;
}