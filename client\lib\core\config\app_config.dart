/// Application configuration constants
class AppConfig {
  // Server Configuration
  static const String serverHost = 'localhost';
  static const int serverPort = 8080;
  static const String baseUrl = 'http://$serverHost:$serverPort';
  
  // API Endpoints based on server routes
  static const String apiV1 = '/api/v1';
  
  // Authentication endpoints
  static const String authEndpoint = '/auth';
  static const String loginEndpoint = '$authEndpoint/login';
  static const String registerEndpoint = '$authEndpoint/register';
  static const String logoutEndpoint = '$authEndpoint/logout';
  static const String refreshTokenEndpoint = '$authEndpoint/refresh-token';
  
  // Gamification endpoints
  static const String gamificationEndpoint = '$apiV1/gamification';
  static const String userPointsEndpoint = '$gamificationEndpoint/user';
  static const String achievementsEndpoint = '$gamificationEndpoint/achievements';
  static const String leaderboardEndpoint = '$gamificationEndpoint/leaderboard';
  static const String rewardsEndpoint = '$gamificationEndpoint/rewards';
  
  // Freelancing endpoints
  static const String freelancingEndpoint = '$apiV1/freelancing';
  static const String projectsEndpoint = '$freelancingEndpoint/projects';
  static const String freelancersEndpoint = '$freelancingEndpoint/freelancers';
  static const String proposalsEndpoint = '$freelancingEndpoint/proposals';
  static const String contractsEndpoint = '$freelancingEndpoint/contracts';
  
  // Learning endpoints
  static const String learningEndpoint = '$apiV1/learning';
  static const String coursesEndpoint = '$learningEndpoint/courses';
  static const String lessonsEndpoint = '$learningEndpoint/lessons';
  static const String enrollmentsEndpoint = '$learningEndpoint/enrollments';
  static const String certificatesEndpoint = '$learningEndpoint/certificates';
  
  // Analytics endpoints
  static const String analyticsEndpoint = '$apiV1/analytics';
  static const String eventsEndpoint = '$analyticsEndpoint/events';
  static const String dashboardEndpoint = '$analyticsEndpoint/dashboard';
  static const String metricsEndpoint = '$analyticsEndpoint/metrics';
  static const String insightsEndpoint = '$analyticsEndpoint/insights';
  static const String reportsEndpoint = '$analyticsEndpoint/reports';
  
  // Collaboration endpoints
  static const String collaborationEndpoint = '$apiV1/collaboration';
  static const String teamQuestEndpoint = '$collaborationEndpoint/team-quest';
  static const String messagesEndpoint = '$collaborationEndpoint/messages';
  
  // Enterprise endpoints
  static const String enterpriseEndpoint = '$apiV1/enterprise';
  static const String organizationsEndpoint = '$enterpriseEndpoint/organizations';
  static const String membersEndpoint = '$enterpriseEndpoint/members';
  static const String rolesEndpoint = '$enterpriseEndpoint/roles';
  static const String enterpriseAnalyticsEndpoint = '$enterpriseEndpoint/analytics';
  
  // Advanced auth endpoints
  static const String verifyEmailEndpoint = '$authEndpoint/verify-email';
  static const String resendVerificationEndpoint = '$authEndpoint/resend-verification';
  static const String forgotPasswordEndpoint = '$authEndpoint/forgot-password';
  static const String resetPasswordEndpoint = '$authEndpoint/reset-password';
  static const String changePasswordEndpoint = '$authEndpoint/change-password';
  static const String twoFactorEndpoint = '$authEndpoint/2fa';
  static const String oauthEndpoint = '$authEndpoint/oauth';
  static const String sessionsEndpoint = '$authEndpoint/sessions';
  
  // Monitoring endpoints
  static const String monitoringEndpoint = '/monitoring';
  static const String performanceEndpoint = '$monitoringEndpoint/performance';
  static const String healthCheckEndpoint = '$monitoringEndpoint/health';
  static const String systemInfoEndpoint = '$monitoringEndpoint/admin/system/info';

  // WebSocket endpoints
  static const String wsEndpoint = '/ws';
  static const String gamificationWsEndpoint = '$gamificationEndpoint/ws';
  
  // App Configuration
  static const String appName = 'Quester';
  static const String appVersion = '1.0.0';
  
  // Routes
  static const String homeRoute = '/';
  static const String authRoute = '/auth';
  static const String loginRoute = '/auth/login';
  static const String registerRoute = '/auth/register';
  static const String gamificationRoute = '/gamification';
  static const String freelancingRoute = '/freelancing';
  static const String learningRoute = '/learning';
  static const String analyticsRoute = '/analytics';
  static const String collaborationRoute = '/collaboration';
  static const String enterpriseRoute = '/enterprise';
  static const String profileRoute = '/profile';
  static const String settingsRoute = '/settings';
}