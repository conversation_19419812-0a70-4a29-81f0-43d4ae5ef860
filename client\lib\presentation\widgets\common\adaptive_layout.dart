import 'package:flutter/material.dart';

/// Responsive breakpoints for adaptive layouts
class Breakpoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
  static const double largeDesktop = 1600;
}

/// Device type enumeration
enum DeviceType { mobile, tablet, desktop, largeDesktop }

/// Adaptive layout builder that responds to screen size changes
class AdaptiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? largeDesktop;
  final EdgeInsets? padding;
  final bool useIntrinsicHeight;

  const AdaptiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
    this.padding,
    this.useIntrinsicHeight = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = _getDeviceType(screenWidth);
    
    Widget child;
    switch (deviceType) {
      case DeviceType.largeDesktop:
        child = largeDesktop ?? desktop ?? tablet ?? mobile;
        break;
      case DeviceType.desktop:
        child = desktop ?? tablet ?? mobile;
        break;
      case DeviceType.tablet:
        child = tablet ?? mobile;
        break;
      case DeviceType.mobile:
        child = mobile;
        break;
    }

    if (padding != null) {
      child = Padding(padding: padding!, child: child);
    }

    if (useIntrinsicHeight) {
      child = IntrinsicHeight(child: child);
    }

    return child;
  }

  static DeviceType _getDeviceType(double width) {
    if (width >= Breakpoints.largeDesktop) return DeviceType.largeDesktop;
    if (width >= Breakpoints.desktop) return DeviceType.desktop;
    if (width >= Breakpoints.tablet) return DeviceType.tablet;
    return DeviceType.mobile;
  }

  static DeviceType getDeviceType(BuildContext context) {
    return _getDeviceType(MediaQuery.of(context).size.width);
  }

  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  static bool isDesktop(BuildContext context) {
    final type = getDeviceType(context);
    return type == DeviceType.desktop || type == DeviceType.largeDesktop;
  }
}

/// Adaptive grid that adjusts column count based on screen size
class AdaptiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double runSpacing;
  final double childAspectRatio;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const AdaptiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
    this.childAspectRatio = 1.0,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = AdaptiveLayout.getDeviceType(context);
    
    int columns;
    switch (deviceType) {
      case DeviceType.largeDesktop:
        columns = desktopColumns + 1;
        break;
      case DeviceType.desktop:
        columns = desktopColumns;
        break;
      case DeviceType.tablet:
        columns = tabletColumns;
        break;
      case DeviceType.mobile:
        columns = mobileColumns;
        break;
    }

    return GridView.builder(
      padding: padding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// Adaptive spacing that scales with screen size
class AdaptiveSpacing {
  static double small(BuildContext context) {
    final deviceType = AdaptiveLayout.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.largeDesktop:
        return 12.0;
      case DeviceType.desktop:
        return 10.0;
      case DeviceType.tablet:
        return 8.0;
      case DeviceType.mobile:
        return 6.0;
    }
  }

  static double medium(BuildContext context) {
    final deviceType = AdaptiveLayout.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.largeDesktop:
        return 24.0;
      case DeviceType.desktop:
        return 20.0;
      case DeviceType.tablet:
        return 16.0;
      case DeviceType.mobile:
        return 12.0;
    }
  }

  static double large(BuildContext context) {
    final deviceType = AdaptiveLayout.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.largeDesktop:
        return 48.0;
      case DeviceType.desktop:
        return 40.0;
      case DeviceType.tablet:
        return 32.0;
      case DeviceType.mobile:
        return 24.0;
    }
  }

  static EdgeInsets padding(BuildContext context) {
    final deviceType = AdaptiveLayout.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(32.0);
      case DeviceType.desktop:
        return const EdgeInsets.all(24.0);
      case DeviceType.tablet:
        return const EdgeInsets.all(20.0);
      case DeviceType.mobile:
        return const EdgeInsets.all(16.0);
    }
  }

  static EdgeInsets horizontalPadding(BuildContext context) {
    final deviceType = AdaptiveLayout.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.largeDesktop:
        return const EdgeInsets.symmetric(horizontal: 48.0);
      case DeviceType.desktop:
        return const EdgeInsets.symmetric(horizontal: 32.0);
      case DeviceType.tablet:
        return const EdgeInsets.symmetric(horizontal: 24.0);
      case DeviceType.mobile:
        return const EdgeInsets.symmetric(horizontal: 16.0);
    }
  }
}

/// Adaptive container with max width constraints
class AdaptiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final Decoration? decoration;

  const AdaptiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = AdaptiveLayout.getDeviceType(context);
    
    double? containerMaxWidth = maxWidth;
    if (containerMaxWidth == null) {
      switch (deviceType) {
        case DeviceType.largeDesktop:
          containerMaxWidth = 1400;
          break;
        case DeviceType.desktop:
          containerMaxWidth = 1200;
          break;
        case DeviceType.tablet:
          containerMaxWidth = 800;
          break;
        case DeviceType.mobile:
          containerMaxWidth = null;
          break;
      }
    }

    Widget container = Container(
      padding: padding ?? AdaptiveSpacing.padding(context),
      margin: margin,
      color: color,
      decoration: decoration,
      child: child,
    );

    if (containerMaxWidth != null) {
      container = Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: containerMaxWidth),
          child: container,
        ),
      );
    }

    return container;
  }
}
