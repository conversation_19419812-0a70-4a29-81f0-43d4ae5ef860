import 'package:json_annotation/json_annotation.dart';

part 'backup_code_models.g.dart';

@JsonSerializable()
class BackupCode {
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'code_hash')
  final String codeHash;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON><PERSON>(name: 'used_at')
  final DateTime? usedAt;
  @Json<PERSON><PERSON>(name: 'is_used')
  final bool isUsed;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'revoked_at')
  final DateTime? revokedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'revocation_reason')
  final String? revocationReason;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ip_address')
  final String? ipAddress;
  @Json<PERSON>ey(name: 'user_agent')
  final String? userAgent;
  @Json<PERSON>ey(name: 'used_ip_address')
  final String? usedIpAddress;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'used_user_agent')
  final String? usedUserAgent;

  const BackupCode({
    required this.id,
    required this.userId,
    required this.codeHash,
    required this.createdAt,
    this.usedAt,
    this.isUsed = false,
    this.isActive = true,
    this.revokedAt,
    this.revocationReason,
    this.ipAddress,
    this.userAgent,
    this.usedIpAddress,
    this.usedUserAgent,
  });

  factory BackupCode.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeToJson(this);

  BackupCode copyWith({
    String? id,
    String? userId,
    String? codeHash,
    DateTime? createdAt,
    DateTime? usedAt,
    bool? isUsed,
    bool? isActive,
    DateTime? revokedAt,
    String? revocationReason,
    String? ipAddress,
    String? userAgent,
    String? usedIpAddress,
    String? usedUserAgent,
  }) {
    return BackupCode(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      codeHash: codeHash ?? this.codeHash,
      createdAt: createdAt ?? this.createdAt,
      usedAt: usedAt ?? this.usedAt,
      isUsed: isUsed ?? this.isUsed,
      isActive: isActive ?? this.isActive,
      revokedAt: revokedAt ?? this.revokedAt,
      revocationReason: revocationReason ?? this.revocationReason,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      usedIpAddress: usedIpAddress ?? this.usedIpAddress,
      usedUserAgent: usedUserAgent ?? this.usedUserAgent,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BackupCode && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@JsonSerializable()
class BackupCodeSet {
  final List<String> codes;
  @JsonKey(name: 'generated_at')
  final DateTime generatedAt;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'remaining_codes')
  final int remainingCodes;
  @JsonKey(name: 'display_hint')
  final String? displayHint;

  const BackupCodeSet({
    required this.codes,
    required this.generatedAt,
    required this.userId,
    required this.remainingCodes,
    this.displayHint,
  });

  factory BackupCodeSet.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeSetFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeSetToJson(this);

  BackupCodeSet copyWith({
    List<String>? codes,
    DateTime? generatedAt,
    String? userId,
    int? remainingCodes,
    String? displayHint,
  }) {
    return BackupCodeSet(
      codes: codes ?? this.codes,
      generatedAt: generatedAt ?? this.generatedAt,
      userId: userId ?? this.userId,
      remainingCodes: remainingCodes ?? this.remainingCodes,
      displayHint: displayHint ?? this.displayHint,
    );
  }
}

@JsonSerializable()
class BackupCodeValidationResult {
  @JsonKey(name: 'is_valid')
  final bool isValid;
  @JsonKey(name: 'backup_code_id')
  final String? backupCodeId;
  @JsonKey(name: 'error_message')
  final String? errorMessage;
  @JsonKey(name: 'warning_message')
  final String? warningMessage;
  final Map<String, dynamic> metadata;
  @JsonKey(name: 'validated_at')
  final DateTime validatedAt;
  @JsonKey(name: 'remaining_codes')
  final int? remainingCodes;
  @JsonKey(name: 'code_consumed')
  final bool codeConsumed;

  const BackupCodeValidationResult({
    required this.isValid,
    this.backupCodeId,
    this.errorMessage,
    this.warningMessage,
    this.metadata = const {},
    required this.validatedAt,
    this.remainingCodes,
    this.codeConsumed = false,
  });

  factory BackupCodeValidationResult.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeValidationResultFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeValidationResultToJson(this);

  BackupCodeValidationResult copyWith({
    bool? isValid,
    String? backupCodeId,
    String? errorMessage,
    String? warningMessage,
    Map<String, dynamic>? metadata,
    DateTime? validatedAt,
    int? remainingCodes,
    bool? codeConsumed,
  }) {
    return BackupCodeValidationResult(
      isValid: isValid ?? this.isValid,
      backupCodeId: backupCodeId ?? this.backupCodeId,
      errorMessage: errorMessage ?? this.errorMessage,
      warningMessage: warningMessage ?? this.warningMessage,
      metadata: metadata ?? this.metadata,
      validatedAt: validatedAt ?? this.validatedAt,
      remainingCodes: remainingCodes ?? this.remainingCodes,
      codeConsumed: codeConsumed ?? this.codeConsumed,
    );
  }
}

@JsonSerializable()
class BackupCodeGenerationConfig {
  @JsonKey(name: 'code_count')
  final int codeCount;
  @JsonKey(name: 'code_length')
  final int codeLength;
  @JsonKey(name: 'include_hyphens')
  final bool includeHyphens;
  final String charset;
  @JsonKey(name: 'max_codes_per_user')
  final int maxCodesPerUser;
  @JsonKey(name: 'code_lifetime_days')
  final int codeLifetimeDays;
  @JsonKey(name: 'revoke_existing_codes')
  final bool revokeExistingCodes;

  const BackupCodeGenerationConfig({
    this.codeCount = 10,
    this.codeLength = 8,
    this.includeHyphens = true,
    this.charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    this.maxCodesPerUser = 10,
    this.codeLifetimeDays = 365,
    this.revokeExistingCodes = true,
  });

  factory BackupCodeGenerationConfig.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeGenerationConfigFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeGenerationConfigToJson(this);

  Duration get codeLifetime => Duration(days: codeLifetimeDays);

  BackupCodeGenerationConfig copyWith({
    int? codeCount,
    int? codeLength,
    bool? includeHyphens,
    String? charset,
    int? maxCodesPerUser,
    int? codeLifetimeDays,
    bool? revokeExistingCodes,
  }) {
    return BackupCodeGenerationConfig(
      codeCount: codeCount ?? this.codeCount,
      codeLength: codeLength ?? this.codeLength,
      includeHyphens: includeHyphens ?? this.includeHyphens,
      charset: charset ?? this.charset,
      maxCodesPerUser: maxCodesPerUser ?? this.maxCodesPerUser,
      codeLifetimeDays: codeLifetimeDays ?? this.codeLifetimeDays,
      revokeExistingCodes: revokeExistingCodes ?? this.revokeExistingCodes,
    );
  }
}

@JsonSerializable()
class BackupCodeStatus {
  @JsonKey(name: 'has_active_codes')
  final bool hasActiveCodes;
  @JsonKey(name: 'total_active_codes')
  final int totalActiveCodes;
  @JsonKey(name: 'available_codes')
  final int availableCodes;
  @JsonKey(name: 'used_codes')
  final int usedCodes;
  @JsonKey(name: 'last_generated')
  final DateTime? lastGenerated;
  @JsonKey(name: 'last_used')
  final DateTime? lastUsed;
  @JsonKey(name: 'needs_regeneration')
  final bool needsRegeneration;
  @JsonKey(name: 'low_codes_warning')
  final bool lowCodesWarning;

  const BackupCodeStatus({
    required this.hasActiveCodes,
    required this.totalActiveCodes,
    required this.availableCodes,
    required this.usedCodes,
    this.lastGenerated,
    this.lastUsed,
    this.needsRegeneration = false,
    this.lowCodesWarning = false,
  });

  factory BackupCodeStatus.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeStatusFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeStatusToJson(this);

  BackupCodeStatus copyWith({
    bool? hasActiveCodes,
    int? totalActiveCodes,
    int? availableCodes,
    int? usedCodes,
    DateTime? lastGenerated,
    DateTime? lastUsed,
    bool? needsRegeneration,
    bool? lowCodesWarning,
  }) {
    return BackupCodeStatus(
      hasActiveCodes: hasActiveCodes ?? this.hasActiveCodes,
      totalActiveCodes: totalActiveCodes ?? this.totalActiveCodes,
      availableCodes: availableCodes ?? this.availableCodes,
      usedCodes: usedCodes ?? this.usedCodes,
      lastGenerated: lastGenerated ?? this.lastGenerated,
      lastUsed: lastUsed ?? this.lastUsed,
      needsRegeneration: needsRegeneration ?? this.needsRegeneration,
      lowCodesWarning: lowCodesWarning ?? this.lowCodesWarning,
    );
  }
}

// Request DTOs for backup codes API
@JsonSerializable()
class GenerateBackupCodesRequest {
  final BackupCodeGenerationConfig? config;
  @JsonKey(name: 'revoke_existing')
  final bool revokeExisting;
  final String? reason;

  const GenerateBackupCodesRequest({
    this.config,
    this.revokeExisting = true,
    this.reason,
  });

  factory GenerateBackupCodesRequest.fromJson(Map<String, dynamic> json) =>
      _$GenerateBackupCodesRequestFromJson(json);

  Map<String, dynamic> toJson() => _$GenerateBackupCodesRequestToJson(this);
}

@JsonSerializable()
class ValidateBackupCodeRequest {
  final String code;
  @JsonKey(name: 'consume_on_use')
  final bool consumeOnUse;

  const ValidateBackupCodeRequest({
    required this.code,
    this.consumeOnUse = true,
  });

  factory ValidateBackupCodeRequest.fromJson(Map<String, dynamic> json) =>
      _$ValidateBackupCodeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ValidateBackupCodeRequestToJson(this);
}

@JsonSerializable()
class RevokeBackupCodesRequest {
  @JsonKey(name: 'specific_codes')
  final List<String>? specificCodes;
  final String reason;

  const RevokeBackupCodesRequest({
    this.specificCodes,
    this.reason = 'Manual revocation',
  });

  factory RevokeBackupCodesRequest.fromJson(Map<String, dynamic> json) =>
      _$RevokeBackupCodesRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RevokeBackupCodesRequestToJson(this);
}

// Response DTOs for backup codes API
@JsonSerializable()
class BackupCodesResponse {
  final BackupCodeSet data;
  final String message;
  final bool success;

  const BackupCodesResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory BackupCodesResponse.fromJson(Map<String, dynamic> json) =>
      _$BackupCodesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodesResponseToJson(this);
}

@JsonSerializable()
class BackupCodeValidationResponse {
  final BackupCodeValidationResult data;
  final String message;
  final bool success;

  const BackupCodeValidationResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory BackupCodeValidationResponse.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeValidationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeValidationResponseToJson(this);
}

@JsonSerializable()
class BackupCodeStatusResponse {
  final BackupCodeStatus data;
  final String message;
  final bool success;

  const BackupCodeStatusResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory BackupCodeStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeStatusResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeStatusResponseToJson(this);
}