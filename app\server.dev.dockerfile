# Development Dockerfile for Quester Server
FROM dart:stable AS development

RUN apt-get update && apt-get install -y wget curl git && rm -rf /var/lib/apt/lists/*

WORKDIR /app
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY server/ ./server/

# Set working directory to server and install dependencies
WORKDIR /app/server
RUN dart pub get

RUN groupadd -r quester && useradd -r -g quester quester -m
RUN chown -R quester:quester /app

EXPOSE 8080 9229

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

CMD ["dart", "run", "--enable-vm-service=0.0.0.0:9229", "bin/server.dart"]
