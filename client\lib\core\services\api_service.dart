import 'dart:convert';
import 'package:http/http.dart' as http;

import '../config/app_config.dart';
import '../models/api_response.dart';

/// HTTP API service for communicating with the server
class ApiService {
  final http.Client _client = http.Client();
  
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  Map<String, String> _headersWithAuth(String? token) => {
    ..._headers,
    if (token != null) 'Authorization': 'Bearer $token',
  };

  /// GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    String? token,
    Map<String, String>? queryParams,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParams);
      final response = await _client.get(
        uri,
        headers: _headersWithAuth(token),
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    String? token,
    Map<String, dynamic>? body,
  }) async {
    try {
      final uri = _buildUri(endpoint);
      final response = await _client.post(
        uri,
        headers: _headersWithAuth(token),
        body: body != null ? jsonEncode(body) : null,
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    String? token,
    Map<String, dynamic>? body,
  }) async {
    try {
      final uri = _buildUri(endpoint);
      final response = await _client.put(
        uri,
        headers: _headersWithAuth(token),
        body: body != null ? jsonEncode(body) : null,
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    String? token,
  }) async {
    try {
      final uri = _buildUri(endpoint);
      final response = await _client.delete(
        uri,
        headers: _headersWithAuth(token),
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  Uri _buildUri(String endpoint, [Map<String, String>? queryParams]) {
    final url = '${AppConfig.baseUrl}$endpoint';
    final uri = Uri.parse(url);
    
    if (queryParams != null && queryParams.isNotEmpty) {
      return uri.replace(queryParameters: queryParams);
    }
    
    return uri;
  }

  ApiResponse<T> _handleResponse<T>(http.Response response) {
    try {
      final data = jsonDecode(response.body);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse.success(data);
      } else {
        final errorMessage = data['error'] ?? 'Unknown error occurred';
        return ApiResponse.error(errorMessage);
      }
    } catch (e) {
      return ApiResponse.error('Failed to parse response: $e');
    }
  }

  void dispose() {
    _client.close();
  }
}