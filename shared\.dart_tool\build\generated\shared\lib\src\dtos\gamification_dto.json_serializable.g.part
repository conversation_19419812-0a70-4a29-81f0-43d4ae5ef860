// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AchievementUnlockDto _$AchievementUnlockDtoFromJson(
  Map<String, dynamic> json,
) => AchievementUnlockDto(
  achievementId: json['achievementId'] as String,
  userId: json['userId'] as String,
  pointsEarned: (json['pointsEarned'] as num).toInt(),
  unlockedAt: DateTime.parse(json['unlockedAt'] as String),
  achievement: Achievement.fromJson(
    json['achievement'] as Map<String, dynamic>,
  ),
  isFirstTime: json['isFirstTime'] as bool,
  progressBefore: (json['progressBefore'] as num).toDouble(),
  progressAfter: (json['progressAfter'] as num).toDouble(),
);

Map<String, dynamic> _$AchievementUnlockDtoToJson(
  AchievementUnlockDto instance,
) => <String, dynamic>{
  'achievementId': instance.achievementId,
  'userId': instance.userId,
  'pointsEarned': instance.pointsEarned,
  'unlockedAt': instance.unlockedAt.toIso8601String(),
  'achievement': instance.achievement,
  'isFirstTime': instance.isFirstTime,
  'progressBefore': instance.progressBefore,
  'progressAfter': instance.progressAfter,
};

AchievementProgressDto _$AchievementProgressDtoFromJson(
  Map<String, dynamic> json,
) => AchievementProgressDto(
  achievementId: json['achievementId'] as String,
  userId: json['userId'] as String,
  progress: (json['progress'] as num).toDouble(),
  currentValue: (json['currentValue'] as num).toInt(),
  requiredValue: (json['requiredValue'] as num).toInt(),
  currentSecondaryValue: (json['currentSecondaryValue'] as num?)?.toInt(),
  requiredSecondaryValue: (json['requiredSecondaryValue'] as num?)?.toInt(),
  isUnlocked: json['isUnlocked'] as bool,
  unlockedAt: json['unlockedAt'] == null
      ? null
      : DateTime.parse(json['unlockedAt'] as String),
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$AchievementProgressDtoToJson(
  AchievementProgressDto instance,
) => <String, dynamic>{
  'achievementId': instance.achievementId,
  'userId': instance.userId,
  'progress': instance.progress,
  'currentValue': instance.currentValue,
  'requiredValue': instance.requiredValue,
  'currentSecondaryValue': instance.currentSecondaryValue,
  'requiredSecondaryValue': instance.requiredSecondaryValue,
  'isUnlocked': instance.isUnlocked,
  'unlockedAt': instance.unlockedAt?.toIso8601String(),
  'lastUpdated': instance.lastUpdated.toIso8601String(),
};

RewardPurchaseDto _$RewardPurchaseDtoFromJson(Map<String, dynamic> json) =>
    RewardPurchaseDto(
      rewardId: json['rewardId'] as String,
      quantity: (json['quantity'] as num?)?.toInt() ?? 1,
      customizationData: json['customizationData'] as String?,
    );

Map<String, dynamic> _$RewardPurchaseDtoToJson(RewardPurchaseDto instance) =>
    <String, dynamic>{
      'rewardId': instance.rewardId,
      'quantity': instance.quantity,
      'customizationData': instance.customizationData,
    };

RewardPurchaseResponseDto _$RewardPurchaseResponseDtoFromJson(
  Map<String, dynamic> json,
) => RewardPurchaseResponseDto(
  userRewardId: json['userRewardId'] as String,
  rewardId: json['rewardId'] as String,
  userId: json['userId'] as String,
  pointsCost: (json['pointsCost'] as num).toInt(),
  quantity: (json['quantity'] as num).toInt(),
  purchasedAt: DateTime.parse(json['purchasedAt'] as String),
  reward: Reward.fromJson(json['reward'] as Map<String, dynamic>),
  remainingPoints: (json['remainingPoints'] as num).toInt(),
);

Map<String, dynamic> _$RewardPurchaseResponseDtoToJson(
  RewardPurchaseResponseDto instance,
) => <String, dynamic>{
  'userRewardId': instance.userRewardId,
  'rewardId': instance.rewardId,
  'userId': instance.userId,
  'pointsCost': instance.pointsCost,
  'quantity': instance.quantity,
  'purchasedAt': instance.purchasedAt.toIso8601String(),
  'reward': instance.reward,
  'remainingPoints': instance.remainingPoints,
};

PointsTransactionSummaryDto _$PointsTransactionSummaryDtoFromJson(
  Map<String, dynamic> json,
) => PointsTransactionSummaryDto(
  totalPointsEarned: (json['totalPointsEarned'] as num).toInt(),
  totalPointsSpent: (json['totalPointsSpent'] as num).toInt(),
  availablePoints: (json['availablePoints'] as num).toInt(),
  transactionCount: (json['transactionCount'] as num).toInt(),
  lastTransaction: json['lastTransaction'] == null
      ? null
      : DateTime.parse(json['lastTransaction'] as String),
  earningsBySource: Map<String, int>.from(json['earningsBySource'] as Map),
  spendingByCategory: Map<String, int>.from(json['spendingByCategory'] as Map),
  dailyAverage: (json['dailyAverage'] as num).toDouble(),
  weeklyAverage: (json['weeklyAverage'] as num).toDouble(),
  bestDay: (json['bestDay'] as num).toInt(),
  bestDayDate: json['bestDayDate'] as String,
);

Map<String, dynamic> _$PointsTransactionSummaryDtoToJson(
  PointsTransactionSummaryDto instance,
) => <String, dynamic>{
  'totalPointsEarned': instance.totalPointsEarned,
  'totalPointsSpent': instance.totalPointsSpent,
  'availablePoints': instance.availablePoints,
  'transactionCount': instance.transactionCount,
  'lastTransaction': instance.lastTransaction?.toIso8601String(),
  'earningsBySource': instance.earningsBySource,
  'spendingByCategory': instance.spendingByCategory,
  'dailyAverage': instance.dailyAverage,
  'weeklyAverage': instance.weeklyAverage,
  'bestDay': instance.bestDay,
  'bestDayDate': instance.bestDayDate,
};

LeaderboardRequestDto _$LeaderboardRequestDtoFromJson(
  Map<String, dynamic> json,
) => LeaderboardRequestDto(
  category: $enumDecode(_$LeaderboardCategoryEnumMap, json['category']),
  period: $enumDecode(_$LeaderboardPeriodEnumMap, json['period']),
  limit: (json['limit'] as num?)?.toInt(),
  offset: (json['offset'] as num?)?.toInt(),
  userId: json['userId'] as String?,
);

Map<String, dynamic> _$LeaderboardRequestDtoToJson(
  LeaderboardRequestDto instance,
) => <String, dynamic>{
  'category': _$LeaderboardCategoryEnumMap[instance.category]!,
  'period': _$LeaderboardPeriodEnumMap[instance.period]!,
  'limit': instance.limit,
  'offset': instance.offset,
  'userId': instance.userId,
};

const _$LeaderboardCategoryEnumMap = {
  LeaderboardCategory.totalPoints: 'total_points',
  LeaderboardCategory.monthlyPoints: 'monthly_points',
  LeaderboardCategory.weeklyPoints: 'weekly_points',
  LeaderboardCategory.dailyPoints: 'daily_points',
  LeaderboardCategory.questsCompleted: 'quests_completed',
  LeaderboardCategory.tasksCompleted: 'tasks_completed',
  LeaderboardCategory.currentStreak: 'current_streak',
  LeaderboardCategory.longestStreak: 'longest_streak',
  LeaderboardCategory.achievementsCount: 'achievements_count',
  LeaderboardCategory.collaborationScore: 'collaboration_score',
  LeaderboardCategory.efficiencyRating: 'efficiency_rating',
};

const _$LeaderboardPeriodEnumMap = {
  LeaderboardPeriod.allTime: 'all_time',
  LeaderboardPeriod.yearly: 'yearly',
  LeaderboardPeriod.monthly: 'monthly',
  LeaderboardPeriod.weekly: 'weekly',
  LeaderboardPeriod.daily: 'daily',
};

StreakUpdateDto _$StreakUpdateDtoFromJson(Map<String, dynamic> json) =>
    StreakUpdateDto(
      streakType: $enumDecode(_$StreakTypeEnumMap, json['streakType']),
      activityDate: json['activityDate'] == null
          ? null
          : DateTime.parse(json['activityDate'] as String),
      activityData: json['activityData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$StreakUpdateDtoToJson(StreakUpdateDto instance) =>
    <String, dynamic>{
      'streakType': _$StreakTypeEnumMap[instance.streakType]!,
      'activityDate': instance.activityDate?.toIso8601String(),
      'activityData': instance.activityData,
    };

const _$StreakTypeEnumMap = {
  StreakType.dailyLogin: 'daily_login',
  StreakType.dailyQuest: 'daily_quest',
  StreakType.dailyTask: 'daily_task',
  StreakType.weeklyGoal: 'weekly_goal',
  StreakType.monthlyChallenge: 'monthly_challenge',
  StreakType.custom: 'custom',
};

GamificationStatsDto _$GamificationStatsDtoFromJson(
  Map<String, dynamic> json,
) => GamificationStatsDto(
  totalPoints: (json['totalPoints'] as num).toInt(),
  availablePoints: (json['availablePoints'] as num).toInt(),
  currentLevel: (json['currentLevel'] as num).toInt(),
  levelProgress: (json['levelProgress'] as num).toDouble(),
  achievementsUnlocked: (json['achievementsUnlocked'] as num).toInt(),
  totalAchievements: (json['totalAchievements'] as num).toInt(),
  rewardsOwned: (json['rewardsOwned'] as num).toInt(),
  currentStreak: (json['currentStreak'] as num).toInt(),
  longestStreak: (json['longestStreak'] as num).toInt(),
  leaderboardRank: (json['leaderboardRank'] as num).toInt(),
  leaderboardCategory: json['leaderboardCategory'] as String,
  questsCompleted: (json['questsCompleted'] as num).toInt(),
  tasksCompleted: (json['tasksCompleted'] as num).toInt(),
  streakSummary: json['streakSummary'] as Map<String, dynamic>,
  recentActivity: json['recentActivity'] as Map<String, dynamic>,
);

Map<String, dynamic> _$GamificationStatsDtoToJson(
  GamificationStatsDto instance,
) => <String, dynamic>{
  'totalPoints': instance.totalPoints,
  'availablePoints': instance.availablePoints,
  'currentLevel': instance.currentLevel,
  'levelProgress': instance.levelProgress,
  'achievementsUnlocked': instance.achievementsUnlocked,
  'totalAchievements': instance.totalAchievements,
  'rewardsOwned': instance.rewardsOwned,
  'currentStreak': instance.currentStreak,
  'longestStreak': instance.longestStreak,
  'leaderboardRank': instance.leaderboardRank,
  'leaderboardCategory': instance.leaderboardCategory,
  'questsCompleted': instance.questsCompleted,
  'tasksCompleted': instance.tasksCompleted,
  'streakSummary': instance.streakSummary,
  'recentActivity': instance.recentActivity,
};

UserAchievementsDto _$UserAchievementsDtoFromJson(
  Map<String, dynamic> json,
) => UserAchievementsDto(
  unlocked: (json['unlocked'] as List<dynamic>)
      .map((e) => AchievementProgressDto.fromJson(e as Map<String, dynamic>))
      .toList(),
  inProgress: (json['inProgress'] as List<dynamic>)
      .map((e) => AchievementProgressDto.fromJson(e as Map<String, dynamic>))
      .toList(),
  available: (json['available'] as List<dynamic>)
      .map((e) => Achievement.fromJson(e as Map<String, dynamic>))
      .toList(),
  totalUnlocked: (json['totalUnlocked'] as num).toInt(),
  totalAvailable: (json['totalAvailable'] as num).toInt(),
  overallProgress: (json['overallProgress'] as num).toDouble(),
);

Map<String, dynamic> _$UserAchievementsDtoToJson(
  UserAchievementsDto instance,
) => <String, dynamic>{
  'unlocked': instance.unlocked,
  'inProgress': instance.inProgress,
  'available': instance.available,
  'totalUnlocked': instance.totalUnlocked,
  'totalAvailable': instance.totalAvailable,
  'overallProgress': instance.overallProgress,
};

UserRewardsDto _$UserRewardsDtoFromJson(Map<String, dynamic> json) =>
    UserRewardsDto(
      owned: (json['owned'] as List<dynamic>)
          .map((e) => UserRewardDto.fromJson(e as Map<String, dynamic>))
          .toList(),
      available: (json['available'] as List<dynamic>)
          .map((e) => Reward.fromJson(e as Map<String, dynamic>))
          .toList(),
      purchasable: (json['purchasable'] as List<dynamic>)
          .map((e) => Reward.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalOwned: (json['totalOwned'] as num).toInt(),
      totalAvailable: (json['totalAvailable'] as num).toInt(),
    );

Map<String, dynamic> _$UserRewardsDtoToJson(UserRewardsDto instance) =>
    <String, dynamic>{
      'owned': instance.owned,
      'available': instance.available,
      'purchasable': instance.purchasable,
      'totalOwned': instance.totalOwned,
      'totalAvailable': instance.totalAvailable,
    };

UserRewardDto _$UserRewardDtoFromJson(Map<String, dynamic> json) =>
    UserRewardDto(
      id: json['id'] as String,
      rewardId: json['rewardId'] as String,
      userId: json['userId'] as String,
      quantity: (json['quantity'] as num).toInt(),
      obtainedAt: DateTime.parse(json['obtainedAt'] as String),
      isActive: json['isActive'] as bool,
      reward: Reward.fromJson(json['reward'] as Map<String, dynamic>),
      customizationData: json['customizationData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UserRewardDtoToJson(UserRewardDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'rewardId': instance.rewardId,
      'userId': instance.userId,
      'quantity': instance.quantity,
      'obtainedAt': instance.obtainedAt.toIso8601String(),
      'isActive': instance.isActive,
      'reward': instance.reward,
      'customizationData': instance.customizationData,
    };
