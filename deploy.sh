#!/bin/bash

# Quester Production Deployment Script
# Manages deployment of the Quester platform to production environment

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Configuration
ENVIRONMENT="${1:-production}"
COMPOSE_FILES="-f app/docker-compose.base.yml"
LOG_DIR="logs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Help function
show_help() {
    echo "Quester Production Deployment Script"
    echo ""
    echo "Usage: $0 [environment] [command]"
    echo ""
    echo "Environments:"
    echo "  production  - Full production deployment with scaling"
    echo "  staging     - Staging environment for testing"
    echo "  development - Development environment (default from docker.sh)"
    echo ""
    echo "Commands:"
    echo "  deploy      - Deploy the application (default)"
    echo "  status      - Show deployment status"
    echo "  logs        - Show application logs"
    echo "  stop        - Stop the application"
    echo "  restart     - Restart the application"
    echo "  health      - Run health checks"
    echo "  migrate     - Run database migrations"
    echo "  backup      - Create database backup"
    echo "  rollback    - Rollback to previous deployment"
    echo ""
    echo "Examples:"
    echo "  $0 production deploy"
    echo "  $0 staging status"
    echo "  $0 production logs"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed or not in PATH"
    fi
    
    # Check if .env file exists
    if [[ ! -f .env ]]; then
        warn ".env file not found, using default configuration"
    fi
    
    # Create logs directory
    mkdir -p "$LOG_DIR"
    
    log "Prerequisites check passed"
}

# Set compose files based on environment
setup_environment() {
    case "$ENVIRONMENT" in
        production)
            COMPOSE_FILES="$COMPOSE_FILES -f app/docker-compose.prod.yml"
            log "Setting up production environment with scaling"
            ;;
        staging)
            COMPOSE_FILES="$COMPOSE_FILES -f app/docker-compose.staging.yml"
            log "Setting up staging environment"
            ;;
        development)
            COMPOSE_FILES="$COMPOSE_FILES -f app/docker-compose.dev.yml"
            log "Setting up development environment"
            ;;
        *)
            error "Unknown environment: $ENVIRONMENT"
            ;;
    esac
}

# Deploy application
deploy() {
    log "Starting deployment to $ENVIRONMENT environment..."
    
    # Pull latest images
    log "Pulling latest images..."
    docker-compose $COMPOSE_FILES pull
    
    # Build custom images
    log "Building application images..."
    docker-compose $COMPOSE_FILES build --no-cache
    
    # Run database migrations
    log "Running database migrations..."
    run_migrations
    
    # Start services
    log "Starting services..."
    docker-compose $COMPOSE_FILES up -d
    
    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 30
    
    # Run health checks
    health_check
    
    log "Deployment completed successfully!"
}

# Show status
show_status() {
    log "Showing deployment status..."
    docker-compose $COMPOSE_FILES ps
    
    echo ""
    log "Container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Show logs
show_logs() {
    log "Showing application logs..."
    docker-compose $COMPOSE_FILES logs --tail=100 -f
}

# Stop application
stop_application() {
    log "Stopping application..."
    docker-compose $COMPOSE_FILES down
    log "Application stopped"
}

# Restart application
restart_application() {
    log "Restarting application..."
    docker-compose $COMPOSE_FILES restart
    
    # Wait for services to be ready
    sleep 20
    health_check
    log "Application restarted successfully"
}

# Run health checks
health_check() {
    log "Running health checks..."
    
    # Check server health
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/health &> /dev/null; then
            log "Server health check passed"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "Server health check failed after $max_attempts attempts"
        fi
        
        log "Waiting for server... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    # Check client
    if curl -f http://localhost:3000 &> /dev/null; then
        log "Client health check passed"
    else
        error "Client health check failed"
    fi
    
    # Check database
    if docker-compose $COMPOSE_FILES exec -T postgres pg_isready -U quester &> /dev/null; then
        log "Database health check passed"
    else
        error "Database health check failed"
    fi
    
    log "All health checks passed!"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Wait for database to be ready
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose $COMPOSE_FILES exec -T postgres pg_isready -U quester &> /dev/null; then
            log "Database is ready"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "Database not ready after $max_attempts attempts"
        fi
        
        log "Waiting for database... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    # Run migrations using the server container
    log "Executing migrations..."
    if docker-compose $COMPOSE_FILES run --rm server dart run bin/migrate.dart up; then
        log "Migrations completed successfully"
    else
        error "Migration failed"
    fi
}

# Create database backup
create_backup() {
    log "Creating database backup..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    local backup_path="$LOG_DIR/$backup_file"
    
    docker-compose $COMPOSE_FILES exec -T postgres pg_dump -U quester questerdb > "$backup_path"
    
    if [[ $? -eq 0 ]]; then
        log "Database backup created: $backup_path"
    else
        error "Database backup failed"
    fi
}

# Rollback deployment
rollback_deployment() {
    warn "Rollback functionality not yet implemented"
    warn "Please manually restore from backup if needed"
    
    # Future implementation:
    # - Tag deployments
    # - Store deployment metadata
    # - Implement rollback logic
}

# Main execution
main() {
    local command="${2:-deploy}"
    
    case "$1" in
        -h|--help|help)
            show_help
            exit 0
            ;;
    esac
    
    check_prerequisites
    setup_environment
    
    case "$command" in
        deploy)
            deploy
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        stop)
            stop_application
            ;;
        restart)
            restart_application
            ;;
        health)
            health_check
            ;;
        migrate)
            run_migrations
            ;;
        backup)
            create_backup
            ;;
        rollback)
            rollback_deployment
            ;;
        *)
            error "Unknown command: $command"
            ;;
    esac
}

# Run main function with all arguments
main "$@"