import 'package:shared/shared.dart';

void main() {
  // Example using shared utilities
  final user = User(
    id: 'user_1',
    email: '<EMAIL>',
    displayName: 'Example User',
    role: UserRole.newcomer,
    status: UserStatus.active,
    totalPoints: 100,
    currentLevelPoints: 50,
    level: 1,
    currentStreak: 5,
    longestStreak: 10,
    achievementCount: 2,
    questsCompleted: 3,
    tasksCompleted: 15,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
  
  print('User created: ${user.displayName}');
  print('User email: ${user.email}');
  print('User level: ${user.level}');
}
