import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'api_response.g.dart';

/// Generic API response wrapper
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> extends Equatable {
  final bool success;
  final String message;
  final T? data;
  final String? error;
  final String? errorCode;
  final Map<String, dynamic>? metadata;
  final DateTime timestamp;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.error,
    this.errorCode,
    this.metadata,
    required this.timestamp,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// Create successful response
  factory ApiResponse.success({
    required T data,
    String message = 'Success',
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse(
      success: true,
      message: message,
      data: data,
      timestamp: DateTime.now(),
      metadata: metadata,
    );
  }

  /// Create error response
  factory ApiResponse.error({
    required String message,
    String? error,
    String? errorCode,
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse(
      success: false,
      message: message,
      error: error,
      errorCode: errorCode,
      timestamp: DateTime.now(),
      metadata: metadata,
    );
  }

  /// Create validation error response
  factory ApiResponse.validationError({
    required Map<String, List<String>> validationErrors,
    String message = 'Validation failed',
  }) {
    return ApiResponse(
      success: false,
      message: message,
      errorCode: 'VALIDATION_ERROR',
      timestamp: DateTime.now(),
      metadata: {'validation_errors': validationErrors},
    );
  }

  @override
  List<Object?> get props => [
        success,
        message,
        data,
        error,
        errorCode,
        metadata,
        timestamp,
      ];

  @override
  bool get stringify => true;
}

/// Paginated response wrapper
@JsonSerializable(genericArgumentFactories: true)
class PaginatedResponse<T> extends Equatable {
  final List<T> items;
  final int totalCount;
  final int page;
  final int pageSize;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;
  final Map<String, dynamic>? metadata;

  const PaginatedResponse({
    required this.items,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
    this.metadata,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedResponseToJson(this, toJsonT);

  /// Create paginated response from data
  factory PaginatedResponse.fromData({
    required List<T> items,
    required int totalCount,
    required int page,
    required int pageSize,
    Map<String, dynamic>? metadata,
  }) {
    final totalPages = (totalCount / pageSize).ceil();
    return PaginatedResponse(
      items: items,
      totalCount: totalCount,
      page: page,
      pageSize: pageSize,
      totalPages: totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
      metadata: metadata,
    );
  }

  @override
  List<Object?> get props => [
        items,
        totalCount,
        page,
        pageSize,
        totalPages,
        hasNext,
        hasPrevious,
        metadata,
      ];

  @override
  bool get stringify => true;
}

/// Error details for API responses
@JsonSerializable()
class ApiError extends Equatable {
  final String code;
  final String message;
  final String? field;
  final Map<String, dynamic>? details;

  const ApiError({
    required this.code,
    required this.message,
    this.field,
    this.details,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) => _$ApiErrorFromJson(json);
  Map<String, dynamic> toJson() => _$ApiErrorToJson(this);

  @override
  List<Object?> get props => [code, message, field, details];

  @override
  bool get stringify => true;
}

/// Health check response
@JsonSerializable()
class HealthCheckResponse extends Equatable {
  final String status;
  final DateTime timestamp;
  final String version;
  final Map<String, dynamic> services;
  final int uptime;
  final Map<String, dynamic>? metadata;

  const HealthCheckResponse({
    required this.status,
    required this.timestamp,
    required this.version,
    required this.services,
    required this.uptime,
    this.metadata,
  });

  factory HealthCheckResponse.fromJson(Map<String, dynamic> json) => _$HealthCheckResponseFromJson(json);
  Map<String, dynamic> toJson() => _$HealthCheckResponseToJson(this);

  @override
  List<Object?> get props => [status, timestamp, version, services, uptime, metadata];

  @override
  bool get stringify => true;
}

/// Notification DTO
@JsonSerializable()
class NotificationDto extends Equatable {
  final String id;
  final String userId;
  final String title;
  final String message;
  final String type;
  final String? actionUrl;
  final Map<String, dynamic>? actionData;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;

  const NotificationDto({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    this.actionUrl,
    this.actionData,
    required this.isRead,
    required this.createdAt,
    this.readAt,
  });

  factory NotificationDto.fromJson(Map<String, dynamic> json) => _$NotificationDtoFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationDtoToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        message,
        type,
        actionUrl,
        actionData,
        isRead,
        createdAt,
        readAt,
      ];

  @override
  bool get stringify => true;
}

/// File upload response
@JsonSerializable()
class FileUploadResponse extends Equatable {
  final String fileId;
  final String fileName;
  final String originalName;
  final String mimeType;
  final int size;
  final String url;
  final DateTime uploadedAt;
  final Map<String, dynamic>? metadata;

  const FileUploadResponse({
    required this.fileId,
    required this.fileName,
    required this.originalName,
    required this.mimeType,
    required this.size,
    required this.url,
    required this.uploadedAt,
    this.metadata,
  });

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) => _$FileUploadResponseFromJson(json);
  Map<String, dynamic> toJson() => _$FileUploadResponseToJson(this);

  @override
  List<Object?> get props => [
        fileId,
        fileName,
        originalName,
        mimeType,
        size,
        url,
        uploadedAt,
        metadata,
      ];

  @override
  bool get stringify => true;
}