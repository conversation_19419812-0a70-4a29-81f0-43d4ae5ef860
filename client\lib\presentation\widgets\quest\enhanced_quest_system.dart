import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../common/enhanced_animations.dart';
import '../common/adaptive_layout.dart';
import '../common/enhanced_card.dart';

/// Enhanced quest card with dynamic difficulty visualization
class EnhancedQuestCard extends StatefulWidget {
  final String title;
  final String description;
  final String difficulty;
  final String status;
  final double progress;
  final int points;
  final int? timeLimit;
  final List<String> tags;
  final List<String>? requirements;
  final VoidCallback? onTap;
  final VoidCallback? onStart;
  final VoidCallback? onComplete;

  const EnhancedQuestCard({
    super.key,
    required this.title,
    required this.description,
    required this.difficulty,
    required this.status,
    required this.progress,
    required this.points,
    this.timeLimit,
    this.tags = const [],
    this.requirements,
    this.onTap,
    this.onStart,
    this.onComplete,
  });

  @override
  State<EnhancedQuestCard> createState() => _EnhancedQuestCardState();
}

class _EnhancedQuestCardState extends State<EnhancedQuestCard>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;
  final bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: GameCurves.slideIn,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _progressController.forward();
    
    if (widget.status == 'available' || widget.status == 'in_progress') {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(EnhancedQuestCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _progressController,
        curve: GameCurves.slideIn,
      ));
      _progressController.reset();
      _progressController.forward();
    }
    
    if (oldWidget.status != widget.status) {
      if (widget.status == 'available' || widget.status == 'in_progress') {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
      }
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final difficultyColor = AppTheme.getDifficultyColor(widget.difficulty);
    final statusColor = AppTheme.getStatusColor(widget.status);

    return AdaptiveLayout(
      mobile: _buildMobileCard(theme, difficultyColor, statusColor),
      tablet: _buildTabletCard(theme, difficultyColor, statusColor),
      desktop: _buildDesktopCard(theme, difficultyColor, statusColor),
    );
  }

  Widget _buildMobileCard(ThemeData theme, Color difficultyColor, Color statusColor) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.status == 'available' ? _pulseAnimation.value : 1.0,
          child: EnhancedCard(
            variant: CardVariant.elevated,
            onTap: widget.onTap,
            gradient: _buildGradient(difficultyColor),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(theme, statusColor),
                const SizedBox(height: 12),
                _buildDescription(theme),
                const SizedBox(height: 16),
                _buildProgressSection(theme, statusColor),
                const SizedBox(height: 16),
                _buildMetadata(theme, difficultyColor),
                if (widget.tags.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  _buildTags(theme),
                ],
                const SizedBox(height: 16),
                _buildActionButtons(theme),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTabletCard(ThemeData theme, Color difficultyColor, Color statusColor) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.status == 'available' ? _pulseAnimation.value : 1.0,
          child: EnhancedCard(
            variant: CardVariant.elevated,
            onTap: widget.onTap,
            gradient: _buildGradient(difficultyColor),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(theme, statusColor),
                      const SizedBox(height: 8),
                      _buildDescription(theme),
                      const SizedBox(height: 12),
                      _buildProgressSection(theme, statusColor),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      _buildMetadata(theme, difficultyColor),
                      if (widget.tags.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        _buildTags(theme),
                      ],
                      const SizedBox(height: 12),
                      _buildActionButtons(theme),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDesktopCard(ThemeData theme, Color difficultyColor, Color statusColor) {
    return _buildTabletCard(theme, difficultyColor, statusColor);
  }

  LinearGradient _buildGradient(Color difficultyColor) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        difficultyColor.withValues(alpha: 0.05),
        Colors.transparent,
      ],
    );
  }

  Widget _buildHeader(ThemeData theme, Color statusColor) {
    return Row(
      children: [
        Expanded(
          child: Text(
            widget.title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            widget.status.toUpperCase(),
            style: theme.textTheme.labelSmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(ThemeData theme) {
    return Text(
      widget.description,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildProgressSection(ThemeData theme, Color statusColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AnimatedCounter(
              value: (widget.progress * 100).round(),
              suffix: '%',
              textStyle: theme.textTheme.labelMedium?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return Container(
              height: 8,
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                widthFactor: _progressAnimation.value.clamp(0.0, 1.0),
                alignment: Alignment.centerLeft,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        statusColor,
                        statusColor.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: statusColor.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildMetadata(ThemeData theme, Color difficultyColor) {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        _buildMetadataChip(
          theme,
          difficultyColor,
          _getDifficultyIcon(widget.difficulty),
          widget.difficulty.toUpperCase(),
        ),
        _buildMetadataChip(
          theme,
          AppTheme.successColor,
          Icons.stars,
          '${widget.points}',
        ),
        if (widget.timeLimit != null)
          _buildMetadataChip(
            theme,
            AppTheme.warningColor,
            Icons.timer,
            _formatTime(widget.timeLimit!),
          ),
      ],
    );
  }

  Widget _buildMetadataChip(ThemeData theme, Color color, IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: theme.textTheme.labelSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTags(ThemeData theme) {
    return Wrap(
      spacing: 6,
      runSpacing: 4,
      children: widget.tags.map((tag) => Chip(
        label: Text(tag),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        labelStyle: theme.textTheme.labelSmall,
      )).toList(),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      children: [
        if (widget.status == 'available' && widget.onStart != null)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: widget.onStart,
              icon: const Icon(Icons.play_arrow, size: 18),
              label: const Text('Start Quest'),
            ),
          ),
        if (widget.status == 'in_progress' && widget.onComplete != null)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: widget.onComplete,
              icon: const Icon(Icons.check, size: 18),
              label: const Text('Complete'),
            ),
          ),
        if (widget.status == 'completed')
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle, 
                       color: AppTheme.successColor, size: 18),
                  const SizedBox(width: 8),
                  Text(
                    'Completed',
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: AppTheme.successColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  IconData _getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Icons.sentiment_satisfied;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_dissatisfied;
      case 'expert':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  String _formatTime(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    if (hours > 0) {
      return '${hours}h ${mins}m';
    }
    return '${mins}m';
  }
}
