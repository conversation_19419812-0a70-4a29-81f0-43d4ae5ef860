# Production Setup Guide

This guide covers the complete setup process for deploying <PERSON><PERSON> to production, including SendGrid and Twilio configuration.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [SendGrid Setup](#sendgrid-setup)
3. [Twilio Setup](#twilio-setup)
4. [Environment Configuration](#environment-configuration)
5. [Database Setup](#database-setup)
6. [Security Configuration](#security-configuration)
7. [Deployment](#deployment)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

Before starting the production setup, ensure you have:

- [ ] Domain name configured with DNS
- [ ] SSL certificate (Let's Encrypt recommended)
- [ ] Production server with Docker support
- [ ] PostgreSQL database instance
- [ ] Redis instance
- [ ] SendGrid account
- [ ] Twilio account

## SendGrid Setup

### 1. Create SendGrid Account

1. Sign up at [SendGrid](https://sendgrid.com/)
2. Verify your account and complete setup
3. Add and verify your sending domain

### 2. Generate API Key

1. Go to Settings > API Keys
2. Click "Create API Key"
3. Choose "Restricted Access"
4. Grant the following permissions:
   - Mail Send: Full Access
   - Template Engine: Read Access
   - Suppressions: Read Access
   - Stats: Read Access

### 3. Create Email Templates

Create the following dynamic templates in SendGrid:

#### Welcome Email Template
```html
<h1>Welcome to Quester, {{name}}!</h1>
<p>Thank you for joining our quest management platform.</p>
<p>Get started by creating your first quest!</p>
<a href="{{dashboard_url}}" class="button">Go to Dashboard</a>
```

#### Email Verification Template
```html
<h1>Verify Your Email</h1>
<p>Hi {{name}},</p>
<p>Please verify your email address by clicking the button below:</p>
<a href="{{verification_url}}" class="button">Verify Email</a>
<p>This link expires in 24 hours.</p>
```

#### Password Reset Template
```html
<h1>Reset Your Password</h1>
<p>Hi {{name}},</p>
<p>You requested a password reset. Click the button below to set a new password:</p>
<a href="{{reset_url}}" class="button">Reset Password</a>
<p>This link expires in 1 hour.</p>
```

#### MFA Code Template
```html
<h1>Your Verification Code</h1>
<p>Hi {{name}},</p>
<p>Your verification code is: <strong>{{mfa_code}}</strong></p>
<p>This code expires in 10 minutes.</p>
```

### 4. Configure Domain Authentication

1. Go to Settings > Sender Authentication
2. Click "Authenticate Your Domain"
3. Follow the DNS configuration steps
4. Wait for verification (may take up to 48 hours)

## Twilio Setup

### 1. Create Twilio Account

1. Sign up at [Twilio](https://www.twilio.com/)
2. Complete phone number verification
3. Add billing information

### 2. Get Account Credentials

1. Go to Console Dashboard
2. Note your Account SID and Auth Token
3. Purchase a phone number for sending SMS

### 3. Configure Messaging Service (Recommended)

1. Go to Messaging > Services
2. Create a new Messaging Service
3. Add your phone number to the service
4. Configure compliance settings

### 4. Set Up Verify Service (For MFA)

1. Go to Verify > Services
2. Create a new Verify Service
3. Configure rate limiting and attempt limits
4. Note the Service SID

### 5. Configure Webhooks

1. Set up status callbacks for message delivery
2. Configure webhook endpoints:
   - Status Callback: `https://api.yourdomain.com/callbacks/sms-status`
   - Webhook URL: `https://api.yourdomain.com/webhooks/twilio`

## Environment Configuration

### 1. Copy Production Environment File

```bash
cp .env.production .env
```

### 2. Configure Required Variables

Replace all `REPLACE_WITH_*` placeholders in `.env`:

#### SendGrid Configuration
```bash
SENDGRID_API_KEY=SG.your_actual_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_TEMPLATE_WELCOME=d-your_welcome_template_id
SENDGRID_TEMPLATE_VERIFICATION=d-your_verification_template_id
# ... other templates
```

#### Twilio Configuration
```bash
TWILIO_ACCOUNT_SID=ACyour_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_FROM_PHONE=+**********
TWILIO_MESSAGING_SERVICE_SID=MGyour_messaging_service_sid
TWILIO_VERIFY_SERVICE_SID=VAyour_verify_service_sid
```

#### Security Configuration
```bash
# Generate secure secrets (32+ characters)
JWT_SECRET=$(openssl rand -hex 32)
ENCRYPTION_KEY=$(openssl rand -hex 32)
SESSION_SECRET=$(openssl rand -hex 32)
API_KEY=$(openssl rand -hex 32)
```

### 3. Database Configuration

```bash
DATABASE_URL=****************************************/database
POSTGRES_SSL_MODE=require
```

### 4. Set File Permissions

```bash
chmod 600 .env
chown app:app .env
```

## Database Setup

### 1. Create Production Database

```sql
CREATE DATABASE quester_prod;
CREATE USER quester_prod WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE quester_prod TO quester_prod;
```

### 2. Run Migrations

```bash
cd server
dart run bin/migrate.dart
```

### 3. Seed Initial Data

```bash
dart run bin/seed_production.dart
```

## Security Configuration

### 1. SSL Certificate Setup

Using Let's Encrypt with Certbot:

```bash
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com
```

### 2. Firewall Configuration

```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 3. Security Headers

Ensure these headers are configured in your reverse proxy:

```nginx
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header Referrer-Policy strict-origin-when-cross-origin always;
```

## Deployment

### 1. Build Production Images

```bash
docker-compose -f docker-compose.prod.yml build
```

### 2. Deploy Services

```bash
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Verify Deployment

```bash
# Check service health
curl https://api.yourdomain.com/health

# Check email service
curl -X POST https://api.yourdomain.com/admin/test-email \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# Check SMS service
curl -X POST https://api.yourdomain.com/admin/test-sms \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

## Monitoring and Maintenance

### 1. Set Up Monitoring

Configure monitoring for:
- Application health endpoints
- Database performance
- Email delivery rates
- SMS delivery rates
- Error rates and response times

### 2. Log Management

```bash
# Set up log rotation
sudo logrotate -d /etc/logrotate.d/quester

# Monitor logs
tail -f /var/log/quester/app.log
```

### 3. Backup Strategy

```bash
# Database backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backup script
crontab -e
# Add: 0 2 * * * /path/to/backup_script.sh
```

### 4. Regular Maintenance Tasks

- [ ] Update dependencies monthly
- [ ] Rotate secrets quarterly
- [ ] Review and update rate limits
- [ ] Monitor service quotas and usage
- [ ] Test disaster recovery procedures

## Troubleshooting

### Common Issues

#### SendGrid Issues

**Problem**: Emails not being delivered
**Solution**:
1. Check API key permissions
2. Verify domain authentication
3. Check suppression lists
4. Review SendGrid activity logs

**Problem**: Template not found error
**Solution**:
1. Verify template IDs in environment variables
2. Ensure templates are active in SendGrid
3. Check template syntax and dynamic data

#### Twilio Issues

**Problem**: SMS not being sent
**Solution**:
1. Verify account balance
2. Check phone number format (+**********)
3. Review Twilio console logs
4. Verify messaging service configuration

**Problem**: MFA codes not working
**Solution**:
1. Check Verify service configuration
2. Verify rate limiting settings
3. Check webhook endpoints
4. Review attempt limits

#### General Issues

**Problem**: Service health check failures
**Solution**:
1. Check environment variable configuration
2. Verify network connectivity
3. Review application logs
4. Test service connections manually

### Support Contacts

- SendGrid Support: https://support.sendgrid.com/
- Twilio Support: https://support.twilio.com/
- Application Issues: Create issue in project repository

### Useful Commands

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f service_name

# Restart services
docker-compose -f docker-compose.prod.yml restart

# Update configuration
docker-compose -f docker-compose.prod.yml up -d --force-recreate
```

## Security Checklist

- [ ] All secrets are properly configured and secured
- [ ] SSL certificates are valid and auto-renewing
- [ ] Database connections use SSL
- [ ] API keys have minimal required permissions
- [ ] Rate limiting is properly configured
- [ ] Monitoring and alerting are set up
- [ ] Backup and recovery procedures are tested
- [ ] Security headers are configured
- [ ] Firewall rules are restrictive
- [ ] Log retention policies are in place

## Performance Optimization

- [ ] Database indexes are optimized
- [ ] Redis caching is configured
- [ ] CDN is set up for static assets
- [ ] Connection pooling is optimized
- [ ] Rate limiting prevents abuse
- [ ] Monitoring tracks key metrics
- [ ] Auto-scaling is configured if needed

---

For additional support or questions, please refer to the project documentation or create an issue in the repository.
