import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/repositories/api_repository.dart';

// Events
abstract class CollaborationEvent extends Equatable {
  const CollaborationEvent();
  
  @override
  List<Object?> get props => [];
}

class CreateTeamQuest extends CollaborationEvent {
  final Map<String, dynamic> questData;
  
  const CreateTeamQuest({required this.questData});
  
  @override
  List<Object?> get props => [questData];
}

class JoinTeamQuest extends CollaborationEvent {
  final String questId;
  final String userId;
  
  const JoinTeamQuest({
    required this.questId,
    required this.userId,
  });
  
  @override
  List<Object?> get props => [questId, userId];
}

class LoadTeamQuestStatus extends CollaborationEvent {
  final String questId;
  
  const LoadTeamQuestStatus({required this.questId});
  
  @override
  List<Object?> get props => [questId];
}

class LoadUserTeamQuests extends CollaborationEvent {
  final String userId;
  final String? status;
  
  const LoadUserTeamQuests({
    required this.userId,
    this.status,
  });
  
  @override
  List<Object?> get props => [userId, status];
}

class SendTeamMessage extends CollaborationEvent {
  final String questId;
  final Map<String, dynamic> messageData;
  
  const SendTeamMessage({
    required this.questId,
    required this.messageData,
  });
  
  @override
  List<Object?> get props => [questId, messageData];
}

class LoadTeamMessages extends CollaborationEvent {
  final String questId;
  final int limit;
  final int offset;
  
  const LoadTeamMessages({
    required this.questId,
    this.limit = 50,
    this.offset = 0,
  });
  
  @override
  List<Object?> get props => [questId, limit, offset];
}

class RefreshCollaboration extends CollaborationEvent {
  const RefreshCollaboration();
}

// States
abstract class CollaborationState extends Equatable {
  const CollaborationState();
  
  @override
  List<Object?> get props => [];
}

class CollaborationInitial extends CollaborationState {
  const CollaborationInitial();
}

class CollaborationLoading extends CollaborationState {
  const CollaborationLoading();
}

class CollaborationLoaded extends CollaborationState {
  final List<dynamic>? userTeamQuests;
  final Map<String, dynamic>? currentQuestStatus;
  final List<dynamic>? teamMessages;
  final Map<String, dynamic>? lastCreatedQuest;
  
  const CollaborationLoaded({
    this.userTeamQuests,
    this.currentQuestStatus,
    this.teamMessages,
    this.lastCreatedQuest,
  });
  
  @override
  List<Object?> get props => [userTeamQuests, currentQuestStatus, teamMessages, lastCreatedQuest];
  
  CollaborationLoaded copyWith({
    List<dynamic>? userTeamQuests,
    Map<String, dynamic>? currentQuestStatus,
    List<dynamic>? teamMessages,
    Map<String, dynamic>? lastCreatedQuest,
  }) {
    return CollaborationLoaded(
      userTeamQuests: userTeamQuests ?? this.userTeamQuests,
      currentQuestStatus: currentQuestStatus ?? this.currentQuestStatus,
      teamMessages: teamMessages ?? this.teamMessages,
      lastCreatedQuest: lastCreatedQuest ?? this.lastCreatedQuest,
    );
  }
}

class CollaborationError extends CollaborationState {
  final String message;
  
  const CollaborationError({required this.message});
  
  @override
  List<Object?> get props => [message];
}

class TeamQuestCreated extends CollaborationState {
  final Map<String, dynamic> quest;
  
  const TeamQuestCreated({required this.quest});
  
  @override
  List<Object?> get props => [quest];
}

class TeamQuestJoined extends CollaborationState {
  final String questId;
  final String message;
  
  const TeamQuestJoined({
    required this.questId,
    required this.message,
  });
  
  @override
  List<Object?> get props => [questId, message];
}

class MessageSent extends CollaborationState {
  final String questId;
  final String message;
  
  const MessageSent({
    required this.questId,
    required this.message,
  });
  
  @override
  List<Object?> get props => [questId, message];
}

// BLoC
class CollaborationBloc extends Bloc<CollaborationEvent, CollaborationState> {
  final ApiRepository repository;
  
  CollaborationBloc({required this.repository}) : super(const CollaborationInitial()) {
    on<CreateTeamQuest>(_onCreateTeamQuest);
    on<JoinTeamQuest>(_onJoinTeamQuest);
    on<LoadTeamQuestStatus>(_onLoadTeamQuestStatus);
    on<LoadUserTeamQuests>(_onLoadUserTeamQuests);
    on<SendTeamMessage>(_onSendTeamMessage);
    on<LoadTeamMessages>(_onLoadTeamMessages);
    on<RefreshCollaboration>(_onRefreshCollaboration);
  }

  Future<void> _onCreateTeamQuest(CreateTeamQuest event, Emitter<CollaborationState> emit) async {
    emit(const CollaborationLoading());
    
    try {
      final response = await repository.createTeamQuest(event.questData);
      
      if (response.isSuccess && response.data != null) {
        emit(TeamQuestCreated(quest: response.data!));
        // Update the loaded state with the new quest
        final currentState = state is CollaborationLoaded ? state as CollaborationLoaded : const CollaborationLoaded();
        emit(currentState.copyWith(lastCreatedQuest: response.data));
      } else {
        emit(CollaborationError(message: response.error ?? 'Failed to create team quest'));
      }
    } catch (e) {
      emit(CollaborationError(message: 'Error creating team quest: $e'));
    }
  }

  Future<void> _onJoinTeamQuest(JoinTeamQuest event, Emitter<CollaborationState> emit) async {
    emit(const CollaborationLoading());
    
    try {
      final response = await repository.joinTeamQuest(event.questId, event.userId);
      
      if (response.isSuccess) {
        emit(TeamQuestJoined(
          questId: event.questId,
          message: 'Successfully joined team quest',
        ));
        // Return to loaded state
        if (state is CollaborationLoaded) {
          emit(state as CollaborationLoaded);
        } else {
          emit(const CollaborationLoaded());
        }
      } else {
        emit(CollaborationError(message: response.error ?? 'Failed to join team quest'));
      }
    } catch (e) {
      emit(CollaborationError(message: 'Error joining team quest: $e'));
    }
  }

  Future<void> _onLoadTeamQuestStatus(LoadTeamQuestStatus event, Emitter<CollaborationState> emit) async {
    try {
      final response = await repository.getTeamQuestStatus(event.questId);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is CollaborationLoaded ? state as CollaborationLoaded : const CollaborationLoaded();
        emit(currentState.copyWith(currentQuestStatus: response.data));
      } else {
        emit(CollaborationError(message: response.error ?? 'Failed to load team quest status'));
      }
    } catch (e) {
      emit(CollaborationError(message: 'Error loading team quest status: $e'));
    }
  }

  Future<void> _onLoadUserTeamQuests(LoadUserTeamQuests event, Emitter<CollaborationState> emit) async {
    emit(const CollaborationLoading());
    
    try {
      final response = await repository.getUserTeamQuests(event.userId, status: event.status);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is CollaborationLoaded ? state as CollaborationLoaded : const CollaborationLoaded();
        emit(currentState.copyWith(userTeamQuests: response.data));
      } else {
        emit(CollaborationError(message: response.error ?? 'Failed to load user team quests'));
      }
    } catch (e) {
      emit(CollaborationError(message: 'Error loading user team quests: $e'));
    }
  }

  Future<void> _onSendTeamMessage(SendTeamMessage event, Emitter<CollaborationState> emit) async {
    try {
      final response = await repository.sendTeamMessage(event.questId, event.messageData);
      
      if (response.isSuccess) {
        emit(MessageSent(
          questId: event.questId,
          message: 'Message sent successfully',
        ));
        // Return to previous state after showing success
        if (state is CollaborationLoaded) {
          emit(state as CollaborationLoaded);
        } else {
          emit(const CollaborationLoaded());
        }
      } else {
        emit(CollaborationError(message: response.error ?? 'Failed to send message'));
      }
    } catch (e) {
      emit(CollaborationError(message: 'Error sending message: $e'));
    }
  }

  Future<void> _onLoadTeamMessages(LoadTeamMessages event, Emitter<CollaborationState> emit) async {
    try {
      final response = await repository.getTeamMessages(
        event.questId,
        limit: event.limit,
        offset: event.offset,
      );
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is CollaborationLoaded ? state as CollaborationLoaded : const CollaborationLoaded();
        emit(currentState.copyWith(teamMessages: response.data));
      } else {
        emit(CollaborationError(message: response.error ?? 'Failed to load team messages'));
      }
    } catch (e) {
      emit(CollaborationError(message: 'Error loading team messages: $e'));
    }
  }

  Future<void> _onRefreshCollaboration(RefreshCollaboration event, Emitter<CollaborationState> emit) async {
    emit(const CollaborationLoading());
    
    try {
      // Refresh collaboration data
      emit(const CollaborationLoaded());
    } catch (e) {
      emit(CollaborationError(message: 'Error refreshing collaboration: $e'));
    }
  }
}