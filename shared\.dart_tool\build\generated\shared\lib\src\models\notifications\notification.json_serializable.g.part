// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Notification _$NotificationFromJson(Map<String, dynamic> json) => Notification(
  id: json['id'] as String,
  userId: json['userId'] as String,
  type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
  title: json['title'] as String,
  body: json['body'] as String,
  actionText: json['actionText'] as String?,
  actionUrl: json['actionUrl'] as String?,
  priority: $enumDecode(_$NotificationPriorityEnumMap, json['priority']),
  status: $enumDecode(_$NotificationStatusEnumMap, json['status']),
  deliveryMethods: (json['deliveryMethods'] as List<dynamic>)
      .map((e) => $enumDecode(_$DeliveryMethodEnumMap, e))
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  relatedEntityId: json['relatedEntityId'] as String?,
  relatedEntityType: json['relatedEntityType'] as String?,
  icon: json['icon'] as String?,
  imageUrl: json['imageUrl'] as String?,
  sound: json['sound'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  scheduledAt: json['scheduledAt'] == null
      ? null
      : DateTime.parse(json['scheduledAt'] as String),
  sentAt: json['sentAt'] == null
      ? null
      : DateTime.parse(json['sentAt'] as String),
  readAt: json['readAt'] == null
      ? null
      : DateTime.parse(json['readAt'] as String),
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  retryCount: (json['retryCount'] as num?)?.toInt() ?? 0,
  canGroup: json['canGroup'] as bool? ?? false,
  groupKey: json['groupKey'] as String?,
);

Map<String, dynamic> _$NotificationToJson(Notification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'title': instance.title,
      'body': instance.body,
      'actionText': instance.actionText,
      'actionUrl': instance.actionUrl,
      'priority': _$NotificationPriorityEnumMap[instance.priority]!,
      'status': _$NotificationStatusEnumMap[instance.status]!,
      'deliveryMethods': instance.deliveryMethods
          .map((e) => _$DeliveryMethodEnumMap[e]!)
          .toList(),
      'metadata': instance.metadata,
      'relatedEntityId': instance.relatedEntityId,
      'relatedEntityType': instance.relatedEntityType,
      'icon': instance.icon,
      'imageUrl': instance.imageUrl,
      'sound': instance.sound,
      'createdAt': instance.createdAt.toIso8601String(),
      'scheduledAt': instance.scheduledAt?.toIso8601String(),
      'sentAt': instance.sentAt?.toIso8601String(),
      'readAt': instance.readAt?.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'retryCount': instance.retryCount,
      'canGroup': instance.canGroup,
      'groupKey': instance.groupKey,
    };

const _$NotificationTypeEnumMap = {
  NotificationType.questReminder: 'quest_reminder',
  NotificationType.taskReminder: 'task_reminder',
  NotificationType.deadlineAlert: 'deadline_alert',
  NotificationType.achievementUnlocked: 'achievement_unlocked',
  NotificationType.pointsEarned: 'points_earned',
  NotificationType.levelUp: 'level_up',
  NotificationType.messageReceived: 'message_received',
  NotificationType.collaborationInvite: 'collaboration_invite',
  NotificationType.questInvite: 'quest_invite',
  NotificationType.teamInvite: 'team_invite',
  NotificationType.systemAlert: 'system_alert',
  NotificationType.maintenanceNotice: 'maintenance_notice',
  NotificationType.updateAvailable: 'update_available',
  NotificationType.streakReminder: 'streak_reminder',
  NotificationType.leaderboardUpdate: 'leaderboard_update',
  NotificationType.rewardAvailable: 'reward_available',
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'low',
  NotificationPriority.normal: 'normal',
  NotificationPriority.high: 'high',
  NotificationPriority.urgent: 'urgent',
};

const _$NotificationStatusEnumMap = {
  NotificationStatus.pending: 'pending',
  NotificationStatus.scheduled: 'scheduled',
  NotificationStatus.sent: 'sent',
  NotificationStatus.delivered: 'delivered',
  NotificationStatus.read: 'read',
  NotificationStatus.failed: 'failed',
  NotificationStatus.expired: 'expired',
  NotificationStatus.cancelled: 'cancelled',
};

const _$DeliveryMethodEnumMap = {
  DeliveryMethod.inApp: 'in_app',
  DeliveryMethod.push: 'push',
  DeliveryMethod.email: 'email',
  DeliveryMethod.sms: 'sms',
  DeliveryMethod.webhook: 'webhook',
};

NotificationPreferences _$NotificationPreferencesFromJson(
  Map<String, dynamic> json,
) => NotificationPreferences(
  userId: json['userId'] as String,
  globalEnabled: json['globalEnabled'] as bool? ?? true,
  quietHours: json['quietHours'] == null
      ? null
      : QuietHours.fromJson(json['quietHours'] as Map<String, dynamic>),
  typeSettings: (json['typeSettings'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(
      $enumDecode(_$NotificationTypeEnumMap, k),
      NotificationTypeSettings.fromJson(e as Map<String, dynamic>),
    ),
  ),
  deliveryMethodSettings:
      (json['deliveryMethodSettings'] as Map<String, dynamic>).map(
        (k, e) => MapEntry($enumDecode(_$DeliveryMethodEnumMap, k), e as bool),
      ),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$NotificationPreferencesToJson(
  NotificationPreferences instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'globalEnabled': instance.globalEnabled,
  'quietHours': instance.quietHours,
  'typeSettings': instance.typeSettings.map(
    (k, e) => MapEntry(_$NotificationTypeEnumMap[k]!, e),
  ),
  'deliveryMethodSettings': instance.deliveryMethodSettings.map(
    (k, e) => MapEntry(_$DeliveryMethodEnumMap[k]!, e),
  ),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

NotificationTypeSettings _$NotificationTypeSettingsFromJson(
  Map<String, dynamic> json,
) => NotificationTypeSettings(
  enabled: json['enabled'] as bool? ?? true,
  enabledMethods: (json['enabledMethods'] as List<dynamic>)
      .map((e) => $enumDecode(_$DeliveryMethodEnumMap, e))
      .toList(),
  customSound: json['customSound'] as String?,
  showPreview: json['showPreview'] as bool? ?? true,
);

Map<String, dynamic> _$NotificationTypeSettingsToJson(
  NotificationTypeSettings instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'enabledMethods': instance.enabledMethods
      .map((e) => _$DeliveryMethodEnumMap[e]!)
      .toList(),
  'customSound': instance.customSound,
  'showPreview': instance.showPreview,
};
