/// Security Policy Validation Utilities
/// 
/// Provides comprehensive validation for various security policies
/// used throughout the Quester platform.
library;

import '../models/security/organization_security_policy.dart';

/// Security policy validation result
class SecurityValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const SecurityValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  SecurityValidationResult.success() : this(isValid: true);
  
  SecurityValidationResult.failure(List<String> errors, [List<String> warnings = const []])
      : this(isValid: false, errors: errors, warnings: warnings);
}

/// Comprehensive security policy validator
class SecurityPolicyValidator {
  
  /// Validates organization security policy
  static SecurityValidationResult validateOrganizationSecurityPolicy(OrganizationSecurityPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate password policy
    if (policy.passwordPolicy.minLength < 8) {
      errors.add('Password minimum length must be at least 8 characters');
    }
    
    if (policy.passwordPolicy.minLength > 128) {
      errors.add('Password minimum length cannot exceed 128 characters');
    }
    
    if (!policy.passwordPolicy.requireUppercase && 
        !policy.passwordPolicy.requireLowercase && 
        !policy.passwordPolicy.requireNumbers && 
        !policy.passwordPolicy.requireSymbols) {
      warnings.add('Password policy should require at least one character type requirement');
    }

    // Validate session policy
    if (policy.sessionPolicy.idleTimeoutMinutes <= 0) {
      errors.add('Idle timeout must be positive');
    }
    
    if (policy.sessionPolicy.absoluteTimeoutHours <= 0) {
      errors.add('Absolute timeout must be positive');
    }
    
    if (policy.sessionPolicy.absoluteTimeoutHours > 24) {
      warnings.add('Session duration longer than 24 hours may pose security risks');
    }
    
    if (policy.sessionPolicy.concurrentSessionsLimit <= 0) {
      errors.add('Concurrent sessions limit must be positive');
    }

    // Validate MFA policy
    if (policy.mfaPolicy.required && policy.mfaPolicy.allowedMethods.isEmpty) {
      errors.add('MFA is required but no methods are allowed');
    }
    
    if (policy.mfaPolicy.backupCodesCount != null) {
      if (policy.mfaPolicy.backupCodesCount! < 0) {
        errors.add('Backup codes count cannot be negative');
      }
      
      if (policy.mfaPolicy.backupCodesCount! > 20) {
        warnings.add('Large number of backup codes may be difficult to manage');
      }
    }

    return SecurityValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validates password against policy requirements
  static SecurityValidationResult validatePassword(String password, PasswordPolicy policy) {
    final errors = <String>[];
    
    if (password.length < policy.minLength) {
      errors.add('Password must be at least ${policy.minLength} characters long');
    }
    
    if (policy.requireUppercase && !password.contains(RegExp(r'[A-Z]'))) {
      errors.add('Password must contain at least one uppercase letter');
    }
    
    if (policy.requireLowercase && !password.contains(RegExp(r'[a-z]'))) {
      errors.add('Password must contain at least one lowercase letter');
    }
    
    if (policy.requireNumbers && !password.contains(RegExp(r'[0-9]'))) {
      errors.add('Password must contain at least one number');
    }
    
    if (policy.requireSymbols && !password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      errors.add('Password must contain at least one special character');
    }

    return SecurityValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Validates session configuration
  static SecurityValidationResult validateSessionPolicy(SessionPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    if (policy.idleTimeoutMinutes <= 0) {
      errors.add('Idle timeout must be positive');
    }
    
    if (policy.absoluteTimeoutHours <= 0) {
      errors.add('Absolute timeout must be positive');
    }
    
    if (policy.idleTimeoutMinutes >= (policy.absoluteTimeoutHours * 60)) {
      warnings.add('Idle timeout should be less than absolute timeout');
    }
    
    if (policy.concurrentSessionsLimit <= 0) {
      errors.add('Concurrent sessions limit must be positive');
    }
    
    if (policy.concurrentSessionsLimit > 100) {
      warnings.add('Large number of concurrent sessions may impact performance');
    }

    return SecurityValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validates MFA policy configuration
  static SecurityValidationResult validateMFAPolicy(MFAPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    if (policy.required && policy.allowedMethods.isEmpty) {
      errors.add('MFA is required but no allowed methods specified');
    }

    final validMethods = ['totp', 'sms', 'email', 'backup_codes', 'webauthn'];
    for (final method in policy.allowedMethods) {
      if (!validMethods.contains(method)) {
        warnings.add('Unknown MFA method: $method');
      }
    }

    if (policy.gracePeriodDays < 0) {
      errors.add('Grace period cannot be negative');
    }

    if (policy.gracePeriodDays > 30) {
      warnings.add('Long grace period may reduce security effectiveness');
    }

    if (policy.backupCodesCount != null) {
      if (policy.backupCodesCount! < 0) {
        errors.add('Backup codes count cannot be negative');
      }
      if (policy.backupCodesCount! > 20) {
        warnings.add('Too many backup codes may be hard to manage');
      }
    }

    return SecurityValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validates password strength
  static SecurityValidationResult validatePasswordStrength(String password) {
    final errors = <String>[];
    final warnings = <String>[];

    if (password.length < 8) {
      errors.add('Password must be at least 8 characters long');
    }

    if (password.length < 12) {
      warnings.add('Consider using a longer password for better security');
    }

    bool hasUpper = password.contains(RegExp(r'[A-Z]'));
    bool hasLower = password.contains(RegExp(r'[a-z]'));
    bool hasDigit = password.contains(RegExp(r'[0-9]'));
    bool hasSpecial = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    int complexity = 0;
    if (hasUpper) complexity++;
    if (hasLower) complexity++;
    if (hasDigit) complexity++;
    if (hasSpecial) complexity++;

    if (complexity < 3) {
      errors.add('Password must contain at least 3 different character types');
    }

    // Check for common patterns
    if (RegExp(r'(.)\1{2,}').hasMatch(password)) {
      warnings.add('Avoid repeating characters');
    }

    if (RegExp(r'123|abc|qwe', caseSensitive: false).hasMatch(password)) {
      warnings.add('Avoid common sequences');
    }

    return SecurityValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}