import 'package:equatable/equatable.dart';

/// Organization subscription tiers
enum SubscriptionTier {
  free,
  team,
  business,
  enterprise,
}

/// Organization status
enum OrganizationStatus {
  active,
  suspended,
  deactivated,
  trial,
}

/// Enterprise organization model for multi-tenancy
class Organization extends Equatable {
  /// Unique organization identifier
  final String id;

  /// Organization name
  final String name;

  /// Organization slug for URLs
  final String slug;

  /// Organization description
  final String? description;

  /// Organization logo URL
  final String? logoUrl;

  /// Primary domain for the organization
  final String? primaryDomain;

  /// Organization status
  final OrganizationStatus status;

  /// Subscription tier
  final SubscriptionTier subscriptionTier;

  /// Maximum number of users allowed
  final int maxUsers;

  /// Current number of active users
  final int currentUsers;

  /// Organization settings and configuration
  final Map<String, dynamic> settings;

  /// Gamification configuration
  final Map<String, dynamic> gamificationConfig;

  /// SSO configuration
  final Map<String, dynamic>? ssoConfig;

  /// Billing configuration
  final Map<String, dynamic>? billingConfig;

  /// Organization metadata
  final Map<String, dynamic>? metadata;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Trial end date (if applicable)
  final DateTime? trialEndsAt;

  /// Subscription end date
  final DateTime? subscriptionEndsAt;

  const Organization({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.logoUrl,
    this.primaryDomain,
    required this.status,
    required this.subscriptionTier,
    required this.maxUsers,
    required this.currentUsers,
    required this.settings,
    required this.gamificationConfig,
    this.ssoConfig,
    this.billingConfig,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.trialEndsAt,
    this.subscriptionEndsAt,
  });

  /// Create Organization from JSON
  factory Organization.fromJson(Map<String, dynamic> json) {
    return Organization(
      id: json['id'] as String,
      name: json['name'] as String,
      slug: json['slug'] as String,
      description: json['description'] as String?,
      logoUrl: json['logoUrl'] as String?,
      primaryDomain: json['primaryDomain'] as String?,
      status: OrganizationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => OrganizationStatus.active,
      ),
      subscriptionTier: SubscriptionTier.values.firstWhere(
        (e) => e.name == json['subscriptionTier'],
        orElse: () => SubscriptionTier.free,
      ),
      maxUsers: json['maxUsers'] as int,
      currentUsers: json['currentUsers'] as int,
      settings: Map<String, dynamic>.from(json['settings'] as Map),
      gamificationConfig: Map<String, dynamic>.from(json['gamificationConfig'] as Map),
      ssoConfig: json['ssoConfig'] != null 
          ? Map<String, dynamic>.from(json['ssoConfig'] as Map)
          : null,
      billingConfig: json['billingConfig'] != null
          ? Map<String, dynamic>.from(json['billingConfig'] as Map)
          : null,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      trialEndsAt: json['trialEndsAt'] != null
          ? DateTime.parse(json['trialEndsAt'] as String)
          : null,
      subscriptionEndsAt: json['subscriptionEndsAt'] != null
          ? DateTime.parse(json['subscriptionEndsAt'] as String)
          : null,
    );
  }

  /// Convert Organization to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'logoUrl': logoUrl,
      'primaryDomain': primaryDomain,
      'status': status.name,
      'subscriptionTier': subscriptionTier.name,
      'maxUsers': maxUsers,
      'currentUsers': currentUsers,
      'settings': settings,
      'gamificationConfig': gamificationConfig,
      'ssoConfig': ssoConfig,
      'billingConfig': billingConfig,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'trialEndsAt': trialEndsAt?.toIso8601String(),
      'subscriptionEndsAt': subscriptionEndsAt?.toIso8601String(),
    };
  }

  /// Check if organization is on trial
  bool get isOnTrial => status == OrganizationStatus.trial;

  /// Check if trial has expired
  bool get isTrialExpired {
    if (!isOnTrial || trialEndsAt == null) return false;
    return DateTime.now().isAfter(trialEndsAt!);
  }

  /// Check if subscription is active
  bool get hasActiveSubscription {
    if (subscriptionTier == SubscriptionTier.free) return true;
    if (subscriptionEndsAt == null) return true;
    return DateTime.now().isBefore(subscriptionEndsAt!);
  }

  /// Get remaining trial days
  int get remainingTrialDays {
    if (!isOnTrial || trialEndsAt == null) return 0;
    final difference = trialEndsAt!.difference(DateTime.now());
    return difference.inDays.clamp(0, double.infinity).toInt();
  }

  /// Check if organization can add more users
  bool get canAddUsers => currentUsers < maxUsers;

  /// Get remaining user slots
  int get remainingUserSlots => (maxUsers - currentUsers).clamp(0, maxUsers);

  /// Get subscription tier features
  Map<String, dynamic> get tierFeatures {
    switch (subscriptionTier) {
      case SubscriptionTier.free:
        return {
          'maxUsers': 10,
          'maxTeams': 3,
          'analyticsRetention': 30, // days
          'apiRateLimit': 1000, // per hour
          'storageLimit': 1024, // MB
          'features': ['basic_gamification', 'team_leaderboards'],
        };
      case SubscriptionTier.team:
        return {
          'maxUsers': 50,
          'maxTeams': 10,
          'analyticsRetention': 90,
          'apiRateLimit': 5000,
          'storageLimit': 5120,
          'features': [
            'basic_gamification',
            'team_leaderboards',
            'custom_achievements',
            'advanced_reporting',
          ],
        };
      case SubscriptionTier.business:
        return {
          'maxUsers': 200,
          'maxTeams': 50,
          'analyticsRetention': 365,
          'apiRateLimit': 25000,
          'storageLimit': 25600,
          'features': [
            'basic_gamification',
            'team_leaderboards',
            'custom_achievements',
            'advanced_reporting',
            'api_access',
            'sso_integration',
            'custom_branding',
          ],
        };
      case SubscriptionTier.enterprise:
        return {
          'maxUsers': -1, // unlimited
          'maxTeams': -1,
          'analyticsRetention': -1, // unlimited
          'apiRateLimit': 100000,
          'storageLimit': -1, // unlimited
          'features': [
            'basic_gamification',
            'team_leaderboards',
            'custom_achievements',
            'advanced_reporting',
            'api_access',
            'sso_integration',
            'custom_branding',
            'white_labeling',
            'advanced_analytics',
            'compliance_reporting',
            'priority_support',
            'dedicated_success_manager',
          ],
        };
    }
  }

  /// Create a copy with updated fields
  Organization copyWith({
    String? id,
    String? name,
    String? slug,
    String? description,
    String? logoUrl,
    String? primaryDomain,
    OrganizationStatus? status,
    SubscriptionTier? subscriptionTier,
    int? maxUsers,
    int? currentUsers,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? gamificationConfig,
    Map<String, dynamic>? ssoConfig,
    Map<String, dynamic>? billingConfig,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? trialEndsAt,
    DateTime? subscriptionEndsAt,
  }) {
    return Organization(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      description: description ?? this.description,
      logoUrl: logoUrl ?? this.logoUrl,
      primaryDomain: primaryDomain ?? this.primaryDomain,
      status: status ?? this.status,
      subscriptionTier: subscriptionTier ?? this.subscriptionTier,
      maxUsers: maxUsers ?? this.maxUsers,
      currentUsers: currentUsers ?? this.currentUsers,
      settings: settings ?? this.settings,
      gamificationConfig: gamificationConfig ?? this.gamificationConfig,
      ssoConfig: ssoConfig ?? this.ssoConfig,
      billingConfig: billingConfig ?? this.billingConfig,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      trialEndsAt: trialEndsAt ?? this.trialEndsAt,
      subscriptionEndsAt: subscriptionEndsAt ?? this.subscriptionEndsAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        slug,
        description,
        logoUrl,
        primaryDomain,
        status,
        subscriptionTier,
        maxUsers,
        currentUsers,
        settings,
        gamificationConfig,
        ssoConfig,
        billingConfig,
        metadata,
        createdAt,
        updatedAt,
        trialEndsAt,
        subscriptionEndsAt,
      ];

  @override
  bool get stringify => true;
}
