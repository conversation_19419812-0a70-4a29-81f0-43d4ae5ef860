import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/freelancing_service.dart';

/// Freelancing marketplace routes handler
class FreelancingRoutes {
  static late FreelancingService _freelancingService;

  static void initialize(FreelancingService freelancingService) {
    _freelancingService = freelancingService;
  }

  static Router createRouter() {
    final router = Router()
      // Health check
      ..get('/health', _healthHandler)
      
      // Project Management
      ..post('/projects', _createProjectHandler)
      ..get('/projects/<projectId>', _getProjectHandler)
      ..put('/projects/<projectId>', _updateProjectHandler)
      ..get('/projects', _searchProjectsHandler)
      
      // Freelancer Profile Management
      ..post('/freelancers/profile', _createFreelancerProfileHandler)
      ..get('/freelancers/<userId>/profile', _getFreelancerProfileHandler)
      ..put('/freelancers/<userId>/profile', _updateFreelancerProfileHandler)
      
      // Proposal Management
      ..post('/proposals', _submitProposalHandler)
      ..get('/proposals/<proposalId>', _getProposalHandler)
      ..get('/projects/<projectId>/proposals', _getProjectProposalsHandler)
      
      // Contract Management
      ..post('/contracts', _createContractHandler)
      ..get('/contracts/<contractId>', _getContractHandler)
      
      // Payment and Billing
      ..post('/contracts/<contractId>/payments', _processPaymentHandler)
      
      // Analytics and Reporting
      ..get('/freelancers/<freelancerId>/analytics', _getFreelancerAnalyticsHandler);

    return router;
  }

  static Response _healthHandler(Request request) {
    return Response.ok(jsonEncode({
      'status': 'operational',
      'timestamp': DateTime.now().toIso8601String(),
      'service': 'freelancing',
      'version': '1.0.0'
    }));
  }

  // Project Management Handlers
  static Future<Response> _createProjectHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _freelancingService.createProject(data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to create project: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getProjectHandler(Request request, String projectId) async {
    try {
      final result = await _freelancingService.getProject(projectId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get project: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _updateProjectHandler(Request request, String projectId) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _freelancingService.updateProject(projectId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to update project: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _searchProjectsHandler(Request request) async {
    try {
      final params = request.url.queryParameters;
      
      final result = await _freelancingService.searchProjects(
        category: params['category'],
        skills: params['skills']?.split(','),
        budgetType: params['budgetType'],
        minBudget: int.tryParse(params['minBudget'] ?? ''),
        maxBudget: int.tryParse(params['maxBudget'] ?? ''),
        page: int.tryParse(params['page'] ?? '1') ?? 1,
        limit: int.tryParse(params['limit'] ?? '20') ?? 20,
      );
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to search projects: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Freelancer Profile Management Handlers
  static Future<Response> _createFreelancerProfileHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _freelancingService.createFreelancerProfile(data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to create freelancer profile: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getFreelancerProfileHandler(Request request, String userId) async {
    try {
      final result = await _freelancingService.getFreelancerProfile(userId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get freelancer profile: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _updateFreelancerProfileHandler(Request request, String userId) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _freelancingService.updateFreelancerProfile(userId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to update freelancer profile: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Proposal Management Handlers
  static Future<Response> _submitProposalHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _freelancingService.submitProposal(data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to submit proposal: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getProposalHandler(Request request, String proposalId) async {
    try {
      final result = await _freelancingService.getProposal(proposalId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get proposal: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getProjectProposalsHandler(Request request, String projectId) async {
    try {
      final result = await _freelancingService.getProjectProposals(projectId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get project proposals: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Contract Management Handlers
  static Future<Response> _createContractHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _freelancingService.createContract(data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to create contract: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getContractHandler(Request request, String contractId) async {
    try {
      final result = await _freelancingService.getContract(contractId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get contract: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Payment and Billing Handlers
  static Future<Response> _processPaymentHandler(Request request, String contractId) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _freelancingService.processPayment(contractId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to process payment: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Analytics and Reporting Handlers
  static Future<Response> _getFreelancerAnalyticsHandler(Request request, String freelancerId) async {
    try {
      final result = await _freelancingService.getFreelancerAnalytics(freelancerId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get freelancer analytics: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }
}