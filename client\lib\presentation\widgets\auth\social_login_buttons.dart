import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Social login buttons widget for authentication screens
class SocialLoginButtons extends StatelessWidget {
  /// Whether to show all social login options
  final bool showAll;
  
  /// Custom button height
  final double? buttonHeight;
  
  /// Spacing between buttons
  final double spacing;
  
  /// Button layout direction
  final Axis direction;

  const SocialLoginButtons({
    super.key,
    this.showAll = true,
    this.buttonHeight,
    this.spacing = AppConstants.defaultPadding,
    this.direction = Axis.horizontal,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = _buildSocialButtons(context);
    
    if (direction == Axis.horizontal) {
      return Row(
        children: _intersperse(buttons, SizedBox(width: spacing)),
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: _intersperse(buttons, SizedBox(height: spacing)),
      );
    }
  }

  /// Build social login buttons
  List<Widget> _buildSocialButtons(BuildContext context) {
    final buttons = <Widget>[
      _SocialLoginButton(
        provider: SocialProvider.google,
        onPressed: () => _handleSocialLogin(context, SocialProvider.google),
        height: buttonHeight,
        isExpanded: direction == Axis.vertical,
      ),
      _SocialLoginButton(
        provider: SocialProvider.apple,
        onPressed: () => _handleSocialLogin(context, SocialProvider.apple),
        height: buttonHeight,
        isExpanded: direction == Axis.vertical,
      ),
    ];

    if (showAll) {
      buttons.addAll([
        _SocialLoginButton(
          provider: SocialProvider.facebook,
          onPressed: () => _handleSocialLogin(context, SocialProvider.facebook),
          height: buttonHeight,
          isExpanded: direction == Axis.vertical,
        ),
        _SocialLoginButton(
          provider: SocialProvider.github,
          onPressed: () => _handleSocialLogin(context, SocialProvider.github),
          height: buttonHeight,
          isExpanded: direction == Axis.vertical,
        ),
      ]);
    }

    return buttons;
  }

  /// Handle social login
  void _handleSocialLogin(BuildContext context, SocialProvider provider) {
    // TODO: Implement social login
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${provider.name} login coming soon!'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Intersperse widgets with separator
  List<Widget> _intersperse<T>(List<T> list, T separator) {
    if (list.isEmpty) return [];
    
    final result = <T>[];
    for (int i = 0; i < list.length; i++) {
      result.add(list[i]);
      if (i < list.length - 1) {
        result.add(separator);
      }
    }
    return result;
  }
}

/// Individual social login button
class _SocialLoginButton extends StatelessWidget {
  final SocialProvider provider;
  final VoidCallback onPressed;
  final double? height;
  final bool isExpanded;

  const _SocialLoginButton({
    required this.provider,
    required this.onPressed,
    this.height,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getSocialConfig(provider);
    final effectiveHeight = height ?? 48.0;

    Widget button = Container(
      height: effectiveHeight,
      decoration: BoxDecoration(
        color: config.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.smallPadding,
            ),
            child: Row(
              mainAxisSize: isExpanded ? MainAxisSize.max : MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildIcon(config),
                if (isExpanded) ...[
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: Text(
                      'Continue with ${config.name}',
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: config.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );

    if (isExpanded) {
      return button;
    } else {
      return Expanded(child: button);
    }
  }

  /// Build provider icon
  Widget _buildIcon(SocialConfig config) {
    if (config.iconData != null) {
      return Icon(
        config.iconData,
        color: config.iconColor,
        size: 24,
      );
    } else if (config.iconAsset != null) {
      return Image.asset(
        config.iconAsset!,
        width: 24,
        height: 24,
      );
    } else {
      // Fallback to text
      return Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: config.iconColor,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            config.name.substring(0, 1).toUpperCase(),
            style: TextStyle(
              color: config.backgroundColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      );
    }
  }

  /// Get social provider configuration
  SocialConfig _getSocialConfig(SocialProvider provider) {
    switch (provider) {
      case SocialProvider.google:
        return SocialConfig(
          name: 'Google',
          backgroundColor: Colors.white,
          textColor: Colors.black87,
          iconColor: Colors.red,
          iconData: Icons.g_mobiledata,
        );
      case SocialProvider.apple:
        return SocialConfig(
          name: 'Apple',
          backgroundColor: Colors.black,
          textColor: Colors.white,
          iconColor: Colors.white,
          iconData: Icons.apple,
        );
      case SocialProvider.facebook:
        return SocialConfig(
          name: 'Facebook',
          backgroundColor: const Color(0xFF1877F2),
          textColor: Colors.white,
          iconColor: Colors.white,
          iconData: Icons.facebook,
        );
      case SocialProvider.github:
        return SocialConfig(
          name: 'GitHub',
          backgroundColor: const Color(0xFF24292E),
          textColor: Colors.white,
          iconColor: Colors.white,
          iconData: Icons.code,
        );
    }
  }
}

/// Compact social login buttons for limited space
class CompactSocialLoginButtons extends StatelessWidget {
  /// Button size
  final double size;
  
  /// Spacing between buttons
  final double spacing;

  const CompactSocialLoginButtons({
    super.key,
    this.size = 48,
    this.spacing = AppConstants.defaultPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _CompactSocialButton(
          provider: SocialProvider.google,
          size: size,
          onPressed: () => _handleSocialLogin(context, SocialProvider.google),
        ),
        SizedBox(width: spacing),
        _CompactSocialButton(
          provider: SocialProvider.apple,
          size: size,
          onPressed: () => _handleSocialLogin(context, SocialProvider.apple),
        ),
        SizedBox(width: spacing),
        _CompactSocialButton(
          provider: SocialProvider.facebook,
          size: size,
          onPressed: () => _handleSocialLogin(context, SocialProvider.facebook),
        ),
        SizedBox(width: spacing),
        _CompactSocialButton(
          provider: SocialProvider.github,
          size: size,
          onPressed: () => _handleSocialLogin(context, SocialProvider.github),
        ),
      ],
    );
  }

  /// Handle social login
  void _handleSocialLogin(BuildContext context, SocialProvider provider) {
    // TODO: Implement social login
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${provider.name} login coming soon!'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// Compact social login button
class _CompactSocialButton extends StatelessWidget {
  final SocialProvider provider;
  final double size;
  final VoidCallback onPressed;

  const _CompactSocialButton({
    required this.provider,
    required this.size,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getSocialConfig(provider);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: config.backgroundColor,
        shape: BoxShape.circle,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          child: Center(
            child: Icon(
              config.iconData,
              color: config.iconColor,
              size: size * 0.5,
            ),
          ),
        ),
      ),
    );
  }

  /// Get social provider configuration
  SocialConfig _getSocialConfig(SocialProvider provider) {
    switch (provider) {
      case SocialProvider.google:
        return SocialConfig(
          name: 'Google',
          backgroundColor: Colors.white,
          textColor: Colors.black87,
          iconColor: Colors.red,
          iconData: Icons.g_mobiledata,
        );
      case SocialProvider.apple:
        return SocialConfig(
          name: 'Apple',
          backgroundColor: Colors.black,
          textColor: Colors.white,
          iconColor: Colors.white,
          iconData: Icons.apple,
        );
      case SocialProvider.facebook:
        return SocialConfig(
          name: 'Facebook',
          backgroundColor: const Color(0xFF1877F2),
          textColor: Colors.white,
          iconColor: Colors.white,
          iconData: Icons.facebook,
        );
      case SocialProvider.github:
        return SocialConfig(
          name: 'GitHub',
          backgroundColor: const Color(0xFF24292E),
          textColor: Colors.white,
          iconColor: Colors.white,
          iconData: Icons.code,
        );
    }
  }
}

/// Social provider enumeration
enum SocialProvider {
  google,
  apple,
  facebook,
  github,
}

/// Social provider configuration
class SocialConfig {
  final String name;
  final Color backgroundColor;
  final Color textColor;
  final Color iconColor;
  final IconData? iconData;
  final String? iconAsset;

  const SocialConfig({
    required this.name,
    required this.backgroundColor,
    required this.textColor,
    required this.iconColor,
    this.iconData,
    this.iconAsset,
  });
}
