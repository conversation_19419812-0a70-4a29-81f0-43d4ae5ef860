import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'prediction_models.g.dart';

/// Prediction model types
enum PredictionModelType {
  @JsonValue('user_behavior')
  userBehavior,
  @JsonValue('task_completion')
  taskCompletion,
  @JsonValue('engagement_forecast')
  engagementForecast,
  @JsonValue('churn_prediction')
  churnPrediction,
  @JsonValue('performance_optimization')
  performanceOptimization,
  @JsonValue('skill_recommendation')
  skillRecommendation,
  @JsonValue('project_success')
  projectSuccess,
  @JsonValue('resource_allocation')
  resourceAllocation,
}

/// Confidence levels for predictions
enum ConfidenceLevel {
  @JsonValue('very_low')
  veryLow,
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('very_high')
  veryHigh,
}

/// Risk levels for predictions
enum RiskLevel {
  @JsonValue('very_low')
  veryLow,
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

/// Feature importance for model interpretation
@JsonSerializable()
class FeatureImportance extends Equatable {
  /// Feature name
  final String featureName;

  /// Feature importance score (0.0 to 1.0)
  final double importance;

  /// Feature category
  final String category;

  /// Feature description
  final String? description;

  const FeatureImportance({
    required this.featureName,
    required this.importance,
    required this.category,
    this.description,
  });

  /// Create FeatureImportance from JSON
  factory FeatureImportance.fromJson(Map<String, dynamic> json) => _$FeatureImportanceFromJson(json);

  /// Convert FeatureImportance to JSON
  Map<String, dynamic> toJson() => _$FeatureImportanceToJson(this);

  @override
  List<Object?> get props => [featureName, importance, category, description];
}

/// Time series prediction point
@JsonSerializable()
class TimeSeriesPrediction extends Equatable {
  /// Timestamp for the prediction
  final DateTime timestamp;

  /// Predicted value
  final double predictedValue;

  /// Upper confidence bound
  final double? upperBound;

  /// Lower confidence bound
  final double? lowerBound;

  /// Confidence score (0.0 to 1.0)
  final double confidence;

  const TimeSeriesPrediction({
    required this.timestamp,
    required this.predictedValue,
    this.upperBound,
    this.lowerBound,
    required this.confidence,
  });

  /// Create TimeSeriesPrediction from JSON
  factory TimeSeriesPrediction.fromJson(Map<String, dynamic> json) => _$TimeSeriesPredictionFromJson(json);

  /// Convert TimeSeriesPrediction to JSON
  Map<String, dynamic> toJson() => _$TimeSeriesPredictionToJson(this);

  @override
  List<Object?> get props => [timestamp, predictedValue, upperBound, lowerBound, confidence];
}

/// Base prediction model
@JsonSerializable()
class PredictionModel extends Equatable {
  /// Model ID
  final String id;

  /// Model type
  final PredictionModelType type;

  /// Model name
  final String name;

  /// Model description
  final String? description;

  /// Model version
  final String version;

  /// Organization ID (if applicable)
  final String? organizationId;

  /// Target entity ID (user, project, etc.)
  final String? targetEntityId;

  /// Prediction confidence level
  final ConfidenceLevel confidenceLevel;

  /// Confidence score (0.0 to 1.0)
  final double confidenceScore;

  /// Prediction accuracy (if available from validation)
  final double? accuracy;

  /// Model training timestamp
  final DateTime trainedAt;

  /// Last prediction timestamp
  final DateTime? lastPredictionAt;

  /// Model expiry timestamp
  final DateTime? expiresAt;

  /// Input features used by the model
  final List<String> inputFeatures;

  /// Feature importance rankings
  final List<FeatureImportance>? featureImportances;

  /// Model metadata
  final Map<String, dynamic> metadata;

  /// Whether model is active
  final bool isActive;

  const PredictionModel({
    required this.id,
    required this.type,
    required this.name,
    this.description,
    required this.version,
    this.organizationId,
    this.targetEntityId,
    required this.confidenceLevel,
    required this.confidenceScore,
    this.accuracy,
    required this.trainedAt,
    this.lastPredictionAt,
    this.expiresAt,
    required this.inputFeatures,
    this.featureImportances,
    this.metadata = const {},
    this.isActive = true,
  });

  /// Create PredictionModel from JSON
  factory PredictionModel.fromJson(Map<String, dynamic> json) => _$PredictionModelFromJson(json);

  /// Convert PredictionModel to JSON
  Map<String, dynamic> toJson() => _$PredictionModelToJson(this);

  /// Check if model is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Get confidence level as percentage string
  String get confidencePercentage => '${(confidenceScore * 100).toStringAsFixed(1)}%';

  @override
  List<Object?> get props => [
        id,
        type,
        name,
        description,
        version,
        organizationId,
        targetEntityId,
        confidenceLevel,
        confidenceScore,
        accuracy,
        trainedAt,
        lastPredictionAt,
        expiresAt,
        inputFeatures,
        featureImportances,
        metadata,
        isActive,
      ];
}

/// User behavior prediction model
@JsonSerializable()
class UserBehaviorPrediction extends Equatable {
  /// Base prediction model
  final PredictionModel model;

  /// Predicted engagement score (0.0 to 1.0)
  final double engagementScore;

  /// Predicted productivity score (0.0 to 1.0)
  final double productivityScore;

  /// Churn risk level
  final RiskLevel churnRisk;

  /// Churn probability (0.0 to 1.0)
  final double churnProbability;

  /// Recommended actions to improve engagement
  final List<String> recommendedActions;

  /// Next likely activities
  final List<Map<String, dynamic>> nextActivities;

  /// Optimal times for user engagement
  final List<DateTime> optimalEngagementTimes;

  /// Predicted completion time for current tasks (in hours)
  final double? predictedCompletionTime;

  /// Skills likely to be developed next
  final List<String> skillDevelopmentOpportunities;

  const UserBehaviorPrediction({
    required this.model,
    required this.engagementScore,
    required this.productivityScore,
    required this.churnRisk,
    required this.churnProbability,
    this.recommendedActions = const [],
    this.nextActivities = const [],
    this.optimalEngagementTimes = const [],
    this.predictedCompletionTime,
    this.skillDevelopmentOpportunities = const [],
  });

  /// Create UserBehaviorPrediction from JSON
  factory UserBehaviorPrediction.fromJson(Map<String, dynamic> json) => _$UserBehaviorPredictionFromJson(json);

  /// Convert UserBehaviorPrediction to JSON
  Map<String, dynamic> toJson() => _$UserBehaviorPredictionToJson(this);

  @override
  List<Object?> get props => [
        model,
        engagementScore,
        productivityScore,
        churnRisk,
        churnProbability,
        recommendedActions,
        nextActivities,
        optimalEngagementTimes,
        predictedCompletionTime,
        skillDevelopmentOpportunities,
      ];
}

/// Task completion prediction model
@JsonSerializable()
class TaskCompletionPrediction extends Equatable {
  /// Base prediction model
  final PredictionModel model;

  /// Task ID
  final String taskId;

  /// Completion probability (0.0 to 1.0)
  final double completionProbability;

  /// Predicted completion date
  final DateTime? predictedCompletionDate;

  /// Predicted effort hours
  final double? predictedEffortHours;

  /// Risk factors that might affect completion
  final List<String> riskFactors;

  /// Success factors that support completion
  final List<String> successFactors;

  /// Recommended resources or support
  final List<String> recommendedResources;

  /// Blockers that might prevent completion
  final List<String> potentialBlockers;

  /// Similar tasks for comparison
  final List<Map<String, dynamic>> similarTasks;

  const TaskCompletionPrediction({
    required this.model,
    required this.taskId,
    required this.completionProbability,
    this.predictedCompletionDate,
    this.predictedEffortHours,
    this.riskFactors = const [],
    this.successFactors = const [],
    this.recommendedResources = const [],
    this.potentialBlockers = const [],
    this.similarTasks = const [],
  });

  /// Create TaskCompletionPrediction from JSON
  factory TaskCompletionPrediction.fromJson(Map<String, dynamic> json) => _$TaskCompletionPredictionFromJson(json);

  /// Convert TaskCompletionPrediction to JSON
  Map<String, dynamic> toJson() => _$TaskCompletionPredictionToJson(this);

  /// Get completion likelihood as text
  String get completionLikelihood {
    if (completionProbability >= 0.8) return 'Very Likely';
    if (completionProbability >= 0.6) return 'Likely';
    if (completionProbability >= 0.4) return 'Moderate';
    if (completionProbability >= 0.2) return 'Unlikely';
    return 'Very Unlikely';
  }

  @override
  List<Object?> get props => [
        model,
        taskId,
        completionProbability,
        predictedCompletionDate,
        predictedEffortHours,
        riskFactors,
        successFactors,
        recommendedResources,
        potentialBlockers,
        similarTasks,
      ];
}

/// Engagement forecast model
@JsonSerializable()
class EngagementForecast extends Equatable {
  /// Base prediction model
  final PredictionModel model;

  /// Organization or user ID
  final String entityId;

  /// Entity type (organization, user, team)
  final String entityType;

  /// Time series predictions for engagement
  final List<TimeSeriesPrediction> engagementTimeSeries;

  /// Predicted peak engagement periods
  final List<DateTime> peakEngagementPeriods;

  /// Predicted low engagement periods
  final List<DateTime> lowEngagementPeriods;

  /// Factors influencing engagement
  final Map<String, double> engagementFactors;

  /// Seasonal patterns detected
  final Map<String, dynamic> seasonalPatterns;

  /// Trend direction (positive, negative, stable)
  final String trendDirection;

  /// Forecast horizon (number of days)
  final int forecastHorizonDays;

  const EngagementForecast({
    required this.model,
    required this.entityId,
    required this.entityType,
    required this.engagementTimeSeries,
    this.peakEngagementPeriods = const [],
    this.lowEngagementPeriods = const [],
    this.engagementFactors = const {},
    this.seasonalPatterns = const {},
    required this.trendDirection,
    required this.forecastHorizonDays,
  });

  /// Create EngagementForecast from JSON
  factory EngagementForecast.fromJson(Map<String, dynamic> json) => _$EngagementForecastFromJson(json);

  /// Convert EngagementForecast to JSON
  Map<String, dynamic> toJson() => _$EngagementForecastToJson(this);

  /// Get average predicted engagement
  double get averagePredictedEngagement {
    if (engagementTimeSeries.isEmpty) return 0.0;
    final sum = engagementTimeSeries.fold(0.0, (sum, prediction) => sum + prediction.predictedValue);
    return sum / engagementTimeSeries.length;
  }

  @override
  List<Object?> get props => [
        model,
        entityId,
        entityType,
        engagementTimeSeries,
        peakEngagementPeriods,
        lowEngagementPeriods,
        engagementFactors,
        seasonalPatterns,
        trendDirection,
        forecastHorizonDays,
      ];
}
