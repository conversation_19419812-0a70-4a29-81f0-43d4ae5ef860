/// Tests for intelligent caching service
library;

import 'package:test/test.dart';
import 'package:server/services/intelligent_cache_service.dart';

void main() {
  group('IntelligentCacheService', () {
    late IntelligentCacheService cacheService;

    setUp(() {
      cacheService = IntelligentCacheService();
      cacheService.clear(); // Start with clean cache
    });

    group('Basic Cache Operations', () {
      test('should store and retrieve data', () async {
        await cacheService.set('test_key', 'test_value');
        final result = await cacheService.get<String>('test_key');
        
        expect(result, equals('test_value'));
      });

      test('should return null for non-existent keys', () async {
        final result = await cacheService.get<String>('non_existent_key');
        
        expect(result, isNull);
      });

      test('should handle different data types', () async {
        await cacheService.set('string_key', 'string_value');
        await cacheService.set('int_key', 42);
        await cacheService.set('map_key', {'nested': 'data'});
        await cacheService.set('list_key', [1, 2, 3]);

        expect(await cacheService.get<String>('string_key'), equals('string_value'));
        expect(await cacheService.get<int>('int_key'), equals(42));
        expect(await cacheService.get<Map>('map_key'), equals({'nested': 'data'}));
        expect(await cacheService.get<List>('list_key'), equals([1, 2, 3]));
      });
    });

    group('TTL and Expiration', () {
      test('should expire entries after TTL', () async {
        await cacheService.set(
          'expiring_key', 
          'expiring_value',
          ttl: const Duration(milliseconds: 100),
        );

        // Should be available immediately
        expect(await cacheService.get<String>('expiring_key'), equals('expiring_value'));

        // Wait for expiration
        await Future.delayed(const Duration(milliseconds: 150));

        // Should be expired
        expect(await cacheService.get<String>('expiring_key'), isNull);
      });

      test('should use category-specific TTL', () async {
        await cacheService.set('user_key', 'user_value', category: 'user_data');
        await cacheService.set('leaderboard_key', 'leaderboard_value', category: 'leaderboard');

        final stats = cacheService.getStatistics();
        expect(stats['categories']['user_data'], equals(1));
        expect(stats['categories']['leaderboard'], equals(1));
      });
    });

    group('Category-based Caching', () {
      test('should apply category-specific strategies', () async {
        await cacheService.set('achievement_1', {'id': 1}, category: 'achievements');
        await cacheService.set('quest_1', {'id': 1}, category: 'quest_data');
        await cacheService.set('analytics_1', {'data': 'large'}, category: 'analytics');

        final stats = cacheService.getStatistics();
        expect(stats['categories']['achievements'], equals(1));
        expect(stats['categories']['quest_data'], equals(1));
        expect(stats['categories']['analytics'], equals(1));
      });

      test('should invalidate by category', () async {
        await cacheService.set('user_1', 'data1', category: 'user_data');
        await cacheService.set('user_2', 'data2', category: 'user_data');
        await cacheService.set('quest_1', 'data3', category: 'quest_data');

        await cacheService.invalidateCategory('user_data');

        expect(await cacheService.get<String>('user_1'), isNull);
        expect(await cacheService.get<String>('user_2'), isNull);
        expect(await cacheService.get<String>('quest_1'), equals('data3'));
      });
    });

    group('Pattern-based Invalidation', () {
      test('should invalidate by pattern', () async {
        await cacheService.set('user:123:profile', 'profile_data');
        await cacheService.set('user:123:settings', 'settings_data');
        await cacheService.set('user:456:profile', 'other_profile');
        await cacheService.set('quest:789:data', 'quest_data');

        await cacheService.invalidatePattern(r'user:123:.*');

        expect(await cacheService.get<String>('user:123:profile'), isNull);
        expect(await cacheService.get<String>('user:123:settings'), isNull);
        expect(await cacheService.get<String>('user:456:profile'), equals('other_profile'));
        expect(await cacheService.get<String>('quest:789:data'), equals('quest_data'));
      });
    });

    group('Get-or-Set Pattern', () {
      test('should return cached value if available', () async {
        await cacheService.set('cached_key', 'cached_value');

        var fallbackCalled = false;
        final result = await cacheService.getOrSet<String>(
          'cached_key',
          () async {
            fallbackCalled = true;
            return 'fallback_value';
          },
        );

        expect(result, equals('cached_value'));
        expect(fallbackCalled, isFalse);
      });

      test('should call fallback and cache result if not available', () async {
        var fallbackCalled = false;
        final result = await cacheService.getOrSet<String>(
          'new_key',
          () async {
            fallbackCalled = true;
            return 'fallback_value';
          },
        );

        expect(result, equals('fallback_value'));
        expect(fallbackCalled, isTrue);

        // Should now be cached
        final cachedResult = await cacheService.get<String>('new_key');
        expect(cachedResult, equals('fallback_value'));
      });
    });

    group('Cache Statistics', () {
      test('should track hit and miss statistics', () async {
        await cacheService.set('key1', 'value1');
        
        // Hit
        await cacheService.get<String>('key1');
        
        // Miss
        await cacheService.get<String>('non_existent');

        final stats = cacheService.getStatistics();
        expect(stats['hits'], equals(1));
        expect(stats['misses'], equals(1));
        expect(stats['hit_ratio'], equals(0.5));
      });

      test('should track cache size and memory usage', () async {
        await cacheService.set('key1', 'small_value');
        await cacheService.set('key2', {'large': 'data_structure_with_more_content'});

        final stats = cacheService.getStatistics();
        expect(stats['cache_size'], equals(2));
        expect(stats['memory_usage_bytes'], greaterThan(0));
        expect(stats['memory_usage_mb'], greaterThan(0.0));
      });

      test('should track top accessed keys', () async {
        await cacheService.set('popular_key', 'popular_value');
        await cacheService.set('unpopular_key', 'unpopular_value');

        // Access popular key multiple times
        for (int i = 0; i < 5; i++) {
          await cacheService.get<String>('popular_key');
        }

        // Access unpopular key once
        await cacheService.get<String>('unpopular_key');

        final stats = cacheService.getStatistics();
        final topKeys = stats['top_accessed_keys'] as List;
        
        expect(topKeys, isNotEmpty);
        expect(topKeys.first['key'], equals('popular_key'));
        expect(topKeys.first['access_count'], equals(5));
      });
    });

    group('Cache Optimization', () {
      test('should optimize cache by removing expired entries', () async {
        await cacheService.set(
          'expiring_key',
          'expiring_value',
          ttl: const Duration(milliseconds: 50),
        );
        await cacheService.set('permanent_key', 'permanent_value');

        // Wait for expiration
        await Future.delayed(const Duration(milliseconds: 100));

        await cacheService.optimize();

        expect(await cacheService.get<String>('expiring_key'), isNull);
        expect(await cacheService.get<String>('permanent_key'), equals('permanent_value'));
      });

      test('should handle cache capacity limits', () async {
        // This test would require setting a very low cache limit
        // For now, we'll just verify the optimize method works
        await cacheService.set('key1', 'value1');
        await cacheService.set('key2', 'value2');

        expect(() => cacheService.optimize(), returnsNormally);
      });
    });

    group('Cache Health', () {
      test('should calculate cache health score', () async {
        await cacheService.set('key1', 'value1');
        await cacheService.get<String>('key1'); // Hit
        await cacheService.get<String>('missing'); // Miss

        final healthScore = cacheService.getHealthScore();
        
        expect(healthScore, isA<double>());
        expect(healthScore, greaterThanOrEqualTo(0.0));
        expect(healthScore, lessThanOrEqualTo(1.0));
      });
    });

    group('Warm-up and Preloading', () {
      test('should warm up cache with provided data', () async {
        final warmUpData = {
          'warm_key_1': () async => 'warm_value_1',
          'warm_key_2': () async => {'warm': 'data'},
        };

        await cacheService.warmUp(warmUpData);

        expect(await cacheService.get<String>('warm_key_1'), equals('warm_value_1'));
        expect(await cacheService.get<Map>('warm_key_2'), equals({'warm': 'data'}));
      });

      test('should handle warm-up failures gracefully', () async {
        final warmUpData = {
          'good_key': () async => 'good_value',
          'bad_key': () async => throw Exception('Warm-up failed'),
        };

        // Should not throw despite one failure
        expect(() => cacheService.warmUp(warmUpData), returnsNormally);
      });

      test('should analyze access patterns for predictive caching', () async {
        // Create some access patterns
        await cacheService.set('user:123:profile', 'profile1');
        await cacheService.set('user:456:profile', 'profile2');
        await cacheService.set('user:789:profile', 'profile3');

        // Access them to create patterns
        await cacheService.get<String>('user:123:profile');
        await cacheService.get<String>('user:456:profile');
        await cacheService.get<String>('user:789:profile');

        // Should not throw when analyzing patterns
        expect(() => cacheService.preloadPredictedData(), returnsNormally);
      });
    });

    group('Edge Cases', () {
      test('should handle null values', () async {
        await cacheService.set('null_key', null);
        final result = await cacheService.get('null_key');
        
        expect(result, isNull);
      });

      test('should handle empty strings', () async {
        await cacheService.set('empty_key', '');
        final result = await cacheService.get<String>('empty_key');
        
        expect(result, equals(''));
      });

      test('should handle large data structures', () async {
        final largeData = List.generate(1000, (i) => 'item_$i');
        
        await cacheService.set('large_key', largeData);
        final result = await cacheService.get<List>('large_key');
        
        expect(result, equals(largeData));
      });

      test('should handle concurrent access', () async {
        final futures = <Future>[];
        
        // Concurrent writes
        for (int i = 0; i < 10; i++) {
          futures.add(cacheService.set('concurrent_key_$i', 'value_$i'));
        }
        
        await Future.wait(futures);
        futures.clear();
        
        // Concurrent reads
        for (int i = 0; i < 10; i++) {
          futures.add(cacheService.get<String>('concurrent_key_$i'));
        }
        
        final results = await Future.wait(futures);
        
        for (int i = 0; i < 10; i++) {
          expect(results[i], equals('value_$i'));
        }
      });
    });

    group('Memory Management', () {
      test('should clear all cache entries', () async {
        await cacheService.set('key1', 'value1');
        await cacheService.set('key2', 'value2');

        cacheService.clear();

        expect(await cacheService.get<String>('key1'), isNull);
        expect(await cacheService.get<String>('key2'), isNull);
        
        final stats = cacheService.getStatistics();
        expect(stats['cache_size'], equals(0));
        expect(stats['hits'], greaterThanOrEqualTo(0));
        expect(stats['misses'], greaterThanOrEqualTo(0));
      });
    });
  });
}
