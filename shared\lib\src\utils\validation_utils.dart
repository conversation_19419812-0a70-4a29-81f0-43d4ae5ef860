import 'package:validators/validators.dart';

/// Comprehensive validation utilities for Quester
class ValidationUtils {
  // Common validation patterns
  static final RegExp _alphanumericRegex = RegExp(r'^[a-zA-Z0-9]+$');
  static final RegExp _usernameRegex = RegExp(r'^[a-zA-Z0-9_-]{3,20}$');
  static final RegExp _nameRegex = RegExp(r"^[a-zA-Z\s\-\.\']{2,50}$");
  static final RegExp _passwordRegex = RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$');
  static final RegExp _phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,15}$');

  /// Validation result class
  static ValidationResult valid() => const ValidationResult(isValid: true);
  static ValidationResult invalid(String message) => ValidationResult(isValid: false, message: message);

  // Email validation
  static ValidationResult validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return invalid('Email is required');
    }
    if (!isEmail(email)) {
      return invalid('Please enter a valid email address');
    }
    if (email.length > 254) {
      return invalid('Email address is too long');
    }
    return valid();
  }

  // Password validation
  static ValidationResult validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return invalid('Password is required');
    }
    if (password.length < 8) {
      return invalid('Password must be at least 8 characters long');
    }
    if (password.length > 128) {
      return invalid('Password is too long');
    }
    if (!_passwordRegex.hasMatch(password)) {
      return invalid('Password must contain at least one uppercase letter, one lowercase letter, and one number');
    }
    return valid();
  }

  // Display name validation
  static ValidationResult validateDisplayName(String? displayName) {
    if (displayName == null || displayName.isEmpty) {
      return invalid('Display name is required');
    }
    if (displayName.length < 2) {
      return invalid('Display name must be at least 2 characters long');
    }
    if (displayName.length > 50) {
      return invalid('Display name is too long');
    }
    if (displayName.trim() != displayName) {
      return invalid('Display name cannot start or end with whitespace');
    }
    return valid();
  }

  // Name validation (first/last name)
  static ValidationResult validateName(String? name) {
    if (name == null || name.isEmpty) {
      return valid(); // Optional field
    }
    if (name.length < 2) {
      return invalid('Name must be at least 2 characters long');
    }
    if (name.length > 50) {
      return invalid('Name is too long');
    }
    if (!_nameRegex.hasMatch(name)) {
      return invalid('Name can only contain letters, spaces, hyphens, apostrophes, and periods');
    }
    return valid();
  }

  // Username validation
  static ValidationResult validateUsername(String? username) {
    if (username == null || username.isEmpty) {
      return invalid('Username is required');
    }
    if (username.length < 3) {
      return invalid('Username must be at least 3 characters long');
    }
    if (username.length > 20) {
      return invalid('Username is too long');
    }
    if (!_usernameRegex.hasMatch(username)) {
      return invalid('Username can only contain letters, numbers, underscores, and hyphens');
    }
    return valid();
  }

  // Phone number validation
  static ValidationResult validatePhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return valid(); // Optional field
    }
    if (!_phoneRegex.hasMatch(phoneNumber)) {
      return invalid('Please enter a valid phone number');
    }
    return valid();
  }

  // URL validation
  static ValidationResult validateUrl(String? url) {
    if (url == null || url.isEmpty) {
      return valid(); // Optional field
    }
    if (!isURL(url)) {
      return invalid('Please enter a valid URL');
    }
    return valid();
  }

  // Quest/Task title validation
  static ValidationResult validateTitle(String? title) {
    if (title == null || title.isEmpty) {
      return invalid('Title is required');
    }
    if (title.length < 3) {
      return invalid('Title must be at least 3 characters long');
    }
    if (title.length > 200) {
      return invalid('Title is too long (maximum 200 characters)');
    }
    if (title.trim() != title) {
      return invalid('Title cannot start or end with whitespace');
    }
    return valid();
  }

  // Description validation
  static ValidationResult validateDescription(String? description) {
    if (description == null || description.isEmpty) {
      return valid(); // Optional field
    }
    if (description.length > 5000) {
      return invalid('Description is too long (maximum 5000 characters)');
    }
    return valid();
  }

  // Points validation
  static ValidationResult validatePoints(int? points) {
    if (points == null) {
      return invalid('Points value is required');
    }
    if (points < 0) {
      return invalid('Points cannot be negative');
    }
    if (points > 100000) {
      return invalid('Points value is too large');
    }
    return valid();
  }

  // Time validation (minutes)
  static ValidationResult validateMinutes(int? minutes) {
    if (minutes == null) {
      return valid(); // Optional field
    }
    if (minutes < 0) {
      return invalid('Time cannot be negative');
    }
    if (minutes > 43200) { // 720 hours = 30 days
      return invalid('Time is too large (maximum 30 days)');
    }
    return valid();
  }

  // Date validation (not in the past)
  static ValidationResult validateFutureDate(DateTime? date) {
    if (date == null) {
      return valid(); // Optional field
    }
    if (date.isBefore(DateTime.now())) {
      return invalid('Date cannot be in the past');
    }
    return valid();
  }

  // Date validation (deadline reasonable)
  static ValidationResult validateDeadline(DateTime? deadline) {
    if (deadline == null) {
      return valid(); // Optional field
    }
    final now = DateTime.now();
    final maxFuture = now.add(const Duration(days: 3650)); // 10 years
    
    if (deadline.isBefore(now)) {
      return invalid('Deadline cannot be in the past');
    }
    if (deadline.isAfter(maxFuture)) {
      return invalid('Deadline is too far in the future');
    }
    return valid();
  }

  // Tag validation
  static ValidationResult validateTag(String? tag) {
    if (tag == null || tag.isEmpty) {
      return invalid('Tag cannot be empty');
    }
    if (tag.length < 2) {
      return invalid('Tag must be at least 2 characters long');
    }
    if (tag.length > 30) {
      return invalid('Tag is too long (maximum 30 characters)');
    }
    if (!_alphanumericRegex.hasMatch(tag.replaceAll(' ', '').replaceAll('-', '').replaceAll('_', ''))) {
      return invalid('Tag can only contain letters, numbers, spaces, hyphens, and underscores');
    }
    return valid();
  }

  // Tags list validation
  static ValidationResult validateTags(List<String>? tags) {
    if (tags == null || tags.isEmpty) {
      return valid(); // Optional field
    }
    if (tags.length > 20) {
      return invalid('Too many tags (maximum 20)');
    }
    
    for (final tag in tags) {
      final tagValidation = validateTag(tag);
      if (!tagValidation.isValid) {
        return tagValidation;
      }
    }
    
    // Check for duplicates
    final uniqueTags = tags.toSet();
    if (uniqueTags.length != tags.length) {
      return invalid('Duplicate tags are not allowed');
    }
    
    return valid();
  }

  // File size validation
  static ValidationResult validateFileSize(int? sizeInBytes, {int maxSizeInMB = 10}) {
    if (sizeInBytes == null) {
      return invalid('File size is required');
    }
    if (sizeInBytes <= 0) {
      return invalid('Invalid file size');
    }
    
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (sizeInBytes > maxSizeInBytes) {
      return invalid('File is too large (maximum ${maxSizeInMB}MB)');
    }
    
    return valid();
  }

  // File extension validation
  static ValidationResult validateFileExtension(String? fileName, List<String> allowedExtensions) {
    if (fileName == null || fileName.isEmpty) {
      return invalid('File name is required');
    }
    
    final extension = fileName.toLowerCase().split('.').last;
    if (!allowedExtensions.map((e) => e.toLowerCase()).contains(extension)) {
      return invalid('File type not allowed. Allowed types: ${allowedExtensions.join(', ')}');
    }
    
    return valid();
  }

  // Streak validation
  static ValidationResult validateStreakDays(int? days) {
    if (days == null) {
      return invalid('Streak days is required');
    }
    if (days < 0) {
      return invalid('Streak days cannot be negative');
    }
    if (days > 3650) { // 10 years
      return invalid('Streak days is unreasonably high');
    }
    return valid();
  }

  // Achievement validation
  static ValidationResult validateAchievementRequirement(int? requirement) {
    if (requirement == null) {
      return invalid('Achievement requirement is required');
    }
    if (requirement < 1) {
      return invalid('Achievement requirement must be at least 1');
    }
    if (requirement > 1000000) {
      return invalid('Achievement requirement is too high');
    }
    return valid();
  }

  // Search query validation
  static ValidationResult validateSearchQuery(String? query) {
    if (query == null || query.isEmpty) {
      return invalid('Search query cannot be empty');
    }
    if (query.length < 2) {
      return invalid('Search query must be at least 2 characters long');
    }
    if (query.length > 100) {
      return invalid('Search query is too long');
    }
    return valid();
  }

  // Pagination validation
  static ValidationResult validatePagination({int? page, int? limit}) {
    if (page != null) {
      if (page < 1) {
        return invalid('Page number must be at least 1');
      }
      if (page > 10000) {
        return invalid('Page number is too high');
      }
    }
    
    if (limit != null) {
      if (limit < 1) {
        return invalid('Limit must be at least 1');
      }
      if (limit > 1000) {
        return invalid('Limit is too high (maximum 1000)');
      }
    }
    
    return valid();
  }

  // Color validation (hex format)
  static ValidationResult validateHexColor(String? color) {
    if (color == null || color.isEmpty) {
      return valid(); // Optional field
    }
    
    final hexColorRegex = RegExp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$');
    if (!hexColorRegex.hasMatch(color)) {
      return invalid('Please enter a valid hex color (e.g., #FF0000)');
    }
    
    return valid();
  }

  // JSON validation
  static ValidationResult validateJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) {
      return valid(); // Optional field
    }
    
    try {
      // ignore: unused_local_variable
      final decoded = jsonString; // In real app, use json.decode()
      return valid();
    } catch (e) {
      return invalid('Invalid JSON format');
    }
  }

  // Custom field validation with regex
  static ValidationResult validateWithRegex(String? value, RegExp regex, String errorMessage) {
    if (value == null || value.isEmpty) {
      return invalid('Value is required');
    }
    if (!regex.hasMatch(value)) {
      return invalid(errorMessage);
    }
    return valid();
  }

  // Batch validation
  static Map<String, ValidationResult> validateMultiple(Map<String, ValidationResult Function()> validators) {
    final results = <String, ValidationResult>{};
    for (final entry in validators.entries) {
      results[entry.key] = entry.value();
    }
    return results;
  }

  // Check if all validations passed
  static bool allValid(Map<String, ValidationResult> validations) {
    return validations.values.every((result) => result.isValid);
  }

  // Get first error message
  static String? getFirstError(Map<String, ValidationResult> validations) {
    for (final result in validations.values) {
      if (!result.isValid && result.message != null) {
        return result.message;
      }
    }
    return null;
  }
}

/// Validation result model
class ValidationResult {
  final bool isValid;
  final String? message;

  const ValidationResult({
    required this.isValid,
    this.message,
  });

  @override
  String toString() => isValid ? 'Valid' : 'Invalid: $message';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ValidationResult &&
          runtimeType == other.runtimeType &&
          isValid == other.isValid &&
          message == other.message;

  @override
  int get hashCode => isValid.hashCode ^ message.hashCode;
}