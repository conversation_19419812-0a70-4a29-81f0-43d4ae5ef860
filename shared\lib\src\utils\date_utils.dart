import 'package:intl/intl.dart';

/// Comprehensive date utility functions for Quester
class DateUtils {
  // Date formatters
  static final DateFormat _isoDateFormat = DateFormat('yyyy-MM-dd');
  static final DateFormat _isoDateTimeFormat = DateFormat('yyyy-MM-ddTHH:mm:ss.SSSZ');
  static final DateFormat _displayDateFormat = DateFormat('MMM dd, yyyy');
  static final DateFormat _displayDateTimeFormat = DateFormat('MMM dd, yyyy HH:mm');
  static final DateFormat _timeFormat = DateFormat('HH:mm');
  static final DateFormat _shortDateFormat = DateFormat('M/d');
  static final DateFormat _weekdayFormat = DateFormat('EEEE');
  static final DateFormat _monthYearFormat = DateFormat('MMMM yyyy');

  /// Get current date as ISO string (YYYY-MM-DD)
  static String get todayIso => _isoDateFormat.format(DateTime.now());

  /// Get current datetime as ISO string
  static String get nowIso => _isoDateTimeFormat.format(DateTime.now());

  /// Format date as ISO string (YYYY-MM-DD)
  static String formatIsoDate(DateTime date) => _isoDateFormat.format(date);

  /// Format datetime as ISO string
  static String formatIsoDateTime(DateTime date) => _isoDateTimeFormat.format(date);

  /// Format date for display (MMM dd, yyyy)
  static String formatDisplayDate(DateTime date) => _displayDateFormat.format(date);

  /// Format datetime for display (MMM dd, yyyy HH:mm)
  static String formatDisplayDateTime(DateTime date) => _displayDateTimeFormat.format(date);

  /// Format time only (HH:mm)
  static String formatTime(DateTime date) => _timeFormat.format(date);

  /// Format short date (M/d)
  static String formatShortDate(DateTime date) => _shortDateFormat.format(date);

  /// Format weekday name
  static String formatWeekday(DateTime date) => _weekdayFormat.format(date);

  /// Format month and year
  static String formatMonthYear(DateTime date) => _monthYearFormat.format(date);

  /// Parse ISO date string to DateTime
  static DateTime? parseIsoDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return _isoDateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse ISO datetime string to DateTime
  static DateTime? parseIsoDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) return null;
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Check if two dates are the same day
  static bool isSameDay(DateTime? date1, DateTime? date2) {
    if (date1 == null || date2 == null) return false;
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Check if date is today
  static bool isToday(DateTime? date) {
    if (date == null) return false;
    return isSameDay(date, DateTime.now());
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime? date) {
    if (date == null) return false;
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  /// Check if date is tomorrow
  static bool isTomorrow(DateTime? date) {
    if (date == null) return false;
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return isSameDay(date, tomorrow);
  }

  /// Check if date is in the past
  static bool isPast(DateTime? date) {
    if (date == null) return false;
    return date.isBefore(DateTime.now());
  }

  /// Check if date is in the future
  static bool isFuture(DateTime? date) {
    if (date == null) return false;
    return date.isAfter(DateTime.now());
  }

  /// Get relative time description (e.g., "2 hours ago", "in 3 days")
  static String getRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);
    final absDifference = difference.abs();

    if (absDifference.inSeconds < 60) {
      return 'just now';
    } else if (absDifference.inMinutes < 60) {
      final minutes = absDifference.inMinutes;
      final suffix = difference.isNegative ? 'ago' : 'from now';
      return '$minutes minute${minutes == 1 ? '' : 's'} $suffix';
    } else if (absDifference.inHours < 24) {
      final hours = absDifference.inHours;
      final suffix = difference.isNegative ? 'ago' : 'from now';
      return '$hours hour${hours == 1 ? '' : 's'} $suffix';
    } else if (absDifference.inDays < 7) {
      final days = absDifference.inDays;
      if (days == 1) {
        return difference.isNegative ? 'yesterday' : 'tomorrow';
      }
      final suffix = difference.isNegative ? 'ago' : 'from now';
      return '$days days $suffix';
    } else if (absDifference.inDays < 30) {
      final weeks = (absDifference.inDays / 7).floor();
      final suffix = difference.isNegative ? 'ago' : 'from now';
      return '$weeks week${weeks == 1 ? '' : 's'} $suffix';
    } else if (absDifference.inDays < 365) {
      final months = (absDifference.inDays / 30).floor();
      final suffix = difference.isNegative ? 'ago' : 'from now';
      return '$months month${months == 1 ? '' : 's'} $suffix';
    } else {
      final years = (absDifference.inDays / 365).floor();
      final suffix = difference.isNegative ? 'ago' : 'from now';
      return '$years year${years == 1 ? '' : 's'} $suffix';
    }
  }

  /// Get smart date display (Today, Yesterday, or formatted date)
  static String getSmartDateDisplay(DateTime date) {
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    if (isTomorrow(date)) return 'Tomorrow';
    
    final now = DateTime.now();
    final difference = date.difference(now).inDays.abs();
    
    if (difference < 7) {
      return formatWeekday(date);
    } else if (date.year == now.year) {
      return DateFormat('MMM dd').format(date);
    } else {
      return formatDisplayDate(date);
    }
  }

  /// Get deadline urgency level
  static String getDeadlineUrgency(DateTime? deadline) {
    if (deadline == null) return 'none';
    
    final now = DateTime.now();
    final hoursUntil = deadline.difference(now).inHours;
    
    if (hoursUntil < 0) return 'overdue';
    if (hoursUntil < 24) return 'urgent'; // Less than 1 day
    if (hoursUntil < 72) return 'soon'; // Less than 3 days
    if (hoursUntil < 168) return 'upcoming'; // Less than 1 week
    return 'distant';
  }

  /// Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Get start of week (Monday)
  static DateTime startOfWeek(DateTime date) {
    final monday = date.subtract(Duration(days: date.weekday - 1));
    return startOfDay(monday);
  }

  /// Get end of week (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final sunday = date.add(Duration(days: 7 - date.weekday));
    return endOfDay(sunday);
  }

  /// Get start of month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get end of month
  static DateTime endOfMonth(DateTime date) {
    final nextMonth = date.month == 12 
        ? DateTime(date.year + 1, 1, 1)
        : DateTime(date.year, date.month + 1, 1);
    return nextMonth.subtract(const Duration(microseconds: 1));
  }

  /// Get start of year
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  /// Get end of year
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }

  /// Get days in month
  static int getDaysInMonth(DateTime date) {
    final firstDayNextMonth = date.month == 12
        ? DateTime(date.year + 1, 1, 1)
        : DateTime(date.year, date.month + 1, 1);
    return firstDayNextMonth.subtract(const Duration(days: 1)).day;
  }

  /// Check if year is leap year
  static bool isLeapYear(int year) {
    return (year % 4 == 0) && ((year % 100 != 0) || (year % 400 == 0));
  }

  /// Get age in years
  static int getAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// Get duration in human readable format
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} day${duration.inDays == 1 ? '' : 's'}';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} hour${duration.inHours == 1 ? '' : 's'}';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} minute${duration.inMinutes == 1 ? '' : 's'}';
    } else {
      return '${duration.inSeconds} second${duration.inSeconds == 1 ? '' : 's'}';
    }
  }

  /// Get working days between two dates (excluding weekends)
  static int getWorkingDays(DateTime start, DateTime end) {
    int workingDays = 0;
    DateTime current = startOfDay(start);
    final endDay = startOfDay(end);
    
    while (!current.isAfter(endDay)) {
      if (current.weekday != DateTime.saturday && current.weekday != DateTime.sunday) {
        workingDays++;
      }
      current = current.add(const Duration(days: 1));
    }
    
    return workingDays;
  }

  /// Get next working day (skipping weekends)
  static DateTime getNextWorkingDay(DateTime date) {
    DateTime next = date.add(const Duration(days: 1));
    while (next.weekday == DateTime.saturday || next.weekday == DateTime.sunday) {
      next = next.add(const Duration(days: 1));
    }
    return next;
  }

  /// Get previous working day (skipping weekends)
  static DateTime getPreviousWorkingDay(DateTime date) {
    DateTime previous = date.subtract(const Duration(days: 1));
    while (previous.weekday == DateTime.saturday || previous.weekday == DateTime.sunday) {
      previous = previous.subtract(const Duration(days: 1));
    }
    return previous;
  }

  /// Format minutes to hours and minutes
  static String formatMinutesToHoursMinutes(int minutes) {
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;
    
    if (hours == 0) {
      return '${remainingMinutes}m';
    } else if (remainingMinutes == 0) {
      return '${hours}h';
    } else {
      return '${hours}h ${remainingMinutes}m';
    }
  }
}