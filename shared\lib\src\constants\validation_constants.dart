/// Validation Constants for Security Policies
/// 
/// This file contains all validation constants used throughout the security
/// system, including limits, thresholds, formats, and validation rules.
library;

/// Password validation constants
class PasswordValidationConstants {
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int recommendedMinLength = 12;
  static const int maxPasswordHistoryCount = 24;
  static const int defaultPasswordHistoryCount = 5;
  
  /// Password complexity requirements
  static const bool defaultRequireUppercase = true;
  static const bool defaultRequireLowercase = true;
  static const bool defaultRequireNumbers = true;
  static const bool defaultRequireSpecialChars = true;
  
  /// Account lockout settings
  static const int minLockoutThreshold = 3;
  static const int maxLockoutThreshold = 10;
  static const int defaultLockoutThreshold = 5;
  static const int minLockoutDurationMinutes = 5;
  static const int maxLockoutDurationMinutes = 1440; // 24 hours
  static const int defaultLockoutDurationMinutes = 30;
  
  /// Password expiry settings  
  static const int minPasswordExpiryDays = 30;
  static const int maxPasswordExpiryDays = 365;
  static const int defaultPasswordExpiryDays = 90;
}

/// MFA validation constants
class MFAValidationConstants {
  static const List<String> validMFAMethods = [
    'totp',
    'sms', 
    'email',
    'backupCodes',
    'hardware',
    'push'
  ];
  
  static const int minBackupCodesCount = 5;
  static const int maxBackupCodesCount = 20;
  static const int defaultBackupCodesCount = 10;
  
  static const int minTrustDeviceDays = 1;
  static const int maxTrustDeviceDays = 365;
  static const int defaultTrustDeviceDays = 30;
  
  static const int minGracePeriodDays = 0;
  static const int maxGracePeriodDays = 90;
  static const int defaultGracePeriodDays = 7;
}

/// Session validation constants  
class SessionValidationConstants {
  static const int minSessionTimeoutMinutes = 15;
  static const int maxSessionTimeoutMinutes = 1440; // 24 hours
  static const int defaultSessionTimeoutMinutes = 480; // 8 hours
  static const int recommendedMaxTimeoutMinutes = 480; // 8 hours
  
  static const int minIdleTimeoutMinutes = 5;
  static const int maxIdleTimeoutMinutes = 240; // 4 hours
  static const int defaultIdleTimeoutMinutes = 60;
  
  static const int minConcurrentSessions = 1;
  static const int maxConcurrentSessions = 50;
  static const int defaultMaxConcurrentSessions = 5;
  static const int recommendedMaxConcurrentSessions = 10;
}

/// Access policy validation constants
class AccessPolicyValidationConstants {
  static const int maxIPRules = 1000;
  static const int recommendedMaxIPRules = 100;
  static const int maxTimeRestrictions = 50;
  static const int maxCountryRestrictions = 250; // All countries
  
  /// Valid country codes (ISO 3166-1 alpha-2)
  static const List<String> validCountryCodes = [
    'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AO', 'AQ', 'AR', 'AS', 'AT',
    'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI',
    'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT', 'BV', 'BW', 'BY',
    'BZ', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM', 'CN',
    'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ', 'DK', 'DM',
    'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK',
    'FM', 'FO', 'FR', 'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL',
    'GM', 'GN', 'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY', 'HK', 'HM',
    'HN', 'HR', 'HT', 'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR',
    'IS', 'IT', 'JE', 'JM', 'JO', 'JP', 'KE', 'KG', 'KH', 'KI', 'KM', 'KN',
    'KP', 'KR', 'KW', 'KY', 'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS',
    'LT', 'LU', 'LV', 'LY', 'MA', 'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK',
    'ML', 'MM', 'MN', 'MO', 'MP', 'MQ', 'MR', 'MS', 'MT', 'MU', 'MV', 'MW',
    'MX', 'MY', 'MZ', 'NA', 'NC', 'NE', 'NF', 'NG', 'NI', 'NL', 'NO', 'NP',
    'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG', 'PH', 'PK', 'PL', 'PM',
    'PN', 'PR', 'PS', 'PT', 'PW', 'PY', 'QA', 'RE', 'RO', 'RS', 'RU', 'RW',
    'SA', 'SB', 'SC', 'SD', 'SE', 'SG', 'SH', 'SI', 'SJ', 'SK', 'SL', 'SM',
    'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX', 'SY', 'SZ', 'TC', 'TD', 'TF',
    'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO', 'TR', 'TT', 'TV', 'TW',
    'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ', 'VA', 'VC', 'VE', 'VG', 'VI',
    'VN', 'VU', 'WF', 'WS', 'YE', 'YT', 'ZA', 'ZM', 'ZW'
  ];
  
  /// Valid days of week for time restrictions
  static const List<String> validDaysOfWeek = [
    'monday', 'tuesday', 'wednesday', 'thursday', 
    'friday', 'saturday', 'sunday'
  ];
  
  /// Time format validation regex (HH:mm)
  static const String timeFormatRegex = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$';
  
  /// IPv4 address validation regex
  static const String ipv4Regex = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$';
  
  /// CIDR notation validation regex
  static const String cidrRegex = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:3[0-2]|[1-2]?[0-9])$';
  
  /// Private IP ranges (RFC 1918)
  static const List<String> privateIPRanges = [
    '10.0.0.0/8',
    '**********/12', 
    '***********/16'
  ];
  
  /// Reserved IP ranges
  static const List<String> reservedIPRanges = [
    '0.0.0.0/8',        // Current network
    '*********/8',      // Loopback
    '***********/16',   // Link-local
    '*********/4',      // Multicast
    '240.0.0.0/4'       // Reserved
  ];
}

/// Audit policy validation constants
class AuditPolicyValidationConstants {
  static const int minRetentionDays = 30;
  static const int maxRetentionDays = 2555; // ~7 years
  static const int defaultRetentionDays = 365; // 1 year
  
  /// Compliance-based retention requirements
  static const Map<String, int> complianceRetentionDays = {
    'SOX': 2555,        // 7 years
    'HIPAA': 2190,      // 6 years  
    'GDPR': 2190,       // 6 years (typically less)
    'PCI_DSS': 365,     // 1 year minimum
    'SOC2': 365,        // 1 year minimum
    'ISO27001': 365,    // 1 year minimum
  };
  
  /// Valid audit event types
  static const List<String> validAuditEventTypes = [
    'authentication',
    'authorization', 
    'dataAccess',
    'configuration',
    'securityPolicy',
    'mfa',
    'sso',
    'session',
    'suspicious',
    'compliance',
    'dataExport',
    'userManagement'
  ];
  
  /// Valid compliance standards
  static const List<String> validComplianceStandards = [
    'SOX',
    'HIPAA', 
    'GDPR',
    'PCI_DSS',
    'SOC2',
    'ISO27001',
    'NIST',
    'FedRAMP',
    'FISMA'
  ];
  
  /// Maximum number of audit event types
  static const int maxAuditEventTypes = 20;
  
  /// Maximum number of compliance standards
  static const int maxComplianceStandards = 10;
}

/// Risk assessment constants
class RiskAssessmentConstants {
  /// Risk score thresholds
  static const int lowRiskThreshold = 20;
  static const int mediumRiskThreshold = 50; 
  static const int highRiskThreshold = 80;
  
  /// Risk weights for different policy violations
  static const Map<String, int> riskWeights = {
    'weak_password_length': 15,
    'weak_password_complexity': 10,
    'no_password_expiry': 5,
    'no_mfa_required': 25,
    'weak_mfa_methods': 10,
    'long_session_timeout': 10,
    'unlimited_concurrent_sessions': 5,
    'no_access_restrictions': 15,
    'limited_audit_logging': 10,
    'short_audit_retention': 5,
  };
  
  /// Risk levels
  static const List<String> riskLevels = [
    'LOW',
    'MEDIUM', 
    'HIGH',
    'CRITICAL'
  ];
}

/// General validation constants
class GeneralValidationConstants {
  /// UUID format validation regex
  static const String uuidRegex = r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$';
  
  /// Email validation regex (basic)
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  
  /// Organization ID format (alphanumeric with underscores and hyphens)
  static const String organizationIdRegex = r'^[a-zA-Z0-9_-]+$';
  
  /// User ID format (alphanumeric with underscores and hyphens)
  static const String userIdRegex = r'^[a-zA-Z0-9_-]+$';
  
  /// Maximum string lengths
  static const int maxNameLength = 255;
  static const int maxDescriptionLength = 1000;
  static const int maxUrlLength = 2048;
  static const int maxJsonLength = 65535; // For JSON fields in database
  
  /// Minimum string lengths
  static const int minNameLength = 1;
  static const int minDescriptionLength = 0;
  
  /// Rate limiting constants
  static const int maxValidationRequestsPerMinute = 100;
  static const int maxPolicyUpdatesPerHour = 10;
}

/// Environment-specific validation overrides
class EnvironmentValidationOverrides {
  /// Development environment - more lenient validation
  static const Map<String, dynamic> developmentOverrides = {
    'password_min_length': 6,
    'session_timeout_minutes': 1440, // 24 hours
    'mfa_required': false,
    'audit_retention_days': 30,
  };
  
  /// Test environment - strict validation for testing
  static const Map<String, dynamic> testOverrides = {
    'password_min_length': 8,
    'session_timeout_minutes': 60,
    'mfa_required': true,
    'audit_retention_days': 90,
  };
  
  /// Production environment - strictest validation
  static const Map<String, dynamic> productionOverrides = {
    'password_min_length': 12,
    'session_timeout_minutes': 480, // 8 hours
    'mfa_required': true,
    'audit_retention_days': 365,
  };
}