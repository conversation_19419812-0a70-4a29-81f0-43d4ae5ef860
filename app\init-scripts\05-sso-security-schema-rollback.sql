-- Rollback: SSO and Security Features Schema Setup
-- Version: 1.0.0
-- Date: 2025-08-21
-- Description: Complete rollback script for SSO and Security Features

BEGIN;

-- =============================================================================
-- DROP HELPER FUNCTIONS
-- =============================================================================

DROP FUNCTION IF EXISTS cleanup_expired_sessions();
DROP FUNCTION IF EXISTS create_audit_log_partition(DATE);
DROP FUNCTION IF EXISTS calculate_ip_risk_score(INET, INTEGER, BOOLEAN, DECIMAL);

-- =============================================================================
-- DROP VIEWS
-- =============================================================================

DROP VIEW IF EXISTS security_dashboard_view;

-- =============================================================================
-- DROP TRIGGERS
-- =============================================================================

DROP TRIGGER IF EXISTS update_sso_providers_updated_at ON sso_providers;
DROP TRIGGER IF EXISTS update_user_sso_identities_updated_at ON user_sso_identities;
DROP TRIGGER IF EXISTS update_user_mfa_settings_updated_at ON user_mfa_settings;
DROP TRIGGER IF EXISTS update_organization_security_policies_updated_at ON organization_security_policies;
DROP TRIGGER IF EXISTS update_user_sessions_enhanced_updated_at ON user_sessions_enhanced;
DROP TRIGGER IF EXISTS update_ip_access_control_updated_at ON ip_access_control;

-- =============================================================================
-- DROP INDEXES FOR ENHANCED USERS TABLE
-- =============================================================================

DROP INDEX IF EXISTS idx_users_failed_attempts;
DROP INDEX IF EXISTS idx_users_locked_until;
DROP INDEX IF EXISTS idx_users_last_password_change;
DROP INDEX IF EXISTS idx_users_last_security_scan;

-- =============================================================================
-- REVERT ENHANCED USERS TABLE
-- =============================================================================

-- Remove constraints from enhanced users table
ALTER TABLE users DROP CONSTRAINT IF EXISTS valid_failed_login_attempts;
ALTER TABLE users DROP CONSTRAINT IF EXISTS valid_password_history;
ALTER TABLE users DROP CONSTRAINT IF EXISTS valid_security_questions;
ALTER TABLE users DROP CONSTRAINT IF EXISTS valid_security_flags;

-- Remove columns from users table
ALTER TABLE users DROP COLUMN IF EXISTS last_password_change;
ALTER TABLE users DROP COLUMN IF EXISTS password_history;
ALTER TABLE users DROP COLUMN IF EXISTS failed_login_attempts;
ALTER TABLE users DROP COLUMN IF EXISTS locked_until;
ALTER TABLE users DROP COLUMN IF EXISTS security_questions_encrypted;
ALTER TABLE users DROP COLUMN IF EXISTS last_security_scan;
ALTER TABLE users DROP COLUMN IF EXISTS security_flags;

-- =============================================================================
-- DROP SECURITY TABLES (in reverse order of dependencies)
-- =============================================================================

-- 7. Drop IP Access Control table
DROP TABLE IF EXISTS ip_access_control CASCADE;

-- 6. Drop User Sessions Enhanced table
DROP TABLE IF EXISTS user_sessions_enhanced CASCADE;

-- 5. Drop Security Audit Logs partitions and main table
DROP TABLE IF EXISTS security_audit_logs_2026_02 CASCADE;
DROP TABLE IF EXISTS security_audit_logs_2026_01 CASCADE;
DROP TABLE IF EXISTS security_audit_logs_2025_12 CASCADE;
DROP TABLE IF EXISTS security_audit_logs_2025_11 CASCADE;
DROP TABLE IF EXISTS security_audit_logs_2025_10 CASCADE;
DROP TABLE IF EXISTS security_audit_logs_2025_09 CASCADE;
DROP TABLE IF EXISTS security_audit_logs_2025_08 CASCADE;
DROP TABLE IF EXISTS security_audit_logs CASCADE;

-- 4. Drop Organization Security Policies table
DROP TABLE IF EXISTS organization_security_policies CASCADE;

-- 3. Drop User MFA Settings table
DROP TABLE IF EXISTS user_mfa_settings CASCADE;

-- 2. Drop User SSO Identities table
DROP TABLE IF EXISTS user_sso_identities CASCADE;

-- 1. Drop SSO Providers table
DROP TABLE IF EXISTS sso_providers CASCADE;

-- =============================================================================
-- DROP UTILITY FUNCTION
-- =============================================================================

DROP FUNCTION IF EXISTS update_updated_at_column();

-- =============================================================================
-- OPTIONAL: REMOVE EXTENSIONS (only if not used elsewhere)
-- =============================================================================

-- Note: Only uncomment these if you're sure no other parts of your application use them
-- DROP EXTENSION IF EXISTS "citext";
-- DROP EXTENSION IF EXISTS "pgcrypto"; 
-- DROP EXTENSION IF EXISTS "uuid-ossp";

COMMIT;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Run these queries after rollback to verify cleanup:
-- 
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%sso%';
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%security%';
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%mfa%';
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%audit%';
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%session%enhanced%';
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%ip_access%';
--
-- All queries above should return no rows if rollback was successful.
--
-- To check if user table columns were removed:
-- SELECT column_name FROM information_schema.columns 
-- WHERE table_name = 'users' AND column_name IN (
--     'last_password_change', 'password_history', 'failed_login_attempts',
--     'locked_until', 'security_questions_encrypted', 'last_security_scan', 'security_flags'
-- );
-- This query should return no rows if user table rollback was successful.