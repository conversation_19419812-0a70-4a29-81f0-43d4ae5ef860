/// Tests for enhanced WebSocket service
library;

import 'dart:convert';
import 'dart:io';
import 'package:test/test.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:server/services/websocket_service.dart';

void main() {
  group('WebSocketService', () {
    late HttpServer server;
    late String serverUrl;

    setUpAll(() async {
      // Start a test HTTP server for WebSocket connections
      server = await HttpServer.bind('localhost', 0);
      serverUrl = 'ws://localhost:${server.port}';
      
      server.transform(WebSocketTransformer()).listen((WebSocket webSocket) {
        final channel = IOWebSocketChannel(webSocket);
        WebSocketService.handleConnection(channel);
      });
    });

    tearDownAll(() async {
      await server.close();
    });

    setUp(() {
      // Clear service state before each test
      WebSocketService.clearAllConnections();
    });

    group('Connection Management', () {
      test('should handle new WebSocket connections', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Wait a bit for connection to be established
        await Future.delayed(const Duration(milliseconds: 100));
        
        final stats = WebSocketService.getConnectionStats();
        expect(stats['active_connections'], equals(1));
        expect(stats['total_connections'], equals(1));
        
        await client.sink.close();
      });

      test('should handle client disconnection', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Wait for connection
        await Future.delayed(const Duration(milliseconds: 100));
        
        var stats = WebSocketService.getConnectionStats();
        expect(stats['active_connections'], equals(1));
        
        // Disconnect client
        await client.sink.close();
        await Future.delayed(const Duration(milliseconds: 100));
        
        stats = WebSocketService.getConnectionStats();
        expect(stats['active_connections'], equals(0));
      });

      test('should track multiple connections', () async {
        final client1 = WebSocketChannel.connect(Uri.parse(serverUrl));
        final client2 = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        final stats = WebSocketService.getConnectionStats();
        expect(stats['active_connections'], equals(2));
        expect(stats['total_connections'], equals(2));
        
        await client1.sink.close();
        await client2.sink.close();
      });
    });

    group('User Authentication', () {
      test('should handle user authentication', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Send authentication message
        client.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'test_user_123',
          'token': 'test_token',
          'device_info': {
            'platform': 'test',
            'version': '1.0.0',
          },
        }));
        
        // Wait for processing
        await Future.delayed(const Duration(milliseconds: 100));
        
        final userConnections = WebSocketService.getUserConnections();
        expect(userConnections.containsKey('test_user_123'), isTrue);
        expect(userConnections['test_user_123']!.length, equals(1));
        
        await client.sink.close();
      });

      test('should handle multiple connections for same user', () async {
        final client1 = WebSocketChannel.connect(Uri.parse(serverUrl));
        final client2 = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Authenticate both connections with same user
        client1.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'test_user_123',
          'token': 'test_token',
        }));
        
        client2.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'test_user_123',
          'token': 'test_token',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        final userConnections = WebSocketService.getUserConnections();
        expect(userConnections['test_user_123']!.length, equals(2));
        
        await client1.sink.close();
        await client2.sink.close();
      });
    });

    group('Room Management', () {
      test('should handle room join requests', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Authenticate first
        client.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'test_user_123',
          'token': 'test_token',
        }));
        
        await Future.delayed(const Duration(milliseconds: 50));
        
        // Join room
        client.sink.add(jsonEncode({
          'type': 'join_room',
          'room_id': 'test_room_456',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        final roomConnections = WebSocketService.getRoomConnections();
        expect(roomConnections.containsKey('test_room_456'), isTrue);
        expect(roomConnections['test_room_456']!.length, equals(1));
        
        await client.sink.close();
      });

      test('should handle room leave requests', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Authenticate and join room
        client.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'test_user_123',
          'token': 'test_token',
        }));
        
        await Future.delayed(const Duration(milliseconds: 50));
        
        client.sink.add(jsonEncode({
          'type': 'join_room',
          'room_id': 'test_room_456',
        }));
        
        await Future.delayed(const Duration(milliseconds: 50));
        
        // Leave room
        client.sink.add(jsonEncode({
          'type': 'leave_room',
          'room_id': 'test_room_456',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        final roomConnections = WebSocketService.getRoomConnections();
        expect(roomConnections.containsKey('test_room_456'), isFalse);
        
        await client.sink.close();
      });

      test('should handle multiple users in same room', () async {
        final client1 = WebSocketChannel.connect(Uri.parse(serverUrl));
        final client2 = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Authenticate both clients
        client1.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'user_1',
          'token': 'token_1',
        }));
        
        client2.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'user_2',
          'token': 'token_2',
        }));
        
        await Future.delayed(const Duration(milliseconds: 50));
        
        // Both join same room
        client1.sink.add(jsonEncode({
          'type': 'join_room',
          'room_id': 'shared_room',
        }));
        
        client2.sink.add(jsonEncode({
          'type': 'join_room',
          'room_id': 'shared_room',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        final roomConnections = WebSocketService.getRoomConnections();
        expect(roomConnections['shared_room']!.length, equals(2));
        
        await client1.sink.close();
        await client2.sink.close();
      });
    });

    group('Presence Management', () {
      test('should handle presence updates', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Authenticate first
        client.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'test_user_123',
          'token': 'test_token',
        }));
        
        await Future.delayed(const Duration(milliseconds: 50));
        
        // Update presence
        client.sink.add(jsonEncode({
          'type': 'update_presence',
          'status': 'busy',
          'activity': 'working_on_quest',
          'status_message': 'Deep in focus mode',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        final userPresence = WebSocketService.getUserPresence();
        expect(userPresence.containsKey('test_user_123'), isTrue);
        expect(userPresence['test_user_123']!['status'], equals('busy'));
        expect(userPresence['test_user_123']!['activity'], equals('working_on_quest'));
        
        await client.sink.close();
      });

      test('should set user offline when disconnecting', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Authenticate
        client.sink.add(jsonEncode({
          'type': 'user_auth',
          'user_id': 'test_user_123',
          'token': 'test_token',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        var userPresence = WebSocketService.getUserPresence();
        expect(userPresence['test_user_123']!['status'], equals('online'));
        
        // Disconnect
        await client.sink.close();
        await Future.delayed(const Duration(milliseconds: 100));
        
        userPresence = WebSocketService.getUserPresence();
        expect(userPresence['test_user_123']!['status'], equals('offline'));
      });
    });

    group('Message Broadcasting', () {
      test('should broadcast to all subscribers', () async {
        final client1 = WebSocketChannel.connect(Uri.parse(serverUrl));
        final client2 = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Subscribe both clients
        client1.sink.add(jsonEncode({
          'type': 'subscribe',
          'channel': 'test_channel',
        }));
        
        client2.sink.add(jsonEncode({
          'type': 'subscribe',
          'channel': 'test_channel',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        // Broadcast message
        WebSocketService.broadcast('test_channel', {
          'type': 'test_message',
          'content': 'Hello subscribers!',
        });
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        await client1.sink.close();
        await client2.sink.close();
      });

      test('should broadcast to room members only', () async {
        final client1 = WebSocketChannel.connect(Uri.parse(serverUrl));
        final client2 = WebSocketChannel.connect(Uri.parse(serverUrl));
        final client3 = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Authenticate all clients
        for (int i = 1; i <= 3; i++) {
          final client = i == 1 ? client1 : i == 2 ? client2 : client3;
          client.sink.add(jsonEncode({
            'type': 'user_auth',
            'user_id': 'user_$i',
            'token': 'token_$i',
          }));
        }
        
        await Future.delayed(const Duration(milliseconds: 50));
        
        // Only client1 and client2 join the room
        client1.sink.add(jsonEncode({
          'type': 'join_room',
          'room_id': 'private_room',
        }));
        
        client2.sink.add(jsonEncode({
          'type': 'join_room',
          'room_id': 'private_room',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        // Broadcast to room should only reach client1 and client2
        WebSocketService.broadcastToRoom('private_room', {
          'type': 'room_message',
          'content': 'Room-only message',
        });
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        await client1.sink.close();
        await client2.sink.close();
        await client3.sink.close();
      });
    });

    group('Error Handling', () {
      test('should handle invalid message format', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Send invalid JSON
        client.sink.add('invalid json');
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        // Connection should still be active
        final stats = WebSocketService.getConnectionStats();
        expect(stats['active_connections'], equals(1));
        
        await client.sink.close();
      });

      test('should handle unknown message types', () async {
        final client = WebSocketChannel.connect(Uri.parse(serverUrl));
        
        // Send unknown message type
        client.sink.add(jsonEncode({
          'type': 'unknown_type',
          'data': 'test',
        }));
        
        await Future.delayed(const Duration(milliseconds: 100));
        
        // Connection should still be active
        final stats = WebSocketService.getConnectionStats();
        expect(stats['active_connections'], equals(1));
        
        await client.sink.close();
      });
    });
  });
}
