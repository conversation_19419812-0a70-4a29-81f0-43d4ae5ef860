import 'package:shared/shared.dart';

/// Enterprise organization management service
class EnterpriseService {
  /// Create a new organization
  static Future<Map<String, dynamic>> createOrganization(Map<String, dynamic> data) async {
    final organization = Organization(
      id: _generateId(),
      name: data['name'] as String,
      slug: _generateSlug(data['name'] as String),
      description: data['description'] as String?,
      logoUrl: data['logoUrl'] as String?,
      primaryDomain: data['primaryDomain'] as String?,
      status: OrganizationStatus.trial,
      subscriptionTier: SubscriptionTier.free,
      maxUsers: 10,
      currentUsers: 0,
      settings: data['settings'] as Map<String, dynamic>? ?? {},
      gamificationConfig: data['gamificationConfig'] as Map<String, dynamic>? ?? {
        'enabledFeatures': ['points', 'achievements', 'leaderboards'],
        'pointsConfig': {'dailyLogin': 10, 'questCompletion': 50},
      },
      ssoConfig: data['ssoConfig'] as Map<String, dynamic>?,
      billingConfig: data['billingConfig'] as Map<String, dynamic>?,
      metadata: data['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      trialEndsAt: DateTime.now().add(const Duration(days: 30)),
      subscriptionEndsAt: null,
    );

    // Create default roles for the organization
    await _createDefaultRoles(organization.id);

    return {
      'success': true,
      'data': organization.toJson(),
      'message': 'Organization created successfully',
    };
  }

  /// Get organization by ID
  static Future<Map<String, dynamic>> getOrganization(String organizationId) async {
    try {
      // Simulate database lookup
      final organization = _mockOrganization(organizationId);
      
      return {
        'success': true,
        'data': organization.toJson(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Organization not found',
      };
    }
  }

  /// Update organization
  static Future<Map<String, dynamic>> updateOrganization(
    String organizationId,
    Map<String, dynamic> data,
  ) async {
    try {
      final organization = _mockOrganization(organizationId);
      final updatedOrganization = organization.copyWith(
        name: data['name'] as String?,
        description: data['description'] as String?,
        logoUrl: data['logoUrl'] as String?,
        primaryDomain: data['primaryDomain'] as String?,
        settings: data['settings'] as Map<String, dynamic>?,
        gamificationConfig: data['gamificationConfig'] as Map<String, dynamic>?,
        updatedAt: DateTime.now(),
      );

      return {
        'success': true,
        'data': updatedOrganization.toJson(),
        'message': 'Organization updated successfully',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Failed to update organization',
      };
    }
  }

  /// List organizations for a user
  static Future<Map<String, dynamic>> listOrganizations(String userId) async {
    // Mock multiple organizations
    final organizations = [
      _mockOrganization('org1'),
      _mockOrganization('org2'),
    ];

    return {
      'success': true,
      'data': organizations.map((org) => org.toJson()).toList(),
      'total': organizations.length,
    };
  }

  /// Create organization member
  static Future<Map<String, dynamic>> createMember(
    String organizationId,
    Map<String, dynamic> data,
  ) async {
    final member = OrganizationMember(
      id: _generateId(),
      organizationId: organizationId,
      userId: data['userId'] as String,
      email: data['email'] as String,
      displayName: data['displayName'] as String,
      avatarUrl: data['avatarUrl'] as String?,
      status: OrganizationMemberStatus.invited,
      roleIds: data['roleIds'] as List<String>? ?? ['member-$organizationId'],
      teamIds: data['teamIds'] as List<String>? ?? [],
      title: data['title'] as String?,
      department: data['department'] as String?,
      location: data['location'] as String?,
      timezone: data['timezone'] as String?,
      invitationDetails: {
        'invitedAt': DateTime.now().toIso8601String(),
        'invitedBy': data['invitedBy'] as String?,
        'invitationToken': _generateId(),
      },
      joinedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return {
      'success': true,
      'data': member.toJson(),
      'message': 'Member invited successfully',
    };
  }

  /// List organization members
  static Future<Map<String, dynamic>> listMembers(String organizationId) async {
    final members = [
      _mockMember(organizationId, 'user1'),
      _mockMember(organizationId, 'user2'),
    ];

    return {
      'success': true,
      'data': members.map((member) => member.toJson()).toList(),
      'total': members.length,
    };
  }

  /// Update member
  static Future<Map<String, dynamic>> updateMember(
    String organizationId,
    String memberId,
    Map<String, dynamic> data,
  ) async {
    try {
      final member = _mockMember(organizationId, 'user1');
      final updatedMember = member.copyWith(
        status: data['status'] != null
            ? OrganizationMemberStatus.values.firstWhere(
                (e) => e.name == data['status'],
                orElse: () => OrganizationMemberStatus.active,
              )
            : null,
        roleIds: data['roleIds'] as List<String>?,
        teamIds: data['teamIds'] as List<String>?,
        title: data['title'] as String?,
        department: data['department'] as String?,
        updatedAt: DateTime.now(),
      );

      return {
        'success': true,
        'data': updatedMember.toJson(),
        'message': 'Member updated successfully',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Failed to update member',
      };
    }
  }

  /// List organization roles
  static Future<Map<String, dynamic>> listRoles(String organizationId) async {
    final roles = [
      OrganizationRole.createOwnerRole(organizationId),
      OrganizationRole.createAdminRole(organizationId),
      OrganizationRole.createManagerRole(organizationId),
      OrganizationRole.createMemberRole(organizationId),
      OrganizationRole.createViewerRole(organizationId),
    ];

    return {
      'success': true,
      'data': roles.map((role) => role.toJson()).toList(),
      'total': roles.length,
    };
  }

  /// Create custom role
  static Future<Map<String, dynamic>> createRole(
    String organizationId,
    Map<String, dynamic> data,
  ) async {
    final role = OrganizationRole(
      id: _generateId(),
      organizationId: organizationId,
      name: data['name'] as String,
      description: data['description'] as String?,
      color: data['color'] as String? ?? '#0066FF',
      isSystemRole: false,
      isActive: true,
      permissions: (data['permissions'] as List<dynamic>?)
              ?.map((p) => Permission.values.firstWhere(
                    (e) => e.name == p,
                    orElse: () => Permission.orgRead,
                  ))
              .toSet() ??
          {Permission.orgRead},
      priority: data['priority'] as int? ?? 500,
      metadata: data['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return {
      'success': true,
      'data': role.toJson(),
      'message': 'Role created successfully',
    };
  }

  /// Get organization analytics summary
  static Future<Map<String, dynamic>> getAnalyticsSummary(String organizationId) async {
    return {
      'success': true,
      'data': {
        'userEngagement': {
          'activeUsers': 156,
          'dailyActiveUsers': 87,
          'weeklyActiveUsers': 142,
          'monthlyActiveUsers': 156,
          'averageSessionDuration': 1847, // seconds
        },
        'questMetrics': {
          'totalQuests': 342,
          'completedQuests': 278,
          'inProgressQuests': 64,
          'completionRate': 81.3,
          'averageCompletionTime': 4.2, // days
        },
        'teamMetrics': {
          'totalTeams': 23,
          'activeTeams': 19,
          'averageTeamSize': 6.8,
          'teamCompletionRate': 85.7,
        },
        'gamificationMetrics': {
          'totalPointsAwarded': 45670,
          'achievementsUnlocked': 234,
          'leaderboardParticipation': 89.7, // percentage
          'rewardsClaimed': 145,
        },
        'systemMetrics': {
          'totalLogins': 1456,
          'apiCalls': 23456,
          'storageUsed': 512, // MB
          'uptime': 99.9, // percentage
        },
      },
    };
  }

  /// Get detailed analytics
  static Future<Map<String, dynamic>> getAnalytics(
    String organizationId,
    Map<String, dynamic> query,
  ) async {
    final metricType = query['type'] as String? ?? 'userEngagement';
    final timePeriod = query['period'] as String? ?? 'week';
    
    // Generate mock time series data
    final dataPoints = _generateMockAnalyticsData(metricType, timePeriod);

    return {
      'success': true,
      'data': {
        'metricType': metricType,
        'timePeriod': timePeriod,
        'dataPoints': dataPoints,
        'summary': {
          'total': dataPoints.fold<double>(0, (sum, point) => sum + point.value),
          'average': dataPoints.fold<double>(0, (sum, point) => sum + point.value) / dataPoints.length,
          'peak': dataPoints.map((p) => p.value).reduce((a, b) => a > b ? a : b),
          'low': dataPoints.map((p) => p.value).reduce((a, b) => a < b ? a : b),
        },
      },
    };
  }

  // Helper methods
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  static String _generateSlug(String name) {
    return name.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), '-');
  }

  static Future<void> _createDefaultRoles(String organizationId) async {
    // In real implementation, save to database
    print('Creating default roles for organization: $organizationId');
  }

  static Organization _mockOrganization(String id) {
    return Organization(
      id: id,
      name: 'Acme Corporation',
      slug: 'acme-corp',
      description: 'A leading technology company',
      logoUrl: 'https://example.com/logo.png',
      primaryDomain: 'acme.com',
      status: OrganizationStatus.active,
      subscriptionTier: SubscriptionTier.business,
      maxUsers: 200,
      currentUsers: 156,
      settings: {
        'timezone': 'UTC',
        'language': 'en',
        'theme': 'light',
      },
      gamificationConfig: {
        'enabledFeatures': ['points', 'achievements', 'leaderboards', 'rewards'],
        'pointsConfig': {
          'dailyLogin': 10,
          'questCompletion': 50,
          'teamCollaboration': 25,
        },
      },
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      subscriptionEndsAt: DateTime.now().add(const Duration(days: 30)),
    );
  }

  static OrganizationMember _mockMember(String organizationId, String userId) {
    return OrganizationMember(
      id: '$userId-$organizationId',
      organizationId: organizationId,
      userId: userId,
      email: '<EMAIL>',
      displayName: 'John Doe',
      avatarUrl: 'https://example.com/avatar.png',
      status: OrganizationMemberStatus.active,
      roleIds: ['member-$organizationId'],
      teamIds: ['team1', 'team2'],
      title: 'Software Engineer',
      department: 'Engineering',
      location: 'San Francisco, CA',
      timezone: 'America/Los_Angeles',
      lastLoginAt: DateTime.now().subtract(const Duration(hours: 2)),
      joinedAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    );
  }

  static List<AnalyticsDataPoint> _generateMockAnalyticsData(String type, String period) {
    final now = DateTime.now();
    final dataPoints = <AnalyticsDataPoint>[];
    
    int count;
    Duration interval;
    
    switch (period) {
      case 'hour':
        count = 24;
        interval = const Duration(hours: 1);
        break;
      case 'day':
        count = 7;
        interval = const Duration(days: 1);
        break;
      case 'week':
        count = 4;
        interval = const Duration(days: 7);
        break;
      case 'month':
        count = 12;
        interval = const Duration(days: 30);
        break;
      default:
        count = 7;
        interval = const Duration(days: 1);
    }

    for (int i = 0; i < count; i++) {
      final timestamp = now.subtract(interval * (count - 1 - i));
      final value = _generateMockValue(type, i, count);
      
      dataPoints.add(AnalyticsDataPoint(
        timestamp: timestamp,
        value: value,
        metadata: {
          'period': period,
          'index': i,
        },
      ));
    }

    return dataPoints;
  }

  static double _generateMockValue(String type, int index, int total) {
    final random = (index * 1234567) % 100;
    
    switch (type) {
      case 'userEngagement':
        return 50 + (random * 0.5);
      case 'questCompletion':
        return 10 + (random * 0.3);
      case 'teamPerformance':
        return 75 + (random * 0.25);
      case 'gamificationEngagement':
        return 60 + (random * 0.4);
      default:
        return random.toDouble();
    }
  }
}
