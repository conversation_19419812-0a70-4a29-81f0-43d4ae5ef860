import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../core/optimization/performance_optimizer.dart';

/// Performance-optimized widget wrapper
class PerformanceWidget extends StatefulWidget {
  final Widget child;
  final String widgetName;
  final bool enableOptimization;
  final bool enableRepaintBoundary;
  final bool enableKeepAlive;
  final VoidCallback? onBuild;

  const PerformanceWidget({
    super.key,
    required this.child,
    required this.widgetName,
    this.enableOptimization = true,
    this.enableRepaintBoundary = true,
    this.enableKeepAlive = false,
    this.onBuild,
  });

  @override
  State<PerformanceWidget> createState() => _PerformanceWidgetState();
}

class _PerformanceWidgetState extends State<PerformanceWidget>
    with AutomaticKeepAliveClientMixin {
  final PerformanceOptimizer _optimizer = PerformanceOptimizer();
  int _buildCount = 0;

  @override
  bool get wantKeepAlive => widget.enableKeepAlive;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    if (!widget.enableOptimization) {
      return widget.child;
    }

    return _optimizer.optimizeBuild(
      '${widget.widgetName}_${_buildCount++}',
      () {
        widget.onBuild?.call();
        
        Widget child = widget.child;
        
        // Wrap with RepaintBoundary for better performance
        if (widget.enableRepaintBoundary) {
          child = RepaintBoundary(child: child);
        }
        
        return child;
      },
    );
  }
}

/// Optimized StatelessWidget base class
abstract class OptimizedStatelessWidget extends StatelessWidget {
  const OptimizedStatelessWidget({super.key});

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      return PerformanceWidget(
        widgetName: runtimeType.toString(),
        child: buildOptimized(context),
      );
    }
    return buildOptimized(context);
  }

  /// Build method to be implemented by subclasses
  Widget buildOptimized(BuildContext context);
}

/// Optimized StatefulWidget base class
abstract class OptimizedStatefulWidget extends StatefulWidget {
  const OptimizedStatefulWidget({super.key});

  @override
  State<OptimizedStatefulWidget> createState();
}

/// Optimized State base class
abstract class OptimizedState<T extends OptimizedStatefulWidget> 
    extends State<T> {
  
  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      return PerformanceWidget(
        widgetName: widget.runtimeType.toString(),
        child: buildOptimized(context),
      );
    }
    return buildOptimized(context);
  }

  /// Build method to be implemented by subclasses
  Widget buildOptimized(BuildContext context);
}

/// Memoized widget for expensive computations
class MemoizedWidget extends StatefulWidget {
  final Widget Function() builder;
  final List<Object?> dependencies;
  final String? debugLabel;

  const MemoizedWidget({
    super.key,
    required this.builder,
    required this.dependencies,
    this.debugLabel,
  });

  @override
  State<MemoizedWidget> createState() => _MemoizedWidgetState();
}

class _MemoizedWidgetState extends State<MemoizedWidget> {
  Widget? _cachedWidget;
  List<Object?>? _lastDependencies;

  @override
  Widget build(BuildContext context) {
    // Check if dependencies have changed
    if (_cachedWidget == null || 
        !listEquals(_lastDependencies, widget.dependencies)) {
      
      if (kDebugMode && widget.debugLabel != null) {
        debugPrint('MemoizedWidget rebuilding: ${widget.debugLabel}');
      }
      
      _cachedWidget = widget.builder();
      _lastDependencies = List.from(widget.dependencies);
    }

    return _cachedWidget!;
  }
}

/// Conditional widget that only rebuilds when condition changes
class ConditionalWidget extends StatefulWidget {
  final bool condition;
  final Widget Function() trueBuilder;
  final Widget Function()? falseBuilder;
  final Widget? fallback;

  const ConditionalWidget({
    super.key,
    required this.condition,
    required this.trueBuilder,
    this.falseBuilder,
    this.fallback,
  });

  @override
  State<ConditionalWidget> createState() => _ConditionalWidgetState();
}

class _ConditionalWidgetState extends State<ConditionalWidget> {
  Widget? _trueWidget;
  Widget? _falseWidget;
  bool? _lastCondition;

  @override
  Widget build(BuildContext context) {
    if (_lastCondition != widget.condition) {
      _lastCondition = widget.condition;
      
      if (widget.condition) {
        _trueWidget ??= widget.trueBuilder();
        return _trueWidget!;
      } else {
        if (widget.falseBuilder != null) {
          _falseWidget ??= widget.falseBuilder!();
          return _falseWidget!;
        }
        return widget.fallback ?? const SizedBox.shrink();
      }
    }

    // Return cached widget
    if (widget.condition) {
      return _trueWidget ?? widget.trueBuilder();
    } else {
      if (widget.falseBuilder != null) {
        return _falseWidget ?? widget.falseBuilder!();
      }
      return widget.fallback ?? const SizedBox.shrink();
    }
  }
}

/// Lazy builder that only builds when visible
class LazyBuilder extends StatefulWidget {
  final Widget Function(BuildContext context) builder;
  final Widget? placeholder;
  final double threshold;

  const LazyBuilder({
    super.key,
    required this.builder,
    this.placeholder,
    this.threshold = 0.0,
  });

  @override
  State<LazyBuilder> createState() => _LazyBuilderState();
}

class _LazyBuilderState extends State<LazyBuilder> {
  Widget? _builtWidget;
  bool _hasBuilt = false;

  @override
  Widget build(BuildContext context) {
    if (_hasBuilt) {
      return _builtWidget!;
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Build immediately if no threshold or constraints are available
        if (widget.threshold <= 0 || constraints.maxHeight == double.infinity) {
          _builtWidget = widget.builder(context);
          _hasBuilt = true;
          return _builtWidget!;
        }

        // Use placeholder until ready to build
        return widget.placeholder ?? const SizedBox.shrink();
      },
    );
  }
}

/// Performance metrics display widget (debug only)
class PerformanceMetrics extends StatefulWidget {
  final Widget child;
  final bool showMetrics;

  const PerformanceMetrics({
    super.key,
    required this.child,
    this.showMetrics = kDebugMode,
  });

  @override
  State<PerformanceMetrics> createState() => _PerformanceMetricsState();
}

class _PerformanceMetricsState extends State<PerformanceMetrics> {
  // final PerformanceOptimizer _optimizer = PerformanceOptimizer(); // TODO: Implement performance optimization
  Map<String, dynamic>? _metrics;

  @override
  void initState() {
    super.initState();
    if (widget.showMetrics) {
      _loadMetrics();
    }
  }

  Future<void> _loadMetrics() async {
    // This would load actual performance metrics in a real implementation
    setState(() {
      _metrics = {
        'buildCount': 42,
        'averageBuildTime': '12ms',
        'memoryUsage': '45MB',
        'cacheHitRate': '87%',
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showMetrics || _metrics == null) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: 50,
          right: 10,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black87,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: _metrics!.entries.map((entry) {
                return Text(
                  '${entry.key}: ${entry.value}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontFamily: 'monospace',
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}
