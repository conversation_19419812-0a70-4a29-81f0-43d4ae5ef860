import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('WebSocketEvent Tests', () {
    test('should create a WebSocketEvent with correct properties', () {
      // Test basic WebSocketEvent creation
      final timestamp = DateTime.now();
      final event = WebSocketEvent(
        id: 'test-id',
        type: WebSocketEventType.userPresence,
        data: {'userId': 'user123'},
        timestamp: timestamp,
        priority: EventPriority.high,
      );

      expect(event.id, equals('test-id'));
      expect(event.type, equals(WebSocketEventType.userPresence));
      expect(event.data['userId'], equals('user123'));
      expect(event.priority, equals(EventPriority.high));
      expect(event.timestamp, equals(timestamp));
    });

    test('should serialize to and from JSON', () {
      final timestamp = DateTime.now();
      final event = WebSocketEvent(
        id: 'test-id',
        type: WebSocketEventType.userPresence,
        data: {'status': 'online'},
        timestamp: timestamp,
        priority: EventPriority.normal,
        userId: 'user123',
        organizationId: 'org456',
      );

      // Serialize to JSON
      final json = event.toJson();
      expect(json['id'], equals('test-id'));
      expect(json['type'], equals('user_presence'));
      expect(json['data'], equals({'status': 'online'}));
      expect(json['userId'], equals('user123'));
      expect(json['organizationId'], equals('org456'));

      // Deserialize from JSON
      final eventFromJson = WebSocketEvent.fromJson(json);
      expect(eventFromJson.id, equals(event.id));
      expect(eventFromJson.type, equals(event.type));
      expect(eventFromJson.data, equals(event.data));
      expect(eventFromJson.priority, equals(event.priority));
      expect(eventFromJson.userId, equals(event.userId));
    });

    test('should create factory methods correctly', () {
      final userPresenceEvent = WebSocketEvent.userPresence(
        userId: 'user123',
        status: 'online',
        organizationId: 'org456',
      );
      expect(userPresenceEvent.type, equals(WebSocketEventType.userPresence));
      expect(userPresenceEvent.data['status'], equals('online'));
      expect(userPresenceEvent.userId, equals('user123'));

      final questUpdateEvent = WebSocketEvent.questUpdate(
        questId: 'quest789',
        userId: 'user123',
        action: 'update',
        questData: {'status': 'completed'},
      );
      expect(questUpdateEvent.type, equals(WebSocketEventType.questUpdate));
      expect(questUpdateEvent.data['quest_id'], equals('quest789'));
      expect(questUpdateEvent.data['action'], equals('update'));
      expect(questUpdateEvent.data['quest_data'], equals({'status': 'completed'}));
    });

    test('should create achievement unlock event', () {
      final achievementEvent = WebSocketEvent.achievementUnlock(
        userId: 'user123',
        achievementId: 'ach456',
        pointsAwarded: 100,
      );
      expect(achievementEvent.type, equals(WebSocketEventType.achievementUnlock));
      expect(achievementEvent.data['achievement_id'], equals('ach456'));
      expect(achievementEvent.data['points_awarded'], equals(100));
      expect(achievementEvent.priority, equals(EventPriority.high));
    });

    test('should check if event has expired', () {
      final expiredEvent = WebSocketEvent(
        id: 'expired-event',
        type: WebSocketEventType.heartbeat,
        data: {},
        timestamp: DateTime.now().subtract(Duration(minutes: 10)),
        ttl: 300, // 5 minutes
      );
      expect(expiredEvent.isExpired, isTrue);

      final validEvent = WebSocketEvent(
        id: 'valid-event',
        type: WebSocketEventType.heartbeat,
        data: {},
        timestamp: DateTime.now().subtract(Duration(minutes: 2)),
        ttl: 300, // 5 minutes
      );
      expect(validEvent.isExpired, isFalse);
    });
  });

  group('UserPresence Tests', () {
    test('should create UserPresence with correct status', () {
      final timestamp = DateTime.now();
      final presence = UserPresence(
        userId: 'user123',
        status: PresenceStatus.online,
        lastSeen: timestamp,
        activity: UserActivity.editingQuest,
        deviceInfo: {'type': 'mobile', 'platform': 'android'},
      );

      expect(presence.userId, equals('user123'));
      expect(presence.status, equals(PresenceStatus.online));
      expect(presence.activity, equals(UserActivity.editingQuest));
      expect(presence.lastSeen, equals(timestamp));
      expect(presence.deviceInfo?['type'], equals('mobile'));
    });

    test('should create online presence factory method', () {
      final onlinePresence = UserPresence.online(
        userId: 'user123',
        activity: UserActivity.viewingDashboard,
      );
      expect(onlinePresence.status, equals(PresenceStatus.online));
      expect(onlinePresence.userId, equals('user123'));
      expect(onlinePresence.activity, equals(UserActivity.viewingDashboard));
    });

    test('should serialize UserPresence to and from JSON', () {
      final timestamp = DateTime.now();
      final presence = UserPresence(
        userId: 'user123',
        status: PresenceStatus.busy,
        lastSeen: timestamp,
        activity: UserActivity.workingOnTask,
        activityContext: 'task456',
        statusMessage: 'Working on quest',
        organizationId: 'org789',
      );

      final json = presence.toJson();
      expect(json['userId'], equals('user123'));
      expect(json['status'], equals('busy'));
      expect(json['activity'], equals('working_on_task'));
      expect(json['activityContext'], equals('task456'));

      final presenceFromJson = UserPresence.fromJson(json);
      expect(presenceFromJson.userId, equals(presence.userId));
      expect(presenceFromJson.status, equals(presence.status));
      expect(presenceFromJson.activity, equals(presence.activity));
      expect(presenceFromJson.statusMessage, equals(presence.statusMessage));
    });

    test('should manage active rooms correctly', () {
      final presence = UserPresence(
        userId: 'user123',
        status: PresenceStatus.online,
        lastSeen: DateTime.now(),
        activeRooms: ['room1', 'room2', 'room3'],
      );

      expect(presence.activeRooms.contains('room1'), isTrue);
      expect(presence.activeRooms.contains('room4'), isFalse);
      expect(presence.activeRooms.length, equals(3));
    });
  });
}
