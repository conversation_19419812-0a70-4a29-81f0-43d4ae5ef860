import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('PredictionModel Tests', () {
    test('should create a prediction model with correct properties', () {
      final model = PredictionModel(
        id: 'model123',
        type: PredictionModelType.userBehavior,
        name: 'User Engagement Model',
        version: '2.1.0',
        confidenceLevel: ConfidenceLevel.high,
        confidenceScore: 0.89,
        trainedAt: DateTime.now().subtract(Duration(days: 1)),
        inputFeatures: ['login_frequency', 'task_completion_rate', 'engagement_duration'],
        metadata: {'algorithm': 'random_forest', 'samples': 10000},
      );

      expect(model.id, equals('model123'));
      expect(model.type, equals(PredictionModelType.userBehavior));
      expect(model.name, equals('User Engagement Model'));
      expect(model.confidenceLevel, equals(ConfidenceLevel.high));
      expect(model.confidenceScore, equals(0.89));
      expect(model.inputFeatures.length, equals(3));
      expect(model.isActive, isTrue);
    });

    test('should serialize prediction model to and from JSON', () {
      final now = DateTime.now();
      final model = PredictionModel(
        id: 'model456',
        type: PredictionModelType.taskCompletion,
        name: 'Task Completion Predictor',
        version: '1.5.0',
        confidenceLevel: ConfidenceLevel.medium,
        confidenceScore: 0.75,
        trainedAt: now,
        inputFeatures: ['difficulty_level', 'user_skill'],
        organizationId: 'org123',
        targetEntityId: 'user789',
      );

      final json = model.toJson();
      expect(json['id'], equals('model456'));
      expect(json['type'], equals('task_completion'));
      expect(json['name'], equals('Task Completion Predictor'));
      expect(json['confidenceLevel'], equals('medium'));

      final modelFromJson = PredictionModel.fromJson(json);
      expect(modelFromJson.id, equals(model.id));
      expect(modelFromJson.type, equals(model.type));
      expect(modelFromJson.name, equals(model.name));
      expect(modelFromJson.confidenceLevel, equals(model.confidenceLevel));
      expect(modelFromJson.organizationId, equals(model.organizationId));
    });

    test('should check if model is expired', () {
      final expiredModel = PredictionModel(
        id: 'expired',
        type: PredictionModelType.engagementForecast,
        name: 'Expired Model',
        version: '1.0',
        confidenceLevel: ConfidenceLevel.low,
        confidenceScore: 0.5,
        trainedAt: DateTime.now().subtract(Duration(days: 10)),
        inputFeatures: ['activity'],
        expiresAt: DateTime.now().subtract(Duration(hours: 1)),
      );
      expect(expiredModel.isExpired, isTrue);

      final validModel = PredictionModel(
        id: 'valid',
        type: PredictionModelType.engagementForecast,
        name: 'Valid Model',
        version: '1.0',
        confidenceLevel: ConfidenceLevel.high,
        confidenceScore: 0.9,
        trainedAt: DateTime.now().subtract(Duration(hours: 1)),
        inputFeatures: ['activity'],
        expiresAt: DateTime.now().add(Duration(hours: 1)),
      );
      expect(validModel.isExpired, isFalse);
    });
  });

  group('UserBehaviorPrediction Tests', () {
    test('should create a user behavior prediction', () {
      final baseModel = PredictionModel(
        id: 'behavior_model',
        type: PredictionModelType.userBehavior,
        name: 'User Behavior Predictor',
        version: '2.0',
        confidenceLevel: ConfidenceLevel.high,
        confidenceScore: 0.88,
        trainedAt: DateTime.now(),
        inputFeatures: ['engagement', 'productivity'],
      );

      final prediction = UserBehaviorPrediction(
        model: baseModel,
        engagementScore: 0.85,
        productivityScore: 0.92,
        churnRisk: RiskLevel.low,
        churnProbability: 0.15,
        recommendedActions: [
          'Increase task variety',
          'Provide skill development opportunities'
        ],
        nextActivities: [
          {'activity': 'complete_task', 'probability': 0.8},
          {'activity': 'start_new_quest', 'probability': 0.6},
        ],
      );

      expect(prediction.engagementScore, equals(0.85));
      expect(prediction.productivityScore, equals(0.92));
      expect(prediction.churnRisk, equals(RiskLevel.low));
      expect(prediction.recommendedActions.length, equals(2));
      expect(prediction.nextActivities.length, equals(2));
      expect(prediction.churnProbability, equals(0.15));
    });

    test('should serialize user behavior prediction to and from JSON', () {
      final baseModel = PredictionModel(
        id: 'test_model',
        type: PredictionModelType.userBehavior,
        name: 'Test Model',
        version: '1.0',
        confidenceLevel: ConfidenceLevel.medium,
        confidenceScore: 0.7,
        trainedAt: DateTime.now(),
        inputFeatures: ['test_feature'],
      );

      final prediction = UserBehaviorPrediction(
        model: baseModel,
        engagementScore: 0.75,
        productivityScore: 0.82,
        churnRisk: RiskLevel.medium,
        churnProbability: 0.35,
      );

      final json = prediction.toJson();
      expect(json['engagementScore'], equals(0.75));
      expect(json['productivityScore'], equals(0.82));
      expect(json['churnRisk'], equals('medium'));

      final predictionFromJson = UserBehaviorPrediction.fromJson(json);
      expect(predictionFromJson.engagementScore, equals(prediction.engagementScore));
      expect(predictionFromJson.productivityScore, equals(prediction.productivityScore));
      expect(predictionFromJson.churnRisk, equals(prediction.churnRisk));
    });
  });

  group('FeatureImportance Tests', () {
    test('should create feature importance with correct properties', () {
      final feature = FeatureImportance(
        featureName: 'login_frequency',
        importance: 0.85,
        category: 'engagement',
        description: 'How often user logs in per week',
      );

      expect(feature.featureName, equals('login_frequency'));
      expect(feature.importance, equals(0.85));
      expect(feature.category, equals('engagement'));
      expect(feature.description, equals('How often user logs in per week'));
    });

    test('should serialize feature importance to and from JSON', () {
      final feature = FeatureImportance(
        featureName: 'task_completion_rate',
        importance: 0.92,
        category: 'productivity',
      );

      final json = feature.toJson();
      expect(json['featureName'], equals('task_completion_rate'));
      expect(json['importance'], equals(0.92));
      expect(json['category'], equals('productivity'));

      final featureFromJson = FeatureImportance.fromJson(json);
      expect(featureFromJson.featureName, equals(feature.featureName));
      expect(featureFromJson.importance, equals(feature.importance));
      expect(featureFromJson.category, equals(feature.category));
    });
  });
}
