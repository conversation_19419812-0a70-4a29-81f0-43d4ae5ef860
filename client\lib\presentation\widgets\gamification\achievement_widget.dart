import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../../../core/theme/app_theme.dart';

/// Achievement badge widget with rarity-based styling
class AchievementBadge extends StatefulWidget {
  final String title;
  final String description;
  final String rarity;
  final IconData icon;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final VoidCallback? onTap;
  final double size;

  const AchievementBadge({
    super.key,
    required this.title,
    required this.description,
    required this.rarity,
    required this.icon,
    required this.isUnlocked,
    this.unlockedAt,
    this.onTap,
    this.size = 80.0,
  });

  @override
  State<AchievementBadge> createState() => _AchievementBadgeState();
}

class _AchievementBadgeState extends State<AchievementBadge>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.isUnlocked) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(AchievementBadge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!oldWidget.isUnlocked && widget.isUnlocked) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final rarityColor = AppTheme.getRarityColor(widget.rarity);

    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: widget.isUnlocked ? _scaleAnimation.value : 1.0,
            child: Transform.rotate(
              angle: widget.isUnlocked ? _rotationAnimation.value * 0.1 : 0.0,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: widget.isUnlocked
                      ? RadialGradient(
                          colors: [
                            rarityColor.withValues(alpha: 0.8),
                            rarityColor.withValues(alpha: 0.4),
                          ],
                        )
                      : null,
                  color: widget.isUnlocked
                      ? null
                      : theme.colorScheme.outline.withValues(alpha: 0.3),
                  border: Border.all(
                    color: widget.isUnlocked
                        ? rarityColor
                        : theme.colorScheme.outline,
                    width: 2,
                  ),
                  boxShadow: widget.isUnlocked
                      ? [
                          BoxShadow(
                            color: rarityColor.withValues(alpha: 0.3),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ]
                      : null,
                ),
                child: Icon(
                  widget.icon,
                  size: widget.size * 0.4,
                  color: widget.isUnlocked
                      ? Colors.white
                      : theme.colorScheme.outline,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Achievement card with detailed information
class AchievementCard extends StatelessWidget {
  final String title;
  final String description;
  final String rarity;
  final IconData icon;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final int? points;
  final VoidCallback? onTap;

  const AchievementCard({
    super.key,
    required this.title,
    required this.description,
    required this.rarity,
    required this.icon,
    required this.isUnlocked,
    this.unlockedAt,
    this.points,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final rarityColor = AppTheme.getRarityColor(rarity);

    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              AchievementBadge(
                title: title,
                description: description,
                rarity: rarity,
                icon: icon,
                isUnlocked: isUnlocked,
                unlockedAt: unlockedAt,
                size: 60,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isUnlocked
                            ? theme.colorScheme.onSurface
                            : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isUnlocked
                            ? theme.colorScheme.onSurface.withValues(alpha: 0.8)
                            : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: rarityColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            rarity.toUpperCase(),
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: rarityColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (points != null) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.successColor.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.stars,
                                  size: 12,
                                  color: AppTheme.successColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '$points',
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: AppTheme.successColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        if (isUnlocked && unlockedAt != null) ...[
                          const Spacer(),
                          Text(
                            _formatDate(unlockedAt!),
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Achievement showcase grid
class AchievementGrid extends StatelessWidget {
  final List<Map<String, dynamic>> achievements;
  final int crossAxisCount;
  final double childAspectRatio;

  const AchievementGrid({
    super.key,
    required this.achievements,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievement = achievements[index];
        return AchievementBadge(
          title: achievement['title'] ?? '',
          description: achievement['description'] ?? '',
          rarity: achievement['rarity'] ?? 'common',
          icon: _getIconFromString(achievement['icon']),
          isUnlocked: achievement['isUnlocked'] ?? false,
          unlockedAt: achievement['unlockedAt'],
          onTap: () => _showAchievementDetails(context, achievement),
        );
      },
    );
  }

  IconData _getIconFromString(String? iconName) {
    switch (iconName) {
      case 'trophy':
        return Icons.emoji_events;
      case 'star':
        return Icons.star;
      case 'medal':
        return Icons.military_tech;
      case 'crown':
        return Icons.workspace_premium;
      case 'diamond':
        return Icons.diamond;
      default:
        return Icons.emoji_events;
    }
  }

  void _showAchievementDetails(BuildContext context, Map<String, dynamic> achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(achievement['title'] ?? ''),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AchievementBadge(
              title: achievement['title'] ?? '',
              description: achievement['description'] ?? '',
              rarity: achievement['rarity'] ?? 'common',
              icon: _getIconFromString(achievement['icon']),
              isUnlocked: achievement['isUnlocked'] ?? false,
              unlockedAt: achievement['unlockedAt'],
              size: 100,
            ),
            const SizedBox(height: 16),
            Text(achievement['description'] ?? ''),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Simple achievement widget wrapper for compatibility
class AchievementWidget extends StatelessWidget {
  final List<Achievement>? achievements;
  final bool showAll;
  final int? maxCount;
  final AchievementLayoutStyle? layoutStyle;
  final VoidCallback? onTap;

  const AchievementWidget({
    super.key,
    this.achievements,
    this.showAll = false,
    this.maxCount,
    this.layoutStyle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Mock achievements for now since we don't have real data
    final mockAchievements = _getMockAchievements();
    final displayAchievements = showAll
        ? mockAchievements
        : mockAchievements.take(maxCount ?? 4).toList();

    if (layoutStyle == AchievementLayoutStyle.grid) {
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: displayAchievements.length,
        itemBuilder: (context, index) {
          final achievement = displayAchievements[index];
          return AchievementBadge(
            title: achievement.name,
            description: achievement.description,
            rarity: achievement.rarity.name,
            icon: _getAchievementIcon(achievement.type),
            isUnlocked: achievement.isActive, // Mock unlock status
            onTap: onTap,
            size: 60.0,
          );
        },
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: displayAchievements.length,
      itemBuilder: (context, index) {
        final achievement = displayAchievements[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: AchievementBadge(
            title: achievement.name,
            description: achievement.description,
            rarity: achievement.rarity.name,
            icon: _getAchievementIcon(achievement.type),
            isUnlocked: achievement.isActive, // Mock unlock status
            onTap: onTap,
            size: 60.0,
          ),
        );
      },
    );
  }

  List<Achievement> _getMockAchievements() {
    final now = DateTime.now();
    return [
      Achievement(
        id: '1',
        name: 'First Quest',
        description: 'Complete your first quest',
        type: AchievementType.progress,
        rarity: AchievementRarity.common,
        iconUrl: 'task_alt',
        pointsAwarded: 10,
        progressRequired: 1,
        isActive: true,
        createdAt: now,
      ),
      Achievement(
        id: '2',
        name: 'Streak Master',
        description: 'Maintain a 7-day streak',
        type: AchievementType.consistency,
        rarity: AchievementRarity.uncommon,
        iconUrl: 'local_fire_department',
        pointsAwarded: 50,
        progressRequired: 7,
        isActive: true,
        createdAt: now,
      ),
      Achievement(
        id: '3',
        name: 'Team Player',
        description: 'Collaborate on 5 quests',
        type: AchievementType.collaboration,
        rarity: AchievementRarity.rare,
        iconUrl: 'people',
        pointsAwarded: 100,
        progressRequired: 5,
        isActive: true,
        createdAt: now,
      ),
      Achievement(
        id: '4',
        name: 'Point Master',
        description: 'Earn 1000 points',
        type: AchievementType.progress,
        rarity: AchievementRarity.epic,
        iconUrl: 'stars',
        pointsAwarded: 200,
        progressRequired: 1000,
        isActive: true,
        createdAt: now,
      ),
    ];
  }

  IconData _getAchievementIcon(AchievementType type) {
    switch (type) {
      case AchievementType.progress:
        return Icons.task_alt;
      case AchievementType.consistency:
        return Icons.local_fire_department;
      case AchievementType.collaboration:
        return Icons.people;
      case AchievementType.skill:
        return Icons.school;
      case AchievementType.special:
        return Icons.celebration;
    }
  }
}

/// Achievement layout styles
enum AchievementLayoutStyle {
  list,
  grid,
}
