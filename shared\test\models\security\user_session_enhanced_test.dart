import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('UserSessionEnhanced Tests', () {
    late UserSessionEnhanced testSession;
    late DeviceInfo testDeviceInfo;
    late SessionGeoLocation testGeoLocation;
    
    setUp(() {
      testDeviceInfo = const DeviceInfo(
        deviceType: 'laptop',
        os: 'macOS',
        osVersion: '14.0',
        browser: 'Chrome',
        browserVersion: '120.0',
        screenResolution: '1920x1080',
        timezone: 'America/Los_Angeles',
        language: 'en-US',
      );
      
      testGeoLocation = const SessionGeoLocation(
        country: 'United States',
        countryCode: 'US',
        region: 'California',
        city: 'San Francisco',
        latitude: 37.7749,
        longitude: -122.4194,
        timezone: 'America/Los_Angeles',
        isp: 'Comcast Cable',
      );
      
      testSession = UserSessionEnhanced(
        id: 'session_123',
        userId: 'user_456',
        organizationId: 'org_789',
        sessionTokenHash: 'hashed_token_123',
        refreshTokenHash: 'hashed_refresh_456',
        deviceFingerprint: 'device_fp_789',
        deviceInfo: testDeviceInfo,
        ipAddress: '***********00',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        geoLocation: testGeoLocation,
        isTrustedDevice: true,
        loginMethod: LoginMethod.sso,
        mfaVerified: true,
        lastActivity: DateTime.parse('2025-01-15T14:30:00.000Z'),
        expiresAt: DateTime.parse('2025-01-15T22:30:00.000Z'), // 8 hours later
        absoluteExpiresAt: DateTime.parse('2025-01-16T14:30:00.000Z'), // 24 hours later
        isActive: true,
        logoutReason: null,
        createdAt: DateTime.parse('2025-01-15T14:30:00.000Z'),
        updatedAt: DateTime.parse('2025-01-15T14:35:00.000Z'),
      );
    });

    test('should create valid UserSessionEnhanced instance', () {
      expect(testSession.id, equals('session_123'));
      expect(testSession.userId, equals('user_456'));
      expect(testSession.organizationId, equals('org_789'));
      expect(testSession.sessionTokenHash, equals('hashed_token_123'));
      expect(testSession.isTrustedDevice, isTrue);
      expect(testSession.loginMethod, equals(LoginMethod.sso));
      expect(testSession.mfaVerified, isTrue);
      expect(testSession.isActive, isTrue);
    });

    test('should create empty UserSessionEnhanced for testing', () {
      final empty = UserSessionEnhanced.empty();
      expect(empty.id, isEmpty);
      expect(empty.userId, isEmpty);
      expect(empty.sessionTokenHash, isEmpty);
      expect(empty.ipAddress, isEmpty);
      expect(empty.isTrustedDevice, isFalse);
      expect(empty.mfaVerified, isFalse);
      expect(empty.isActive, isTrue);
      expect(empty.expiresAt.isAfter(empty.createdAt), isTrue);
      expect(empty.absoluteExpiresAt.isAfter(empty.expiresAt), isTrue);
    });

    group('LoginMethod Tests', () {
      test('should have correct display names', () {
        expect(LoginMethod.password.displayName, equals('Password'));
        expect(LoginMethod.sso.displayName, equals('Single Sign-On'));
        expect(LoginMethod.mfa.displayName, equals('Multi-Factor Authentication'));
        expect(LoginMethod.apiKey.displayName, equals('API Key'));
      });
    });

    group('DeviceInfo Tests', () {
      test('should create valid DeviceInfo instance', () {
        expect(testDeviceInfo.deviceType, equals('laptop'));
        expect(testDeviceInfo.os, equals('macOS'));
        expect(testDeviceInfo.osVersion, equals('14.0'));
        expect(testDeviceInfo.browser, equals('Chrome'));
        expect(testDeviceInfo.browserVersion, equals('120.0'));
        expect(testDeviceInfo.screenResolution, equals('1920x1080'));
        expect(testDeviceInfo.timezone, equals('America/Los_Angeles'));
        expect(testDeviceInfo.language, equals('en-US'));
      });

      test('should format display info correctly', () {
        expect(testDeviceInfo.displayInfo, equals('macOS 14.0 • Chrome 120.0'));
        
        const partialInfo = DeviceInfo(
          os: 'Windows',
          browser: 'Firefox',
        );
        expect(partialInfo.displayInfo, equals('Windows • Firefox'));
        
        const minimalInfo = DeviceInfo(os: 'Linux');
        expect(minimalInfo.displayInfo, equals('Linux'));
        
        const emptyInfo = DeviceInfo();
        expect(emptyInfo.displayInfo, isEmpty);
      });

      test('should serialize and deserialize correctly', () {
        final json = testDeviceInfo.toJson();
        final deserialized = DeviceInfo.fromJson(json);
        expect(deserialized, equals(testDeviceInfo));
      });

      test('should handle null fields in JSON', () {
        final json = {
          'device_type': 'mobile',
          'os': 'iOS',
          'os_version': null,
          'browser': null,
          'browser_version': null,
          'screen_resolution': null,
          'timezone': null,
          'language': null,
        };
        
        final deviceInfo = DeviceInfo.fromJson(json);
        expect(deviceInfo.deviceType, equals('mobile'));
        expect(deviceInfo.os, equals('iOS'));
        expect(deviceInfo.osVersion, isNull);
        expect(deviceInfo.browser, isNull);
      });
    });

    group('SessionGeoLocation Tests', () {
      test('should create valid SessionGeoLocation instance', () {
        expect(testGeoLocation.country, equals('United States'));
        expect(testGeoLocation.countryCode, equals('US'));
        expect(testGeoLocation.region, equals('California'));
        expect(testGeoLocation.city, equals('San Francisco'));
        expect(testGeoLocation.latitude, equals(37.7749));
        expect(testGeoLocation.longitude, equals(-122.4194));
      });

      test('should format display location correctly', () {
        expect(testGeoLocation.displayLocation, equals('San Francisco, California, United States'));
        
        const partialLocation = SessionGeoLocation(
          city: 'New York',
          country: 'United States',
        );
        expect(partialLocation.displayLocation, equals('New York, United States'));
        
        const minimalLocation = SessionGeoLocation(country: 'Canada');
        expect(minimalLocation.displayLocation, equals('Canada'));
      });

      test('should serialize and deserialize correctly', () {
        final json = testGeoLocation.toJson();
        final deserialized = SessionGeoLocation.fromJson(json);
        expect(deserialized, equals(testGeoLocation));
      });
    });

    group('Session Status and Validation', () {
      test('should detect session expiration correctly', () {
        final now = DateTime.now();
        final expiredSession = testSession.copyWith(
          expiresAt: now.subtract(const Duration(hours: 1)),
        );
        expect(expiredSession.isExpired, isTrue);
        expect(testSession.isExpired, isFalse);
      });

      test('should detect absolute expiration correctly', () {
        final now = DateTime.now();
        final absoluteExpiredSession = testSession.copyWith(
          absoluteExpiresAt: now.subtract(const Duration(hours: 1)),
        );
        expect(absoluteExpiredSession.isAbsoluteExpired, isTrue);
        expect(testSession.isAbsoluteExpired, isFalse);
      });

      test('should detect idle sessions correctly', () {
        final now = DateTime.now();
        final idleSession = testSession.copyWith(
          lastActivity: now.subtract(const Duration(hours: 1)),
        );
        expect(idleSession.isIdle, isTrue);
        
        final activeSession = testSession.copyWith(
          lastActivity: now.subtract(const Duration(minutes: 15)),
        );
        expect(activeSession.isIdle, isFalse);
      });

      test('should calculate session duration correctly', () {
        final session = testSession.copyWith(
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        );
        expect(session.sessionDuration.inHours, equals(2));
      });

      test('should calculate time since last activity correctly', () {
        final session = testSession.copyWith(
          lastActivity: DateTime.now().subtract(const Duration(minutes: 30)),
        );
        expect(session.timeSinceLastActivity.inMinutes, equals(30));
      });
    });

    group('Display Methods', () {
      test('should get correct device display name', () {
        expect(testSession.deviceDisplayName, equals('macOS 14.0 • Chrome 120.0'));
        
        final noDeviceInfoSession = testSession.copyWith(
          deviceInfo: null,
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        );
        expect(noDeviceInfoSession.deviceDisplayName, equals('Mobile Device'));
        
        final tabletSession = testSession.copyWith(
          deviceInfo: null,
          userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)',
        );
        expect(tabletSession.deviceDisplayName, equals('Tablet'));
        
        final desktopSession = testSession.copyWith(
          deviceInfo: null,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        );
        expect(desktopSession.deviceDisplayName, equals('Desktop'));
        
        final unknownSession = testSession.copyWith(
          deviceInfo: null,
          userAgent: null,
        );
        expect(unknownSession.deviceDisplayName, equals('Unknown Device'));
      });

      test('should get correct location display name', () {
        expect(testSession.locationDisplayName, equals('San Francisco, California, United States'));
        
        final noLocationSession = testSession.copyWith(geoLocation: null);
        expect(noLocationSession.locationDisplayName, equals('Unknown Location'));
      });

      test('should get correct session status', () {
        expect(testSession.status, equals('Active'));
        
        final inactiveSession = testSession.copyWith(isActive: false);
        expect(inactiveSession.status, equals('Inactive'));
        
        final now = DateTime.now();
        final expiredSession = testSession.copyWith(
          absoluteExpiresAt: now.subtract(const Duration(hours: 1)),
        );
        expect(expiredSession.status, equals('Expired'));
        
        final idleTimeoutSession = testSession.copyWith(
          expiresAt: now.subtract(const Duration(minutes: 1)),
          absoluteExpiresAt: now.add(const Duration(hours: 1)),
        );
        expect(idleTimeoutSession.status, equals('Idle Timeout'));
        
        final idleSession = testSession.copyWith(
          lastActivity: now.subtract(const Duration(hours: 1)),
        );
        expect(idleSession.status, equals('Idle'));
      });
    });

    group('Security Checks', () {
      test('should identify sessions needing MFA verification', () {
        expect(testSession.needsMFAVerification, isFalse); // MFA already verified
        
        final needsMFA = testSession.copyWith(mfaVerified: false);
        expect(needsMFA.needsMFAVerification, isTrue);
        
        final inactiveNeedsMFA = testSession.copyWith(
          mfaVerified: false,
          isActive: false,
        );
        expect(inactiveNeedsMFA.needsMFAVerification, isFalse); // Inactive session
      });

      test('should calculate remaining session time', () {
        final now = DateTime.now();
        final session = testSession.copyWith(
          absoluteExpiresAt: now.add(const Duration(hours: 2)),
        );
        
        final remaining = session.remainingTime;
        expect(remaining.inHours, equals(1)); // Approximately 2 hours
        
        final expiredSession = testSession.copyWith(
          absoluteExpiresAt: now.subtract(const Duration(hours: 1)),
        );
        expect(expiredSession.remainingTime, equals(Duration.zero));
      });
    });

    test('should serialize to JSON correctly', () {
      final json = testSession.toJson();
      expect(json['id'], equals('session_123'));
      expect(json['user_id'], equals('user_456'));
      expect(json['organization_id'], equals('org_789'));
      expect(json['session_token_hash'], equals('hashed_token_123'));
      expect(json['refresh_token_hash'], equals('hashed_refresh_456'));
      expect(json['device_fingerprint'], equals('device_fp_789'));
      expect(json['device_info'], isA<Map<String, dynamic>>());
      expect(json['geo_location'], isA<Map<String, dynamic>>());
      expect(json['is_trusted_device'], isTrue);
      expect(json['login_method'], equals('sso'));
      expect(json['mfa_verified'], isTrue);
      expect(json['is_active'], isTrue);
      expect(json['logout_reason'], isNull);
    });

    test('should deserialize from JSON correctly', () {
      final json = testSession.toJson();
      final deserialized = UserSessionEnhanced.fromJson(json);
      
      expect(deserialized.id, equals(testSession.id));
      expect(deserialized.userId, equals(testSession.userId));
      expect(deserialized.organizationId, equals(testSession.organizationId));
      expect(deserialized.sessionTokenHash, equals(testSession.sessionTokenHash));
      expect(deserialized.refreshTokenHash, equals(testSession.refreshTokenHash));
      expect(deserialized.deviceFingerprint, equals(testSession.deviceFingerprint));
      expect(deserialized.deviceInfo, equals(testSession.deviceInfo));
      expect(deserialized.geoLocation, equals(testSession.geoLocation));
      expect(deserialized.isTrustedDevice, equals(testSession.isTrustedDevice));
      expect(deserialized.loginMethod, equals(testSession.loginMethod));
      expect(deserialized.mfaVerified, equals(testSession.mfaVerified));
      expect(deserialized.isActive, equals(testSession.isActive));
    });

    test('should handle JSON deserialization with invalid login method', () {
      final json = testSession.toJson();
      json['login_method'] = 'invalid_method';
      
      final deserialized = UserSessionEnhanced.fromJson(json);
      expect(deserialized.loginMethod, equals(LoginMethod.password)); // Default fallback
    });

    test('should create copy with updated fields', () {
      final updated = testSession.copyWith(
        isActive: false,
        logoutReason: 'user_initiated',
        mfaVerified: false,
        lastActivity: DateTime.parse('2025-01-15T15:00:00.000Z'),
      );
      
      expect(updated.isActive, isFalse);
      expect(updated.logoutReason, equals('user_initiated'));
      expect(updated.mfaVerified, isFalse);
      expect(updated.lastActivity.hour, equals(15));
      expect(updated.id, equals(testSession.id)); // Unchanged
      expect(updated.sessionTokenHash, equals(testSession.sessionTokenHash)); // Unchanged
    });

    test('should maintain equality for identical instances', () {
      final json = testSession.toJson();
      final identical = UserSessionEnhanced.fromJson(json);
      expect(testSession, equals(identical));
    });

    test('should not be equal for different instances', () {
      final different = testSession.copyWith(id: 'different_id');
      expect(testSession, isNot(equals(different)));
    });

    group('Edge Cases', () {
      test('should handle null optional fields in JSON', () {
        final json = {
          'id': 'test_id',
          'user_id': 'user_123',
          'organization_id': null,
          'session_token_hash': 'token_hash',
          'refresh_token_hash': null,
          'device_fingerprint': null,
          'device_info': null,
          'ip_address': '***********',
          'user_agent': null,
          'geo_location': null,
          'is_trusted_device': false,
          'login_method': null,
          'mfa_verified': false,
          'last_activity': '2025-01-15T10:00:00.000Z',
          'expires_at': '2025-01-15T18:00:00.000Z',
          'absolute_expires_at': '2025-01-16T10:00:00.000Z',
          'is_active': true,
          'logout_reason': null,
          'created_at': '2025-01-15T10:00:00.000Z',
          'updated_at': '2025-01-15T10:00:00.000Z',
        };
        
        final session = UserSessionEnhanced.fromJson(json);
        expect(session.organizationId, isNull);
        expect(session.refreshTokenHash, isNull);
        expect(session.deviceFingerprint, isNull);
        expect(session.deviceInfo, isNull);
        expect(session.userAgent, isNull);
        expect(session.geoLocation, isNull);
        expect(session.loginMethod, isNull);
        expect(session.logoutReason, isNull);
      });

      test('should handle extreme timeout scenarios', () {
        final now = DateTime.now();
        final session = testSession.copyWith(
          createdAt: now.subtract(const Duration(days: 365)),
          expiresAt: now.subtract(const Duration(days: 364)),
          absoluteExpiresAt: now.subtract(const Duration(days: 363)),
          lastActivity: now.subtract(const Duration(days: 362)),
        );
        
        expect(session.isExpired, isTrue);
        expect(session.isAbsoluteExpired, isTrue);
        expect(session.isIdle, isTrue);
        expect(session.sessionDuration.inDays, greaterThan(360));
        expect(session.remainingTime, equals(Duration.zero));
      });

      test('should handle concurrent session limits', () {
        // This would be tested at the service layer, but we can verify the model supports it
        expect(testSession.userId, isA<String>());
        expect(testSession.isActive, isA<bool>());
        expect(testSession.organizationId, isA<String?>());
      });

      test('should handle device fingerprint validation', () {
        final sessionWithFingerprint = testSession.copyWith(
          deviceFingerprint: 'complex_fingerprint_123_abc_xyz',
        );
        expect(sessionWithFingerprint.deviceFingerprint, isA<String>());
        expect(sessionWithFingerprint.deviceFingerprint!.length, greaterThan(10));
        
        final sessionWithoutFingerprint = testSession.copyWith(
          deviceFingerprint: null,
        );
        expect(sessionWithoutFingerprint.deviceFingerprint, isNull);
      });
    });
  });
}