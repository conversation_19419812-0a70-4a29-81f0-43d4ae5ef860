import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('SecurityAuditLog Tests', () {
    late SecurityAuditLog testLog;
    late GeoLocation testGeoLocation;
    
    setUp(() {
      testGeoLocation = const GeoLocation(
        country: 'United States',
        countryCode: 'US',
        region: 'California',
        city: 'San Francisco',
        latitude: 37.7749,
        longitude: -122.4194,
        timezone: 'America/Los_Angeles',
        isp: 'Cloudflare Inc.',
      );
      
      testLog = SecurityAuditLog(
        id: 'log_123',
        organizationId: 'org_456',
        userId: 'user_789',
        eventType: 'login_success',
        eventCategory: SecurityEventCategory.authentication,
        eventDescription: 'User successfully logged in via SSO',
        eventSeverity: SecurityEventSeverity.low,
        ipAddress: '***********00',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        deviceFingerprint: 'device_fp_123',
        geoLocation: testGeoLocation,
        sessionId: 'session_456',
        requestId: 'req_789',
        resourceType: 'authentication',
        resourceId: 'auth_123',
        eventData: {
          'login_method': 'sso',
          'provider': 'google',
          'duration_ms': 1200,
        },
        eventMetadata: {
          'client_version': '2.1.0',
          'api_version': 'v1',
        },
        riskScore: 10,
        isAnomaly: false,
        createdAt: DateTime.parse('2025-01-15T14:30:00.000Z'),
      );
    });

    test('should create valid SecurityAuditLog instance', () {
      expect(testLog.id, equals('log_123'));
      expect(testLog.organizationId, equals('org_456'));
      expect(testLog.userId, equals('user_789'));
      expect(testLog.eventType, equals('login_success'));
      expect(testLog.eventCategory, equals(SecurityEventCategory.authentication));
      expect(testLog.eventSeverity, equals(SecurityEventSeverity.low));
      expect(testLog.riskScore, equals(10));
      expect(testLog.isAnomaly, isFalse);
    });

    test('should create empty SecurityAuditLog for testing', () {
      final empty = SecurityAuditLog.empty();
      expect(empty.id, isEmpty);
      expect(empty.eventType, isEmpty);
      expect(empty.eventDescription, isEmpty);
      expect(empty.eventCategory, equals(SecurityEventCategory.authentication));
      expect(empty.eventSeverity, equals(SecurityEventSeverity.medium));
      expect(empty.riskScore, equals(0));
      expect(empty.isAnomaly, isFalse);
    });

    group('Event Category and Severity', () {
      test('should have correct display names for categories', () {
        expect(SecurityEventCategory.authentication.displayName, equals('Authentication'));
        expect(SecurityEventCategory.authorization.displayName, equals('Authorization'));
        expect(SecurityEventCategory.dataAccess.displayName, equals('Data Access'));
        expect(SecurityEventCategory.configuration.displayName, equals('Configuration'));
        expect(SecurityEventCategory.securityPolicy.displayName, equals('Security Policy'));
        expect(SecurityEventCategory.mfa.displayName, equals('Multi-Factor Authentication'));
        expect(SecurityEventCategory.sso.displayName, equals('Single Sign-On'));
        expect(SecurityEventCategory.session.displayName, equals('Session Management'));
        expect(SecurityEventCategory.suspicious.displayName, equals('Suspicious Activity'));
      });

      test('should have correct display names and colors for severities', () {
        expect(SecurityEventSeverity.low.displayName, equals('Low'));
        expect(SecurityEventSeverity.medium.displayName, equals('Medium'));
        expect(SecurityEventSeverity.high.displayName, equals('High'));
        expect(SecurityEventSeverity.critical.displayName, equals('Critical'));
        
        expect(SecurityEventSeverity.low.color, equals('#28a745'));
        expect(SecurityEventSeverity.medium.color, equals('#ffc107'));
        expect(SecurityEventSeverity.high.color, equals('#fd7e14'));
        expect(SecurityEventSeverity.critical.color, equals('#dc3545'));
      });
    });

    group('Risk Assessment', () {
      test('should identify high-risk events correctly', () {
        final highRisk = testLog.copyWith(riskScore: 80);
        expect(highRisk.isHighRisk, isTrue);
        expect(highRisk.riskScore, equals(80));
        
        final lowRisk = testLog.copyWith(riskScore: 30);
        expect(lowRisk.isHighRisk, isFalse);
      });

      test('should identify critical events correctly', () {
        final critical = testLog.copyWith(eventSeverity: SecurityEventSeverity.critical);
        expect(critical.isCritical, isTrue);
        
        expect(testLog.isCritical, isFalse);
      });
    });

    group('Time and Display Utilities', () {
      test('should format time ago correctly', () {
        final now = DateTime.now();
        
        final recentLog = testLog.copyWith(createdAt: now.subtract(const Duration(minutes: 5)));
        expect(recentLog.timeAgo, equals('5m ago'));
        
        final hourlyLog = testLog.copyWith(createdAt: now.subtract(const Duration(hours: 3)));
        expect(hourlyLog.timeAgo, equals('3h ago'));
        
        final dailyLog = testLog.copyWith(createdAt: now.subtract(const Duration(days: 2)));
        expect(dailyLog.timeAgo, equals('2d ago'));
        
        final veryRecentLog = testLog.copyWith(createdAt: now.subtract(const Duration(seconds: 30)));
        expect(veryRecentLog.timeAgo, equals('Just now'));
      });

      test('should format timestamp correctly', () {
        expect(testLog.formattedTimestamp, contains('2025-01-15T14:30:00.000Z'));
      });

      test('should identify device type from user agent', () {
        final mobileLog = testLog.copyWith(
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        );
        expect(mobileLog.shortDeviceInfo, equals('Mobile'));
        
        final tabletLog = testLog.copyWith(
          userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)',
        );
        expect(tabletLog.shortDeviceInfo, equals('Tablet'));
        
        final desktopLog = testLog.copyWith(
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        );
        expect(desktopLog.shortDeviceInfo, equals('Desktop'));
        
        final noUserAgentLog = testLog.copyWith(userAgent: null);
        expect(noUserAgentLog.shortDeviceInfo, isNull);
      });

      test('should check location information availability', () {
        expect(testLog.hasLocationInfo, isTrue);
        
        final noLocationLog = testLog.copyWith(geoLocation: null);
        expect(noLocationLog.hasLocationInfo, isFalse);
      });
    });

    group('GeoLocation Tests', () {
      test('should create valid GeoLocation instance', () {
        expect(testGeoLocation.country, equals('United States'));
        expect(testGeoLocation.countryCode, equals('US'));
        expect(testGeoLocation.region, equals('California'));
        expect(testGeoLocation.city, equals('San Francisco'));
        expect(testGeoLocation.latitude, equals(37.7749));
        expect(testGeoLocation.longitude, equals(-122.4194));
      });

      test('should format display location correctly', () {
        expect(testGeoLocation.displayLocation, equals('San Francisco, California, United States'));
        
        const partialLocation = GeoLocation(
          city: 'New York',
          country: 'United States',
        );
        expect(partialLocation.displayLocation, equals('New York, United States'));
        
        const minimalLocation = GeoLocation(country: 'Canada');
        expect(minimalLocation.displayLocation, equals('Canada'));
      });

      test('should serialize GeoLocation to JSON', () {
        final json = testGeoLocation.toJson();
        expect(json['country'], equals('United States'));
        expect(json['country_code'], equals('US'));
        expect(json['city'], equals('San Francisco'));
        expect(json['latitude'], equals(37.7749));
        expect(json['longitude'], equals(-122.4194));
      });

      test('should deserialize GeoLocation from JSON', () {
        final json = testGeoLocation.toJson();
        final deserialized = GeoLocation.fromJson(json);
        expect(deserialized, equals(testGeoLocation));
      });
    });

    test('should serialize to JSON correctly', () {
      final json = testLog.toJson();
      expect(json['id'], equals('log_123'));
      expect(json['organization_id'], equals('org_456'));
      expect(json['user_id'], equals('user_789'));
      expect(json['event_type'], equals('login_success'));
      expect(json['event_category'], equals('authentication'));
      expect(json['event_severity'], equals('low'));
      expect(json['risk_score'], equals(10));
      expect(json['is_anomaly'], isFalse);
      expect(json['geo_location'], isA<Map<String, dynamic>>());
      expect(json['event_data'], isA<Map<String, dynamic>>());
      expect(json['event_metadata'], isA<Map<String, dynamic>>());
      expect(json['created_at'], isA<String>());
    });

    test('should deserialize from JSON correctly', () {
      final json = testLog.toJson();
      final deserialized = SecurityAuditLog.fromJson(json);
      
      expect(deserialized.id, equals(testLog.id));
      expect(deserialized.organizationId, equals(testLog.organizationId));
      expect(deserialized.userId, equals(testLog.userId));
      expect(deserialized.eventType, equals(testLog.eventType));
      expect(deserialized.eventCategory, equals(testLog.eventCategory));
      expect(deserialized.eventSeverity, equals(testLog.eventSeverity));
      expect(deserialized.riskScore, equals(testLog.riskScore));
      expect(deserialized.isAnomaly, equals(testLog.isAnomaly));
      expect(deserialized.geoLocation, equals(testLog.geoLocation));
    });

    test('should handle JSON deserialization with invalid enum values', () {
      final json = testLog.toJson();
      json['event_category'] = 'invalid_category';
      json['event_severity'] = 'invalid_severity';
      
      final deserialized = SecurityAuditLog.fromJson(json);
      expect(deserialized.eventCategory, equals(SecurityEventCategory.authentication)); // Default
      expect(deserialized.eventSeverity, equals(SecurityEventSeverity.medium)); // Default
    });

    test('should create copy with updated fields', () {
      final updated = testLog.copyWith(
        eventType: 'login_failed',
        eventSeverity: SecurityEventSeverity.medium,
        riskScore: 50,
        isAnomaly: true,
      );
      
      expect(updated.eventType, equals('login_failed'));
      expect(updated.eventSeverity, equals(SecurityEventSeverity.medium));
      expect(updated.riskScore, equals(50));
      expect(updated.isAnomaly, isTrue);
      expect(updated.id, equals(testLog.id)); // Unchanged
    });

    group('Factory Methods', () {
      test('should create login attempt log correctly', () {
        final successfulLogin = SecurityAuditLog.createLoginAttempt(
          organizationId: 'org_123',
          userId: 'user_456',
          ipAddress: '***********',
          userAgent: 'Test Agent',
          successful: true,
          geoLocation: testGeoLocation,
          sessionId: 'session_789',
          eventData: {'method': 'password'},
        );
        
        expect(successfulLogin.eventType, equals('login_success'));
        expect(successfulLogin.eventCategory, equals(SecurityEventCategory.authentication));
        expect(successfulLogin.eventSeverity, equals(SecurityEventSeverity.low));
        expect(successfulLogin.riskScore, equals(0));
        expect(successfulLogin.ipAddress, equals('***********'));
        
        final failedLogin = SecurityAuditLog.createLoginAttempt(
          organizationId: 'org_123',
          userId: 'user_456',
          ipAddress: '***********',
          userAgent: 'Test Agent',
          successful: false,
        );
        
        expect(failedLogin.eventType, equals('login_failed'));
        expect(failedLogin.eventSeverity, equals(SecurityEventSeverity.medium));
        expect(failedLogin.riskScore, equals(30));
      });

      test('should create MFA event log correctly', () {
        final mfaEvent = SecurityAuditLog.createMFAEvent(
          organizationId: 'org_123',
          userId: 'user_456',
          eventType: 'mfa_setup',
          eventDescription: 'User configured TOTP authenticator',
          ipAddress: '***********',
          sessionId: 'session_789',
          eventData: {'method': 'totp'},
        );
        
        expect(mfaEvent.eventType, equals('mfa_setup'));
        expect(mfaEvent.eventCategory, equals(SecurityEventCategory.mfa));
        expect(mfaEvent.eventSeverity, equals(SecurityEventSeverity.medium));
        expect(mfaEvent.riskScore, equals(20));
        expect(mfaEvent.eventDescription, equals('User configured TOTP authenticator'));
      });

      test('should create suspicious activity log correctly', () {
        final suspiciousEvent = SecurityAuditLog.createSuspiciousActivity(
          organizationId: 'org_123',
          userId: 'user_456',
          eventType: 'multiple_failed_logins',
          eventDescription: 'Multiple failed login attempts from new location',
          ipAddress: '********',
          userAgent: 'Suspicious Agent',
          geoLocation: testGeoLocation,
          riskScore: 85,
          eventData: {'attempt_count': 10},
        );
        
        expect(suspiciousEvent.eventType, equals('multiple_failed_logins'));
        expect(suspiciousEvent.eventCategory, equals(SecurityEventCategory.suspicious));
        expect(suspiciousEvent.eventSeverity, equals(SecurityEventSeverity.critical)); // Risk score >= 70
        expect(suspiciousEvent.riskScore, equals(85));
        expect(suspiciousEvent.isAnomaly, isTrue);
        expect(suspiciousEvent.eventData?['attempt_count'], equals(10));
        
        final mediumRiskEvent = SecurityAuditLog.createSuspiciousActivity(
          organizationId: 'org_123',
          eventType: 'unusual_access_pattern',
          eventDescription: 'Unusual access pattern detected',
          ipAddress: '********',
          riskScore: 60,
        );
        
        expect(mediumRiskEvent.eventSeverity, equals(SecurityEventSeverity.high)); // Risk score < 70
      });
    });

    group('Edge Cases', () {
      test('should handle null optional fields in JSON', () {
        final json = {
          'id': 'test_id',
          'organization_id': null,
          'user_id': null,
          'event_type': 'test_event',
          'event_category': 'authentication',
          'event_description': 'Test description',
          'event_severity': 'medium',
          'ip_address': null,
          'user_agent': null,
          'device_fingerprint': null,
          'geo_location': null,
          'session_id': null,
          'request_id': null,
          'resource_type': null,
          'resource_id': null,
          'event_data': null,
          'event_metadata': null,
          'risk_score': null,
          'is_anomaly': null,
          'created_at': '2025-01-15T10:00:00.000Z',
        };
        
        final log = SecurityAuditLog.fromJson(json);
        expect(log.organizationId, isNull);
        expect(log.userId, isNull);
        expect(log.ipAddress, isNull);
        expect(log.userAgent, isNull);
        expect(log.deviceFingerprint, isNull);
        expect(log.geoLocation, isNull);
        expect(log.sessionId, isNull);
        expect(log.requestId, isNull);
        expect(log.resourceType, isNull);
        expect(log.resourceId, isNull);
        expect(log.eventData, isNull);
        expect(log.eventMetadata, isNull);
        expect(log.riskScore, equals(0)); // Default value
        expect(log.isAnomaly, isFalse); // Default value
      });

      test('should handle complex event data structures', () {
        final complexEventData = {
          'nested_object': {
            'key1': 'value1',
            'key2': 123,
            'nested_array': [1, 2, 3],
          },
          'array_field': ['item1', 'item2'],
          'boolean_field': true,
          'null_field': null,
        };
        
        final log = testLog.copyWith(eventData: complexEventData);
        final json = log.toJson();
        final deserialized = SecurityAuditLog.fromJson(json);
        
        expect(deserialized.eventData, equals(complexEventData));
      });
    });
  });
}