// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_auth_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnhancedLoginRequestDto _$EnhancedLoginRequestDtoFromJson(
  Map<String, dynamic> json,
) => EnhancedLoginRequestDto(
  email: json['email'] as String,
  password: json['password'] as String,
  rememberMe: json['rememberMe'] as bool? ?? false,
  twoFactorCode: json['twoFactorCode'] as String?,
  organizationId: json['organizationId'] as String?,
  deviceInfo: (json['deviceInfo'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, e as String),
  ),
);

Map<String, dynamic> _$EnhancedLoginRequestDtoToJson(
  EnhancedLoginRequestDto instance,
) => <String, dynamic>{
  'email': instance.email,
  'password': instance.password,
  'rememberMe': instance.rememberMe,
  'twoFactorCode': instance.twoFactorCode,
  'organizationId': instance.organizationId,
  'deviceInfo': instance.deviceInfo,
};

OAuthLoginRequestDto _$OAuthLoginRequestDtoFromJson(
  Map<String, dynamic> json,
) => OAuthLoginRequestDto(
  provider: $enumDecode(_$OAuthProviderEnumMap, json['provider']),
  authorizationCode: json['authorizationCode'] as String,
  state: json['state'] as String?,
  redirectUri: json['redirectUri'] as String?,
  organizationId: json['organizationId'] as String?,
);

Map<String, dynamic> _$OAuthLoginRequestDtoToJson(
  OAuthLoginRequestDto instance,
) => <String, dynamic>{
  'provider': _$OAuthProviderEnumMap[instance.provider]!,
  'authorizationCode': instance.authorizationCode,
  'state': instance.state,
  'redirectUri': instance.redirectUri,
  'organizationId': instance.organizationId,
};

const _$OAuthProviderEnumMap = {
  OAuthProvider.google: 'google',
  OAuthProvider.microsoft: 'microsoft',
  OAuthProvider.github: 'github',
  OAuthProvider.slack: 'slack',
};

EnhancedRegisterRequestDto _$EnhancedRegisterRequestDtoFromJson(
  Map<String, dynamic> json,
) => EnhancedRegisterRequestDto(
  email: json['email'] as String,
  password: json['password'] as String,
  displayName: json['displayName'] as String,
  firstName: json['firstName'] as String?,
  lastName: json['lastName'] as String?,
  organizationName: json['organizationName'] as String?,
  invitationToken: json['invitationToken'] as String?,
  userPreferences: json['userPreferences'] as Map<String, dynamic>?,
  acceptTerms: json['acceptTerms'] as bool,
  subscribeToNewsletter: json['subscribeToNewsletter'] as bool? ?? false,
);

Map<String, dynamic> _$EnhancedRegisterRequestDtoToJson(
  EnhancedRegisterRequestDto instance,
) => <String, dynamic>{
  'email': instance.email,
  'password': instance.password,
  'displayName': instance.displayName,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'organizationName': instance.organizationName,
  'invitationToken': instance.invitationToken,
  'userPreferences': instance.userPreferences,
  'acceptTerms': instance.acceptTerms,
  'subscribeToNewsletter': instance.subscribeToNewsletter,
};

EnhancedAuthResponseDto _$EnhancedAuthResponseDtoFromJson(
  Map<String, dynamic> json,
) => EnhancedAuthResponseDto(
  session: AuthSession.fromJson(json['session'] as Map<String, dynamic>),
  requiresTwoFactor: json['requiresTwoFactor'] as bool? ?? false,
  availableTwoFactorMethods:
      (json['availableTwoFactorMethods'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$TwoFactorMethodEnumMap, e))
          .toList(),
  requiresEmailVerification:
      json['requiresEmailVerification'] as bool? ?? false,
  onboardingNextStep: json['onboardingNextStep'] as String?,
  additionalData: json['additionalData'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$EnhancedAuthResponseDtoToJson(
  EnhancedAuthResponseDto instance,
) => <String, dynamic>{
  'session': instance.session,
  'requiresTwoFactor': instance.requiresTwoFactor,
  'availableTwoFactorMethods': instance.availableTwoFactorMethods
      ?.map((e) => _$TwoFactorMethodEnumMap[e]!)
      .toList(),
  'requiresEmailVerification': instance.requiresEmailVerification,
  'onboardingNextStep': instance.onboardingNextStep,
  'additionalData': instance.additionalData,
};

const _$TwoFactorMethodEnumMap = {
  TwoFactorMethod.totp: 'totp',
  TwoFactorMethod.sms: 'sms',
  TwoFactorMethod.email: 'email',
  TwoFactorMethod.backupCodes: 'backup_codes',
};

TwoFactorSetupRequestDto _$TwoFactorSetupRequestDtoFromJson(
  Map<String, dynamic> json,
) => TwoFactorSetupRequestDto(
  method: $enumDecode(_$TwoFactorMethodEnumMap, json['method']),
  phoneNumber: json['phoneNumber'] as String?,
  email: json['email'] as String?,
  totpSecret: json['totpSecret'] as String?,
);

Map<String, dynamic> _$TwoFactorSetupRequestDtoToJson(
  TwoFactorSetupRequestDto instance,
) => <String, dynamic>{
  'method': _$TwoFactorMethodEnumMap[instance.method]!,
  'phoneNumber': instance.phoneNumber,
  'email': instance.email,
  'totpSecret': instance.totpSecret,
};

TwoFactorVerificationDto _$TwoFactorVerificationDtoFromJson(
  Map<String, dynamic> json,
) => TwoFactorVerificationDto(
  sessionToken: json['sessionToken'] as String,
  method: $enumDecode(_$TwoFactorMethodEnumMap, json['method']),
  code: json['code'] as String,
);

Map<String, dynamic> _$TwoFactorVerificationDtoToJson(
  TwoFactorVerificationDto instance,
) => <String, dynamic>{
  'sessionToken': instance.sessionToken,
  'method': _$TwoFactorMethodEnumMap[instance.method]!,
  'code': instance.code,
};

EmailVerificationDto _$EmailVerificationDtoFromJson(
  Map<String, dynamic> json,
) => EmailVerificationDto(
  verificationToken: json['verificationToken'] as String,
  email: json['email'] as String,
);

Map<String, dynamic> _$EmailVerificationDtoToJson(
  EmailVerificationDto instance,
) => <String, dynamic>{
  'verificationToken': instance.verificationToken,
  'email': instance.email,
};

InvitationAcceptanceDto _$InvitationAcceptanceDtoFromJson(
  Map<String, dynamic> json,
) => InvitationAcceptanceDto(
  invitationToken: json['invitationToken'] as String,
  password: json['password'] as String?,
  displayName: json['displayName'] as String?,
);

Map<String, dynamic> _$InvitationAcceptanceDtoToJson(
  InvitationAcceptanceDto instance,
) => <String, dynamic>{
  'invitationToken': instance.invitationToken,
  'password': instance.password,
  'displayName': instance.displayName,
};

UserImpersonationDto _$UserImpersonationDtoFromJson(
  Map<String, dynamic> json,
) => UserImpersonationDto(
  targetUserId: json['targetUserId'] as String,
  reason: json['reason'] as String,
  durationMinutes: (json['durationMinutes'] as num?)?.toInt(),
);

Map<String, dynamic> _$UserImpersonationDtoToJson(
  UserImpersonationDto instance,
) => <String, dynamic>{
  'targetUserId': instance.targetUserId,
  'reason': instance.reason,
  'durationMinutes': instance.durationMinutes,
};

SessionManagementDto _$SessionManagementDtoFromJson(
  Map<String, dynamic> json,
) => SessionManagementDto(
  action: json['action'] as String,
  targetSessionId: json['targetSessionId'] as String?,
  parameters: json['parameters'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$SessionManagementDtoToJson(
  SessionManagementDto instance,
) => <String, dynamic>{
  'action': instance.action,
  'targetSessionId': instance.targetSessionId,
  'parameters': instance.parameters,
};

PasswordPolicyDto _$PasswordPolicyDtoFromJson(Map<String, dynamic> json) =>
    PasswordPolicyDto(
      isValid: json['isValid'] as bool,
      minimumLength: (json['minimumLength'] as num?)?.toInt(),
      requiresUppercase: json['requiresUppercase'] as bool,
      requiresLowercase: json['requiresLowercase'] as bool,
      requiresNumbers: json['requiresNumbers'] as bool,
      requiresSpecialChars: json['requiresSpecialChars'] as bool,
      failedRequirements: (json['failedRequirements'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      strengthScore: (json['strengthScore'] as num).toInt(),
    );

Map<String, dynamic> _$PasswordPolicyDtoToJson(PasswordPolicyDto instance) =>
    <String, dynamic>{
      'isValid': instance.isValid,
      'minimumLength': instance.minimumLength,
      'requiresUppercase': instance.requiresUppercase,
      'requiresLowercase': instance.requiresLowercase,
      'requiresNumbers': instance.requiresNumbers,
      'requiresSpecialChars': instance.requiresSpecialChars,
      'failedRequirements': instance.failedRequirements,
      'strengthScore': instance.strengthScore,
    };

AuthAuditLogDto _$AuthAuditLogDtoFromJson(Map<String, dynamic> json) =>
    AuthAuditLogDto(
      id: json['id'] as String,
      userId: json['userId'] as String,
      action: json['action'] as String,
      ipAddress: json['ipAddress'] as String,
      userAgent: json['userAgent'] as String,
      organizationId: json['organizationId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      success: json['success'] as bool,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$AuthAuditLogDtoToJson(AuthAuditLogDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'action': instance.action,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'organizationId': instance.organizationId,
      'metadata': instance.metadata,
      'timestamp': instance.timestamp.toIso8601String(),
      'success': instance.success,
      'errorMessage': instance.errorMessage,
    };
