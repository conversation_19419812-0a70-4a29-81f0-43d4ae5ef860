// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WebSocketEvent _$WebSocketEventFromJson(Map<String, dynamic> json) =>
    WebSocketEvent(
      id: json['id'] as String,
      type: $enumDecode(_$WebSocketEventTypeEnumMap, json['type']),
      priority:
          $enumDecodeNullable(_$EventPriorityEnumMap, json['priority']) ??
          EventPriority.normal,
      userId: json['userId'] as String?,
      targetUserIds: (json['targetUserIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      organizationId: json['organizationId'] as String?,
      roomId: json['roomId'] as String?,
      data: json['data'] as Map<String, dynamic>,
      metadata: json['metadata'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      ttl: (json['ttl'] as num?)?.toInt(),
      requiresAck: json['requiresAck'] as bool? ?? false,
      retryCount: (json['retryCount'] as num?)?.toInt() ?? 0,
      maxRetries: (json['maxRetries'] as num?)?.toInt() ?? 3,
    );

Map<String, dynamic> _$WebSocketEventToJson(WebSocketEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$WebSocketEventTypeEnumMap[instance.type]!,
      'priority': _$EventPriorityEnumMap[instance.priority]!,
      'userId': instance.userId,
      'targetUserIds': instance.targetUserIds,
      'organizationId': instance.organizationId,
      'roomId': instance.roomId,
      'data': instance.data,
      'metadata': instance.metadata,
      'timestamp': instance.timestamp.toIso8601String(),
      'ttl': instance.ttl,
      'requiresAck': instance.requiresAck,
      'retryCount': instance.retryCount,
      'maxRetries': instance.maxRetries,
    };

const _$WebSocketEventTypeEnumMap = {
  WebSocketEventType.connection: 'connection',
  WebSocketEventType.disconnection: 'disconnection',
  WebSocketEventType.userPresence: 'user_presence',
  WebSocketEventType.questUpdate: 'quest_update',
  WebSocketEventType.taskUpdate: 'task_update',
  WebSocketEventType.achievementUnlock: 'achievement_unlock',
  WebSocketEventType.leaderboardUpdate: 'leaderboard_update',
  WebSocketEventType.notification: 'notification',
  WebSocketEventType.collaborationInvite: 'collaboration_invite',
  WebSocketEventType.liveEditing: 'live_editing',
  WebSocketEventType.chatMessage: 'chat_message',
  WebSocketEventType.typingIndicator: 'typing_indicator',
  WebSocketEventType.systemAnnouncement: 'system_announcement',
  WebSocketEventType.error: 'error',
  WebSocketEventType.heartbeat: 'heartbeat',
};

const _$EventPriorityEnumMap = {
  EventPriority.low: 'low',
  EventPriority.normal: 'normal',
  EventPriority.high: 'high',
  EventPriority.critical: 'critical',
};
