-- Migration: Security Query Optimization Indexes
-- Version: 1.0.0
-- Date: 2025-08-21
-- Description: Create specialized indexes for optimal performance of security-related queries

BEGIN;

-- =============================================================================
-- SECURITY AUDIT LOG INDEXES
-- =============================================================================

-- Composite indexes for common audit query patterns
CREATE INDEX IF NOT EXISTS idx_security_audit_logs_user_action_time 
ON security_audit_logs(user_id, action, created_at DESC) 
WHERE user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_security_audit_logs_org_event_time 
ON security_audit_logs(organization_id, event_type, created_at DESC) 
WHERE organization_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_security_audit_logs_risk_time 
ON security_audit_logs(risk_score DESC, created_at DESC) 
WHERE risk_score > 5;

CREATE INDEX IF NOT EXISTS idx_security_audit_logs_ip_time 
ON security_audit_logs(ip_address, created_at DESC) 
WHERE ip_address IS NOT NULL;

-- Partial index for suspicious activities (high-priority queries)
CREATE INDEX IF NOT EXISTS idx_security_audit_logs_suspicious 
ON security_audit_logs(created_at DESC, user_id, action) 
WHERE event_type = 'suspicious' OR risk_score >= 8;

-- Index for compliance reporting queries
CREATE INDEX IF NOT EXISTS idx_security_audit_logs_compliance 
ON security_audit_logs(created_at DESC, event_type, organization_id) 
WHERE event_type IN ('dataAccess', 'configuration', 'securityPolicy');

-- Geographic analysis index
CREATE INDEX IF NOT EXISTS idx_security_audit_logs_geo 
ON security_audit_logs((geo_location->>'country'), (geo_location->>'region'), created_at DESC) 
WHERE geo_location IS NOT NULL;

-- =============================================================================
-- USER SSO IDENTITY INDEXES
-- =============================================================================

-- Fast SSO login lookups
CREATE INDEX IF NOT EXISTS idx_user_sso_identities_provider_external 
ON user_sso_identities(sso_provider_id, external_id) 
WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_user_sso_identities_user_provider 
ON user_sso_identities(user_id, sso_provider_id) 
WHERE is_active = true;

-- Index for SSO attribute queries
CREATE INDEX IF NOT EXISTS idx_user_sso_identities_attributes 
ON user_sso_identities USING GIN(attributes) 
WHERE attributes IS NOT NULL;

-- =============================================================================
-- USER MFA SETTINGS INDEXES
-- =============================================================================

-- MFA enforcement queries
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_enforcement 
ON user_mfa_settings(user_id, is_enabled, enforcement_date) 
WHERE is_enabled = true OR enforcement_date IS NOT NULL;

-- Trusted device cleanup queries
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_devices_cleanup 
ON user_mfa_settings USING GIN(trusted_devices) 
WHERE jsonb_array_length(trusted_devices) > 0;

-- Backup codes usage tracking
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_backup_codes 
ON user_mfa_settings(user_id, backup_codes_generated_at) 
WHERE backup_codes IS NOT NULL;

-- =============================================================================
-- ORGANIZATION SECURITY POLICY INDEXES
-- =============================================================================

-- Active policy lookups
CREATE INDEX IF NOT EXISTS idx_org_security_policies_active 
ON organization_security_policies(organization_id, is_active, updated_at DESC) 
WHERE is_active = true;

-- Policy hierarchy queries
CREATE INDEX IF NOT EXISTS idx_org_security_policies_hierarchy 
ON organization_security_policies(parent_policy_id, created_at) 
WHERE parent_policy_id IS NOT NULL;

-- Compliance tracking
CREATE INDEX IF NOT EXISTS idx_org_security_policies_compliance 
ON organization_security_policies USING GIN((audit_policy->'compliance_standards')) 
WHERE (audit_policy->>'compliance_standards') IS NOT NULL;

-- =============================================================================
-- USER SESSION ENHANCED INDEXES
-- =============================================================================

-- Session security monitoring
CREATE INDEX IF NOT EXISTS idx_user_sessions_risk_monitoring 
ON user_sessions_enhanced(user_id, risk_score DESC, created_at DESC) 
WHERE is_active = true AND risk_score > 0;

-- Device fingerprint tracking
CREATE INDEX IF NOT EXISTS idx_user_sessions_device_tracking 
ON user_sessions_enhanced(device_fingerprint, user_id, created_at DESC) 
WHERE device_fingerprint IS NOT NULL;

-- Concurrent session management
CREATE INDEX IF NOT EXISTS idx_user_sessions_concurrent 
ON user_sessions_enhanced(user_id, is_active, created_at DESC) 
WHERE is_active = true;

-- Geographic anomaly detection
CREATE INDEX IF NOT EXISTS idx_user_sessions_geo_anomaly 
ON user_sessions_enhanced((geo_location->>'country'), user_id, created_at DESC) 
WHERE geo_location IS NOT NULL;

-- Session cleanup (expired sessions)
CREATE INDEX IF NOT EXISTS idx_user_sessions_cleanup 
ON user_sessions_enhanced(expires_at, is_active) 
WHERE expires_at < NOW() OR is_active = false;

-- =============================================================================
-- IP ACCESS CONTROL INDEXES
-- =============================================================================

-- Real-time access control checks
CREATE INDEX IF NOT EXISTS idx_ip_access_control_active_rules 
ON ip_access_control(organization_id, rule_type, is_active, created_at DESC) 
WHERE is_active = true;

-- IP range searches (using GiST for INET operations)
CREATE INDEX IF NOT EXISTS idx_ip_access_control_ip_ranges 
ON ip_access_control USING GIST(ip_range) 
WHERE ip_range IS NOT NULL;

-- Geographic IP filtering
CREATE INDEX IF NOT EXISTS idx_ip_access_control_geo 
ON ip_access_control((geo_location->>'country'), rule_type, is_active) 
WHERE geo_location IS NOT NULL AND is_active = true;

-- Risk-based access control
CREATE INDEX IF NOT EXISTS idx_ip_access_control_risk 
ON ip_access_control(risk_score DESC, rule_type, organization_id) 
WHERE risk_score > 0;

-- =============================================================================
-- EXISTING TABLE SECURITY INDEXES
-- =============================================================================

-- Users table - security-focused composite indexes
CREATE INDEX IF NOT EXISTS idx_users_security_status 
ON users(organization_id, mfa_enabled, account_locked_until, last_login_at DESC) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_users_login_analysis 
ON users(failed_login_attempts, last_login_at DESC, organization_id) 
WHERE failed_login_attempts > 0 OR last_login_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_users_password_security 
ON users(password_changed_at, requires_password_change, organization_id) 
WHERE requires_password_change = true OR password_changed_at IS NOT NULL;

-- SSO integration lookups
CREATE INDEX IF NOT EXISTS idx_users_sso_integration 
ON users(sso_provider_id, sso_external_id, organization_id) 
WHERE sso_provider_id IS NOT NULL;

-- Organizations table - security management indexes
CREATE INDEX IF NOT EXISTS idx_organizations_security_config 
ON organizations(sso_enabled, mfa_required, risk_level, last_security_review) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_organizations_compliance 
ON organizations USING GIN(compliance_standards) 
WHERE compliance_standards IS NOT NULL;

-- Sessions table - performance optimization
CREATE INDEX IF NOT EXISTS idx_sessions_active_monitoring 
ON sessions(user_id, is_active, last_activity_at DESC, risk_score DESC) 
WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_sessions_security_analysis 
ON sessions(ip_address, device_fingerprint, risk_score DESC) 
WHERE ip_address IS NOT NULL OR device_fingerprint IS NOT NULL;

-- Quests and Tasks - security classification
CREATE INDEX IF NOT EXISTS idx_quests_security_access 
ON quests(security_classification, requires_mfa, organization_id) 
WHERE security_classification != 'public' OR requires_mfa = true;

CREATE INDEX IF NOT EXISTS idx_tasks_security_access 
ON tasks(security_classification, requires_mfa, audit_required) 
WHERE security_classification != 'public' OR requires_mfa = true OR audit_required = true;

-- =============================================================================
-- COVERING INDEXES FOR COMMON SECURITY QUERIES
-- =============================================================================

-- User security dashboard query optimization
CREATE INDEX IF NOT EXISTS idx_users_dashboard_covering 
ON users(organization_id, deleted_at) 
INCLUDE (id, email, name, mfa_enabled, last_login_at, failed_login_attempts, account_locked_until) 
WHERE deleted_at IS NULL;

-- Organization security overview covering index
CREATE INDEX IF NOT EXISTS idx_organizations_overview_covering 
ON organizations(deleted_at) 
INCLUDE (id, name, sso_enabled, mfa_required, risk_level, last_security_review) 
WHERE deleted_at IS NULL;

-- Active sessions covering index for security monitoring
CREATE INDEX IF NOT EXISTS idx_sessions_monitoring_covering 
ON sessions(is_active, expires_at) 
INCLUDE (id, user_id, ip_address, device_fingerprint, risk_score, last_activity_at) 
WHERE is_active = true;

-- =============================================================================
-- PARTIAL INDEXES FOR HIGH-FREQUENCY SECURITY QUERIES
-- =============================================================================

-- Failed login monitoring (security alerts)
CREATE INDEX IF NOT EXISTS idx_users_failed_logins_monitoring 
ON users(failed_login_attempts DESC, last_login_at DESC) 
WHERE failed_login_attempts >= 3 AND deleted_at IS NULL;

-- Locked accounts tracking
CREATE INDEX IF NOT EXISTS idx_users_locked_accounts 
ON users(account_locked_until, organization_id, failed_login_attempts) 
WHERE account_locked_until IS NOT NULL AND account_locked_until > NOW();

-- High-risk sessions monitoring
CREATE INDEX IF NOT EXISTS idx_sessions_high_risk 
ON sessions(risk_score DESC, created_at DESC, user_id) 
WHERE is_active = true AND risk_score >= 50;

-- MFA enforcement tracking
CREATE INDEX IF NOT EXISTS idx_users_mfa_enforcement 
ON users(organization_id, mfa_enabled, requires_password_change) 
WHERE mfa_enabled = false AND deleted_at IS NULL;

-- SSO provider active status
CREATE INDEX IF NOT EXISTS idx_sso_providers_active 
ON sso_providers(organization_id, is_active, is_primary) 
WHERE is_active = true;

-- Recent audit events for real-time monitoring
CREATE INDEX IF NOT EXISTS idx_audit_logs_realtime 
ON security_audit_logs(created_at DESC, event_type, risk_score DESC) 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- =============================================================================
-- FUNCTIONAL INDEXES FOR JSON OPERATIONS
-- =============================================================================

-- User trusted devices analysis
CREATE INDEX IF NOT EXISTS idx_users_trusted_devices_count 
ON users(jsonb_array_length(trusted_devices), user_id) 
WHERE trusted_devices IS NOT NULL;

-- Session geo-location country extraction
CREATE INDEX IF NOT EXISTS idx_sessions_geo_country 
ON sessions((geo_location->>'country'), user_id, created_at DESC) 
WHERE geo_location IS NOT NULL;

-- Security policy JSON field optimizations
CREATE INDEX IF NOT EXISTS idx_org_policies_password_min_length 
ON organization_security_policies((password_policy->>'min_length')::int, organization_id) 
WHERE password_policy IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_org_policies_session_timeout 
ON organization_security_policies((session_policy->>'timeout_minutes')::int, organization_id) 
WHERE session_policy IS NOT NULL;

-- IP access control rule matching
CREATE INDEX IF NOT EXISTS idx_ip_access_time_windows 
ON ip_access_control USING GIN(time_restrictions) 
WHERE time_restrictions IS NOT NULL;

-- =============================================================================
-- BTREE INDEXES FOR RANGE QUERIES
-- =============================================================================

-- Audit log retention and cleanup
CREATE INDEX IF NOT EXISTS idx_audit_logs_retention 
ON security_audit_logs(created_at) 
WHERE created_at < NOW() - INTERVAL '1 year';

-- Session expiration cleanup
CREATE INDEX IF NOT EXISTS idx_sessions_expiration 
ON sessions(expires_at) 
WHERE expires_at < NOW();

-- User password expiration tracking
CREATE INDEX IF NOT EXISTS idx_users_password_expiration 
ON users(password_changed_at) 
WHERE password_changed_at < NOW() - INTERVAL '90 days';

-- =============================================================================
-- HASH INDEXES FOR EQUALITY QUERIES
-- =============================================================================

-- Fast token lookups (if your database supports hash indexes well)
-- Note: PostgreSQL hash indexes are generally not recommended for most cases
-- CREATE INDEX IF NOT EXISTS idx_sessions_token_hash ON sessions USING HASH(session_token);

-- =============================================================================
-- EXPRESSION INDEXES FOR COMPUTED VALUES
-- =============================================================================

-- User account age for risk assessment
CREATE INDEX IF NOT EXISTS idx_users_account_age 
ON users((EXTRACT(days FROM NOW() - created_at)), organization_id) 
WHERE deleted_at IS NULL;

-- Session duration tracking
CREATE INDEX IF NOT EXISTS idx_sessions_duration 
ON sessions((EXTRACT(epoch FROM (COALESCE(terminated_at, NOW()) - created_at))/3600)::int, user_id) 
WHERE created_at IS NOT NULL;

-- Days since last security review
CREATE INDEX IF NOT EXISTS idx_organizations_security_review_age 
ON organizations((EXTRACT(days FROM NOW() - last_security_review))::int, risk_level) 
WHERE last_security_review IS NOT NULL;

-- =============================================================================
-- STATISTICS AND MAINTENANCE
-- =============================================================================

-- Analyze tables to update statistics for query planning
ANALYZE security_audit_logs;
ANALYZE user_sso_identities;
ANALYZE user_mfa_settings;
ANALYZE organization_security_policies;
ANALYZE user_sessions_enhanced;
ANALYZE ip_access_control;
ANALYZE sso_providers;
ANALYZE users;
ANALYZE organizations;
ANALYZE sessions;
ANALYZE quests;
ANALYZE tasks;

-- Set statistics target for critical columns (improves query planning)
ALTER TABLE security_audit_logs ALTER COLUMN created_at SET STATISTICS 1000;
ALTER TABLE security_audit_logs ALTER COLUMN risk_score SET STATISTICS 500;
ALTER TABLE users ALTER COLUMN last_login_at SET STATISTICS 500;
ALTER TABLE users ALTER COLUMN failed_login_attempts SET STATISTICS 300;
ALTER TABLE sessions ALTER COLUMN expires_at SET STATISTICS 500;

COMMIT;

-- =============================================================================
-- INDEX USAGE MONITORING QUERIES
-- =============================================================================

-- Use these queries to monitor index usage and performance:

/*
-- Check index usage statistics
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as times_used,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
    AND (indexname LIKE 'idx_%security%' OR indexname LIKE 'idx_%audit%' OR indexname LIKE 'idx_%sso%')
ORDER BY idx_scan DESC;

-- Check for unused indexes (be careful before dropping!)
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
    AND idx_scan < 10
    AND (indexname LIKE 'idx_%security%' OR indexname LIKE 'idx_%audit%' OR indexname LIKE 'idx_%sso%')
ORDER BY pg_relation_size(indexname::regclass) DESC;

-- Check index bloat
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_total_relation_size(indexname::regclass)) as total_size,
    pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
    AND (indexname LIKE 'idx_%security%' OR indexname LIKE 'idx_%audit%' OR indexname LIKE 'idx_%sso%')
ORDER BY pg_total_relation_size(indexname::regclass) DESC;
*/