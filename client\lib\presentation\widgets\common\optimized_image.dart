import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Optimized image widget with lazy loading and caching
class OptimizedImage extends StatelessWidget {
  final String? imageUrl;
  final String? assetPath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Duration fadeInDuration;
  final Duration placeholderFadeInDuration;
  final int? memCacheWidth;
  final int? memCacheHeight;

  const OptimizedImage({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.placeholderFadeInDuration = const Duration(milliseconds: 100),
    this.memCacheWidth,
    this.memCacheHeight,
  }) : assert(imageUrl != null || assetPath != null, 'Either imageUrl or assetPath must be provided');

  @override
  Widget build(BuildContext context) {
    // Asset image
    if (assetPath != null) {
      return _buildAssetImage();
    }

    // Network image with caching
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return _buildNetworkImage();
    }

    // Fallback
    return _buildErrorWidget();
  }

  Widget _buildAssetImage() {
    return Image.asset(
      assetPath!,
      width: width,
      height: height,
      fit: fit,
      // Performance optimization: Use memory cache dimensions
      cacheWidth: memCacheWidth,
      cacheHeight: memCacheHeight,
      errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
    );
  }

  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: imageUrl!,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      fadeInDuration: fadeInDuration,
      placeholderFadeInDuration: placeholderFadeInDuration,
      // Performance optimizations
      memCacheWidth: memCacheWidth,
      memCacheHeight: memCacheHeight,
      useOldImageOnUrlChange: true,
      filterQuality: FilterQuality.medium,
      // Cache configuration
      cacheManager: enableDiskCache ? null : _createNoCacheManager(),
    );
  }

  Widget _buildPlaceholder() {
    if (placeholder != null) return placeholder!;
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (errorWidget != null) return errorWidget!;
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: const Icon(
        Icons.broken_image_outlined,
        color: Colors.grey,
        size: 32,
      ),
    );
  }

  // Create a cache manager that doesn't cache to disk
  dynamic _createNoCacheManager() {
    // This would return a custom cache manager in a real implementation
    return null;
  }
}

/// Optimized avatar image with circular clipping
class OptimizedAvatar extends StatelessWidget {
  final String? imageUrl;
  final String? assetPath;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final String? fallbackText;

  const OptimizedAvatar({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.radius = 20,
    this.placeholder,
    this.errorWidget,
    this.fallbackText,
  });

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: Colors.grey[200],
      child: ClipOval(
        child: OptimizedImage(
          imageUrl: imageUrl,
          assetPath: assetPath,
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          placeholder: placeholder ?? _buildDefaultPlaceholder(),
          errorWidget: errorWidget ?? _buildDefaultError(),
          // Performance optimization: Use smaller cache dimensions for avatars
          memCacheWidth: (radius * 2 * MediaQuery.of(context).devicePixelRatio).round(),
          memCacheHeight: (radius * 2 * MediaQuery.of(context).devicePixelRatio).round(),
        ),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      color: Colors.grey[300],
      child: const Icon(Icons.person, color: Colors.white),
    );
  }

  Widget _buildDefaultError() {
    if (fallbackText != null && fallbackText!.isNotEmpty) {
      return Container(
        width: radius * 2,
        height: radius * 2,
        color: Colors.grey[400],
        child: Center(
          child: Text(
            fallbackText![0].toUpperCase(),
            style: TextStyle(
              color: Colors.white,
              fontSize: radius * 0.8,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    }
    
    return Container(
      width: radius * 2,
      height: radius * 2,
      color: Colors.grey[300],
      child: const Icon(Icons.person, color: Colors.white),
    );
  }
}

/// Lazy loading image list for better performance
class LazyImageList extends StatelessWidget {
  final List<String> imageUrls;
  final double itemHeight;
  final EdgeInsets padding;
  final ScrollPhysics? physics;
  final Widget Function(String imageUrl, int index)? itemBuilder;

  const LazyImageList({
    super.key,
    required this.imageUrls,
    this.itemHeight = 200,
    this.padding = const EdgeInsets.all(8),
    this.physics,
    this.itemBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: physics,
      padding: padding,
      itemCount: imageUrls.length,
      // Performance optimization: Set item extent for better scrolling
      itemExtent: itemHeight,
      // Performance optimization: Add cache extent
      cacheExtent: 500,
      itemBuilder: (context, index) {
        final imageUrl = imageUrls[index];
        
        if (itemBuilder != null) {
          return itemBuilder!(imageUrl, index);
        }
        
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: OptimizedImage(
            imageUrl: imageUrl,
            height: itemHeight - 8,
            fit: BoxFit.cover,
            // Performance optimization: Use appropriate cache dimensions
            memCacheWidth: (MediaQuery.of(context).size.width * MediaQuery.of(context).devicePixelRatio).round(),
            memCacheHeight: ((itemHeight - 8) * MediaQuery.of(context).devicePixelRatio).round(),
          ),
        );
      },
    );
  }
}
