import 'package:equatable/equatable.dart';

/// Password policy configuration
class PasswordPolicy extends Equatable {
  final int minLength;
  final bool requireUppercase;
  final bool requireLowercase;
  final bool requireNumbers;
  final bool requireSymbols;
  final int maxAgeDays;
  final int historyCount;
  final int? expiryDays;
  final int? lockoutThreshold;
  final int? lockoutDurationMinutes;
  final bool? requireSpecialChars;

  const PasswordPolicy({
    required this.minLength,
    required this.requireUppercase,
    required this.requireLowercase,
    required this.requireNumbers,
    required this.requireSymbols,
    required this.maxAgeDays,
    required this.historyCount,
    this.expiryDays,
    this.lockoutThreshold,
    this.lockoutDurationMinutes,
    this.requireSpecialChars,
  });

  factory PasswordPolicy.fromJson(Map<String, dynamic> json) {
    return PasswordPolicy(
      minLength: json['min_length'] as int? ?? 8,
      requireUppercase: json['require_uppercase'] as bool? ?? true,
      requireLowercase: json['require_lowercase'] as bool? ?? true,
      requireNumbers: json['require_numbers'] as bool? ?? true,
      requireSymbols: json['require_symbols'] as bool? ?? false,
      maxAgeDays: json['max_age_days'] as int? ?? 90,
      historyCount: json['history_count'] as int? ?? 5,
      expiryDays: json['expiry_days'] as int?,
      lockoutThreshold: json['lockout_threshold'] as int?,
      lockoutDurationMinutes: json['lockout_duration_minutes'] as int?,
      requireSpecialChars: json['require_special_chars'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'min_length': minLength,
      'require_uppercase': requireUppercase,
      'require_lowercase': requireLowercase,
      'require_numbers': requireNumbers,
      'require_symbols': requireSymbols,
      'max_age_days': maxAgeDays,
      'history_count': historyCount,
      if (expiryDays != null) 'expiry_days': expiryDays,
      if (lockoutThreshold != null) 'lockout_threshold': lockoutThreshold,
      if (lockoutDurationMinutes != null) 'lockout_duration_minutes': lockoutDurationMinutes,
      if (requireSpecialChars != null) 'require_special_chars': requireSpecialChars,
    };
  }

  PasswordPolicy copyWith({
    int? minLength,
    bool? requireUppercase,
    bool? requireLowercase,
    bool? requireNumbers,
    bool? requireSymbols,
    int? maxAgeDays,
    int? historyCount,
    int? expiryDays,
    int? lockoutThreshold,
    int? lockoutDurationMinutes,
    bool? requireSpecialChars,
  }) {
    return PasswordPolicy(
      minLength: minLength ?? this.minLength,
      requireUppercase: requireUppercase ?? this.requireUppercase,
      requireLowercase: requireLowercase ?? this.requireLowercase,
      requireNumbers: requireNumbers ?? this.requireNumbers,
      requireSymbols: requireSymbols ?? this.requireSymbols,
      maxAgeDays: maxAgeDays ?? this.maxAgeDays,
      historyCount: historyCount ?? this.historyCount,
      expiryDays: expiryDays ?? this.expiryDays,
      lockoutThreshold: lockoutThreshold ?? this.lockoutThreshold,
      lockoutDurationMinutes: lockoutDurationMinutes ?? this.lockoutDurationMinutes,
      requireSpecialChars: requireSpecialChars ?? this.requireSpecialChars,
    );
  }

  static PasswordPolicy get defaultPolicy => const PasswordPolicy(
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSymbols: false,
        maxAgeDays: 90,
        historyCount: 5,
      );

  @override
  List<Object?> get props => [
        minLength,
        requireUppercase,
        requireLowercase,
        requireNumbers,
        requireSymbols,
        maxAgeDays,
        historyCount,
        expiryDays,
        lockoutThreshold,
        lockoutDurationMinutes,
        requireSpecialChars,
      ];
}

/// MFA policy configuration
class MFAPolicy extends Equatable {
  final bool required;
  final bool requiredForAdmins;
  final List<String> allowedMethods;
  final int gracePeriodDays;
  final int? backupCodesCount;
  final int? trustDeviceDays;
  final List<String>? requiredMethods;

  const MFAPolicy({
    required this.required,
    required this.requiredForAdmins,
    required this.allowedMethods,
    required this.gracePeriodDays,
    this.backupCodesCount,
    this.trustDeviceDays,
    this.requiredMethods,
  });

  factory MFAPolicy.fromJson(Map<String, dynamic> json) {
    return MFAPolicy(
      required: json['required'] as bool? ?? false,
      requiredForAdmins: json['required_for_admins'] as bool? ?? true,
      allowedMethods: json['allowed_methods'] != null
          ? List<String>.from(json['allowed_methods'] as List)
          : ['totp', 'sms', 'backup_codes'],
      gracePeriodDays: json['grace_period_days'] as int? ?? 7,
      backupCodesCount: json['backup_codes_count'] as int?,
      trustDeviceDays: json['trust_device_days'] as int?,
      requiredMethods: json['required_methods'] != null
          ? List<String>.from(json['required_methods'] as List)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'required': required,
      'required_for_admins': requiredForAdmins,
      'allowed_methods': allowedMethods,
      'grace_period_days': gracePeriodDays,
    };
  }

  MFAPolicy copyWith({
    bool? required,
    bool? requiredForAdmins,
    List<String>? allowedMethods,
    int? gracePeriodDays,
    List<String>? requiredMethods,
    int? backupCodesCount,
    int? trustDeviceDays,
  }) {
    return MFAPolicy(
      required: required ?? this.required,
      requiredForAdmins: requiredForAdmins ?? this.requiredForAdmins,
      allowedMethods: allowedMethods ?? this.allowedMethods,
      gracePeriodDays: gracePeriodDays ?? this.gracePeriodDays,
    );
  }

  static MFAPolicy get defaultPolicy => const MFAPolicy(
        required: false,
        requiredForAdmins: true,
        allowedMethods: ['totp', 'sms', 'backup_codes'],
        gracePeriodDays: 7,
      );

  @override
  List<Object?> get props => [
        required,
        requiredForAdmins,
        allowedMethods,
        gracePeriodDays,
      ];
}

/// Session policy configuration
class SessionPolicy extends Equatable {
  final int idleTimeoutMinutes;
  final int absoluteTimeoutHours;
  final int concurrentSessionsLimit;
  final bool requireDeviceTrust;
  final int? timeoutMinutes; // Alias for idleTimeoutMinutes
  final int? maxConcurrentSessions; // Alias for concurrentSessionsLimit

  const SessionPolicy({
    required this.idleTimeoutMinutes,
    required this.absoluteTimeoutHours,
    required this.concurrentSessionsLimit,
    required this.requireDeviceTrust,
    this.timeoutMinutes,
    this.maxConcurrentSessions,
  });

  factory SessionPolicy.fromJson(Map<String, dynamic> json) {
    return SessionPolicy(
      idleTimeoutMinutes: json['idle_timeout_minutes'] as int? ?? 30,
      absoluteTimeoutHours: json['absolute_timeout_hours'] as int? ?? 8,
      concurrentSessionsLimit: json['concurrent_sessions_limit'] as int? ?? 5,
      requireDeviceTrust: json['require_device_trust'] as bool? ?? false,
      timeoutMinutes: json['timeout_minutes'] as int?,
      maxConcurrentSessions: json['max_concurrent_sessions'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'idle_timeout_minutes': idleTimeoutMinutes,
      'absolute_timeout_hours': absoluteTimeoutHours,
      'concurrent_sessions_limit': concurrentSessionsLimit,
      'require_device_trust': requireDeviceTrust,
    };
  }

  static SessionPolicy get defaultPolicy => const SessionPolicy(
        idleTimeoutMinutes: 30,
        absoluteTimeoutHours: 8,
        concurrentSessionsLimit: 5,
        requireDeviceTrust: false,
      );

  SessionPolicy copyWith({
    int? idleTimeoutMinutes,
    int? absoluteTimeoutHours,
    int? concurrentSessionsLimit,
    bool? requireDeviceTrust,
    int? timeoutMinutes,
    int? maxConcurrentSessions,
  }) {
    return SessionPolicy(
      idleTimeoutMinutes: idleTimeoutMinutes ?? this.idleTimeoutMinutes,
      absoluteTimeoutHours: absoluteTimeoutHours ?? this.absoluteTimeoutHours,
      concurrentSessionsLimit: concurrentSessionsLimit ?? this.concurrentSessionsLimit,
      requireDeviceTrust: requireDeviceTrust ?? this.requireDeviceTrust,
      timeoutMinutes: timeoutMinutes ?? this.timeoutMinutes,
      maxConcurrentSessions: maxConcurrentSessions ?? this.maxConcurrentSessions,
    );
  }

  @override
  List<Object?> get props => [
        idleTimeoutMinutes,
        absoluteTimeoutHours,
        concurrentSessionsLimit,
        requireDeviceTrust,
      ];
}

/// Access policy configuration
class AccessPolicy extends Equatable {
  final bool ipWhitelistEnabled;
  final List<String> ipWhitelist;
  final bool geoRestrictionsEnabled;
  final List<String> allowedCountries;
  final int loginAttemptLimit;
  final int lockoutDurationMinutes;
  final List<String>? allowedIPs; // Alias for ipWhitelist
  final List<String>? blockedIPs;
  final List<String>? blockedCountries;
  final Map<String, dynamic>? timeRestrictions;

  const AccessPolicy({
    required this.ipWhitelistEnabled,
    required this.ipWhitelist,
    required this.geoRestrictionsEnabled,
    required this.allowedCountries,
    required this.loginAttemptLimit,
    required this.lockoutDurationMinutes,
    this.allowedIPs,
    this.blockedIPs,
    this.blockedCountries,
    this.timeRestrictions,
  });

  factory AccessPolicy.fromJson(Map<String, dynamic> json) {
    return AccessPolicy(
      ipWhitelistEnabled: json['ip_whitelist_enabled'] as bool? ?? false,
      ipWhitelist: json['ip_whitelist'] != null
          ? List<String>.from(json['ip_whitelist'] as List)
          : [],
      geoRestrictionsEnabled: json['geo_restrictions_enabled'] as bool? ?? false,
      allowedCountries: json['allowed_countries'] != null
          ? List<String>.from(json['allowed_countries'] as List)
          : [],
      loginAttemptLimit: json['login_attempt_limit'] as int? ?? 5,
      lockoutDurationMinutes: json['lockout_duration_minutes'] as int? ?? 15,
      allowedIPs: json['allowed_ips'] != null
          ? List<String>.from(json['allowed_ips'] as List)
          : null,
      blockedIPs: json['blocked_ips'] != null
          ? List<String>.from(json['blocked_ips'] as List)
          : null,
      blockedCountries: json['blocked_countries'] != null
          ? List<String>.from(json['blocked_countries'] as List)
          : null,
      timeRestrictions: json['time_restrictions'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ip_whitelist_enabled': ipWhitelistEnabled,
      'ip_whitelist': ipWhitelist,
      'geo_restrictions_enabled': geoRestrictionsEnabled,
      'allowed_countries': allowedCountries,
      'login_attempt_limit': loginAttemptLimit,
      'lockout_duration_minutes': lockoutDurationMinutes,
    };
  }

  static AccessPolicy get defaultPolicy => const AccessPolicy(
        ipWhitelistEnabled: false,
        ipWhitelist: [],
        geoRestrictionsEnabled: false,
        allowedCountries: [],
        loginAttemptLimit: 5,
        lockoutDurationMinutes: 15,
      );

  AccessPolicy copyWith({
    bool? ipWhitelistEnabled,
    List<String>? ipWhitelist,
    bool? geoRestrictionsEnabled,
    List<String>? allowedCountries,
    int? loginAttemptLimit,
    int? lockoutDurationMinutes,
    List<String>? allowedIPs,
    List<String>? blockedIPs,
    List<String>? blockedCountries,
    Map<String, dynamic>? timeRestrictions,
  }) {
    return AccessPolicy(
      ipWhitelistEnabled: ipWhitelistEnabled ?? this.ipWhitelistEnabled,
      ipWhitelist: ipWhitelist ?? this.ipWhitelist,
      geoRestrictionsEnabled: geoRestrictionsEnabled ?? this.geoRestrictionsEnabled,
      allowedCountries: allowedCountries ?? this.allowedCountries,
      loginAttemptLimit: loginAttemptLimit ?? this.loginAttemptLimit,
      lockoutDurationMinutes: lockoutDurationMinutes ?? this.lockoutDurationMinutes,
      allowedIPs: allowedIPs ?? this.allowedIPs,
      blockedIPs: blockedIPs ?? this.blockedIPs,
      blockedCountries: blockedCountries ?? this.blockedCountries,
      timeRestrictions: timeRestrictions ?? this.timeRestrictions,
    );
  }

  @override
  List<Object?> get props => [
        ipWhitelistEnabled,
        ipWhitelist,
        geoRestrictionsEnabled,
        allowedCountries,
        loginAttemptLimit,
        lockoutDurationMinutes,
      ];
}

/// Audit policy configuration
class AuditPolicy extends Equatable {
  final int retentionDays;
  final bool logAllActions;
  final bool alertOnSuspicious;
  final bool exportEnabled;
  final List<String>? loggedEvents;
  final List<String>? complianceStandards;

  const AuditPolicy({
    required this.retentionDays,
    required this.logAllActions,
    required this.alertOnSuspicious,
    required this.exportEnabled,
    this.loggedEvents,
    this.complianceStandards,
  });

  factory AuditPolicy.fromJson(Map<String, dynamic> json) {
    return AuditPolicy(
      retentionDays: json['retention_days'] as int? ?? 90,
      logAllActions: json['log_all_actions'] as bool? ?? true,
      alertOnSuspicious: json['alert_on_suspicious'] as bool? ?? true,
      exportEnabled: json['export_enabled'] as bool? ?? true,
      loggedEvents: json['logged_events'] != null 
          ? List<String>.from(json['logged_events'] as List)
          : null,
      complianceStandards: json['compliance_standards'] != null
          ? List<String>.from(json['compliance_standards'] as List)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'retention_days': retentionDays,
      'log_all_actions': logAllActions,
      'alert_on_suspicious': alertOnSuspicious,
      'export_enabled': exportEnabled,
      'logged_events': loggedEvents,
      'compliance_standards': complianceStandards,
    };
  }

  static AuditPolicy get defaultPolicy => const AuditPolicy(
        retentionDays: 90,
        logAllActions: true,
        alertOnSuspicious: true,
        exportEnabled: true,
        loggedEvents: null,
        complianceStandards: null,
      );

  AuditPolicy copyWith({
    int? retentionDays,
    bool? logAllActions,
    bool? alertOnSuspicious,
    bool? exportEnabled,
    List<String>? loggedEvents,
    List<String>? complianceStandards,
  }) {
    return AuditPolicy(
      retentionDays: retentionDays ?? this.retentionDays,
      logAllActions: logAllActions ?? this.logAllActions,
      alertOnSuspicious: alertOnSuspicious ?? this.alertOnSuspicious,
      exportEnabled: exportEnabled ?? this.exportEnabled,
      loggedEvents: loggedEvents ?? this.loggedEvents,
      complianceStandards: complianceStandards ?? this.complianceStandards,
    );
  }

  @override
  List<Object?> get props => [
        retentionDays,
        logAllActions,
        alertOnSuspicious,
        exportEnabled,
        loggedEvents,
        complianceStandards,
      ];
}

/// Organization Security Policy model for database table: organization_security_policies
class OrganizationSecurityPolicy extends Equatable {
  /// Unique policy identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Password policy
  final PasswordPolicy passwordPolicy;

  /// MFA policy
  final MFAPolicy mfaPolicy;

  /// Session policy
  final SessionPolicy sessionPolicy;

  /// Access policy
  final AccessPolicy accessPolicy;

  /// Audit policy
  final AuditPolicy auditPolicy;

  /// Compliance settings
  final Map<String, dynamic> complianceSettings;

  /// Whether policy is active
  final bool isActive;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Created by user ID
  final String? createdBy;

  const OrganizationSecurityPolicy({
    required this.id,
    required this.organizationId,
    required this.passwordPolicy,
    required this.mfaPolicy,
    required this.sessionPolicy,
    required this.accessPolicy,
    required this.auditPolicy,
    required this.complianceSettings,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  /// Create empty OrganizationSecurityPolicy for testing
  factory OrganizationSecurityPolicy.empty() {
    return OrganizationSecurityPolicy(
      id: '',
      organizationId: '',
      passwordPolicy: PasswordPolicy.defaultPolicy,
      mfaPolicy: MFAPolicy.defaultPolicy,
      sessionPolicy: SessionPolicy.defaultPolicy,
      accessPolicy: AccessPolicy.defaultPolicy,
      auditPolicy: AuditPolicy.defaultPolicy,
      complianceSettings: {},
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create OrganizationSecurityPolicy from JSON
  factory OrganizationSecurityPolicy.fromJson(Map<String, dynamic> json) {
    return OrganizationSecurityPolicy(
      id: json['id'] as String,
      organizationId: json['organization_id'] as String,
      passwordPolicy: json['password_policy'] != null
          ? PasswordPolicy.fromJson(json['password_policy'] as Map<String, dynamic>)
          : PasswordPolicy.defaultPolicy,
      mfaPolicy: json['mfa_policy'] != null
          ? MFAPolicy.fromJson(json['mfa_policy'] as Map<String, dynamic>)
          : MFAPolicy.defaultPolicy,
      sessionPolicy: json['session_policy'] != null
          ? SessionPolicy.fromJson(json['session_policy'] as Map<String, dynamic>)
          : SessionPolicy.defaultPolicy,
      accessPolicy: json['access_policy'] != null
          ? AccessPolicy.fromJson(json['access_policy'] as Map<String, dynamic>)
          : AccessPolicy.defaultPolicy,
      auditPolicy: json['audit_policy'] != null
          ? AuditPolicy.fromJson(json['audit_policy'] as Map<String, dynamic>)
          : AuditPolicy.defaultPolicy,
      complianceSettings: json['compliance_settings'] != null
          ? Map<String, dynamic>.from(json['compliance_settings'] as Map)
          : {},
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String?,
    );
  }

  /// Convert OrganizationSecurityPolicy to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organization_id': organizationId,
      'password_policy': passwordPolicy.toJson(),
      'mfa_policy': mfaPolicy.toJson(),
      'session_policy': sessionPolicy.toJson(),
      'access_policy': accessPolicy.toJson(),
      'audit_policy': auditPolicy.toJson(),
      'compliance_settings': complianceSettings,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  /// Create a copy with updated fields
  OrganizationSecurityPolicy copyWith({
    String? id,
    String? organizationId,
    PasswordPolicy? passwordPolicy,
    MFAPolicy? mfaPolicy,
    SessionPolicy? sessionPolicy,
    AccessPolicy? accessPolicy,
    AuditPolicy? auditPolicy,
    Map<String, dynamic>? complianceSettings,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return OrganizationSecurityPolicy(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      passwordPolicy: passwordPolicy ?? this.passwordPolicy,
      mfaPolicy: mfaPolicy ?? this.mfaPolicy,
      sessionPolicy: sessionPolicy ?? this.sessionPolicy,
      accessPolicy: accessPolicy ?? this.accessPolicy,
      auditPolicy: auditPolicy ?? this.auditPolicy,
      complianceSettings: complianceSettings ?? this.complianceSettings,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  /// Check if MFA is required for user role
  bool isMFARequired(String userRole) {
    if (mfaPolicy.required) return true;
    if (mfaPolicy.requiredForAdmins && (userRole == 'admin' || userRole == 'owner')) {
      return true;
    }
    return false;
  }

  /// Check if IP is whitelisted
  bool isIPAllowed(String ipAddress) {
    if (!accessPolicy.ipWhitelistEnabled) return true;
    return accessPolicy.ipWhitelist.contains(ipAddress);
  }

  /// Check if country is allowed
  bool isCountryAllowed(String countryCode) {
    if (!accessPolicy.geoRestrictionsEnabled) return true;
    return accessPolicy.allowedCountries.contains(countryCode);
  }

  /// Get session timeout in milliseconds
  int get sessionTimeoutMs => sessionPolicy.idleTimeoutMinutes * 60 * 1000;

  /// Get absolute session timeout in milliseconds
  int get absoluteSessionTimeoutMs => sessionPolicy.absoluteTimeoutHours * 60 * 60 * 1000;

  @override
  List<Object?> get props => [
        id,
        organizationId,
        passwordPolicy,
        mfaPolicy,
        sessionPolicy,
        accessPolicy,
        auditPolicy,
        complianceSettings,
        isActive,
        createdAt,
        updatedAt,
        createdBy,
      ];

  @override
  bool get stringify => true;
}