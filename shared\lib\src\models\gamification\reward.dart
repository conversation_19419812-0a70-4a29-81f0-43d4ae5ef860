import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'reward.g.dart';

/// Reward types from CLAUDE.md comprehensive reward system
enum RewardType {
  @JsonValue('virtual')
  virtual, // Virtual items like badges, titles, themes
  @JsonValue('privilege')
  privilege, // Special privileges or features
  @JsonValue('cosmetic')
  cosmetic, // Cosmetic customizations
  @JsonValue('functionality')
  functionality, // Additional functionality access
  @JsonValue('recognition')
  recognition, // Public recognition rewards
}

/// Reward rarity levels matching achievement system
enum RewardRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

/// How the reward is obtained
enum RewardUnlockMethod {
  @JsonValue('achievement')
  achievement, // Unlocked by getting an achievement
  @JsonValue('points')
  points, // Purchased with points
  @JsonValue('level')
  level, // Unlocked at certain level
  @JsonValue('quest')
  quest, // Reward for completing specific quest
  @JsonValue('streak')
  streak, // Unlocked through streaks
  @JsonValue('special_event')
  specialEvent, // Limited time events
}

/// Reward model with comprehensive gamification features
@JsonSerializable()
class Reward extends Equatable {
  /// Unique reward identifier
  final String id;

  /// Reward title/name
  final String title;

  /// Reward description
  final String description;

  /// Reward type category
  final RewardType type;

  /// Reward rarity level
  final RewardRarity rarity;

  /// Icon identifier or URL
  final String icon;

  /// Cost in points (if purchasable, 0 if free)
  final int pointsCost;

  /// How this reward is unlocked
  final RewardUnlockMethod unlockMethod;

  /// Required value to unlock (achievement ID, points, level, etc.)
  final String? unlockRequirement;

  /// Whether this reward is currently available
  final bool isAvailable;

  /// Whether this is a limited time reward
  final bool isLimited;

  /// Whether this reward can be equipped/used multiple times
  final bool isReusable;

  /// Maximum number of times this reward can be obtained (null = unlimited)
  final int? maxQuantity;

  /// Reward effect data (JSON for flexible reward effects)
  final Map<String, dynamic>? effectData;

  /// Start date for limited rewards
  final DateTime? availableFrom;

  /// End date for limited rewards
  final DateTime? availableUntil;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const Reward({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.rarity,
    required this.icon,
    required this.pointsCost,
    required this.unlockMethod,
    this.unlockRequirement,
    required this.isAvailable,
    required this.isLimited,
    required this.isReusable,
    this.maxQuantity,
    this.effectData,
    this.availableFrom,
    this.availableUntil,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Reward from JSON
  factory Reward.fromJson(Map<String, dynamic> json) => _$RewardFromJson(json);

  /// Convert Reward to JSON
  Map<String, dynamic> toJson() => _$RewardToJson(this);

  /// Check if reward is currently available (considering time limits)
  bool get isCurrentlyAvailable {
    if (!isAvailable) return false;
    
    final now = DateTime.now();
    
    if (availableFrom != null && now.isBefore(availableFrom!)) {
      return false;
    }
    
    if (availableUntil != null && now.isAfter(availableUntil!)) {
      return false;
    }
    
    return true;
  }

  /// Check if reward is free (no points cost)
  bool get isFree => pointsCost == 0;

  /// Get rarity multiplier for value calculation
  double get rarityMultiplier {
    switch (rarity) {
      case RewardRarity.common:
        return 1.0;
      case RewardRarity.uncommon:
        return 1.5;
      case RewardRarity.rare:
        return 2.0;
      case RewardRarity.epic:
        return 3.0;
      case RewardRarity.legendary:
        return 5.0;
    }
  }

  /// Get reward value (for display purposes)
  int get value => (pointsCost * rarityMultiplier).round();

  /// Get type display name
  String get typeDisplayName {
    switch (type) {
      case RewardType.virtual:
        return 'Virtual Item';
      case RewardType.privilege:
        return 'Privilege';
      case RewardType.cosmetic:
        return 'Cosmetic';
      case RewardType.functionality:
        return 'Feature Access';
      case RewardType.recognition:
        return 'Recognition';
    }
  }

  /// Get rarity display name
  String get rarityDisplayName {
    switch (rarity) {
      case RewardRarity.common:
        return 'Common';
      case RewardRarity.uncommon:
        return 'Uncommon';
      case RewardRarity.rare:
        return 'Rare';
      case RewardRarity.epic:
        return 'Epic';
      case RewardRarity.legendary:
        return 'Legendary';
    }
  }

  /// Get unlock method display name
  String get unlockMethodDisplayName {
    switch (unlockMethod) {
      case RewardUnlockMethod.achievement:
        return 'Achievement Unlock';
      case RewardUnlockMethod.points:
        return 'Purchase with Points';
      case RewardUnlockMethod.level:
        return 'Level Unlock';
      case RewardUnlockMethod.quest:
        return 'Quest Reward';
      case RewardUnlockMethod.streak:
        return 'Streak Reward';
      case RewardUnlockMethod.specialEvent:
        return 'Special Event';
    }
  }

  /// Get days until reward expires (null if not limited)
  int? get daysUntilExpiry {
    if (!isLimited || availableUntil == null) return null;
    final now = DateTime.now();
    return availableUntil!.difference(now).inDays;
  }

  /// Get hours until reward expires
  int? get hoursUntilExpiry {
    if (!isLimited || availableUntil == null) return null;
    final now = DateTime.now();
    return availableUntil!.difference(now).inHours;
  }

  /// Check if reward expires soon (within 24 hours)
  bool get expiresSoon {
    if (!isLimited || availableUntil == null) return false;
    final now = DateTime.now();
    return availableUntil!.difference(now).inHours <= 24;
  }

  /// Check if user can obtain this reward
  bool canUnlock({
    required int userPoints,
    required int userLevel,
    required List<String> userAchievements,
    int? userStreak,
  }) {
    if (!isCurrentlyAvailable) return false;
    
    switch (unlockMethod) {
      case RewardUnlockMethod.points:
        return userPoints >= pointsCost;
      case RewardUnlockMethod.level:
        if (unlockRequirement == null) return false;
        return userLevel >= int.tryParse(unlockRequirement!)!;
      case RewardUnlockMethod.achievement:
        if (unlockRequirement == null) return false;
        return userAchievements.contains(unlockRequirement!);
      case RewardUnlockMethod.streak:
        if (unlockRequirement == null) return false;
        return (userStreak ?? 0) >= int.tryParse(unlockRequirement!)!;
      case RewardUnlockMethod.quest:
      case RewardUnlockMethod.specialEvent:
        // Would require additional context to determine
        return true;
    }
  }

  /// Create a copy with updated fields
  Reward copyWith({
    String? id,
    String? title,
    String? description,
    RewardType? type,
    RewardRarity? rarity,
    String? icon,
    int? pointsCost,
    RewardUnlockMethod? unlockMethod,
    String? unlockRequirement,
    bool? isAvailable,
    bool? isLimited,
    bool? isReusable,
    int? maxQuantity,
    Map<String, dynamic>? effectData,
    DateTime? availableFrom,
    DateTime? availableUntil,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Reward(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      rarity: rarity ?? this.rarity,
      icon: icon ?? this.icon,
      pointsCost: pointsCost ?? this.pointsCost,
      unlockMethod: unlockMethod ?? this.unlockMethod,
      unlockRequirement: unlockRequirement ?? this.unlockRequirement,
      isAvailable: isAvailable ?? this.isAvailable,
      isLimited: isLimited ?? this.isLimited,
      isReusable: isReusable ?? this.isReusable,
      maxQuantity: maxQuantity ?? this.maxQuantity,
      effectData: effectData ?? this.effectData,
      availableFrom: availableFrom ?? this.availableFrom,
      availableUntil: availableUntil ?? this.availableUntil,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create empty reward for initialization
  static Reward empty() {
    final now = DateTime.now();
    return Reward(
      id: '',
      title: '',
      description: '',
      type: RewardType.virtual,
      rarity: RewardRarity.common,
      icon: '',
      pointsCost: 0,
      unlockMethod: RewardUnlockMethod.points,
      isAvailable: true,
      isLimited: false,
      isReusable: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        rarity,
        icon,
        pointsCost,
        unlockMethod,
        unlockRequirement,
        isAvailable,
        isLimited,
        isReusable,
        maxQuantity,
        effectData,
        availableFrom,
        availableUntil,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}