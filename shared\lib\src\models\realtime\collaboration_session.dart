import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'collaboration_session.g.dart';

/// Collaboration session types
enum CollaborationSessionType {
  @JsonValue('quest_editing')
  questEditing,
  @JsonValue('task_editing')
  taskEditing,
  @JsonValue('document_editing')
  documentEditing,
  @JsonValue('whiteboard')
  whiteboard,
  @JsonValue('brainstorming')
  brainstorming,
  @JsonValue('code_review')
  codeReview,
  @JsonValue('meeting')
  meeting,
}

/// Collaboration session status
enum SessionStatus {
  @JsonValue('active')
  active,
  @JsonValue('paused')
  paused,
  @JsonValue('ended')
  ended,
}

/// Participant role in collaboration session
enum ParticipantRole {
  @JsonValue('owner')
  owner,
  @JsonValue('editor')
  editor,
  @JsonValue('viewer')
  viewer,
  @<PERSON>sonValue('commentator')
  commentator,
}

/// Collaboration session participant
@JsonSerializable()
class SessionParticipant extends Equatable {
  /// User ID
  final String userId;

  /// User display name
  final String displayName;

  /// Avatar URL
  final String? avatarUrl;

  /// Participant role
  final ParticipantRole role;

  /// Join timestamp
  final DateTime joinedAt;

  /// Last activity timestamp
  final DateTime? lastActivity;

  /// Whether participant is currently active
  final bool isActive;

  /// Participant cursor position (for editing sessions)
  final Map<String, dynamic>? cursorPosition;

  /// Participant selections (for editing sessions)
  final Map<String, dynamic>? selections;

  const SessionParticipant({
    required this.userId,
    required this.displayName,
    this.avatarUrl,
    required this.role,
    required this.joinedAt,
    this.lastActivity,
    this.isActive = true,
    this.cursorPosition,
    this.selections,
  });

  /// Create SessionParticipant from JSON
  factory SessionParticipant.fromJson(Map<String, dynamic> json) => _$SessionParticipantFromJson(json);

  /// Convert SessionParticipant to JSON
  Map<String, dynamic> toJson() => _$SessionParticipantToJson(this);

  @override
  List<Object?> get props => [
        userId,
        displayName,
        avatarUrl,
        role,
        joinedAt,
        lastActivity,
        isActive,
        cursorPosition,
        selections,
      ];
}

/// Real-time collaboration session model
@JsonSerializable()
class CollaborationSession extends Equatable {
  /// Session ID
  final String id;

  /// Session type
  final CollaborationSessionType type;

  /// Session title
  final String title;

  /// Session description
  final String? description;

  /// Resource being collaborated on (quest ID, task ID, document ID, etc.)
  final String resourceId;

  /// Resource type
  final String resourceType;

  /// Session owner user ID
  final String ownerId;

  /// Organization ID (if applicable)
  final String? organizationId;

  /// Session participants
  final List<SessionParticipant> participants;

  /// Session status
  final SessionStatus status;

  /// Session creation timestamp
  final DateTime createdAt;

  /// Session start timestamp
  final DateTime? startedAt;

  /// Session end timestamp
  final DateTime? endedAt;

  /// Session settings
  final Map<String, dynamic> settings;

  /// Temporary session data (for real-time sync)
  final Map<String, dynamic>? sessionData;

  /// Maximum number of participants allowed
  final int? maxParticipants;

  /// Whether session requires invitation
  final bool requiresInvitation;

  /// Whether session allows anonymous participants
  final bool allowAnonymous;

  /// Session recording information (if applicable)
  final Map<String, dynamic>? recordingInfo;

  const CollaborationSession({
    required this.id,
    required this.type,
    required this.title,
    this.description,
    required this.resourceId,
    required this.resourceType,
    required this.ownerId,
    this.organizationId,
    this.participants = const [],
    this.status = SessionStatus.active,
    required this.createdAt,
    this.startedAt,
    this.endedAt,
    this.settings = const {},
    this.sessionData,
    this.maxParticipants,
    this.requiresInvitation = true,
    this.allowAnonymous = false,
    this.recordingInfo,
  });

  /// Create CollaborationSession from JSON
  factory CollaborationSession.fromJson(Map<String, dynamic> json) => _$CollaborationSessionFromJson(json);

  /// Convert CollaborationSession to JSON
  Map<String, dynamic> toJson() => _$CollaborationSessionToJson(this);

  /// Create a quest editing session
  factory CollaborationSession.questEditing({
    required String questId,
    required String ownerId,
    required String title,
    String? description,
    String? organizationId,
    Map<String, dynamic>? settings,
  }) {
    return CollaborationSession(
      id: 'quest_collab_${DateTime.now().millisecondsSinceEpoch}',
      type: CollaborationSessionType.questEditing,
      title: title,
      description: description,
      resourceId: questId,
      resourceType: 'quest',
      ownerId: ownerId,
      organizationId: organizationId,
      createdAt: DateTime.now(),
      startedAt: DateTime.now(),
      settings: settings ?? {},
    );
  }

  /// Create a meeting session
  factory CollaborationSession.meeting({
    required String ownerId,
    required String title,
    String? description,
    String? organizationId,
    int? maxParticipants,
    Map<String, dynamic>? settings,
  }) {
    return CollaborationSession(
      id: 'meeting_${DateTime.now().millisecondsSinceEpoch}',
      type: CollaborationSessionType.meeting,
      title: title,
      description: description,
      resourceId: 'meeting_${DateTime.now().millisecondsSinceEpoch}',
      resourceType: 'meeting',
      ownerId: ownerId,
      organizationId: organizationId,
      createdAt: DateTime.now(),
      maxParticipants: maxParticipants,
      settings: settings ?? {},
    );
  }

  /// Get active participants
  List<SessionParticipant> get activeParticipants {
    return participants.where((p) => p.isActive).toList();
  }

  /// Get participant by user ID
  SessionParticipant? getParticipant(String userId) {
    try {
      return participants.firstWhere((p) => p.userId == userId);
    } catch (e) {
      return null;
    }
  }

  /// Check if user is participant
  bool hasParticipant(String userId) {
    return participants.any((p) => p.userId == userId);
  }

  /// Check if session is full
  bool get isFull {
    if (maxParticipants == null) return false;
    return activeParticipants.length >= maxParticipants!;
  }

  /// Get session duration
  Duration? get duration {
    if (startedAt == null) return null;
    final endTime = endedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }

  /// Add participant to session
  CollaborationSession addParticipant(SessionParticipant participant) {
    if (hasParticipant(participant.userId)) return this;
    
    return CollaborationSession(
      id: id,
      type: type,
      title: title,
      description: description,
      resourceId: resourceId,
      resourceType: resourceType,
      ownerId: ownerId,
      organizationId: organizationId,
      participants: [...participants, participant],
      status: status,
      createdAt: createdAt,
      startedAt: startedAt,
      endedAt: endedAt,
      settings: settings,
      sessionData: sessionData,
      maxParticipants: maxParticipants,
      requiresInvitation: requiresInvitation,
      allowAnonymous: allowAnonymous,
      recordingInfo: recordingInfo,
    );
  }

  /// Remove participant from session
  CollaborationSession removeParticipant(String userId) {
    return CollaborationSession(
      id: id,
      type: type,
      title: title,
      description: description,
      resourceId: resourceId,
      resourceType: resourceType,
      ownerId: ownerId,
      organizationId: organizationId,
      participants: participants.where((p) => p.userId != userId).toList(),
      status: status,
      createdAt: createdAt,
      startedAt: startedAt,
      endedAt: endedAt,
      settings: settings,
      sessionData: sessionData,
      maxParticipants: maxParticipants,
      requiresInvitation: requiresInvitation,
      allowAnonymous: allowAnonymous,
      recordingInfo: recordingInfo,
    );
  }

  /// Update session status
  CollaborationSession updateStatus(SessionStatus newStatus) {
    return CollaborationSession(
      id: id,
      type: type,
      title: title,
      description: description,
      resourceId: resourceId,
      resourceType: resourceType,
      ownerId: ownerId,
      organizationId: organizationId,
      participants: participants,
      status: newStatus,
      createdAt: createdAt,
      startedAt: startedAt,
      endedAt: newStatus == SessionStatus.ended ? DateTime.now() : endedAt,
      settings: settings,
      sessionData: sessionData,
      maxParticipants: maxParticipants,
      requiresInvitation: requiresInvitation,
      allowAnonymous: allowAnonymous,
      recordingInfo: recordingInfo,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        title,
        description,
        resourceId,
        resourceType,
        ownerId,
        organizationId,
        participants,
        status,
        createdAt,
        startedAt,
        endedAt,
        settings,
        sessionData,
        maxParticipants,
        requiresInvitation,
        allowAnonymous,
        recordingInfo,
      ];

  @override
  bool get stringify => true;
}
