import 'dart:async';
import 'package:logging/logging.dart';

import 'database_service.dart';
import 'websocket_service.dart';
import 'cache_service.dart';

/// Coordinates data persistence across database, cache, and real-time updates
/// Ensures data consistency and real-time synchronization across all connected clients
class PersistenceCoordinator {
  late final DatabaseService _dbService;
  late final CacheService _cacheService;
  final _logger = Logger('PersistenceCoordinator');
  
  // Event tracking for analytics
  final Map<String, int> _eventCounts = {};
  final List<Map<String, dynamic>> _recentEvents = [];
  
  PersistenceCoordinator(this._dbService, this._cacheService);

  /// Initialize persistence coordinator
  Future<void> initialize() async {
    _logger.info('Initializing persistence coordinator...');
    
    // Set up periodic cleanup
    Timer.periodic(Duration(minutes: 15), (_) => _performMaintenance());
    
    _logger.info('Persistence coordinator initialized');
  }

  // User Management with Real-Time Updates

  /// Create user with real-time notification
  Future<Map<String, dynamic>> createUser(Map<String, dynamic> userData) async {
    try {
      // Create user in database
      final userId = await _dbService.createUser(userData);
      
      // Cache user data
      final userKey = 'user:$userId';
      await _cacheService.setData(userKey, userData, ttl: Duration(hours: 24));
      
      // Broadcast user creation
      WebSocketService.broadcastGlobalActivity({
        'type': 'user_joined',
        'user_id': userId,
        'username': userData['username'],
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      _trackEvent('user_created');
      _logger.info('User created: $userId');
      
      final stats = await _dbService.getUserStats(userId);
      return stats ?? {};
    } catch (e) {
      _logger.severe('Failed to create user: $e');
      rethrow;
    }
  }

  /// Update user with real-time sync
  Future<Map<String, dynamic>> updateUser(String userId, Map<String, dynamic> updates) async {
    try {
      // Update in database
      await _dbService.updateUser(userId, updates);
      
      // Update cache
      final userKey = 'user:$userId';
      await _cacheService.setData(userKey, updates, ttl: Duration(hours: 24));
      
      // Broadcast user update
      WebSocketService.broadcastPresenceUpdate(userId, {
        'status': 'updated',
        'changes': updates.keys.toList(),
      });
      
      _trackEvent('user_updated');
      _logger.info('User updated: $userId');
      
      final stats = await _dbService.getUserStats(userId);
      return stats ?? {};
    } catch (e) {
      _logger.severe('Failed to update user $userId: $e');
      rethrow;
    }
  }

  // Gamification with Real-Time Updates

  /// Award points with real-time notification
  Future<Map<String, dynamic>> awardPoints(String userId, int points, String reason) async {
    try {
      // Award points in database
      final result = await _dbService.awardPoints(userId, points, reason, 'manual');
      
      // Update cached user stats
      final statsKey = 'user:$userId:stats';
      if (result != null) {
        await _cacheService.setData(statsKey, result, ttl: Duration(hours: 1));
      }
      
      // Broadcast points awarded
      if (result != null) {
        WebSocketService.broadcastGlobalActivity({
          'type': 'points_awarded',
          'user_id': userId,
          'points': points,
          'reason': reason,
          'total_points': result['total_points'],
          'level': result['current_level'],
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
      
      // Send notification to user
      WebSocketService.sendNotificationToUser(userId, {
        'title': 'Points Earned!',
        'message': 'You earned $points points for $reason',
        'type': 'points_award',
        'points': points,
      });
      
      _trackEvent('points_awarded');
      _logger.info('Points awarded to $userId: $points ($reason)');
      
      return result ?? {};
    } catch (e) {
      _logger.severe('Failed to award points to $userId: $e');
      rethrow;
    }
  }

  /// Award achievement with celebration
  Future<Map<String, dynamic>> awardAchievement(String userId, String achievementId) async {
    try {
      // Award achievement in database
      final result = await _dbService.awardAchievement(userId, achievementId);
      
      if (result['newly_earned'] == true) {
        // Cache achievement data
        final achievementKey = 'achievement:$achievementId';
        final achievementData = await _dbService.getAchievementDetails(achievementId);
        await _cacheService.setData(achievementKey, achievementData, ttl: Duration(hours: 24));
        
        // Broadcast achievement earned
        WebSocketService.broadcastGlobalActivity({
          'type': 'achievement_earned',
          'user_id': userId,
          'achievement_id': achievementId,
          'achievement_name': achievementData['name'],
          'achievement_rarity': achievementData['rarity'],
          'points_awarded': achievementData['points_reward'],
          'timestamp': DateTime.now().toIso8601String(),
        });
        
        // Send celebration notification
        WebSocketService.sendNotificationToUser(userId, {
          'title': 'Achievement Unlocked! 🏆',
          'message': 'You earned "${achievementData['name']}"',
          'type': 'achievement_earned',
          'achievement': achievementData,
          'celebration': true,
        });
        
        _trackEvent('achievement_awarded');
        _logger.info('Achievement awarded to $userId: $achievementId');
      }
      
      return result;
    } catch (e) {
      _logger.severe('Failed to award achievement to $userId: $e');
      rethrow;
    }
  }

  // Collaboration with Real-Time Sync

  /// Create collaboration room with real-time setup
  Future<Map<String, dynamic>> createCollaborationRoom(Map<String, dynamic> roomData) async {
    try {
      final roomId = await _dbService.createCollaborationRoom(roomData);
      
      // Cache room data
      final roomKey = 'room:$roomId';
      await _cacheService.setData(roomKey, roomData, ttl: Duration(hours: 12));
      
      // Set up real-time collaboration
      WebSocketService.broadcastCollaborationUpdate(roomId, {
        'type': 'room_created',
        'room_id': roomId,
        'created_by': roomData['creator_id'],
        'participants': roomData['participants'] ?? [],
      });
      
      _trackEvent('collaboration_room_created');
      _logger.info('Collaboration room created: $roomId');
      
      return {'room_id': roomId, ...roomData};
    } catch (e) {
      _logger.severe('Failed to create collaboration room: $e');
      rethrow;
    }
  }

  /// Update collaboration document with real-time sync
  Future<void> updateCollaborationDocument(String roomId, String documentId, Map<String, dynamic> changes, String userId) async {
    try {
      // Update in database
      await _dbService.updateCollaborationDocument(documentId, changes);
      
      // Update cache
      final docKey = 'document:$documentId';
      final cachedDoc = await _cacheService.getData(docKey) ?? {};
      cachedDoc.addAll(changes);
      await _cacheService.setData(docKey, cachedDoc, ttl: Duration(hours: 6));
      
      // Broadcast real-time update
      WebSocketService.broadcastCollaborationUpdate(roomId, {
        'type': 'document_updated',
        'document_id': documentId,
        'changes': changes,
        'updated_by': userId,
        'version': DateTime.now().millisecondsSinceEpoch,
      });
      
      _trackEvent('document_updated');
      _logger.info('Document updated: $documentId by $userId');
    } catch (e) {
      _logger.severe('Failed to update collaboration document: $e');
      rethrow;
    }
  }

  // Freelancing with Real-Time Updates

  /// Create freelancing project with notifications
  Future<Map<String, dynamic>> createFreelancingProject(Map<String, dynamic> projectData) async {
    try {
      final projectId = await _dbService.createFreelancingProject(projectData);
      
      // Cache project data
      final projectKey = 'project:$projectId';
      await _cacheService.setData(projectKey, projectData, ttl: Duration(hours: 24));
      
      // Broadcast project creation
      WebSocketService.broadcastFreelancingUpdate(projectId, {
        'type': 'project_created',
        'project_id': projectId,
        'title': projectData['title'],
        'budget': projectData['budget'],
        'client_id': projectData['client_id'],
      });
      
      _trackEvent('freelancing_project_created');
      _logger.info('Freelancing project created: $projectId');
      
      return {'project_id': projectId, ...projectData};
    } catch (e) {
      _logger.severe('Failed to create freelancing project: $e');
      rethrow;
    }
  }

  /// Submit proposal with real-time notification
  Future<Map<String, dynamic>> submitProposal(Map<String, dynamic> proposalData) async {
    try {
      final proposalId = await _dbService.submitProposal(proposalData);
      
      // Cache proposal data
      final proposalKey = 'proposal:$proposalId';
      await _cacheService.setData(proposalKey, proposalData, ttl: Duration(hours: 12));
      
      // Notify client about new proposal
      WebSocketService.broadcastFreelancingUpdate(proposalData['project_id'], {
        'type': 'proposal_submitted',
        'proposal_id': proposalId,
        'freelancer_id': proposalData['freelancer_id'],
        'amount': proposalData['amount'],
      });
      
      _trackEvent('proposal_submitted');
      _logger.info('Proposal submitted: $proposalId');
      
      return {'proposal_id': proposalId, ...proposalData};
    } catch (e) {
      _logger.severe('Failed to submit proposal: $e');
      rethrow;
    }
  }

  // Learning with Progress Tracking

  /// Enroll in course with real-time tracking
  Future<Map<String, dynamic>> enrollInCourse(String userId, String courseId) async {
    try {
      final enrollmentId = await _dbService.enrollInCourse(userId, courseId);
      
      // Cache enrollment data
      final enrollmentKey = 'enrollment:$enrollmentId';
      await _cacheService.setData(enrollmentKey, {
        'user_id': userId,
        'course_id': courseId,
        'enrolled_at': DateTime.now().toIso8601String(),
      }, ttl: Duration(hours: 24));
      
      // Broadcast enrollment
      WebSocketService.broadcastLearningUpdate(courseId, {
        'type': 'student_enrolled',
        'enrollment_id': enrollmentId,
        'user_id': userId,
      });
      
      _trackEvent('course_enrolled');
      _logger.info('User $userId enrolled in course $courseId');
      
      return {'enrollment_id': enrollmentId, 'user_id': userId, 'course_id': courseId};
    } catch (e) {
      _logger.severe('Failed to enroll user $userId in course $courseId: $e');
      rethrow;
    }
  }

  /// Update learning progress with real-time sync
  Future<Map<String, dynamic>> updateLearningProgress(String userId, String courseId, Map<String, dynamic> progress) async {
    try {
      // Update progress in database
      await _dbService.updateLearningProgress(userId, courseId, progress);
      
      // Update cached progress
      final progressKey = 'progress:$userId:$courseId';
      await _cacheService.setData(progressKey, progress, ttl: Duration(hours: 6));
      
      // Broadcast progress update
      WebSocketService.broadcastLearningUpdate(courseId, {
        'type': 'progress_updated',
        'user_id': userId,
        'progress': progress,
      });
      
      // Check for achievements based on progress
      if (progress['completion_percentage'] != null) {
        await _checkLearningAchievements(userId, courseId, progress['completion_percentage']);
      }
      
      _trackEvent('learning_progress_updated');
      _logger.info('Learning progress updated for $userId in course $courseId');
      
      return progress;
    } catch (e) {
      _logger.severe('Failed to update learning progress: $e');
      rethrow;
    }
  }

  // Analytics and Monitoring

  /// Get real-time analytics
  Future<Map<String, dynamic>> getRealtimeAnalytics() async {
    try {
      // Combine database analytics with real-time metrics
      final dbAnalytics = await _dbService.getSystemAnalytics();
      final wsStats = WebSocketService.getConnectionStats();
      
      final analytics = {
        ...dbAnalytics,
        'websocket': wsStats,
        'events': _eventCounts,
        'recent_events': _recentEvents.take(50).toList(),
        'cache_stats': _cacheService.getStats(),
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      // Cache analytics for dashboard
      await _cacheService.setData('analytics:realtime', analytics, ttl: Duration(minutes: 5));
      
      return analytics;
    } catch (e) {
      _logger.severe('Failed to get real-time analytics: $e');
      rethrow;
    }
  }

  // Private helper methods

  /// Track events for analytics
  void _trackEvent(String eventType) {
    _eventCounts[eventType] = (_eventCounts[eventType] ?? 0) + 1;
    
    _recentEvents.insert(0, {
      'type': eventType,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    // Keep only recent events
    if (_recentEvents.length > 1000) {
      _recentEvents.removeRange(500, _recentEvents.length);
    }
  }

  /// Check for learning-based achievements
  Future<void> _checkLearningAchievements(String userId, String courseId, double completionPercentage) async {
    try {
      // Award achievements based on progress milestones
      if (completionPercentage >= 25 && completionPercentage < 50) {
        await awardAchievement(userId, 'first_quarter_complete');
      } else if (completionPercentage >= 50 && completionPercentage < 75) {
        await awardAchievement(userId, 'halfway_hero');
      } else if (completionPercentage >= 75 && completionPercentage < 100) {
        await awardAchievement(userId, 'almost_there');
      } else if (completionPercentage >= 100) {
        await awardAchievement(userId, 'course_complete');
      }
    } catch (e) {
      _logger.warning('Failed to check learning achievements: $e');
    }
  }

  /// Perform periodic maintenance
  Future<void> _performMaintenance() async {
    try {
      _logger.info('Performing periodic maintenance...');
      
      // Cleanup WebSocket connections
      WebSocketService.cleanupInactiveConnections();
      
      // Clear old events
      if (_recentEvents.length > 500) {
        _recentEvents.removeRange(250, _recentEvents.length);
      }
      
      // Reset event counts periodically
      _eventCounts.clear();
      
      _logger.info('Maintenance completed');
    } catch (e) {
      _logger.warning('Maintenance failed: $e');
    }
  }
}