import '../services/migration_service.dart';

class MigrationDefinitions {
  static List<Migration> getAllMigrations() {
    return [
      _v001InitialSetup(),
      _v002_gamificationSchema(),
      _v003_organizationsSchema(),
      _v004_sampleData(),
      _v005_enterpriseSchema(),
      _v006_ssoSecuritySchema(),
      _v007_authenticationSecurity(),
      _v008_backupCodes(),
      _v009_existingTableSecurity(),
      _v010_securityOptimization(),
      _v011_trustedDevices(),
      _v012_databaseEncryption(),
      _v013_mfaSystem(),
      _v014_performanceOptimization(),
      _v015_additionalPerformance(),
      _v016_analyticsSchema(),
      _v017_authEnhancements(),
      _v018_authTokens(),
    ];
  }

  static Migration _v001InitialSetup() {
    return Migration(
      version: '001_initial_setup',
      description: 'Create initial database schema with extensions and health check',
      checksum: Migration.generateChecksum(_v001InitialSql),
      sql: _v001InitialSql,
      rollbackSql: '''
        DROP TABLE IF EXISTS quester.health_check;
        DROP SCHEMA IF EXISTS quester CASCADE;
        DROP EXTENSION IF EXISTS "pgcrypto";
        DROP EXTENSION IF EXISTS "uuid-ossp";
      ''',
    );
  }

  static Migration _v002_gamificationSchema() {
    return Migration(
      version: '002_gamification_schema',
      description: 'Create comprehensive gamification system tables and enums',
      checksum: Migration.generateChecksum(_v002GamificationSql),
      sql: _v002GamificationSql,
      rollbackSql: '''
        DROP TRIGGER IF EXISTS update_user_points_updated_at ON quester.user_points;
        DROP TRIGGER IF EXISTS update_users_updated_at ON quester.users;
        DROP FUNCTION IF EXISTS quester.update_updated_at_column();
        DROP TABLE IF EXISTS quester.activity_log;
        DROP TABLE IF EXISTS quester.leaderboards;
        DROP TABLE IF EXISTS quester.streaks;
        DROP TABLE IF EXISTS quester.user_rewards;
        DROP TABLE IF EXISTS quester.rewards;
        DROP TABLE IF EXISTS quester.user_achievements;
        DROP TABLE IF EXISTS quester.achievements;
        DROP TABLE IF EXISTS quester.user_points;
        DROP TABLE IF EXISTS quester.users;
        DROP TYPE IF EXISTS leaderboard_type;
        DROP TYPE IF EXISTS activity_type;
        DROP TYPE IF EXISTS reward_type;
        DROP TYPE IF EXISTS achievement_rarity;
        DROP TYPE IF EXISTS achievement_category;
        DROP TYPE IF EXISTS user_role;
      ''',
    );
  }

  static Migration _v003_organizationsSchema() {
    return Migration(
      version: '003_organizations_schema',
      description: 'Create organizations and team management schema',
      checksum: Migration.generateChecksum(_v003OrganizationsSql),
      sql: _v003OrganizationsSql,
    );
  }

  static Migration _v004_sampleData() {
    return Migration(
      version: '004_sample_data',
      description: 'Insert sample achievements, rewards, and test data',
      checksum: Migration.generateChecksum(_v004SampleDataSql),
      sql: _v004SampleDataSql,
    );
  }

  static Migration _v005_enterpriseSchema() {
    return Migration(
      version: '005_enterprise_schema',
      description: 'Create enterprise features schema',
      checksum: Migration.generateChecksum(_v005EnterpriseSql),
      sql: _v005EnterpriseSql,
    );
  }

  static Migration _v006_ssoSecuritySchema() {
    return Migration(
      version: '006_sso_security_schema',
      description: 'Add SSO and security features',
      checksum: Migration.generateChecksum(_v006SsoSecuritySql),
      sql: _v006SsoSecuritySql,
    );
  }

  static Migration _v007_authenticationSecurity() {
    return Migration(
      version: '007_authentication_security',
      description: 'Enhanced authentication security features',
      checksum: Migration.generateChecksum(_v007AuthSecuritySql),
      sql: _v007AuthSecuritySql,
    );
  }

  static Migration _v008_backupCodes() {
    return Migration(
      version: '008_backup_codes',
      description: 'Add backup codes system for MFA',
      checksum: Migration.generateChecksum(_v008BackupCodesSql),
      sql: _v008BackupCodesSql,
    );
  }

  static Migration _v009_existingTableSecurity() {
    return Migration(
      version: '009_existing_table_security',
      description: 'Security enhancements for existing tables',
      checksum: Migration.generateChecksum(_v009ExistingTableSecuritySql),
      sql: _v009ExistingTableSecuritySql,
    );
  }

  static Migration _v010_securityOptimization() {
    return Migration(
      version: '010_security_optimization',
      description: 'Security query optimization indexes',
      checksum: Migration.generateChecksum(_v010SecurityOptimizationSql),
      sql: _v010SecurityOptimizationSql,
    );
  }

  static Migration _v011_trustedDevices() {
    return Migration(
      version: '011_trusted_devices',
      description: 'Trusted devices schema for enhanced security',
      checksum: Migration.generateChecksum(_v011TrustedDevicesSql),
      sql: _v011TrustedDevicesSql,
    );
  }

  static Migration _v012_databaseEncryption() {
    return Migration(
      version: '012_database_encryption',
      description: 'Database encryption with pgcrypto',
      checksum: Migration.generateChecksum(_v012DatabaseEncryptionSql),
      sql: _v012DatabaseEncryptionSql,
    );
  }

  static Migration _v013_mfaSystem() {
    return Migration(
      version: '013_mfa_system',
      description: 'Multi-factor authentication system schema',
      checksum: Migration.generateChecksum(_v013MfaSystemSql),
      sql: _v013MfaSystemSql,
    );
  }

  static Migration _v014_performanceOptimization() {
    return Migration(
      version: '014_performance_optimization',
      description: 'Performance optimization indexes',
      checksum: Migration.generateChecksum(_v014PerformanceOptimizationSql),
      sql: _v014PerformanceOptimizationSql,
    );
  }

  static Migration _v015_additionalPerformance() {
    return Migration(
      version: '015_additional_performance',
      description: 'Additional performance indexes',
      checksum: Migration.generateChecksum(_v015AdditionalPerformanceSql),
      sql: _v015AdditionalPerformanceSql,
    );
  }

  static Migration _v016_analyticsSchema() {
    return Migration(
      version: '016_analytics_schema',
      description: 'Analytics and reporting schema',
      checksum: Migration.generateChecksum(_v016AnalyticsSql),
      sql: _v016AnalyticsSql,
    );
  }

  static Migration _v017_authEnhancements() {
    return Migration(
      version: '017_auth_enhancements',
      description: 'Authentication system enhancements',
      checksum: Migration.generateChecksum(_v017AuthEnhancementsSql),
      sql: _v017AuthEnhancementsSql,
    );
  }

  static Migration _v018_authTokens() {
    return Migration(
      version: '018_auth_tokens',
      description: 'Authentication tokens schema',
      checksum: Migration.generateChecksum(_v018AuthTokensSql),
      sql: _v018AuthTokensSql,
    );
  }

  // SQL content constants (truncated for brevity - these would contain the actual SQL from the files)
  static const String _v001InitialSql = '''
-- Quester Database Initialization Script
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE SCHEMA IF NOT EXISTS quester;
GRANT ALL PRIVILEGES ON SCHEMA quester TO quester;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA quester TO quester;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA quester TO quester;

CREATE TABLE IF NOT EXISTS quester.health_check (
    id SERIAL PRIMARY KEY,
    status VARCHAR(50) NOT NULL DEFAULT 'healthy',
    last_check TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO quester.health_check (status) VALUES ('healthy') ON CONFLICT DO NOTHING;
  ''';

  static const String _v002GamificationSql = '''
-- Comprehensive Gamification Schema
CREATE TYPE user_role AS ENUM ('novice', 'explorer', 'adventurer', 'hero', 'legend', 'mythic');
CREATE TYPE achievement_category AS ENUM ('quest_completion', 'collaboration', 'streak_building', 'quality_work', 'leadership', 'innovation', 'consistency', 'special_events');
CREATE TYPE achievement_rarity AS ENUM ('common', 'uncommon', 'rare', 'epic', 'legendary');
CREATE TYPE reward_type AS ENUM ('cosmetic', 'privilege', 'title', 'badge', 'avatar_frame', 'theme');
CREATE TYPE activity_type AS ENUM ('task_completion', 'quest_creation', 'quest_completion', 'collaboration', 'mentoring', 'quality_work', 'innovation', 'consistency', 'special_event', 'achievement_unlock', 'reward_purchase');
CREATE TYPE leaderboard_type AS ENUM ('global_points', 'monthly_points', 'quest_completion', 'collaboration', 'streak_days', 'achievement_count', 'quality_score', 'innovation_score', 'leadership_score', 'weekly_points', 'daily_points');

CREATE TABLE IF NOT EXISTS quester.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

CREATE TABLE IF NOT EXISTS quester.user_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE UNIQUE,
    total_points INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    role user_role DEFAULT 'novice',
    points_to_next_level INTEGER DEFAULT 500,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT positive_points CHECK (total_points >= 0),
    CONSTRAINT positive_level CHECK (current_level >= 1)
);

-- Additional tables would be included here...
-- (Truncated for brevity, but would include all gamification tables)
  ''';

  // Additional SQL constants would be defined here for each migration
  // These would be loaded from the actual SQL files in init-scripts/
  static const String _v003OrganizationsSql = '-- Organizations schema SQL here';
  static const String _v004SampleDataSql = '-- Sample data SQL here';
  static const String _v005EnterpriseSql = '-- Enterprise schema SQL here';
  static const String _v006SsoSecuritySql = '-- SSO security SQL here';
  static const String _v007AuthSecuritySql = '-- Auth security SQL here';
  static const String _v008BackupCodesSql = '-- Backup codes SQL here';
  static const String _v009ExistingTableSecuritySql = '-- Existing table security SQL here';
  static const String _v010SecurityOptimizationSql = '-- Security optimization SQL here';
  static const String _v011TrustedDevicesSql = '-- Trusted devices SQL here';
  static const String _v012DatabaseEncryptionSql = '-- Database encryption SQL here';
  static const String _v013MfaSystemSql = '-- MFA system SQL here';
  static const String _v014PerformanceOptimizationSql = '-- Performance optimization SQL here';
  static const String _v015AdditionalPerformanceSql = '-- Additional performance SQL here';
  static const String _v016AnalyticsSql = '-- Analytics schema SQL here';
  static const String _v017AuthEnhancementsSql = '-- Auth enhancements SQL here';
  static const String _v018AuthTokensSql = '-- Auth tokens SQL here';
}