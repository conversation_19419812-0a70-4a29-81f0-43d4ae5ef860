import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Optimized list widget with lazy loading and performance enhancements
class OptimizedList<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final double? itemExtent;
  final EdgeInsets padding;
  final ScrollPhysics? physics;
  final ScrollController? controller;
  final bool shrinkWrap;
  final Widget? separator;
  final Widget? emptyWidget;
  final Widget? loadingWidget;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final int loadMoreThreshold;
  final bool enableLazyLoading;
  final int initialVisibleItems;

  const OptimizedList({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.itemExtent,
    this.padding = EdgeInsets.zero,
    this.physics,
    this.controller,
    this.shrinkWrap = false,
    this.separator,
    this.emptyWidget,
    this.loadingWidget,
    this.isLoading = false,
    this.onLoadMore,
    this.loadMoreThreshold = 5,
    this.enableLazyLoading = true,
    this.initialVisibleItems = 20,
  });

  @override
  State<OptimizedList<T>> createState() => _OptimizedListState<T>();
}

class _OptimizedListState<T> extends State<OptimizedList<T>> {
  late ScrollController _scrollController;
  int _visibleItemCount = 0;
  bool _hasLoadedMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _visibleItemCount = widget.enableLazyLoading 
        ? widget.initialVisibleItems 
        : widget.items.length;
    
    if (widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void didUpdateWidget(OptimizedList<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.items.length != oldWidget.items.length) {
      if (widget.enableLazyLoading) {
        _visibleItemCount = widget.items.length < widget.initialVisibleItems
            ? widget.items.length
            : widget.initialVisibleItems;
      } else {
        _visibleItemCount = widget.items.length;
      }
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;
    
    final position = _scrollController.position;
    final maxScroll = position.maxScrollExtent;
    final currentScroll = position.pixels;
    
    // Load more items when approaching the end
    if (widget.onLoadMore != null && 
        !widget.isLoading && 
        !_hasLoadedMore &&
        currentScroll >= maxScroll - (widget.loadMoreThreshold * (widget.itemExtent ?? 100))) {
      _hasLoadedMore = true;
      widget.onLoadMore!();
      
      // Reset flag after a delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _hasLoadedMore = false;
        }
      });
    }
    
    // Lazy loading: Increase visible items as user scrolls
    if (widget.enableLazyLoading && _visibleItemCount < widget.items.length) {
      final itemsToShow = ((currentScroll / (widget.itemExtent ?? 100)) + 
          widget.initialVisibleItems).round();
      
      if (itemsToShow > _visibleItemCount) {
        setState(() {
          _visibleItemCount = (itemsToShow).clamp(0, widget.items.length);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    final itemsToShow = widget.items.take(_visibleItemCount).toList();

    return Column(
      children: [
        Expanded(
          child: _buildOptimizedListView(itemsToShow),
        ),
        if (widget.isLoading) _buildLoadingIndicator(),
      ],
    );
  }

  Widget _buildOptimizedListView(List<T> items) {
    if (widget.separator != null) {
      return ListView.separated(
        controller: _scrollController,
        physics: widget.physics,
        shrinkWrap: widget.shrinkWrap,
        padding: widget.padding,
        itemCount: items.length,
        itemBuilder: (context, index) => _buildOptimizedItem(items[index], index),
        separatorBuilder: (context, index) => widget.separator!,
        // Performance optimizations
        cacheExtent: 250.0,
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: true,
        addSemanticIndexes: true,
      );
    }

    return ListView.builder(
      controller: _scrollController,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      padding: widget.padding,
      itemCount: items.length,
      itemExtent: widget.itemExtent,
      itemBuilder: (context, index) => _buildOptimizedItem(items[index], index),
      // Performance optimizations
      cacheExtent: 250.0,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: true,
    );
  }

  Widget _buildOptimizedItem(T item, int index) {
    // Wrap in RepaintBoundary for better performance
    return RepaintBoundary(
      child: widget.itemBuilder(context, item, index),
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No items found',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return widget.loadingWidget ?? 
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        );
  }
}

/// Optimized grid widget with lazy loading
class OptimizedGrid<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final EdgeInsets padding;
  final ScrollPhysics? physics;
  final ScrollController? controller;
  final bool shrinkWrap;
  final Widget? emptyWidget;
  final bool enableLazyLoading;
  final int initialVisibleItems;

  const OptimizedGrid({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 8.0,
    this.mainAxisSpacing = 8.0,
    this.padding = EdgeInsets.zero,
    this.physics,
    this.controller,
    this.shrinkWrap = false,
    this.emptyWidget,
    this.enableLazyLoading = true,
    this.initialVisibleItems = 20,
  });

  @override
  State<OptimizedGrid<T>> createState() => _OptimizedGridState<T>();
}

class _OptimizedGridState<T> extends State<OptimizedGrid<T>> {
  late ScrollController _scrollController;
  int _visibleItemCount = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _visibleItemCount = widget.enableLazyLoading 
        ? widget.initialVisibleItems 
        : widget.items.length;
    
    if (widget.enableLazyLoading) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients || _visibleItemCount >= widget.items.length) return;
    
    final position = _scrollController.position;
    final currentScroll = position.pixels;
    final maxScroll = position.maxScrollExtent;
    
    // Load more items as user scrolls
    if (currentScroll >= maxScroll * 0.8) {
      setState(() {
        _visibleItemCount = (_visibleItemCount + widget.initialVisibleItems)
            .clamp(0, widget.items.length);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    final itemsToShow = widget.items.take(_visibleItemCount).toList();

    return GridView.builder(
      controller: _scrollController,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      padding: widget.padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: itemsToShow.length,
      itemBuilder: (context, index) => RepaintBoundary(
        child: widget.itemBuilder(context, itemsToShow[index], index),
      ),
      // Performance optimizations
      cacheExtent: 250.0,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.grid_view_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No items found',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
