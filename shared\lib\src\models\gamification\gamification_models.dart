import 'dart:math' as math;
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'gamification_models.g.dart';

/// Achievement types categorizing different kinds of accomplishments
enum AchievementType {
  @JsonValue('progress')
  progress,        // Based on task/quest completion count
  @JsonValue('skill')
  skill,          // Based on specific skill demonstrations
  @JsonValue('consistency')
  consistency,    // Based on streak maintenance
  @JsonValue('collaboration')
  collaboration,  // Based on team activities
  @JsonValue('special')
  special,        // Limited time or special events
}

/// Achievement rarity levels affecting visual presentation and rewards
enum AchievementRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

/// Achievement status for user progress tracking
enum AchievementStatus {
  @JsonValue('locked')
  locked,
  @JsonValue('available')
  available,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
}

/// Point transaction types for audit and analytics
enum PointTransactionType {
  @JsonValue('task_completion')
  taskCompletion,
  @JsonValue('quest_completion')
  questCompletion,
  @JsonValue('achievement_unlock')
  achievementUnlock,
  @JsonValue('streak_bonus')
  streakBonus,
  @JsonValue('collaboration_bonus')
  collaborationBonus,
  @JsonValue('admin_adjustment')
  adminAdjustment,
}

/// Achievement definition model
@JsonSerializable()
class Achievement extends Equatable {
  /// Unique achievement identifier
  final String id;
  
  /// Achievement display name
  final String name;
  
  /// Detailed description of how to earn this achievement
  final String description;
  
  /// Achievement category/type
  final AchievementType type;
  
  /// Visual rarity level
  final AchievementRarity rarity;
  
  /// Icon URL for display
  final String iconUrl;
  
  /// Points awarded when unlocked
  final int pointsAwarded;
  
  /// Required progress to complete (e.g., number of tasks, streak days)
  final int progressRequired;
  
  /// Whether this achievement is currently active/obtainable
  final bool isActive;
  
  /// Creation timestamp
  final DateTime createdAt;

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.rarity,
    required this.iconUrl,
    required this.pointsAwarded,
    required this.progressRequired,
    this.isActive = true,
    required this.createdAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) => 
      _$AchievementFromJson(json);
  
  Map<String, dynamic> toJson() => _$AchievementToJson(this);

  @override
  List<Object?> get props => [
    id, name, description, type, rarity, iconUrl, 
    pointsAwarded, progressRequired, isActive, createdAt
  ];
}

/// User's progress toward specific achievement
@JsonSerializable()
class UserAchievement extends Equatable {
  /// Reference to the achievement
  final String achievementId;
  
  /// User who is progressing toward this achievement
  final String userId;
  
  /// Current progress value
  final int currentProgress;
  
  /// Current status
  final AchievementStatus status;
  
  /// When this achievement was unlocked (if completed)
  final DateTime? unlockedAt;
  
  /// When progress was last updated
  final DateTime lastUpdated;

  const UserAchievement({
    required this.achievementId,
    required this.userId,
    required this.currentProgress,
    required this.status,
    this.unlockedAt,
    required this.lastUpdated,
  });

  factory UserAchievement.fromJson(Map<String, dynamic> json) => 
      _$UserAchievementFromJson(json);
  
  Map<String, dynamic> toJson() => _$UserAchievementToJson(this);

  @override
  List<Object?> get props => [
    achievementId, userId, currentProgress, status, unlockedAt, lastUpdated
  ];
}

/// User's overall gamification progress and stats
@JsonSerializable()
class UserProgress extends Equatable {
  /// User identifier
  final String userId;
  
  /// Total points accumulated across all activities
  final int totalPoints;
  
  /// Current level based on total points
  final int level;
  
  /// Current streak of consecutive active days
  final int currentStreak;
  
  /// Longest streak ever achieved
  final int longestStreak;
  
  /// List of completed achievement IDs
  final List<String> unlockedAchievements;
  
  /// Number of completed quests
  final int questsCompleted;
  
  /// Number of completed tasks
  final int tasksCompleted;
  
  /// Points earned today (resets daily)
  final int pointsToday;
  
  /// Last activity timestamp
  final DateTime? lastActivity;
  
  /// When this progress was last updated
  final DateTime updatedAt;

  const UserProgress({
    required this.userId,
    required this.totalPoints,
    required this.level,
    required this.currentStreak,
    required this.longestStreak,
    required this.unlockedAchievements,
    required this.questsCompleted,
    required this.tasksCompleted,
    required this.pointsToday,
    this.lastActivity,
    required this.updatedAt,
  });

  factory UserProgress.fromJson(Map<String, dynamic> json) => 
      _$UserProgressFromJson(json);
  
  Map<String, dynamic> toJson() => _$UserProgressToJson(this);

  /// Calculate level based on total points
  static int calculateLevel(int totalPoints) {
    // Exponential leveling: Level = sqrt(totalPoints / 100)
    return (math.sqrt(totalPoints / 100)).floor() + 1;
  }

  /// Calculate points needed for next level
  int get pointsForNextLevel {
    final nextLevel = level + 1;
    final pointsNeeded = nextLevel * nextLevel * 100;
    return pointsNeeded - totalPoints;
  }

  /// Get progress percentage to next level
  double get progressToNextLevel {
    if (pointsForNextLevel <= 0) return 1.0;
    
    final currentLevelPoints = level * level * 100;
    final nextLevelPoints = (level + 1) * (level + 1) * 100;
    final levelRange = nextLevelPoints - currentLevelPoints;
    final currentProgress = totalPoints - currentLevelPoints;
    
    return currentProgress / levelRange;
  }

  @override
  List<Object?> get props => [
    userId, totalPoints, level, currentStreak, longestStreak,
    unlockedAchievements, questsCompleted, tasksCompleted,
    pointsToday, lastActivity, updatedAt
  ];
}

/// Point transaction record for audit and analytics
@JsonSerializable()
class PointTransaction extends Equatable {
  /// Unique transaction identifier
  final String id;
  
  /// User who earned/lost points
  final String userId;
  
  /// Points awarded (positive) or deducted (negative)
  final int points;
  
  /// Type of activity that triggered this transaction
  final PointTransactionType type;
  
  /// Reference to related entity (task ID, quest ID, achievement ID, etc.)
  final String? referenceId;
  
  /// Human-readable description of the transaction
  final String description;
  
  /// Additional metadata about the transaction
  final Map<String, dynamic>? metadata;
  
  /// When this transaction occurred
  final DateTime createdAt;

  const PointTransaction({
    required this.id,
    required this.userId,
    required this.points,
    required this.type,
    this.referenceId,
    required this.description,
    this.metadata,
    required this.createdAt,
  });

  factory PointTransaction.fromJson(Map<String, dynamic> json) => 
      _$PointTransactionFromJson(json);
  
  Map<String, dynamic> toJson() => _$PointTransactionToJson(this);

  @override
  List<Object?> get props => [
    id, userId, points, type, referenceId, description, metadata, createdAt
  ];
}

/// Leaderboard entry showing user ranking
@JsonSerializable()
class LeaderboardEntry extends Equatable {
  /// User identifier
  final String userId;
  
  /// User's display name
  final String displayName;
  
  /// User's avatar URL
  final String? avatarUrl;
  
  /// Current ranking position (1-based)
  final int rank;
  
  /// Total points for ranking
  final int totalPoints;
  
  /// Current level
  final int level;
  
  /// Current streak
  final int currentStreak;
  
  /// Number of achievements unlocked
  final int achievementCount;
  
  /// When this leaderboard entry was calculated
  final DateTime calculatedAt;

  const LeaderboardEntry({
    required this.userId,
    required this.displayName,
    this.avatarUrl,
    required this.rank,
    required this.totalPoints,
    required this.level,
    required this.currentStreak,
    required this.achievementCount,
    required this.calculatedAt,
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) => 
      _$LeaderboardEntryFromJson(json);
  
  Map<String, dynamic> toJson() => _$LeaderboardEntryToJson(this);

  @override
  List<Object?> get props => [
    userId, displayName, avatarUrl, rank, totalPoints, 
    level, currentStreak, achievementCount, calculatedAt
  ];
}
