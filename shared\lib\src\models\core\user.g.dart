// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  email: json['email'] as String,
  displayName: json['displayName'] as String,
  firstName: json['firstName'] as String?,
  lastName: json['lastName'] as String?,
  avatarUrl: json['avatarUrl'] as String?,
  role: $enumDecode(_$UserRoleEnumMap, json['role']),
  status: $enumDecode(_$UserStatusEnumMap, json['status']),
  totalPoints: (json['totalPoints'] as num).toInt(),
  currentLevelPoints: (json['currentLevelPoints'] as num).toInt(),
  level: (json['level'] as num).toInt(),
  currentStreak: (json['currentStreak'] as num).toInt(),
  longestStreak: (json['longestStreak'] as num).toInt(),
  achievementCount: (json['achievementCount'] as num).toInt(),
  questsCompleted: (json['questsCompleted'] as num).toInt(),
  tasksCompleted: (json['tasksCompleted'] as num).toInt(),
  preferences: json['preferences'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  lastLoginAt: json['lastLoginAt'] == null
      ? null
      : DateTime.parse(json['lastLoginAt'] as String),
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'email': instance.email,
  'displayName': instance.displayName,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'avatarUrl': instance.avatarUrl,
  'role': _$UserRoleEnumMap[instance.role]!,
  'status': _$UserStatusEnumMap[instance.status]!,
  'totalPoints': instance.totalPoints,
  'currentLevelPoints': instance.currentLevelPoints,
  'level': instance.level,
  'currentStreak': instance.currentStreak,
  'longestStreak': instance.longestStreak,
  'achievementCount': instance.achievementCount,
  'questsCompleted': instance.questsCompleted,
  'tasksCompleted': instance.tasksCompleted,
  'preferences': instance.preferences,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
};

const _$UserRoleEnumMap = {
  UserRole.newcomer: 'newcomer',
  UserRole.apprentice: 'apprentice',
  UserRole.journeyman: 'journeyman',
  UserRole.expert: 'expert',
  UserRole.master: 'master',
  UserRole.grandmaster: 'grandmaster',
  UserRole.legendary: 'legendary',
  UserRole.mythic: 'mythic',
};

const _$UserStatusEnumMap = {
  UserStatus.active: 'active',
  UserStatus.inactive: 'inactive',
  UserStatus.suspended: 'suspended',
  UserStatus.pending: 'pending',
};
