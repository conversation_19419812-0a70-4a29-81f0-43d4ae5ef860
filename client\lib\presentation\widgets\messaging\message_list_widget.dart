import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../common/user_avatar.dart';

/// Widget for displaying a scrollable list of messages
class MessageListWidget extends StatefulWidget {
  /// List of messages to display
  final List<Message> messages;
  
  /// Scroll controller for the list
  final ScrollController scrollController;
  
  /// Callback when more messages need to be loaded
  final VoidCallback? onLoadMore;
  
  /// Whether more messages are currently being loaded
  final bool isLoadingMore;
  
  /// Callback when a message is tapped
  final Function(Message)? onMessageTap;
  
  /// Callback when a message is long pressed
  final Function(Message)? onMessageLongPress;
  
  /// Callback when a reaction is tapped
  final Function(Message, String)? onReactionTap;

  const MessageListWidget({
    super.key,
    required this.messages,
    required this.scrollController,
    this.onLoadMore,
    this.isLoadingMore = false,
    this.onMessageTap,
    this.onMessageLongPress,
    this.onReactionTap,
  });

  @override
  State<MessageListWidget> createState() => _MessageListWidgetState();
}

class _MessageListWidgetState extends State<MessageListWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.messages.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      controller: widget.scrollController,
      reverse: true, // Show newest messages at bottom
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: widget.messages.length + (widget.isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (widget.isLoadingMore && index == widget.messages.length) {
          return _buildLoadingIndicator(context);
        }

        final message = widget.messages[widget.messages.length - 1 - index];
        final previousMessage = index < widget.messages.length - 1
            ? widget.messages[widget.messages.length - 2 - index]
            : null;
        final nextMessage = index > 0
            ? widget.messages[widget.messages.length - index]
            : null;

        final showAvatar = _shouldShowAvatar(message, nextMessage);
        final showTimestamp = _shouldShowTimestamp(message, previousMessage);

        return MessageBubble(
          message: message,
          showAvatar: showAvatar,
          showTimestamp: showTimestamp,
          onTap: () => widget.onMessageTap?.call(message),
          onLongPress: () => widget.onMessageLongPress?.call(message),
          onReactionTap: (emoji) => widget.onReactionTap?.call(message, emoji),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No messages yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Start the conversation!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(AppConstants.defaultPadding),
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  bool _shouldShowAvatar(Message message, Message? nextMessage) {
    if (nextMessage == null) return true;
    return message.senderId != nextMessage.senderId ||
           _isTimestampGapLarge(message, nextMessage);
  }

  bool _shouldShowTimestamp(Message message, Message? previousMessage) {
    if (previousMessage == null) return true;
    return _isTimestampGapLarge(previousMessage, message);
  }

  bool _isTimestampGapLarge(Message message1, Message message2) {
    final diff = message2.createdAt.difference(message1.createdAt);
    return diff.inMinutes > 5; // Show timestamp if gap is more than 5 minutes
  }
}

/// Individual message bubble widget
class MessageBubble extends StatelessWidget {
  final Message message;
  final bool showAvatar;
  final bool showTimestamp;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Function(String)? onReactionTap;

  const MessageBubble({
    super.key,
    required this.message,
    this.showAvatar = true,
    this.showTimestamp = false,
    this.onTap,
    this.onLongPress,
    this.onReactionTap,
  });

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = message.senderId == 'current_user_id'; // TODO: Get actual current user ID
    
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (showTimestamp) _buildTimestamp(context),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (!isCurrentUser && showAvatar) ...[
                UserAvatar(
                  user: User(
                    id: message.senderId,
                    email: '',
                    displayName: message.senderName,
                    role: UserRole.newcomer,
                    status: UserStatus.active,
                    totalPoints: 0,
                    currentLevelPoints: 0,
                    level: 1,
                    currentStreak: 0,
                    longestStreak: 0,
                    achievementCount: 0,
                    questsCompleted: 0,
                    tasksCompleted: 0,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  ),
                  size: 32,
                ),
                const SizedBox(width: AppConstants.smallPadding),
              ] else if (!isCurrentUser) ...[
                const SizedBox(width: 40), // Space for avatar alignment
              ],
              
              Expanded(
                child: Column(
                  crossAxisAlignment: isCurrentUser 
                      ? CrossAxisAlignment.end 
                      : CrossAxisAlignment.start,
                  children: [
                    if (!isCurrentUser && showAvatar)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text(
                          message.senderName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    
                    GestureDetector(
                      onTap: onTap,
                      onLongPress: onLongPress,
                      child: Container(
                        constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width * 0.75,
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.defaultPadding,
                          vertical: AppConstants.smallPadding,
                        ),
                        decoration: BoxDecoration(
                          color: isCurrentUser
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (message.replyToId != null) _buildReplyPreview(context),
                            _buildMessageContent(context, isCurrentUser),
                            if (message.reactions.isNotEmpty) _buildReactions(context),
                          ],
                        ),
                      ),
                    ),
                    
                    if (isCurrentUser) _buildMessageStatus(context),
                  ],
                ),
              ),
              
              if (isCurrentUser && showAvatar) ...[
                const SizedBox(width: AppConstants.smallPadding),
                UserAvatar(
                  user: User(
                    id: message.senderId,
                    email: '',
                    displayName: message.senderName,
                    role: UserRole.newcomer,
                    status: UserStatus.active,
                    totalPoints: 0,
                    currentLevelPoints: 0,
                    level: 1,
                    currentStreak: 0,
                    longestStreak: 0,
                    achievementCount: 0,
                    questsCompleted: 0,
                    tasksCompleted: 0,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  ),
                  size: 32,
                ),
              ] else if (isCurrentUser) ...[
                const SizedBox(width: 40), // Space for avatar alignment
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.defaultPadding),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
          ),
          child: Text(
            _formatTimestamp(message.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReplyPreview(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border(
          left: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 3,
          ),
        ),
      ),
      child: Text(
        'Replying to message...', // TODO: Load actual reply content
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          fontStyle: FontStyle.italic,
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, bool isCurrentUser) {
    return Text(
      message.content,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: isCurrentUser
            ? Theme.of(context).colorScheme.onPrimary
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildReactions(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: AppConstants.smallPadding),
      child: Wrap(
        spacing: 4,
        children: message.reactions.map((reaction) => 
          GestureDetector(
            onTap: () => onReactionTap?.call(reaction.emoji),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(reaction.emoji, style: const TextStyle(fontSize: 12)),
                  const SizedBox(width: 2),
                  Text(
                    '1', // TODO: Count actual reactions
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ),
        ).toList(),
      ),
    );
  }

  Widget _buildMessageStatus(BuildContext context) {
    IconData statusIcon;
    Color statusColor;

    switch (message.status) {
      case MessageStatus.pending:
        statusIcon = Icons.access_time;
        statusColor = Theme.of(context).colorScheme.onSurface.withOpacity(0.5);
        break;
      case MessageStatus.sent:
        statusIcon = Icons.check;
        statusColor = Theme.of(context).colorScheme.onSurface.withOpacity(0.5);
        break;
      case MessageStatus.delivered:
        statusIcon = Icons.done_all;
        statusColor = Theme.of(context).colorScheme.onSurface.withOpacity(0.5);
        break;
      case MessageStatus.read:
        statusIcon = Icons.done_all;
        statusColor = Theme.of(context).colorScheme.primary;
        break;
      case MessageStatus.failed:
        statusIcon = Icons.error_outline;
        statusColor = Theme.of(context).colorScheme.error;
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _formatTime(message.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
          const SizedBox(width: 4),
          Icon(
            statusIcon,
            size: 16,
            color: statusColor,
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(timestamp.year, timestamp.month, timestamp.day);

    if (messageDate == today) {
      return 'Today ${_formatTime(timestamp)}';
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday ${_formatTime(timestamp)}';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${_formatTime(timestamp)}';
    }
  }

  String _formatTime(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}
