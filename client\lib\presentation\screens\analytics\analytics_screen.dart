import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart' hide DateTimeRange;
import '../../blocs/analytics/analytics_bloc.dart';
import '../../widgets/analytics/dashboard_widget.dart';
import '../../widgets/common/responsive_builder.dart';
import '../../widgets/dashboard/dashboard_widget_config.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load initial analytics data
    _loadAnalyticsData();
  }

  void _loadAnalyticsData() {
    final bloc = context.read<AnalyticsBloc>();
    bloc.add(const LoadDashboard(userId: 'current-user-id')); // Would use actual user ID
    bloc.add(const LoadMetrics());
    bloc.add(const LoadInsights());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.analytics), text: 'Metrics'),
            Tab(icon: Icon(Icons.insights), text: 'Insights'),
            Tab(icon: Icon(Icons.assessment), text: 'Reports'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<AnalyticsBloc>().add(const RefreshAnalytics());
            },
          ),
        ],
      ),
      body: BlocConsumer<AnalyticsBloc, AnalyticsState>(
        listener: (context, state) {
          if (state is AnalyticsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is EventTracked) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is ReportGenerated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Report generated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is AnalyticsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildDashboard(state),
              _buildMetrics(state),
              _buildInsights(state),
              _buildReports(state),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showTrackEventDialog(context),
        tooltip: 'Track Event',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildDashboard(AnalyticsState state) {
    final dashboard = state is AnalyticsLoaded ? state.dashboard : null;

    // Create dashboard widgets configuration
    final widgets = _createDashboardWidgets(dashboard);

    return ResponsiveBuilder(
      mobile: (context) => _buildMobileDashboard(context, widgets),
      tablet: (context) => _buildTabletDashboard(context, widgets),
      desktop: (context) => _buildDesktopDashboard(context, widgets),
    );
  }

  Widget _buildMobileDashboard(BuildContext context, List<DashboardWidgetConfig> widgets) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDashboardHeader(context),
          const SizedBox(height: AppConstants.defaultPadding),
          DashboardWidget(widgets: widgets),
        ],
      ),
    );
  }

  Widget _buildTabletDashboard(BuildContext context, List<DashboardWidgetConfig> widgets) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDashboardHeader(context),
                const SizedBox(height: AppConstants.defaultPadding),
                DashboardWidget(widgets: widgets),
              ],
            ),
          ),
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildDashboardSidebar(context),
        ),
      ],
    );
  }

  Widget _buildDesktopDashboard(BuildContext context, List<DashboardWidgetConfig> widgets) {
    return Row(
      children: [
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildDashboardControls(context),
        ),
        Expanded(
          flex: 4,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDashboardHeader(context),
                const SizedBox(height: AppConstants.defaultPadding),
                DashboardWidget(widgets: widgets),
              ],
            ),
          ),
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildDashboardSidebar(context),
        ),
      ],
    );
  }

  Widget _buildDashboardHeader(BuildContext context) {
    return Row(
      children: [
        Text(
          'Analytics Dashboard',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => _customizeDashboard(),
          icon: const Icon(Icons.edit),
          tooltip: 'Customize Dashboard',
        ),
      ],
    );
  }

  Widget _buildDashboardControls(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dashboard Controls',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Time Range Selector
          Text(
            'Time Range',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          DropdownButtonFormField<String>(
            value: 'week',
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              isDense: true,
            ),
            items: const [
              DropdownMenuItem(value: 'day', child: Text('Today')),
              DropdownMenuItem(value: 'week', child: Text('This Week')),
              DropdownMenuItem(value: 'month', child: Text('This Month')),
              DropdownMenuItem(value: 'quarter', child: Text('This Quarter')),
              DropdownMenuItem(value: 'year', child: Text('This Year')),
            ],
            onChanged: (value) {
              // TODO: Update time range
            },
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Quick Actions
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          ElevatedButton.icon(
            onPressed: () => _exportData(),
            icon: const Icon(Icons.download),
            label: const Text('Export Data'),
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 40),
            ),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          OutlinedButton.icon(
            onPressed: () => _scheduleReport(),
            icon: const Icon(Icons.schedule),
            label: const Text('Schedule Report'),
            style: OutlinedButton.styleFrom(
              minimumSize: const Size(double.infinity, 40),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardSidebar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Activity',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          Expanded(
            child: ListView.builder(
              itemCount: 5, // Mock recent activities
              itemBuilder: (context, index) {
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.analytics,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'Activity ${index + 1}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  subtitle: Text(
                    '${index + 1} minutes ago',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  dense: true,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<DashboardWidgetConfig> _createDashboardWidgets(Map<String, dynamic>? dashboard) {
    return [
      DashboardWidgetConfig(
        id: 'total_events',
        type: DashboardWidgetType.statsCard,
        title: 'Total Events',
        data: {
          'title': 'Total Events',
          'value': dashboard?['total_events']?.toString() ?? '0',
          'subtitle': 'Events tracked',
          'trend': 12.5,
          'icon': Icons.event,
        },
      ),
      DashboardWidgetConfig(
        id: 'active_users',
        type: DashboardWidgetType.statsCard,
        title: 'Active Users',
        data: {
          'title': 'Active Users',
          'value': dashboard?['active_users']?.toString() ?? '0',
          'subtitle': 'Users online',
          'trend': 8.3,
          'icon': Icons.people,
        },
      ),
      DashboardWidgetConfig(
        id: 'conversion_rate',
        type: DashboardWidgetType.statsCard,
        title: 'Conversion Rate',
        data: {
          'title': 'Conversion Rate',
          'value': '${dashboard?['conversion_rate']?.toString() ?? '0'}%',
          'subtitle': 'Goal completions',
          'trend': -2.1,
          'icon': Icons.trending_up,
        },
      ),
      DashboardWidgetConfig(
        id: 'revenue',
        type: DashboardWidgetType.statsCard,
        title: 'Revenue',
        data: {
          'title': 'Revenue',
          'value': '\$${dashboard?['revenue']?.toString() ?? '0'}',
          'subtitle': 'Total earnings',
          'trend': 15.7,
          'icon': Icons.attach_money,
        },
      ),
      DashboardWidgetConfig(
        id: 'user_engagement',
        type: DashboardWidgetType.chart,
        title: 'User Engagement',
        data: {
          'title': 'User Engagement Over Time',
          'chartType': 'line',
        },
      ),
      DashboardWidgetConfig(
        id: 'goal_progress',
        type: DashboardWidgetType.progressBar,
        title: 'Monthly Goal',
        data: {
          'title': 'Monthly Goal Progress',
          'progress': 0.68,
          'subtitle': '68% of monthly target',
        },
      ),
    ];
  }

  void _customizeDashboard() {
    // TODO: Show dashboard customization dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Dashboard customization coming soon!')),
    );
  }

  void _exportData() {
    // TODO: Implement data export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Data export coming soon!')),
    );
  }

  void _scheduleReport() {
    // TODO: Implement report scheduling
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Report scheduling coming soon!')),
    );
  }

  Widget _buildMetrics(AnalyticsState state) {
    final metrics = state is AnalyticsLoaded ? state.metrics : null;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Performance Metrics',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  context.read<AnalyticsBloc>().add(const LoadMetrics());
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Metrics Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildMetricTile(
                'User Engagement',
                metrics?['user_engagement']?.toString() ?? 'N/A',
                Icons.thumb_up,
                Colors.blue,
              ),
              _buildMetricTile(
                'Task Completion',
                metrics?['task_completion']?.toString() ?? 'N/A',
                Icons.task_alt,
                Colors.green,
              ),
              _buildMetricTile(
                'Time on Platform',
                '${metrics?['avg_time']?.toString() ?? '0'} hrs',
                Icons.schedule,
                Colors.orange,
              ),
              _buildMetricTile(
                'Success Rate',
                '${metrics?['success_rate']?.toString() ?? '0'}%',
                Icons.trending_up,
                Colors.purple,
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // Detailed Metrics
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Detailed Breakdown',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  if (metrics != null) ...[
                    _buildMetricRow('Daily Active Users', metrics['dau']?.toString() ?? '0'),
                    _buildMetricRow('Weekly Active Users', metrics['wau']?.toString() ?? '0'),
                    _buildMetricRow('Monthly Active Users', metrics['mau']?.toString() ?? '0'),
                    _buildMetricRow('Retention Rate', '${metrics['retention']?.toString() ?? '0'}%'),
                    _buildMetricRow('Churn Rate', '${metrics['churn']?.toString() ?? '0'}%'),
                  ] else
                    const Text('No metrics data available'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsights(AnalyticsState state) {
    final insights = state is AnalyticsLoaded ? state.insights : null;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'AI Insights',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  context.read<AnalyticsBloc>().add(const LoadInsights());
                },
                icon: const Icon(Icons.auto_awesome),
                label: const Text('Generate'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (insights != null && insights.isNotEmpty)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: insights.length,
              itemBuilder: (context, index) {
                final insight = insights[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              _getInsightIcon(insight['type']),
                              color: _getInsightColor(insight['priority']),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              insight['title'] ?? 'Insight',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            Chip(
                              label: Text(insight['priority'] ?? 'Medium'),
                              backgroundColor: _getInsightColor(insight['priority']).withValues(alpha: 0.2),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(insight['description'] ?? 'No description available'),
                        if (insight['recommendation'] != null) ...[
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.lightbulb, size: 16, color: Colors.blue),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    insight['recommendation'],
                                    style: const TextStyle(fontStyle: FontStyle.italic),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            )
          else
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.psychology,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No insights available yet',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Generate insights to get AI-powered recommendations',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildReports(AnalyticsState state) {
    final lastReport = state is AnalyticsLoaded ? state.lastReport : null;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Analytics Reports',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: () => _showGenerateReportDialog(context),
                icon: const Icon(Icons.add_chart),
                label: const Text('Generate Report'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Report Templates
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Quick Reports',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 2.5,
                    children: [
                      _buildReportTemplate('Weekly Summary', Icons.calendar_view_week),
                      _buildReportTemplate('Monthly Trends', Icons.trending_up),
                      _buildReportTemplate('User Behavior', Icons.person_outline),
                      _buildReportTemplate('Performance', Icons.speed),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Last Generated Report
          if (lastReport != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Last Generated Report',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    ListTile(
                      leading: const Icon(Icons.description),
                      title: Text(lastReport['title'] ?? 'Untitled Report'),
                      subtitle: Text('Generated: ${lastReport['created_at'] ?? 'Unknown'}'),
                      trailing: const Icon(Icons.download),
                      onTap: () {
                        // Handle report download/view
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Report download feature coming soon!')),
                        );
                      },
                    ),
                  ],
                ),
              ),
            )
          else
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.assessment,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No reports generated yet',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Removed unused _buildMetricCard method

  Widget _buildMetricTile(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTemplate(String title, IconData icon) {
    return InkWell(
      onTap: () {
        context.read<AnalyticsBloc>().add(GenerateReport(reportData: {
          'type': title.toLowerCase().replaceAll(' ', '_'),
          'title': title,
        }));
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Theme.of(context).primaryColor),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showTrackEventDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final titleController = TextEditingController();
        final descriptionController = TextEditingController();
        
        return AlertDialog(
          title: const Text('Track Event'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Event Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  context.read<AnalyticsBloc>().add(TrackEvent(eventData: {
                    'title': titleController.text,
                    'description': descriptionController.text,
                    'timestamp': DateTime.now().toIso8601String(),
                  }));
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Track'),
            ),
          ],
        );
      },
    );
  }

  void _showGenerateReportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final titleController = TextEditingController();
        String selectedType = 'weekly_summary';
        
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Generate Report'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'Report Title',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Report Type',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'weekly_summary', child: Text('Weekly Summary')),
                      DropdownMenuItem(value: 'monthly_trends', child: Text('Monthly Trends')),
                      DropdownMenuItem(value: 'user_behavior', child: Text('User Behavior')),
                      DropdownMenuItem(value: 'performance', child: Text('Performance')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedType = value;
                        });
                      }
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    context.read<AnalyticsBloc>().add(GenerateReport(reportData: {
                      'title': titleController.text.isEmpty ? 'Analytics Report' : titleController.text,
                      'type': selectedType,
                      'created_at': DateTime.now().toIso8601String(),
                    }));
                    Navigator.of(context).pop();
                  },
                  child: const Text('Generate'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Removed unused _getActivityIcon method

  IconData _getInsightIcon(String? type) {
    switch (type) {
      case 'performance':
        return Icons.trending_up;
      case 'user_behavior':
        return Icons.person;
      case 'engagement':
        return Icons.favorite;
      default:
        return Icons.psychology;
    }
  }

  Color _getInsightColor(String? priority) {
    switch (priority) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }
}