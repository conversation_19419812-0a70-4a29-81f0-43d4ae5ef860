#!/bin/bash

# Quester Test Runner Script
# Comprehensive testing suite for the Quester platform

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_ENV="test"
SERVER_DIR="server"
CLIENT_DIR="client"
LOG_DIR="test_logs"

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Help function
show_help() {
    echo "Quester Test Runner Script"
    echo ""
    echo "Usage: $0 [test_type] [options]"
    echo ""
    echo "Test Types:"
    echo "  all         - Run all tests (unit, integration, e2e)"
    echo "  unit        - Run unit tests only"
    echo "  integration - Run integration tests only"
    echo "  e2e         - Run end-to-end tests only"
    echo "  server      - Run server-side tests only"
    echo "  client      - Run client-side tests only"
    echo "  coverage    - Run tests with coverage report"
    echo ""
    echo "Options:"
    echo "  --verbose   - Enable verbose output"
    echo "  --watch     - Run tests in watch mode"
    echo "  --debug     - Enable debug logging"
    echo "  --clean     - Clean test environment before running"
    echo ""
    echo "Examples:"
    echo "  $0 all"
    echo "  $0 unit --verbose"
    echo "  $0 e2e --clean"
    echo "  $0 coverage --debug"
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Create logs directory
    mkdir -p "$LOG_DIR"
    
    # Set test environment variables
    export NODE_ENV=test
    export DB_NAME=questerdb_test
    export DB_HOST=localhost
    export DB_PORT=5432
    export DB_USER=quester
    export DB_PASSWORD=questerpass
    export REDIS_DB=1
    export REDIS_HOST=localhost
    export REDIS_PORT=6379
    
    log "Test environment configured"
}

# Clean test environment
clean_test_environment() {
    log "Cleaning test environment..."
    
    # Stop any running test servers
    pkill -f "dart.*server.dart" || true
    pkill -f "flutter.*serve" || true
    
    # Clean test databases
    if command -v psql &> /dev/null; then
        psql -h localhost -U quester -d postgres -c "DROP DATABASE IF EXISTS questerdb_test;" 2>/dev/null || true
        psql -h localhost -U quester -d postgres -c "CREATE DATABASE questerdb_test;" 2>/dev/null || true
    fi
    
    # Clean Redis test database
    if command -v redis-cli &> /dev/null; then
        redis-cli -n 1 FLUSHDB 2>/dev/null || true
    fi
    
    # Clean test logs
    rm -rf "$LOG_DIR"/*
    
    log "Test environment cleaned"
}

# Run server tests
run_server_tests() {
    log "Running server tests..."
    
    cd "$SERVER_DIR"
    
    # Install dependencies
    dart pub get
    
    # Run tests based on type
    case "$TEST_TYPE" in
        "unit")
            dart test test/unit/ --reporter=expanded
            ;;
        "integration")
            dart test test/integration/ --reporter=expanded
            ;;
        "e2e")
            dart test test/e2e/ --reporter=expanded
            ;;
        "all")
            dart test --reporter=expanded
            ;;
        "coverage")
            dart test --coverage=coverage
            dart pub global activate coverage
            dart pub global run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --packages=.packages --report-on=lib
            ;;
    esac
    
    cd "$SCRIPT_DIR"
}

# Run client tests
run_client_tests() {
    log "Running client tests..."
    
    cd "$CLIENT_DIR"
    
    # Install dependencies
    flutter pub get
    
    # Run Flutter tests
    case "$TEST_TYPE" in
        "unit")
            flutter test test/unit/
            ;;
        "integration")
            flutter test integration_test/
            ;;
        "all"|"coverage")
            flutter test --coverage
            ;;
    esac
    
    cd "$SCRIPT_DIR"
}

# Generate coverage report
generate_coverage_report() {
    log "Generating coverage report..."
    
    # Combine coverage from server and client
    mkdir -p coverage
    
    # Server coverage
    if [[ -f "$SERVER_DIR/coverage/lcov.info" ]]; then
        cp "$SERVER_DIR/coverage/lcov.info" "coverage/server_lcov.info"
    fi
    
    # Client coverage
    if [[ -f "$CLIENT_DIR/coverage/lcov.info" ]]; then
        cp "$CLIENT_DIR/coverage/lcov.info" "coverage/client_lcov.info"
    fi
    
    # Generate HTML report if lcov is available
    if command -v genhtml &> /dev/null; then
        if [[ -f "coverage/server_lcov.info" ]]; then
            genhtml coverage/server_lcov.info -o coverage/html/server
        fi
        
        if [[ -f "coverage/client_lcov.info" ]]; then
            genhtml coverage/client_lcov.info -o coverage/html/client
        fi
        
        log "Coverage reports generated in coverage/html/"
    fi
}

# Run performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    # Start services for performance testing
    docker-compose -f app/docker-compose.base.yml up -d postgres redis
    
    # Wait for services
    sleep 10
    
    # Run load tests if available
    if command -v artillery &> /dev/null; then
        artillery run test/performance/load_test.yml
    else
        warn "Artillery not installed, skipping load tests"
    fi
    
    # Cleanup
    docker-compose -f app/docker-compose.base.yml down
}

# Monitor test health
monitor_test_health() {
    log "Monitoring test health..."
    
    # Check if test database is accessible
    if ! pg_isready -h localhost -p 5432 -U quester -d questerdb_test &> /dev/null; then
        warn "Test database not accessible"
    else
        log "Test database: OK"
    fi
    
    # Check if Redis is accessible
    if ! redis-cli -n 1 ping &> /dev/null; then
        warn "Test Redis not accessible"
    else
        log "Test Redis: OK"
    fi
    
    # Check disk space
    local available_space=$(df -h . | awk 'NR==2 {print $4}')
    log "Available disk space: $available_space"
}

# Main execution
main() {
    local test_type="${1:-all}"
    local verbose=false
    local watch=false
    local debug=false
    local clean=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help|help)
                show_help
                exit 0
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --watch)
                watch=true
                shift
                ;;
            --debug)
                debug=true
                shift
                ;;
            --clean)
                clean=true
                shift
                ;;
            *)
                test_type="$1"
                shift
                ;;
        esac
    done
    
    # Set test type
    TEST_TYPE="$test_type"
    
    # Enable verbose output if requested
    if [[ "$verbose" == true ]]; then
        set -x
    fi
    
    # Clean environment if requested
    if [[ "$clean" == true ]]; then
        clean_test_environment
    fi
    
    # Setup environment
    setup_test_environment
    
    # Monitor health
    monitor_test_health
    
    log "Starting test execution: $test_type"
    
    # Run tests based on type
    case "$test_type" in
        "all")
            run_server_tests
            run_client_tests
            ;;
        "unit"|"integration"|"e2e"|"coverage")
            run_server_tests
            run_client_tests
            if [[ "$test_type" == "coverage" ]]; then
                generate_coverage_report
            fi
            ;;
        "server")
            TEST_TYPE="all"
            run_server_tests
            ;;
        "client")
            TEST_TYPE="all"
            run_client_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        *)
            error "Unknown test type: $test_type"
            ;;
    esac
    
    log "Test execution completed: $test_type"
    
    # Show results summary
    echo ""
    echo "🧪 Test Results Summary"
    echo "======================="
    echo "Test Type: $test_type"
    echo "Environment: $TEST_ENV"
    echo "Timestamp: $(date)"
    
    if [[ -d "$LOG_DIR" ]]; then
        echo "Logs: $LOG_DIR/"
    fi
    
    if [[ "$test_type" == "coverage" && -d "coverage/html" ]]; then
        echo "Coverage Reports: coverage/html/"
    fi
}

# Run main function with all arguments
main "$@"