import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/messaging/messaging_bloc.dart';
import '../../widgets/common/responsive_builder.dart';
import '../../widgets/messaging/message_list_widget.dart';
import '../../widgets/messaging/message_input_widget.dart';
import '../../widgets/messaging/chat_app_bar.dart';
import '../../widgets/messaging/typing_indicator_widget.dart';

/// Main chat screen for real-time messaging
class ChatScreen extends StatefulWidget {
  /// Chat ID to display
  final String chatId;
  
  /// Chat name for display
  final String? chatName;

  const ChatScreen({
    super.key,
    required this.chatId,
    this.chatName,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _messageFocusNode = FocusNode();
  
  bool _isLoadingMore = false;
  final List<String> _typingUsers = [];

  @override
  void initState() {
    super.initState();
    _loadMessages();
    _setupScrollListener();
    _setupMessageListener();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    _messageFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ChatAppBar(
        chatId: widget.chatId,
        chatName: widget.chatName,
        onBackPressed: () => Navigator.of(context).pop(),
        onInfoPressed: () => _showChatInfo(),
        onCallPressed: () => _startCall(),
        onVideoCallPressed: () => _startVideoCall(),
      ),
      body: ResponsiveBuilder(
        mobile: _buildMobileLayout,
        tablet: _buildTabletLayout,
        desktop: _buildDesktopLayout,
      ),
    );
  }

  /// Build mobile layout
  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: _buildMessageArea(context),
        ),
        _buildTypingIndicator(context),
        _buildMessageInput(context),
      ],
    );
  }

  /// Build tablet layout
  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            children: [
              Expanded(
                child: _buildMessageArea(context),
              ),
              _buildTypingIndicator(context),
              _buildMessageInput(context),
            ],
          ),
        ),
        if (context.isTablet)
          Container(
            width: 300,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                left: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
            ),
            child: _buildChatSidebar(context),
          ),
      ],
    );
  }

  /// Build desktop layout
  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 4,
          child: Column(
            children: [
              Expanded(
                child: _buildMessageArea(context),
              ),
              _buildTypingIndicator(context),
              _buildMessageInput(context),
            ],
          ),
        ),
        Container(
          width: 350,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildChatSidebar(context),
        ),
      ],
    );
  }

  /// Build message area
  Widget _buildMessageArea(BuildContext context) {
    return BlocBuilder<MessagingBloc, MessagingState>(
      builder: (context, state) {
        if (state is MessagesLoaded && state.chatId == widget.chatId) {
          return MessageListWidget(
            messages: state.messages,
            scrollController: _scrollController,
            onLoadMore: _loadMoreMessages,
            isLoadingMore: _isLoadingMore,
            onMessageTap: _handleMessageTap,
            onMessageLongPress: _handleMessageLongPress,
            onReactionTap: _handleReactionTap,
          );
        } else if (state is MessagingLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is MessagingError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Failed to load messages',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: _loadMessages,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        } else {
          return const Center(
            child: Text('No messages'),
          );
        }
      },
    );
  }

  /// Build typing indicator
  Widget _buildTypingIndicator(BuildContext context) {
    if (_typingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    return TypingIndicatorWidget(
      typingUsers: _typingUsers,
    );
  }

  /// Build message input
  Widget _buildMessageInput(BuildContext context) {
    return MessageInputWidget(
      controller: _messageController,
      focusNode: _messageFocusNode,
      onSendMessage: _sendMessage,
      onStartTyping: _startTyping,
      onStopTyping: _stopTyping,
      onAttachFile: _attachFile,
      onRecordVoice: _recordVoice,
    );
  }

  /// Build chat sidebar
  Widget _buildChatSidebar(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Text(
            'Chat Info',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const Divider(),
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            children: [
              _buildParticipantsSection(context),
              const SizedBox(height: AppConstants.largePadding),
              _buildMediaSection(context),
              const SizedBox(height: AppConstants.largePadding),
              _buildSettingsSection(context),
            ],
          ),
        ),
      ],
    );
  }

  /// Build participants section
  Widget _buildParticipantsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Participants',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        // TODO: Load and display actual participants
        ListTile(
          leading: const CircleAvatar(
            child: Icon(Icons.person),
          ),
          title: const Text('John Doe'),
          subtitle: const Text('Online'),
          trailing: Icon(
            Icons.circle,
            color: Colors.green,
            size: 12,
          ),
        ),
        ListTile(
          leading: const CircleAvatar(
            child: Icon(Icons.person),
          ),
          title: const Text('Jane Smith'),
          subtitle: const Text('Last seen 5 minutes ago'),
          trailing: Icon(
            Icons.circle,
            color: Colors.grey,
            size: 12,
          ),
        ),
      ],
    );
  }

  /// Build media section
  Widget _buildMediaSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Shared Media',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: 6,
          itemBuilder: (context, index) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            ),
            child: Icon(
              Icons.image,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ],
    );
  }

  /// Build settings section
  Widget _buildSettingsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Settings',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        ListTile(
          leading: const Icon(Icons.notifications),
          title: const Text('Notifications'),
          trailing: Switch(
            value: true,
            onChanged: (value) {
              // TODO: Update notification settings
            },
          ),
        ),
        ListTile(
          leading: const Icon(Icons.search),
          title: const Text('Search Messages'),
          onTap: _searchMessages,
        ),
        ListTile(
          leading: const Icon(Icons.exit_to_app),
          title: const Text('Leave Chat'),
          onTap: _leaveChat,
        ),
      ],
    );
  }

  /// Load messages
  void _loadMessages() {
    context.read<MessagingBloc>().add(
      LoadMessages(chatId: widget.chatId),
    );
  }

  /// Load more messages
  void _loadMoreMessages() {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });

    final currentState = context.read<MessagingBloc>().state;
    if (currentState is MessagesLoaded) {
      context.read<MessagingBloc>().add(
        LoadMessages(
          chatId: widget.chatId,
          page: (currentState.messages.length / 50).ceil() + 1,
        ),
      );
    }

    // Reset loading state after a delay
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    });
  }

  /// Setup scroll listener
  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreMessages();
      }
    });
  }

  /// Setup message listener
  void _setupMessageListener() {
    // TODO: Setup WebSocket listener for real-time messages
  }

  /// Send message
  void _sendMessage(String content) {
    if (content.trim().isEmpty) return;

    context.read<MessagingBloc>().add(
      SendMessage(
        chatId: widget.chatId,
        content: content.trim(),
      ),
    );

    _messageController.clear();
    _stopTyping();
  }

  /// Start typing
  void _startTyping() {
    context.read<MessagingBloc>().add(
      StartTyping(chatId: widget.chatId),
    );
  }

  /// Stop typing
  void _stopTyping() {
    context.read<MessagingBloc>().add(
      StopTyping(chatId: widget.chatId),
    );
  }

  /// Handle message tap
  void _handleMessageTap(Message message) {
    // TODO: Handle message tap (e.g., show details)
  }

  /// Handle message long press
  void _handleMessageLongPress(Message message) {
    _showMessageOptions(message);
  }

  /// Handle reaction tap
  void _handleReactionTap(Message message, String emoji) {
    // Check if user already reacted with this emoji
    final userReaction = message.reactions.firstWhere(
      (r) => r.emoji == emoji && r.userId == 'current_user_id',
      orElse: () => const MessageReaction(emoji: '', userId: '', userName: '', createdAt: null),
    );

    if (userReaction.emoji.isNotEmpty) {
      // Remove reaction
      context.read<MessagingBloc>().add(
        RemoveReaction(messageId: message.id, emoji: emoji),
      );
    } else {
      // Add reaction
      context.read<MessagingBloc>().add(
        AddReaction(messageId: message.id, emoji: emoji),
      );
    }
  }

  /// Show message options
  void _showMessageOptions(Message message) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.reply),
              title: const Text('Reply'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Set reply message
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Copy message content
              },
            ),
            if (message.senderId == 'current_user_id')
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Delete'),
                onTap: () {
                  Navigator.of(context).pop();
                  // TODO: Delete message
                },
              ),
          ],
        ),
      ),
    );
  }

  /// Show chat info
  void _showChatInfo() {
    // TODO: Show chat info dialog
  }

  /// Start call
  void _startCall() {
    // TODO: Start voice call
  }

  /// Start video call
  void _startVideoCall() {
    // TODO: Start video call
  }

  /// Attach file
  void _attachFile() {
    // TODO: Show file picker
  }

  /// Record voice
  void _recordVoice() {
    // TODO: Start voice recording
  }

  /// Search messages
  void _searchMessages() {
    // TODO: Navigate to search screen
  }

  /// Leave chat
  void _leaveChat() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Chat'),
        content: const Text('Are you sure you want to leave this chat?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MessagingBloc>().add(
                LeaveChat(chatId: widget.chatId),
              );
              Navigator.of(context).pop();
            },
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }
}
