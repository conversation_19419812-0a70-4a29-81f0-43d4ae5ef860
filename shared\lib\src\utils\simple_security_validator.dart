/// Simple Security Policy Validator
/// 
/// A basic validator for security policies that works with the actual model structure
library;

import '../models/security/organization_security_policy.dart';

class SimpleValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const SimpleValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  factory SimpleValidationResult.success({List<String> warnings = const []}) {
    return SimpleValidationResult(isValid: true, warnings: warnings);
  }

  factory SimpleValidationResult.failure({required List<String> errors}) {
    return SimpleValidationResult(isValid: false, errors: errors);
  }
}

/// Simple security policy validator
class SimpleSecurityValidator {
  /// Validate password policy
  static SimpleValidationResult validatePasswordPolicy(PasswordPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    if (policy.minLength < 8) {
      errors.add('Password minimum length must be at least 8 characters');
    }

    if (policy.minLength > 128) {
      errors.add('Password minimum length cannot exceed 128 characters');
    }

    if (!policy.requireUppercase && !policy.requireLowercase && 
        !policy.requireNumbers && !policy.requireSymbols) {
      warnings.add('No character requirements specified');
    }

    return SimpleValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate MFA policy
  static SimpleValidationResult validateMFAPolicy(MFAPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    final validMethods = ['totp', 'sms', 'email', 'backup_codes'];
    for (final method in policy.allowedMethods) {
      if (!validMethods.contains(method)) {
        errors.add('Invalid MFA method: $method');
      }
    }

    if (policy.gracePeriodDays < 0 || policy.gracePeriodDays > 90) {
      errors.add('Grace period must be between 0 and 90 days');
    }

    return SimpleValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate session policy
  static SimpleValidationResult validateSessionPolicy(SessionPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    if (policy.idleTimeoutMinutes <= 0) {
      errors.add('Idle timeout must be greater than 0');
    }

    if (policy.absoluteTimeoutHours <= 0 || policy.absoluteTimeoutHours > 24) {
      errors.add('Absolute timeout must be between 1 and 24 hours');
    }

    if (policy.concurrentSessionsLimit <= 0 || policy.concurrentSessionsLimit > 50) {
      errors.add('Concurrent sessions must be between 1 and 50');
    }

    return SimpleValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate access policy
  static SimpleValidationResult validateAccessPolicy(AccessPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    // Basic validation for now
    return SimpleValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate audit policy
  static SimpleValidationResult validateAuditPolicy(AuditPolicy policy) {
    final errors = <String>[];
    final warnings = <String>[];

    if (policy.retentionDays <= 0 || policy.retentionDays > 2555) {
      errors.add('Retention days must be between 1 and 2555');
    }

    return SimpleValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate complete organization security policy
  static SimpleValidationResult validateOrganizationPolicy(OrganizationSecurityPolicy policy) {
    final results = <SimpleValidationResult>[];

    results.add(validatePasswordPolicy(policy.passwordPolicy));
    results.add(validateMFAPolicy(policy.mfaPolicy));
    results.add(validateSessionPolicy(policy.sessionPolicy));
    results.add(validateAccessPolicy(policy.accessPolicy));
    results.add(validateAuditPolicy(policy.auditPolicy));

    final allErrors = <String>[];
    final allWarnings = <String>[];
    bool isValid = true;

    for (final result in results) {
      if (!result.isValid) isValid = false;
      allErrors.addAll(result.errors);
      allWarnings.addAll(result.warnings);
    }

    return SimpleValidationResult(
      isValid: isValid,
      errors: allErrors,
      warnings: allWarnings,
    );
  }
}