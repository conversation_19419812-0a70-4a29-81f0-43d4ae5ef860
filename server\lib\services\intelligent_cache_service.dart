/// Intelligent caching service with adaptive strategies
library;

import 'dart:async';
import 'dart:convert';
import 'dart:math';

/// Cache entry with metadata for intelligent management
class IntelligentCacheEntry {
  final String key;
  final dynamic data;
  final DateTime createdAt;
  final DateTime expiresAt;
  final int accessCount;
  final DateTime lastAccessed;
  final double priority;
  final String category;
  final int size;

  IntelligentCacheEntry({
    required this.key,
    required this.data,
    required this.createdAt,
    required this.expiresAt,
    required this.accessCount,
    required this.lastAccessed,
    required this.priority,
    required this.category,
    required this.size,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  
  double get accessFrequency {
    final ageInHours = DateTime.now().difference(createdAt).inHours;
    return ageInHours > 0 ? accessCount / ageInHours : accessCount.toDouble();
  }

  double get recency {
    final hoursSinceAccess = DateTime.now().difference(lastAccessed).inHours;
    return 1.0 / (hoursSinceAccess + 1);
  }

  double get cacheScore => (accessFrequency * 0.4) + (recency * 0.3) + (priority * 0.3);
}

/// Intelligent caching service with adaptive eviction and optimization
class IntelligentCacheService {
  static final IntelligentCacheService _instance = IntelligentCacheService._internal();
  factory IntelligentCacheService() => _instance;
  IntelligentCacheService._internal();

  // Cache storage
  final Map<String, IntelligentCacheEntry> _cache = {};
  
  // Cache statistics
  int _hits = 0;
  int _misses = 0;
  int _evictions = 0;
  
  // Configuration
  static const int _maxCacheSize = 10000;
  static const int _maxMemoryMB = 100;
  // static const Duration _defaultTTL = Duration(minutes: 15); // TODO: Implement default TTL usage
  
  // Cache categories with different strategies
  static const Map<String, Map<String, dynamic>> _categoryStrategies = {
    'user_data': {
      'ttl_minutes': 5,
      'priority': 0.8,
      'max_size_kb': 50,
    },
    'leaderboard': {
      'ttl_minutes': 2,
      'priority': 0.9,
      'max_size_kb': 100,
    },
    'achievements': {
      'ttl_minutes': 30,
      'priority': 0.7,
      'max_size_kb': 20,
    },
    'quest_data': {
      'ttl_minutes': 10,
      'priority': 0.8,
      'max_size_kb': 75,
    },
    'analytics': {
      'ttl_minutes': 60,
      'priority': 0.6,
      'max_size_kb': 200,
    },
  };

  /// Store data in cache with intelligent categorization
  Future<void> set(
    String key,
    dynamic data, {
    Duration? ttl,
    String category = 'default',
    double? priority,
  }) async {
    final strategy = _categoryStrategies[category] ?? _categoryStrategies['user_data']!;
    final effectiveTTL = ttl ?? Duration(minutes: strategy['ttl_minutes'] as int);
    final effectivePriority = priority ?? (strategy['priority'] as double);
    
    final dataSize = _calculateDataSize(data);
    final maxSizeKB = strategy['max_size_kb'] as int;
    
    // Skip caching if data is too large for this category
    if (dataSize > maxSizeKB * 1024) {
      return;
    }

    // Ensure cache capacity
    await _ensureCacheCapacity(dataSize);

    final entry = IntelligentCacheEntry(
      key: key,
      data: data,
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(effectiveTTL),
      accessCount: 0,
      lastAccessed: DateTime.now(),
      priority: effectivePriority,
      category: category,
      size: dataSize,
    );

    _cache[key] = entry;
  }

  /// Get data from cache with access tracking
  Future<T?> get<T>(String key) async {
    final entry = _cache[key];
    
    if (entry == null) {
      _misses++;
      return null;
    }

    if (entry.isExpired) {
      _cache.remove(key);
      _misses++;
      return null;
    }

    // Update access statistics
    final updatedEntry = IntelligentCacheEntry(
      key: entry.key,
      data: entry.data,
      createdAt: entry.createdAt,
      expiresAt: entry.expiresAt,
      accessCount: entry.accessCount + 1,
      lastAccessed: DateTime.now(),
      priority: entry.priority,
      category: entry.category,
      size: entry.size,
    );
    
    _cache[key] = updatedEntry;
    _hits++;
    
    return entry.data as T?;
  }

  /// Get data with fallback and automatic caching
  Future<T> getOrSet<T>(
    String key,
    Future<T> Function() fallback, {
    Duration? ttl,
    String category = 'default',
    double? priority,
  }) async {
    final cached = await get<T>(key);
    if (cached != null) {
      return cached;
    }

    final data = await fallback();
    await set(key, data, ttl: ttl, category: category, priority: priority);
    return data;
  }

  /// Invalidate cache entries by pattern
  Future<void> invalidatePattern(String pattern) async {
    final regex = RegExp(pattern);
    final keysToRemove = _cache.keys.where((key) => regex.hasMatch(key)).toList();
    
    for (final key in keysToRemove) {
      _cache.remove(key);
    }
  }

  /// Invalidate cache entries by category
  Future<void> invalidateCategory(String category) async {
    final keysToRemove = _cache.entries
        .where((entry) => entry.value.category == category)
        .map((entry) => entry.key)
        .toList();
    
    for (final key in keysToRemove) {
      _cache.remove(key);
    }
  }

  /// Warm up cache with frequently accessed data
  Future<void> warmUp(Map<String, Future<dynamic> Function()> warmUpData) async {
    for (final entry in warmUpData.entries) {
      try {
        final data = await entry.value();
        await set(entry.key, data, category: 'warmup', priority: 0.9);
      } catch (e) {
        // Log error but continue with other warm-up data
        print('Failed to warm up cache for ${entry.key}: $e');
      }
    }
  }

  /// Get cache statistics and performance metrics
  Map<String, dynamic> getStatistics() {
    final totalRequests = _hits + _misses;
    final hitRatio = totalRequests > 0 ? _hits / totalRequests : 0.0;
    
    final categoryCounts = <String, int>{};
    final categoryMemory = <String, int>{};
    int totalMemory = 0;
    
    for (final entry in _cache.values) {
      categoryCounts[entry.category] = (categoryCounts[entry.category] ?? 0) + 1;
      categoryMemory[entry.category] = (categoryMemory[entry.category] ?? 0) + entry.size;
      totalMemory += entry.size;
    }

    return {
      'cache_size': _cache.length,
      'hit_ratio': hitRatio,
      'hits': _hits,
      'misses': _misses,
      'evictions': _evictions,
      'memory_usage_bytes': totalMemory,
      'memory_usage_mb': totalMemory / (1024 * 1024),
      'categories': categoryCounts,
      'category_memory': categoryMemory,
      'avg_cache_score': _calculateAverageCacheScore(),
      'top_accessed_keys': _getTopAccessedKeys(10),
    };
  }

  /// Optimize cache by removing low-value entries
  Future<void> optimize() async {
    // Remove expired entries
    final expiredKeys = _cache.entries
        .where((entry) => entry.value.isExpired)
        .map((entry) => entry.key)
        .toList();
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }

    // If still over capacity, remove low-score entries
    if (_cache.length > _maxCacheSize * 0.8) {
      final sortedEntries = _cache.entries.toList()
        ..sort((a, b) => a.value.cacheScore.compareTo(b.value.cacheScore));
      
      final entriesToRemove = sortedEntries.take(_cache.length - (_maxCacheSize ~/ 2));
      for (final entry in entriesToRemove) {
        _cache.remove(entry.key);
        _evictions++;
      }
    }
  }

  /// Ensure cache has capacity for new entry
  Future<void> _ensureCacheCapacity(int newEntrySize) async {
    // Check memory limit
    final currentMemory = _cache.values.fold<int>(0, (sum, entry) => sum + entry.size);
    final maxMemoryBytes = _maxMemoryMB * 1024 * 1024;
    
    if (currentMemory + newEntrySize > maxMemoryBytes || _cache.length >= _maxCacheSize) {
      await optimize();
    }
  }

  /// Calculate data size in bytes (simplified)
  int _calculateDataSize(dynamic data) {
    try {
      final jsonString = jsonEncode(data);
      return jsonString.length;
    } catch (e) {
      // Fallback estimation
      return data.toString().length;
    }
  }

  /// Calculate average cache score
  double _calculateAverageCacheScore() {
    if (_cache.isEmpty) return 0.0;
    
    final totalScore = _cache.values.fold<double>(0.0, (sum, entry) => sum + entry.cacheScore);
    return totalScore / _cache.length;
  }

  /// Get top accessed cache keys
  List<Map<String, dynamic>> _getTopAccessedKeys(int limit) {
    final sortedEntries = _cache.entries.toList()
      ..sort((a, b) => b.value.accessCount.compareTo(a.value.accessCount));
    
    return sortedEntries.take(limit).map((entry) => {
      'key': entry.key,
      'access_count': entry.value.accessCount,
      'cache_score': entry.value.cacheScore,
      'category': entry.value.category,
      'size_bytes': entry.value.size,
    }).toList();
  }

  /// Preload cache with predicted data
  Future<void> preloadPredictedData() async {
    // Analyze access patterns and preload likely-to-be-accessed data
    final accessPatterns = _analyzeAccessPatterns();
    
    for (final pattern in accessPatterns) {
      try {
        // This would integrate with your data sources to preload data
        // For now, we'll just mark the pattern as analyzed
        print('Analyzed access pattern: ${pattern['pattern']} (confidence: ${pattern['confidence']})');
      } catch (e) {
        print('Failed to preload data for pattern ${pattern['pattern']}: $e');
      }
    }
  }

  /// Analyze access patterns for predictive caching
  List<Map<String, dynamic>> _analyzeAccessPatterns() {
    final patterns = <Map<String, dynamic>>[];
    
    // Group keys by common prefixes
    final prefixGroups = <String, List<String>>{};
    for (final key in _cache.keys) {
      final parts = key.split(':');
      if (parts.length > 1) {
        final prefix = parts[0];
        prefixGroups.putIfAbsent(prefix, () => []);
        prefixGroups[prefix]!.add(key);
      }
    }

    // Analyze each prefix group
    for (final entry in prefixGroups.entries) {
      if (entry.value.length > 3) { // Only analyze groups with multiple keys
        final avgAccessCount = entry.value
            .map((key) => _cache[key]?.accessCount ?? 0)
            .reduce((a, b) => a + b) / entry.value.length;
        
        if (avgAccessCount > 5) { // High-access pattern
          patterns.add({
            'pattern': '${entry.key}:*',
            'confidence': (avgAccessCount / 100).clamp(0.0, 1.0),
            'key_count': entry.value.length,
            'avg_access_count': avgAccessCount,
          });
        }
      }
    }

    return patterns..sort((a, b) => (b['confidence'] as double).compareTo(a['confidence'] as double));
  }

  /// Clear all cache entries
  void clear() {
    _cache.clear();
    _hits = 0;
    _misses = 0;
    _evictions = 0;
  }

  /// Get cache health score
  double getHealthScore() {
    final hitRatio = (_hits + _misses) > 0 ? _hits / (_hits + _misses) : 0.0;
    final memoryEfficiency = _calculateMemoryEfficiency();
    final accessDistribution = _calculateAccessDistribution();
    
    return (hitRatio * 0.5) + (memoryEfficiency * 0.3) + (accessDistribution * 0.2);
  }

  /// Calculate memory efficiency
  double _calculateMemoryEfficiency() {
    final currentMemory = _cache.values.fold<int>(0, (sum, entry) => sum + entry.size);
    final maxMemoryBytes = _maxMemoryMB * 1024 * 1024;
    
    return 1.0 - (currentMemory / maxMemoryBytes).clamp(0.0, 1.0);
  }

  /// Calculate access distribution (how evenly accessed the cache is)
  double _calculateAccessDistribution() {
    if (_cache.isEmpty) return 1.0;
    
    final accessCounts = _cache.values.map((entry) => entry.accessCount).toList();
    final mean = accessCounts.reduce((a, b) => a + b) / accessCounts.length;
    
    final variance = accessCounts
        .map((count) => pow(count - mean, 2))
        .reduce((a, b) => a + b) / accessCounts.length;
    
    final standardDeviation = sqrt(variance);
    
    // Lower standard deviation means more even distribution
    return 1.0 / (1.0 + standardDeviation / mean);
  }
}
