import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../common/responsive_builder.dart';

/// Widget for displaying quest information in a card format
class QuestCardWidget extends StatelessWidget {
  /// The quest to display
  final Quest quest;
  
  /// Callback when the card is tapped
  final VoidCallback? onTap;
  
  /// Callback when quest status is changed
  final Function(QuestStatus)? onStatusChanged;
  
  /// Callback when quest priority is changed
  final Function(QuestPriority)? onPriorityChanged;
  
  /// Whether to show detailed information
  final bool showDetails;
  
  /// Whether to show action buttons
  final bool showActions;

  const QuestCardWidget({
    super.key,
    required this.quest,
    this.onTap,
    this.onStatusChanged,
    this.onPriorityChanged,
    this.showDetails = true,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              if (showDetails) ...[
                const SizedBox(height: AppConstants.smallPadding),
                _buildDescription(context),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildProgress(context),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildMetadata(context),
              ],
              if (showActions) ...[
                const SizedBox(height: AppConstants.defaultPadding),
                _buildActions(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                quest.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  _buildPriorityChip(context),
                  const SizedBox(width: AppConstants.smallPadding),
                  _buildStatusChip(context),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        _buildQuestIcon(context),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    if (quest.description.isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      quest.description,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildProgress(BuildContext context) {
    final completedTasks = quest.taskIds.length; // Simplified - we only have task IDs
    final totalTasks = quest.taskIds.length;
    final progress = totalTasks > 0 ? completedTasks / totalTasks : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '$completedTasks / $totalTasks tasks',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
          valueColor: AlwaysStoppedAnimation<Color>(
            _getProgressColor(context, progress),
          ),
        ),
      ],
    );
  }

  Widget _buildMetadata(BuildContext context) {
    return ResponsiveWrap(
      spacing: AppConstants.defaultPadding,
      children: [
        _buildMetadataItem(
          context,
          Icons.calendar_today,
          'Due: ${_formatDate(quest.deadline)}',
        ),
        if (quest.assignedToId != null && quest.assignedToId!.isNotEmpty)
          _buildMetadataItem(
            context,
            Icons.person,
            'Assigned to: ${quest.assignedToId ?? 'Unassigned'}',
          ),
        _buildMetadataItem(
          context,
          Icons.star,
          '${quest.totalPoints} points',
        ),
      ],
    );
  }

  Widget _buildMetadataItem(BuildContext context, IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      children: [
        if (quest.status != QuestStatus.completed) ...[
          _buildActionButton(
            context,
            icon: quest.status == QuestStatus.draft ? Icons.play_arrow : Icons.pause,
            label: quest.status == QuestStatus.draft ? 'Start' : 'Pause',
            onPressed: () => onStatusChanged?.call(
              quest.status == QuestStatus.draft
                  ? QuestStatus.active
                  : QuestStatus.draft,
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
        ],
        _buildActionButton(
          context,
          icon: Icons.edit,
          label: 'Edit',
          onPressed: () {
            // TODO: Navigate to edit quest
          },
        ),
        const Spacer(),
        PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'priority',
              child: ListTile(
                leading: Icon(Icons.flag),
                title: Text('Change Priority'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'duplicate',
              child: ListTile(
                leading: Icon(Icons.copy),
                title: Text('Duplicate'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'archive',
              child: ListTile(
                leading: Icon(Icons.archive),
                title: Text('Archive'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete),
                title: Text('Delete'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
          child: const Icon(Icons.more_vert),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
      ),
    );
  }

  Widget _buildPriorityChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getPriorityColor(quest.priority).withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(
          color: _getPriorityColor(quest.priority),
          width: 1,
        ),
      ),
      child: Text(
        quest.priority.name.toUpperCase(),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getPriorityColor(quest.priority),
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(quest.status).withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(quest.status),
            size: 12,
            color: _getStatusColor(quest.status),
          ),
          const SizedBox(width: 4),
          Text(
            quest.status.name.toUpperCase(),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getStatusColor(quest.status),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestIcon(BuildContext context) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: _getPriorityColor(quest.priority).withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Icon(
        _getQuestTypeIcon(quest.type),
        color: _getPriorityColor(quest.priority),
        size: 24,
      ),
    );
  }

  Color _getPriorityColor(QuestPriority priority) {
    switch (priority) {
      case QuestPriority.low:
        return Colors.green;
      case QuestPriority.medium:
        return Colors.orange;
      case QuestPriority.high:
        return Colors.red;
      case QuestPriority.urgent:
        return Colors.purple;
    }
  }

  Color _getStatusColor(QuestStatus status) {
    switch (status) {
      case QuestStatus.draft:
        return Colors.grey;
      case QuestStatus.active:
        return Colors.blue;
      case QuestStatus.paused:
        return Colors.orange;
      case QuestStatus.completed:
        return Colors.green;
      case QuestStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(QuestStatus status) {
    switch (status) {
      case QuestStatus.draft:
        return Icons.edit;
      case QuestStatus.active:
        return Icons.play_arrow;
      case QuestStatus.paused:
        return Icons.pause;
      case QuestStatus.completed:
        return Icons.check_circle;
      case QuestStatus.cancelled:
        return Icons.cancel;
    }
  }

  IconData _getQuestTypeIcon(QuestType type) {
    switch (type) {
      case QuestType.personal:
        return Icons.person;
      case QuestType.team:
        return Icons.group;
      case QuestType.learning:
        return Icons.school;
      case QuestType.work:
        return Icons.work;
      case QuestType.health:
        return Icons.favorite;
      case QuestType.creative:
        return Icons.palette;
      case QuestType.social:
        return Icons.people;
      case QuestType.other:
        return Icons.explore;
    }
  }

  Color _getProgressColor(BuildContext context, double progress) {
    if (progress < 0.3) {
      return Colors.red;
    } else if (progress < 0.7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'No due date';
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    
    final difference = targetDate.difference(today).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Tomorrow';
    } else if (difference == -1) {
      return 'Yesterday';
    } else if (difference > 0) {
      return 'In $difference days';
    } else {
      return '${-difference} days ago';
    }
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'priority':
        _showPriorityDialog(context);
        break;
      case 'duplicate':
        // TODO: Implement duplicate quest
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Duplicate feature coming soon!')),
        );
        break;
      case 'archive':
        // TODO: Implement archive quest
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Archive feature coming soon!')),
        );
        break;
      case 'delete':
        _showDeleteDialog(context);
        break;
    }
  }

  void _showPriorityDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Priority'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: QuestPriority.values.map((priority) =>
            RadioListTile<QuestPriority>(
              title: Text(priority.name.toUpperCase()),
              value: priority,
              groupValue: quest.priority,
              onChanged: (value) {
                if (value != null) {
                  Navigator.of(context).pop();
                  onPriorityChanged?.call(value);
                }
              },
            ),
          ).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Quest'),
        content: Text('Are you sure you want to delete "${quest.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement delete quest
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Delete feature coming soon!')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
