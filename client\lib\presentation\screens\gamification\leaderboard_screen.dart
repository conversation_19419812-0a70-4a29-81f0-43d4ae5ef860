import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/gamification/gamification_bloc.dart';
import '../../widgets/common/responsive_builder.dart';
import '../../widgets/common/user_avatar.dart';

/// Comprehensive leaderboard screen showing user rankings
class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  LeaderboardType _selectedType = LeaderboardType.points;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadLeaderboard();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Leaderboard'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Points', icon: Icon(Icons.stars)),
            Tab(text: 'Level', icon: Icon(Icons.trending_up)),
            Tab(text: 'Streaks', icon: Icon(Icons.local_fire_department)),
            Tab(text: 'Achievements', icon: Icon(Icons.emoji_events)),
          ],
          onTap: (index) {
            setState(() {
              _selectedType = LeaderboardType.values[index];
            });
            _loadLeaderboard();
          },
        ),
      ),
      body: ResponsiveBuilder(
        mobile: _buildMobileLayout,
        tablet: _buildTabletLayout,
        desktop: _buildDesktopLayout,
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        _buildTopThree(context),
        Expanded(
          child: _buildLeaderboardList(context),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Column(
            children: [
              _buildTopThree(context),
              Expanded(
                child: _buildLeaderboardList(context),
              ),
            ],
          ),
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildLeaderboardStats(context),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildLeaderboardFilters(context),
        ),
        Expanded(
          flex: 3,
          child: Column(
            children: [
              _buildTopThree(context),
              Expanded(
                child: _buildLeaderboardList(context),
              ),
            ],
          ),
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildLeaderboardStats(context),
        ),
      ],
    );
  }

  Widget _buildTopThree(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoaded && state.leaderboard != null) {
          final topUsers = state.leaderboard!.take(3).toList();
          
          if (topUsers.isEmpty) {
            return const SizedBox.shrink();
          }

          return Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (topUsers.length > 1) _buildPodiumPosition(context, topUsers[1], 2),
                if (topUsers.isNotEmpty) _buildPodiumPosition(context, topUsers[0], 1),
                if (topUsers.length > 2) _buildPodiumPosition(context, topUsers[2], 3),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildPodiumPosition(BuildContext context, LeaderboardEntry entry, int position) {
    final colors = [
      Colors.amber, // Gold
      Colors.grey[400]!, // Silver
      Colors.brown[400]!, // Bronze
    ];
    
    final heights = [120.0, 100.0, 80.0];
    final avatarSizes = [60.0, 50.0, 45.0];

    return Column(
      children: [
        UserAvatar(
          user: User(
            id: entry.userId,
            email: '',
            displayName: entry.displayName,
            avatarUrl: entry.avatarUrl,
          ),
          size: avatarSizes[position - 1],
          showLevelBadge: true,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          entry.userName,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Container(
          width: 80,
          height: heights[position - 1],
          decoration: BoxDecoration(
            color: colors[position - 1],
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppConstants.defaultBorderRadius),
              topRight: Radius.circular(AppConstants.defaultBorderRadius),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                position == 1 ? Icons.emoji_events : Icons.military_tech,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                '#$position',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                _getScoreText(entry),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLeaderboardList(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is GamificationError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Failed to load leaderboard',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: _loadLeaderboard,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        } else if (state is GamificationLoaded && state.leaderboard != null) {
          final entries = state.leaderboard!.skip(3).toList(); // Skip top 3
          
          return ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: entries.length,
            itemBuilder: (context, index) {
              final entry = entries[index];
              final position = index + 4; // +4 because we skip top 3
              
              return Card(
                margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                child: ListTile(
                  leading: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '#$position',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      UserAvatar(
                        user: User(
                          id: entry.userId,
                          email: '',
                          displayName: entry.userName,
                          avatarUrl: entry.avatarUrl,
                        ),
                        size: 40,
                      ),
                    ],
                  ),
                  title: Text(
                    entry.userName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text(_getSubtitleText(entry)),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _getScoreText(entry),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      Text(
                        _getScoreLabel(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }

        return const Center(
          child: Text('No leaderboard data available'),
        );
      },
    );
  }

  Widget _buildLeaderboardFilters(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          // Time period filter
          Text(
            'Time Period',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          ...['All Time', 'This Month', 'This Week', 'Today'].map((period) =>
            RadioListTile<String>(
              title: Text(period),
              value: period,
              groupValue: 'All Time', // TODO: Implement time period state
              onChanged: (value) {
                // TODO: Implement time period filtering
              },
              dense: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboardStats(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Ranking',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Row(
                    children: [
                      UserAvatar(
                        user: User(
                          id: 'current_user',
                          email: '<EMAIL>',
                          displayName: 'You',
                        ),
                        size: 50,
                      ),
                      const SizedBox(width: AppConstants.defaultPadding),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Your Position',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            Text(
                              '#42', // TODO: Get actual user position
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Score:', style: Theme.of(context).textTheme.bodyMedium),
                      Text('1,250', style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      )),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getScoreText(LeaderboardEntry entry) {
    switch (_selectedType) {
      case LeaderboardType.points:
        return '${entry.score}';
      case LeaderboardType.level:
        return 'L${entry.score}';
      case LeaderboardType.streaks:
        return '${entry.score} days';
      case LeaderboardType.achievements:
        return '${entry.score}';
    }
  }

  String _getScoreLabel() {
    switch (_selectedType) {
      case LeaderboardType.points:
        return 'points';
      case LeaderboardType.level:
        return 'level';
      case LeaderboardType.streaks:
        return 'streak';
      case LeaderboardType.achievements:
        return 'achievements';
    }
  }

  String _getSubtitleText(LeaderboardEntry entry) {
    // TODO: Add more detailed subtitle based on type
    return 'Active player';
  }

  void _loadLeaderboard() {
    context.read<GamificationBloc>().add(const LoadLeaderboard());
  }
}

/// Leaderboard types for filtering
enum LeaderboardType {
  points,
  level,
  streaks,
  achievements,
}
