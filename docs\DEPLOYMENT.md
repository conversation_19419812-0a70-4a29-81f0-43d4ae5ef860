# Deployment Guide

This guide covers deploying the Quester platform to various environments including development, staging, and production.

## Overview

Quester uses a multi-environment deployment strategy:

- **Development**: Local development with hot reload
- **Staging**: Testing environment with production-like configuration
- **Production**: Live environment with full monitoring and scaling

## Prerequisites

### Required <PERSON>ls
- <PERSON><PERSON> and Docker Compose
- Kubernetes CLI (kubectl) for production
- Cloud provider CLI (AWS CLI, Google Cloud SDK, etc.)
- Flutter SDK 3.10.0+
- PostgreSQL 13+

### Environment Variables

Create environment files for each environment:

```bash
# Development (.env.development)
DATABASE_URL=postgresql://user:password@localhost:5432/quester_dev
JWT_SECRET=your-dev-jwt-secret
REDIS_URL=redis://localhost:6379
API_BASE_URL=http://localhost:8080
WEB_BASE_URL=http://localhost:3000

# Staging (.env.staging)
DATABASE_URL=******************************************/quester_staging
JWT_SECRET=your-staging-jwt-secret
REDIS_URL=redis://staging-redis:6379
API_BASE_URL=https://api-staging.quester.app
WEB_BASE_URL=https://staging.quester.app

# Production (.env.production)
DATABASE_URL=***************************************/quester_prod
JWT_SECRET=your-production-jwt-secret
REDIS_URL=redis://prod-redis:6379
API_BASE_URL=https://api.quester.app
WEB_BASE_URL=https://quester.app
```

## Local Development

### Using Docker Compose

1. **Start all services**:
   ```bash
   docker-compose up -d
   ```

2. **Run database migrations**:
   ```bash
   docker-compose exec server dart run bin/migrate.dart
   ```

3. **Start Flutter app**:
   ```bash
   cd client && flutter run -d web-server --web-port 3000
   ```

### Manual Setup

1. **Start PostgreSQL**:
   ```bash
   pg_ctl -D /usr/local/var/postgres start
   createdb quester_dev
   ```

2. **Start Redis**:
   ```bash
   redis-server
   ```

3. **Start Dart server**:
   ```bash
   cd server
   dart pub get
   dart run bin/server.dart
   ```

4. **Start Flutter app**:
   ```bash
   cd client
   flutter pub get
   flutter run
   ```

## Staging Deployment

### Using Docker

1. **Build images**:
   ```bash
   # Build server image
   docker build -t quester-server:staging -f server/Dockerfile .
   
   # Build client image
   docker build -t quester-client:staging -f client/Dockerfile .
   ```

2. **Deploy with Docker Compose**:
   ```bash
   docker-compose -f docker-compose.staging.yml up -d
   ```

3. **Run migrations**:
   ```bash
   docker-compose -f docker-compose.staging.yml exec server dart run bin/migrate.dart
   ```

### Using Kubernetes

1. **Apply configurations**:
   ```bash
   kubectl apply -f k8s/staging/
   ```

2. **Verify deployment**:
   ```bash
   kubectl get pods -n quester-staging
   kubectl get services -n quester-staging
   ```

## Production Deployment

### AWS Deployment

#### Prerequisites
- AWS CLI configured
- EKS cluster set up
- RDS PostgreSQL instance
- ElastiCache Redis cluster
- Application Load Balancer

#### Deployment Steps

1. **Build and push images**:
   ```bash
   # Build images
   docker build -t quester-server:latest -f server/Dockerfile .
   docker build -t quester-client:latest -f client/Dockerfile .
   
   # Tag for ECR
   docker tag quester-server:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/quester-server:latest
   docker tag quester-client:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/quester-client:latest
   
   # Push to ECR
   aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-west-2.amazonaws.com
   docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/quester-server:latest
   docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/quester-client:latest
   ```

2. **Deploy to EKS**:
   ```bash
   # Apply Kubernetes configurations
   kubectl apply -f k8s/production/namespace.yaml
   kubectl apply -f k8s/production/secrets.yaml
   kubectl apply -f k8s/production/configmaps.yaml
   kubectl apply -f k8s/production/deployments.yaml
   kubectl apply -f k8s/production/services.yaml
   kubectl apply -f k8s/production/ingress.yaml
   ```

3. **Run database migrations**:
   ```bash
   kubectl run migration --image=123456789012.dkr.ecr.us-west-2.amazonaws.com/quester-server:latest --rm -it --restart=Never -- dart run bin/migrate.dart
   ```

4. **Verify deployment**:
   ```bash
   kubectl get pods -n quester-production
   kubectl get services -n quester-production
   kubectl get ingress -n quester-production
   ```

### Google Cloud Platform Deployment

#### Prerequisites
- Google Cloud SDK configured
- GKE cluster set up
- Cloud SQL PostgreSQL instance
- Cloud Memorystore Redis instance

#### Deployment Steps

1. **Build and push images**:
   ```bash
   # Configure Docker for GCR
   gcloud auth configure-docker
   
   # Build and push images
   docker build -t gcr.io/your-project/quester-server:latest -f server/Dockerfile .
   docker build -t gcr.io/your-project/quester-client:latest -f client/Dockerfile .
   
   docker push gcr.io/your-project/quester-server:latest
   docker push gcr.io/your-project/quester-client:latest
   ```

2. **Deploy to GKE**:
   ```bash
   # Get cluster credentials
   gcloud container clusters get-credentials quester-cluster --zone us-central1-a
   
   # Apply configurations
   kubectl apply -f k8s/production/
   ```

### Azure Deployment

#### Prerequisites
- Azure CLI configured
- AKS cluster set up
- Azure Database for PostgreSQL
- Azure Cache for Redis

#### Deployment Steps

1. **Build and push images**:
   ```bash
   # Login to ACR
   az acr login --name questerregistry
   
   # Build and push images
   docker build -t questerregistry.azurecr.io/quester-server:latest -f server/Dockerfile .
   docker build -t questerregistry.azurecr.io/quester-client:latest -f client/Dockerfile .
   
   docker push questerregistry.azurecr.io/quester-server:latest
   docker push questerregistry.azurecr.io/quester-client:latest
   ```

2. **Deploy to AKS**:
   ```bash
   # Get cluster credentials
   az aks get-credentials --resource-group quester-rg --name quester-cluster
   
   # Apply configurations
   kubectl apply -f k8s/production/
   ```

## Database Migrations

### Running Migrations

```bash
# Development
cd server && dart run bin/migrate.dart

# Docker
docker-compose exec server dart run bin/migrate.dart

# Kubernetes
kubectl run migration --image=quester-server:latest --rm -it --restart=Never -- dart run bin/migrate.dart
```

### Creating Migrations

```bash
# Create new migration
cd server && dart run bin/create_migration.dart add_user_preferences

# Migration file structure
migrations/
├── 001_initial_schema.sql
├── 002_add_quests_table.sql
├── 003_add_achievements_table.sql
└── 004_add_user_preferences.sql
```

## SSL/TLS Configuration

### Let's Encrypt with Cert-Manager

1. **Install cert-manager**:
   ```bash
   kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.12.0/cert-manager.yaml
   ```

2. **Create ClusterIssuer**:
   ```yaml
   apiVersion: cert-manager.io/v1
   kind: ClusterIssuer
   metadata:
     name: letsencrypt-prod
   spec:
     acme:
       server: https://acme-v02.api.letsencrypt.org/directory
       email: <EMAIL>
       privateKeySecretRef:
         name: letsencrypt-prod
       solvers:
       - http01:
           ingress:
             class: nginx
   ```

3. **Configure Ingress**:
   ```yaml
   apiVersion: networking.k8s.io/v1
   kind: Ingress
   metadata:
     name: quester-ingress
     annotations:
       cert-manager.io/cluster-issuer: letsencrypt-prod
       nginx.ingress.kubernetes.io/ssl-redirect: "true"
   spec:
     tls:
     - hosts:
       - quester.app
       - api.quester.app
       secretName: quester-tls
     rules:
     - host: quester.app
       http:
         paths:
         - path: /
           pathType: Prefix
           backend:
             service:
               name: quester-client
               port:
                 number: 80
     - host: api.quester.app
       http:
         paths:
         - path: /
           pathType: Prefix
           backend:
             service:
               name: quester-server
               port:
                 number: 8080
   ```

## Monitoring and Logging

### Prometheus and Grafana

1. **Install monitoring stack**:
   ```bash
   helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
   helm repo update
   
   helm install prometheus prometheus-community/kube-prometheus-stack \
     --namespace monitoring \
     --create-namespace
   ```

2. **Configure service monitors**:
   ```yaml
   apiVersion: monitoring.coreos.com/v1
   kind: ServiceMonitor
   metadata:
     name: quester-server
   spec:
     selector:
       matchLabels:
         app: quester-server
     endpoints:
     - port: metrics
       path: /metrics
   ```

### Logging with ELK Stack

1. **Install Elasticsearch and Kibana**:
   ```bash
   helm repo add elastic https://helm.elastic.co
   helm install elasticsearch elastic/elasticsearch --namespace logging --create-namespace
   helm install kibana elastic/kibana --namespace logging
   ```

2. **Configure Filebeat**:
   ```yaml
   apiVersion: beat.k8s.elastic.co/v1beta1
   kind: Filebeat
   metadata:
     name: quester-filebeat
   spec:
     config:
       filebeat.inputs:
       - type: container
         paths:
         - /var/log/containers/*quester*.log
       output.elasticsearch:
         hosts: ["elasticsearch-master:9200"]
   ```

## Backup and Recovery

### Database Backups

1. **Automated backups**:
   ```bash
   # Create backup script
   #!/bin/bash
   BACKUP_DIR="/backups"
   DATE=$(date +%Y%m%d_%H%M%S)
   
   pg_dump $DATABASE_URL > $BACKUP_DIR/quester_backup_$DATE.sql
   
   # Upload to S3
   aws s3 cp $BACKUP_DIR/quester_backup_$DATE.sql s3://quester-backups/
   
   # Clean up old backups (keep last 30 days)
   find $BACKUP_DIR -name "quester_backup_*.sql" -mtime +30 -delete
   ```

2. **Schedule with CronJob**:
   ```yaml
   apiVersion: batch/v1
   kind: CronJob
   metadata:
     name: database-backup
   spec:
     schedule: "0 2 * * *"  # Daily at 2 AM
     jobTemplate:
       spec:
         template:
           spec:
             containers:
             - name: backup
               image: postgres:13
               command: ["/bin/bash", "-c"]
               args:
               - |
                 pg_dump $DATABASE_URL > /backup/quester_backup_$(date +%Y%m%d_%H%M%S).sql
                 aws s3 cp /backup/quester_backup_*.sql s3://quester-backups/
             restartPolicy: OnFailure
   ```

### Disaster Recovery

1. **Database restoration**:
   ```bash
   # Download backup from S3
   aws s3 cp s3://quester-backups/quester_backup_20231201_020000.sql ./
   
   # Restore database
   psql $DATABASE_URL < quester_backup_20231201_020000.sql
   ```

2. **Application recovery**:
   ```bash
   # Rollback to previous version
   kubectl rollout undo deployment/quester-server
   kubectl rollout undo deployment/quester-client
   
   # Check rollout status
   kubectl rollout status deployment/quester-server
   kubectl rollout status deployment/quester-client
   ```

## Performance Optimization

### Database Optimization

1. **Connection pooling**:
   ```dart
   // server/lib/database/connection_pool.dart
   final pool = PostgreSQLConnectionPool(
     '************************************/database',
     minConnections: 5,
     maxConnections: 20,
   );
   ```

2. **Query optimization**:
   ```sql
   -- Add indexes for frequently queried columns
   CREATE INDEX idx_quests_user_id ON quests(created_by);
   CREATE INDEX idx_quests_status ON quests(status);
   CREATE INDEX idx_tasks_quest_id ON tasks(quest_id);
   ```

### Application Optimization

1. **Caching with Redis**:
   ```dart
   // Cache frequently accessed data
   final redis = Redis(connectionString: redisUrl);
   
   // Cache user data
   await redis.setex('user:$userId', 3600, jsonEncode(user.toJson()));
   
   // Cache quest lists
   await redis.setex('quests:$userId', 300, jsonEncode(quests));
   ```

2. **CDN configuration**:
   ```yaml
   # CloudFront distribution for static assets
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: cdn-config
   data:
     cdn_url: "https://d1234567890.cloudfront.net"
   ```

## Security Considerations

### Network Security

1. **Network policies**:
   ```yaml
   apiVersion: networking.k8s.io/v1
   kind: NetworkPolicy
   metadata:
     name: quester-network-policy
   spec:
     podSelector:
       matchLabels:
         app: quester-server
     policyTypes:
     - Ingress
     - Egress
     ingress:
     - from:
       - podSelector:
           matchLabels:
             app: quester-client
       ports:
       - protocol: TCP
         port: 8080
   ```

2. **Pod security policies**:
   ```yaml
   apiVersion: policy/v1beta1
   kind: PodSecurityPolicy
   metadata:
     name: quester-psp
   spec:
     privileged: false
     allowPrivilegeEscalation: false
     requiredDropCapabilities:
       - ALL
     volumes:
       - 'configMap'
       - 'emptyDir'
       - 'projected'
       - 'secret'
       - 'downwardAPI'
       - 'persistentVolumeClaim'
   ```

### Secrets Management

1. **Kubernetes secrets**:
   ```bash
   # Create secrets
   kubectl create secret generic quester-secrets \
     --from-literal=database-url="postgresql://..." \
     --from-literal=jwt-secret="..." \
     --from-literal=redis-url="redis://..."
   ```

2. **External secrets operator**:
   ```yaml
   apiVersion: external-secrets.io/v1beta1
   kind: SecretStore
   metadata:
     name: aws-secrets-manager
   spec:
     provider:
       aws:
         service: SecretsManager
         region: us-west-2
   ```

## Troubleshooting

### Common Issues

1. **Database connection issues**:
   ```bash
   # Check database connectivity
   kubectl exec -it deployment/quester-server -- psql $DATABASE_URL -c "SELECT 1"
   
   # Check connection pool status
   kubectl logs deployment/quester-server | grep "connection pool"
   ```

2. **Memory issues**:
   ```bash
   # Check memory usage
   kubectl top pods
   
   # Increase memory limits
   kubectl patch deployment quester-server -p '{"spec":{"template":{"spec":{"containers":[{"name":"server","resources":{"limits":{"memory":"1Gi"}}}]}}}}'
   ```

3. **SSL certificate issues**:
   ```bash
   # Check certificate status
   kubectl describe certificate quester-tls
   
   # Check cert-manager logs
   kubectl logs -n cert-manager deployment/cert-manager
   ```

### Health Checks

1. **Application health endpoints**:
   ```dart
   // server/lib/routes/health.dart
   router.get('/health', (Request request) {
     return Response.ok(jsonEncode({
       'status': 'healthy',
       'timestamp': DateTime.now().toIso8601String(),
       'version': '1.0.0',
     }));
   });
   ```

2. **Kubernetes probes**:
   ```yaml
   livenessProbe:
     httpGet:
       path: /health
       port: 8080
     initialDelaySeconds: 30
     periodSeconds: 10
   
   readinessProbe:
     httpGet:
       path: /ready
       port: 8080
     initialDelaySeconds: 5
     periodSeconds: 5
   ```

## Rollback Procedures

### Application Rollback

```bash
# Check deployment history
kubectl rollout history deployment/quester-server

# Rollback to previous version
kubectl rollout undo deployment/quester-server

# Rollback to specific revision
kubectl rollout undo deployment/quester-server --to-revision=2

# Check rollback status
kubectl rollout status deployment/quester-server
```

### Database Rollback

```bash
# Restore from backup
psql $DATABASE_URL < backup_before_migration.sql

# Run rollback migration
dart run bin/rollback_migration.dart --to-version=003
```

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly tasks**:
   - Review monitoring dashboards
   - Check error logs
   - Verify backup integrity
   - Update security patches

2. **Monthly tasks**:
   - Review resource usage
   - Update dependencies
   - Performance optimization
   - Security audit

3. **Quarterly tasks**:
   - Disaster recovery testing
   - Capacity planning
   - Architecture review
   - Cost optimization

### Emergency Contacts

- **On-call Engineer**: +1-555-0123
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **Management**: <EMAIL>

---

For additional support, refer to the [troubleshooting guide](TROUBLESHOOTING.md) or contact the DevOps team.
