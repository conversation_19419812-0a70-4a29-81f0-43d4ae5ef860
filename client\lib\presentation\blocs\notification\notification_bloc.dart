import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';
import '../../../data/repositories/api_repository.dart';
// Import specific SortDirection from notifications DTOs
import 'package:shared/src/dtos/notifications/notification_dtos.dart' as notification_dtos;

/// BLoC for managing notification-related state and operations
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final ApiRepository _repository;

  NotificationBloc({required ApiRepository repository})
      : _repository = repository,
        super(const NotificationInitial()) {
    on<LoadNotifications>(_onLoadNotifications);
    on<LoadNotificationPreferences>(_onLoadNotificationPreferences);
    on<UpdateNotificationPreferences>(_onUpdateNotificationPreferences);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<MarkAllNotificationsAsRead>(_onMarkAllNotificationsAsRead);
    on<DeleteNotification>(_onDeleteNotification);
    on<CreateNotification>(_onCreateNotification);
    on<CreateNotificationFromTemplate>(_onCreateNotificationFromTemplate);
    on<RegisterDevice>(_onRegisterDevice);
    on<LoadNotificationStats>(_onLoadNotificationStats);
    on<RefreshNotifications>(_onRefreshNotifications);
    on<NotificationReceived>(_onNotificationReceived);
  }

  Future<void> _onLoadNotifications(LoadNotifications event, Emitter<NotificationState> emit) async {
    try {
      if (event.isRefresh) {
        emit(const NotificationLoading());
      }
      
      final request = GetNotificationsRequest(
        status: event.status,
        type: event.type,
        category: event.category,
        priority: event.priority,
        isRead: event.isRead,
        startDate: event.startDate,
        endDate: event.endDate,
        page: event.page,
        limit: event.limit,
        sortBy: event.sortBy,
        sortDirection: event.sortDirection,
      );
      
      final response = await _repository.getNotifications(request);
      
      emit(NotificationsLoaded(
        notifications: response.notifications,
        pagination: response.pagination,
        unreadCount: response.unreadCount,
      ));
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onLoadNotificationPreferences(
    LoadNotificationPreferences event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(const NotificationLoading());
      
      final preferences = await _repository.getNotificationPreferences();
      
      emit(NotificationPreferencesLoaded(preferences: preferences));
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onUpdateNotificationPreferences(
    UpdateNotificationPreferences event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(const NotificationLoading());
      
      await _repository.updateNotificationPreferences(event.request);

      // Get the updated preferences
      final updatedPreferences = await _repository.getNotificationPreferences();

      emit(NotificationPreferencesUpdated(preferences: updatedPreferences));
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _repository.markNotificationsAsRead([event.notificationId]);
      
      emit(NotificationMarkedAsRead(notificationId: event.notificationId));
      
      // Reload notifications to update the list
      add(const RefreshNotifications());
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onMarkAllNotificationsAsRead(
    MarkAllNotificationsAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      // Get all unread notification IDs
      final currentState = state;
      if (currentState is NotificationsLoaded) {
        final unreadIds = currentState.notifications
            .where((n) => !n.isRead)
            .map((n) => n.id)
            .toList();
        
        if (unreadIds.isNotEmpty) {
          await _repository.markNotificationsAsRead(unreadIds);
          
          emit(const AllNotificationsMarkedAsRead());
          
          // Reload notifications to update the list
          add(const RefreshNotifications());
        }
      }
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onDeleteNotification(
    DeleteNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _repository.deleteNotification(event.notificationId);
      
      emit(NotificationDeleted(notificationId: event.notificationId));
      
      // Reload notifications to update the list
      add(const RefreshNotifications());
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onCreateNotification(
    CreateNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final notification = await _repository.createNotification(event.request);
      
      emit(NotificationCreated(notification: notification));
      
      // Reload notifications to update the list
      add(const RefreshNotifications());
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onCreateNotificationFromTemplate(
    CreateNotificationFromTemplate event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final notification = await _repository.createNotificationFromTemplate(event.request);
      
      emit(NotificationCreated(notification: notification));
      
      // Reload notifications to update the list
      add(const RefreshNotifications());
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onRegisterDevice(
    RegisterDevice event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _repository.registerDevice(event.request);
      
      emit(const DeviceRegistered());
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onLoadNotificationStats(
    LoadNotificationStats event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final stats = await _repository.getNotificationStats();
      
      emit(NotificationStatsLoaded(stats: stats));
    } catch (error) {
      emit(NotificationError(message: error.toString()));
    }
  }

  Future<void> _onRefreshNotifications(
    RefreshNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    add(const LoadNotifications(isRefresh: true));
  }

  Future<void> _onNotificationReceived(
    NotificationReceived event,
    Emitter<NotificationState> emit,
  ) async {
    emit(NewNotificationReceived(notification: event.notification));
    
    // Reload notifications to update the list
    add(const RefreshNotifications());
  }
}

/// Base class for all notification events
abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load notifications with optional filters
class LoadNotifications extends NotificationEvent {
  final NotificationStatus? status;
  final NotificationType? type;
  final NotificationCategory? category;
  final NotificationPriority? priority;
  final bool? isRead;
  final DateTime? startDate;
  final DateTime? endDate;
  final int page;
  final int limit;
  final String? sortBy;
  final notification_dtos.SortDirection? sortDirection;
  final bool isRefresh;

  const LoadNotifications({
    this.status,
    this.type,
    this.category,
    this.priority,
    this.isRead,
    this.startDate,
    this.endDate,
    this.page = 1,
    this.limit = 20,
    this.sortBy,
    this.sortDirection,
    this.isRefresh = false,
  });

  @override
  List<Object?> get props => [
        status,
        type,
        category,
        priority,
        isRead,
        startDate,
        endDate,
        page,
        limit,
        sortBy,
        sortDirection,
        isRefresh,
      ];
}

/// Event to load notification preferences
class LoadNotificationPreferences extends NotificationEvent {
  const LoadNotificationPreferences();
}

/// Event to update notification preferences
class UpdateNotificationPreferences extends NotificationEvent {
  final UpdateNotificationPreferencesRequest request;

  const UpdateNotificationPreferences({required this.request});

  @override
  List<Object> get props => [request];
}

/// Event to mark a notification as read
class MarkNotificationAsRead extends NotificationEvent {
  final String notificationId;

  const MarkNotificationAsRead({required this.notificationId});

  @override
  List<Object> get props => [notificationId];
}

/// Event to mark all notifications as read
class MarkAllNotificationsAsRead extends NotificationEvent {
  const MarkAllNotificationsAsRead();
}

/// Event to delete a notification
class DeleteNotification extends NotificationEvent {
  final String notificationId;

  const DeleteNotification({required this.notificationId});

  @override
  List<Object> get props => [notificationId];
}

/// Event to create a new notification
class CreateNotification extends NotificationEvent {
  final CreateNotificationRequest request;

  const CreateNotification({required this.request});

  @override
  List<Object> get props => [request];
}

/// Event to create notification from template
class CreateNotificationFromTemplate extends NotificationEvent {
  final CreateNotificationFromTemplateRequest request;

  const CreateNotificationFromTemplate({required this.request});

  @override
  List<Object> get props => [request];
}

/// Event to register device for push notifications
class RegisterDevice extends NotificationEvent {
  final RegisterDeviceRequest request;

  const RegisterDevice({required this.request});

  @override
  List<Object> get props => [request];
}

/// Event to load notification statistics
class LoadNotificationStats extends NotificationEvent {
  const LoadNotificationStats();
}

/// Event to refresh notifications
class RefreshNotifications extends NotificationEvent {
  const RefreshNotifications();
}

/// Event when a new notification is received
class NotificationReceived extends NotificationEvent {
  final Notification notification;

  const NotificationReceived({required this.notification});

  @override
  List<Object> get props => [notification];
}

/// Base class for all notification states
abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class NotificationInitial extends NotificationState {
  const NotificationInitial();
}

/// Loading state
class NotificationLoading extends NotificationState {
  const NotificationLoading();
}

/// State when notifications are successfully loaded
class NotificationsLoaded extends NotificationState {
  final List<Notification> notifications;
  final PaginationInfo pagination;
  final int unreadCount;

  const NotificationsLoaded({
    required this.notifications,
    required this.pagination,
    required this.unreadCount,
  });

  @override
  List<Object> get props => [notifications, pagination, unreadCount];
}

/// State when notification preferences are loaded
class NotificationPreferencesLoaded extends NotificationState {
  final NotificationPreferences preferences;

  const NotificationPreferencesLoaded({required this.preferences});

  @override
  List<Object> get props => [preferences];
}

/// State when notification preferences are updated
class NotificationPreferencesUpdated extends NotificationState {
  final NotificationPreferences preferences;

  const NotificationPreferencesUpdated({required this.preferences});

  @override
  List<Object> get props => [preferences];
}

/// State when a notification is marked as read
class NotificationMarkedAsRead extends NotificationState {
  final String notificationId;

  const NotificationMarkedAsRead({required this.notificationId});

  @override
  List<Object> get props => [notificationId];
}

/// State when all notifications are marked as read
class AllNotificationsMarkedAsRead extends NotificationState {
  const AllNotificationsMarkedAsRead();
}

/// State when a notification is deleted
class NotificationDeleted extends NotificationState {
  final String notificationId;

  const NotificationDeleted({required this.notificationId});

  @override
  List<Object> get props => [notificationId];
}

/// State when a notification is created
class NotificationCreated extends NotificationState {
  final Notification notification;

  const NotificationCreated({required this.notification});

  @override
  List<Object> get props => [notification];
}

/// State when device is registered
class DeviceRegistered extends NotificationState {
  const DeviceRegistered();
}

/// State when notification stats are loaded
class NotificationStatsLoaded extends NotificationState {
  final NotificationStatsResponse stats;

  const NotificationStatsLoaded({required this.stats});

  @override
  List<Object> get props => [stats];
}

/// State when a new notification is received
class NewNotificationReceived extends NotificationState {
  final Notification notification;

  const NewNotificationReceived({required this.notification});

  @override
  List<Object> get props => [notification];
}

/// Error state
class NotificationError extends NotificationState {
  final String message;

  const NotificationError({required this.message});

  @override
  List<Object> get props => [message];
}
