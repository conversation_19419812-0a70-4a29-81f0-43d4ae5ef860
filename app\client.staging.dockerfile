# Staging Dockerfile for Quester Client
FROM ghcr.io/cirruslabs/flutter:latest AS build

WORKDIR /app
RUN flutter config --enable-web
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY client/ ./client/

# Set working directory to client and build
WORKDIR /app/client
RUN flutter pub get
RUN flutter build web --release --web-renderer html

FROM nginx:latest AS staging
COPY --from=build /app/client/build/web /usr/share/nginx/html

RUN groupadd -g 1001 quester && \
    useradd -u 1001 -g 1001 -d /usr/share/nginx/html -s /sbin/nologin quester && \
    chown -R quester:quester /usr/share/nginx/html

EXPOSE 3000

CMD ["nginx", "-g", "daemon off;"]
