// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationTemplate _$NotificationTemplateFromJson(
  Map<String, dynamic> json,
) => NotificationTemplate(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  category: $enumDecode(_$NotificationCategoryEnumMap, json['category']),
  type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
  priority: $enumDecode(_$NotificationPriorityEnumMap, json['priority']),
  titleTemplate: json['titleTemplate'] as String,
  bodyTemplate: json['bodyTemplate'] as String,
  actionTextTemplate: json['actionTextTemplate'] as String?,
  actionUrlTemplate: json['actionUrlTemplate'] as String?,
  defaultDeliveryMethods: (json['defaultDeliveryMethods'] as List<dynamic>)
      .map((e) => $enumDecode(_$DeliveryMethodEnumMap, e))
      .toList(),
  variables:
      (json['variables'] as Map<String, dynamic>?)?.map(
        (k, e) =>
            MapEntry(k, TemplateVariable.fromJson(e as Map<String, dynamic>)),
      ) ??
      const {},
  icon: json['icon'] as String?,
  sound: json['sound'] as String?,
  vibrationPattern: (json['vibrationPattern'] as List<dynamic>?)
      ?.map((e) => (e as num).toInt())
      .toList(),
  showBadge: json['showBadge'] as bool? ?? true,
  expirationMinutes: (json['expirationMinutes'] as num?)?.toInt(),
  channelId: json['channelId'] as String?,
  isActive: json['isActive'] as bool? ?? true,
  supportedLanguages:
      (json['supportedLanguages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const ['en'],
  localizations: (json['localizations'] as Map<String, dynamic>?)?.map(
    (k, e) =>
        MapEntry(k, LocalizedTemplate.fromJson(e as Map<String, dynamic>)),
  ),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  createdBy: json['createdBy'] as String,
);

Map<String, dynamic> _$NotificationTemplateToJson(
  NotificationTemplate instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'category': _$NotificationCategoryEnumMap[instance.category]!,
  'type': _$NotificationTypeEnumMap[instance.type]!,
  'priority': _$NotificationPriorityEnumMap[instance.priority]!,
  'titleTemplate': instance.titleTemplate,
  'bodyTemplate': instance.bodyTemplate,
  'actionTextTemplate': instance.actionTextTemplate,
  'actionUrlTemplate': instance.actionUrlTemplate,
  'defaultDeliveryMethods': instance.defaultDeliveryMethods
      .map((e) => _$DeliveryMethodEnumMap[e]!)
      .toList(),
  'variables': instance.variables,
  'icon': instance.icon,
  'sound': instance.sound,
  'vibrationPattern': instance.vibrationPattern,
  'showBadge': instance.showBadge,
  'expirationMinutes': instance.expirationMinutes,
  'channelId': instance.channelId,
  'isActive': instance.isActive,
  'supportedLanguages': instance.supportedLanguages,
  'localizations': instance.localizations,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'createdBy': instance.createdBy,
};

const _$NotificationCategoryEnumMap = {
  NotificationCategory.system: 'system',
  NotificationCategory.quest: 'quest',
  NotificationCategory.achievement: 'achievement',
  NotificationCategory.social: 'social',
  NotificationCategory.reminder: 'reminder',
  NotificationCategory.marketing: 'marketing',
  NotificationCategory.security: 'security',
  NotificationCategory.team: 'team',
};

const _$NotificationTypeEnumMap = {
  NotificationType.questReminder: 'quest_reminder',
  NotificationType.taskReminder: 'task_reminder',
  NotificationType.deadlineAlert: 'deadline_alert',
  NotificationType.achievementUnlocked: 'achievement_unlocked',
  NotificationType.pointsEarned: 'points_earned',
  NotificationType.levelUp: 'level_up',
  NotificationType.messageReceived: 'message_received',
  NotificationType.collaborationInvite: 'collaboration_invite',
  NotificationType.questInvite: 'quest_invite',
  NotificationType.teamInvite: 'team_invite',
  NotificationType.systemAlert: 'system_alert',
  NotificationType.maintenanceNotice: 'maintenance_notice',
  NotificationType.updateAvailable: 'update_available',
  NotificationType.streakReminder: 'streak_reminder',
  NotificationType.leaderboardUpdate: 'leaderboard_update',
  NotificationType.rewardAvailable: 'reward_available',
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'low',
  NotificationPriority.normal: 'normal',
  NotificationPriority.high: 'high',
  NotificationPriority.urgent: 'urgent',
};

const _$DeliveryMethodEnumMap = {
  DeliveryMethod.inApp: 'in_app',
  DeliveryMethod.push: 'push',
  DeliveryMethod.email: 'email',
  DeliveryMethod.sms: 'sms',
  DeliveryMethod.webhook: 'webhook',
};

TemplateVariable _$TemplateVariableFromJson(Map<String, dynamic> json) =>
    TemplateVariable(
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$VariableTypeEnumMap, json['type']),
      required: json['required'] as bool? ?? false,
      defaultValue: json['defaultValue'],
      validationPattern: json['validationPattern'] as String?,
      exampleValue: json['exampleValue'],
    );

Map<String, dynamic> _$TemplateVariableToJson(TemplateVariable instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'type': _$VariableTypeEnumMap[instance.type]!,
      'required': instance.required,
      'defaultValue': instance.defaultValue,
      'validationPattern': instance.validationPattern,
      'exampleValue': instance.exampleValue,
    };

const _$VariableTypeEnumMap = {
  VariableType.string: 'string',
  VariableType.number: 'number',
  VariableType.boolean: 'boolean',
  VariableType.date: 'date',
  VariableType.url: 'url',
  VariableType.email: 'email',
  VariableType.userId: 'userId',
  VariableType.questId: 'questId',
  VariableType.taskId: 'taskId',
};

LocalizedTemplate _$LocalizedTemplateFromJson(Map<String, dynamic> json) =>
    LocalizedTemplate(
      titleTemplate: json['titleTemplate'] as String,
      bodyTemplate: json['bodyTemplate'] as String,
      actionTextTemplate: json['actionTextTemplate'] as String?,
      actionUrlTemplate: json['actionUrlTemplate'] as String?,
    );

Map<String, dynamic> _$LocalizedTemplateToJson(LocalizedTemplate instance) =>
    <String, dynamic>{
      'titleTemplate': instance.titleTemplate,
      'bodyTemplate': instance.bodyTemplate,
      'actionTextTemplate': instance.actionTextTemplate,
      'actionUrlTemplate': instance.actionUrlTemplate,
    };
