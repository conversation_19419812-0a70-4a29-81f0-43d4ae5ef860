import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../common/notification_badge.dart';

/// Rail navigation widget for tablet layouts
class RailNavigationWidget extends StatelessWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Whether the rail is extended
  final bool isExtended;
  
  /// Callback when extended state changes
  final ValueChanged<bool>? onExtendedChanged;
  
  /// Background color
  final Color? backgroundColor;
  
  /// Selected item color
  final Color? selectedColor;
  
  /// Unselected item color
  final Color? unselectedColor;

  const RailNavigationWidget({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.isExtended = false,
    this.onExtendedChanged,
    this.backgroundColor,
    this.selectedColor,
    this.unselectedColor,
  });

  @override
  Widget build(BuildContext context) {
    return NavigationRail(
      backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.surface,
      selectedIndex: selectedIndex,
      onDestinationSelected: onItemSelected,
      extended: isExtended,
      minWidth: AppConstants.railWidth,
      minExtendedWidth: AppConstants.extendedRailWidth,
      selectedIconTheme: IconThemeData(
        color: selectedColor ?? Theme.of(context).colorScheme.primary,
        size: 24,
      ),
      unselectedIconTheme: IconThemeData(
        color: unselectedColor ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        size: 24,
      ),
      selectedLabelTextStyle: Theme.of(context).textTheme.labelMedium?.copyWith(
        color: selectedColor ?? Theme.of(context).colorScheme.primary,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelTextStyle: Theme.of(context).textTheme.labelMedium?.copyWith(
        color: unselectedColor ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
      ),
      leading: _buildLeading(context),
      trailing: _buildTrailing(context),
      destinations: _buildDestinations(context),
    );
  }

  /// Build leading widget (toggle button)
  Widget? _buildLeading(context) {
    if (onExtendedChanged == null) return null;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: IconButton(
        onPressed: () => onExtendedChanged?.call(!isExtended),
        icon: AnimatedRotation(
          turns: isExtended ? 0.5 : 0,
          duration: AppConstants.mediumAnimation,
          child: const Icon(Icons.menu),
        ),
        tooltip: isExtended ? 'Collapse navigation' : 'Expand navigation',
      ),
    );
  }

  /// Build trailing widget (settings)
  Widget? _buildTrailing(BuildContext context) {
    return Expanded(
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
          child: IconButton(
            onPressed: () {
              // TODO: Navigate to settings
            },
            icon: const Icon(Icons.settings_outlined),
            tooltip: 'Settings',
          ),
        ),
      ),
    );
  }

  /// Build navigation destinations
  List<NavigationRailDestination> _buildDestinations(BuildContext context) {
    final items = _getNavigationItems();
    
    return items.map((item) {
      Widget icon = Icon(item.icon);
      Widget selectedIcon = Icon(item.selectedIcon);
      
      // Add badge if needed
      if (item.badgeCount != null && item.badgeCount! > 0) {
        icon = NotificationBadge(
          count: item.badgeCount!,
          child: icon,
        );
        selectedIcon = NotificationBadge(
          count: item.badgeCount!,
          child: selectedIcon,
        );
      }
      
      return NavigationRailDestination(
        icon: icon,
        selectedIcon: selectedIcon,
        label: Text(item.label),
        padding: const EdgeInsets.symmetric(vertical: 4),
      );
    }).toList();
  }

  /// Get navigation items configuration
  List<RailNavigationItemData> _getNavigationItems() {
    return [
      RailNavigationItemData(
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard,
        label: 'Dashboard',
      ),
      RailNavigationItemData(
        icon: Icons.explore_outlined,
        selectedIcon: Icons.explore,
        label: 'Quests',
      ),
      RailNavigationItemData(
        icon: Icons.task_outlined,
        selectedIcon: Icons.task,
        label: 'Tasks',
      ),
      RailNavigationItemData(
        icon: Icons.chat_bubble_outline,
        selectedIcon: Icons.chat_bubble,
        label: 'Messages',
        badgeCount: 3, // TODO: Get from state
      ),
      RailNavigationItemData(
        icon: Icons.leaderboard_outlined,
        selectedIcon: Icons.leaderboard,
        label: 'Leaderboard',
      ),
      RailNavigationItemData(
        icon: Icons.emoji_events_outlined,
        selectedIcon: Icons.emoji_events,
        label: 'Achievements',
      ),
    ];
  }
}

/// Rail navigation item data class
class RailNavigationItemData {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final int? badgeCount;

  const RailNavigationItemData({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    this.badgeCount,
  });
}

/// Custom rail navigation with more control
class CustomRailNavigation extends StatefulWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Whether the rail is extended
  final bool isExtended;
  
  /// Callback when extended state changes
  final ValueChanged<bool>? onExtendedChanged;
  
  /// Custom header widget
  final Widget? header;
  
  /// Custom footer widget
  final Widget? footer;

  const CustomRailNavigation({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.isExtended = false,
    this.onExtendedChanged,
    this.header,
    this.footer,
  });

  @override
  State<CustomRailNavigation> createState() => _CustomRailNavigationState();
}

class _CustomRailNavigationState extends State<CustomRailNavigation> {
  @override
  Widget build(BuildContext context) {
    final width = widget.isExtended 
        ? AppConstants.extendedRailWidth 
        : AppConstants.railWidth;

    return AnimatedContainer(
      duration: AppConstants.mediumAnimation,
      width: width,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          if (widget.header != null) widget.header!,
          _buildToggleButton(context),
          Expanded(
            child: _buildNavigationItems(context),
          ),
          if (widget.footer != null) widget.footer!,
        ],
      ),
    );
  }

  /// Build toggle button
  Widget _buildToggleButton(BuildContext context) {
    if (widget.onExtendedChanged == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      child: IconButton(
        onPressed: () => widget.onExtendedChanged?.call(!widget.isExtended),
        icon: AnimatedRotation(
          turns: widget.isExtended ? 0.5 : 0,
          duration: AppConstants.mediumAnimation,
          child: const Icon(Icons.menu),
        ),
        tooltip: widget.isExtended ? 'Collapse navigation' : 'Expand navigation',
      ),
    );
  }

  /// Build navigation items
  Widget _buildNavigationItems(BuildContext context) {
    final items = _getNavigationItems();
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final isSelected = index == widget.selectedIndex;
        
        return _CustomRailNavigationItem(
          item: item,
          isSelected: isSelected,
          isExtended: widget.isExtended,
          onTap: () => widget.onItemSelected?.call(index),
        );
      },
    );
  }

  /// Get navigation items configuration
  List<RailNavigationItemData> _getNavigationItems() {
    return [
      RailNavigationItemData(
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard,
        label: 'Dashboard',
      ),
      RailNavigationItemData(
        icon: Icons.explore_outlined,
        selectedIcon: Icons.explore,
        label: 'Quests',
      ),
      RailNavigationItemData(
        icon: Icons.task_outlined,
        selectedIcon: Icons.task,
        label: 'Tasks',
      ),
      RailNavigationItemData(
        icon: Icons.chat_bubble_outline,
        selectedIcon: Icons.chat_bubble,
        label: 'Messages',
        badgeCount: 3,
      ),
      RailNavigationItemData(
        icon: Icons.leaderboard_outlined,
        selectedIcon: Icons.leaderboard,
        label: 'Leaderboard',
      ),
      RailNavigationItemData(
        icon: Icons.emoji_events_outlined,
        selectedIcon: Icons.emoji_events,
        label: 'Achievements',
      ),
    ];
  }
}

/// Individual custom rail navigation item
class _CustomRailNavigationItem extends StatefulWidget {
  final RailNavigationItemData item;
  final bool isSelected;
  final bool isExtended;
  final VoidCallback? onTap;

  const _CustomRailNavigationItem({
    required this.item,
    required this.isSelected,
    required this.isExtended,
    this.onTap,
  });

  @override
  State<_CustomRailNavigationItem> createState() => _CustomRailNavigationItemState();
}

class _CustomRailNavigationItemState extends State<_CustomRailNavigationItem> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 4,
        vertical: 2,
      ),
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        child: AnimatedContainer(
          duration: AppConstants.shortAnimation,
          decoration: BoxDecoration(
            color: widget.isSelected
                ? Theme.of(context).colorScheme.primaryContainer
                : _isHovered
                    ? Theme.of(context).colorScheme.surfaceContainerHighest
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          child: InkWell(
            onTap: widget.onTap,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.smallPadding,
                vertical: AppConstants.defaultPadding,
              ),
              child: widget.isExtended
                  ? _buildExtendedItem(context)
                  : _buildCompactItem(context),
            ),
          ),
        ),
      ),
    );
  }

  /// Build compact item (icon only)
  Widget _buildCompactItem(BuildContext context) {
    final color = widget.isSelected
        ? Theme.of(context).colorScheme.onPrimaryContainer
        : Theme.of(context).colorScheme.onSurface;

    Widget iconWidget = Icon(
      widget.isSelected ? widget.item.selectedIcon : widget.item.icon,
      color: color,
      size: 24,
    );

    if (widget.item.badgeCount != null && widget.item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: widget.item.badgeCount!,
        child: iconWidget,
      );
    }

    return Tooltip(
      message: widget.item.label,
      preferBelow: false,
      child: Center(child: iconWidget),
    );
  }

  /// Build extended item (icon + label)
  Widget _buildExtendedItem(BuildContext context) {
    final color = widget.isSelected
        ? Theme.of(context).colorScheme.onPrimaryContainer
        : Theme.of(context).colorScheme.onSurface;

    Widget iconWidget = Icon(
      widget.isSelected ? widget.item.selectedIcon : widget.item.icon,
      color: color,
      size: 24,
    );

    if (widget.item.badgeCount != null && widget.item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: widget.item.badgeCount!,
        child: iconWidget,
      );
    }

    return Row(
      children: [
        iconWidget,
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: AnimatedOpacity(
            opacity: widget.isExtended ? 1.0 : 0.0,
            duration: AppConstants.shortAnimation,
            child: Text(
              widget.item.label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Floating rail navigation with modern design
class FloatingRailNavigation extends StatelessWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Margin from screen edges
  final EdgeInsets margin;

  const FloatingRailNavigation({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.margin = const EdgeInsets.all(AppConstants.defaultPadding),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
        child: Container(
          width: AppConstants.railWidth,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _buildFloatingItems(context),
          ),
        ),
      ),
    );
  }

  /// Build floating navigation items
  List<Widget> _buildFloatingItems(BuildContext context) {
    final items = _getNavigationItems();
    
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final isSelected = index == selectedIndex;
      
      return _FloatingRailNavigationItem(
        item: item,
        isSelected: isSelected,
        onTap: () => onItemSelected?.call(index),
      );
    }).toList();
  }

  /// Get navigation items configuration
  List<RailNavigationItemData> _getNavigationItems() {
    return [
      RailNavigationItemData(
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard,
        label: 'Dashboard',
      ),
      RailNavigationItemData(
        icon: Icons.explore_outlined,
        selectedIcon: Icons.explore,
        label: 'Quests',
      ),
      RailNavigationItemData(
        icon: Icons.task_outlined,
        selectedIcon: Icons.task,
        label: 'Tasks',
      ),
      RailNavigationItemData(
        icon: Icons.chat_bubble_outline,
        selectedIcon: Icons.chat_bubble,
        label: 'Messages',
        badgeCount: 3,
      ),
      RailNavigationItemData(
        icon: Icons.person_outline,
        selectedIcon: Icons.person,
        label: 'Profile',
      ),
    ];
  }
}

/// Individual floating rail navigation item
class _FloatingRailNavigationItem extends StatelessWidget {
  final RailNavigationItemData item;
  final bool isSelected;
  final VoidCallback? onTap;

  const _FloatingRailNavigationItem({
    required this.item,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final color = isSelected 
        ? Theme.of(context).colorScheme.primary
        : Theme.of(context).colorScheme.onSurface.withOpacity(0.6);

    Widget iconWidget = AnimatedSwitcher(
      duration: AppConstants.shortAnimation,
      child: Icon(
        isSelected ? item.selectedIcon : item.icon,
        key: ValueKey(isSelected),
        color: color,
        size: 24,
      ),
    );

    if (item.badgeCount != null && item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: item.badgeCount!,
        child: iconWidget,
      );
    }

    return Tooltip(
      message: item.label,
      preferBelow: false,
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: AppConstants.mediumAnimation,
          curve: Curves.easeInOut,
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          child: iconWidget,
        ),
      ),
    );
  }
}
