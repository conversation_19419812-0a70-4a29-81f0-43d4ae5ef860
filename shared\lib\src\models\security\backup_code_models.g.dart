// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backup_code_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BackupCode _$BackupCodeFromJson(Map<String, dynamic> json) => BackupCode(
  id: json['id'] as String,
  userId: json['user_id'] as String,
  codeHash: json['code_hash'] as String,
  createdAt: DateTime.parse(json['created_at'] as String),
  usedAt: json['used_at'] == null
      ? null
      : DateTime.parse(json['used_at'] as String),
  isUsed: json['is_used'] as bool? ?? false,
  isActive: json['is_active'] as bool? ?? true,
  revokedAt: json['revoked_at'] == null
      ? null
      : DateTime.parse(json['revoked_at'] as String),
  revocationReason: json['revocation_reason'] as String?,
  ipAddress: json['ip_address'] as String?,
  userAgent: json['user_agent'] as String?,
  usedIpAddress: json['used_ip_address'] as String?,
  usedUserAgent: json['used_user_agent'] as String?,
);

Map<String, dynamic> _$BackupCodeToJson(BackupCode instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'code_hash': instance.codeHash,
      'created_at': instance.createdAt.toIso8601String(),
      'used_at': instance.usedAt?.toIso8601String(),
      'is_used': instance.isUsed,
      'is_active': instance.isActive,
      'revoked_at': instance.revokedAt?.toIso8601String(),
      'revocation_reason': instance.revocationReason,
      'ip_address': instance.ipAddress,
      'user_agent': instance.userAgent,
      'used_ip_address': instance.usedIpAddress,
      'used_user_agent': instance.usedUserAgent,
    };

BackupCodeSet _$BackupCodeSetFromJson(Map<String, dynamic> json) =>
    BackupCodeSet(
      codes: (json['codes'] as List<dynamic>).map((e) => e as String).toList(),
      generatedAt: DateTime.parse(json['generated_at'] as String),
      userId: json['user_id'] as String,
      remainingCodes: (json['remaining_codes'] as num).toInt(),
      displayHint: json['display_hint'] as String?,
    );

Map<String, dynamic> _$BackupCodeSetToJson(BackupCodeSet instance) =>
    <String, dynamic>{
      'codes': instance.codes,
      'generated_at': instance.generatedAt.toIso8601String(),
      'user_id': instance.userId,
      'remaining_codes': instance.remainingCodes,
      'display_hint': instance.displayHint,
    };

BackupCodeValidationResult _$BackupCodeValidationResultFromJson(
  Map<String, dynamic> json,
) => BackupCodeValidationResult(
  isValid: json['is_valid'] as bool,
  backupCodeId: json['backup_code_id'] as String?,
  errorMessage: json['error_message'] as String?,
  warningMessage: json['warning_message'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  validatedAt: DateTime.parse(json['validated_at'] as String),
  remainingCodes: (json['remaining_codes'] as num?)?.toInt(),
  codeConsumed: json['code_consumed'] as bool? ?? false,
);

Map<String, dynamic> _$BackupCodeValidationResultToJson(
  BackupCodeValidationResult instance,
) => <String, dynamic>{
  'is_valid': instance.isValid,
  'backup_code_id': instance.backupCodeId,
  'error_message': instance.errorMessage,
  'warning_message': instance.warningMessage,
  'metadata': instance.metadata,
  'validated_at': instance.validatedAt.toIso8601String(),
  'remaining_codes': instance.remainingCodes,
  'code_consumed': instance.codeConsumed,
};

BackupCodeGenerationConfig _$BackupCodeGenerationConfigFromJson(
  Map<String, dynamic> json,
) => BackupCodeGenerationConfig(
  codeCount: (json['code_count'] as num?)?.toInt() ?? 10,
  codeLength: (json['code_length'] as num?)?.toInt() ?? 8,
  includeHyphens: json['include_hyphens'] as bool? ?? true,
  charset: json['charset'] as String? ?? '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  maxCodesPerUser: (json['max_codes_per_user'] as num?)?.toInt() ?? 10,
  codeLifetimeDays: (json['code_lifetime_days'] as num?)?.toInt() ?? 365,
  revokeExistingCodes: json['revoke_existing_codes'] as bool? ?? true,
);

Map<String, dynamic> _$BackupCodeGenerationConfigToJson(
  BackupCodeGenerationConfig instance,
) => <String, dynamic>{
  'code_count': instance.codeCount,
  'code_length': instance.codeLength,
  'include_hyphens': instance.includeHyphens,
  'charset': instance.charset,
  'max_codes_per_user': instance.maxCodesPerUser,
  'code_lifetime_days': instance.codeLifetimeDays,
  'revoke_existing_codes': instance.revokeExistingCodes,
};

BackupCodeStatus _$BackupCodeStatusFromJson(Map<String, dynamic> json) =>
    BackupCodeStatus(
      hasActiveCodes: json['has_active_codes'] as bool,
      totalActiveCodes: (json['total_active_codes'] as num).toInt(),
      availableCodes: (json['available_codes'] as num).toInt(),
      usedCodes: (json['used_codes'] as num).toInt(),
      lastGenerated: json['last_generated'] == null
          ? null
          : DateTime.parse(json['last_generated'] as String),
      lastUsed: json['last_used'] == null
          ? null
          : DateTime.parse(json['last_used'] as String),
      needsRegeneration: json['needs_regeneration'] as bool? ?? false,
      lowCodesWarning: json['low_codes_warning'] as bool? ?? false,
    );

Map<String, dynamic> _$BackupCodeStatusToJson(BackupCodeStatus instance) =>
    <String, dynamic>{
      'has_active_codes': instance.hasActiveCodes,
      'total_active_codes': instance.totalActiveCodes,
      'available_codes': instance.availableCodes,
      'used_codes': instance.usedCodes,
      'last_generated': instance.lastGenerated?.toIso8601String(),
      'last_used': instance.lastUsed?.toIso8601String(),
      'needs_regeneration': instance.needsRegeneration,
      'low_codes_warning': instance.lowCodesWarning,
    };

GenerateBackupCodesRequest _$GenerateBackupCodesRequestFromJson(
  Map<String, dynamic> json,
) => GenerateBackupCodesRequest(
  config: json['config'] == null
      ? null
      : BackupCodeGenerationConfig.fromJson(
          json['config'] as Map<String, dynamic>,
        ),
  revokeExisting: json['revoke_existing'] as bool? ?? true,
  reason: json['reason'] as String?,
);

Map<String, dynamic> _$GenerateBackupCodesRequestToJson(
  GenerateBackupCodesRequest instance,
) => <String, dynamic>{
  'config': instance.config,
  'revoke_existing': instance.revokeExisting,
  'reason': instance.reason,
};

ValidateBackupCodeRequest _$ValidateBackupCodeRequestFromJson(
  Map<String, dynamic> json,
) => ValidateBackupCodeRequest(
  code: json['code'] as String,
  consumeOnUse: json['consume_on_use'] as bool? ?? true,
);

Map<String, dynamic> _$ValidateBackupCodeRequestToJson(
  ValidateBackupCodeRequest instance,
) => <String, dynamic>{
  'code': instance.code,
  'consume_on_use': instance.consumeOnUse,
};

RevokeBackupCodesRequest _$RevokeBackupCodesRequestFromJson(
  Map<String, dynamic> json,
) => RevokeBackupCodesRequest(
  specificCodes: (json['specific_codes'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  reason: json['reason'] as String? ?? 'Manual revocation',
);

Map<String, dynamic> _$RevokeBackupCodesRequestToJson(
  RevokeBackupCodesRequest instance,
) => <String, dynamic>{
  'specific_codes': instance.specificCodes,
  'reason': instance.reason,
};

BackupCodesResponse _$BackupCodesResponseFromJson(Map<String, dynamic> json) =>
    BackupCodesResponse(
      data: BackupCodeSet.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String,
      success: json['success'] as bool? ?? true,
    );

Map<String, dynamic> _$BackupCodesResponseToJson(
  BackupCodesResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

BackupCodeValidationResponse _$BackupCodeValidationResponseFromJson(
  Map<String, dynamic> json,
) => BackupCodeValidationResponse(
  data: BackupCodeValidationResult.fromJson(
    json['data'] as Map<String, dynamic>,
  ),
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$BackupCodeValidationResponseToJson(
  BackupCodeValidationResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

BackupCodeStatusResponse _$BackupCodeStatusResponseFromJson(
  Map<String, dynamic> json,
) => BackupCodeStatusResponse(
  data: BackupCodeStatus.fromJson(json['data'] as Map<String, dynamic>),
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$BackupCodeStatusResponseToJson(
  BackupCodeStatusResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};
