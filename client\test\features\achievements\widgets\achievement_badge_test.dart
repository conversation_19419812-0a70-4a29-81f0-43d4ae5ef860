import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quester_client/presentation/widgets/gamification/achievement_widget.dart';

void main() {
  group('AchievementBadge Widget Tests', () {
    testWidgets('displays achievement badge with direct parameters', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AchievementBadge(
              title: 'Test Achievement',
              description: 'Test Description',
              rarity: 'common',
              icon: Icons.emoji_events,
              isUnlocked: true,
            ),
          ),
        ),
      );

      // Verify achievement name is displayed
      expect(find.text('Test Achievement'), findsOneWidget);
      
      // Verify points are displayed
      expect(find.text('100 pts'), findsOneWidget);
    });

    testWidgets('displays achievement badge with achievement object', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AchievementBadge(
              title: 'Mock Achievement',
              description: 'Mock Description',
              rarity: 'rare',
              icon: Icons.star,
              isUnlocked: false,
            ),
          ),
        ),
      );

      // Verify achievement badge is displayed
      expect(find.byType(AchievementBadge), findsOneWidget);
    });

    testWidgets('displays compact achievement badge', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AchievementBadge(
              title: 'Compact Achievement',
              description: 'Compact Description',
              rarity: 'epic',
              icon: Icons.diamond,
              isUnlocked: true,
              size: 60,
            ),
          ),
        ),
      );

      // Verify compact badge is displayed
      expect(find.byType(AchievementBadge), findsOneWidget);
    });
  });
}
