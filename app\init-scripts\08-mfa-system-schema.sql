-- Complete MFA System Schema
-- Includes policies, challenges, sessions, and temporary verifications

-- MFA method enum
CREATE TYPE mfa_method AS ENUM ('totp', 'sms', 'email', 'backup_code', 'trusted_device');

-- MFA policy type enum
CREATE TYPE mfa_policy_type AS ENUM ('always', 'risk_based', 'device_based', 'time_based', 'location_based');

-- MFA enforcement level enum
CREATE TYPE mfa_enforcement_level AS ENUM ('disabled', 'optional', 'required', 'strict');

-- MFA bypass reason enum
CREATE TYPE mfa_bypass_reason AS ENUM ('trusted_device', 'emergency_access', 'admin_override', 'temporary_exception');

-- MFA Policies table
CREATE TABLE IF NOT EXISTS mfa_policies (
    id VARCHAR(64) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    organization_id UUID NULL,
    type mfa_policy_type NOT NULL DEFAULT 'always',
    enforcement_level mfa_enforcement_level NOT NULL DEFAULT 'required',
    allowed_methods mfa_method[] NOT NULL DEFAULT ARRAY['totp', 'sms', 'email']::mfa_method[],
    conditions JSONB NOT NULL DEFAULT '{}',
    settings JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 100,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_by VARCHAR(255) NULL,
    
    CONSTRAINT fk_mfa_policies_organization 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_mfa_policies_name 
        CHECK (LENGTH(name) >= 3 AND LENGTH(name) <= 255),
    
    CONSTRAINT chk_mfa_policies_priority 
        CHECK (priority >= 1 AND priority <= 1000),
    
    CONSTRAINT chk_mfa_policies_allowed_methods 
        CHECK (array_length(allowed_methods, 1) > 0)
);

-- MFA Challenges table
CREATE TABLE IF NOT EXISTS mfa_challenges (
    id VARCHAR(64) PRIMARY KEY,
    user_id UUID NOT NULL,
    method mfa_method NOT NULL,
    challenge_data VARCHAR(255) NULL,
    destination_hint VARCHAR(255) NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_completed BOOLEAN NOT NULL DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE NULL,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    metadata JSONB NOT NULL DEFAULT '{}',
    ip_address INET NULL,
    user_agent TEXT NULL,
    
    CONSTRAINT fk_mfa_challenges_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_mfa_challenges_expires 
        CHECK (expires_at > created_at),
    
    CONSTRAINT chk_mfa_challenges_attempts 
        CHECK (attempt_count >= 0 AND attempt_count <= max_attempts),
    
    CONSTRAINT chk_mfa_challenges_completed 
        CHECK (
            (is_completed = false AND completed_at IS NULL) OR 
            (is_completed = true AND completed_at IS NOT NULL)
        )
);

-- MFA Sessions table
CREATE TABLE IF NOT EXISTS mfa_sessions (
    token VARCHAR(128) PRIMARY KEY,
    user_id UUID NOT NULL,
    method mfa_method NOT NULL,
    device_fingerprint VARCHAR(256) NULL,
    ip_address INET NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_accessed TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    revoked_at TIMESTAMP WITH TIME ZONE NULL,
    revocation_reason TEXT NULL,
    metadata JSONB NOT NULL DEFAULT '{}',
    
    CONSTRAINT fk_mfa_sessions_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_mfa_sessions_expires 
        CHECK (expires_at > created_at),
    
    CONSTRAINT chk_mfa_sessions_revocation 
        CHECK (
            (is_revoked = false AND revoked_at IS NULL) OR 
            (is_revoked = true AND revoked_at IS NOT NULL)
        )
);

-- Temporary Verifications table (for phone/email verification during setup)
CREATE TABLE IF NOT EXISTS temp_verifications (
    id VARCHAR(64) PRIMARY KEY,
    user_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL,
    destination VARCHAR(255) NOT NULL,
    code VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE NULL,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    
    CONSTRAINT fk_temp_verifications_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_temp_verifications_expires 
        CHECK (expires_at > created_at),
    
    CONSTRAINT chk_temp_verifications_attempts 
        CHECK (attempt_count >= 0 AND attempt_count <= max_attempts),
    
    CONSTRAINT chk_temp_verifications_verified 
        CHECK (
            (is_verified = false AND verified_at IS NULL) OR 
            (is_verified = true AND verified_at IS NOT NULL)
        ),
    
    UNIQUE (user_id, type)
);

-- MFA Recovery Requests table
CREATE TABLE IF NOT EXISTS mfa_recovery_requests (
    id VARCHAR(64) PRIMARY KEY,
    user_id UUID NOT NULL,
    recovery_method VARCHAR(50) NOT NULL,
    recovery_destination VARCHAR(255) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_completed BOOLEAN NOT NULL DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE NULL,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    ip_address INET NULL,
    user_agent TEXT NULL,
    metadata JSONB NOT NULL DEFAULT '{}',
    
    CONSTRAINT fk_mfa_recovery_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_mfa_recovery_expires 
        CHECK (expires_at > created_at),
    
    CONSTRAINT chk_mfa_recovery_attempts 
        CHECK (attempt_count >= 0 AND attempt_count <= max_attempts),
    
    CONSTRAINT chk_mfa_recovery_completed 
        CHECK (
            (is_completed = false AND completed_at IS NULL) OR 
            (is_completed = true AND completed_at IS NOT NULL)
        )
);

-- Indexes for MFA Policies
CREATE INDEX IF NOT EXISTS idx_mfa_policies_organization 
    ON mfa_policies(organization_id);

CREATE INDEX IF NOT EXISTS idx_mfa_policies_active 
    ON mfa_policies(is_active, priority) 
    WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_mfa_policies_type 
    ON mfa_policies(type, enforcement_level);

-- Indexes for MFA Challenges
CREATE INDEX IF NOT EXISTS idx_mfa_challenges_user_id 
    ON mfa_challenges(user_id);

CREATE INDEX IF NOT EXISTS idx_mfa_challenges_active 
    ON mfa_challenges(user_id, is_completed, expires_at) 
    WHERE is_completed = false;

CREATE INDEX IF NOT EXISTS idx_mfa_challenges_method 
    ON mfa_challenges(method, created_at);

CREATE INDEX IF NOT EXISTS idx_mfa_challenges_cleanup 
    ON mfa_challenges(expires_at, is_completed);

-- Indexes for MFA Sessions
CREATE UNIQUE INDEX IF NOT EXISTS idx_mfa_sessions_token 
    ON mfa_sessions(token) 
    WHERE is_revoked = false;

CREATE INDEX IF NOT EXISTS idx_mfa_sessions_user_id 
    ON mfa_sessions(user_id);

CREATE INDEX IF NOT EXISTS idx_mfa_sessions_expires 
    ON mfa_sessions(expires_at) 
    WHERE is_revoked = false;

CREATE INDEX IF NOT EXISTS idx_mfa_sessions_device 
    ON mfa_sessions(device_fingerprint, user_id);

-- Indexes for Temporary Verifications
CREATE INDEX IF NOT EXISTS idx_temp_verifications_user_type 
    ON temp_verifications(user_id, type);

CREATE INDEX IF NOT EXISTS idx_temp_verifications_cleanup 
    ON temp_verifications(expires_at, is_verified);

-- Indexes for MFA Recovery
CREATE INDEX IF NOT EXISTS idx_mfa_recovery_user_id 
    ON mfa_recovery_requests(user_id);

CREATE INDEX IF NOT EXISTS idx_mfa_recovery_active 
    ON mfa_recovery_requests(user_id, is_completed, expires_at) 
    WHERE is_completed = false;

-- MFA Statistics View
CREATE OR REPLACE VIEW mfa_user_stats AS
SELECT 
    u.id as user_id,
    u.email,
    -- TOTP Status
    CASE WHEN t.is_verified = true THEN true ELSE false END as totp_enabled,
    -- Backup Codes Status
    COALESCE(bc.active_codes, 0) as backup_codes_count,
    COALESCE(bc.used_codes, 0) as backup_codes_used,
    -- Trusted Devices Status
    COALESCE(td.active_devices, 0) as trusted_devices_count,
    -- MFA Session Status
    COALESCE(ms.active_sessions, 0) as active_mfa_sessions,
    -- Last Activity
    GREATEST(
        t.last_used,
        bc.last_used,
        td.last_activity,
        ms.last_accessed
    ) as last_mfa_activity,
    -- Security Score (0-1)
    CASE 
        WHEN t.is_verified = true AND bc.active_codes > 2 THEN 1.0
        WHEN t.is_verified = true AND bc.active_codes > 0 THEN 0.8
        WHEN t.is_verified = true THEN 0.6
        WHEN bc.active_codes > 0 THEN 0.4
        ELSE 0.0
    END as security_score
FROM users u
LEFT JOIN totp_secrets t ON u.id = t.user_id
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) FILTER (WHERE is_active = true AND is_used = false) as active_codes,
        COUNT(*) FILTER (WHERE is_used = true) as used_codes,
        MAX(used_at) as last_used
    FROM mfa_backup_codes 
    GROUP BY user_id
) bc ON u.id = bc.user_id
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) FILTER (WHERE status = 'active') as active_devices,
        MAX(last_seen) as last_activity
    FROM trusted_devices 
    GROUP BY user_id
) td ON u.id = td.user_id
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) FILTER (WHERE is_revoked = false AND expires_at > NOW()) as active_sessions,
        MAX(last_accessed) as last_accessed
    FROM mfa_sessions 
    GROUP BY user_id
) ms ON u.id = ms.user_id;

-- Functions for MFA management

-- Function to update MFA policy priority
CREATE OR REPLACE FUNCTION update_mfa_policy_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for MFA policies timestamp update
DROP TRIGGER IF EXISTS trigger_update_mfa_policy_timestamp ON mfa_policies;
CREATE TRIGGER trigger_update_mfa_policy_timestamp
    BEFORE UPDATE ON mfa_policies
    FOR EACH ROW
    EXECUTE FUNCTION update_mfa_policy_timestamp();

-- Function to cleanup expired MFA data
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_data()
RETURNS TABLE (
    expired_challenges INTEGER,
    expired_sessions INTEGER,
    expired_verifications INTEGER,
    expired_recovery_requests INTEGER
) AS $$
DECLARE
    challenges_count INTEGER := 0;
    sessions_count INTEGER := 0;
    verifications_count INTEGER := 0;
    recovery_count INTEGER := 0;
BEGIN
    -- Cleanup expired challenges
    DELETE FROM mfa_challenges 
    WHERE expires_at < NOW() AND is_completed = false;
    GET DIAGNOSTICS challenges_count = ROW_COUNT;
    
    -- Cleanup expired sessions
    UPDATE mfa_sessions 
    SET is_revoked = true, revoked_at = NOW(), revocation_reason = 'Expired'
    WHERE expires_at < NOW() AND is_revoked = false;
    GET DIAGNOSTICS sessions_count = ROW_COUNT;
    
    -- Cleanup expired temporary verifications
    DELETE FROM temp_verifications 
    WHERE expires_at < NOW() AND is_verified = false;
    GET DIAGNOSTICS verifications_count = ROW_COUNT;
    
    -- Cleanup expired recovery requests
    DELETE FROM mfa_recovery_requests 
    WHERE expires_at < NOW() AND is_completed = false;
    GET DIAGNOSTICS recovery_count = ROW_COUNT;
    
    RETURN QUERY SELECT challenges_count, sessions_count, verifications_count, recovery_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user MFA status
CREATE OR REPLACE FUNCTION get_user_mfa_status(p_user_id UUID)
RETURNS TABLE (
    mfa_enabled BOOLEAN,
    totp_configured BOOLEAN,
    backup_codes_available BOOLEAN,
    trusted_devices_count INTEGER,
    active_sessions_count INTEGER,
    security_level TEXT,
    recommendations TEXT[]
) AS $$
DECLARE
    user_stats RECORD;
    recommendations_array TEXT[] := ARRAY[]::TEXT[];
BEGIN
    SELECT * INTO user_stats FROM mfa_user_stats WHERE user_id = p_user_id;
    
    IF user_stats IS NULL THEN
        RETURN QUERY SELECT false, false, false, 0, 0, 'None', ARRAY['Enable MFA for better security']::TEXT[];
        RETURN;
    END IF;
    
    -- Generate recommendations
    IF NOT user_stats.totp_enabled THEN
        recommendations_array := array_append(recommendations_array, 'Enable TOTP authentication app');
    END IF;
    
    IF user_stats.backup_codes_count = 0 THEN
        recommendations_array := array_append(recommendations_array, 'Generate backup codes');
    ELSIF user_stats.backup_codes_count <= 2 THEN
        recommendations_array := array_append(recommendations_array, 'Generate new backup codes (running low)');
    END IF;
    
    IF user_stats.trusted_devices_count = 0 THEN
        recommendations_array := array_append(recommendations_array, 'Consider trusting frequently used devices');
    ELSIF user_stats.trusted_devices_count > 5 THEN
        recommendations_array := array_append(recommendations_array, 'Review and remove unused trusted devices');
    END IF;
    
    RETURN QUERY SELECT 
        user_stats.totp_enabled OR user_stats.backup_codes_count > 0,
        user_stats.totp_enabled,
        user_stats.backup_codes_count > 0,
        user_stats.trusted_devices_count,
        user_stats.active_mfa_sessions,
        CASE 
            WHEN user_stats.security_score >= 0.9 THEN 'Excellent'
            WHEN user_stats.security_score >= 0.7 THEN 'Good'
            WHEN user_stats.security_score >= 0.5 THEN 'Fair'
            WHEN user_stats.security_score >= 0.3 THEN 'Poor'
            ELSE 'Critical'
        END,
        CASE WHEN array_length(recommendations_array, 1) IS NULL 
             THEN ARRAY['Your MFA setup is complete']::TEXT[]
             ELSE recommendations_array 
        END;
END;
$$ LANGUAGE plpgsql;

-- Function to revoke all user MFA sessions
CREATE OR REPLACE FUNCTION revoke_user_mfa_sessions(p_user_id UUID, p_reason TEXT DEFAULT 'Admin revocation')
RETURNS INTEGER AS $$
DECLARE
    affected_count INTEGER;
BEGIN
    UPDATE mfa_sessions 
    SET is_revoked = true, revoked_at = NOW(), revocation_reason = p_reason
    WHERE user_id = p_user_id AND is_revoked = false;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    -- Log the action
    INSERT INTO security_audit_log (id, user_id, event_type, event_details, created_at)
    VALUES (
        'mfa_sessions_revoke_' || extract(epoch from now()),
        p_user_id,
        'mfa_sessions_bulk_revoked',
        json_build_object('revoked_sessions', affected_count, 'reason', p_reason),
        NOW()
    );
    
    RETURN affected_count;
END;
$$ LANGUAGE plpgsql;

-- Security policies (Row Level Security)
ALTER TABLE mfa_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE temp_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_recovery_requests ENABLE ROW LEVEL SECURITY;

-- Policies for MFA Policies (admin only)
CREATE POLICY mfa_policies_admin_access ON mfa_policies
    FOR ALL 
    TO admin_users
    USING (true);

-- Policies for MFA Challenges (users can only access their own)
CREATE POLICY mfa_challenges_user_access ON mfa_challenges
    FOR ALL 
    TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

CREATE POLICY mfa_challenges_admin_access ON mfa_challenges
    FOR ALL 
    TO admin_users
    USING (true);

-- Policies for MFA Sessions (users can only access their own)
CREATE POLICY mfa_sessions_user_access ON mfa_sessions
    FOR ALL 
    TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

CREATE POLICY mfa_sessions_admin_access ON mfa_sessions
    FOR ALL 
    TO admin_users
    USING (true);

-- Policies for Temporary Verifications (users can only access their own)
CREATE POLICY temp_verifications_user_access ON temp_verifications
    FOR ALL 
    TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

-- Policies for Recovery Requests (users can only access their own)
CREATE POLICY mfa_recovery_user_access ON mfa_recovery_requests
    FOR ALL 
    TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

CREATE POLICY mfa_recovery_admin_access ON mfa_recovery_requests
    FOR ALL 
    TO admin_users
    USING (true);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON mfa_policies TO admin_users;
GRANT SELECT, INSERT, UPDATE ON mfa_challenges TO authenticated_users;
GRANT ALL ON mfa_challenges TO admin_users;
GRANT SELECT, INSERT, UPDATE ON mfa_sessions TO authenticated_users;
GRANT ALL ON mfa_sessions TO admin_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON temp_verifications TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON mfa_recovery_requests TO authenticated_users;
GRANT ALL ON mfa_recovery_requests TO admin_users;
GRANT SELECT ON mfa_user_stats TO authenticated_users;
GRANT ALL ON mfa_user_stats TO admin_users;

-- Insert default MFA policy for development
DO $$
BEGIN
    -- Only insert default policy if in development mode
    IF current_setting('app.environment', true) = 'development' THEN
        INSERT INTO mfa_policies (
            id,
            name,
            organization_id,
            type,
            enforcement_level,
            allowed_methods,
            conditions,
            settings,
            is_active,
            priority,
            created_by
        ) VALUES (
            'default_policy_' || extract(epoch from now()),
            'Default MFA Policy',
            NULL,
            'risk_based',
            'required',
            ARRAY['totp', 'sms', 'email', 'backup_code']::mfa_method[],
            '{"risk_threshold": 0.5, "allowed_hours": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}'::jsonb,
            '{"auto_trust_after_mfa": true, "session_duration_hours": 24}'::jsonb,
            true,
            100,
            'system_init'
        ) ON CONFLICT (id) DO NOTHING;
        
        RAISE NOTICE 'Inserted default MFA policy for development';
    END IF;
END $$;

-- Log the completion
DO $$
BEGIN
    RAISE NOTICE 'Complete MFA system schema initialized successfully';
    RAISE NOTICE 'Tables: mfa_policies, mfa_challenges, mfa_sessions, temp_verifications, mfa_recovery_requests';
    RAISE NOTICE 'Views: mfa_user_stats';
    RAISE NOTICE 'Enums: mfa_method, mfa_policy_type, mfa_enforcement_level, mfa_bypass_reason';
    RAISE NOTICE 'Functions: update_mfa_policy_timestamp, cleanup_expired_mfa_data, get_user_mfa_status, revoke_user_mfa_sessions';
    RAISE NOTICE 'Security: Row Level Security enabled with user and admin policies';
END $$;