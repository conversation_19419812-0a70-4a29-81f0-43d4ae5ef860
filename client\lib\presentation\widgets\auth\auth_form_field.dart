import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Custom form field widget for authentication screens
class AuthFormField extends StatefulWidget {
  /// Text controller for the field
  final TextEditingController controller;
  
  /// Label text
  final String label;
  
  /// Hint text
  final String? hintText;
  
  /// Whether to obscure text (for passwords)
  final bool obscureText;
  
  /// Keyboard type
  final TextInputType keyboardType;
  
  /// Text input action
  final TextInputAction textInputAction;
  
  /// Prefix icon
  final IconData? prefixIcon;
  
  /// Suffix icon widget
  final Widget? suffixIcon;
  
  /// Validator function
  final String? Function(String?)? validator;
  
  /// Whether the field is enabled
  final bool enabled;
  
  /// Maximum lines
  final int maxLines;
  
  /// Auto focus
  final bool autofocus;
  
  /// On field submitted callback
  final VoidCallback? onFieldSubmitted;
  
  /// On changed callback
  final ValueChanged<String>? onChanged;

  const AuthFormField({
    super.key,
    required this.controller,
    required this.label,
    this.hintText,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.enabled = true,
    this.maxLines = 1,
    this.autofocus = false,
    this.onFieldSubmitted,
    this.onChanged,
  });

  @override
  State<AuthFormField> createState() => _AuthFormFieldState();
}

class _AuthFormFieldState extends State<AuthFormField> {
  bool _isFocused = false;
  bool _hasError = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel(context),
        const SizedBox(height: AppConstants.smallPadding),
        _buildTextField(context),
      ],
    );
  }

  /// Build label
  Widget _buildLabel(BuildContext context) {
    return Text(
      widget.label,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
        color: _hasError
            ? Theme.of(context).colorScheme.error
            : _isFocused
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  /// Build text field
  Widget _buildTextField(BuildContext context) {
    return Focus(
      onFocusChange: (focused) {
        setState(() {
          _isFocused = focused;
        });
      },
      child: TextFormField(
        controller: widget.controller,
        obscureText: widget.obscureText,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        enabled: widget.enabled,
        maxLines: widget.maxLines,
        autofocus: widget.autofocus,
        onFieldSubmitted: widget.onFieldSubmitted != null 
            ? (_) => widget.onFieldSubmitted!()
            : null,
        onChanged: (value) {
          widget.onChanged?.call(value);
          // Clear error state when user starts typing
          if (_hasError) {
            setState(() {
              _hasError = false;
            });
          }
        },
        validator: (value) {
          final error = widget.validator?.call(value);
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _hasError = error != null;
              });
            }
          });
          return error;
        },
        style: Theme.of(context).textTheme.bodyLarge,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          prefixIcon: widget.prefixIcon != null
              ? Icon(
                  widget.prefixIcon,
                  color: _hasError
                      ? Theme.of(context).colorScheme.error
                      : _isFocused
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                )
              : null,
          suffixIcon: widget.suffixIcon,
          filled: true,
          fillColor: _hasError
              ? Theme.of(context).colorScheme.error.withOpacity(0.05)
              : _isFocused
                  ? Theme.of(context).colorScheme.primary.withOpacity(0.05)
                  : Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.5),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.error,
              width: 2,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.error,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.defaultPadding,
          ),
        ),
      ),
    );
  }
}

/// Password form field with built-in visibility toggle
class PasswordFormField extends StatefulWidget {
  /// Text controller for the field
  final TextEditingController controller;
  
  /// Label text
  final String label;
  
  /// Hint text
  final String? hintText;
  
  /// Validator function
  final String? Function(String?)? validator;
  
  /// Whether the field is enabled
  final bool enabled;
  
  /// Auto focus
  final bool autofocus;
  
  /// On field submitted callback
  final VoidCallback? onFieldSubmitted;
  
  /// On changed callback
  final ValueChanged<String>? onChanged;
  
  /// Whether to show password strength indicator
  final bool showStrengthIndicator;

  const PasswordFormField({
    super.key,
    required this.controller,
    required this.label,
    this.hintText,
    this.validator,
    this.enabled = true,
    this.autofocus = false,
    this.onFieldSubmitted,
    this.onChanged,
    this.showStrengthIndicator = false,
  });

  @override
  State<PasswordFormField> createState() => _PasswordFormFieldState();
}

class _PasswordFormFieldState extends State<PasswordFormField> {
  bool _obscureText = true;
  PasswordStrength _strength = PasswordStrength.weak;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AuthFormField(
          controller: widget.controller,
          label: widget.label,
          hintText: widget.hintText,
          obscureText: _obscureText,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off),
            onPressed: () => setState(() => _obscureText = !_obscureText),
          ),
          validator: widget.validator,
          enabled: widget.enabled,
          autofocus: widget.autofocus,
          onFieldSubmitted: widget.onFieldSubmitted,
          onChanged: (value) {
            widget.onChanged?.call(value);
            if (widget.showStrengthIndicator) {
              setState(() {
                _strength = _calculatePasswordStrength(value);
              });
            }
          },
        ),
        if (widget.showStrengthIndicator) ...[
          const SizedBox(height: AppConstants.smallPadding),
          _buildPasswordStrengthIndicator(context),
        ],
      ],
    );
  }

  /// Build password strength indicator
  Widget _buildPasswordStrengthIndicator(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: _getStrengthValue(),
                backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(_getStrengthColor(context)),
                minHeight: 4,
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Text(
              _getStrengthText(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _getStrengthColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        if (_strength != PasswordStrength.strong) ...[
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _getStrengthHint(),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ],
    );
  }

  /// Calculate password strength
  PasswordStrength _calculatePasswordStrength(String password) {
    if (password.length < 6) return PasswordStrength.weak;
    
    int score = 0;
    if (password.length >= 8) score++;
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;
    
    if (score >= 4) return PasswordStrength.strong;
    if (score >= 2) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }

  /// Get strength value for progress indicator
  double _getStrengthValue() {
    switch (_strength) {
      case PasswordStrength.weak:
        return 0.33;
      case PasswordStrength.medium:
        return 0.66;
      case PasswordStrength.strong:
        return 1.0;
    }
  }

  /// Get strength color
  Color _getStrengthColor(BuildContext context) {
    switch (_strength) {
      case PasswordStrength.weak:
        return Theme.of(context).colorScheme.error;
      case PasswordStrength.medium:
        return Colors.orange;
      case PasswordStrength.strong:
        return Colors.green;
    }
  }

  /// Get strength text
  String _getStrengthText() {
    switch (_strength) {
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.medium:
        return 'Medium';
      case PasswordStrength.strong:
        return 'Strong';
    }
  }

  /// Get strength hint
  String _getStrengthHint() {
    switch (_strength) {
      case PasswordStrength.weak:
        return 'Use at least 8 characters with uppercase, lowercase, numbers, and symbols';
      case PasswordStrength.medium:
        return 'Add more character types for better security';
      case PasswordStrength.strong:
        return '';
    }
  }
}

/// Password strength enumeration
enum PasswordStrength {
  weak,
  medium,
  strong,
}
