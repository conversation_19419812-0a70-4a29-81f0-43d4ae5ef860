import 'dart:convert';

import 'package:shelf/shelf.dart';
import 'package:shelf_web_socket/shelf_web_socket.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// WebSocket service for real-time gamification features
/// Handles live leaderboard updates, achievement notifications, and activity feeds
class WebSocketService {
  static const String version = '1.0.0';
  
  // Connected clients organized by subscription type
  static final Map<String, Set<WebSocketChannel>> _subscribers = {};
  static final Map<WebSocketChannel, Map<String, dynamic>> _clientInfo = {};

  // User tracking - maps user IDs to their WebSocket connections
  static final Map<String, Set<WebSocketChannel>> _userConnections = {};

  // Room management - maps room IDs to connected clients
  static final Map<String, Set<WebSocketChannel>> _roomConnections = {};

  // Presence tracking - maps user IDs to their presence status
  static final Map<String, Map<String, dynamic>> _userPresence = {};

  // Performance tracking
  static int _totalConnections = 0;
  static int _activeConnections = 0;
  static final List<String> _connectionHistory = [];

  // Heartbeat tracking
  static final Map<WebSocketChannel, DateTime> _lastHeartbeat = {};
  
  /// Initialize WebSocket service
  static void initialize() {
    print('🔗 Initializing WebSocket Service v$version');
    _initializeSubscriptionTypes();
  }
  
  /// Initialize subscription types
  static void _initializeSubscriptionTypes() {
    final subscriptionTypes = [
      'leaderboard',
      'achievements', 
      'user_activity',
      'global_activity',
      'notifications',
      'system_updates',
      'collaboration',
      'freelancing',
      'learning',
      'enterprise',
      'real_time_updates',
      'chat_messages',
      'project_updates',
      'task_updates',
      'quest_updates',
    ];
    
    for (final type in subscriptionTypes) {
      _subscribers[type] = <WebSocketChannel>{};
    }
    
    print('📊 Initialized ${subscriptionTypes.length} subscription types');
  }
  
  /// Create WebSocket handler
  static Handler getWebSocketHandler() {
    return webSocketHandler((WebSocketChannel webSocket, String? protocol) {
      _totalConnections++;
      _activeConnections++;

      final connectionId = 'ws_${_totalConnections}_${DateTime.now().millisecondsSinceEpoch}';
      _connectionHistory.add('[$connectionId] Connected at ${DateTime.now().toIso8601String()}');

      print('🔗 New WebSocket connection: $connectionId (Active: $_activeConnections)');

      // Initialize client info
      _clientInfo[webSocket] = {
        'id': connectionId,
        'connected_at': DateTime.now().toIso8601String(),
        'subscriptions': <String>[],
        'user_id': null,
        'last_activity': DateTime.now().toIso8601String(),
      };

      // Send welcome message
      _sendMessage(webSocket, {
        'type': 'welcome',
        'connection_id': connectionId,
        'server_version': version,
        'timestamp': DateTime.now().toIso8601String(),
        'available_subscriptions': _subscribers.keys.toList(),
      });

      // Handle incoming messages
      webSocket.stream.listen(
        (message) => _handleMessage(webSocket, message),
        onError: (error) => _handleError(webSocket, error),
        onDone: () => _handleDisconnection(webSocket),
      );
    });
  }
  
  /// Handle incoming WebSocket messages
  static void _handleMessage(WebSocketChannel webSocket, dynamic message) {
    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;
      final messageType = data['type'] as String?;
      
      // Update client activity
      if (_clientInfo.containsKey(webSocket)) {
        _clientInfo[webSocket]!['last_activity'] = DateTime.now().toIso8601String();
      }
      
      switch (messageType) {
        case 'subscribe':
          _handleSubscription(webSocket, data);
          break;
        case 'unsubscribe':
          _handleUnsubscription(webSocket, data);
          break;
        case 'ping':
          _handlePing(webSocket, data);
          break;
        case 'user_auth':
          _handleUserAuth(webSocket, data);
          break;
        case 'join_room':
          _handleJoinRoom(webSocket, data);
          break;
        case 'leave_room':
          _handleLeaveRoom(webSocket, data);
          break;
        case 'update_presence':
          _handlePresenceUpdate(webSocket, data);
          break;
        case 'get_status':
          _sendStatus(webSocket);
          break;
        default:
          _sendError(webSocket, 'Unknown message type: $messageType');
      }
    } catch (e) {
      _sendError(webSocket, 'Invalid message format: $e');
    }
  }
  
  /// Handle subscription requests
  static void _handleSubscription(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final subscriptionType = data['subscription'] as String?;
    final userId = data['user_id'] as String?;
    
    if (subscriptionType == null || !_subscribers.containsKey(subscriptionType)) {
      _sendError(webSocket, 'Invalid subscription type: $subscriptionType');
      return;
    }
    
    // Add client to subscription
    _subscribers[subscriptionType]!.add(webSocket);
    
    // Update client info
    if (_clientInfo.containsKey(webSocket)) {
      final subscriptions = _clientInfo[webSocket]!['subscriptions'] as List<String>;
      if (!subscriptions.contains(subscriptionType)) {
        subscriptions.add(subscriptionType);
      }
      
      if (userId != null) {
        _clientInfo[webSocket]!['user_id'] = userId;
      }
    }
    
    _sendMessage(webSocket, {
      'type': 'subscription_confirmed',
      'subscription': subscriptionType,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    print('📡 Client subscribed to: $subscriptionType${userId != null ? ' (User: $userId)' : ''}');
  }
  
  /// Handle unsubscription requests
  static void _handleUnsubscription(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final subscriptionType = data['subscription'] as String?;
    
    if (subscriptionType == null || !_subscribers.containsKey(subscriptionType)) {
      _sendError(webSocket, 'Invalid subscription type: $subscriptionType');
      return;
    }
    
    // Remove client from subscription
    _subscribers[subscriptionType]!.remove(webSocket);
    
    // Update client info
    if (_clientInfo.containsKey(webSocket)) {
      final subscriptions = _clientInfo[webSocket]!['subscriptions'] as List<String>;
      subscriptions.remove(subscriptionType);
    }
    
    _sendMessage(webSocket, {
      'type': 'unsubscription_confirmed',
      'subscription': subscriptionType,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    print('📡 Client unsubscribed from: $subscriptionType');
  }
  
  /// Handle ping messages (heartbeat)
  static void _handlePing(WebSocketChannel webSocket, Map<String, dynamic> data) {
    _sendMessage(webSocket, {
      'type': 'pong',
      'timestamp': DateTime.now().toIso8601String(),
      'server_time': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  /// Handle user authentication
  static void _handleUserAuth(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final userId = data['user_id'] as String?;
    // final token = data['token'] as String?; // TODO: Implement token validation

    if (userId == null) {
      _sendError(webSocket, 'User ID is required for authentication');
      return;
    }

    // Simple auth validation (in real app, validate JWT token)
    if (_clientInfo.containsKey(webSocket)) {
      _clientInfo[webSocket]!['user_id'] = userId;
      _clientInfo[webSocket]!['authenticated'] = true;
      _clientInfo[webSocket]!['rooms'] = <String>[];

      // Track user connection
      _userConnections.putIfAbsent(userId, () => <WebSocketChannel>{});
      _userConnections[userId]!.add(webSocket);

      // Initialize user presence
      _userPresence[userId] = {
        'status': 'online',
        'last_seen': DateTime.now().toIso8601String(),
        'activity': 'active',
        'device_info': data['device_info'],
      };

      // Update heartbeat
      _lastHeartbeat[webSocket] = DateTime.now();
    }

    _sendMessage(webSocket, {
      'type': 'auth_success',
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Broadcast presence update
    broadcastPresenceUpdate(userId, _userPresence[userId]!);

    print('🔐 User authenticated: $userId');
  }

  /// Handle room join requests
  static void _handleJoinRoom(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final roomId = data['room_id'] as String?;
    final userId = _clientInfo[webSocket]?['user_id'] as String?;

    if (roomId == null) {
      _sendError(webSocket, 'Room ID is required');
      return;
    }

    if (userId == null) {
      _sendError(webSocket, 'User must be authenticated to join rooms');
      return;
    }

    // Add client to room
    _roomConnections.putIfAbsent(roomId, () => <WebSocketChannel>{});
    _roomConnections[roomId]!.add(webSocket);

    // Update client info
    final clientRooms = _clientInfo[webSocket]!['rooms'] as List<String>;
    if (!clientRooms.contains(roomId)) {
      clientRooms.add(roomId);
    }

    _sendMessage(webSocket, {
      'type': 'room_joined',
      'room_id': roomId,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Notify other room members
    _broadcastToRoom(roomId, {
      'type': 'user_joined_room',
      'room_id': roomId,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    }, exclude: webSocket);

    print('🏠 User $userId joined room: $roomId');
  }

  /// Handle room leave requests
  static void _handleLeaveRoom(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final roomId = data['room_id'] as String?;
    final userId = _clientInfo[webSocket]?['user_id'] as String?;

    if (roomId == null) {
      _sendError(webSocket, 'Room ID is required');
      return;
    }

    if (userId == null) {
      _sendError(webSocket, 'User must be authenticated');
      return;
    }

    // Remove client from room
    _roomConnections[roomId]?.remove(webSocket);
    if (_roomConnections[roomId]?.isEmpty == true) {
      _roomConnections.remove(roomId);
    }

    // Update client info
    final clientRooms = _clientInfo[webSocket]!['rooms'] as List<String>;
    clientRooms.remove(roomId);

    _sendMessage(webSocket, {
      'type': 'room_left',
      'room_id': roomId,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Notify other room members
    _broadcastToRoom(roomId, {
      'type': 'user_left_room',
      'room_id': roomId,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    });

    print('🚪 User $userId left room: $roomId');
  }

  /// Handle presence updates
  static void _handlePresenceUpdate(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final userId = _clientInfo[webSocket]?['user_id'] as String?;

    if (userId == null) {
      _sendError(webSocket, 'User must be authenticated');
      return;
    }

    final status = data['status'] as String?;
    final activity = data['activity'] as String?;
    final statusMessage = data['status_message'] as String?;

    // Update user presence
    _userPresence[userId] = {
      ..._userPresence[userId] ?? {},
      if (status != null) 'status': status,
      if (activity != null) 'activity': activity,
      if (statusMessage != null) 'status_message': statusMessage,
      'last_seen': DateTime.now().toIso8601String(),
    };

    _sendMessage(webSocket, {
      'type': 'presence_updated',
      'presence': _userPresence[userId],
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Broadcast presence update
    broadcastPresenceUpdate(userId, _userPresence[userId]!);

    print('👤 Presence updated for user: $userId');
  }

  /// Send status information
  static void _sendStatus(WebSocketChannel webSocket) {
    final clientInfo = _clientInfo[webSocket];
    
    _sendMessage(webSocket, {
      'type': 'status',
      'client_info': clientInfo,
      'server_stats': {
        'total_connections': _totalConnections,
        'active_connections': _activeConnections,
        'subscription_counts': _subscribers.map((k, v) => MapEntry(k, v.length)),
      },
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Handle connection errors
  static void _handleError(WebSocketChannel webSocket, dynamic error) {
    print('❌ WebSocket error: $error');
    _cleanupConnection(webSocket);
  }
  
  /// Handle client disconnection
  static void _handleDisconnection(WebSocketChannel webSocket) {
    final clientInfo = _clientInfo[webSocket];
    final clientId = clientInfo?['id'] ?? 'unknown';
    final userId = clientInfo?['user_id'] as String?;

    print('🔌 WebSocket disconnected: $clientId');

    // Remove from user connections
    if (userId != null) {
      _userConnections[userId]?.remove(webSocket);
      if (_userConnections[userId]?.isEmpty == true) {
        _userConnections.remove(userId);

        // Update user presence to offline if no more connections
        _userPresence[userId] = {
          ..._userPresence[userId] ?? {},
          'status': 'offline',
          'last_seen': DateTime.now().toIso8601String(),
        };

        // Broadcast offline status
        broadcastPresenceUpdate(userId, _userPresence[userId]!);
      }
    }

    // Remove from all rooms
    final clientRooms = clientInfo?['rooms'] as List<String>? ?? <String>[];
    for (final roomId in clientRooms) {
      _roomConnections[roomId]?.remove(webSocket);
      if (_roomConnections[roomId]?.isEmpty == true) {
        _roomConnections.remove(roomId);
      }

      // Notify room members of user leaving
      if (userId != null) {
        _broadcastToRoom(roomId, {
          'type': 'user_left_room',
          'room_id': roomId,
          'user_id': userId,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    }

    // Clean up heartbeat tracking
    _lastHeartbeat.remove(webSocket);

    _cleanupConnection(webSocket);
  }

  /// Broadcast message to all clients in a specific room
  static void _broadcastToRoom(String roomId, Map<String, dynamic> message, {WebSocketChannel? exclude}) {
    final roomClients = _roomConnections[roomId];
    if (roomClients == null || roomClients.isEmpty) return;

    final messageJson = jsonEncode(message);
    int sentCount = 0;

    for (final client in roomClients.toList()) {
      if (exclude != null && client == exclude) continue;

      try {
        client.sink.add(messageJson);
        sentCount++;
      } catch (e) {
        print('❌ Failed to send message to room client: $e');
        // Remove dead connection
        roomClients.remove(client);
        _handleDisconnection(client);
      }
    }

    print('📡 Broadcasted to room $roomId: ${message['type']} (sent to $sentCount clients)');
  }

  /// Clean up connection resources
  static void _cleanupConnection(WebSocketChannel webSocket) {
    // Remove from all subscriptions
    for (final subscribers in _subscribers.values) {
      subscribers.remove(webSocket);
    }
    
    // Remove client info
    _clientInfo.remove(webSocket);
    
    // Update active connections count
    _activeConnections--;
    
    print('🧹 Cleaned up connection. Active connections: $_activeConnections');
  }
  
  /// Broadcast message to all subscribers of a specific type
  static void broadcast(String subscriptionType, Map<String, dynamic> message) {
    if (!_subscribers.containsKey(subscriptionType)) {
      print('⚠️  Unknown subscription type: $subscriptionType');
      return;
    }
    
    final subscribers = _subscribers[subscriptionType]!;
    final activeSubscribers = <WebSocketChannel>[];
    
    message['timestamp'] = DateTime.now().toIso8601String();
    message['subscription_type'] = subscriptionType;
    
    for (final client in subscribers) {
      try {
        _sendMessage(client, message);
        activeSubscribers.add(client);
      } catch (e) {
        print('❌ Failed to send to client: $e');
        // Client will be cleaned up automatically on next message attempt
      }
    }
    
    // Update active subscribers (remove failed ones)
    _subscribers[subscriptionType] = activeSubscribers.toSet();
    
    print('📡 Broadcasted $subscriptionType to ${activeSubscribers.length} clients');
  }
  
  /// Send message to specific client
  static void _sendMessage(WebSocketChannel webSocket, Map<String, dynamic> message) {
    try {
      webSocket.sink.add(jsonEncode(message));
    } catch (e) {
      print('❌ Failed to send message: $e');
      _cleanupConnection(webSocket);
    }
  }
  
  /// Send error message to client
  static void _sendError(WebSocketChannel webSocket, String error) {
    _sendMessage(webSocket, {
      'type': 'error',
      'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Get service statistics
  static Map<String, dynamic> getStats() {
    return {
      'version': version,
      'total_connections': _totalConnections,
      'active_connections': _activeConnections,
      'subscription_types': _subscribers.keys.toList(),
      'subscription_counts': _subscribers.map((k, v) => MapEntry(k, v.length)),
      'recent_connections': _connectionHistory.take(10).toList(),
    };
  }
  
  /// Broadcast leaderboard update
  static void broadcastLeaderboardUpdate(Map<String, dynamic> leaderboardData) {
    broadcast('leaderboard', {
      'type': 'leaderboard_update',
      'data': leaderboardData,
    });
  }
  
  /// Broadcast achievement unlock
  static void broadcastAchievementUnlock(String userId, Map<String, dynamic> achievement) {
    broadcast('achievements', {
      'type': 'achievement_unlock',
      'user_id': userId,
      'achievement': achievement,
    });
  }
  
  /// Broadcast user activity
  static void broadcastUserActivity(Map<String, dynamic> activity) {
    broadcast('user_activity', {
      'type': 'user_activity',
      'activity': activity,
    });
  }
  
  /// Broadcast global activity feed update
  static void broadcastGlobalActivity(Map<String, dynamic> activity) {
    broadcast('global_activity', {
      'type': 'global_activity',
      'activity': activity,
    });
  }
  
  /// Broadcast system notification
  static void broadcastNotification(Map<String, dynamic> notification) {
    broadcast('notifications', {
      'type': 'notification',
      'notification': notification,
    });
  }
  
  /// Send message to specific user (if connected)
  static void sendToUser(String userId, Map<String, dynamic> message) {
    // For now, broadcast to all clients with user filter
    // A full implementation would track user-specific connections
    broadcast('notifications', {
      'type': 'user_specific',
      'target_user_id': userId,
      'message': message,
    });
  }
  
  /// Broadcast team activity updates
  static void broadcastTeamActivity(Map<String, dynamic> activity) {
    broadcast('global_activity', {
      'type': 'team_activity',
      'activity': activity,
    });
  }
  
  /// Send real-time notification to specific user
  static void sendNotificationToUser(String userId, Map<String, dynamic> notification) {
    sendToUser(userId, {
      'type': 'notification',
      'notification': notification,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    print('🔔 Sent notification to user $userId: ${notification['title']}');
  }

  // Advanced Real-Time Features

  /// Broadcast collaboration updates (document editing, comments, etc.)
  static void broadcastCollaborationUpdate(String roomId, Map<String, dynamic> update) {
    broadcast('collaboration', {
      'type': 'collaboration_update',
      'room_id': roomId,
      'update': update,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast freelancing project updates
  static void broadcastFreelancingUpdate(String projectId, Map<String, dynamic> update) {
    broadcast('freelancing', {
      'type': 'freelancing_update',
      'project_id': projectId,
      'update': update,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast learning progress updates
  static void broadcastLearningUpdate(String courseId, Map<String, dynamic> update) {
    broadcast('learning', {
      'type': 'learning_update',
      'course_id': courseId,
      'update': update,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast enterprise-wide notifications
  static void broadcastEnterpriseUpdate(String organizationId, Map<String, dynamic> update) {
    broadcast('enterprise', {
      'type': 'enterprise_update',
      'organization_id': organizationId,
      'update': update,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast chat messages for real-time communication
  static void broadcastChatMessage(String channelId, Map<String, dynamic> message) {
    broadcast('chat_messages', {
      'type': 'chat_message',
      'channel_id': channelId,
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast task updates for project management
  static void broadcastTaskUpdate(String taskId, Map<String, dynamic> update) {
    broadcast('task_updates', {
      'type': 'task_update',
      'task_id': taskId,
      'update': update,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast quest updates for gamification
  static void broadcastQuestUpdate(String questId, Map<String, dynamic> update) {
    broadcast('quest_updates', {
      'type': 'quest_update',
      'quest_id': questId,
      'update': update,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast project updates for enterprise features
  static void broadcastProjectUpdate(String projectId, Map<String, dynamic> update) {
    broadcast('project_updates', {
      'type': 'project_update',
      'project_id': projectId,
      'update': update,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Send typing indicator for chat features
  static void sendTypingIndicator(String channelId, String userId, bool isTyping) {
    broadcast('chat_messages', {
      'type': 'typing_indicator',
      'channel_id': channelId,
      'user_id': userId,
      'is_typing': isTyping,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Broadcast presence updates (online/offline status)
  static void broadcastPresenceUpdate(String userId, Map<String, dynamic> presence) {
    broadcast('real_time_updates', {
      'type': 'presence_update',
      'user_id': userId,
      'presence': presence,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Send real-time analytics updates
  static void broadcastAnalyticsUpdate(Map<String, dynamic> analytics) {
    broadcast('system_updates', {
      'type': 'analytics_update',
      'analytics': analytics,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Get connection statistics for monitoring
  static Map<String, dynamic> getConnectionStats() {
    return {
      'total_connections': _totalConnections,
      'active_connections': _activeConnections,
      'subscribers_by_type': _subscribers.map((type, subs) => MapEntry(type, subs.length)),
      'connection_history': _connectionHistory.take(100).toList(), // Last 100 connections
      'user_connections': _userConnections.length,
      'active_rooms': _roomConnections.length,
      'users_online': _userPresence.values.where((p) => p['status'] == 'online').length,
    };
  }

  /// Get user connections for testing
  static Map<String, Set<WebSocketChannel>> getUserConnections() {
    return Map.from(_userConnections);
  }

  /// Get room connections for testing
  static Map<String, Set<WebSocketChannel>> getRoomConnections() {
    return Map.from(_roomConnections);
  }

  /// Get user presence data for testing
  static Map<String, Map<String, dynamic>> getUserPresence() {
    return Map.from(_userPresence);
  }

  /// Clear all connections for testing
  static void clearAllConnections() {
    _subscribers.clear();
    _clientInfo.clear();
    _userConnections.clear();
    _roomConnections.clear();
    _userPresence.clear();
    _lastHeartbeat.clear();
    _activeConnections = 0;
    _totalConnections = 0;
    _connectionHistory.clear();
  }

  /// Broadcast to room (public method for testing)
  static void broadcastToRoom(String roomId, Map<String, dynamic> message) {
    _broadcastToRoom(roomId, message);
  }

  /// Cleanup inactive connections
  static void cleanupInactiveConnections() {
    final now = DateTime.now();
    final inactiveThreshold = Duration(minutes: 30);

    _clientInfo.removeWhere((socket, info) {
      final lastActivity = DateTime.parse(info['last_activity']);
      final isInactive = now.difference(lastActivity) > inactiveThreshold;

      if (isInactive) {
        _handleDisconnection(socket);
        return true;
      }
      return false;
    });
  }

  /// Handle new WebSocket connection (for testing)
  static void handleConnection(WebSocketChannel webSocket) {
    _handleConnection(webSocket);
  }

  /// Internal method to handle new connections
  static void _handleConnection(WebSocketChannel webSocket) {
    _totalConnections++;
    _activeConnections++;

    final connectionId = 'ws_${_totalConnections}_${DateTime.now().millisecondsSinceEpoch}';
    _connectionHistory.add('[$connectionId] Connected at ${DateTime.now().toIso8601String()}');

    print('🔗 New WebSocket connection: $connectionId (Active: $_activeConnections)');

    // Initialize client info
    _clientInfo[webSocket] = {
      'id': connectionId,
      'connected_at': DateTime.now().toIso8601String(),
      'subscriptions': <String>[],
      'user_id': null,
      'last_activity': DateTime.now().toIso8601String(),
      'rooms': <String>[],
    };

    // Send welcome message
    _sendMessage(webSocket, {
      'type': 'welcome',
      'connection_id': connectionId,
      'server_version': version,
      'timestamp': DateTime.now().toIso8601String(),
      'available_subscriptions': _subscribers.keys.toList(),
    });

    // Handle incoming messages
    webSocket.stream.listen(
      (message) => _handleMessage(webSocket, message),
      onError: (error) => _handleError(webSocket, error),
      onDone: () => _handleDisconnection(webSocket),
    );
  }
}