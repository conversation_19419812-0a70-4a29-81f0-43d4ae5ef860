import 'dart:convert';
import 'dart:math';
import '../services/database_service.dart';
import 'totp_service.dart';
import 'mfa_delivery_service.dart';
import 'backup_codes_service.dart';
import 'trusted_device_service.dart';

enum MFAMethod { totp, sms, email, backupCode, trustedDevice }
enum MFAPolicyType { always, riskBased, deviceBased, timeBased, locationBased }
enum MFAEnforcementLevel { disabled, optional, required, strict }
enum MFABypassReason { trustedDevice, emergencyAccess, adminOverride, temporaryException }

class MFAPolicy {
  final String id;
  final String name;
  final String organizationId;
  final MFAPolicyType type;
  final MFAEnforcementLevel enforcementLevel;
  final List<MFAMethod> allowedMethods;
  final Map<String, dynamic> conditions;
  final Map<String, dynamic> settings;
  final bool isActive;
  final int priority;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;

  MFAPolicy({
    required this.id,
    required this.name,
    required this.organizationId,
    required this.type,
    required this.enforcementLevel,
    required this.allowedMethods,
    required this.conditions,
    required this.settings,
    this.isActive = true,
    this.priority = 100,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'organization_id': organizationId,
    'type': type.name,
    'enforcement_level': enforcementLevel.name,
    'allowed_methods': allowedMethods.map((m) => m.name).toList(),
    'conditions': conditions,
    'settings': settings,
    'is_active': isActive,
    'priority': priority,
    'created_at': createdAt.toIso8601String(),
    'updated_at': updatedAt.toIso8601String(),
    'created_by': createdBy,
  };

  factory MFAPolicy.fromJson(Map<String, dynamic> json) => MFAPolicy(
    id: json['id'],
    name: json['name'],
    organizationId: json['organization_id'],
    type: MFAPolicyType.values.byName(json['type']),
    enforcementLevel: MFAEnforcementLevel.values.byName(json['enforcement_level']),
    allowedMethods: (json['allowed_methods'] as List)
        .map((m) => MFAMethod.values.byName(m))
        .toList(),
    conditions: Map<String, dynamic>.from(json['conditions'] ?? {}),
    settings: Map<String, dynamic>.from(json['settings'] ?? {}),
    isActive: json['is_active'] ?? true,
    priority: json['priority'] ?? 100,
    createdAt: DateTime.parse(json['created_at']),
    updatedAt: DateTime.parse(json['updated_at']),
    createdBy: json['created_by'],
  );
}

class MFAEnforcementResult {
  final bool isMFARequired;
  final bool canBypassMFA;
  final List<MFAMethod> availableMethods;
  final List<MFAMethod> recommendedMethods;
  final MFABypassReason? bypassReason;
  final String? challengeId;
  final Map<String, dynamic> policyContext;
  final List<String> warnings;
  final int maxAttempts;
  final Duration challengeValidDuration;
  final DateTime enforcedAt;

  MFAEnforcementResult({
    required this.isMFARequired,
    required this.canBypassMFA,
    required this.availableMethods,
    required this.recommendedMethods,
    this.bypassReason,
    this.challengeId,
    required this.policyContext,
    this.warnings = const [],
    this.maxAttempts = 3,
    this.challengeValidDuration = const Duration(minutes: 5),
    required this.enforcedAt,
  });

  Map<String, dynamic> toJson() => {
    'is_mfa_required': isMFARequired,
    'can_bypass_mfa': canBypassMFA,
    'available_methods': availableMethods.map((m) => m.name).toList(),
    'recommended_methods': recommendedMethods.map((m) => m.name).toList(),
    'bypass_reason': bypassReason?.name,
    'challenge_id': challengeId,
    'policy_context': policyContext,
    'warnings': warnings,
    'max_attempts': maxAttempts,
    'challenge_valid_duration_minutes': challengeValidDuration.inMinutes,
    'enforced_at': enforcedAt.toIso8601String(),
  };
}

class MFAChallenge {
  final String id;
  final String userId;
  final MFAMethod method;
  final String? challengeData;
  final String? destinationHint;
  final DateTime createdAt;
  final DateTime expiresAt;
  final bool isCompleted;
  final int attemptCount;
  final int maxAttempts;
  final Map<String, dynamic> metadata;

  MFAChallenge({
    required this.id,
    required this.userId,
    required this.method,
    this.challengeData,
    this.destinationHint,
    required this.createdAt,
    required this.expiresAt,
    this.isCompleted = false,
    this.attemptCount = 0,
    this.maxAttempts = 3,
    this.metadata = const {},
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get hasAttemptsRemaining => attemptCount < maxAttempts;
  Duration get timeRemaining => expiresAt.difference(DateTime.now());

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'method': method.name,
    'challenge_data': challengeData,
    'destination_hint': destinationHint,
    'created_at': createdAt.toIso8601String(),
    'expires_at': expiresAt.toIso8601String(),
    'is_completed': isCompleted,
    'attempt_count': attemptCount,
    'max_attempts': maxAttempts,
    'metadata': metadata,
  };
}

class MFAVerificationRequest {
  final String challengeId;
  final String userId;
  final MFAMethod method;
  final String verificationCode;
  final String? deviceFingerprint;
  final String? ipAddress;
  final String? userAgent;
  final bool trustDevice;

  MFAVerificationRequest({
    required this.challengeId,
    required this.userId,
    required this.method,
    required this.verificationCode,
    this.deviceFingerprint,
    this.ipAddress,
    this.userAgent,
    this.trustDevice = false,
  });

  Map<String, dynamic> toJson() => {
    'challenge_id': challengeId,
    'user_id': userId,
    'method': method.name,
    'verification_code': verificationCode,
    'device_fingerprint': deviceFingerprint,
    'ip_address': ipAddress,
    'user_agent': userAgent,
    'trust_device': trustDevice,
  };
}

class MFAVerificationResult {
  final bool isValid;
  final bool isCompleted;
  final String? sessionToken;
  final String? errorMessage;
  final int attemptsRemaining;
  final bool deviceTrusted;
  final Map<String, dynamic> metadata;
  final DateTime verifiedAt;

  MFAVerificationResult({
    required this.isValid,
    required this.isCompleted,
    this.sessionToken,
    this.errorMessage,
    required this.attemptsRemaining,
    this.deviceTrusted = false,
    this.metadata = const {},
    required this.verifiedAt,
  });

  Map<String, dynamic> toJson() => {
    'is_valid': isValid,
    'is_completed': isCompleted,
    'session_token': sessionToken,
    'error_message': errorMessage,
    'attempts_remaining': attemptsRemaining,
    'device_trusted': deviceTrusted,
    'metadata': metadata,
    'verified_at': verifiedAt.toIso8601String(),
  };
}

class MFAPolicyEngine {
  final DatabaseService _databaseService;
  final TOTPService _totpService;
  final MFADeliveryService _deliveryService;
  final BackupCodesService _backupCodesService;
  final TrustedDeviceService _trustedDeviceService;
  final Random _secureRandom;
  
  static const String _mfaPoliciesTable = 'mfa_policies';
  static const String _mfaChallengesTable = 'mfa_challenges';
  static const String _mfaSessionsTable = 'mfa_sessions';

  MFAPolicyEngine(
    this._databaseService,
    this._totpService,
    this._deliveryService,
    this._backupCodesService,
    this._trustedDeviceService,
  ) : _secureRandom = Random.secure();

  Future<MFAEnforcementResult> evaluateMFARequirement(
    String userId, {
    String? organizationId,
    String? deviceFingerprint,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic> context = const {},
  }) async {
    try {
      final enforcedAt = DateTime.now();
      
      // Get applicable policies
      final policies = await _getApplicablePolicies(userId, organizationId);
      
      if (policies.isEmpty) {
        return _createNoMFAResult(enforcedAt);
      }

      // Check for trusted device bypass
      DeviceVerificationResult? deviceVerification;
      if (deviceFingerprint != null) {
        deviceVerification = await _trustedDeviceService.verifyDevice(
          userId,
          deviceFingerprint,
          ipAddress: ipAddress,
          userAgent: userAgent,
        );
        
        if (deviceVerification.isTrusted && !deviceVerification.isSuspicious) {
          return MFAEnforcementResult(
            isMFARequired: false,
            canBypassMFA: true,
            availableMethods: [],
            recommendedMethods: [],
            bypassReason: MFABypassReason.trustedDevice,
            policyContext: {
              'trusted_device': true,
              'device_id': deviceVerification.device?.id,
              'device_name': deviceVerification.device?.deviceName,
            },
            enforcedAt: enforcedAt,
          );
        }
      }

      // Evaluate policies by priority
      policies.sort((a, b) => a.priority.compareTo(b.priority));
      
      for (final policy in policies) {
        final policyResult = await _evaluatePolicy(
          policy,
          userId,
          deviceVerification,
          ipAddress,
          userAgent,
          context,
        );
        
        if (policyResult != null) {
          return policyResult.copyWith(enforcedAt: enforcedAt);
        }
      }

      return _createDefaultMFAResult(enforcedAt);
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'mfa_policy_evaluation_error',
        details: {
          'error': e.toString(),
          'organization_id': organizationId,
          'ip_address': ipAddress,
        },
      );
      
      // Fail secure - require MFA on errors
      return _createDefaultMFAResult(DateTime.now());
    }
  }

  Future<MFAChallenge> createMFAChallenge(
    String userId,
    MFAMethod method, {
    String? destination,
    Duration? validDuration,
    int? maxAttempts,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final now = DateTime.now();
      final challengeId = _generateChallengeId();
      final expiresAt = now.add(validDuration ?? Duration(minutes: 5));
      
      String? challengeData;
      String? destinationHint;
      
      switch (method) {
        case MFAMethod.totp:
          // TOTP doesn't need challenge data - user provides from their app
          challengeData = null;
          destinationHint = 'Authenticator App';
          break;
          
        case MFAMethod.sms:
          if (destination == null) {
            final userPhone = await _getUserPhoneNumber(userId);
            if (userPhone == null) {
              throw Exception('No phone number found for SMS MFA');
            }
            destination = userPhone;
          }
          
          final smsCode = _generateVerificationCode();
          challengeData = smsCode;
          destinationHint = _maskPhoneNumber(destination);
          
          await _deliveryService.sendSMSCode(
            userId: userId,
            phoneNumber: destination,
          );
          break;
          
        case MFAMethod.email:
          if (destination == null) {
            final userEmail = await _getUserEmail(userId);
            if (userEmail == null) {
              throw Exception('No email found for email MFA');
            }
            destination = userEmail;
          }
          
          final emailCode = _generateVerificationCode();
          challengeData = emailCode;
          destinationHint = _maskEmail(destination);
          
          await _deliveryService.sendEmailCode(
            userId: userId,
            email: destination,
          );
          break;
          
        case MFAMethod.backupCode:
          // Backup codes don't need challenge data
          challengeData = null;
          destinationHint = 'Backup Code';
          break;
          
        case MFAMethod.trustedDevice:
          throw Exception('Trusted device is not a challenge-based method');
      }
      
      final challenge = MFAChallenge(
        id: challengeId,
        userId: userId,
        method: method,
        challengeData: challengeData,
        destinationHint: destinationHint,
        createdAt: now,
        expiresAt: expiresAt,
        maxAttempts: maxAttempts ?? 3,
        metadata: {
          ...metadata,
          'destination': destination,
          'delivery_method': method.name,
        },
      );
      
      await _storeChallenge(challenge);
      
      await _logSecurityEvent(
        userId: userId,
        eventType: 'mfa_challenge_created',
        details: {
          'challenge_id': challengeId,
          'method': method.name,
          'destination_hint': destinationHint,
          'expires_at': expiresAt.toIso8601String(),
        },
      );
      
      return challenge;
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'mfa_challenge_creation_failed',
        details: {
          'error': e.toString(),
          'method': method.name,
          'destination': destination,
        },
      );
      rethrow;
    }
  }

  Future<MFAVerificationResult> verifyMFAChallenge(MFAVerificationRequest request) async {
    try {
      final challenge = await _getChallenge(request.challengeId);
      if (challenge == null) {
        return MFAVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Invalid or expired challenge',
          attemptsRemaining: 0,
          verifiedAt: DateTime.now(),
        );
      }

      if (challenge.isExpired) {
        await _expireChallenge(challenge.id);
        return MFAVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Challenge has expired',
          attemptsRemaining: 0,
          verifiedAt: DateTime.now(),
        );
      }

      if (!challenge.hasAttemptsRemaining) {
        return MFAVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Maximum attempts exceeded',
          attemptsRemaining: 0,
          verifiedAt: DateTime.now(),
        );
      }

      // Increment attempt count
      await _incrementChallengeAttempt(challenge.id);
      
      // Verify the code based on method
      bool isCodeValid = false;
      Map<String, dynamic> verificationMetadata = {};
      
      switch (challenge.method) {
        case MFAMethod.totp:
          final totpResult = await _totpService.validateCode(
            userId: request.userId,
            code: request.verificationCode,
          );
          isCodeValid = totpResult.isValid;
          verificationMetadata = {
            'method': 'totp',
            'time_step': totpResult.timeStep,
            'window_offset': totpResult.windowOffset,
            'is_backup_code': totpResult.isBackupCode,
            'error': totpResult.error,
            ...totpResult.metadata,
          };
          break;
          
        case MFAMethod.sms:
        case MFAMethod.email:
          isCodeValid = challenge.challengeData == request.verificationCode;
          break;
          
        case MFAMethod.backupCode:
          final backupResult = await _backupCodesService.validateBackupCode(
            request.userId,
            request.verificationCode,
            ipAddress: request.ipAddress,
            userAgent: request.userAgent,
          );
          isCodeValid = backupResult.isValid;
          verificationMetadata = backupResult.toJson();
          break;
          
        case MFAMethod.trustedDevice:
          throw Exception('Trusted device verification not handled here');
      }

      if (!isCodeValid) {
        await _logSecurityEvent(
          userId: request.userId,
          eventType: 'mfa_verification_failed',
          details: {
            'challenge_id': request.challengeId,
            'method': challenge.method.name,
            'attempts_remaining': challenge.maxAttempts - challenge.attemptCount - 1,
            'ip_address': request.ipAddress,
          },
        );
        
        return MFAVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Invalid verification code',
          attemptsRemaining: challenge.maxAttempts - challenge.attemptCount - 1,
          verifiedAt: DateTime.now(),
        );
      }

      // Verification successful
      await _completeChallenge(challenge.id);
      
      // Handle device trust if requested
      bool deviceTrusted = false;
      if (request.trustDevice && request.deviceFingerprint != null) {
        try {
          await _trustedDeviceService.trustDevice(
            request.userId,
            request.deviceFingerprint!,
            trustedBy: 'mfa_verification',
            reason: 'Post-MFA device trust',
          );
          deviceTrusted = true;
        } catch (e) {
          // Log but don't fail MFA verification
          await _logSecurityEvent(
            userId: request.userId,
            eventType: 'device_trust_after_mfa_failed',
            details: {'error': e.toString()},
          );
        }
      }

      // Create MFA session token
      final sessionToken = await _createMFASession(
        request.userId,
        challenge.method,
        request.deviceFingerprint,
        request.ipAddress,
        request.userAgent,
      );

      await _logSecurityEvent(
        userId: request.userId,
        eventType: 'mfa_verification_successful',
        details: {
          'challenge_id': request.challengeId,
          'method': challenge.method.name,
          'device_trusted': deviceTrusted,
          'ip_address': request.ipAddress,
        },
      );

      return MFAVerificationResult(
        isValid: true,
        isCompleted: true,
        sessionToken: sessionToken,
        attemptsRemaining: challenge.maxAttempts - challenge.attemptCount - 1,
        deviceTrusted: deviceTrusted,
        metadata: {
          ...verificationMetadata,
          'challenge_completed_at': DateTime.now().toIso8601String(),
          'method_used': challenge.method.name,
        },
        verifiedAt: DateTime.now(),
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: request.userId,
        eventType: 'mfa_verification_error',
        details: {
          'error': e.toString(),
          'challenge_id': request.challengeId,
          'ip_address': request.ipAddress,
        },
      );
      
      return MFAVerificationResult(
        isValid: false,
        isCompleted: false,
        errorMessage: 'Verification failed: ${e.toString()}',
        attemptsRemaining: 0,
        verifiedAt: DateTime.now(),
      );
    }
  }

  Future<List<MFAPolicy>> getUserMFAPolicies(String userId, {String? organizationId}) async {
    return await _getApplicablePolicies(userId, organizationId);
  }

  Future<void> cleanupExpiredChallenges() async {
    try {
      final result = await _databaseService.execute('''
        DELETE FROM $_mfaChallengesTable 
        WHERE expires_at < NOW() OR is_completed = true
      ''');
      
      final affectedRows = result.affectedRows;
      if (affectedRows > 0) {
        print('Cleaned up $affectedRows expired MFA challenges');
      }
    } catch (e) {
      print('Error cleaning up expired MFA challenges: $e');
    }
  }

  String _generateChallengeId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomBytes = List.generate(16, (_) => _secureRandom.nextInt(256));
    final randomHex = randomBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    return 'mfa_${timestamp}_$randomHex';
  }

  String _generateVerificationCode({int length = 6}) {
    final code = StringBuffer();
    for (int i = 0; i < length; i++) {
      code.write(_secureRandom.nextInt(10));
    }
    return code.toString();
  }

  String _maskPhoneNumber(String phone) {
    if (phone.length < 4) return phone;
    return '***-***-${phone.substring(phone.length - 4)}';
  }

  String _maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2 || parts[0].length < 2) return email;
    return '${parts[0].substring(0, 2)}***@${parts[1]}';
  }

  Future<List<MFAPolicy>> _getApplicablePolicies(String userId, String? organizationId) async {
    String whereClause = 'is_active = true';
    Map<String, dynamic> params = {};

    if (organizationId != null) {
      whereClause += ' AND organization_id = @organization_id';
      params['organization_id'] = organizationId;
    }

    final result = await _databaseService.query('''
      SELECT * FROM $_mfaPoliciesTable 
      WHERE $whereClause
      ORDER BY priority ASC
    ''', params);
    
    return result.map((row) => MFAPolicy.fromJson(row)).toList();
  }

  Future<MFAEnforcementResult?> _evaluatePolicy(
    MFAPolicy policy,
    String userId,
    DeviceVerificationResult? deviceVerification,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic> context,
  ) async {
    // Policy-specific evaluation logic
    switch (policy.type) {
      case MFAPolicyType.always:
        return await _evaluateAlwaysPolicy(policy, userId);
        
      case MFAPolicyType.riskBased:
        return await _evaluateRiskBasedPolicy(
          policy,
          userId,
          deviceVerification,
          ipAddress,
          context,
        );
        
      case MFAPolicyType.deviceBased:
        return await _evaluateDeviceBasedPolicy(policy, userId, deviceVerification);
        
      case MFAPolicyType.timeBased:
        return await _evaluateTimeBasedPolicy(policy, userId, context);
        
      case MFAPolicyType.locationBased:
        return await _evaluateLocationBasedPolicy(policy, userId, ipAddress, context);
    }
  }

  Future<MFAEnforcementResult> _evaluateAlwaysPolicy(MFAPolicy policy, String userId) async {
    return MFAEnforcementResult(
      isMFARequired: policy.enforcementLevel != MFAEnforcementLevel.disabled,
      canBypassMFA: policy.enforcementLevel == MFAEnforcementLevel.optional,
      availableMethods: policy.allowedMethods,
      recommendedMethods: _selectRecommendedMethods(policy.allowedMethods, userId),
      policyContext: {
        'policy_id': policy.id,
        'policy_name': policy.name,
        'policy_type': 'always',
      },
      enforcedAt: DateTime.now(),
    );
  }

  Future<MFAEnforcementResult?> _evaluateRiskBasedPolicy(
    MFAPolicy policy,
    String userId,
    DeviceVerificationResult? deviceVerification,
    String? ipAddress,
    Map<String, dynamic> context,
  ) async {
    var riskScore = 0.0;
    final riskFactors = <String>[];

    // Device-based risk
    if (deviceVerification?.isNewDevice == true) {
      riskScore += 0.3;
      riskFactors.add('new_device');
    }
    
    if (deviceVerification?.isSuspicious == true) {
      riskScore += 0.5;
      riskFactors.add('suspicious_device');
    }

    // Time-based risk
    final currentHour = DateTime.now().hour;
    if (currentHour < 6 || currentHour > 22) {
      riskScore += 0.2;
      riskFactors.add('unusual_time');
    }

    // Location-based risk (simplified - would need geolocation service)
    // This is a placeholder for actual implementation
    
    final riskThreshold = policy.settings['risk_threshold'] as double? ?? 0.5;
    
    if (riskScore >= riskThreshold) {
      return MFAEnforcementResult(
        isMFARequired: true,
        canBypassMFA: false,
        availableMethods: policy.allowedMethods,
        recommendedMethods: _selectRecommendedMethods(policy.allowedMethods, userId),
        policyContext: {
          'policy_id': policy.id,
          'policy_type': 'risk_based',
          'risk_score': riskScore,
          'risk_factors': riskFactors,
          'risk_threshold': riskThreshold,
        },
        warnings: ['High risk detected: ${riskFactors.join(', ')}'],
        enforcedAt: DateTime.now(),
      );
    }
    
    return null; // Policy doesn't apply
  }

  Future<MFAEnforcementResult?> _evaluateDeviceBasedPolicy(
    MFAPolicy policy,
    String userId,
    DeviceVerificationResult? deviceVerification,
  ) async {
    if (deviceVerification?.isTrusted == true) {
      return null; // Trusted device, policy doesn't require MFA
    }
    
    return MFAEnforcementResult(
      isMFARequired: true,
      canBypassMFA: false,
      availableMethods: policy.allowedMethods,
      recommendedMethods: _selectRecommendedMethods(policy.allowedMethods, userId),
      policyContext: {
        'policy_id': policy.id,
        'policy_type': 'device_based',
        'device_status': deviceVerification?.isTrusted == true 
            ? 'trusted' 
            : deviceVerification?.isSuspicious == true 
              ? 'suspicious' 
              : deviceVerification?.isNewDevice == true 
                ? 'new' 
                : 'unknown',
      },
      enforcedAt: DateTime.now(),
    );
  }

  Future<MFAEnforcementResult?> _evaluateTimeBasedPolicy(
    MFAPolicy policy,
    String userId,
    Map<String, dynamic> context,
  ) async {
    final now = DateTime.now();
    final conditions = policy.conditions;
    
    // Check time-based conditions
    if (conditions.containsKey('allowed_hours')) {
      final allowedHours = List<int>.from(conditions['allowed_hours']);
      if (!allowedHours.contains(now.hour)) {
        return MFAEnforcementResult(
          isMFARequired: true,
          canBypassMFA: false,
          availableMethods: policy.allowedMethods,
          recommendedMethods: _selectRecommendedMethods(policy.allowedMethods, userId),
          policyContext: {
            'policy_id': policy.id,
            'policy_type': 'time_based',
            'current_hour': now.hour,
            'allowed_hours': allowedHours,
          },
          warnings: ['Access outside allowed hours'],
          enforcedAt: DateTime.now(),
        );
      }
    }
    
    return null; // Within allowed time
  }

  Future<MFAEnforcementResult?> _evaluateLocationBasedPolicy(
    MFAPolicy policy,
    String userId,
    String? ipAddress,
    Map<String, dynamic> context,
  ) async {
    // Placeholder for location-based evaluation
    // In a real implementation, this would use IP geolocation services
    
    return null; // Policy doesn't apply (not implemented)
  }

  List<MFAMethod> _selectRecommendedMethods(List<MFAMethod> availableMethods, String userId) {
    // Prioritize methods based on security and user experience
    final methodPriority = {
      MFAMethod.totp: 10,
      MFAMethod.sms: 7,
      MFAMethod.email: 5,
      MFAMethod.backupCode: 3,
    };
    
    final sortedMethods = List<MFAMethod>.from(availableMethods)
      ..sort((a, b) => (methodPriority[b] ?? 0).compareTo(methodPriority[a] ?? 0));
    
    return sortedMethods.take(2).toList();
  }

  MFAEnforcementResult _createNoMFAResult(DateTime enforcedAt) {
    return MFAEnforcementResult(
      isMFARequired: false,
      canBypassMFA: true,
      availableMethods: [],
      recommendedMethods: [],
      policyContext: {'reason': 'no_policies_found'},
      enforcedAt: enforcedAt,
    );
  }

  MFAEnforcementResult _createDefaultMFAResult(DateTime enforcedAt) {
    return MFAEnforcementResult(
      isMFARequired: true,
      canBypassMFA: false,
      availableMethods: [MFAMethod.totp, MFAMethod.sms, MFAMethod.email],
      recommendedMethods: [MFAMethod.totp, MFAMethod.sms],
      policyContext: {'reason': 'default_security_policy'},
      enforcedAt: enforcedAt,
    );
  }

  Future<String?> _getUserPhoneNumber(String userId) async {
    final result = await _databaseService.query(
      'SELECT phone FROM users WHERE id = @user_id',
      {'user_id': userId},
    );
    return result.isNotEmpty ? result.first['phone'] : null;
  }

  Future<String?> _getUserEmail(String userId) async {
    final result = await _databaseService.query(
      'SELECT email FROM users WHERE id = @user_id',
      {'user_id': userId},
    );
    return result.isNotEmpty ? result.first['email'] : null;
  }

  Future<void> _storeChallenge(MFAChallenge challenge) async {
    await _databaseService.execute('''
      INSERT INTO $_mfaChallengesTable (
        id, user_id, method, challenge_data, destination_hint,
        created_at, expires_at, is_completed, attempt_count, max_attempts, metadata
      ) VALUES (@id, @user_id, @method, @challenge_data, @destination_hint, @created_at, @expires_at, @is_completed, @attempt_count, @max_attempts, @metadata)
    ''', parameters: {
      'id': challenge.id,
      'user_id': challenge.userId,
      'method': challenge.method.name,
      'challenge_data': challenge.challengeData,
      'destination_hint': challenge.destinationHint,
      'created_at': challenge.createdAt,
      'expires_at': challenge.expiresAt,
      'is_completed': challenge.isCompleted,
      'attempt_count': challenge.attemptCount,
      'max_attempts': challenge.maxAttempts,
      'metadata': jsonEncode(challenge.metadata),
    });
  }

  Future<MFAChallenge?> _getChallenge(String challengeId) async {
    final result = await _databaseService.query('''
      SELECT id, user_id, method, challenge_data, destination_hint,
             created_at, expires_at, is_completed, attempt_count, max_attempts, metadata
      FROM $_mfaChallengesTable 
      WHERE id = @challenge_id
    ''', {'challenge_id': challengeId});
    
    if (result.isEmpty) return null;
    
    final row = result.first;
    return MFAChallenge(
      id: row['id'],
      userId: row['user_id'],
      method: MFAMethod.values.byName(row['method']),
      challengeData: row['challenge_data'],
      destinationHint: row['destination_hint'],
      createdAt: DateTime.parse(row['created_at']),
      expiresAt: DateTime.parse(row['expires_at']),
      isCompleted: row['is_completed'],
      attemptCount: row['attempt_count'],
      maxAttempts: row['max_attempts'],
      metadata: jsonDecode(row['metadata']),
    );
  }

  Future<void> _incrementChallengeAttempt(String challengeId) async {
    await _databaseService.execute('''
      UPDATE $_mfaChallengesTable 
      SET attempt_count = attempt_count + 1 
      WHERE id = @challenge_id
    ''', parameters: {'challenge_id': challengeId});
  }

  Future<void> _completeChallenge(String challengeId) async {
    await _databaseService.execute('''
      UPDATE $_mfaChallengesTable 
      SET is_completed = true 
      WHERE id = @challenge_id
    ''', parameters: {'challenge_id': challengeId});
  }

  Future<void> _expireChallenge(String challengeId) async {
    await _databaseService.execute('''
      DELETE FROM $_mfaChallengesTable 
      WHERE id = @challenge_id
    ''', parameters: {'challenge_id': challengeId});
  }

  Future<String> _createMFASession(
    String userId,
    MFAMethod method,
    String? deviceFingerprint,
    String? ipAddress,
    String? userAgent,
  ) async {
    final sessionToken = _generateSessionToken();
    final expiresAt = DateTime.now().add(Duration(hours: 24));
    
    await _databaseService.execute('''
      INSERT INTO $_mfaSessionsTable (
        token, user_id, method, device_fingerprint, ip_address, 
        user_agent, created_at, expires_at
      ) VALUES (@token, @user_id, @method, @device_fingerprint, @ip_address, @user_agent, NOW(), @expires_at)
    ''', parameters: {
      'token': sessionToken,
      'user_id': userId,
      'method': method.name,
      'device_fingerprint': deviceFingerprint,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'expires_at': expiresAt,
    });
    
    return sessionToken;
  }

  String _generateSessionToken() {
    final bytes = List.generate(32, (_) => _secureRandom.nextInt(256));
    return bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
  }

  Future<void> _logSecurityEvent({
    required String userId,
    required String eventType,
    required Map<String, dynamic> details,
  }) async {
    try {
      await _databaseService.execute('''
        INSERT INTO security_audit_log (id, user_id, event_type, event_details, created_at)
        VALUES (@id, @user_id, @event_type, @event_details, NOW())
      ''', parameters: {
        'id': _generateChallengeId(),
        'user_id': userId,
        'event_type': eventType,
        'event_details': jsonEncode(details),
      });
    } catch (e) {
      print('Warning: Failed to log security event: $e');
    }
  }
}

extension MFAEnforcementResultExtension on MFAEnforcementResult {
  MFAEnforcementResult copyWith({
    bool? isMFARequired,
    bool? canBypassMFA,
    List<MFAMethod>? availableMethods,
    List<MFAMethod>? recommendedMethods,
    MFABypassReason? bypassReason,
    String? challengeId,
    Map<String, dynamic>? policyContext,
    List<String>? warnings,
    int? maxAttempts,
    Duration? challengeValidDuration,
    DateTime? enforcedAt,
  }) {
    return MFAEnforcementResult(
      isMFARequired: isMFARequired ?? this.isMFARequired,
      canBypassMFA: canBypassMFA ?? this.canBypassMFA,
      availableMethods: availableMethods ?? this.availableMethods,
      recommendedMethods: recommendedMethods ?? this.recommendedMethods,
      bypassReason: bypassReason ?? this.bypassReason,
      challengeId: challengeId ?? this.challengeId,
      policyContext: policyContext ?? this.policyContext,
      warnings: warnings ?? this.warnings,
      maxAttempts: maxAttempts ?? this.maxAttempts,
      challengeValidDuration: challengeValidDuration ?? this.challengeValidDuration,
      enforcedAt: enforcedAt ?? this.enforcedAt,
    );
  }
}

extension MFAPolicyEngineRegistration on DatabaseService {
  MFAPolicyEngine createMFAPolicyEngine(
    TOTPService totpService,
    MFADeliveryService deliveryService,
    BackupCodesService backupCodesService,
    TrustedDeviceService trustedDeviceService,
  ) => MFAPolicyEngine(
    this,
    totpService,
    deliveryService,
    backupCodesService,
    trustedDeviceService,
  );
}