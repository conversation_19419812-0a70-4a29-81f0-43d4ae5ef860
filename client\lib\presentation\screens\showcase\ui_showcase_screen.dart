import 'package:flutter/material.dart';
import '../../widgets/common/adaptive_layout.dart';
import '../../widgets/common/enhanced_forms.dart';
import '../../widgets/common/enhanced_notifications.dart';
import '../../widgets/dashboard/enhanced_dashboard.dart';
import '../../widgets/gamification/enhanced_gamification.dart';
import '../../widgets/quest/enhanced_quest_system.dart';
import '../../widgets/charts/enhanced_charts.dart';
import '../../../core/theme/app_theme.dart';

/// Comprehensive UI showcase demonstrating the optimized design system
class UIShowcaseScreen extends StatefulWidget {
  const UIShowcaseScreen({super.key});

  @override
  State<UIShowcaseScreen> createState() => _UIShowcaseScreenState();
}

class _UIShowcaseScreenState extends State<UIShowcaseScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UI Showcase - Optimized Design System'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.games), text: 'Gamification'),
            Tab(icon: Icon(Icons.task), text: 'Quests'),
            Tab(icon: Icon(Icons.bar_chart), text: 'Charts'),
            Tab(icon: Icon(Icons.input), text: 'Forms'),
            Tab(icon: Icon(Icons.notifications), text: 'Notifications'),
          ],
        ),
      ),
      body: Stack(
        children: [
          TabBarView(
            controller: _tabController,
            children: [
              _buildDashboardShowcase(),
              _buildGamificationShowcase(),
              _buildQuestShowcase(),
              _buildChartsShowcase(),
              _buildFormsShowcase(),
              _buildNotificationsShowcase(),
            ],
          ),
          const NotificationOverlay(),
        ],
      ),
    );
  }

  Widget _buildDashboardShowcase() {
    final mockUserStats = {
      'displayName': 'UI Showcase User',
      'level': 15,
      'experience': 2450,
      'nextLevelExperience': 3000,
      'totalPoints': 12500,
      'completedQuests': 47,
      'currentStreak': 12,
      'achievementCount': 23,
      'weeklyGoal': 100,
      'weeklyProgress': 75,
      'dailyGoal': 20,
      'dailyProgress': 18,
    };

    final mockActivities = [
      {
        'type': 'quest_completed',
        'title': 'Completed "Master Flutter Animations"',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'points': 150,
      },
      {
        'type': 'achievement_unlocked',
        'title': 'Unlocked "Animation Expert" achievement',
        'timestamp': DateTime.now().subtract(const Duration(hours: 4)),
        'points': 200,
      },
      {
        'type': 'level_up',
        'title': 'Reached Level 15!',
        'timestamp': DateTime.now().subtract(const Duration(days: 1)),
        'points': 500,
      },
    ];

    final mockAchievements = [
      {
        'title': 'Flutter Master',
        'description': 'Complete 50 Flutter quests',
        'icon': 'trophy',
        'rarity': 'legendary',
        'isUnlocked': true,
        'unlockedAt': DateTime.now().subtract(const Duration(days: 2)),
      },
      {
        'title': 'UI Designer',
        'description': 'Create 10 beautiful interfaces',
        'icon': 'star',
        'rarity': 'epic',
        'isUnlocked': true,
        'unlockedAt': DateTime.now().subtract(const Duration(days: 5)),
      },
    ];

    return EnhancedDashboard(
      userStats: mockUserStats,
      recentActivities: mockActivities,
      achievements: mockAchievements,
    );
  }

  Widget _buildGamificationShowcase() {
    return AdaptiveContainer(
      child: SingleChildScrollView(
        padding: AdaptiveSpacing.padding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Gamification Components',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: AdaptiveSpacing.large(context)),
            
            // XP Bar Showcase
            const Text('Enhanced XP Bar with Particle Effects'),
            const SizedBox(height: 16),
            EnhancedXPBar(
              currentXP: 2450,
              maxXP: 3000,
              level: 15,
              onLevelUp: () {
                EnhancedNotificationService().showLevelUp(
                  newLevel: 16,
                  pointsEarned: 500,
                );
              },
            ),
            SizedBox(height: AdaptiveSpacing.large(context)),
            
            // Achievement Showcase
            const Text('3D Achievement Showcase'),
            const SizedBox(height: 16),
            AdaptiveGrid(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mobileColumns: 2,
              tabletColumns: 3,
              desktopColumns: 4,
              childAspectRatio: 0.8,
              children: [
                Achievement3DShowcase(
                  title: 'Flutter Master',
                  description: 'Complete 50 Flutter quests',
                  icon: Icons.emoji_events,
                  rarity: 'legendary',
                  isUnlocked: true,
                  onTap: () {
                    EnhancedNotificationService().showAchievement(
                      title: 'Flutter Master',
                      description: 'You are a true Flutter expert!',
                      rarity: 'legendary',
                    );
                  },
                ),
                Achievement3DShowcase(
                  title: 'UI Designer',
                  description: 'Create beautiful interfaces',
                  icon: Icons.palette,
                  rarity: 'epic',
                  isUnlocked: true,
                ),
                Achievement3DShowcase(
                  title: 'Code Warrior',
                  description: 'Write clean, efficient code',
                  icon: Icons.code,
                  rarity: 'rare',
                  isUnlocked: false,
                ),
                Achievement3DShowcase(
                  title: 'Team Player',
                  description: 'Collaborate effectively',
                  icon: Icons.group,
                  rarity: 'common',
                  isUnlocked: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestShowcase() {
    return AdaptiveContainer(
      child: SingleChildScrollView(
        padding: AdaptiveSpacing.padding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Quest System',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: AdaptiveSpacing.large(context)),
            
            EnhancedQuestCard(
              title: 'Master Flutter Animations',
              description: 'Learn to create smooth, engaging animations that bring your UI to life with advanced techniques and best practices.',
              difficulty: 'hard',
              status: 'in_progress',
              progress: 0.75,
              points: 500,
              timeLimit: 120,
              tags: ['Flutter', 'Animations', 'UI/UX'],
              onStart: () {
                EnhancedNotificationService().showNotification(
                  EnhancedNotification(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    type: NotificationType.info,
                    title: 'Quest Started!',
                    description: 'Good luck with your animation journey!',
                    icon: Icons.play_arrow,
                  ),
                );
              },
              onComplete: () {
                EnhancedNotificationService().showQuestComplete(
                  questTitle: 'Master Flutter Animations',
                  pointsEarned: 500,
                );
              },
            ),
            SizedBox(height: AdaptiveSpacing.medium(context)),
            
            EnhancedQuestCard(
              title: 'Build Responsive Layouts',
              description: 'Create layouts that work perfectly across all device sizes and orientations.',
              difficulty: 'medium',
              status: 'available',
              progress: 0.0,
              points: 300,
              tags: ['Responsive', 'Layout', 'Mobile'],
              onStart: () {
                EnhancedNotificationService().showNotification(
                  EnhancedNotification(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    type: NotificationType.info,
                    title: 'Quest Started!',
                    description: 'Time to master responsive design!',
                    icon: Icons.devices,
                  ),
                );
              },
            ),
            SizedBox(height: AdaptiveSpacing.medium(context)),
            
            EnhancedQuestCard(
              title: 'Implement State Management',
              description: 'Master BLoC pattern and state management for scalable Flutter applications.',
              difficulty: 'expert',
              status: 'completed',
              progress: 1.0,
              points: 750,
              tags: ['BLoC', 'State Management', 'Architecture'],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartsShowcase() {
    final chartData = [
      const ChartData(label: 'Mon', value: 65),
      const ChartData(label: 'Tue', value: 78),
      const ChartData(label: 'Wed', value: 90),
      const ChartData(label: 'Thu', value: 81),
      const ChartData(label: 'Fri', value: 95),
      const ChartData(label: 'Sat', value: 88),
      const ChartData(label: 'Sun', value: 92),
    ];

    return AdaptiveContainer(
      child: SingleChildScrollView(
        padding: AdaptiveSpacing.padding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Charts & Analytics',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: AdaptiveSpacing.large(context)),
            
            AdaptiveGrid(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mobileColumns: 2,
              tabletColumns: 4,
              desktopColumns: 4,
              childAspectRatio: 1.2,
              children: const [
                AnimatedStatistic(
                  label: 'Total Points',
                  value: 12500,
                  icon: Icons.stars,
                  color: AppTheme.successColor,
                ),
                AnimatedStatistic(
                  label: 'Quests Completed',
                  value: 47,
                  icon: Icons.task_alt,
                  color: AppTheme.infoColor,
                ),
                AnimatedStatistic(
                  label: 'Current Level',
                  value: 15,
                  icon: Icons.trending_up,
                  color: AppTheme.warningColor,
                ),
                AnimatedStatistic(
                  label: 'Achievements',
                  value: 23,
                  icon: Icons.emoji_events,
                  color: AppTheme.legendaryColor,
                ),
              ],
            ),
            SizedBox(height: AdaptiveSpacing.large(context)),
            
            EnhancedProgressChart(
              data: chartData,
              title: 'Weekly Progress',
              subtitle: 'Your quest completion rate this week',
              type: ChartType.line,
            ),
            SizedBox(height: AdaptiveSpacing.medium(context)),
            
            EnhancedProgressChart(
              data: chartData,
              title: 'Daily Activity',
              subtitle: 'Points earned each day',
              type: ChartType.bar,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormsShowcase() {
    return AdaptiveContainer(
      child: SingleChildScrollView(
        padding: AdaptiveSpacing.padding(context),
        child: EnhancedForm(
          formKey: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Enhanced Forms System',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: AdaptiveSpacing.large(context)),
              
              EnhancedTextField(
                label: 'Full Name',
                hint: 'Enter your full name',
                controller: _nameController,
                prefixIcon: Icons.person,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your name';
                  }
                  return null;
                },
              ),
              SizedBox(height: AdaptiveSpacing.medium(context)),
              
              EnhancedTextField(
                label: 'Email Address',
                hint: 'Enter your email',
                controller: _emailController,
                prefixIcon: Icons.email,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!value.contains('@')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: AdaptiveSpacing.medium(context)),
              
              EnhancedDropdownField<String>(
                label: 'Difficulty Preference',
                hint: 'Select your preferred difficulty',
                items: const [
                  DropdownItem(value: 'easy', label: 'Easy', icon: Icons.sentiment_satisfied),
                  DropdownItem(value: 'medium', label: 'Medium', icon: Icons.sentiment_neutral),
                  DropdownItem(value: 'hard', label: 'Hard', icon: Icons.sentiment_dissatisfied),
                  DropdownItem(value: 'expert', label: 'Expert', icon: Icons.warning),
                ],
                onChanged: (value) {
                  // Handle selection
                },
              ),
              SizedBox(height: AdaptiveSpacing.large(context)),
              
              EnhancedSubmitButton(
                text: 'Save Preferences',
                icon: Icons.save,
                onPressed: () {
                  if (_formKey.currentState?.validate() ?? false) {
                    EnhancedNotificationService().showNotification(
                      EnhancedNotification(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        type: NotificationType.success,
                        title: 'Preferences Saved!',
                        description: 'Your settings have been updated successfully.',
                        icon: Icons.check_circle,
                      ),
                    );
                  }
                },
                width: double.infinity,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationsShowcase() {
    return AdaptiveContainer(
      child: SingleChildScrollView(
        padding: AdaptiveSpacing.padding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Notifications System',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: AdaptiveSpacing.large(context)),
            
            const Text('Try different notification types:'),
            SizedBox(height: AdaptiveSpacing.medium(context)),
            
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showAchievement(
                      title: 'New Achievement!',
                      description: 'You unlocked the "Notification Master" badge!',
                      rarity: 'epic',
                    );
                  },
                  icon: const Icon(Icons.emoji_events),
                  label: const Text('Achievement'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showLevelUp(
                      newLevel: 16,
                      pointsEarned: 500,
                    );
                  },
                  icon: const Icon(Icons.trending_up),
                  label: const Text('Level Up'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showQuestComplete(
                      questTitle: 'UI Mastery Quest',
                      pointsEarned: 300,
                    );
                  },
                  icon: const Icon(Icons.task_alt),
                  label: const Text('Quest Complete'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showPointsEarned(
                      points: 150,
                      source: 'Daily Challenge',
                    );
                  },
                  icon: const Icon(Icons.stars),
                  label: const Text('Points Earned'),
                ),
              ],
            ),
            SizedBox(height: AdaptiveSpacing.large(context)),
            
            const Text('System Notifications:'),
            SizedBox(height: AdaptiveSpacing.medium(context)),
            
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showNotification(
                      EnhancedNotification(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        type: NotificationType.success,
                        title: 'Success!',
                        description: 'Operation completed successfully.',
                        icon: Icons.check_circle,
                      ),
                    );
                  },
                  icon: const Icon(Icons.check_circle),
                  label: const Text('Success'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showNotification(
                      EnhancedNotification(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        type: NotificationType.warning,
                        title: 'Warning',
                        description: 'Please check your internet connection.',
                        icon: Icons.warning,
                      ),
                    );
                  },
                  icon: const Icon(Icons.warning),
                  label: const Text('Warning'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showNotification(
                      EnhancedNotification(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        type: NotificationType.error,
                        title: 'Error',
                        description: 'Something went wrong. Please try again.',
                        icon: Icons.error,
                      ),
                    );
                  },
                  icon: const Icon(Icons.error),
                  label: const Text('Error'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    EnhancedNotificationService().showNotification(
                      EnhancedNotification(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        type: NotificationType.info,
                        title: 'Information',
                        description: 'Here\'s some helpful information for you.',
                        icon: Icons.info,
                      ),
                    );
                  },
                  icon: const Icon(Icons.info),
                  label: const Text('Info'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
