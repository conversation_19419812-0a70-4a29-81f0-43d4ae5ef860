// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prediction_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FeatureImportance _$FeatureImportanceFromJson(Map<String, dynamic> json) =>
    FeatureImportance(
      featureName: json['featureName'] as String,
      importance: (json['importance'] as num).toDouble(),
      category: json['category'] as String,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$FeatureImportanceToJson(FeatureImportance instance) =>
    <String, dynamic>{
      'featureName': instance.featureName,
      'importance': instance.importance,
      'category': instance.category,
      'description': instance.description,
    };

TimeSeriesPrediction _$TimeSeriesPredictionFromJson(
  Map<String, dynamic> json,
) => TimeSeriesPrediction(
  timestamp: DateTime.parse(json['timestamp'] as String),
  predictedValue: (json['predictedValue'] as num).toDouble(),
  upperBound: (json['upperBound'] as num?)?.toDouble(),
  lowerBound: (json['lowerBound'] as num?)?.toDouble(),
  confidence: (json['confidence'] as num).toDouble(),
);

Map<String, dynamic> _$TimeSeriesPredictionToJson(
  TimeSeriesPrediction instance,
) => <String, dynamic>{
  'timestamp': instance.timestamp.toIso8601String(),
  'predictedValue': instance.predictedValue,
  'upperBound': instance.upperBound,
  'lowerBound': instance.lowerBound,
  'confidence': instance.confidence,
};

PredictionModel _$PredictionModelFromJson(Map<String, dynamic> json) =>
    PredictionModel(
      id: json['id'] as String,
      type: $enumDecode(_$PredictionModelTypeEnumMap, json['type']),
      name: json['name'] as String,
      description: json['description'] as String?,
      version: json['version'] as String,
      organizationId: json['organizationId'] as String?,
      targetEntityId: json['targetEntityId'] as String?,
      confidenceLevel: $enumDecode(
        _$ConfidenceLevelEnumMap,
        json['confidenceLevel'],
      ),
      confidenceScore: (json['confidenceScore'] as num).toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      trainedAt: DateTime.parse(json['trainedAt'] as String),
      lastPredictionAt: json['lastPredictionAt'] == null
          ? null
          : DateTime.parse(json['lastPredictionAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      inputFeatures: (json['inputFeatures'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      featureImportances: (json['featureImportances'] as List<dynamic>?)
          ?.map((e) => FeatureImportance.fromJson(e as Map<String, dynamic>))
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$PredictionModelToJson(PredictionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$PredictionModelTypeEnumMap[instance.type]!,
      'name': instance.name,
      'description': instance.description,
      'version': instance.version,
      'organizationId': instance.organizationId,
      'targetEntityId': instance.targetEntityId,
      'confidenceLevel': _$ConfidenceLevelEnumMap[instance.confidenceLevel]!,
      'confidenceScore': instance.confidenceScore,
      'accuracy': instance.accuracy,
      'trainedAt': instance.trainedAt.toIso8601String(),
      'lastPredictionAt': instance.lastPredictionAt?.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'inputFeatures': instance.inputFeatures,
      'featureImportances': instance.featureImportances,
      'metadata': instance.metadata,
      'isActive': instance.isActive,
    };

const _$PredictionModelTypeEnumMap = {
  PredictionModelType.userBehavior: 'user_behavior',
  PredictionModelType.taskCompletion: 'task_completion',
  PredictionModelType.engagementForecast: 'engagement_forecast',
  PredictionModelType.churnPrediction: 'churn_prediction',
  PredictionModelType.performanceOptimization: 'performance_optimization',
  PredictionModelType.skillRecommendation: 'skill_recommendation',
  PredictionModelType.projectSuccess: 'project_success',
  PredictionModelType.resourceAllocation: 'resource_allocation',
};

const _$ConfidenceLevelEnumMap = {
  ConfidenceLevel.veryLow: 'very_low',
  ConfidenceLevel.low: 'low',
  ConfidenceLevel.medium: 'medium',
  ConfidenceLevel.high: 'high',
  ConfidenceLevel.veryHigh: 'very_high',
};

UserBehaviorPrediction _$UserBehaviorPredictionFromJson(
  Map<String, dynamic> json,
) => UserBehaviorPrediction(
  model: PredictionModel.fromJson(json['model'] as Map<String, dynamic>),
  engagementScore: (json['engagementScore'] as num).toDouble(),
  productivityScore: (json['productivityScore'] as num).toDouble(),
  churnRisk: $enumDecode(_$RiskLevelEnumMap, json['churnRisk']),
  churnProbability: (json['churnProbability'] as num).toDouble(),
  recommendedActions:
      (json['recommendedActions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  nextActivities:
      (json['nextActivities'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList() ??
      const [],
  optimalEngagementTimes:
      (json['optimalEngagementTimes'] as List<dynamic>?)
          ?.map((e) => DateTime.parse(e as String))
          .toList() ??
      const [],
  predictedCompletionTime: (json['predictedCompletionTime'] as num?)
      ?.toDouble(),
  skillDevelopmentOpportunities:
      (json['skillDevelopmentOpportunities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$UserBehaviorPredictionToJson(
  UserBehaviorPrediction instance,
) => <String, dynamic>{
  'model': instance.model,
  'engagementScore': instance.engagementScore,
  'productivityScore': instance.productivityScore,
  'churnRisk': _$RiskLevelEnumMap[instance.churnRisk]!,
  'churnProbability': instance.churnProbability,
  'recommendedActions': instance.recommendedActions,
  'nextActivities': instance.nextActivities,
  'optimalEngagementTimes': instance.optimalEngagementTimes
      .map((e) => e.toIso8601String())
      .toList(),
  'predictedCompletionTime': instance.predictedCompletionTime,
  'skillDevelopmentOpportunities': instance.skillDevelopmentOpportunities,
};

const _$RiskLevelEnumMap = {
  RiskLevel.veryLow: 'very_low',
  RiskLevel.low: 'low',
  RiskLevel.medium: 'medium',
  RiskLevel.high: 'high',
  RiskLevel.critical: 'critical',
};

TaskCompletionPrediction _$TaskCompletionPredictionFromJson(
  Map<String, dynamic> json,
) => TaskCompletionPrediction(
  model: PredictionModel.fromJson(json['model'] as Map<String, dynamic>),
  taskId: json['taskId'] as String,
  completionProbability: (json['completionProbability'] as num).toDouble(),
  predictedCompletionDate: json['predictedCompletionDate'] == null
      ? null
      : DateTime.parse(json['predictedCompletionDate'] as String),
  predictedEffortHours: (json['predictedEffortHours'] as num?)?.toDouble(),
  riskFactors:
      (json['riskFactors'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  successFactors:
      (json['successFactors'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  recommendedResources:
      (json['recommendedResources'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  potentialBlockers:
      (json['potentialBlockers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  similarTasks:
      (json['similarTasks'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList() ??
      const [],
);

Map<String, dynamic> _$TaskCompletionPredictionToJson(
  TaskCompletionPrediction instance,
) => <String, dynamic>{
  'model': instance.model,
  'taskId': instance.taskId,
  'completionProbability': instance.completionProbability,
  'predictedCompletionDate': instance.predictedCompletionDate
      ?.toIso8601String(),
  'predictedEffortHours': instance.predictedEffortHours,
  'riskFactors': instance.riskFactors,
  'successFactors': instance.successFactors,
  'recommendedResources': instance.recommendedResources,
  'potentialBlockers': instance.potentialBlockers,
  'similarTasks': instance.similarTasks,
};

EngagementForecast _$EngagementForecastFromJson(Map<String, dynamic> json) =>
    EngagementForecast(
      model: PredictionModel.fromJson(json['model'] as Map<String, dynamic>),
      entityId: json['entityId'] as String,
      entityType: json['entityType'] as String,
      engagementTimeSeries: (json['engagementTimeSeries'] as List<dynamic>)
          .map((e) => TimeSeriesPrediction.fromJson(e as Map<String, dynamic>))
          .toList(),
      peakEngagementPeriods:
          (json['peakEngagementPeriods'] as List<dynamic>?)
              ?.map((e) => DateTime.parse(e as String))
              .toList() ??
          const [],
      lowEngagementPeriods:
          (json['lowEngagementPeriods'] as List<dynamic>?)
              ?.map((e) => DateTime.parse(e as String))
              .toList() ??
          const [],
      engagementFactors:
          (json['engagementFactors'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toDouble()),
          ) ??
          const {},
      seasonalPatterns:
          json['seasonalPatterns'] as Map<String, dynamic>? ?? const {},
      trendDirection: json['trendDirection'] as String,
      forecastHorizonDays: (json['forecastHorizonDays'] as num).toInt(),
    );

Map<String, dynamic> _$EngagementForecastToJson(EngagementForecast instance) =>
    <String, dynamic>{
      'model': instance.model,
      'entityId': instance.entityId,
      'entityType': instance.entityType,
      'engagementTimeSeries': instance.engagementTimeSeries,
      'peakEngagementPeriods': instance.peakEngagementPeriods
          .map((e) => e.toIso8601String())
          .toList(),
      'lowEngagementPeriods': instance.lowEngagementPeriods
          .map((e) => e.toIso8601String())
          .toList(),
      'engagementFactors': instance.engagementFactors,
      'seasonalPatterns': instance.seasonalPatterns,
      'trendDirection': instance.trendDirection,
      'forecastHorizonDays': instance.forecastHorizonDays,
    };
