-- Quester Database Initialization Script
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create initial schema (can be expanded)
CREATE SCHEMA IF NOT EXISTS quester;

-- Grant permissions
GRANT ALL PRIVILEGES ON SCHEMA quester TO quester;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA quester TO quester;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA quester TO quester;

-- Create a simple health check table
CREATE TABLE IF NOT EXISTS quester.health_check (
    id SERIAL PRIMARY KEY,
    status VARCHAR(50) NOT NULL DEFAULT 'healthy',
    last_check TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial health record
INSERT INTO quester.health_check (status) VALUES ('healthy') ON CONFLICT DO NOTHING;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'Quester database initialized successfully';
END $$;
