import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared/shared.dart';
import 'package:client/main.dart' as app;
import 'package:client/presentation/blocs/auth/auth_bloc.dart';
import 'package:client/data/repositories/api_repository.dart';
import 'package:client/core/di/dependency_injection.dart';

class MockApiRepository extends Mock implements ApiRepository {}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Authentication Flow Integration Tests', () {
    late MockApiRepository mockRepository;

    setUp(() {
      mockRepository = MockApiRepository();
      
      // Register mock repository
      DependencyInjection.instance.registerSingleton<ApiRepository>(mockRepository);
    });

    tearDown(() {
      DependencyInjection.instance.reset();
    });

    testWidgets('complete login flow', (tester) async {
      // Mock successful login
      const user = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'Test User',
      );
      const loginResponse = LoginResponse(
        user: user,
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      );

      when(() => mockRepository.login(any()))
          .thenAnswer((_) async => loginResponse);
      when(() => mockRepository.getCurrentUser())
          .thenThrow(Exception('No user found'));

      // Start the app
      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Verify we're on the login screen
      expect(find.text('Welcome to Quester'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);

      // Enter email and password
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );

      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();

      // Verify we're navigated to the main app
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Test User'), findsOneWidget);

      // Verify the API was called
      verify(() => mockRepository.login(const LoginRequest(
        email: '<EMAIL>',
        password: 'password123',
      ))).called(1);
    });

    testWidgets('login with invalid credentials shows error', (tester) async {
      // Mock failed login
      when(() => mockRepository.login(any()))
          .thenThrow(Exception('Invalid credentials'));
      when(() => mockRepository.getCurrentUser())
          .thenThrow(Exception('No user found'));

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Enter invalid credentials
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'wrongpassword',
      );

      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();

      // Verify error message is shown
      expect(find.textContaining('Invalid credentials'), findsOneWidget);
      expect(find.text('Dashboard'), findsNothing);
    });

    testWidgets('complete registration flow', (tester) async {
      // Mock successful registration
      const user = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'New User',
      );
      const registerResponse = RegisterResponse(
        user: user,
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      );

      when(() => mockRepository.register(any()))
          .thenAnswer((_) async => registerResponse);
      when(() => mockRepository.getCurrentUser())
          .thenThrow(Exception('No user found'));

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Navigate to registration screen
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Verify we're on the registration screen
      expect(find.text('Create Your Account'), findsOneWidget);

      // Fill in registration form
      await tester.enterText(
        find.byKey(const Key('display_name_field')),
        'New User',
      );
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        'password123',
      );

      // Tap register button
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle();

      // Verify we're navigated to the main app
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('New User'), findsOneWidget);

      // Verify the API was called
      verify(() => mockRepository.register(const RegisterRequest(
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'New User',
      ))).called(1);
    });

    testWidgets('registration with mismatched passwords shows error', (tester) async {
      when(() => mockRepository.getCurrentUser())
          .thenThrow(Exception('No user found'));

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Navigate to registration screen
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Fill in registration form with mismatched passwords
      await tester.enterText(
        find.byKey(const Key('display_name_field')),
        'New User',
      );
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        'differentpassword',
      );

      // Tap register button
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle();

      // Verify error message is shown
      expect(find.textContaining('Passwords do not match'), findsOneWidget);
      expect(find.text('Dashboard'), findsNothing);
    });

    testWidgets('logout flow', (tester) async {
      // Mock authenticated user
      const user = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'Test User',
      );

      when(() => mockRepository.getCurrentUser())
          .thenAnswer((_) async => user);
      when(() => mockRepository.getStoredToken())
          .thenAnswer((_) async => 'stored_token');
      when(() => mockRepository.logout())
          .thenAnswer((_) async => {});

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Verify we're in the main app
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Test User'), findsOneWidget);

      // Open user menu
      await tester.tap(find.byKey(const Key('user_menu_button')));
      await tester.pumpAndSettle();

      // Tap logout
      await tester.tap(find.text('Logout'));
      await tester.pumpAndSettle();

      // Verify we're back to login screen
      expect(find.text('Welcome to Quester'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Dashboard'), findsNothing);

      // Verify logout API was called
      verify(() => mockRepository.logout()).called(1);
    });

    testWidgets('password reset flow', (tester) async {
      when(() => mockRepository.getCurrentUser())
          .thenThrow(Exception('No user found'));
      when(() => mockRepository.resetPassword(any()))
          .thenAnswer((_) async => {});

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Tap forgot password
      await tester.tap(find.text('Forgot Password?'));
      await tester.pumpAndSettle();

      // Verify we're on the password reset screen
      expect(find.text('Reset Password'), findsOneWidget);

      // Enter email
      await tester.enterText(
        find.byKey(const Key('reset_email_field')),
        '<EMAIL>',
      );

      // Tap reset button
      await tester.tap(find.byKey(const Key('reset_button')));
      await tester.pumpAndSettle();

      // Verify success message
      expect(find.textContaining('Password reset email sent'), findsOneWidget);

      // Verify API was called
      verify(() => mockRepository.resetPassword(const ResetPasswordRequest(
        email: '<EMAIL>',
      ))).called(1);
    });

    testWidgets('social login flow', (tester) async {
      // Mock successful social login
      const user = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'Social User',
        avatarUrl: 'https://example.com/avatar.jpg',
      );
      const socialLoginResponse = SocialLoginResponse(
        user: user,
        accessToken: 'social_access_token',
        refreshToken: 'social_refresh_token',
      );

      when(() => mockRepository.getCurrentUser())
          .thenThrow(Exception('No user found'));
      when(() => mockRepository.socialLogin(SocialProvider.google))
          .thenAnswer((_) async => socialLoginResponse);

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Tap Google login button
      await tester.tap(find.byKey(const Key('google_login_button')));
      await tester.pumpAndSettle();

      // Verify we're navigated to the main app
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Social User'), findsOneWidget);

      // Verify the API was called
      verify(() => mockRepository.socialLogin(SocialProvider.google)).called(1);
    });

    testWidgets('auto-login with stored token', (tester) async {
      // Mock authenticated user with stored token
      const user = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'Test User',
      );

      when(() => mockRepository.getCurrentUser())
          .thenAnswer((_) async => user);
      when(() => mockRepository.getStoredToken())
          .thenAnswer((_) async => 'stored_token');

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Verify we're directly in the main app (auto-login worked)
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Test User'), findsOneWidget);
      expect(find.text('Welcome to Quester'), findsNothing);

      // Verify the APIs were called
      verify(() => mockRepository.getCurrentUser()).called(1);
      verify(() => mockRepository.getStoredToken()).called(1);
    });

    testWidgets('token refresh on expired token', (tester) async {
      // Mock token refresh scenario
      const user = User(
        id: '1',
        email: '<EMAIL>',
        displayName: 'Test User',
      );
      const refreshResponse = RefreshTokenResponse(
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
      );

      when(() => mockRepository.getCurrentUser())
          .thenAnswer((_) async => user);
      when(() => mockRepository.getStoredToken())
          .thenAnswer((_) async => 'expired_token');
      when(() => mockRepository.refreshToken('old_refresh_token'))
          .thenAnswer((_) async => refreshResponse);

      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Simulate token expiry by triggering refresh
      final authBloc = BlocProvider.of<AuthBloc>(
        tester.element(find.byType(MaterialApp)),
      );
      authBloc.add(const RefreshToken(refreshToken: 'old_refresh_token'));
      await tester.pumpAndSettle();

      // Verify we remain in the main app with refreshed token
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Test User'), findsOneWidget);

      // Verify refresh API was called
      verify(() => mockRepository.refreshToken('old_refresh_token')).called(1);
    });
  });
}
