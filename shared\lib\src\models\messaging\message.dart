/// Message model for real-time chat functionality
library;

import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'message.g.dart';

/// Represents a chat message in the system
@JsonSerializable()
class Message extends Equatable {
  /// Unique identifier for the message
  final String id;
  
  /// ID of the chat/conversation this message belongs to
  final String chatId;
  
  /// ID of the user who sent the message
  final String senderId;
  
  /// Display name of the sender (for UI purposes)
  final String senderName;
  
  /// Avatar URL of the sender
  final String? senderAvatar;
  
  /// Content of the message
  final String content;
  
  /// Type of message (text, image, file, system, etc.)
  final MessageType type;
  
  /// Current status of the message
  final MessageStatus status;
  
  /// Timestamp when the message was created
  final DateTime createdAt;
  
  /// Timestamp when the message was last updated
  final DateTime? updatedAt;
  
  /// Timestamp when the message was delivered
  final DateTime? deliveredAt;
  
  /// Timestamp when the message was read
  final DateTime? readAt;
  
  /// ID of the message this is replying to (for threading)
  final String? replyToId;
  
  /// Metadata for different message types (file info, image dimensions, etc.)
  final Map<String, dynamic>? metadata;
  
  /// List of reactions to this message
  final List<MessageReaction> reactions;
  
  /// List of user IDs who have read this message
  final List<String> readBy;
  
  /// Whether this message has been edited
  final bool isEdited;
  
  /// Whether this message has been deleted
  final bool isDeleted;
  
  /// Priority of the message
  final MessagePriority priority;

  const Message({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    required this.content,
    required this.type,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.deliveredAt,
    this.readAt,
    this.replyToId,
    this.metadata,
    this.reactions = const [],
    this.readBy = const [],
    this.isEdited = false,
    this.isDeleted = false,
    this.priority = MessagePriority.normal,
  });

  /// Create Message from JSON
  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);

  /// Convert Message to JSON
  Map<String, dynamic> toJson() => _$MessageToJson(this);

  /// Create a copy with updated fields
  Message copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    String? content,
    MessageType? type,
    MessageStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    String? replyToId,
    Map<String, dynamic>? metadata,
    List<MessageReaction>? reactions,
    List<String>? readBy,
    bool? isEdited,
    bool? isDeleted,
    MessagePriority? priority,
  }) {
    return Message(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      replyToId: replyToId ?? this.replyToId,
      metadata: metadata ?? this.metadata,
      reactions: reactions ?? this.reactions,
      readBy: readBy ?? this.readBy,
      isEdited: isEdited ?? this.isEdited,
      isDeleted: isDeleted ?? this.isDeleted,
      priority: priority ?? this.priority,
    );
  }

  @override
  List<Object?> get props => [
        id,
        chatId,
        senderId,
        senderName,
        senderAvatar,
        content,
        type,
        status,
        createdAt,
        updatedAt,
        deliveredAt,
        readAt,
        replyToId,
        metadata,
        reactions,
        readBy,
        isEdited,
        isDeleted,
        priority,
      ];
}

/// Types of messages
enum MessageType {
  @JsonValue('text')
  text,
  @JsonValue('image')
  image,
  @JsonValue('file')
  file,
  @JsonValue('audio')
  audio,
  @JsonValue('video')
  video,
  @JsonValue('system')
  system,
  @JsonValue('quest_invite')
  questInvite,
  @JsonValue('achievement')
  achievement,
  @JsonValue('reaction')
  reaction,
}

/// Status of a message
enum MessageStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('sent')
  sent,
  @JsonValue('delivered')
  delivered,
  @JsonValue('read')
  read,
  @JsonValue('failed')
  failed,
}

/// Priority levels for messages
enum MessagePriority {
  @JsonValue('low')
  low,
  @JsonValue('normal')
  normal,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

/// Represents a reaction to a message
@JsonSerializable()
class MessageReaction extends Equatable {
  /// The emoji or reaction identifier
  final String emoji;
  
  /// ID of the user who reacted
  final String userId;
  
  /// Display name of the user who reacted
  final String userName;
  
  /// Timestamp when the reaction was added
  final DateTime createdAt;

  const MessageReaction({
    required this.emoji,
    required this.userId,
    required this.userName,
    required this.createdAt,
  });

  /// Create MessageReaction from JSON
  factory MessageReaction.fromJson(Map<String, dynamic> json) => 
      _$MessageReactionFromJson(json);

  /// Convert MessageReaction to JSON
  Map<String, dynamic> toJson() => _$MessageReactionToJson(this);

  @override
  List<Object?> get props => [emoji, userId, userName, createdAt];
}

/// Represents a chat/conversation
@JsonSerializable()
class Chat extends Equatable {
  /// Unique identifier for the chat
  final String id;
  
  /// Name of the chat (for group chats)
  final String? name;
  
  /// Description of the chat
  final String? description;
  
  /// Type of chat (direct, group, quest, team)
  final ChatType type;
  
  /// ID of the user who created the chat
  final String createdBy;
  
  /// List of participant IDs
  final List<String> participantIds;
  
  /// Chat settings and permissions
  final ChatSettings settings;
  
  /// Timestamp when the chat was created
  final DateTime createdAt;
  
  /// Timestamp when the chat was last updated
  final DateTime updatedAt;
  
  /// ID of the last message in the chat
  final String? lastMessageId;
  
  /// Timestamp of the last message
  final DateTime? lastMessageAt;
  
  /// Whether the chat is archived
  final bool isArchived;
  
  /// Whether the chat is muted for notifications
  final bool isMuted;
  
  /// Avatar/image URL for the chat
  final String? avatarUrl;

  const Chat({
    required this.id,
    this.name,
    this.description,
    required this.type,
    required this.createdBy,
    required this.participantIds,
    required this.settings,
    required this.createdAt,
    required this.updatedAt,
    this.lastMessageId,
    this.lastMessageAt,
    this.isArchived = false,
    this.isMuted = false,
    this.avatarUrl,
  });

  /// Create Chat from JSON
  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);

  /// Convert Chat to JSON
  Map<String, dynamic> toJson() => _$ChatToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        type,
        createdBy,
        participantIds,
        settings,
        createdAt,
        updatedAt,
        lastMessageId,
        lastMessageAt,
        isArchived,
        isMuted,
        avatarUrl,
      ];
}

/// Types of chats
enum ChatType {
  @JsonValue('direct')
  direct,
  @JsonValue('group')
  group,
  @JsonValue('quest')
  quest,
  @JsonValue('team')
  team,
  @JsonValue('public')
  public,
}

/// Chat settings and permissions
@JsonSerializable()
class ChatSettings extends Equatable {
  /// Whether anyone can add members
  final bool allowMemberInvites;
  
  /// Whether messages can be edited
  final bool allowMessageEditing;
  
  /// Whether messages can be deleted
  final bool allowMessageDeletion;
  
  /// Whether file uploads are allowed
  final bool allowFileUploads;
  
  /// Maximum file size for uploads (in bytes)
  final int maxFileSize;
  
  /// Whether reactions are enabled
  final bool allowReactions;
  
  /// Whether threading is enabled
  final bool allowThreading;

  const ChatSettings({
    this.allowMemberInvites = true,
    this.allowMessageEditing = true,
    this.allowMessageDeletion = false,
    this.allowFileUploads = true,
    this.maxFileSize = 25 * 1024 * 1024, // 25MB
    this.allowReactions = true,
    this.allowThreading = true,
  });

  /// Create ChatSettings from JSON
  factory ChatSettings.fromJson(Map<String, dynamic> json) => 
      _$ChatSettingsFromJson(json);

  /// Convert ChatSettings to JSON
  Map<String, dynamic> toJson() => _$ChatSettingsToJson(this);

  @override
  List<Object?> get props => [
        allowMemberInvites,
        allowMessageEditing,
        allowMessageDeletion,
        allowFileUploads,
        maxFileSize,
        allowReactions,
        allowThreading,
      ];
}
