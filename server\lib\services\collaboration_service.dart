import '../services/database_service.dart';
import '../services/websocket_service.dart';
import '../services/gamification_service.dart';

/// Collaboration service for team quests and social features
class CollaborationService {
  // ignore: unused_field
  final DatabaseService _databaseService;
  final GamificationService _gamificationService;
  
  CollaborationService(this._databaseService, this._gamificationService);
  
  /// Create a team quest invitation
  Future<Map<String, dynamic>?> createTeamQuest({
    required String creatorId,
    required String title,
    required String description,
    required List<String> invitedUserIds,
    int maxParticipants = 10,
  }) async {
    try {
      // Award collaboration points for creating a team quest
      await _gamificationService.awardCollaborationPoints(creatorId, 1);
      
      // Create mock team quest data (database implementation would go here)
      final teamQuest = {
        'id': 'team_quest_${DateTime.now().millisecondsSinceEpoch}',
        'creator_id': creatorId,
        'title': title,
        'description': description,
        'max_participants': maxParticipants,
        'current_participants': [creatorId],
        'invited_users': invitedUserIds,
        'status': 'pending',
        'created_at': DateTime.now().toIso8601String(),
        'progress': {
          'tasks_completed': 0,
          'total_tasks': 1,
          'completion_percentage': 0.0,
        },
      };
      
      // Send invitations via WebSocket
      for (final invitedUserId in invitedUserIds) {
        WebSocketService.sendToUser(invitedUserId, {
          'type': 'team_quest_invitation',
          'data': {
            'quest': teamQuest,
            'creator_id': creatorId,
            'message': 'You have been invited to join a team quest!',
          },
        });
      }
      
      // Broadcast team quest creation
      WebSocketService.broadcastTeamActivity({
        'type': 'team_quest_created',
        'data': teamQuest,
      });
      
      return teamQuest;
    } catch (e) {
      print('❌ Error creating team quest: $e');
      return null;
    }
  }
  
  /// Join a team quest
  Future<Map<String, dynamic>?> joinTeamQuest({
    required String questId,
    required String userId,
  }) async {
    try {
      // Award collaboration points for joining a team quest
      await _gamificationService.awardCollaborationPoints(userId, 1);
      
      // Mock implementation - would update database
      final joinResult = {
        'quest_id': questId,
        'user_id': userId,
        'joined_at': DateTime.now().toIso8601String(),
        'status': 'accepted',
        'role': 'participant',
      };
      
      // Notify other team members
      WebSocketService.broadcastTeamActivity({
        'type': 'team_member_joined',
        'data': {
          'quest_id': questId,
          'user_id': userId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      });
      
      return joinResult;
    } catch (e) {
      print('❌ Error joining team quest: $e');
      return null;
    }
  }
  
  /// Complete a team quest task
  Future<Map<String, dynamic>?> completeTeamTask({
    required String questId,
    required String userId,
    required String taskDescription,
  }) async {
    try {
      // Award collaboration points for completing team task
      await _gamificationService.awardCollaborationPoints(userId, 1);
      
      // Mock task completion
      final taskCompletion = {
        'quest_id': questId,
        'user_id': userId,
        'task_description': taskDescription,
        'completed_at': DateTime.now().toIso8601String(),
        'points_earned': 75,
      };
      
      // Notify team members of progress
      WebSocketService.broadcastTeamActivity({
        'type': 'team_task_completed',
        'data': taskCompletion,
      });
      
      return taskCompletion;
    } catch (e) {
      print('❌ Error completing team task: $e');
      return null;
    }
  }
  
  /// Send a team message
  Future<Map<String, dynamic>?> sendTeamMessage({
    required String questId,
    required String senderId,
    required String message,
  }) async {
    try {
      final teamMessage = {
        'id': 'msg_${DateTime.now().millisecondsSinceEpoch}',
        'quest_id': questId,
        'sender_id': senderId,
        'message': message,
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'team_message',
      };
      
      // Broadcast message to team members
      WebSocketService.broadcastTeamActivity({
        'type': 'team_message',
        'data': teamMessage,
      });
      
      return teamMessage;
    } catch (e) {
      print('❌ Error sending team message: $e');
      return null;
    }
  }
  
  /// Get team quest status and participants
  Future<Map<String, dynamic>?> getTeamQuestStatus(String questId) async {
    try {
      // Mock implementation - would query database
      return {
        'quest_id': questId,
        'status': 'active',
        'participants': [
          {
            'user_id': '6e055aa2-4e16-4288-aa9a-bc8c543a2da6',
            'username': 'alice_cooper',
            'role': 'creator',
            'contribution_points': 150,
            'tasks_completed': 2,
          },
        ],
        'progress': {
          'total_tasks': 5,
          'completed_tasks': 2,
          'completion_percentage': 40.0,
        },
        'recent_activity': [
          {
            'type': 'task_completed',
            'user_id': '6e055aa2-4e16-4288-aa9a-bc8c543a2da6',
            'description': 'Completed team task: Setup project structure',
            'timestamp': DateTime.now().subtract(Duration(hours: 2)).toIso8601String(),
          },
        ],
      };
    } catch (e) {
      print('❌ Error getting team quest status: $e');
      return null;
    }
  }

  /// Get team messages for a quest
  Future<Map<String, dynamic>> getTeamMessages({
    required String questId,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      // Mock data - in real implementation would fetch from database
      return {
        'quest_id': questId,
        'messages': [
          {
            'id': 'msg_001',
            'sender_id': 'user_001',
            'sender_name': 'John Doe',
            'message': 'Lets work together on this quest!',
            'timestamp': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
          },
          {
            'id': 'msg_002',
            'sender_id': 'user_002',
            'sender_name': 'Jane Smith',
            'message': 'Great idea! Ill handle the first task.',
            'timestamp': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
          },
        ],
        'pagination': {
          'current_page': (offset ~/ limit) + 1,
          'total_count': 2,
          'has_more': false,
        },
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Error getting team messages: $e');
      return {
        'quest_id': questId,
        'messages': <Map<String, dynamic>>[],
        'pagination': {
          'current_page': 1,
          'total_count': 0,
          'has_more': false,
        },
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get user's team quests
  Future<Map<String, dynamic>> getUserTeamQuests({
    required String userId,
    String? status,
  }) async {
    try {
      // Mock data - in real implementation would fetch from database
      return {
        'user_id': userId,
        'team_quests': [
          {
            'id': 'team_quest_001',
            'title': 'Fitness Challenge Team',
            'description': 'Complete fitness goals as a team',
            'status': 'active',
            'participants': [
              {'id': 'user_001', 'name': 'John Doe', 'role': 'leader'},
              {'id': 'user_002', 'name': 'Jane Smith', 'role': 'member'},
              {'id': userId, 'name': 'Current User', 'role': 'member'},
            ],
            'progress_percentage': 65.0,
            'created_at': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
            'due_date': DateTime.now().add(const Duration(days: 4)).toIso8601String(),
          },
          {
            'id': 'team_quest_002',
            'title': 'Learning Sprint Team',
            'description': 'Complete learning objectives together',
            'status': 'completed',
            'participants': [
              {'id': 'user_003', 'name': 'Bob Johnson', 'role': 'leader'},
              {'id': userId, 'name': 'Current User', 'role': 'member'},
            ],
            'progress_percentage': 100.0,
            'created_at': DateTime.now().subtract(const Duration(days: 10)).toIso8601String(),
            'completed_at': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
          },
        ],
        'total_count': status == null ? 2 : (status == 'active' ? 1 : 1),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Error getting user team quests: $e');
      return {
        'user_id': userId,
        'team_quests': <Map<String, dynamic>>[],
        'total_count': 0,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
