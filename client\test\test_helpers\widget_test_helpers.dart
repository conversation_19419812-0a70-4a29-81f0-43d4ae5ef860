import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'dart:math';

/// Widget test helpers for creating consistent test environments
class WidgetTestHelpers {

  /// Create a MaterialApp wrapper for widget testing
  static Widget createTestApp({
    required Widget child,
    ThemeData? theme,
    List<BlocProvider>? providers,
    Locale? locale,
  }) {
    Widget app = MaterialApp(
      home: Scaffold(body: child),
      theme: theme ?? ThemeData.light(),
      locale: locale,
      localizationsDelegates: const [
        // Add localization delegates if needed
      ],
    );

    if (providers != null && providers.isNotEmpty) {
      app = MultiBlocProvider(
        providers: providers,
        child: app,
      );
    }

    return app;
  }

  /// Create a test app with navigation support
  static Widget createTestAppWithNavigation({
    required Widget child,
    String initialRoute = '/',
    Map<String, WidgetBuilder>? routes,
  }) {
    return MaterialApp(
      home: child,
      initialRoute: initialRoute,
      routes: routes ?? {},
    );
  }

  /// Pump widget with common setup
  static Future<void> pumpTestWidget(
    WidgetTester tester,
    Widget widget, {
    Duration? duration,
    bool settleAfterPump = true,
  }) async {
    await tester.pumpWidget(widget);
    if (settleAfterPump) {
      await tester.pumpAndSettle(duration ?? const Duration(seconds: 1));
    }
  }

  /// Find widget by key with type safety
  static Finder findByKeyAndType<T extends Widget>(String key) {
    return find.descendant(
      of: find.byKey(Key(key)),
      matching: find.byType(T),
    );
  }

  /// Verify widget properties
  static void verifyWidgetProperties<T extends Widget>(
    WidgetTester tester,
    Finder finder,
    void Function(T widget) verifier,
  ) {
    final widget = tester.widget<T>(finder);
    verifier(widget);
  }

  /// Simulate user interaction with delay
  static Future<void> simulateUserTap(
    WidgetTester tester,
    Finder finder, {
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    await tester.tap(finder);
    await tester.pump(delay);
  }

  /// Simulate scrolling
  static Future<void> simulateScroll(
    WidgetTester tester,
    Finder finder,
    Offset offset, {
    Duration duration = const Duration(milliseconds: 300),
  }) async {
    await tester.drag(finder, offset);
    await tester.pumpAndSettle(duration);
  }

  /// Verify accessibility
  static Future<void> verifyAccessibility(WidgetTester tester) async {
    final SemanticsHandle handle = tester.ensureSemantics();
    await expectLater(tester, meetsGuideline(androidTapTargetGuideline));
    await expectLater(tester, meetsGuideline(iOSTapTargetGuideline));
    await expectLater(tester, meetsGuideline(labeledTapTargetGuideline));
    await expectLater(tester, meetsGuideline(textContrastGuideline));
    handle.dispose();
  }

  /// Create responsive test environment
  static Future<void> testResponsiveWidget(
    WidgetTester tester,
    Widget widget,
    List<Size> testSizes,
    void Function(Size size) testCallback,
  ) async {
    for (final size in testSizes) {
      await tester.binding.setSurfaceSize(size);
      await tester.pumpWidget(widget);
      await tester.pumpAndSettle();
      testCallback(size);
    }
  }

  /// Common test sizes for responsive testing
  static const List<Size> commonTestSizes = [
    Size(320, 568), // iPhone SE
    Size(375, 667), // iPhone 8
    Size(414, 896), // iPhone 11 Pro Max
    Size(768, 1024), // iPad
    Size(1024, 768), // iPad Landscape
    Size(1920, 1080), // Desktop
  ];
}

/// Mock data factory for widget tests
class MockDataFactory {
  static final Random _random = Random();

  /// Create mock quest for testing
  static Quest createMockQuest({
    String? id,
    String? title,
    String? description,
    String? createdById,
    QuestStatus? status,
    QuestPriority? priority,
    QuestDifficulty? difficulty,
    QuestCategory? category,
    int? basePoints,
    int? bonusPoints,
    int? totalPoints,
    int? earnedPoints,
    double? progressPercentage,
    List<String>? taskIds,
    List<String>? participantIds,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    final base = basePoints ?? (_random.nextInt(200) + 50);
    final bonus = bonusPoints ?? (_random.nextInt(100) + 25);

    return Quest(
      id: id ?? 'quest_${_random.nextInt(10000)}',
      title: title ?? 'Test Quest ${_random.nextInt(100)}',
      description: description ?? 'This is a test quest description for testing purposes.',
      createdById: createdById ?? 'user_${_random.nextInt(1000)}',
      status: status ?? QuestStatus.active,
      priority: priority ?? QuestPriority.medium,
      difficulty: difficulty ?? QuestDifficulty.intermediate,
      category: category ?? QuestCategory.personal,
      basePoints: base,
      bonusPoints: bonus,
      totalPoints: totalPoints ?? (base + bonus),
      earnedPoints: earnedPoints ?? (_random.nextInt(base)),
      progressPercentage: progressPercentage ?? (_random.nextDouble() * 100),
      taskIds: taskIds ?? ['task_${_random.nextInt(1000)}', 'task_${_random.nextInt(1000)}'],
      participantIds: participantIds ?? ['user_${_random.nextInt(1000)}'],
      tags: tags ?? ['test', 'flutter', 'widget'],
      createdAt: createdAt ?? now.subtract(Duration(days: _random.nextInt(30))),
      updatedAt: updatedAt ?? now.subtract(Duration(hours: _random.nextInt(24))),
    );
  }

  /// Create mock quest progress
  static QuestProgress createMockQuestProgress({
    String? id,
    String? questId,
    String? userId,
    double? progressPercentage,
    int? pointsEarned,
    int? totalPointsAvailable,
    int? tasksCompleted,
    int? totalTasks,
    int? timeSpentMinutes,
  }) {
    final completed = tasksCompleted ?? _random.nextInt(8) + 1;
    final total = totalTasks ?? (completed + _random.nextInt(5) + 1);
    final now = DateTime.now();

    return QuestProgress(
      id: id ?? 'progress_${_random.nextInt(10000)}',
      questId: questId ?? 'quest_${_random.nextInt(10000)}',
      userId: userId ?? 'user_${_random.nextInt(1000)}',
      progressPercentage: progressPercentage ?? (completed / total),
      pointsEarned: pointsEarned ?? _random.nextInt(200),
      totalPointsAvailable: totalPointsAvailable ?? (_random.nextInt(300) + 100),
      tasksCompleted: completed,
      totalTasks: total,
      timeSpentMinutes: timeSpentMinutes ?? _random.nextInt(300),
      milestoneTimestamps: {},
      dailyProgress: {},
      currentStreak: _random.nextInt(10),
      longestStreak: _random.nextInt(20) + 5,
      isActive: true,
      startedAt: now.subtract(Duration(days: _random.nextInt(10))),
      updatedAt: now.subtract(Duration(hours: _random.nextInt(24))),
    );
  }

  /// Create mock user
  static User createMockUser({
    String? id,
    String? email,
    String? displayName,
    UserRole? role,
    String? avatarUrl,
  }) {
    final now = DateTime.now();
    return User(
      id: id ?? 'user_${_random.nextInt(10000)}',
      email: email ?? 'test${_random.nextInt(1000)}@example.com',
      displayName: displayName ?? 'Test User ${_random.nextInt(100)}',
      role: role ?? UserRole.apprentice,
      status: UserStatus.active,
      totalPoints: _random.nextInt(1000) + 100,
      currentLevelPoints: _random.nextInt(100),
      level: _random.nextInt(10) + 1,
      currentStreak: _random.nextInt(30),
      longestStreak: _random.nextInt(100) + 10,
      achievementCount: _random.nextInt(20),
      questsCompleted: _random.nextInt(50),
      tasksCompleted: _random.nextInt(200),
      avatarUrl: avatarUrl,
      createdAt: now.subtract(Duration(days: _random.nextInt(365))),
      updatedAt: now.subtract(Duration(hours: _random.nextInt(72))),
      lastLoginAt: now.subtract(Duration(hours: _random.nextInt(72))),
    );
  }

  /// Create mock achievement
  static Achievement createMockAchievement({
    String? id,
    String? name,
    String? description,
    AchievementType? type,
    AchievementRarity? rarity,
    int? pointsAwarded,
    String? iconUrl,
  }) {
    final now = DateTime.now();
    return Achievement(
      id: id ?? 'achievement_${_random.nextInt(10000)}',
      name: name ?? 'Test Achievement ${_random.nextInt(100)}',
      description: description ?? 'This is a test achievement for testing purposes.',
      type: type ?? AchievementType.progress,
      rarity: rarity ?? AchievementRarity.common,
      iconUrl: iconUrl ?? 'https://example.com/achievement_${_random.nextInt(20)}.png',
      pointsAwarded: pointsAwarded ?? (_random.nextInt(200) + 50),
      progressRequired: _random.nextInt(10) + 1,
      isActive: true,
      createdAt: now.subtract(Duration(days: _random.nextInt(100))),
    );
  }
}

/// Test matchers for custom assertions
class CustomMatchers {
  /// Matcher for checking if a widget has specific accessibility properties
  static Matcher hasAccessibilityLabel(String label) {
    return _HasAccessibilityLabel(label);
  }

  /// Matcher for checking if a widget is responsive
  static Matcher isResponsive() {
    return _IsResponsive();
  }

  /// Matcher for checking color contrast
  static Matcher hasGoodColorContrast() {
    return _HasGoodColorContrast();
  }
}

class _HasAccessibilityLabel extends Matcher {
  final String expectedLabel;
  
  _HasAccessibilityLabel(this.expectedLabel);

  @override
  bool matches(dynamic item, Map matchState) {
    if (item is Widget) {
      // Implementation would check accessibility properties
      return true; // Simplified for now
    }
    return false;
  }

  @override
  Description describe(Description description) {
    return description.add('has accessibility label "$expectedLabel"');
  }
}

class _IsResponsive extends Matcher {
  @override
  bool matches(dynamic item, Map matchState) {
    // Implementation would check responsive behavior
    return true; // Simplified for now
  }

  @override
  Description describe(Description description) {
    return description.add('is responsive to different screen sizes');
  }
}

class _HasGoodColorContrast extends Matcher {
  @override
  bool matches(dynamic item, Map matchState) {
    // Implementation would check color contrast ratios
    return true; // Simplified for now
  }

  @override
  Description describe(Description description) {
    return description.add('has good color contrast for accessibility');
  }
}

/// Performance testing helpers
class PerformanceTestHelpers {
  /// Measure widget build time
  static Future<Duration> measureBuildTime(
    WidgetTester tester,
    Widget widget,
  ) async {
    final stopwatch = Stopwatch()..start();
    await tester.pumpWidget(widget);
    stopwatch.stop();
    return stopwatch.elapsed;
  }

  /// Measure animation performance
  static Future<Map<String, dynamic>> measureAnimationPerformance(
    WidgetTester tester,
    Widget widget,
    VoidCallback triggerAnimation,
  ) async {
    await tester.pumpWidget(widget);
    
    final stopwatch = Stopwatch()..start();
    triggerAnimation();
    await tester.pumpAndSettle();
    stopwatch.stop();
    
    return {
      'duration': stopwatch.elapsed,
      'frame_count': tester.binding.transientCallbackCount,
    };
  }
}
