import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../common/user_avatar.dart';
import '../common/notification_badge.dart';
import '../../blocs/auth/auth_bloc.dart';

/// Side navigation widget for tablet and desktop layouts
class SideNavigationWidget extends StatelessWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Whether the navigation is collapsed
  final bool isCollapsed;
  
  /// Whether to show header with user info
  final bool showHeader;
  
  /// Custom header widget
  final Widget? customHeader;
  
  /// Custom footer widget
  final Widget? customFooter;
  
  /// Background color
  final Color? backgroundColor;

  const SideNavigationWidget({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.isCollapsed = false,
    this.showHeader = false,
    this.customHeader,
    this.customFooter,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final width = isCollapsed 
        ? AppConstants.sideNavCollapsedWidth 
        : AppConstants.sideNavWidth;

    return Container(
      width: width,
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          if (showHeader) _buildHeader(context),
          Expanded(
            child: _buildNavigationItems(context),
          ),
          if (customFooter != null) customFooter!,
          if (!isCollapsed) _buildFooter(context),
        ],
      ),
    );
  }

  /// Build header section
  Widget _buildHeader(BuildContext context) {
    if (customHeader != null) {
      return customHeader!;
    }

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthAuthenticated) {
          return Container(
            padding: EdgeInsets.all(
              isCollapsed ? AppConstants.smallPadding : AppConstants.defaultPadding,
            ),
            child: isCollapsed 
                ? UserAvatar(
                    user: state.user,
                    size: 40,
                    showLevelBadge: true,
                  )
                : UserAvatarWithInfo(
                    user: state.user,
                    avatarSize: 48,
                    showName: true,
                    showLevel: true,
                    direction: Axis.vertical,
                    spacing: 8,
                  ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// Build navigation items
  Widget _buildNavigationItems(BuildContext context) {
    final items = _getNavigationItems();
    
    return ListView.builder(
      padding: EdgeInsets.symmetric(
        vertical: AppConstants.smallPadding,
        horizontal: isCollapsed ? 4 : AppConstants.smallPadding,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final isSelected = index == selectedIndex;
        
        return _SideNavigationItem(
          item: item,
          isSelected: isSelected,
          isCollapsed: isCollapsed,
          onTap: () => onItemSelected?.call(index),
        );
      },
    );
  }

  /// Build footer section
  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          const Divider(),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: [
              Icon(
                Icons.explore,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppConstants.appName,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    Text(
                      'v${AppConstants.appVersion}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Get navigation items configuration
  List<SideNavigationItemData> _getNavigationItems() {
    return [
      SideNavigationItemData(
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard,
        label: 'Dashboard',
        tooltip: 'View your dashboard',
      ),
      SideNavigationItemData(
        icon: Icons.explore_outlined,
        selectedIcon: Icons.explore,
        label: 'Quests',
        tooltip: 'Browse and manage quests',
      ),
      SideNavigationItemData(
        icon: Icons.task_outlined,
        selectedIcon: Icons.task,
        label: 'Tasks',
        tooltip: 'View your tasks',
      ),
      SideNavigationItemData(
        icon: Icons.chat_bubble_outline,
        selectedIcon: Icons.chat_bubble,
        label: 'Messages',
        tooltip: 'Chat with team members',
        badgeCount: 3, // TODO: Get from state
      ),
      SideNavigationItemData(
        icon: Icons.leaderboard_outlined,
        selectedIcon: Icons.leaderboard,
        label: 'Leaderboard',
        tooltip: 'View rankings',
      ),
      SideNavigationItemData(
        icon: Icons.emoji_events_outlined,
        selectedIcon: Icons.emoji_events,
        label: 'Achievements',
        tooltip: 'View your achievements',
      ),
      SideNavigationItemData(
        icon: Icons.analytics_outlined,
        selectedIcon: Icons.analytics,
        label: 'Analytics',
        tooltip: 'View performance analytics',
      ),
      SideNavigationItemData(
        icon: Icons.settings_outlined,
        selectedIcon: Icons.settings,
        label: 'Settings',
        tooltip: 'App settings',
      ),
    ];
  }
}

/// Individual side navigation item
class _SideNavigationItem extends StatefulWidget {
  final SideNavigationItemData item;
  final bool isSelected;
  final bool isCollapsed;
  final VoidCallback? onTap;

  const _SideNavigationItem({
    required this.item,
    required this.isSelected,
    required this.isCollapsed,
    this.onTap,
  });

  @override
  State<_SideNavigationItem> createState() => _SideNavigationItemState();
}

class _SideNavigationItemState extends State<_SideNavigationItem> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final item = Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        child: AnimatedContainer(
          duration: AppConstants.shortAnimation,
          margin: EdgeInsets.symmetric(
            horizontal: widget.isCollapsed ? 4 : 0,
          ),
          decoration: BoxDecoration(
            color: widget.isSelected
                ? Theme.of(context).colorScheme.primaryContainer
                : _isHovered
                    ? Theme.of(context).colorScheme.surfaceContainerHighest
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          child: InkWell(
            onTap: widget.onTap,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: widget.isCollapsed ? 8 : AppConstants.defaultPadding,
                vertical: AppConstants.defaultPadding,
              ),
              child: widget.isCollapsed
                  ? _buildCollapsedItem(context)
                  : _buildExpandedItem(context),
            ),
          ),
        ),
      ),
    );

    if (widget.isCollapsed && widget.item.tooltip != null) {
      return Tooltip(
        message: widget.item.tooltip!,
        preferBelow: false,
        child: item,
      );
    }

    return item;
  }

  /// Build collapsed item (icon only)
  Widget _buildCollapsedItem(BuildContext context) {
    final color = widget.isSelected
        ? Theme.of(context).colorScheme.onPrimaryContainer
        : Theme.of(context).colorScheme.onSurface;

    Widget iconWidget = Icon(
      widget.isSelected ? widget.item.selectedIcon : widget.item.icon,
      color: color,
      size: 24,
    );

    if (widget.item.badgeCount != null && widget.item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: widget.item.badgeCount!,
        child: iconWidget,
      );
    }

    return Center(child: iconWidget);
  }

  /// Build expanded item (icon + label)
  Widget _buildExpandedItem(BuildContext context) {
    final color = widget.isSelected
        ? Theme.of(context).colorScheme.onPrimaryContainer
        : Theme.of(context).colorScheme.onSurface;

    Widget iconWidget = Icon(
      widget.isSelected ? widget.item.selectedIcon : widget.item.icon,
      color: color,
      size: 24,
    );

    if (widget.item.badgeCount != null && widget.item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: widget.item.badgeCount!,
        child: iconWidget,
      );
    }

    return Row(
      children: [
        iconWidget,
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: Text(
            widget.item.label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color,
              fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }
}

/// Side navigation item data class
class SideNavigationItemData {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final String? tooltip;
  final int? badgeCount;

  const SideNavigationItemData({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    this.tooltip,
    this.badgeCount,
  });
}

/// Collapsible side navigation with toggle button
class CollapsibleSideNavigation extends StatefulWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Initial collapsed state
  final bool initiallyCollapsed;
  
  /// Callback when collapse state changes
  final ValueChanged<bool>? onCollapseChanged;

  const CollapsibleSideNavigation({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.initiallyCollapsed = false,
    this.onCollapseChanged,
  });

  @override
  State<CollapsibleSideNavigation> createState() => _CollapsibleSideNavigationState();
}

class _CollapsibleSideNavigationState extends State<CollapsibleSideNavigation> {
  late bool _isCollapsed;

  @override
  void initState() {
    super.initState();
    _isCollapsed = widget.initiallyCollapsed;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SideNavigationWidget(
          selectedIndex: widget.selectedIndex,
          onItemSelected: widget.onItemSelected,
          isCollapsed: _isCollapsed,
          showHeader: true,
        ),
        Positioned(
          top: AppConstants.defaultPadding,
          right: -12,
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              onPressed: _toggleCollapse,
              icon: AnimatedRotation(
                turns: _isCollapsed ? 0.5 : 0,
                duration: AppConstants.mediumAnimation,
                child: const Icon(Icons.chevron_left),
              ),
              iconSize: 20,
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                minWidth: 24,
                minHeight: 24,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _toggleCollapse() {
    setState(() {
      _isCollapsed = !_isCollapsed;
    });
    widget.onCollapseChanged?.call(_isCollapsed);
  }
}
