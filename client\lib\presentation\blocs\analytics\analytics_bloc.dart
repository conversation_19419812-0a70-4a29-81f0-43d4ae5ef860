import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/repositories/api_repository.dart';

// Events
abstract class AnalyticsEvent extends Equatable {
  const AnalyticsEvent();
  
  @override
  List<Object?> get props => [];
}

class LoadDashboard extends AnalyticsEvent {
  final String userId;
  
  const LoadDashboard({required this.userId});
  
  @override
  List<Object?> get props => [userId];
}

class LoadMetrics extends AnalyticsEvent {
  final Map<String, String>? filters;
  
  const LoadMetrics({this.filters});
  
  @override
  List<Object?> get props => [filters];
}

class LoadInsights extends AnalyticsEvent {
  final Map<String, String>? params;
  
  const LoadInsights({this.params});
  
  @override
  List<Object?> get props => [params];
}

class TrackEvent extends AnalyticsEvent {
  final Map<String, dynamic> eventData;
  
  const TrackEvent({required this.eventData});
  
  @override
  List<Object?> get props => [eventData];
}

class GenerateReport extends AnalyticsEvent {
  final Map<String, dynamic> reportData;
  
  const GenerateReport({required this.reportData});
  
  @override
  List<Object?> get props => [reportData];
}

class RefreshAnalytics extends AnalyticsEvent {
  const RefreshAnalytics();
}

// States
abstract class AnalyticsState extends Equatable {
  const AnalyticsState();
  
  @override
  List<Object?> get props => [];
}

class AnalyticsInitial extends AnalyticsState {
  const AnalyticsInitial();
}

class AnalyticsLoading extends AnalyticsState {
  const AnalyticsLoading();
}

class AnalyticsLoaded extends AnalyticsState {
  final Map<String, dynamic>? dashboard;
  final Map<String, dynamic>? metrics;
  final List<dynamic>? insights;
  final Map<String, dynamic>? lastReport;
  
  const AnalyticsLoaded({
    this.dashboard,
    this.metrics,
    this.insights,
    this.lastReport,
  });
  
  @override
  List<Object?> get props => [dashboard, metrics, insights, lastReport];
  
  AnalyticsLoaded copyWith({
    Map<String, dynamic>? dashboard,
    Map<String, dynamic>? metrics,
    List<dynamic>? insights,
    Map<String, dynamic>? lastReport,
  }) {
    return AnalyticsLoaded(
      dashboard: dashboard ?? this.dashboard,
      metrics: metrics ?? this.metrics,
      insights: insights ?? this.insights,
      lastReport: lastReport ?? this.lastReport,
    );
  }
}

class AnalyticsError extends AnalyticsState {
  final String message;
  
  const AnalyticsError({required this.message});
  
  @override
  List<Object?> get props => [message];
}

class EventTracked extends AnalyticsState {
  final String message;
  
  const EventTracked({required this.message});
  
  @override
  List<Object?> get props => [message];
}

class ReportGenerated extends AnalyticsState {
  final Map<String, dynamic> report;
  
  const ReportGenerated({required this.report});
  
  @override
  List<Object?> get props => [report];
}

// BLoC
class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  final ApiRepository repository;
  
  AnalyticsBloc({required this.repository}) : super(const AnalyticsInitial()) {
    on<LoadDashboard>(_onLoadDashboard);
    on<LoadMetrics>(_onLoadMetrics);
    on<LoadInsights>(_onLoadInsights);
    on<TrackEvent>(_onTrackEvent);
    on<GenerateReport>(_onGenerateReport);
    on<RefreshAnalytics>(_onRefreshAnalytics);
  }

  Future<void> _onLoadDashboard(LoadDashboard event, Emitter<AnalyticsState> emit) async {
    emit(const AnalyticsLoading());
    
    try {
      final response = await repository.getDashboard(event.userId);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is AnalyticsLoaded ? state as AnalyticsLoaded : const AnalyticsLoaded();
        emit(currentState.copyWith(dashboard: response.data));
      } else {
        emit(AnalyticsError(message: response.error ?? 'Failed to load dashboard'));
      }
    } catch (e) {
      emit(AnalyticsError(message: 'Error loading dashboard: $e'));
    }
  }

  Future<void> _onLoadMetrics(LoadMetrics event, Emitter<AnalyticsState> emit) async {
    emit(const AnalyticsLoading());
    
    try {
      final response = await repository.getMetrics(filters: event.filters);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is AnalyticsLoaded ? state as AnalyticsLoaded : const AnalyticsLoaded();
        emit(currentState.copyWith(metrics: response.data));
      } else {
        emit(AnalyticsError(message: response.error ?? 'Failed to load metrics'));
      }
    } catch (e) {
      emit(AnalyticsError(message: 'Error loading metrics: $e'));
    }
  }

  Future<void> _onLoadInsights(LoadInsights event, Emitter<AnalyticsState> emit) async {
    try {
      final response = await repository.getInsights(params: event.params);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is AnalyticsLoaded ? state as AnalyticsLoaded : const AnalyticsLoaded();
        emit(currentState.copyWith(insights: response.data));
      } else {
        emit(AnalyticsError(message: response.error ?? 'Failed to load insights'));
      }
    } catch (e) {
      emit(AnalyticsError(message: 'Error loading insights: $e'));
    }
  }

  Future<void> _onTrackEvent(TrackEvent event, Emitter<AnalyticsState> emit) async {
    try {
      final response = await repository.trackEvent(event.eventData);
      
      if (response.isSuccess) {
        emit(const EventTracked(message: 'Event tracked successfully'));
        // Return to previous state after showing success
        if (state is AnalyticsLoaded) {
          emit(state as AnalyticsLoaded);
        } else {
          emit(const AnalyticsLoaded());
        }
      } else {
        emit(AnalyticsError(message: response.error ?? 'Failed to track event'));
      }
    } catch (e) {
      emit(AnalyticsError(message: 'Error tracking event: $e'));
    }
  }

  Future<void> _onGenerateReport(GenerateReport event, Emitter<AnalyticsState> emit) async {
    emit(const AnalyticsLoading());
    
    try {
      final response = await repository.generateReport(event.reportData);
      
      if (response.isSuccess && response.data != null) {
        emit(ReportGenerated(report: response.data!));
        // Also update the loaded state with the new report
        final currentState = state is AnalyticsLoaded ? state as AnalyticsLoaded : const AnalyticsLoaded();
        emit(currentState.copyWith(lastReport: response.data));
      } else {
        emit(AnalyticsError(message: response.error ?? 'Failed to generate report'));
      }
    } catch (e) {
      emit(AnalyticsError(message: 'Error generating report: $e'));
    }
  }

  Future<void> _onRefreshAnalytics(RefreshAnalytics event, Emitter<AnalyticsState> emit) async {
    emit(const AnalyticsLoading());
    
    try {
      // Refresh all analytics data (you might want to store user ID in the bloc)
      // For now, just emit a basic loaded state
      emit(const AnalyticsLoaded());
    } catch (e) {
      emit(AnalyticsError(message: 'Error refreshing analytics: $e'));
    }
  }
}