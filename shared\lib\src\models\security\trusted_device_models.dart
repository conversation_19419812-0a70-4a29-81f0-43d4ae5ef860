import 'package:json_annotation/json_annotation.dart';

part 'trusted_device_models.g.dart';

enum DeviceStatus { active, revoked, expired, suspicious }
enum DeviceTrustLevel { low, medium, high, verified }
enum DeviceType { mobile, desktop, tablet, unknown }

@JsonSerializable()
class TrustedDevice {
  final String id;
  @Json<PERSON><PERSON>(name: 'user_id')
  final String userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'device_fingerprint')
  final String deviceFingerprint;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'device_name')
  final String deviceName;
  @Json<PERSON>ey(name: 'device_type')
  final DeviceType deviceType;
  @Json<PERSON>ey(name: 'device_model')
  final String? deviceModel;
  @Json<PERSON>ey(name: 'operating_system')
  final String? operatingSystem;
  @J<PERSON><PERSON><PERSON>(name: 'browser_info')
  final String? browserInfo;
  @JsonKey(name: 'ip_address')
  final String ipAddress;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_agent')
  final String? userAgent;
  final DeviceStatus status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'trust_level')
  final DeviceTrustLevel trustLevel;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_seen')
  final DateTime firstSeen;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_seen')
  final DateTime lastSeen;
  @Json<PERSON><PERSON>(name: 'trusted_at')
  final DateTime? trustedAt;
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
  @JsonKey(name: 'access_count')
  final int accessCount;
  final Map<String, dynamic> metadata;
  @JsonKey(name: 'revoked_by')
  final String? revokedBy;
  @JsonKey(name: 'revocation_reason')
  final String? revocationReason;
  @JsonKey(name: 'revoked_at')
  final DateTime? revokedAt;

  const TrustedDevice({
    required this.id,
    required this.userId,
    required this.deviceFingerprint,
    required this.deviceName,
    required this.deviceType,
    this.deviceModel,
    this.operatingSystem,
    this.browserInfo,
    required this.ipAddress,
    this.userAgent,
    this.status = DeviceStatus.active,
    this.trustLevel = DeviceTrustLevel.low,
    required this.firstSeen,
    required this.lastSeen,
    this.trustedAt,
    this.expiresAt,
    this.accessCount = 1,
    this.metadata = const {},
    this.revokedBy,
    this.revocationReason,
    this.revokedAt,
  });

  factory TrustedDevice.fromJson(Map<String, dynamic> json) =>
      _$TrustedDeviceFromJson(json);

  Map<String, dynamic> toJson() => _$TrustedDeviceToJson(this);

  bool get isActive => status == DeviceStatus.active;
  bool get isTrusted => trustedAt != null && isActive && !isExpired;
  bool get isExpired => expiresAt != null && expiresAt!.isBefore(DateTime.now());
  bool get isRevoked => status == DeviceStatus.revoked;
  bool get isSuspicious => status == DeviceStatus.suspicious;

  String get displayName => deviceName.isNotEmpty ? deviceName : 'Unknown Device';
  String get shortFingerprint => deviceFingerprint.length > 8 
      ? '${deviceFingerprint.substring(0, 8)}...'
      : deviceFingerprint;

  Duration? get timeUntilExpiry => expiresAt?.difference(DateTime.now());
  Duration get ageSinceRegistration => DateTime.now().difference(firstSeen);
  Duration get timeSinceLastSeen => DateTime.now().difference(lastSeen);

  TrustedDevice copyWith({
    String? id,
    String? userId,
    String? deviceFingerprint,
    String? deviceName,
    DeviceType? deviceType,
    String? deviceModel,
    String? operatingSystem,
    String? browserInfo,
    String? ipAddress,
    String? userAgent,
    DeviceStatus? status,
    DeviceTrustLevel? trustLevel,
    DateTime? firstSeen,
    DateTime? lastSeen,
    DateTime? trustedAt,
    DateTime? expiresAt,
    int? accessCount,
    Map<String, dynamic>? metadata,
    String? revokedBy,
    String? revocationReason,
    DateTime? revokedAt,
  }) {
    return TrustedDevice(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      deviceFingerprint: deviceFingerprint ?? this.deviceFingerprint,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      deviceModel: deviceModel ?? this.deviceModel,
      operatingSystem: operatingSystem ?? this.operatingSystem,
      browserInfo: browserInfo ?? this.browserInfo,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      status: status ?? this.status,
      trustLevel: trustLevel ?? this.trustLevel,
      firstSeen: firstSeen ?? this.firstSeen,
      lastSeen: lastSeen ?? this.lastSeen,
      trustedAt: trustedAt ?? this.trustedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      accessCount: accessCount ?? this.accessCount,
      metadata: metadata ?? this.metadata,
      revokedBy: revokedBy ?? this.revokedBy,
      revocationReason: revocationReason ?? this.revocationReason,
      revokedAt: revokedAt ?? this.revokedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TrustedDevice && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@JsonSerializable()
class DeviceRegistrationRequest {
  @JsonKey(name: 'device_fingerprint')
  final String deviceFingerprint;
  @JsonKey(name: 'device_name')
  final String deviceName;
  @JsonKey(name: 'device_type')
  final DeviceType deviceType;
  @JsonKey(name: 'device_model')
  final String? deviceModel;
  @JsonKey(name: 'operating_system')
  final String? operatingSystem;
  @JsonKey(name: 'browser_info')
  final String? browserInfo;
  @JsonKey(name: 'ip_address')
  final String ipAddress;
  @JsonKey(name: 'user_agent')
  final String? userAgent;
  @JsonKey(name: 'trust_immediately')
  final bool trustImmediately;
  @JsonKey(name: 'trust_duration_hours')
  final int? trustDurationHours;
  final Map<String, dynamic> metadata;

  const DeviceRegistrationRequest({
    required this.deviceFingerprint,
    required this.deviceName,
    required this.deviceType,
    this.deviceModel,
    this.operatingSystem,
    this.browserInfo,
    required this.ipAddress,
    this.userAgent,
    this.trustImmediately = false,
    this.trustDurationHours,
    this.metadata = const {},
  });

  factory DeviceRegistrationRequest.fromJson(Map<String, dynamic> json) =>
      _$DeviceRegistrationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceRegistrationRequestToJson(this);

  Duration? get trustDuration => trustDurationHours != null 
      ? Duration(hours: trustDurationHours!)
      : null;

  DeviceRegistrationRequest copyWith({
    String? deviceFingerprint,
    String? deviceName,
    DeviceType? deviceType,
    String? deviceModel,
    String? operatingSystem,
    String? browserInfo,
    String? ipAddress,
    String? userAgent,
    bool? trustImmediately,
    int? trustDurationHours,
    Map<String, dynamic>? metadata,
  }) {
    return DeviceRegistrationRequest(
      deviceFingerprint: deviceFingerprint ?? this.deviceFingerprint,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      deviceModel: deviceModel ?? this.deviceModel,
      operatingSystem: operatingSystem ?? this.operatingSystem,
      browserInfo: browserInfo ?? this.browserInfo,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      trustImmediately: trustImmediately ?? this.trustImmediately,
      trustDurationHours: trustDurationHours ?? this.trustDurationHours,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable()
class DeviceVerificationResult {
  @JsonKey(name: 'is_trusted')
  final bool isTrusted;
  @JsonKey(name: 'requires_mfa')
  final bool requiresMFA;
  @JsonKey(name: 'is_new_device')
  final bool isNewDevice;
  @JsonKey(name: 'is_suspicious')
  final bool isSuspicious;
  final TrustedDevice? device;
  @JsonKey(name: 'warning_message')
  final String? warningMessage;
  @JsonKey(name: 'risk_factors')
  final List<String> riskFactors;
  final Map<String, dynamic> metadata;
  @JsonKey(name: 'verification_timestamp')
  final DateTime verificationTimestamp;

  DeviceVerificationResult({
    required this.isTrusted,
    required this.requiresMFA,
    required this.isNewDevice,
    this.isSuspicious = false,
    this.device,
    this.warningMessage,
    this.riskFactors = const [],
    this.metadata = const {},
    DateTime? verificationTimestamp,
  }) : verificationTimestamp = verificationTimestamp ?? DateTime.fromMillisecondsSinceEpoch(0);

  factory DeviceVerificationResult.fromJson(Map<String, dynamic> json) =>
      _$DeviceVerificationResultFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceVerificationResultToJson(this);

  bool get hasWarnings => warningMessage != null || riskFactors.isNotEmpty;
  bool get isSecure => isTrusted && !isSuspicious && riskFactors.isEmpty;
  int get riskLevel => riskFactors.length;

  String get statusDescription {
    if (isTrusted) return 'Trusted Device';
    if (isSuspicious) return 'Suspicious Device';
    if (isNewDevice) return 'New Device';
    return 'Untrusted Device';
  }

  DeviceVerificationResult copyWith({
    bool? isTrusted,
    bool? requiresMFA,
    bool? isNewDevice,
    bool? isSuspicious,
    TrustedDevice? device,
    String? warningMessage,
    List<String>? riskFactors,
    Map<String, dynamic>? metadata,
    DateTime? verificationTimestamp,
  }) {
    return DeviceVerificationResult(
      isTrusted: isTrusted ?? this.isTrusted,
      requiresMFA: requiresMFA ?? this.requiresMFA,
      isNewDevice: isNewDevice ?? this.isNewDevice,
      isSuspicious: isSuspicious ?? this.isSuspicious,
      device: device ?? this.device,
      warningMessage: warningMessage ?? this.warningMessage,
      riskFactors: riskFactors ?? this.riskFactors,
      metadata: metadata ?? this.metadata,
      verificationTimestamp: verificationTimestamp ?? this.verificationTimestamp,
    );
  }
}

@JsonSerializable()
class TrustedDeviceConfig {
  @JsonKey(name: 'default_trust_duration_days')
  final int defaultTrustDurationDays;
  @JsonKey(name: 'max_trusted_devices')
  final int maxTrustedDevices;
  @JsonKey(name: 'require_mfa_for_new_devices')
  final bool requireMFAForNewDevices;
  @JsonKey(name: 'auto_trust_after_mfa')
  final bool autoTrustAfterMFA;
  @JsonKey(name: 'suspicious_device_timeout_hours')
  final int suspiciousDeviceTimeoutHours;
  @JsonKey(name: 'max_failed_attempts')
  final int maxFailedAttempts;
  @JsonKey(name: 'allowed_countries')
  final List<String> allowedCountries;
  @JsonKey(name: 'enable_location_tracking')
  final bool enableLocationTracking;

  const TrustedDeviceConfig({
    this.defaultTrustDurationDays = 30,
    this.maxTrustedDevices = 10,
    this.requireMFAForNewDevices = true,
    this.autoTrustAfterMFA = true,
    this.suspiciousDeviceTimeoutHours = 24,
    this.maxFailedAttempts = 5,
    this.allowedCountries = const [],
    this.enableLocationTracking = false,
  });

  factory TrustedDeviceConfig.fromJson(Map<String, dynamic> json) =>
      _$TrustedDeviceConfigFromJson(json);

  Map<String, dynamic> toJson() => _$TrustedDeviceConfigToJson(this);

  Duration get defaultTrustDuration => Duration(days: defaultTrustDurationDays);
  Duration get suspiciousDeviceTimeout => Duration(hours: suspiciousDeviceTimeoutHours);

  TrustedDeviceConfig copyWith({
    int? defaultTrustDurationDays,
    int? maxTrustedDevices,
    bool? requireMFAForNewDevices,
    bool? autoTrustAfterMFA,
    int? suspiciousDeviceTimeoutHours,
    int? maxFailedAttempts,
    List<String>? allowedCountries,
    bool? enableLocationTracking,
  }) {
    return TrustedDeviceConfig(
      defaultTrustDurationDays: defaultTrustDurationDays ?? this.defaultTrustDurationDays,
      maxTrustedDevices: maxTrustedDevices ?? this.maxTrustedDevices,
      requireMFAForNewDevices: requireMFAForNewDevices ?? this.requireMFAForNewDevices,
      autoTrustAfterMFA: autoTrustAfterMFA ?? this.autoTrustAfterMFA,
      suspiciousDeviceTimeoutHours: suspiciousDeviceTimeoutHours ?? this.suspiciousDeviceTimeoutHours,
      maxFailedAttempts: maxFailedAttempts ?? this.maxFailedAttempts,
      allowedCountries: allowedCountries ?? this.allowedCountries,
      enableLocationTracking: enableLocationTracking ?? this.enableLocationTracking,
    );
  }
}

@JsonSerializable()
class DeviceStats {
  @JsonKey(name: 'total_devices')
  final int totalDevices;
  @JsonKey(name: 'active_devices')
  final int activeDevices;
  @JsonKey(name: 'trusted_devices')
  final int trustedDevices;
  @JsonKey(name: 'suspicious_devices')
  final int suspiciousDevices;
  @JsonKey(name: 'revoked_devices')
  final int revokedDevices;
  @JsonKey(name: 'expired_devices')
  final int expiredDevices;
  @JsonKey(name: 'last_activity')
  final DateTime? lastActivity;
  @JsonKey(name: 'security_score')
  final double securityScore;
  @JsonKey(name: 'needs_cleanup')
  final bool needsCleanup;

  const DeviceStats({
    required this.totalDevices,
    required this.activeDevices,
    required this.trustedDevices,
    required this.suspiciousDevices,
    required this.revokedDevices,
    required this.expiredDevices,
    this.lastActivity,
    required this.securityScore,
    required this.needsCleanup,
  });

  factory DeviceStats.fromJson(Map<String, dynamic> json) =>
      _$DeviceStatsFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceStatsToJson(this);

  bool get hasDevices => totalDevices > 0;
  bool get hasActiveDevices => activeDevices > 0;
  bool get hasSecurityIssues => suspiciousDevices > 0 || needsCleanup;
  
  double get trustRatio => activeDevices > 0 ? trustedDevices / activeDevices : 0.0;
  double get suspiciousRatio => totalDevices > 0 ? suspiciousDevices / totalDevices : 0.0;

  String get securityLevel {
    if (securityScore >= 0.9) return 'Excellent';
    if (securityScore >= 0.7) return 'Good';
    if (securityScore >= 0.5) return 'Fair';
    if (securityScore >= 0.3) return 'Poor';
    return 'Critical';
  }

  DeviceStats copyWith({
    int? totalDevices,
    int? activeDevices,
    int? trustedDevices,
    int? suspiciousDevices,
    int? revokedDevices,
    int? expiredDevices,
    DateTime? lastActivity,
    double? securityScore,
    bool? needsCleanup,
  }) {
    return DeviceStats(
      totalDevices: totalDevices ?? this.totalDevices,
      activeDevices: activeDevices ?? this.activeDevices,
      trustedDevices: trustedDevices ?? this.trustedDevices,
      suspiciousDevices: suspiciousDevices ?? this.suspiciousDevices,
      revokedDevices: revokedDevices ?? this.revokedDevices,
      expiredDevices: expiredDevices ?? this.expiredDevices,
      lastActivity: lastActivity ?? this.lastActivity,
      securityScore: securityScore ?? this.securityScore,
      needsCleanup: needsCleanup ?? this.needsCleanup,
    );
  }
}

// Request DTOs
@JsonSerializable()
class TrustDeviceRequest {
  @JsonKey(name: 'trust_duration_hours')
  final int? trustDurationHours;
  final String? reason;
  @JsonKey(name: 'trusted_by')
  final String? trustedBy;

  const TrustDeviceRequest({
    this.trustDurationHours,
    this.reason = 'Manual trust',
    this.trustedBy,
  });

  factory TrustDeviceRequest.fromJson(Map<String, dynamic> json) =>
      _$TrustDeviceRequestFromJson(json);

  Map<String, dynamic> toJson() => _$TrustDeviceRequestToJson(this);

  Duration? get trustDuration => trustDurationHours != null 
      ? Duration(hours: trustDurationHours!)
      : null;
}

@JsonSerializable()
class RevokeDeviceRequest {
  final String reason;
  @JsonKey(name: 'revoked_by')
  final String? revokedBy;

  const RevokeDeviceRequest({
    this.reason = 'Manual revocation',
    this.revokedBy,
  });

  factory RevokeDeviceRequest.fromJson(Map<String, dynamic> json) =>
      _$RevokeDeviceRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RevokeDeviceRequestToJson(this);
}

@JsonSerializable()
class DeviceVerificationRequest {
  @JsonKey(name: 'device_fingerprint')
  final String deviceFingerprint;
  @JsonKey(name: 'ip_address')
  final String? ipAddress;
  @JsonKey(name: 'user_agent')
  final String? userAgent;

  const DeviceVerificationRequest({
    required this.deviceFingerprint,
    this.ipAddress,
    this.userAgent,
  });

  factory DeviceVerificationRequest.fromJson(Map<String, dynamic> json) =>
      _$DeviceVerificationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceVerificationRequestToJson(this);
}

// Response DTOs
@JsonSerializable()
class TrustedDeviceResponse {
  final TrustedDevice data;
  final String message;
  final bool success;

  const TrustedDeviceResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory TrustedDeviceResponse.fromJson(Map<String, dynamic> json) =>
      _$TrustedDeviceResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TrustedDeviceResponseToJson(this);
}

@JsonSerializable()
class DeviceVerificationResponse {
  final DeviceVerificationResult data;
  final String message;
  final bool success;

  const DeviceVerificationResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory DeviceVerificationResponse.fromJson(Map<String, dynamic> json) =>
      _$DeviceVerificationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceVerificationResponseToJson(this);
}

@JsonSerializable()
class DeviceListResponse {
  final List<TrustedDevice> data;
  final String message;
  final bool success;
  final DeviceStats? stats;

  const DeviceListResponse({
    required this.data,
    required this.message,
    this.success = true,
    this.stats,
  });

  factory DeviceListResponse.fromJson(Map<String, dynamic> json) =>
      _$DeviceListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceListResponseToJson(this);
}

@JsonSerializable()
class DeviceStatsResponse {
  final DeviceStats data;
  final String message;
  final bool success;

  const DeviceStatsResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory DeviceStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$DeviceStatsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceStatsResponseToJson(this);
}