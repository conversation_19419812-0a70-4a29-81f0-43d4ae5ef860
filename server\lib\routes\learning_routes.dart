import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/learning_service.dart';

/// Learning management system routes handler
class LearningRoutes {
  static late LearningService _learningService;

  static void initialize(LearningService learningService) {
    _learningService = learningService;
  }

  static Router createRouter() {
    final router = Router()
      // Health check
      ..get('/health', _healthHandler)
      
      // Course Management
      ..post('/courses', _createCourseHandler)
      ..get('/courses/<courseId>', _getCourseHandler)
      ..put('/courses/<courseId>', _updateCourseHandler)
      ..get('/courses', _searchCoursesHandler)
      
      // Lesson Management
      ..post('/lessons', _createLessonHandler)
      ..get('/lessons/<lessonId>', _getLessonHandler)
      
      // Enrollment Management
      ..post('/enrollments', _enrollUserHandler)
      ..get('/users/<userId>/enrollments', _getUserEnrollmentsHandler)
      
      // Progress Tracking
      ..post('/users/<userId>/courses/<courseId>/progress', _updateProgressHandler)
      ..get('/users/<userId>/courses/<courseId>/progress', _getUserProgressHandler)
      
      // Certificate Management
      ..post('/users/<userId>/courses/<courseId>/certificate', _generateCertificateHandler)
      ..get('/users/<userId>/certificates', _getUserCertificatesHandler)
      
      // Assessment and Quiz Management
      ..post('/users/<userId>/lessons/<lessonId>/quiz', _submitQuizAttemptHandler)
      
      // Analytics and Reporting
      ..get('/users/<userId>/analytics', _getLearnerAnalyticsHandler);

    return router;
  }

  static Response _healthHandler(Request request) {
    return Response.ok(jsonEncode({
      'status': 'operational',
      'timestamp': DateTime.now().toIso8601String(),
      'service': 'learning',
      'version': '1.0.0'
    }));
  }

  // Course Management Handlers
  static Future<Response> _createCourseHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _learningService.createCourse(data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to create course: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getCourseHandler(Request request, String courseId) async {
    try {
      final result = await _learningService.getCourse(courseId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get course: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _updateCourseHandler(Request request, String courseId) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _learningService.updateCourse(courseId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to update course: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _searchCoursesHandler(Request request) async {
    try {
      final params = request.url.queryParameters;
      
      final result = await _learningService.searchCourses(
        query: params['query'],
        category: params['category'],
        level: params['level'],
        language: params['language'],
        minRating: double.tryParse(params['minRating'] ?? ''),
        maxPrice: int.tryParse(params['maxPrice'] ?? ''),
        page: int.tryParse(params['page'] ?? '1') ?? 1,
        limit: int.tryParse(params['limit'] ?? '20') ?? 20,
      );
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to search courses: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Lesson Management Handlers
  static Future<Response> _createLessonHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _learningService.createLesson(data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to create lesson: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getLessonHandler(Request request, String lessonId) async {
    try {
      final result = await _learningService.getLesson(lessonId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get lesson: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Enrollment Management Handlers
  static Future<Response> _enrollUserHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _learningService.enrollUser(
        data['userId'] as String,
        data['courseId'] as String
      );
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to enroll user: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserEnrollmentsHandler(Request request, String userId) async {
    try {
      final result = await _learningService.getUserEnrollments(userId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get user enrollments: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Progress Tracking Handlers
  static Future<Response> _updateProgressHandler(Request request, String userId, String courseId) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _learningService.updateProgress(userId, courseId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to update progress: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserProgressHandler(Request request, String userId, String courseId) async {
    try {
      final result = await _learningService.getUserProgress(userId, courseId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get user progress: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Certificate Management Handlers
  static Future<Response> _generateCertificateHandler(Request request, String userId, String courseId) async {
    try {
      final result = await _learningService.generateCertificate(userId, courseId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to generate certificate: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserCertificatesHandler(Request request, String userId) async {
    try {
      final result = await _learningService.getUserCertificates(userId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get user certificates: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Assessment and Quiz Management Handlers
  static Future<Response> _submitQuizAttemptHandler(Request request, String userId, String lessonId) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _learningService.submitQuizAttempt(userId, lessonId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to submit quiz attempt: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  // Analytics and Reporting Handlers
  static Future<Response> _getLearnerAnalyticsHandler(Request request, String userId) async {
    try {
      final result = await _learningService.getLearnerAnalytics(userId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get learner analytics: $e'
        }),
        headers: {'content-type': 'application/json'},
      );
    }
  }
}