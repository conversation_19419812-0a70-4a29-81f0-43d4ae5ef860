import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shared/shared.dart';
import 'database_service.dart';

/// Compliance and audit service for enterprise features
class ComplianceService {
  final DatabaseService _databaseService;
  
  ComplianceService(this._databaseService);

  /// Get audit events for an organization
  Future<Response> getAuditEvents(Request request) async {
    try {
      final organizationId = request.params['organizationId'];
      final queryParams = request.url.queryParameters;
      
      final startDate = queryParams['startDate'] != null 
          ? DateTime.parse(queryParams['startDate']!)
          : DateTime.now().subtract(const Duration(days: 30));
      final endDate = queryParams['endDate'] != null
          ? DateTime.parse(queryParams['endDate']!)
          : DateTime.now();
      final eventType = queryParams['eventType'];
      final severity = queryParams['severity'];
      final page = int.tryParse(queryParams['page'] ?? '1') ?? 1;
      final limit = int.tryParse(queryParams['limit'] ?? '50') ?? 50;
      
      final offset = (page - 1) * limit;
      
      var query = '''
        SELECT * FROM audit_events 
        WHERE organization_id = @organizationId
        AND timestamp BETWEEN @startDate AND @endDate
      ''';
      
      final params = {
        'organizationId': organizationId,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
      };
      
      if (eventType != null) {
        query += ' AND event_type = @eventType';
        params['eventType'] = eventType;
      }
      
      if (severity != null) {
        query += ' AND severity = @severity';
        params['severity'] = severity;
      }
      
      query += ' ORDER BY timestamp DESC LIMIT @limit OFFSET @offset';
      params['limit'] = limit.toString();
      params['offset'] = offset.toString();
      
      final result = await _databaseService.query(query, params);
      
      final events = result.map((row) => AuditEvent.fromJson(row)).toList();
      
      // Get total count for pagination
      final countQuery = '''
        SELECT COUNT(*) as total FROM audit_events 
        WHERE organization_id = @organizationId
        AND timestamp BETWEEN @startDate AND @endDate
      ''';
      
      final countResult = await _databaseService.query(countQuery, {
        'organizationId': organizationId,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
      });
      
      final total = countResult.first['total'] as int;
      
      return Response.ok(
        jsonEncode({
          'events': events.map((e) => e.toJson()).toList(),
          'pagination': {
            'page': page,
            'limit': limit,
            'total': total,
            'totalPages': (total / limit).ceil(),
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get audit events: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Create audit event
  Future<Response> createAuditEvent(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final event = AuditEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        organizationId: data['organizationId'] as String,
        userId: data['userId'] as String?,
        eventType: AuditEventType.values.firstWhere(
          (e) => e.name == data['eventType'],
          orElse: () => AuditEventType.dataAccessed,
        ),
        severity: AuditSeverity.values.firstWhere(
          (e) => e.name == data['severity'],
          orElse: () => AuditSeverity.low,
        ),
        description: data['description'] as String,
        resourceType: data['resourceType'] as String?,
        resourceId: data['resourceId'] as String?,
        ipAddress: data['ipAddress'] as String?,
        userAgent: data['userAgent'] as String?,
        sessionId: data['sessionId'] as String?,
        metadata: data['metadata'] as Map<String, dynamic>?,
        requestDetails: data['requestDetails'] as Map<String, dynamic>?,
        responseDetails: data['responseDetails'] as Map<String, dynamic>?,
        timestamp: DateTime.now(),
        geolocation: data['geolocation'] as Map<String, dynamic>?,
        requiresAttention: data['requiresAttention'] as bool? ?? false,
        complianceFrameworks: (data['complianceFrameworks'] as List<dynamic>?)
            ?.map((f) => ComplianceFramework.values.firstWhere(
                  (e) => e.name == f,
                  orElse: () => ComplianceFramework.gdpr,
                ))
            .toList() ?? [],
      );
      
      await _databaseService.insert('audit_events', event.toJson());
      
      return Response.ok(
        jsonEncode(event.toJson()),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create audit event: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Generate compliance report
  Future<Response> generateComplianceReport(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final organizationId = data['organizationId'] as String;
      final framework = ComplianceFramework.values.firstWhere(
        (f) => f.name == data['framework'],
        orElse: () => ComplianceFramework.gdpr,
      );
      final periodStart = DateTime.parse(data['periodStart'] as String);
      final periodEnd = DateTime.parse(data['periodEnd'] as String);
      final generatedBy = data['generatedBy'] as String;
      
      // Generate compliance score and findings based on audit events
      final auditQuery = '''
        SELECT * FROM audit_events 
        WHERE organization_id = @organizationId
        AND timestamp BETWEEN @periodStart AND @periodEnd
        AND @framework = ANY(compliance_frameworks)
      ''';
      
      final auditEvents = await _databaseService.query(auditQuery, {
        'organizationId': organizationId,
        'periodStart': periodStart.toIso8601String(),
        'periodEnd': periodEnd.toIso8601String(),
        'framework': framework.name,
      });
      
      // Calculate compliance metrics
      final totalRequirements = _getTotalRequirements(framework);
      final complianceScore = _calculateComplianceScore(auditEvents, framework);
      final compliantRequirements = ((complianceScore / 100) * totalRequirements).round();
      final nonCompliantRequirements = totalRequirements - compliantRequirements;
      
      final report = ComplianceReport(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        organizationId: organizationId,
        framework: framework,
        title: 'Compliance Report - ${framework.name.toUpperCase()}',
        description: 'Automated compliance report for ${framework.name.toUpperCase()}',
        periodStart: periodStart,
        periodEnd: periodEnd,
        status: 'completed',
        complianceScore: complianceScore,
        totalRequirements: totalRequirements,
        compliantRequirements: compliantRequirements,
        nonCompliantRequirements: nonCompliantRequirements,
        pendingRequirements: 0,
        findings: _generateFindings(auditEvents, framework),
        recommendations: _generateRecommendations(complianceScore, framework),
        evidenceFiles: [],
        riskAssessment: _generateRiskAssessment(complianceScore),
        actionItems: _generateActionItems(complianceScore, framework),
        generatedBy: generatedBy,
        generatedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await _databaseService.insert('compliance_reports', report.toJson());
      
      return Response.ok(
        jsonEncode(report.toJson()),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to generate compliance report: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get compliance reports
  Future<Response> getComplianceReports(Request request) async {
    try {
      final organizationId = request.params['organizationId'];
      final queryParams = request.url.queryParameters;
      
      final framework = queryParams['framework'];
      final page = int.tryParse(queryParams['page'] ?? '1') ?? 1;
      final limit = int.tryParse(queryParams['limit'] ?? '20') ?? 20;
      final offset = (page - 1) * limit;
      
      var query = '''
        SELECT * FROM compliance_reports 
        WHERE organization_id = @organizationId
      ''';
      
      final params = {'organizationId': organizationId};
      
      if (framework != null) {
        query += ' AND framework = @framework';
        params['framework'] = framework;
      }
      
      query += ' ORDER BY generated_at DESC LIMIT @limit OFFSET @offset';
      params['limit'] = limit.toString();
      params['offset'] = offset.toString();
      
      final result = await _databaseService.query(query, params);
      final reports = result.map((row) => ComplianceReport.fromJson(row)).toList();
      
      return Response.ok(
        jsonEncode({
          'reports': reports.map((r) => r.toJson()).toList(),
          'pagination': {
            'page': page,
            'limit': limit,
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get compliance reports: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// GDPR data export
  Future<Response> exportGdprData(Request request) async {
    try {
      final organizationId = request.params['organizationId'];
      final userId = request.params['userId'];
      
      // Get all user data across all tables
      final userData = await _getUserData(organizationId!, userId!);
      
      // Create audit event
      await _createAuditEventDirect({
        'organizationId': organizationId,
        'userId': userId,
        'eventType': 'dataExported',
        'severity': 'medium',
        'description': 'GDPR data export completed for user $userId',
        'resourceType': 'user_data',
        'resourceId': userId,
        'requiresAttention': false,
        'complianceFrameworks': ['gdpr'],
      });
      
      return Response.ok(
        jsonEncode({
          'exportedAt': DateTime.now().toIso8601String(),
          'dataSubject': userId,
          'data': userData,
        }),
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': 'attachment; filename="gdpr-export-$userId.json"',
        },
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to export GDPR data: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// GDPR data deletion
  Future<Response> deleteGdprData(Request request) async {
    try {
      final organizationId = request.params['organizationId'];
      final userId = request.params['userId'];
      
      // Delete user data according to GDPR requirements
      await _deleteUserData(organizationId!, userId!);
      
      // Create audit event
      await _createAuditEventDirect({
        'organizationId': organizationId,
        'userId': userId,
        'eventType': 'dataDeleted',
        'severity': 'high',
        'description': 'GDPR data deletion completed for user $userId',
        'resourceType': 'user_data',
        'resourceId': userId,
        'requiresAttention': true,
        'complianceFrameworks': ['gdpr'],
      });
      
      return Response.ok(
        jsonEncode({
          'deletedAt': DateTime.now().toIso8601String(),
          'dataSubject': userId,
          'status': 'completed',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to delete GDPR data: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Helper methods
  int _getTotalRequirements(ComplianceFramework framework) {
    switch (framework) {
      case ComplianceFramework.gdpr:
        return 42; // Total GDPR requirements
      case ComplianceFramework.soc2:
        return 67; // Total SOC 2 requirements
      case ComplianceFramework.hipaa:
        return 45; // Total HIPAA requirements
      case ComplianceFramework.pci:
        return 78; // Total PCI DSS requirements
      case ComplianceFramework.iso27001:
        return 114; // Total ISO 27001 requirements
      case ComplianceFramework.ccpa:
        return 28; // Total CCPA requirements
    }
  }

  double _calculateComplianceScore(List<Map<String, dynamic>> auditEvents, ComplianceFramework framework) {
    // Simplified compliance scoring based on security events and violations
    final totalEvents = auditEvents.length;
    if (totalEvents == 0) return 85.0; // Default score
    
    final violations = auditEvents.where((e) => 
      e['event_type'] == 'complianceViolation' ||
      e['event_type'] == 'securityIncident' ||
      e['event_type'] == 'dataBreachDetected'
    ).length;
    
    final baseScore = 100.0;
    final violationPenalty = violations * 10.0;
    
    return (baseScore - violationPenalty).clamp(0.0, 100.0);
  }

  List<Map<String, dynamic>> _generateFindings(List<Map<String, dynamic>> auditEvents, ComplianceFramework framework) {
    final findings = <Map<String, dynamic>>[];
    
    // Analyze audit events for compliance findings
    final violations = auditEvents.where((e) => e['event_type'] == 'complianceViolation').toList();
    
    for (final violation in violations) {
      findings.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'type': 'violation',
        'severity': violation['severity'],
        'title': 'Compliance Violation Detected',
        'description': violation['description'],
        'requirement': 'Data Protection',
        'evidence': violation['metadata'],
        'timestamp': violation['timestamp'],
      });
    }
    
    return findings;
  }

  List<Map<String, dynamic>> _generateRecommendations(double complianceScore, ComplianceFramework framework) {
    final recommendations = <Map<String, dynamic>>[];
    
    if (complianceScore < 80) {
      recommendations.add({
        'id': '1',
        'priority': 'high',
        'title': 'Improve Security Monitoring',
        'description': 'Implement enhanced security monitoring and alerting systems.',
        'framework': framework.name,
      });
    }
    
    if (complianceScore < 90) {
      recommendations.add({
        'id': '2',
        'priority': 'medium',
        'title': 'Enhance Access Controls',
        'description': 'Review and strengthen access control mechanisms.',
        'framework': framework.name,
      });
    }
    
    return recommendations;
  }

  Map<String, dynamic> _generateRiskAssessment(double complianceScore) {
    String riskLevel;
    if (complianceScore >= 95) {
      riskLevel = 'low';
    } else if (complianceScore >= 80) {
      riskLevel = 'medium';
    } else if (complianceScore >= 60) {
      riskLevel = 'high';
    } else {
      riskLevel = 'critical';
    }
    
    return {
      'overallRisk': riskLevel,
      'score': complianceScore,
      'factors': [
        {
          'category': 'Data Protection',
          'risk': riskLevel,
          'score': complianceScore,
        },
        {
          'category': 'Access Control',
          'risk': riskLevel,
          'score': complianceScore,
        },
      ],
    };
  }

  List<Map<String, dynamic>> _generateActionItems(double complianceScore, ComplianceFramework framework) {
    final actionItems = <Map<String, dynamic>>[];
    
    if (complianceScore < 90) {
      actionItems.add({
        'id': '1',
        'title': 'Review Security Policies',
        'description': 'Conduct a comprehensive review of security policies and procedures.',
        'priority': 'high',
        'assignee': null,
        'dueDate': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
        'status': 'open',
      });
    }
    
    return actionItems;
  }

  Future<Map<String, dynamic>> _getUserData(String organizationId, String userId) async {
    // Collect user data from all relevant tables
    final userData = <String, dynamic>{};
    
    // User profile data
    final userQuery = 'SELECT * FROM users WHERE id = @userId AND organization_id = @organizationId';
    final userResult = await _databaseService.query(userQuery, {
      'userId': userId,
      'organizationId': organizationId,
    });
    
    if (userResult.isNotEmpty) {
      userData['profile'] = userResult.first;
    }
    
    // User tasks
    final tasksQuery = 'SELECT * FROM tasks WHERE assigned_to = @userId AND organization_id = @organizationId';
    final tasksResult = await _databaseService.query(tasksQuery, {
      'userId': userId,
      'organizationId': organizationId,
    });
    
    userData['tasks'] = tasksResult;
    
    // Add other relevant data tables...
    
    return userData;
  }

  Future<void> _deleteUserData(String organizationId, String userId) async {
    // Delete user data from all relevant tables
    final tables = ['users', 'tasks', 'audit_events', 'user_points', 'achievements'];
    
    for (final table in tables) {
      await _databaseService.delete(table, {
        'user_id': userId,
        'organization_id': organizationId,
      });
    }
  }
  
  /// Create audit event directly without HTTP request
  Future<void> _createAuditEventDirect(Map<String, dynamic> data) async {
    final event = AuditEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      organizationId: data['organizationId'] as String,
      userId: data['userId'] as String?,
      eventType: AuditEventType.values.firstWhere(
        (e) => e.name == data['eventType'],
        orElse: () => AuditEventType.dataAccessed,
      ),
      severity: AuditSeverity.values.firstWhere(
        (e) => e.name == data['severity'],
        orElse: () => AuditSeverity.low,
      ),
      description: data['description'] as String,
      resourceType: data['resourceType'] as String?,
      resourceId: data['resourceId'] as String?,
      ipAddress: data['ipAddress'] as String?,
      userAgent: data['userAgent'] as String?,
      sessionId: data['sessionId'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
      requestDetails: data['requestDetails'] as Map<String, dynamic>?,
      responseDetails: data['responseDetails'] as Map<String, dynamic>?,
      timestamp: DateTime.now(),
      geolocation: data['geolocation'] as Map<String, dynamic>?,
      requiresAttention: data['requiresAttention'] as bool? ?? false,
      complianceFrameworks: (data['complianceFrameworks'] as List<dynamic>?)
          ?.map((f) => ComplianceFramework.values.firstWhere(
                (e) => e.name == f,
                orElse: () => ComplianceFramework.gdpr,
              ))
          .toList() ?? [],
    );
    
    await _databaseService.insert('audit_events', event.toJson());
  }
}
