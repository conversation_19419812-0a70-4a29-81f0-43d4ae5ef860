# Production Dockerfile for Quester Server
FROM dart:stable AS build

WORKDIR /app
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY server/ ./server/

# Set working directory to server and build
WORKDIR /app/server
RUN dart pub get
RUN dart compile exe bin/server.dart -o bin/server

FROM gcr.io/distroless/base-debian12 AS production
WORKDIR /app
COPY --from=build /app/server/bin/server ./

USER nonroot:nonroot
EXPOSE 8080

CMD ["./server"]
