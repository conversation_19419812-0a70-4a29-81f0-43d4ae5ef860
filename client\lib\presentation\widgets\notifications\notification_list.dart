import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart' hide Notification;
import 'package:shared/src/models/notifications/notification.dart' as NotificationModel;
import '../../blocs/notification/notification_bloc.dart';
import '../common/responsive_builder.dart';
import 'notification_card.dart';

/// Widget for displaying a list of notifications
class NotificationList extends StatefulWidget {
  /// Optional filter for notification status
  final NotificationStatus? statusFilter;
  
  /// Optional filter for notification category
  final NotificationCategory? categoryFilter;
  
  /// Optional filter for notification priority
  final NotificationPriority? priorityFilter;
  
  /// Whether to show only unread notifications
  final bool? showOnlyUnread;
  
  /// Whether to enable pull-to-refresh
  final bool enableRefresh;
  
  /// Whether to enable infinite scroll
  final bool enableInfiniteScroll;

  const NotificationList({
    super.key,
    this.statusFilter,
    this.categoryFilter,
    this.priorityFilter,
    this.showOnlyUnread,
    this.enableRefresh = true,
    this.enableInfiniteScroll = true,
  });

  @override
  State<NotificationList> createState() => _NotificationListState();
}

class _NotificationListState extends State<NotificationList> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
    
    if (widget.enableInfiniteScroll) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<NotificationBloc, NotificationState>(
      listener: (context, state) {
        if (state is NotificationError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } else if (state is NotificationMarkedAsRead) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Notification marked as read')),
          );
        } else if (state is NotificationDeleted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Notification deleted')),
          );
        } else if (state is AllNotificationsMarkedAsRead) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('All notifications marked as read')),
          );
        }
      },
      builder: (context, state) {
        if (state is NotificationLoading && _currentPage == 1) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is NotificationError && _currentPage == 1) {
          return _buildErrorState(context, state.message);
        } else if (state is NotificationsLoaded) {
          return _buildNotificationList(context, state);
        }

        return _buildEmptyState(context);
      },
    );
  }

  Widget _buildNotificationList(BuildContext context, NotificationsLoaded state) {
    if (state.notifications.isEmpty) {
      return _buildEmptyState(context);
    }

    Widget listView = ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
      itemCount: state.notifications.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.notifications.length) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(AppConstants.defaultPadding),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final notification = state.notifications[index];
        return NotificationCard(
          notification: notification,
          onTap: () => _handleNotificationTap(context, notification),
          onMarkAsRead: notification.isRead 
              ? null 
              : () => _markAsRead(context, notification.id),
          onDelete: () => _deleteNotification(context, notification),
        );
      },
    );

    if (widget.enableRefresh) {
      listView = RefreshIndicator(
        onRefresh: () => _refreshNotifications(context),
        child: listView,
      );
    }

    return ResponsiveBuilder(
      mobile: (context) => listView,
      tablet: (context) => _buildTabletLayout(context, state, listView),
      desktop: (context) => _buildDesktopLayout(context, state, listView),
    );
  }

  Widget _buildTabletLayout(BuildContext context, NotificationsLoaded state, Widget listView) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: listView,
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildNotificationSummary(context, state),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context, NotificationsLoaded state, Widget listView) {
    return Row(
      children: [
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildNotificationFilters(context),
        ),
        Expanded(
          flex: 3,
          child: listView,
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildNotificationSummary(context, state),
        ),
      ],
    );
  }

  Widget _buildNotificationFilters(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Category Filter
          _buildFilterSection(
            context,
            'Category',
            NotificationCategory.values.map((category) =>
              FilterChip(
                label: Text(_getCategoryName(category)),
                selected: widget.categoryFilter == category,
                onSelected: (selected) {
                  // TODO: Implement filter change
                },
              ),
            ).toList(),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Priority Filter
          _buildFilterSection(
            context,
            'Priority',
            NotificationPriority.values.map((priority) =>
              FilterChip(
                label: Text(priority.name.toUpperCase()),
                selected: widget.priorityFilter == priority,
                onSelected: (selected) {
                  // TODO: Implement filter change
                },
              ),
            ).toList(),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Read Status Filter
          SwitchListTile(
            title: const Text('Unread Only'),
            value: widget.showOnlyUnread ?? false,
            onChanged: (value) {
              // TODO: Implement filter change
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(BuildContext context, String title, List<Widget> chips) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          runSpacing: AppConstants.smallPadding,
          children: chips,
        ),
      ],
    );
  }

  Widget _buildNotificationSummary(BuildContext context, NotificationsLoaded state) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Summary',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (state.unreadCount > 0)
                TextButton(
                  onPressed: () => _markAllAsRead(context),
                  child: const Text('Mark All Read'),
                ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          _buildSummaryCard(
            context,
            'Total',
            state.notifications.length.toString(),
            Icons.notifications,
            Colors.blue,
          ),
          
          _buildSummaryCard(
            context,
            'Unread',
            state.unreadCount.toString(),
            Icons.mark_email_unread,
            Colors.orange,
          ),
          
          _buildSummaryCard(
            context,
            'Read',
            (state.notifications.length - state.unreadCount).toString(),
            Icons.mark_email_read,
            Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No notifications',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You\'re all caught up!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load notifications',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => _loadNotifications(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _loadNotifications() {
    context.read<NotificationBloc>().add(LoadNotifications(
      status: widget.statusFilter,
      category: widget.categoryFilter,
      priority: widget.priorityFilter,
      isRead: widget.showOnlyUnread == true ? false : null,
      page: _currentPage,
    ));
  }

  Future<void> _refreshNotifications(BuildContext context) async {
    _currentPage = 1;
    _isLoadingMore = false;
    context.read<NotificationBloc>().add(const RefreshNotifications());
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore) {
      _loadMoreNotifications();
    }
  }

  void _loadMoreNotifications() {
    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });
    
    context.read<NotificationBloc>().add(LoadNotifications(
      status: widget.statusFilter,
      category: widget.categoryFilter,
      priority: widget.priorityFilter,
      isRead: widget.showOnlyUnread == true ? false : null,
      page: _currentPage,
    ));
  }

  void _handleNotificationTap(BuildContext context, NotificationModel.Notification notification) {
    if (!notification.isRead) {
      _markAsRead(context, notification.id);
    }
    
    // Handle navigation based on notification action
    if (notification.actionUrl != null) {
      // TODO: Navigate to the specified URL
    }
  }

  void _markAsRead(BuildContext context, String notificationId) {
    context.read<NotificationBloc>().add(
      MarkNotificationAsRead(notificationId: notificationId),
    );
  }

  void _markAllAsRead(BuildContext context) {
    context.read<NotificationBloc>().add(const MarkAllNotificationsAsRead());
  }

  void _deleteNotification(BuildContext context, NotificationModel.Notification notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notification'),
        content: const Text('Are you sure you want to delete this notification?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<NotificationBloc>().add(
                DeleteNotification(notificationId: notification.id),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _getCategoryName(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.system:
        return 'System';
      case NotificationCategory.quest:
        return 'Quests';
      case NotificationCategory.achievement:
        return 'Achievements';
      case NotificationCategory.social:
        return 'Social';
      case NotificationCategory.reminder:
        return 'Reminders';
      case NotificationCategory.marketing:
        return 'Marketing';
      case NotificationCategory.security:
        return 'Security';
      case NotificationCategory.team:
        return 'Team';
    }
  }
}
