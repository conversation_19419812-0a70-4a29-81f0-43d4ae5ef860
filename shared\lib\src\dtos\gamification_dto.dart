import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../models/gamification/achievement.dart';
import '../models/gamification/reward.dart';
import '../models/gamification/leaderboard.dart';
import '../models/gamification/streak.dart';

part 'gamification_dto.g.dart';

/// Achievement unlock response DTO
@JsonSerializable()
class AchievementUnlockDto extends Equatable {
  final String achievementId;
  final String userId;
  final int pointsEarned;
  final DateTime unlockedAt;
  final Achievement achievement;
  final bool isFirstTime;
  final double progressBefore;
  final double progressAfter;

  const AchievementUnlockDto({
    required this.achievementId,
    required this.userId,
    required this.pointsEarned,
    required this.unlockedAt,
    required this.achievement,
    required this.isFirstTime,
    required this.progressBefore,
    required this.progressAfter,
  });

  factory AchievementUnlockDto.fromJson(Map<String, dynamic> json) => _$AchievementUnlockDtoFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementUnlockDtoToJson(this);

  @override
  List<Object?> get props => [
        achievementId,
        userId,
        pointsEarned,
        unlockedAt,
        achievement,
        isFirstTime,
        progressBefore,
        progressAfter,
      ];
}

/// Achievement progress DTO
@JsonSerializable()
class AchievementProgressDto extends Equatable {
  final String achievementId;
  final String userId;
  final double progress;
  final int currentValue;
  final int requiredValue;
  final int? currentSecondaryValue;
  final int? requiredSecondaryValue;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final DateTime lastUpdated;

  const AchievementProgressDto({
    required this.achievementId,
    required this.userId,
    required this.progress,
    required this.currentValue,
    required this.requiredValue,
    this.currentSecondaryValue,
    this.requiredSecondaryValue,
    required this.isUnlocked,
    this.unlockedAt,
    required this.lastUpdated,
  });

  factory AchievementProgressDto.fromJson(Map<String, dynamic> json) => _$AchievementProgressDtoFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementProgressDtoToJson(this);

  @override
  List<Object?> get props => [
        achievementId,
        userId,
        progress,
        currentValue,
        requiredValue,
        currentSecondaryValue,
        requiredSecondaryValue,
        isUnlocked,
        unlockedAt,
        lastUpdated,
      ];
}

/// Reward purchase DTO
@JsonSerializable()
class RewardPurchaseDto extends Equatable {
  final String rewardId;
  final int quantity;
  final String? customizationData;

  const RewardPurchaseDto({
    required this.rewardId,
    this.quantity = 1,
    this.customizationData,
  });

  factory RewardPurchaseDto.fromJson(Map<String, dynamic> json) => _$RewardPurchaseDtoFromJson(json);
  Map<String, dynamic> toJson() => _$RewardPurchaseDtoToJson(this);

  @override
  List<Object?> get props => [rewardId, quantity, customizationData];
}

/// Reward purchase response DTO
@JsonSerializable()
class RewardPurchaseResponseDto extends Equatable {
  final String userRewardId;
  final String rewardId;
  final String userId;
  final int pointsCost;
  final int quantity;
  final DateTime purchasedAt;
  final Reward reward;
  final int remainingPoints;

  const RewardPurchaseResponseDto({
    required this.userRewardId,
    required this.rewardId,
    required this.userId,
    required this.pointsCost,
    required this.quantity,
    required this.purchasedAt,
    required this.reward,
    required this.remainingPoints,
  });

  factory RewardPurchaseResponseDto.fromJson(Map<String, dynamic> json) => _$RewardPurchaseResponseDtoFromJson(json);
  Map<String, dynamic> toJson() => _$RewardPurchaseResponseDtoToJson(this);

  @override
  List<Object?> get props => [
        userRewardId,
        rewardId,
        userId,
        pointsCost,
        quantity,
        purchasedAt,
        reward,
        remainingPoints,
      ];
}

/// Points transaction summary DTO
@JsonSerializable()
class PointsTransactionSummaryDto extends Equatable {
  final int totalPointsEarned;
  final int totalPointsSpent;
  final int availablePoints;
  final int transactionCount;
  final DateTime? lastTransaction;
  final Map<String, int> earningsBySource;
  final Map<String, int> spendingByCategory;
  final double dailyAverage;
  final double weeklyAverage;
  final int bestDay;
  final String bestDayDate;

  const PointsTransactionSummaryDto({
    required this.totalPointsEarned,
    required this.totalPointsSpent,
    required this.availablePoints,
    required this.transactionCount,
    this.lastTransaction,
    required this.earningsBySource,
    required this.spendingByCategory,
    required this.dailyAverage,
    required this.weeklyAverage,
    required this.bestDay,
    required this.bestDayDate,
  });

  factory PointsTransactionSummaryDto.fromJson(Map<String, dynamic> json) => _$PointsTransactionSummaryDtoFromJson(json);
  Map<String, dynamic> toJson() => _$PointsTransactionSummaryDtoToJson(this);

  @override
  List<Object?> get props => [
        totalPointsEarned,
        totalPointsSpent,
        availablePoints,
        transactionCount,
        lastTransaction,
        earningsBySource,
        spendingByCategory,
        dailyAverage,
        weeklyAverage,
        bestDay,
        bestDayDate,
      ];
}

/// Leaderboard request DTO
@JsonSerializable()
class LeaderboardRequestDto extends Equatable {
  final LeaderboardCategory category;
  final LeaderboardPeriod period;
  final int? limit;
  final int? offset;
  final String? userId; // To get user's position context

  const LeaderboardRequestDto({
    required this.category,
    required this.period,
    this.limit,
    this.offset,
    this.userId,
  });

  factory LeaderboardRequestDto.fromJson(Map<String, dynamic> json) => _$LeaderboardRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$LeaderboardRequestDtoToJson(this);

  @override
  List<Object?> get props => [category, period, limit, offset, userId];
}

/// Streak update DTO
@JsonSerializable()
class StreakUpdateDto extends Equatable {
  final StreakType streakType;
  final DateTime? activityDate;
  final Map<String, dynamic>? activityData;

  const StreakUpdateDto({
    required this.streakType,
    this.activityDate,
    this.activityData,
  });

  factory StreakUpdateDto.fromJson(Map<String, dynamic> json) => _$StreakUpdateDtoFromJson(json);
  Map<String, dynamic> toJson() => _$StreakUpdateDtoToJson(this);

  @override
  List<Object?> get props => [streakType, activityDate, activityData];
}

/// Gamification stats overview DTO
@JsonSerializable()
class GamificationStatsDto extends Equatable {
  final int totalPoints;
  final int availablePoints;
  final int currentLevel;
  final double levelProgress;
  final int achievementsUnlocked;
  final int totalAchievements;
  final int rewardsOwned;
  final int currentStreak;
  final int longestStreak;
  final int leaderboardRank;
  final String leaderboardCategory;
  final int questsCompleted;
  final int tasksCompleted;
  final Map<String, dynamic> streakSummary;
  final Map<String, dynamic> recentActivity;

  const GamificationStatsDto({
    required this.totalPoints,
    required this.availablePoints,
    required this.currentLevel,
    required this.levelProgress,
    required this.achievementsUnlocked,
    required this.totalAchievements,
    required this.rewardsOwned,
    required this.currentStreak,
    required this.longestStreak,
    required this.leaderboardRank,
    required this.leaderboardCategory,
    required this.questsCompleted,
    required this.tasksCompleted,
    required this.streakSummary,
    required this.recentActivity,
  });

  factory GamificationStatsDto.fromJson(Map<String, dynamic> json) => _$GamificationStatsDtoFromJson(json);
  Map<String, dynamic> toJson() => _$GamificationStatsDtoToJson(this);

  @override
  List<Object?> get props => [
        totalPoints,
        availablePoints,
        currentLevel,
        levelProgress,
        achievementsUnlocked,
        totalAchievements,
        rewardsOwned,
        currentStreak,
        longestStreak,
        leaderboardRank,
        leaderboardCategory,
        questsCompleted,
        tasksCompleted,
        streakSummary,
        recentActivity,
      ];
}

/// User achievements list DTO
@JsonSerializable()
class UserAchievementsDto extends Equatable {
  final List<AchievementProgressDto> unlocked;
  final List<AchievementProgressDto> inProgress;
  final List<Achievement> available;
  final int totalUnlocked;
  final int totalAvailable;
  final double overallProgress;

  const UserAchievementsDto({
    required this.unlocked,
    required this.inProgress,
    required this.available,
    required this.totalUnlocked,
    required this.totalAvailable,
    required this.overallProgress,
  });

  factory UserAchievementsDto.fromJson(Map<String, dynamic> json) => _$UserAchievementsDtoFromJson(json);
  Map<String, dynamic> toJson() => _$UserAchievementsDtoToJson(this);

  @override
  List<Object?> get props => [
        unlocked,
        inProgress,
        available,
        totalUnlocked,
        totalAvailable,
        overallProgress,
      ];
}

/// User rewards inventory DTO
@JsonSerializable()
class UserRewardsDto extends Equatable {
  final List<UserRewardDto> owned;
  final List<Reward> available;
  final List<Reward> purchasable;
  final int totalOwned;
  final int totalAvailable;

  const UserRewardsDto({
    required this.owned,
    required this.available,
    required this.purchasable,
    required this.totalOwned,
    required this.totalAvailable,
  });

  factory UserRewardsDto.fromJson(Map<String, dynamic> json) => _$UserRewardsDtoFromJson(json);
  Map<String, dynamic> toJson() => _$UserRewardsDtoToJson(this);

  @override
  List<Object?> get props => [owned, available, purchasable, totalOwned, totalAvailable];
}

/// Individual user reward DTO
@JsonSerializable()
class UserRewardDto extends Equatable {
  final String id;
  final String rewardId;
  final String userId;
  final int quantity;
  final DateTime obtainedAt;
  final bool isActive;
  final Reward reward;
  final Map<String, dynamic>? customizationData;

  const UserRewardDto({
    required this.id,
    required this.rewardId,
    required this.userId,
    required this.quantity,
    required this.obtainedAt,
    required this.isActive,
    required this.reward,
    this.customizationData,
  });

  factory UserRewardDto.fromJson(Map<String, dynamic> json) => _$UserRewardDtoFromJson(json);
  Map<String, dynamic> toJson() => _$UserRewardDtoToJson(this);

  @override
  List<Object?> get props => [
        id,
        rewardId,
        userId,
        quantity,
        obtainedAt,
        isActive,
        reward,
        customizationData,
      ];
}