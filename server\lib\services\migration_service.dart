import 'dart:io';
import 'package:postgres/postgres.dart';
import 'package:logging/logging.dart';

class MigrationService {
  late final Connection _connection;
  final _logger = Logger('MigrationService');
  
  String get _host => Platform.environment['DB_HOST'] ?? 'localhost';
  int get _port => int.parse(Platform.environment['DB_PORT'] ?? '5432');
  String get _database => Platform.environment['DB_NAME'] ?? 'questerdb';
  String get _username => Platform.environment['DB_USER'] ?? 'quester';
  String get _password => Platform.environment['DB_PASSWORD'] ?? 'questerpass';

  Future<void> initialize() async {
    try {
      _connection = await Connection.open(
        Endpoint(
          host: _host,
          port: _port,
          database: _database,
          username: _username,
          password: _password,
        ),
      );
      _logger.info('Migration service connected to database');
      await _ensureMigrationsTable();
    } catch (e) {
      _logger.severe('Failed to initialize migration service: $e');
      rethrow;
    }
  }

  Future<void> _ensureMigrationsTable() async {
    try {
      await _connection.execute('''
        CREATE TABLE IF NOT EXISTS quester.migrations (
          id SERIAL PRIMARY KEY,
          version VARCHAR(50) UNIQUE NOT NULL,
          description TEXT NOT NULL,
          applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          execution_time_ms INTEGER,
          checksum VARCHAR(64)
        )
      ''');
      _logger.info('Migrations table ensured');
    } catch (e) {
      _logger.severe('Failed to create migrations table: $e');
      rethrow;
    }
  }

  Future<List<String>> getAppliedMigrations() async {
    try {
      final result = await _connection.execute(
        'SELECT version FROM quester.migrations ORDER BY applied_at ASC'
      );
      return result.map((row) => row[0] as String).toList();
    } catch (e) {
      _logger.severe('Failed to get applied migrations: $e');
      return [];
    }
  }

  Future<void> applyMigration(Migration migration) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      _logger.info('Applying migration ${migration.version}: ${migration.description}');
      
      // Execute migration in transaction
      await _connection.runTx((ctx) async {
        await ctx.execute(migration.sql);
        
        // Record successful migration
        await ctx.execute(
          'INSERT INTO quester.migrations (version, description, execution_time_ms, checksum) VALUES (\$1, \$2, \$3, \$4)',
          parameters: [
            migration.version,
            migration.description,
            stopwatch.elapsedMilliseconds,
            migration.checksum,
          ],
        );
      });
      
      _logger.info('Migration ${migration.version} applied successfully in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      _logger.severe('Failed to apply migration ${migration.version}: $e');
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> rollbackMigration(Migration migration) async {
    if (migration.rollbackSql == null) {
      throw Exception('Migration ${migration.version} does not support rollback');
    }

    try {
      _logger.info('Rolling back migration ${migration.version}: ${migration.description}');
      
      await _connection.runTx((ctx) async {
        await ctx.execute(migration.rollbackSql!);
        
        // Remove migration record
        await ctx.execute(
          'DELETE FROM quester.migrations WHERE version = \$1',
          parameters: [migration.version],
        );
      });
      
      _logger.info('Migration ${migration.version} rolled back successfully');
    } catch (e) {
      _logger.severe('Failed to rollback migration ${migration.version}: $e');
      rethrow;
    }
  }

  Future<void> runMigrations(List<Migration> migrations) async {
    final appliedMigrations = await getAppliedMigrations();
    
    for (final migration in migrations) {
      if (!appliedMigrations.contains(migration.version)) {
        await applyMigration(migration);
      } else {
        _logger.info('Migration ${migration.version} already applied, skipping');
      }
    }
  }

  Future<DatabaseStatus> getDatabaseStatus() async {
    try {
      final appliedMigrations = await getAppliedMigrations();
      
      // Check table existence
      final tablesResult = await _connection.execute('''
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'quester'
      ''');
      
      final tables = tablesResult.map((row) => row[0] as String).toList();
      
      // Check extensions
      final extensionsResult = await _connection.execute('''
        SELECT extname FROM pg_extension 
        WHERE extname IN ('uuid-ossp', 'pgcrypto')
      ''');
      
      final extensions = extensionsResult.map((row) => row[0] as String).toList();
      
      return DatabaseStatus(
        isInitialized: tables.isNotEmpty,
        appliedMigrations: appliedMigrations,
        availableTables: tables,
        installedExtensions: extensions,
      );
    } catch (e) {
      _logger.severe('Failed to get database status: $e');
      rethrow;
    }
  }

  Future<void> validateSchema() async {
    try {
      // Validate core tables exist
      final coreTableChecks = [
        'users', 'user_points', 'achievements', 'user_achievements',
        'rewards', 'user_rewards', 'streaks', 'leaderboards', 'activity_log'
      ];
      
      for (final table in coreTableChecks) {
        final result = await _connection.execute('''
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'quester' AND table_name = '\$1'
          )
        ''');
        
        if (!(result.first[0] as bool)) {
          throw Exception('Core table $table is missing');
        }
      }
      
      _logger.info('Schema validation passed');
    } catch (e) {
      _logger.severe('Schema validation failed: $e');
      rethrow;
    }
  }

  Future<void> close() async {
    await _connection.close();
    _logger.info('Migration service connection closed');
  }
}

class Migration {
  final String version;
  final String description;
  final String sql;
  final String? rollbackSql;
  final String checksum;

  Migration({
    required this.version,
    required this.description,
    required this.sql,
    this.rollbackSql,
    required this.checksum,
  });

  static String generateChecksum(String content) {
    // Simple checksum for migration integrity
    return content.hashCode.toRadixString(16);
  }
}

class DatabaseStatus {
  final bool isInitialized;
  final List<String> appliedMigrations;
  final List<String> availableTables;
  final List<String> installedExtensions;

  DatabaseStatus({
    required this.isInitialized,
    required this.appliedMigrations,
    required this.availableTables,
    required this.installedExtensions,
  });

  Map<String, dynamic> toJson() => {
    'is_initialized': isInitialized,
    'applied_migrations': appliedMigrations,
    'available_tables': availableTables,
    'installed_extensions': installedExtensions,
    'migration_count': appliedMigrations.length,
    'table_count': availableTables.length,
  };
}