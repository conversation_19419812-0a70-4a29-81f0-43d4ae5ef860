/// API endpoint constants for Quester platform
class ApiEndpoints {
  // Base configuration
  static const String apiVersion = 'v1';
  static const String baseApiPath = '/api/$apiVersion';
  
  // Authentication endpoints
  static const String auth = '$baseApiPath/auth';
  static const String login = '$auth/login';
  static const String register = '$auth/register';
  static const String logout = '$auth/logout';
  static const String refreshToken = '$auth/refresh';
  static const String forgotPassword = '$auth/forgot-password';
  static const String resetPassword = '$auth/reset-password';
  static const String changePassword = '$auth/change-password';
  static const String verifyEmail = '$auth/verify-email';
  static const String resendVerification = '$auth/resend-verification';
  
  // User endpoints
  static const String users = '$baseApiPath/users';
  static const String profile = '$users/profile';
  static const String updateProfile = '$users/profile';
  static const String uploadAvatar = '$users/avatar';
  static const String deleteAccount = '$users/delete';
  static const String userPreferences = '$users/preferences';
  
  // Quest endpoints
  static const String quests = '$baseApiPath/quests';
  static const String createQuest = quests;
  static const String questById = '$quests/{id}';
  static const String updateQuest = '$quests/{id}';
  static const String deleteQuest = '$quests/{id}';
  static const String completeQuest = '$quests/{id}/complete';
  static const String questTasks = '$quests/{id}/tasks';
  static const String questParticipants = '$quests/{id}/participants';
  static const String addParticipant = '$quests/{id}/participants';
  static const String removeParticipant = '$quests/{id}/participants/{userId}';
  static const String questProgress = '$quests/{id}/progress';
  static const String myQuests = '$quests/my';
  static const String questStats = '$quests/stats';
  static const String questSearch = '$quests/search';
  
  // Task endpoints
  static const String tasks = '$baseApiPath/tasks';
  static const String createTask = tasks;
  static const String taskById = '$tasks/{id}';
  static const String updateTask = '$tasks/{id}';
  static const String deleteTask = '$tasks/{id}';
  static const String completeTask = '$tasks/{id}/complete';
  static const String startTask = '$tasks/{id}/start';
  static const String pauseTask = '$tasks/{id}/pause';
  static const String resumeTask = '$tasks/{id}/resume';
  static const String taskComments = '$tasks/{id}/comments';
  static const String addTaskComment = '$tasks/{id}/comments';
  static const String taskAttachments = '$tasks/{id}/attachments';
  static const String uploadTaskAttachment = '$tasks/{id}/attachments';
  static const String myTasks = '$tasks/my';
  static const String taskStats = '$tasks/stats';
  static const String bulkUpdateTasks = '$tasks/bulk-update';
  static const String taskSearch = '$tasks/search';
  
  // Gamification endpoints
  static const String gamification = '$baseApiPath/gamification';
  
  // Achievement endpoints
  static const String achievements = '$gamification/achievements';
  static const String allAchievements = achievements;
  static const String achievementById = '$achievements/{id}';
  static const String myAchievements = '$achievements/my';
  static const String achievementProgress = '$achievements/progress';
  static const String unlockAchievement = '$achievements/{id}/unlock';
  static const String achievementStats = '$achievements/stats';
  
  // Points endpoints
  static const String points = '$gamification/points';
  static const String myPoints = '$points/my';
  static const String pointsHistory = '$points/history';
  static const String pointsTransactions = '$points/transactions';
  static const String earnPoints = '$points/earn';
  static const String spendPoints = '$points/spend';
  static const String pointsStats = '$points/stats';
  
  // Reward endpoints
  static const String rewards = '$gamification/rewards';
  static const String allRewards = rewards;
  static const String rewardById = '$rewards/{id}';
  static const String myRewards = '$rewards/my';
  static const String availableRewards = '$rewards/available';
  static const String purchaseReward = '$rewards/{id}/purchase';
  static const String activateReward = '$rewards/{id}/activate';
  static const String rewardInventory = '$rewards/inventory';
  
  // Leaderboard endpoints
  static const String leaderboards = '$gamification/leaderboards';
  static const String allLeaderboards = leaderboards;
  static const String leaderboardByCategory = '$leaderboards/{category}';
  static const String myLeaderboardPosition = '$leaderboards/{category}/my-position';
  static const String leaderboardStats = '$leaderboards/stats';
  
  // Streak endpoints
  static const String streaks = '$gamification/streaks';
  static const String myStreaks = '$streaks/my';
  static const String streakByType = '$streaks/{type}';
  static const String updateStreak = '$streaks/{type}/update';
  static const String breakStreak = '$streaks/{type}/break';
  static const String streakStats = '$streaks/stats';
  
  // Notification endpoints
  static const String notifications = '$baseApiPath/notifications';
  static const String allNotifications = notifications;
  static const String unreadNotifications = '$notifications/unread';
  static const String markAsRead = '$notifications/{id}/read';
  static const String markAllAsRead = '$notifications/mark-all-read';
  static const String deleteNotification = '$notifications/{id}';
  static const String notificationSettings = '$notifications/settings';
  
  // File upload endpoints
  static const String files = '$baseApiPath/files';
  static const String uploadFile = '$files/upload';
  static const String downloadFile = '$files/{id}';
  static const String deleteFile = '$files/{id}';
  static const String fileInfo = '$files/{id}/info';
  
  // Search endpoints
  static const String search = '$baseApiPath/search';
  static const String globalSearch = search;
  static const String searchQuests = '$search/quests';
  static const String searchTasks = '$search/tasks';
  static const String searchUsers = '$search/users';
  static const String searchSuggestions = '$search/suggestions';
  
  // Admin endpoints
  static const String admin = '$baseApiPath/admin';
  static const String adminUsers = '$admin/users';
  static const String adminQuests = '$admin/quests';
  static const String adminTasks = '$admin/tasks';
  static const String adminAchievements = '$admin/achievements';
  static const String adminRewards = '$admin/rewards';
  static const String adminStats = '$admin/stats';
  static const String adminLogs = '$admin/logs';
  static const String adminConfig = '$admin/config';
  
  // WebSocket endpoints
  static const String websocket = '/ws';
  static const String questUpdates = '$websocket/quests/{id}';
  static const String taskUpdates = '$websocket/tasks/{id}';
  static const String userUpdates = '$websocket/user';
  static const String notificationsWs = '$websocket/notifications';
  static const String leaderboardUpdates = '$websocket/leaderboards';
  
  // Health check endpoints
  static const String health = '/health';
  static const String apiHealth = '$baseApiPath/health';
  static const String dbHealth = '/health/db';
  static const String cacheHealth = '/health/cache';
  static const String servicesHealth = '/health/services';
  
  // Utility methods
  
  /// Replace path parameters with actual values
  static String replacePathParam(String endpoint, String paramName, String value) {
    return endpoint.replaceAll('{$paramName}', value);
  }
  
  /// Replace multiple path parameters
  static String replacePathParams(String endpoint, Map<String, String> params) {
    String result = endpoint;
    for (final entry in params.entries) {
      result = result.replaceAll('{${entry.key}}', entry.value);
    }
    return result;
  }
  
  /// Build query string from parameters
  static String buildQueryString(Map<String, dynamic> params) {
    if (params.isEmpty) return '';
    
    final queryParams = params.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${Uri.encodeComponent(entry.key)}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');
    
    return queryParams.isNotEmpty ? '?$queryParams' : '';
  }
  
  /// Build full URL with query parameters
  static String buildUrl(String endpoint, [Map<String, dynamic>? queryParams]) {
    final query = queryParams != null ? buildQueryString(queryParams) : '';
    return '$endpoint$query';
  }
  
  /// Get quest endpoint with ID
  static String getQuestEndpoint(String questId) {
    return replacePathParam(questById, 'id', questId);
  }
  
  /// Get task endpoint with ID
  static String getTaskEndpoint(String taskId) {
    return replacePathParam(taskById, 'id', taskId);
  }
  
  /// Get achievement endpoint with ID
  static String getAchievementEndpoint(String achievementId) {
    return replacePathParam(achievementById, 'id', achievementId);
  }
  
  /// Get reward endpoint with ID
  static String getRewardEndpoint(String rewardId) {
    return replacePathParam(rewardById, 'id', rewardId);
  }
  
  /// Get leaderboard endpoint with category
  static String getLeaderboardEndpoint(String category) {
    return replacePathParam(leaderboardByCategory, 'category', category);
  }
  
  /// Get streak endpoint with type
  static String getStreakEndpoint(String type) {
    return replacePathParam(streakByType, 'type', type);
  }
  
  /// Get user-specific quest endpoint
  static String getUserQuests(String userId) => '$users/$userId/quests';
  
  /// Get user-specific task endpoint
  static String getUserTasks(String userId) => '$users/$userId/tasks';
  
  /// Get user-specific achievement endpoint
  static String getUserAchievements(String userId) => '$users/$userId/achievements';
  
  /// Get user-specific points endpoint
  static String getUserPoints(String userId) => '$users/$userId/points';
  
  /// Get user-specific streaks endpoint
  static String getUserStreaks(String userId) => '$users/$userId/streaks';
  
  /// Get user-specific stats endpoint
  static String getUserStats(String userId) => '$users/$userId/stats';
  
  /// Pagination parameters
  static const String pageParam = 'page';
  static const String limitParam = 'limit';
  static const String offsetParam = 'offset';
  static const String sortParam = 'sort';
  static const String orderParam = 'order';
  static const String searchParam = 'search';
  static const String filterParam = 'filter';
}

/// HTTP methods
class HttpMethods {
  static const String get = 'GET';
  static const String post = 'POST';
  static const String put = 'PUT';
  static const String patch = 'PATCH';
  static const String delete = 'DELETE';
  static const String head = 'HEAD';
  static const String options = 'OPTIONS';
}

/// HTTP headers
class HttpHeaders {
  static const String authorization = 'Authorization';
  static const String contentType = 'Content-Type';
  static const String accept = 'Accept';
  static const String userAgent = 'User-Agent';
  static const String xRequestId = 'X-Request-ID';
  static const String xApiKey = 'X-API-Key';
  static const String xClientVersion = 'X-Client-Version';
  
  // Content types
  static const String applicationJson = 'application/json';
  static const String applicationFormUrlEncoded = 'application/x-www-form-urlencoded';
  static const String multipartFormData = 'multipart/form-data';
  static const String textPlain = 'text/plain';
}

/// Response status codes
class HttpStatusCodes {
  // Success
  static const int ok = 200;
  static const int created = 201;
  static const int accepted = 202;
  static const int noContent = 204;
  
  // Client errors
  static const int badRequest = 400;
  static const int unauthorized = 401;
  static const int forbidden = 403;
  static const int notFound = 404;
  static const int methodNotAllowed = 405;
  static const int conflict = 409;
  static const int unprocessableEntity = 422;
  static const int tooManyRequests = 429;
  
  // Server errors
  static const int internalServerError = 500;
  static const int badGateway = 502;
  static const int serviceUnavailable = 503;
  static const int gatewayTimeout = 504;
}