import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user.g.dart';

/// User roles within the Quester platform
enum UserRole {
  @JsonValue('newcomer')
  newcomer,
  @<PERSON>sonValue('apprentice')
  apprentice,
  @<PERSON><PERSON>Value('journeyman')
  journeyman,
  @JsonValue('expert')
  expert,
  @JsonValue('master')
  master,
  @JsonValue('grandmaster')
  grandmaster,
  @<PERSON>sonValue('legendary')
  legendary,
  @JsonValue('mythic')
  mythic,
}

/// User account status
enum UserStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
  @JsonValue('pending')
  pending,
}

/// Core user model with gamification integration
@JsonSerializable()
class User extends Equatable {
  /// Unique user identifier
  final String id;

  /// User's email address (unique)
  final String email;

  /// User's display name
  final String displayName;

  /// Optional first name
  final String? firstName;

  /// Optional last name
  final String? lastName;

  /// Profile avatar URL
  final String? avatarUrl;

  /// User's current role based on points
  final UserRole role;

  /// Current account status
  final UserStatus status;

  /// Total points accumulated
  final int totalPoints;

  /// Points earned in current level
  final int currentLevelPoints;

  /// Current experience level
  final int level;

  /// Current streak count (days)
  final int currentStreak;

  /// Longest streak achieved
  final int longestStreak;

  /// Number of achievements unlocked
  final int achievementCount;

  /// Number of quests completed
  final int questsCompleted;

  /// Number of tasks completed
  final int tasksCompleted;

  /// User preferences as JSON
  final Map<String, dynamic>? preferences;

  /// Account creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Last login timestamp
  final DateTime? lastLoginAt;

  const User({
    required this.id,
    required this.email,
    required this.displayName,
    this.firstName,
    this.lastName,
    this.avatarUrl,
    required this.role,
    required this.status,
    required this.totalPoints,
    required this.currentLevelPoints,
    required this.level,
    required this.currentStreak,
    required this.longestStreak,
    required this.achievementCount,
    required this.questsCompleted,
    required this.tasksCompleted,
    this.preferences,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
  });

  /// Create User from JSON
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  /// Convert User to JSON
  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// Get user's full name
  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    if (firstName != null) return firstName!;
    if (lastName != null) return lastName!;
    return displayName;
  }

  /// Get points needed for next level
  int get pointsToNextLevel {
    final nextLevelThreshold = _getLevelThreshold(level + 1);
    return nextLevelThreshold - totalPoints;
  }

  /// Get progress percentage to next level
  double get levelProgress {
    final currentLevelThreshold = _getLevelThreshold(level);
    final nextLevelThreshold = _getLevelThreshold(level + 1);
    final levelRange = nextLevelThreshold - currentLevelThreshold;
    return levelRange > 0 ? currentLevelPoints / levelRange : 1.0;
  }

  /// Get role multiplier for points calculation
  double get roleMultiplier {
    switch (role) {
      case UserRole.newcomer:
        return 1.0;
      case UserRole.apprentice:
        return 1.2;
      case UserRole.journeyman:
        return 1.4;
      case UserRole.expert:
        return 1.6;
      case UserRole.master:
        return 1.8;
      case UserRole.grandmaster:
        return 2.0;
      case UserRole.legendary:
        return 2.5;
      case UserRole.mythic:
        return 3.0;
    }
  }

  /// Create a copy with updated fields
  User copyWith({
    String? id,
    String? email,
    String? displayName,
    String? firstName,
    String? lastName,
    String? avatarUrl,
    UserRole? role,
    UserStatus? status,
    int? totalPoints,
    int? currentLevelPoints,
    int? level,
    int? currentStreak,
    int? longestStreak,
    int? achievementCount,
    int? questsCompleted,
    int? tasksCompleted,
    Map<String, dynamic>? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      role: role ?? this.role,
      status: status ?? this.status,
      totalPoints: totalPoints ?? this.totalPoints,
      currentLevelPoints: currentLevelPoints ?? this.currentLevelPoints,
      level: level ?? this.level,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      achievementCount: achievementCount ?? this.achievementCount,
      questsCompleted: questsCompleted ?? this.questsCompleted,
      tasksCompleted: tasksCompleted ?? this.tasksCompleted,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  /// Get threshold points for a given level
  static int _getLevelThreshold(int level) {
    // Progressive level system: level^2 * 100 points
    return level * level * 100;
  }

  /// Create empty user for initialization
  static User empty() {
    final now = DateTime.now();
    return User(
      id: '',
      email: '',
      displayName: '',
      role: UserRole.newcomer,
      status: UserStatus.pending,
      totalPoints: 0,
      currentLevelPoints: 0,
      level: 1,
      currentStreak: 0,
      longestStreak: 0,
      achievementCount: 0,
      questsCompleted: 0,
      tasksCompleted: 0,
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        firstName,
        lastName,
        avatarUrl,
        role,
        status,
        totalPoints,
        currentLevelPoints,
        level,
        currentStreak,
        longestStreak,
        achievementCount,
        questsCompleted,
        tasksCompleted,
        preferences,
        createdAt,
        updatedAt,
        lastLoginAt,
      ];

  @override
  bool get stringify => true;
}