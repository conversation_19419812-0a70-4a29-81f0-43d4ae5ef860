import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shared/shared.dart';
import '../services/notification_service.dart';
import '../utils/response_utils.dart';

/// Routes for notification-related endpoints
class NotificationRoutes {
  final NotificationService _notificationService;

  NotificationRoutes({required NotificationService notificationService})
      : _notificationService = notificationService;

  Router get router {
    final router = Router();

    // Get notifications for current user
    router.get('/notifications', _getNotifications);
    
    // Get notification by ID
    router.get('/notifications/<id>', _getNotification);
    
    // Create a new notification
    router.post('/notifications', _createNotification);
    
    // Create notification from template
    router.post('/notifications/from-template', _createNotificationFromTemplate);
    
    // Mark notifications as read
    router.patch('/notifications/mark-read', _markNotificationsAsRead);
    
    // Delete notification
    router.delete('/notifications/<id>', _deleteNotification);
    
    // Get notification preferences
    router.get('/notifications/preferences', _getNotificationPreferences);
    
    // Update notification preferences
    router.put('/notifications/preferences', _updateNotificationPreferences);
    
    // Get notification statistics
    router.get('/notifications/stats', _getNotificationStats);
    
    // Register device for push notifications
    router.post('/notifications/register-device', _registerDevice);
    
    // Unregister device
    router.delete('/notifications/register-device', _unregisterDevice);
    
    // Get notification templates (admin only)
    router.get('/notifications/templates', _getNotificationTemplates);
    
    // Create notification template (admin only)
    router.post('/notifications/templates', _createNotificationTemplate);
    
    // Update notification template (admin only)
    router.put('/notifications/templates/<id>', _updateNotificationTemplate);
    
    // Delete notification template (admin only)
    router.delete('/notifications/templates/<id>', _deleteNotificationTemplate);

    return router;
  }

  /// GET /notifications - Get notifications for current user
  Future<Response> _getNotifications(Request request) async {
    try {
      final userId = request.context['userId'] as String;
      
      // Parse query parameters
      final params = request.url.queryParameters;
      final getRequest = GetNotificationsRequest(
        status: params['status'] != null 
            ? NotificationStatus.values.firstWhere((s) => s.name == params['status'])
            : null,
        type: params['type'] != null
            ? NotificationType.values.firstWhere((t) => t.name == params['type'])
            : null,
        category: params['category'] != null
            ? NotificationCategory.values.firstWhere((c) => c.name == params['category'])
            : null,
        priority: params['priority'] != null
            ? NotificationPriority.values.firstWhere((p) => p.name == params['priority'])
            : null,
        isRead: params['isRead'] != null ? params['isRead'] == 'true' : null,
        startDate: params['startDate'] != null ? DateTime.parse(params['startDate']!) : null,
        endDate: params['endDate'] != null ? DateTime.parse(params['endDate']!) : null,
        page: int.tryParse(params['page'] ?? '1') ?? 1,
        limit: int.tryParse(params['limit'] ?? '20') ?? 20,
        sortBy: params['sortBy'],
        sortDirection: params['sortDirection'] != null
            ? SortDirection.values.firstWhere((d) => d.name == params['sortDirection'])
            : null,
      );

      final response = await _notificationService.getNotifications(userId, getRequest);
      
      return ResponseUtils.success(data: response.toJson());
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// GET /notifications/:id - Get notification by ID
  Future<Response> _getNotification(Request request) async {
    try {
      final userId = request.context['userId'] as String;
      final notificationId = request.params['id']!;
      
      final notification = await _notificationService.getNotificationById(userId, notificationId);
      
      if (notification == null) {
        return ResponseUtils.notFound(message: 'Notification not found');
      }
      
      return ResponseUtils.success(data: notification.toJson());
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// POST /notifications - Create a new notification
  Future<Response> _createNotification(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final createRequest = CreateNotificationRequest.fromJson(data);
      final notification = await _notificationService.createNotification(createRequest);
      
      return ResponseUtils.created(data: notification.toJson());
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// POST /notifications/from-template - Create notification from template
  Future<Response> _createNotificationFromTemplate(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final createRequest = CreateNotificationFromTemplateRequest.fromJson(data);
      final notification = await _notificationService.createNotificationFromTemplate(createRequest);
      
      return ResponseUtils.created(data: notification.toJson());
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// PATCH /notifications/mark-read - Mark notifications as read
  Future<Response> _markNotificationsAsRead(Request request) async {
    try {
      final userId = request.context['userId'] as String;
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final markReadRequest = MarkNotificationsReadRequest.fromJson(data);
      await _notificationService.markNotificationsAsRead(userId, markReadRequest);
      
      return ResponseUtils.success(message: 'Notifications marked as read');
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// DELETE /notifications/:id - Delete notification
  Future<Response> _deleteNotification(Request request) async {
    try {
      final userId = request.context['userId'] as String;
      final notificationId = request.params['id']!;
      
      await _notificationService.deleteNotification(userId, notificationId);
      
      return ResponseUtils.success(message: 'Notification deleted');
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// GET /notifications/preferences - Get notification preferences
  Future<Response> _getNotificationPreferences(Request request) async {
    try {
      final userId = request.context['userId'] as String;
      
      final preferences = await _notificationService.getNotificationPreferences(userId);
      
      return ResponseUtils.success(data: preferences);
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// PUT /notifications/preferences - Update notification preferences
  Future<Response> _updateNotificationPreferences(Request request) async {
    try {
      final userId = request.context['userId'] as String;
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final updateRequest = UpdateNotificationPreferencesRequest.fromJson(data);
      final preferences = await _notificationService.updateNotificationPreferences(
        userId,
        updateRequest.toJson(),
      );
      
      return ResponseUtils.success(data: {'success': preferences});
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// GET /notifications/stats - Get notification statistics
  Future<Response> _getNotificationStats(Request request) async {
    try {
      final userId = request.context['userId'] as String;
      
      final stats = await _notificationService.getNotificationStats(userId);
      
      return ResponseUtils.success(data: stats.toJson());
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// POST /notifications/register-device - Register device for push notifications
  Future<Response> _registerDevice(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final registerRequest = RegisterDeviceRequest.fromJson(data);
      await _notificationService.registerDevice(registerRequest);
      
      return ResponseUtils.success(message: 'Device registered successfully');
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// DELETE /notifications/register-device - Unregister device
  Future<Response> _unregisterDevice(Request request) async {
    try {
      final params = request.url.queryParameters;
      final deviceToken = params['deviceToken'];
      
      if (deviceToken == null) {
        return ResponseUtils.badRequest(message: 'Device token is required');
      }
      
      await _notificationService.unregisterDevice(deviceToken);
      
      return ResponseUtils.success(message: 'Device unregistered successfully');
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// GET /notifications/templates - Get notification templates (admin only)
  Future<Response> _getNotificationTemplates(Request request) async {
    try {
      // Check if user is admin
      final userRole = request.context['userRole'] as String?;
      if (userRole != 'admin') {
        return ResponseUtils.forbidden(message: 'Admin access required');
      }
      
      final templates = await _notificationService.getNotificationTemplates();
      
      return ResponseUtils.success(data: {
        'templates': templates.map((t) => t.toJson()).toList(),
      });
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// POST /notifications/templates - Create notification template (admin only)
  Future<Response> _createNotificationTemplate(Request request) async {
    try {
      // Check if user is admin
      final userRole = request.context['userRole'] as String?;
      if (userRole != 'admin') {
        return ResponseUtils.forbidden(message: 'Admin access required');
      }
      
      final userId = request.context['userId'] as String;
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final template = NotificationTemplate.fromJson({
        ...data,
        'id': _generateTemplateId(),
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'createdBy': userId,
      });
      
      final createdTemplate = await _notificationService.createNotificationTemplate(template.toJson());
      
      return ResponseUtils.created(data: createdTemplate.toJson());
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// PUT /notifications/templates/:id - Update notification template (admin only)
  Future<Response> _updateNotificationTemplate(Request request) async {
    try {
      // Check if user is admin
      final userRole = request.context['userRole'] as String?;
      if (userRole != 'admin') {
        return ResponseUtils.forbidden(message: 'Admin access required');
      }
      
      final templateId = request.params['id']!;
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final updatedTemplate = await _notificationService.updateNotificationTemplate(
        templateId,
        data,
      );
      
      return ResponseUtils.success(data: updatedTemplate.toJson());
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// DELETE /notifications/templates/:id - Delete notification template (admin only)
  Future<Response> _deleteNotificationTemplate(Request request) async {
    try {
      // Check if user is admin
      final userRole = request.context['userRole'] as String?;
      if (userRole != 'admin') {
        return ResponseUtils.forbidden(message: 'Admin access required');
      }
      
      final templateId = request.params['id']!;
      await _notificationService.deleteNotificationTemplate(templateId);
      
      return ResponseUtils.success(message: 'Template deleted successfully');
    } catch (error) {
      return ResponseUtils.error(message: error.toString());
    }
  }

  /// Generates a unique template ID
  String _generateTemplateId() {
    return 'template_${DateTime.now().millisecondsSinceEpoch}';
  }
}
