import 'package:flutter/material.dart';

/// Enhanced card variants
enum CardVariant {
  elevated,
  outlined,
  filled,
  glass,
  gradient,
}

/// Enhanced card with multiple variants and animations
class EnhancedCard extends StatefulWidget {
  final Widget child;
  final CardVariant variant;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? width;
  final double? height;
  final Color? color;
  final Gradient? gradient;
  final double borderRadius;
  final double elevation;
  final Border? border;
  final bool enableHoverEffect;
  final bool enablePressEffect;
  final Duration animationDuration;
  final List<BoxShadow>? customShadows;

  const EnhancedCard({
    super.key,
    required this.child,
    this.variant = CardVariant.elevated,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.color,
    this.gradient,
    this.borderRadius = 16.0,
    this.elevation = 2.0,
    this.border,
    this.enableHoverEffect = true,
    this.enablePressEffect = true,
    this.animationDuration = const Duration(milliseconds: 200),
    this.customShadows,
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _elevationAnimation = Tween<double>(
      begin: widget.elevation,
      end: widget.elevation + 4,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enablePressEffect) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.enablePressEffect) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.enablePressEffect) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleHover(bool isHovered) {
    if (widget.enableHoverEffect) {
      setState(() => _isHovered = isHovered);
      if (isHovered && !_isPressed) {
        _animationController.forward();
      } else if (!isHovered && !_isPressed) {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.enablePressEffect ? _scaleAnimation.value : 1.0,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin,
            child: MouseRegion(
              onEnter: (_) => _handleHover(true),
              onExit: (_) => _handleHover(false),
              child: GestureDetector(
                onTap: widget.onTap,
                onLongPress: widget.onLongPress,
                onTapDown: _handleTapDown,
                onTapUp: _handleTapUp,
                onTapCancel: _handleTapCancel,
                child: AnimatedContainer(
                  duration: widget.animationDuration,
                  decoration: _buildDecoration(theme),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    child: Padding(
                      padding: widget.padding ?? const EdgeInsets.all(16.0),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _buildDecoration(ThemeData theme) {
    switch (widget.variant) {
      case CardVariant.elevated:
        return BoxDecoration(
          color: widget.color ?? theme.cardColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: widget.customShadows ?? [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: _isHovered ? _elevationAnimation.value : widget.elevation,
              offset: Offset(0, _isHovered ? _elevationAnimation.value / 2 : widget.elevation / 2),
            ),
          ],
          border: widget.border,
        );
        
      case CardVariant.outlined:
        return BoxDecoration(
          color: widget.color ?? Colors.transparent,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: widget.border ?? Border.all(
            color: theme.colorScheme.outline,
            width: _isHovered ? 2.0 : 1.0,
          ),
        );
        
      case CardVariant.filled:
        return BoxDecoration(
          color: widget.color ?? theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: widget.border,
        );
        
      case CardVariant.glass:
        return BoxDecoration(
          color: (widget.color ?? theme.cardColor).withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: widget.border ?? Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1.0,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.05),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        );
        
      case CardVariant.gradient:
        return BoxDecoration(
          gradient: widget.gradient ?? LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.1),
              theme.colorScheme.secondary.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: widget.border,
          boxShadow: widget.customShadows ?? [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: widget.elevation,
              offset: Offset(0, widget.elevation / 2),
            ),
          ],
        );
    }
  }
}

/// Expandable card with smooth animations
class ExpandableCard extends StatefulWidget {
  final Widget header;
  final Widget expandedContent;
  final bool initiallyExpanded;
  final Duration animationDuration;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final CardVariant variant;
  final double borderRadius;
  final VoidCallback? onExpansionChanged;

  const ExpandableCard({
    super.key,
    required this.header,
    required this.expandedContent,
    this.initiallyExpanded = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.padding,
    this.margin,
    this.variant = CardVariant.elevated,
    this.borderRadius = 16.0,
    this.onExpansionChanged,
  });

  @override
  State<ExpandableCard> createState() => _ExpandableCardState();
}

class _ExpandableCardState extends State<ExpandableCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  late Animation<double> _iconRotationAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    
    _iconRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
    widget.onExpansionChanged?.call();
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      variant: widget.variant,
      padding: EdgeInsets.zero,
      margin: widget.margin,
      borderRadius: widget.borderRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: _toggleExpansion,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            child: Padding(
              padding: widget.padding ?? const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(child: widget.header),
                  AnimatedBuilder(
                    animation: _iconRotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _iconRotationAnimation.value * 3.14159,
                        child: Icon(
                          Icons.expand_more,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Padding(
              padding: widget.padding ?? const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: widget.expandedContent,
            ),
          ),
        ],
      ),
    );
  }
}
