import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:aws_ses_api/email-2010-12-01.dart' as aws_ses;
import 'package:mailer/mailer.dart' as mailer;
import 'package:mailer/smtp_server.dart';
import 'logging_service.dart';

/// Email service for sending various types of emails
/// Supports multiple providers (SendGrid, AWS SES, SMTP)
class EmailService {
  final String _provider;
  final Map<String, String> _config;
  
  EmailService({
    String provider = 'mock',
    Map<String, String> config = const {},
  }) : _provider = provider,
       _config = config;

  /// Send verification email
  Future<bool> sendVerificationEmail(String email, String token) async {
    final subject = 'Verify Your Email Address';
    final body = '''
    <html>
      <body>
        <h2>Email Verification</h2>
        <p>Please click the link below to verify your email address:</p>
        <a href="${_config['baseUrl'] ?? 'http://localhost:8080'}/auth/verify-email?token=$token">
          Verify Email
        </a>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't request this verification, please ignore this email.</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email, String token) async {
    final subject = 'Reset Your Password';
    final body = '''
    <html>
      <body>
        <h2>Password Reset</h2>
        <p>You requested a password reset. Click the link below to reset your password:</p>
        <a href="${_config['baseUrl'] ?? 'http://localhost:8080'}/auth/reset-password?token=$token">
          Reset Password
        </a>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this reset, please ignore this email.</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Send MFA code via email
  Future<bool> sendMFACode(String email, String code) async {
    final subject = 'Your Verification Code';
    final body = '''
    <html>
      <body>
        <h2>Verification Code</h2>
        <p>Your verification code is:</p>
        <h1 style="color: #007bff; font-size: 32px; letter-spacing: 4px;">$code</h1>
        <p>This code will expire in 5 minutes.</p>
        <p>If you didn't request this code, please ignore this email.</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Send welcome email
  Future<bool> sendWelcomeEmail(String email, String name) async {
    final subject = 'Welcome to Quester!';
    final body = '''
    <html>
      <body>
        <h2>Welcome to Quester, $name!</h2>
        <p>Thank you for joining our gamified productivity platform.</p>
        <p>Get started by:</p>
        <ul>
          <li>Creating your first quest</li>
          <li>Setting up your profile</li>
          <li>Exploring the leaderboards</li>
        </ul>
        <p>Happy questing!</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Internal method to send email based on provider
  Future<bool> _sendEmail(String to, String subject, String body) async {
    try {
      switch (_provider.toLowerCase()) {
        case 'sendgrid':
          return await _sendViaSendGrid(to, subject, body);
        case 'ses':
        case 'aws':
          return await _sendViaAWSSES(to, subject, body);
        case 'smtp':
          return await _sendViaSMTP(to, subject, body);
        case 'mock':
          return await _sendViaMock(to, subject, body);
        default:
          LoggingService.error('Unknown email provider: $_provider');
          return false;
      }
    } catch (e) {
      LoggingService.error('Failed to send email to $to: $e');
      return false;
    }
  }

  /// Send email via SendGrid
  Future<bool> _sendViaSendGrid(String to, String subject, String body) async {
    final apiKey = _config['sendgridApiKey'];
    if (apiKey == null) {
      LoggingService.error('SendGrid API key not configured');
      return false;
    }

    final response = await http.post(
      Uri.parse('https://api.sendgrid.com/v3/mail/send'),
      headers: {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'personalizations': [
          {
            'to': [{'email': to}],
            'subject': subject,
          }
        ],
        'from': {
          'email': _config['fromEmail'] ?? '<EMAIL>',
          'name': _config['fromName'] ?? 'Quester',
        },
        'content': [
          {
            'type': 'text/html',
            'value': body,
          }
        ],
      }),
    );

    if (response.statusCode == 202) {
      LoggingService.email('Email sent via SendGrid to $to', recipient: to);
      return true;
    } else {
      LoggingService.error('SendGrid API error: ${response.statusCode} - ${response.body}');
      return false;
    }
  }

  /// Send email via AWS SES
  Future<bool> _sendViaAWSSES(String to, String subject, String body) async {
    try {
      // Get AWS credentials from environment or config
      final accessKeyId = _config['aws_access_key_id'] ?? Platform.environment['AWS_ACCESS_KEY_ID'];
      final secretAccessKey = _config['aws_secret_access_key'] ?? Platform.environment['AWS_SECRET_ACCESS_KEY'];
      final region = _config['aws_region'] ?? Platform.environment['AWS_REGION'] ?? 'us-east-1';
      final fromEmail = _config['from_email'] ?? '<EMAIL>';

      if (accessKeyId == null || secretAccessKey == null) {
        LoggingService.error('AWS SES credentials not configured');
        return await _sendViaMock(to, subject, body);
      }

      // Initialize SES client
      final ses = aws_ses.SES(
        region: region,
        credentials: aws_ses.AwsClientCredentials(
          accessKey: accessKeyId,
          secretKey: secretAccessKey,
        ),
      );

      // Send email using SES
      final result = await ses.sendEmail(
        source: fromEmail,
        destination: aws_ses.Destination(toAddresses: [to]),
        message: aws_ses.Message(
          subject: aws_ses.Content(data: subject),
          body: aws_ses.Body(
            html: aws_ses.Content(data: body),
            text: aws_ses.Content(data: _stripHtml(body)),
          ),
        ),
      );

      LoggingService.email('Email sent via AWS SES - MessageId: ${result.messageId}', recipient: to);
      return true;

    } catch (e) {
      LoggingService.error('AWS SES error: $e');
      // Fallback to mock for development/testing
      return await _sendViaMock(to, subject, body);
    }
  }

  /// Send email via SMTP
  Future<bool> _sendViaSMTP(String to, String subject, String body) async {
    try {
      // Get SMTP configuration from config or environment
      final smtpHost = _config['smtp_host'] ?? Platform.environment['SMTP_HOST'] ?? 'localhost';
      final smtpPort = int.tryParse(_config['smtp_port'] ?? Platform.environment['SMTP_PORT'] ?? '587') ?? 587;
      final smtpUsername = _config['smtp_username'] ?? Platform.environment['SMTP_USERNAME'];
      final smtpPassword = _config['smtp_password'] ?? Platform.environment['SMTP_PASSWORD'];
      final fromEmail = _config['from_email'] ?? '<EMAIL>';
      final fromName = _config['from_name'] ?? 'Quester';
      final useTLS = (_config['smtp_use_tls'] ?? Platform.environment['SMTP_USE_TLS'] ?? 'true').toLowerCase() == 'true';

      if (smtpUsername == null || smtpPassword == null) {
        LoggingService.error('SMTP credentials not configured');
        return await _sendViaMock(to, subject, body);
      }

      // Configure SMTP server
      final smtpServer = SmtpServer(
        smtpHost,
        port: smtpPort,
        username: smtpUsername,
        password: smtpPassword,
        ssl: smtpPort == 465, // Use SSL for port 465
        allowInsecure: !useTLS,
      );

      // Create message
      final message = mailer.Message()
        ..from = mailer.Address(fromEmail, fromName)
        ..recipients.add(to)
        ..subject = subject
        ..html = body
        ..text = _stripHtml(body);

      // Send email
      final sendReport = await mailer.send(message, smtpServer);

      // Check if send was successful (no exceptions means success)
      LoggingService.email('Email sent via SMTP to $to - Report: ${sendReport.toString()}', recipient: to);
      return true;

    } catch (e) {
      LoggingService.error('SMTP error: $e');
      // Fallback to mock for development/testing
      return await _sendViaMock(to, subject, body);
    }
  }

  /// Mock email sending for development/testing
  Future<bool> _sendViaMock(String to, String subject, String body) async {
    LoggingService.email(
      'Mock email sent - To: $to, Subject: $subject (${body.length} chars)',
      recipient: to,
    );

    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 100));
    return true;
  }

  /// Strip HTML tags from text for plain text email content
  String _stripHtml(String html) {
    return html
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll('&nbsp;', ' ')          // Replace non-breaking spaces
        .replaceAll('&amp;', '&')           // Replace HTML entities
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .trim();
  }
}
