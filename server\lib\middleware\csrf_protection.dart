import 'dart:convert';
import 'dart:math';
import 'package:shelf/shelf.dart';
import 'package:crypto/crypto.dart';

/// CSRF Protection Middleware
class CSRFProtection {
  final Map<String, String> _tokens = {};
  final String _secret;
  final Duration _tokenExpiry;

  CSRFProtection({
    String? secret,
    Duration tokenExpiry = const Duration(hours: 1),
  }) : _secret = secret ?? _generateSecret(),
       _tokenExpiry = tokenExpiry;

  /// Create CSRF protection middleware
  Middleware get middleware => (Handler innerHandler) {
    return (Request request) async {
      // Skip CSRF for GET, HEAD, OPTIONS requests
      if (['GET', 'HEAD', 'OPTIONS'].contains(request.method)) {
        return await innerHandler(request);
      }

      // Skip CSRF for API key authentication
      if (request.headers['authorization']?.startsWith('ApiKey ') == true) {
        return await innerHandler(request);
      }

      // Check CSRF token for state-changing requests
      final token = request.headers['x-csrf-token'] ?? 
                   request.url.queryParameters['_csrf_token'];
                   
      if (token == null || !_validateToken(token)) {
        return Response(403,
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'error': 'CSRF token missing or invalid',
            'errorCode': 'CSRF_TOKEN_INVALID',
          }),
        );
      }

      return await innerHandler(request);
    };
  };

  /// Generate a CSRF token for a session
  String generateToken(String sessionId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final payload = '$sessionId:$timestamp';
    final signature = _signPayload(payload);
    final token = base64Url.encode(utf8.encode('$payload:$signature'));
    
    _tokens[sessionId] = token;
    
    // Clean up expired tokens
    _cleanExpiredTokens();
    
    return token;
  }

  /// Validate a CSRF token
  bool _validateToken(String token) {
    try {
      final decoded = utf8.decode(base64Url.decode(token));
      final parts = decoded.split(':');
      
      if (parts.length != 3) return false;
      
      final sessionId = parts[0];
      final timestamp = int.parse(parts[1]);
      final signature = parts[2];
      
      // Check if token is expired
      final tokenTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      if (DateTime.now().difference(tokenTime) > _tokenExpiry) {
        return false;
      }
      
      // Verify signature
      final payload = '${parts[0]}:${parts[1]}';
      final expectedSignature = _signPayload(payload);
      
      return signature == expectedSignature && _tokens[sessionId] == token;
    } catch (e) {
      return false;
    }
  }

  String _signPayload(String payload) {
    final key = utf8.encode(_secret);
    final bytes = utf8.encode(payload);
    final hmac = Hmac(sha256, key);
    return hmac.convert(bytes).toString();
  }

  static String _generateSecret() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  void _cleanExpiredTokens() {
    final now = DateTime.now();
    _tokens.removeWhere((sessionId, token) {
      try {
        final decoded = utf8.decode(base64Url.decode(token));
        final parts = decoded.split(':');
        if (parts.length < 2) return true;
        
        final timestamp = int.parse(parts[1]);
        final tokenTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        return now.difference(tokenTime) > _tokenExpiry;
      } catch (e) {
        return true; // Remove invalid tokens
      }
    });
  }
}
