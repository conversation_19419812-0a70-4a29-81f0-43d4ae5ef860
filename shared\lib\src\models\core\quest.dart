import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'quest.g.dart';

/// Quest priority levels
enum QuestPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

/// Quest status enumeration
enum QuestStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('active')
  active,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('paused')
  paused,
  @JsonValue('completed')
  completed,
  @JsonValue('archived')
  archived,
  @JsonValue('cancelled')
  cancelled,
}

/// Quest difficulty levels for point calculation
enum QuestDifficulty {
  @JsonValue('beginner')
  beginner,
  @JsonValue('intermediate')
  intermediate,
  @JsonValue('advanced')
  advanced,
  @JsonValue('expert')
  expert,
  @JsonValue('master')
  master,
}

/// Quest category for organization
enum QuestCategory {
  @JsonValue('personal')
  personal,
  @JsonValue('work')
  work,
  @JsonValue('learning')
  learning,
  @JsonValue('health')
  health,
  @JsonValue('social')
  social,
  @JsonValue('creative')
  creative,
  @JsonValue('productivity')
  productivity,
  @JsonValue('other')
  other,
}

/// Quest type for UI filtering (alias for QuestCategory with additional team type)
enum QuestType {
  @JsonValue('personal')
  personal,
  @JsonValue('team')
  team,
  @JsonValue('work')
  work,
  @JsonValue('learning')
  learning,
  @JsonValue('health')
  health,
  @JsonValue('social')
  social,
  @JsonValue('creative')
  creative,
  @JsonValue('other')
  other,
}

/// Core quest model with gamification features
@JsonSerializable()
class Quest extends Equatable {
  /// Unique quest identifier
  final String id;

  /// Quest title
  final String title;

  /// Detailed description
  final String description;

  /// Quest creator user ID
  final String createdById;

  /// Quest assignee user ID (can be same as creator)
  final String? assignedToId;

  /// Current quest status
  final QuestStatus status;

  /// Quest priority level
  final QuestPriority priority;

  /// Quest difficulty for point calculation
  final QuestDifficulty difficulty;

  /// Quest category
  final QuestCategory category;

  /// Base points awarded on completion
  final int basePoints;

  /// Bonus points for early completion
  final int bonusPoints;

  /// Total points available (base + bonus)
  final int totalPoints;

  /// Points earned so far (progress-based)
  final int earnedPoints;

  /// Quest deadline (optional)
  final DateTime? deadline;

  /// Estimated duration in hours
  final int? estimatedHours;

  /// Actual time spent in hours
  final int? actualHours;

  /// Progress percentage (0-100)
  final double progressPercentage;

  /// List of task IDs belonging to this quest
  final List<String> taskIds;

  /// List of participant user IDs
  final List<String> participantIds;

  /// Quest tags for organization
  final List<String> tags;

  /// Quest metadata as JSON
  final Map<String, dynamic>? metadata;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Completion timestamp (if completed)
  final DateTime? completedAt;

  /// Quest start timestamp (when moved to active/in_progress)
  final DateTime? startedAt;

  const Quest({
    required this.id,
    required this.title,
    required this.description,
    required this.createdById,
    this.assignedToId,
    required this.status,
    required this.priority,
    required this.difficulty,
    required this.category,
    required this.basePoints,
    required this.bonusPoints,
    required this.totalPoints,
    required this.earnedPoints,
    this.deadline,
    this.estimatedHours,
    this.actualHours,
    required this.progressPercentage,
    required this.taskIds,
    required this.participantIds,
    required this.tags,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.completedAt,
    this.startedAt,
  });

  /// Create Quest from JSON
  factory Quest.fromJson(Map<String, dynamic> json) => _$QuestFromJson(json);

  /// Convert Quest to JSON
  Map<String, dynamic> toJson() => _$QuestToJson(this);

  /// Check if quest is overdue
  bool get isOverdue {
    if (deadline == null) return false;
    return DateTime.now().isAfter(deadline!) && status != QuestStatus.completed;
  }

  /// Check if quest is completed
  bool get isCompleted => status == QuestStatus.completed;

  /// Check if quest is active (in progress or active)
  bool get isActive => status == QuestStatus.active || status == QuestStatus.inProgress;

  /// Get days until deadline (negative if overdue)
  int? get daysUntilDeadline {
    if (deadline == null) return null;
    final now = DateTime.now();
    return deadline!.difference(now).inDays;
  }

  /// Get hours until deadline
  int? get hoursUntilDeadline {
    if (deadline == null) return null;
    final now = DateTime.now();
    return deadline!.difference(now).inHours;
  }

  /// Get difficulty multiplier for points
  double get difficultyMultiplier {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 1.0;
      case QuestDifficulty.intermediate:
        return 1.5;
      case QuestDifficulty.advanced:
        return 2.0;
      case QuestDifficulty.expert:
        return 2.5;
      case QuestDifficulty.master:
        return 3.0;
    }
  }

  /// Get priority multiplier for urgency
  double get priorityMultiplier {
    switch (priority) {
      case QuestPriority.low:
        return 1.0;
      case QuestPriority.medium:
        return 1.2;
      case QuestPriority.high:
        return 1.5;
      case QuestPriority.urgent:
        return 2.0;
    }
  }

  /// Calculate time efficiency bonus (1.0 = on time, >1.0 = early, <1.0 = late)
  double get timeEfficiencyBonus {
    if (actualHours == null || estimatedHours == null || estimatedHours! <= 0) {
      return 1.0;
    }
    return estimatedHours! / actualHours!;
  }

  /// Get total participant count including creator
  int get totalParticipants => participantIds.length + 1; // +1 for creator

  /// Check if user is a participant
  bool isParticipant(String userId) {
    return createdById == userId || 
           assignedToId == userId || 
           participantIds.contains(userId);
  }

  /// Create a copy with updated fields
  Quest copyWith({
    String? id,
    String? title,
    String? description,
    String? createdById,
    String? assignedToId,
    QuestStatus? status,
    QuestPriority? priority,
    QuestDifficulty? difficulty,
    QuestCategory? category,
    int? basePoints,
    int? bonusPoints,
    int? totalPoints,
    int? earnedPoints,
    DateTime? deadline,
    int? estimatedHours,
    int? actualHours,
    double? progressPercentage,
    List<String>? taskIds,
    List<String>? participantIds,
    List<String>? tags,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
    DateTime? startedAt,
  }) {
    return Quest(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdById: createdById ?? this.createdById,
      assignedToId: assignedToId ?? this.assignedToId,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      difficulty: difficulty ?? this.difficulty,
      category: category ?? this.category,
      basePoints: basePoints ?? this.basePoints,
      bonusPoints: bonusPoints ?? this.bonusPoints,
      totalPoints: totalPoints ?? this.totalPoints,
      earnedPoints: earnedPoints ?? this.earnedPoints,
      deadline: deadline ?? this.deadline,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      taskIds: taskIds ?? this.taskIds,
      participantIds: participantIds ?? this.participantIds,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
      startedAt: startedAt ?? this.startedAt,
    );
  }

  /// Create empty quest for initialization
  static Quest empty() {
    final now = DateTime.now();
    return Quest(
      id: '',
      title: '',
      description: '',
      createdById: '',
      status: QuestStatus.draft,
      priority: QuestPriority.medium,
      difficulty: QuestDifficulty.beginner,
      category: QuestCategory.personal,
      basePoints: 0,
      bonusPoints: 0,
      totalPoints: 0,
      earnedPoints: 0,
      progressPercentage: 0.0,
      taskIds: [],
      participantIds: [],
      tags: [],
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        createdById,
        assignedToId,
        status,
        priority,
        difficulty,
        category,
        basePoints,
        bonusPoints,
        totalPoints,
        earnedPoints,
        deadline,
        estimatedHours,
        actualHours,
        progressPercentage,
        taskIds,
        participantIds,
        tags,
        metadata,
        createdAt,
        updatedAt,
        completedAt,
        startedAt,
      ];

  @override
  bool get stringify => true;
}