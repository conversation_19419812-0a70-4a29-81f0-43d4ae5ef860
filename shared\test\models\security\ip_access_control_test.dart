import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('IPAccessControl Tests', () {
    late IPAccessControl testIPControl;
    late IPGeoLocation testGeoLocation;
    
    setUp(() {
      testGeoLocation = const IPGeoLocation(
        country: 'United States',
        countryCode: 'US',
        region: 'California',
        city: 'San Francisco',
        latitude: 37.7749,
        longitude: -122.4194,
        timezone: 'America/Los_Angeles',
        isp: 'Cloudflare Inc.',
        organization: 'Cloudflare',
        asn: 'AS13335',
      );
      
      testIPControl = IPAccessControl(
        id: 'ip_control_123',
        organizationId: 'org_456',
        userId: 'user_789',
        ipAddress: '***********00',
        ipRange: '***********/24',
        accessType: IPAccessType.whitelist,
        ruleType: IPRuleType.organization,
        description: 'Office network whitelist',
        geoLocation: testGeoLocation,
        firstSeen: DateTime.parse('2025-01-10T09:00:00.000Z'),
        lastSeen: DateTime.parse('2025-01-15T14:30:00.000Z'),
        accessCount: 150,
        isSuspicious: false,
        riskScore: 10,
        isActive: true,
        createdAt: DateTime.parse('2025-01-10T09:00:00.000Z'),
        updatedAt: DateTime.parse('2025-01-15T14:30:00.000Z'),
        createdBy: 'admin_123',
      );
    });

    test('should create valid IPAccessControl instance', () {
      expect(testIPControl.id, equals('ip_control_123'));
      expect(testIPControl.organizationId, equals('org_456'));
      expect(testIPControl.userId, equals('user_789'));
      expect(testIPControl.ipAddress, equals('***********00'));
      expect(testIPControl.ipRange, equals('***********/24'));
      expect(testIPControl.accessType, equals(IPAccessType.whitelist));
      expect(testIPControl.ruleType, equals(IPRuleType.organization));
      expect(testIPControl.accessCount, equals(150));
      expect(testIPControl.riskScore, equals(10));
      expect(testIPControl.isSuspicious, isFalse);
      expect(testIPControl.isActive, isTrue);
    });

    test('should create empty IPAccessControl for testing', () {
      final empty = IPAccessControl.empty();
      expect(empty.id, isEmpty);
      expect(empty.ipAddress, isEmpty);
      expect(empty.accessType, equals(IPAccessType.monitor));
      expect(empty.ruleType, equals(IPRuleType.organization));
      expect(empty.accessCount, equals(0));
      expect(empty.riskScore, equals(0));
      expect(empty.isSuspicious, isFalse);
      expect(empty.isActive, isTrue);
    });

    group('Enum Display Names and Descriptions', () {
      test('should have correct display names for access types', () {
        expect(IPAccessType.whitelist.displayName, equals('Whitelist'));
        expect(IPAccessType.blacklist.displayName, equals('Blacklist'));
        expect(IPAccessType.monitor.displayName, equals('Monitor'));
      });

      test('should have correct descriptions for access types', () {
        expect(IPAccessType.whitelist.description, equals('Allow access only from this IP/range'));
        expect(IPAccessType.blacklist.description, equals('Block access from this IP/range'));
        expect(IPAccessType.monitor.description, equals('Monitor and track access from this IP/range'));
      });

      test('should have correct display names for rule types', () {
        expect(IPRuleType.organization.displayName, equals('Organization Rule'));
        expect(IPRuleType.user.displayName, equals('User Rule'));
        expect(IPRuleType.automatic.displayName, equals('Automatic Rule'));
      });
    });

    group('IPGeoLocation Tests', () {
      test('should create valid IPGeoLocation instance', () {
        expect(testGeoLocation.country, equals('United States'));
        expect(testGeoLocation.countryCode, equals('US'));
        expect(testGeoLocation.region, equals('California'));
        expect(testGeoLocation.city, equals('San Francisco'));
        expect(testGeoLocation.latitude, equals(37.7749));
        expect(testGeoLocation.longitude, equals(-122.4194));
        expect(testGeoLocation.isp, equals('Cloudflare Inc.'));
        expect(testGeoLocation.organization, equals('Cloudflare'));
        expect(testGeoLocation.asn, equals('AS13335'));
      });

      test('should format display location correctly', () {
        expect(testGeoLocation.displayLocation, equals('San Francisco, California, United States'));
        
        const partialLocation = IPGeoLocation(
          city: 'New York',
          country: 'United States',
        );
        expect(partialLocation.displayLocation, equals('New York, United States'));
        
        const minimalLocation = IPGeoLocation(country: 'Canada');
        expect(minimalLocation.displayLocation, equals('Canada'));
        
        const emptyLocation = IPGeoLocation();
        expect(emptyLocation.displayLocation, isEmpty);
      });

      test('should format full display info correctly', () {
        expect(testGeoLocation.fullDisplayInfo, 
            equals('San Francisco, California, United States • ISP: Cloudflare Inc. • Org: Cloudflare'));
        
        const minimalGeoInfo = IPGeoLocation(
          city: 'Seattle',
          isp: 'Amazon',
        );
        expect(minimalGeoInfo.fullDisplayInfo, equals('Seattle • ISP: Amazon'));
        
        const noLocationGeoInfo = IPGeoLocation(organization: 'Google');
        expect(noLocationGeoInfo.fullDisplayInfo, equals('Org: Google'));
      });

      test('should serialize and deserialize correctly', () {
        final json = testGeoLocation.toJson();
        final deserialized = IPGeoLocation.fromJson(json);
        expect(deserialized, equals(testGeoLocation));
      });
    });

    group('Risk Assessment', () {
      test('should identify risk levels correctly', () {
        expect(testIPControl.isLowRisk, isTrue);
        expect(testIPControl.isMediumRisk, isFalse);
        expect(testIPControl.isHighRisk, isFalse);
        expect(testIPControl.riskLevel, equals('Low'));
        expect(testIPControl.riskColor, equals('#28a745'));

        final mediumRisk = testIPControl.copyWith(riskScore: 50);
        expect(mediumRisk.isLowRisk, isFalse);
        expect(mediumRisk.isMediumRisk, isTrue);
        expect(mediumRisk.isHighRisk, isFalse);
        expect(mediumRisk.riskLevel, equals('Medium'));
        expect(mediumRisk.riskColor, equals('#fd7e14'));

        final highRisk = testIPControl.copyWith(riskScore: 80);
        expect(highRisk.isLowRisk, isFalse);
        expect(highRisk.isMediumRisk, isFalse);
        expect(highRisk.isHighRisk, isTrue);
        expect(highRisk.riskLevel, equals('High'));
        expect(highRisk.riskColor, equals('#dc3545'));
      });
    });

    group('Activity Analysis', () {
      test('should identify recently active IPs', () {
        expect(testIPControl.isRecentlyActive, isTrue);
        
        final oldActivity = testIPControl.copyWith(
          lastSeen: DateTime.now().subtract(const Duration(days: 30)),
        );
        expect(oldActivity.isRecentlyActive, isFalse);
      });

      test('should calculate activity frequency correctly', () {
        final now = DateTime.now();
        final recentIP = testIPControl.copyWith(
          firstSeen: now.subtract(const Duration(days: 10)),
          accessCount: 100,
        );
        
        expect(recentIP.activityFrequency, equals(10.0)); // 100 accesses / 10 days
        
        final sameDay = testIPControl.copyWith(
          firstSeen: now,
          accessCount: 50,
        );
        expect(sameDay.activityFrequency, equals(50.0)); // Same day, so full count
      });

      test('should format time since last seen', () {
        final now = DateTime.now();
        
        final recentIP = testIPControl.copyWith(
          lastSeen: now.subtract(const Duration(minutes: 5)),
        );
        expect(recentIP.timeSinceLastSeen, equals('5m ago'));
        
        final hourlyIP = testIPControl.copyWith(
          lastSeen: now.subtract(const Duration(hours: 3)),
        );
        expect(hourlyIP.timeSinceLastSeen, equals('3h ago'));
        
        final dailyIP = testIPControl.copyWith(
          lastSeen: now.subtract(const Duration(days: 2)),
        );
        expect(dailyIP.timeSinceLastSeen, equals('2d ago'));
        
        final veryRecentIP = testIPControl.copyWith(
          lastSeen: now.subtract(const Duration(seconds: 30)),
        );
        expect(veryRecentIP.timeSinceLastSeen, equals('Just now'));
      });
    });

    group('Display and Utility Methods', () {
      test('should get correct display names', () {
        expect(testIPControl.displayName, equals('***********/24')); // Uses range when available
        
        final noRangeIP = testIPControl.copyWith(ipRange: null);
        expect(noRangeIP.displayName, equals('***********00')); // Falls back to IP
      });

      test('should get effective address', () {
        expect(testIPControl.effectiveAddress, equals('***********/24'));
        
        final noRangeIP = testIPControl.copyWith(ipRange: null);
        expect(noRangeIP.effectiveAddress, equals('***********00'));
      });

      test('should check if rule applies to IP', () {
        expect(testIPControl.appliesToIP('************'), isTrue); // Simplified matching
        expect(testIPControl.appliesToIP('********'), isFalse);
        
        final exactIP = testIPControl.copyWith(ipRange: null);
        expect(exactIP.appliesToIP('***********00'), isTrue);
        expect(exactIP.appliesToIP('***********01'), isFalse);
      });

      test('should get location and ISP display', () {
        expect(testIPControl.locationDisplay, equals('San Francisco, California, United States'));
        expect(testIPControl.ispDisplay, equals('Cloudflare Inc.'));
        
        final noLocationIP = testIPControl.copyWith(geoLocation: null);
        expect(noLocationIP.locationDisplay, equals('Unknown Location'));
        expect(noLocationIP.ispDisplay, equals('Unknown ISP'));
      });
    });

    group('IP Type Detection', () {
      test('should identify private IP addresses', () {
        final privateIP1 = testIPControl.copyWith(ipAddress: '********');
        expect(privateIP1.isPrivateIP, isTrue);
        
        final privateIP2 = testIPControl.copyWith(ipAddress: '**********');
        expect(privateIP2.isPrivateIP, isTrue);
        
        final privateIP3 = testIPControl.copyWith(ipAddress: '***********');
        expect(privateIP3.isPrivateIP, isTrue);
        
        final localhostIP = testIPControl.copyWith(ipAddress: '127.0.0.1');
        expect(localhostIP.isPrivateIP, isTrue);
        
        final publicIP = testIPControl.copyWith(ipAddress: '*******');
        expect(publicIP.isPrivateIP, isFalse);
      });

      test('should identify cloud provider IPs', () {
        expect(testIPControl.isCloudProvider, isTrue); // Cloudflare in organization
        
        final amazonIP = testIPControl.copyWith(
          geoLocation: testGeoLocation.copyWith(
            organization: 'Amazon Web Services',
            isp: 'Amazon.com Inc.',
          ),
        );
        expect(amazonIP.isCloudProvider, isTrue);
        
        final googleIP = testIPControl.copyWith(
          geoLocation: testGeoLocation.copyWith(
            organization: 'Google LLC',
          ),
        );
        expect(googleIP.isCloudProvider, isTrue);
        
        final regularISP = testIPControl.copyWith(
          geoLocation: testGeoLocation.copyWith(
            organization: 'Regular ISP Corp',
            isp: 'Local Internet Provider',
          ),
        );
        expect(regularISP.isCloudProvider, isFalse);
        
        final noGeoLocation = testIPControl.copyWith(geoLocation: null);
        expect(noGeoLocation.isCloudProvider, isFalse);
      });
    });

    test('should serialize to JSON correctly', () {
      final json = testIPControl.toJson();
      expect(json['id'], equals('ip_control_123'));
      expect(json['organization_id'], equals('org_456'));
      expect(json['user_id'], equals('user_789'));
      expect(json['ip_address'], equals('***********00'));
      expect(json['ip_range'], equals('***********/24'));
      expect(json['access_type'], equals('whitelist'));
      expect(json['rule_type'], equals('organization'));
      expect(json['description'], equals('Office network whitelist'));
      expect(json['geo_location'], isA<Map<String, dynamic>>());
      expect(json['access_count'], equals(150));
      expect(json['is_suspicious'], isFalse);
      expect(json['risk_score'], equals(10));
      expect(json['is_active'], isTrue);
      expect(json['created_by'], equals('admin_123'));
    });

    test('should deserialize from JSON correctly', () {
      final json = testIPControl.toJson();
      final deserialized = IPAccessControl.fromJson(json);
      
      expect(deserialized.id, equals(testIPControl.id));
      expect(deserialized.organizationId, equals(testIPControl.organizationId));
      expect(deserialized.userId, equals(testIPControl.userId));
      expect(deserialized.ipAddress, equals(testIPControl.ipAddress));
      expect(deserialized.ipRange, equals(testIPControl.ipRange));
      expect(deserialized.accessType, equals(testIPControl.accessType));
      expect(deserialized.ruleType, equals(testIPControl.ruleType));
      expect(deserialized.description, equals(testIPControl.description));
      expect(deserialized.geoLocation, equals(testIPControl.geoLocation));
      expect(deserialized.accessCount, equals(testIPControl.accessCount));
      expect(deserialized.isSuspicious, equals(testIPControl.isSuspicious));
      expect(deserialized.riskScore, equals(testIPControl.riskScore));
      expect(deserialized.isActive, equals(testIPControl.isActive));
    });

    test('should handle JSON deserialization with invalid enum values', () {
      final json = testIPControl.toJson();
      json['access_type'] = 'invalid_access_type';
      json['rule_type'] = 'invalid_rule_type';
      
      final deserialized = IPAccessControl.fromJson(json);
      expect(deserialized.accessType, equals(IPAccessType.monitor)); // Default
      expect(deserialized.ruleType, equals(IPRuleType.organization)); // Default
    });

    test('should create copy with updated fields', () {
      final updated = testIPControl.copyWith(
        accessType: IPAccessType.blacklist,
        riskScore: 75,
        isSuspicious: true,
        description: 'Blocked suspicious IP',
        accessCount: 200,
      );
      
      expect(updated.accessType, equals(IPAccessType.blacklist));
      expect(updated.riskScore, equals(75));
      expect(updated.isSuspicious, isTrue);
      expect(updated.description, equals('Blocked suspicious IP'));
      expect(updated.accessCount, equals(200));
      expect(updated.id, equals(testIPControl.id)); // Unchanged
      expect(updated.ipAddress, equals(testIPControl.ipAddress)); // Unchanged
    });

    group('Factory Methods', () {
      test('should create whitelist rule correctly', () {
        final whitelistRule = IPAccessControl.createWhitelistRule(
          organizationId: 'org_123',
          ipAddress: '***********',
          ipRange: '***********/24',
          description: 'Office network',
          createdBy: 'admin_456',
        );
        
        expect(whitelistRule.organizationId, equals('org_123'));
        expect(whitelistRule.ipAddress, equals('***********'));
        expect(whitelistRule.ipRange, equals('***********/24'));
        expect(whitelistRule.accessType, equals(IPAccessType.whitelist));
        expect(whitelistRule.ruleType, equals(IPRuleType.organization));
        expect(whitelistRule.description, equals('Office network'));
        expect(whitelistRule.createdBy, equals('admin_456'));
        expect(whitelistRule.accessCount, equals(0));
        expect(whitelistRule.riskScore, equals(0));
        expect(whitelistRule.isSuspicious, isFalse);
        expect(whitelistRule.isActive, isTrue);
      });

      test('should create suspicious IP rule correctly', () {
        final suspiciousRule = IPAccessControl.createSuspiciousIP(
          organizationId: 'org_123',
          ipAddress: '***********',
          riskScore: 85,
          geoLocation: testGeoLocation,
          accessCount: 50,
        );
        
        expect(suspiciousRule.organizationId, equals('org_123'));
        expect(suspiciousRule.ipAddress, equals('***********'));
        expect(suspiciousRule.accessType, equals(IPAccessType.monitor));
        expect(suspiciousRule.ruleType, equals(IPRuleType.automatic));
        expect(suspiciousRule.description, equals('Automatically flagged as suspicious'));
        expect(suspiciousRule.geoLocation, equals(testGeoLocation));
        expect(suspiciousRule.accessCount, equals(50));
        expect(suspiciousRule.riskScore, equals(85));
        expect(suspiciousRule.isSuspicious, isTrue);
        expect(suspiciousRule.isActive, isTrue);
      });
    });

    test('should maintain equality for identical instances', () {
      final json = testIPControl.toJson();
      final identical = IPAccessControl.fromJson(json);
      expect(testIPControl, equals(identical));
    });

    test('should not be equal for different instances', () {
      final different = testIPControl.copyWith(id: 'different_id');
      expect(testIPControl, isNot(equals(different)));
    });

    group('Edge Cases', () {
      test('should handle null optional fields in JSON', () {
        final json = {
          'id': 'test_id',
          'organization_id': null,
          'user_id': null,
          'ip_address': '***********',
          'ip_range': null,
          'access_type': 'monitor',
          'rule_type': 'automatic',
          'description': null,
          'geo_location': null,
          'first_seen': '2025-01-15T10:00:00.000Z',
          'last_seen': '2025-01-15T10:00:00.000Z',
          'access_count': 1,
          'is_suspicious': false,
          'risk_score': 0,
          'is_active': true,
          'created_at': '2025-01-15T10:00:00.000Z',
          'updated_at': '2025-01-15T10:00:00.000Z',
          'created_by': null,
        };
        
        final ipControl = IPAccessControl.fromJson(json);
        expect(ipControl.organizationId, isNull);
        expect(ipControl.userId, isNull);
        expect(ipControl.ipRange, isNull);
        expect(ipControl.description, isNull);
        expect(ipControl.geoLocation, isNull);
        expect(ipControl.createdBy, isNull);
      });

      test('should handle extreme risk scores', () {
        final minRisk = testIPControl.copyWith(riskScore: 0);
        expect(minRisk.riskScore, equals(0));
        expect(minRisk.isLowRisk, isTrue);
        
        final maxRisk = testIPControl.copyWith(riskScore: 100);
        expect(maxRisk.riskScore, equals(100));
        expect(maxRisk.isHighRisk, isTrue);
      });

      test('should handle IPv6 addresses', () {
        final ipv6Control = testIPControl.copyWith(
          ipAddress: '2001:0db8:85a3:0000:0000:8a2e:0370:7334',
          ipRange: '2001:0db8:85a3::/48',
        );
        
        expect(ipv6Control.ipAddress, contains('2001:0db8'));
        expect(ipv6Control.displayName, equals('2001:0db8:85a3::/48'));
        expect(ipv6Control.effectiveAddress, equals('2001:0db8:85a3::/48'));
      });

      test('should handle very high access counts', () {
        final highTrafficIP = testIPControl.copyWith(
          accessCount: 1000000,
          firstSeen: DateTime.now().subtract(const Duration(days: 1)),
        );
        
        expect(highTrafficIP.accessCount, equals(1000000));
        expect(highTrafficIP.activityFrequency, equals(1000000.0)); // 1M accesses per day
      });

      test('should handle empty geo location fields', () {
        const emptyGeoLocation = IPGeoLocation();
        final ipWithEmptyGeo = testIPControl.copyWith(geoLocation: emptyGeoLocation);
        
        expect(ipWithEmptyGeo.locationDisplay, isEmpty);
        expect(ipWithEmptyGeo.geoLocation!.fullDisplayInfo, isEmpty);
      });
    });

    group('IPGeoLocation copyWith method', () {
      test('should create copy of IPGeoLocation with updated fields', () {
        final updated = testGeoLocation.copyWith(
          city: 'Los Angeles',
          isp: 'AT&T',
        );
        
        expect(updated.city, equals('Los Angeles'));
        expect(updated.isp, equals('AT&T'));
        expect(updated.country, equals(testGeoLocation.country)); // Unchanged
        expect(updated.organization, equals(testGeoLocation.organization)); // Unchanged
      });
    });
  });
}