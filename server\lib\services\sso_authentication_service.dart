/// SSO Authentication Service
/// 
/// Core service for handling Single Sign-On (SSO) authentication flows.
/// Supports SAML 2.0, OAuth 2.0, and OpenID Connect protocols.
/// 
/// Key Features:
/// - Multi-protocol SSO support (SAML, OAuth2, OIDC)
/// - Authentication flow orchestration
/// - Identity provider discovery
/// - User provisioning and mapping
/// - Session management integration
/// - Security validation and audit logging
library;

import 'dart:convert';
import '../services/database_service.dart';
import 'package:shared/shared.dart';

/// SSO authentication result
class SSOAuthenticationResult {
  final bool success;
  final String? userId;
  final String? sessionId;
  final Map<String, dynamic>? userAttributes;
  final String? errorMessage;
  final String? errorCode;
  final Map<String, dynamic> metadata;

  const SSOAuthenticationResult({
    required this.success,
    this.userId,
    this.sessionId,
    this.userAttributes,
    this.errorMessage,
    this.errorCode,
    this.metadata = const {},
  });

  factory SSOAuthenticationResult.success({
    required String userId,
    required String sessionId,
    Map<String, dynamic>? userAttributes,
    Map<String, dynamic> metadata = const {},
  }) {
    return SSOAuthenticationResult(
      success: true,
      userId: userId,
      sessionId: sessionId,
      userAttributes: userAttributes,
      metadata: metadata,
    );
  }

  factory SSOAuthenticationResult.failure({
    required String errorMessage,
    String? errorCode,
    Map<String, dynamic> metadata = const {},
  }) {
    return SSOAuthenticationResult(
      success: false,
      errorMessage: errorMessage,
      errorCode: errorCode,
      metadata: metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'user_id': userId,
      'session_id': sessionId,
      'user_attributes': userAttributes,
      'error_message': errorMessage,
      'error_code': errorCode,
      'metadata': metadata,
    };
  }
}

/// SSO authentication request
class SSOAuthenticationRequest {
  final String providerId;
  final String organizationId;
  final String? relayState;
  final Map<String, String> parameters;
  final String? ipAddress;
  final String? userAgent;
  final DateTime timestamp;

  const SSOAuthenticationRequest({
    required this.providerId,
    required this.organizationId,
    this.relayState,
    this.parameters = const {},
    this.ipAddress,
    this.userAgent,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'provider_id': providerId,
      'organization_id': organizationId,
      'relay_state': relayState,
      'parameters': parameters,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Main SSO Authentication Service
class SSOAuthenticationService {
  final DatabaseService _databaseService;
  
  // Cache for provider configurations
  final Map<String, SSOProvider> _providerCache = {};
  final Duration _cacheTimeout = const Duration(minutes: 15);
  final Map<String, DateTime> _cacheTimestamps = {};

  SSOAuthenticationService(this._databaseService);

  /// Initialize SSO authentication for a provider
  Future<Map<String, dynamic>> initiateAuthentication({
    required String organizationId,
    required String providerId,
    required String returnUrl,
    String? relayState,
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      // Get and validate provider configuration
      final provider = await _getProviderConfig(organizationId, providerId);
      if (provider == null) {
        throw SSOException('SSO provider not found or inactive', 'PROVIDER_NOT_FOUND');
      }

      // Generate authentication request
      final authRequest = await _generateAuthenticationRequest(
        provider: provider,
        returnUrl: returnUrl,
        relayState: relayState,
        ipAddress: ipAddress,
        userAgent: userAgent,
      );

      // Log authentication initiation
      await _logSSOEvent(
        organizationId: organizationId,
        providerId: providerId,
        eventType: 'authentication_initiated',
        details: {
          'return_url': returnUrl,
          'relay_state': relayState,
          'provider_type': provider.providerType.name,
        },
        ipAddress: ipAddress,
        userAgent: userAgent,
      );

      return authRequest;

    } catch (e) {
      await _logSSOEvent(
        organizationId: organizationId,
        providerId: providerId,
        eventType: 'authentication_init_failed',
        details: {
          'error': e.toString(),
          'return_url': returnUrl,
        },
        ipAddress: ipAddress,
        userAgent: userAgent,
        severity: 'error',
      );
      rethrow;
    }
  }

  /// Process SSO authentication response/callback
  Future<SSOAuthenticationResult> processAuthenticationResponse({
    required String organizationId,
    required String providerId,
    required Map<String, dynamic> responseData,
    String? ipAddress,
    String? userAgent,
  }) async {
    final startTime = DateTime.now();
    
    try {
      // Get provider configuration
      final provider = await _getProviderConfig(organizationId, providerId);
      if (provider == null) {
        return SSOAuthenticationResult.failure(
          errorMessage: 'SSO provider not found or inactive',
          errorCode: 'PROVIDER_NOT_FOUND',
        );
      }

      // Validate authentication response
      final validationResult = await _validateAuthenticationResponse(
        provider: provider,
        responseData: responseData,
      );

      if (!validationResult['valid']) {
        await _logSSOEvent(
          organizationId: organizationId,
          providerId: providerId,
          eventType: 'authentication_validation_failed',
          details: {
            'validation_error': validationResult['error'],
            'provider_type': provider.providerType.name,
          },
          ipAddress: ipAddress,
          userAgent: userAgent,
          severity: 'warning',
        );

        return SSOAuthenticationResult.failure(
          errorMessage: validationResult['error'] ?? 'Authentication validation failed',
          errorCode: 'VALIDATION_FAILED',
        );
      }

      // Extract user attributes from response
      final userAttributes = await _extractUserAttributes(
        provider: provider,
        responseData: responseData,
      );

      // Find or create user identity
      final userResult = await _resolveUserIdentity(
        provider: provider,
        userAttributes: userAttributes,
        organizationId: organizationId,
      );

      if (!userResult['success']) {
        return SSOAuthenticationResult.failure(
          errorMessage: userResult['error'] ?? 'User identity resolution failed',
          errorCode: 'USER_RESOLUTION_FAILED',
        );
      }

      final userId = userResult['user_id'] as String;

      // Create authenticated session
      final sessionResult = await _createAuthenticatedSession(
        userId: userId,
        organizationId: organizationId,
        providerId: providerId,
        userAttributes: userAttributes,
        ipAddress: ipAddress,
        userAgent: userAgent,
      );

      if (!sessionResult['success']) {
        return SSOAuthenticationResult.failure(
          errorMessage: sessionResult['error'] ?? 'Session creation failed',
          errorCode: 'SESSION_CREATION_FAILED',
        );
      }

      final sessionId = sessionResult['session_id'] as String;

      // Update provider statistics
      await _updateProviderStatistics(providerId, successful: true);

      // Log successful authentication
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      await _logSSOEvent(
        organizationId: organizationId,
        providerId: providerId,
        eventType: 'authentication_successful',
        details: {
          'user_id': userId,
          'session_id': sessionId,
          'provider_type': provider.providerType.name,
          'duration_ms': duration,
          'user_attributes_count': userAttributes.length,
        },
        ipAddress: ipAddress,
        userAgent: userAgent,
        severity: 'info',
      );

      return SSOAuthenticationResult.success(
        userId: userId,
        sessionId: sessionId,
        userAttributes: userAttributes,
        metadata: {
          'provider_type': provider.providerType.name,
          'duration_ms': duration,
          'authenticated_at': DateTime.now().toIso8601String(),
        },
      );

    } catch (e) {
      await _updateProviderStatistics(providerId, successful: false);
      
      await _logSSOEvent(
        organizationId: organizationId,
        providerId: providerId,
        eventType: 'authentication_failed',
        details: {
          'error': e.toString(),
          'duration_ms': DateTime.now().difference(startTime).inMilliseconds,
        },
        ipAddress: ipAddress,
        userAgent: userAgent,
        severity: 'error',
      );

      return SSOAuthenticationResult.failure(
        errorMessage: e.toString(),
        errorCode: 'AUTHENTICATION_ERROR',
      );
    }
  }

  /// Get SSO provider configuration with caching
  Future<SSOProvider?> _getProviderConfig(String organizationId, String providerId) async {
    final cacheKey = '$organizationId:$providerId';
    
    // Check cache first
    if (_providerCache.containsKey(cacheKey)) {
      final cacheTime = _cacheTimestamps[cacheKey];
      if (cacheTime != null && DateTime.now().difference(cacheTime) < _cacheTimeout) {
        return _providerCache[cacheKey];
      }
    }

    // Fetch from database
    final providers = await _databaseService.getSSOProviders(organizationId, activeOnly: true);
    final provider = providers.cast<Map<String, dynamic>?>().firstWhere(
      (p) => p != null && p['id'] == providerId,
      orElse: () => null,
    );

    if (provider == null) {
      return null;
    }

    final providerConfig = SSOProvider.fromJson(provider);
    
    // Update cache
    _providerCache[cacheKey] = providerConfig;
    _cacheTimestamps[cacheKey] = DateTime.now();

    return providerConfig;
  }

  /// Generate authentication request based on provider type
  Future<Map<String, dynamic>> _generateAuthenticationRequest({
    required SSOProvider provider,
    required String returnUrl,
    String? relayState,
    String? ipAddress,
    String? userAgent,
  }) async {
    final requestId = _generateRequestId();
    // Timestamp available for future logging: DateTime.now().toIso8601String()

    switch (provider.providerType) {
      case SSOProviderType.saml:
        return await _generateSAMLRequest(
          provider: provider,
          returnUrl: returnUrl,
          requestId: requestId,
          relayState: relayState,
        );
      
      case SSOProviderType.oauth2:
        return await _generateOAuth2Request(
          provider: provider,
          returnUrl: returnUrl,
          requestId: requestId,
          relayState: relayState,
        );
      
      case SSOProviderType.oidc:
        return await _generateOIDCRequest(
          provider: provider,
          returnUrl: returnUrl,
          requestId: requestId,
          relayState: relayState,
        );
    }
  }

  /// Generate SAML authentication request
  Future<Map<String, dynamic>> _generateSAMLRequest({
    required SSOProvider provider,
    required String returnUrl,
    required String requestId,
    String? relayState,
  }) async {
    final config = provider.providerConfig;
    final entityId = config['entity_id'] as String? ?? provider.entityId;
    final ssoUrl = config['sso_url'] as String? ?? config['metadata_url'];

    if (ssoUrl == null) {
      throw SSOException('SAML SSO URL not configured', 'MISSING_SSO_URL');
    }

    // Generate SAML AuthnRequest
    final samlRequest = _buildSAMLAuthnRequest(
      entityId: entityId ?? 'quester-${provider.organizationId}',
      assertionConsumerServiceUrl: returnUrl,
      requestId: requestId,
      destination: ssoUrl,
    );

    return {
      'type': 'saml_redirect',
      'url': ssoUrl,
      'request_id': requestId,
      'saml_request': base64Encode(utf8.encode(samlRequest)),
      'relay_state': relayState,
      'method': 'GET',
    };
  }

  /// Generate OAuth 2.0 authentication request
  Future<Map<String, dynamic>> _generateOAuth2Request({
    required SSOProvider provider,
    required String returnUrl,
    required String requestId,
    String? relayState,
  }) async {
    final config = provider.providerConfig;
    final clientId = config['client_id'] as String?;
    final authorizationUrl = config['authorization_url'] as String?;
    final scope = config['scope'] as String? ?? 'openid profile email';

    if (clientId == null || authorizationUrl == null) {
      throw SSOException('OAuth2 configuration incomplete', 'INCOMPLETE_OAUTH2_CONFIG');
    }

    final state = _generateStateParameter(requestId, relayState);

    final authUrl = Uri.parse(authorizationUrl).replace(queryParameters: {
      'response_type': 'code',
      'client_id': clientId,
      'redirect_uri': returnUrl,
      'scope': scope,
      'state': state,
      'nonce': _generateNonce(),
    });

    return {
      'type': 'oauth2_redirect',
      'url': authUrl.toString(),
      'request_id': requestId,
      'state': state,
      'method': 'GET',
    };
  }

  /// Generate OpenID Connect authentication request
  Future<Map<String, dynamic>> _generateOIDCRequest({
    required SSOProvider provider,
    required String returnUrl,
    required String requestId,
    String? relayState,
  }) async {
    // OIDC is built on OAuth 2.0, so we use similar flow
    return await _generateOAuth2Request(
      provider: provider,
      returnUrl: returnUrl,
      requestId: requestId,
      relayState: relayState,
    );
  }

  /// Validate authentication response
  Future<Map<String, dynamic>> _validateAuthenticationResponse({
    required SSOProvider provider,
    required Map<String, dynamic> responseData,
  }) async {
    try {
      switch (provider.providerType) {
        case SSOProviderType.saml:
          return await _validateSAMLResponse(provider, responseData);
        case SSOProviderType.oauth2:
        case SSOProviderType.oidc:
          return await _validateOAuthResponse(provider, responseData);
      }
    } catch (e) {
      return {
        'valid': false,
        'error': e.toString(),
      };
    }
  }

  /// Extract user attributes from authentication response
  Future<Map<String, dynamic>> _extractUserAttributes({
    required SSOProvider provider,
    required Map<String, dynamic> responseData,
  }) async {
    switch (provider.providerType) {
      case SSOProviderType.saml:
        return _extractSAMLAttributes(provider, responseData);
      case SSOProviderType.oauth2:
      case SSOProviderType.oidc:
        return await _extractOAuthAttributes(provider, responseData);
    }
  }

  /// Resolve user identity (find existing or create new user)
  Future<Map<String, dynamic>> _resolveUserIdentity({
    required SSOProvider provider,
    required Map<String, dynamic> userAttributes,
    required String organizationId,
  }) async {
    try {
      final externalId = _extractExternalId(userAttributes, provider);
      if (externalId == null) {
        return {
          'success': false,
          'error': 'External ID not found in user attributes',
        };
      }

      // Check for existing SSO identity
      final existingIdentity = await _databaseService.getUserSSOIdentity(
        provider.id,
        externalId,
      );

      if (existingIdentity != null) {
        // Update existing identity attributes
        await _databaseService.updateUserSSOIdentity(existingIdentity['id'], {
          'attributes': userAttributes,
          'last_login_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });

        return {
          'success': true,
          'user_id': existingIdentity['user_id'],
          'identity_id': existingIdentity['id'],
          'is_new_user': false,
        };
      }

      // Create new user and identity
      final email = userAttributes['email'] as String? ?? 
                   userAttributes['mail'] as String? ??
                   '$externalId@${provider.providerName.toLowerCase().replaceAll(' ', '.')}.local';

      final name = userAttributes['displayName'] as String? ??
                  userAttributes['name'] as String? ??
                  '${userAttributes['givenName'] ?? ''} ${userAttributes['surname'] ?? ''}'.trim();

      // Create user
      final userId = await _databaseService.createUser({
        'email': email,
        'name': name.isNotEmpty ? name : email.split('@')[0],
        'organization_id': organizationId,
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'sso_provider_id': provider.id,
        'sso_external_id': externalId,
      });

      // Create SSO identity mapping
      final identityId = await _databaseService.createUserSSOIdentity({
        'user_id': userId,
        'sso_provider_id': provider.id,
        'external_id': externalId,
        'attributes': userAttributes,
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
      });

      return {
        'success': true,
        'user_id': userId,
        'identity_id': identityId,
        'is_new_user': true,
      };

    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Create authenticated session
  Future<Map<String, dynamic>> _createAuthenticatedSession({
    required String userId,
    required String organizationId,
    required String providerId,
    required Map<String, dynamic> userAttributes,
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      final sessionId = _generateSessionId();
      final expiresAt = DateTime.now().add(const Duration(hours: 8));

      await _databaseService.createSession({
        'id': sessionId,
        'user_id': userId,
        'session_token': _generateSessionToken(),
        'expires_at': expiresAt.toIso8601String(),
        'ip_address': ipAddress,
        'user_agent': userAgent,
        'is_active': true,
        'sso_provider_id': providerId,
        'created_at': DateTime.now().toIso8601String(),
      });

      // Update user last login
      await _databaseService.updateUser(userId, {
        'last_login_at': DateTime.now().toIso8601String(),
        'login_count': 'COALESCE(login_count, 0) + 1',
        'failed_login_attempts': 0,
      });

      return {
        'success': true,
        'session_id': sessionId,
      };

    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Log SSO events for audit and monitoring
  Future<void> _logSSOEvent({
    required String organizationId,
    required String providerId,
    required String eventType,
    required Map<String, dynamic> details,
    String? ipAddress,
    String? userAgent,
    String severity = 'info',
  }) async {
    try {
      await _databaseService.createSecurityAuditLog({
        'organization_id': organizationId,
        'event_type': 'sso',
        'event_category': eventType,
        'event_description': 'SSO $eventType for provider $providerId',
        'event_severity': severity,
        'details': details,
        'ip_address': ipAddress,
        'user_agent': userAgent,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Don't fail authentication due to logging issues
      print('Failed to log SSO event: $e');
    }
  }

  /// Update provider usage statistics
  Future<void> _updateProviderStatistics(String providerId, {required bool successful}) async {
    try {
      // This would update provider statistics in the database
      // For now, we'll log it for monitoring
      print('SSO Provider $providerId: ${successful ? 'success' : 'failure'}');
    } catch (e) {
      print('Failed to update provider statistics: $e');
    }
  }

  // Helper methods for generating IDs and tokens
  String _generateRequestId() {
    return 'req_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(8)}';
  }

  String _generateSessionId() {
    return 'sess_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(16)}';
  }

  String _generateSessionToken() {
    return _generateRandomString(64);
  }

  String _generateStateParameter(String requestId, String? relayState) {
    final stateData = {
      'request_id': requestId,
      'relay_state': relayState,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    return base64Encode(utf8.encode(json.encode(stateData)));
  }

  String _generateNonce() {
    return _generateRandomString(32);
  }

  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(length, (index) => chars[random % chars.length]).join();
  }

  /// Extract external ID from user attributes
  String? _extractExternalId(Map<String, dynamic> attributes, SSOProvider provider) {
    // Try common attribute names for external ID
    final candidates = [
      'sub', 'id', 'user_id', 'userId', 'uid', 'nameID', 
      'email', 'username', 'preferred_username'
    ];
    
    for (final candidate in candidates) {
      if (attributes.containsKey(candidate)) {
        return attributes[candidate]?.toString();
      }
    }
    
    return null;
  }

  // Protocol-specific methods (implementations would be expanded)
  String _buildSAMLAuthnRequest({
    required String entityId,
    required String assertionConsumerServiceUrl,
    required String requestId,
    required String destination,
  }) {
    // Simplified SAML AuthnRequest template
    return '''<?xml version="1.0" encoding="UTF-8"?>
<samlp:AuthnRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                    xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                    ID="$requestId"
                    Version="2.0"
                    IssueInstant="${DateTime.now().toUtc().toIso8601String()}"
                    Destination="$destination"
                    AssertionConsumerServiceURL="$assertionConsumerServiceUrl"
                    ProtocolBinding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST">
  <saml:Issuer>$entityId</saml:Issuer>
</samlp:AuthnRequest>''';
  }

  Future<Map<String, dynamic>> _validateSAMLResponse(
    SSOProvider provider, 
    Map<String, dynamic> responseData
  ) async {
    // Simplified SAML response validation
    // In production, this would include signature verification, 
    // timestamp validation, etc.
    return {'valid': true};
  }

  Future<Map<String, dynamic>> _validateOAuthResponse(
    SSOProvider provider, 
    Map<String, dynamic> responseData
  ) async {
    // Simplified OAuth response validation
    // In production, this would validate the authorization code,
    // exchange it for tokens, etc.
    return {'valid': true};
  }

  Map<String, dynamic> _extractSAMLAttributes(
    SSOProvider provider, 
    Map<String, dynamic> responseData
  ) {
    // Extract attributes from SAML assertion
    // This is a simplified implementation
    return responseData['attributes'] ?? {};
  }

  Future<Map<String, dynamic>> _extractOAuthAttributes(
    SSOProvider provider, 
    Map<String, dynamic> responseData
  ) async {
    // Extract attributes from OAuth/OIDC tokens
    // This would typically involve calling the userinfo endpoint
    return responseData['user_attributes'] ?? {};
  }
}

/// SSO Exception class
class SSOException implements Exception {
  final String message;
  final String code;

  SSOException(this.message, this.code);

  @override
  String toString() => 'SSOException($code): $message';
}