# Staging environment configuration for <PERSON><PERSON>
# Includes monitoring and logging

services:
  server:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  client:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  nginx:
    image: nginx:latest
    platform: linux/amd64
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    ports:
      - "${NGINX_EXTERNAL_PORT:-80}:80"
    depends_on:
      - server
      - client
    networks:
      - quester-network

volumes:
  prometheus_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-staging}-prometheus-data
  grafana_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-staging}-grafana-data
