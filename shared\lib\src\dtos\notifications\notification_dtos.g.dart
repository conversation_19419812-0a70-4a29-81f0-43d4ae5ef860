// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_dtos.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateNotificationRequest _$CreateNotificationRequestFromJson(
  Map<String, dynamic> json,
) => CreateNotificationRequest(
  userId: json['userId'] as String,
  type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
  title: json['title'] as String,
  body: json['body'] as String,
  actionText: json['actionText'] as String?,
  actionUrl: json['actionUrl'] as String?,
  priority: $enumDecode(_$NotificationPriorityEnumMap, json['priority']),
  deliveryMethods: (json['deliveryMethods'] as List<dynamic>)
      .map((e) => $enumDecode(_$DeliveryMethodEnumMap, e))
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  relatedEntityId: json['relatedEntityId'] as String?,
  relatedEntityType: json['relatedEntityType'] as String?,
  icon: json['icon'] as String?,
  sound: json['sound'] as String?,
  vibrationPattern: (json['vibrationPattern'] as List<dynamic>?)
      ?.map((e) => (e as num).toInt())
      .toList(),
  showBadge: json['showBadge'] as bool? ?? true,
  channelId: json['channelId'] as String?,
  groupId: json['groupId'] as String?,
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  scheduledFor: json['scheduledFor'] == null
      ? null
      : DateTime.parse(json['scheduledFor'] as String),
);

Map<String, dynamic> _$CreateNotificationRequestToJson(
  CreateNotificationRequest instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'type': _$NotificationTypeEnumMap[instance.type]!,
  'title': instance.title,
  'body': instance.body,
  'actionText': instance.actionText,
  'actionUrl': instance.actionUrl,
  'priority': _$NotificationPriorityEnumMap[instance.priority]!,
  'deliveryMethods': instance.deliveryMethods
      .map((e) => _$DeliveryMethodEnumMap[e]!)
      .toList(),
  'metadata': instance.metadata,
  'relatedEntityId': instance.relatedEntityId,
  'relatedEntityType': instance.relatedEntityType,
  'icon': instance.icon,
  'sound': instance.sound,
  'vibrationPattern': instance.vibrationPattern,
  'showBadge': instance.showBadge,
  'channelId': instance.channelId,
  'groupId': instance.groupId,
  'expiresAt': instance.expiresAt?.toIso8601String(),
  'scheduledFor': instance.scheduledFor?.toIso8601String(),
};

const _$NotificationTypeEnumMap = {
  NotificationType.questReminder: 'quest_reminder',
  NotificationType.taskReminder: 'task_reminder',
  NotificationType.deadlineAlert: 'deadline_alert',
  NotificationType.achievementUnlocked: 'achievement_unlocked',
  NotificationType.pointsEarned: 'points_earned',
  NotificationType.levelUp: 'level_up',
  NotificationType.messageReceived: 'message_received',
  NotificationType.collaborationInvite: 'collaboration_invite',
  NotificationType.questInvite: 'quest_invite',
  NotificationType.teamInvite: 'team_invite',
  NotificationType.systemAlert: 'system_alert',
  NotificationType.maintenanceNotice: 'maintenance_notice',
  NotificationType.updateAvailable: 'update_available',
  NotificationType.streakReminder: 'streak_reminder',
  NotificationType.leaderboardUpdate: 'leaderboard_update',
  NotificationType.rewardAvailable: 'reward_available',
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'low',
  NotificationPriority.normal: 'normal',
  NotificationPriority.high: 'high',
  NotificationPriority.urgent: 'urgent',
};

const _$DeliveryMethodEnumMap = {
  DeliveryMethod.inApp: 'in_app',
  DeliveryMethod.push: 'push',
  DeliveryMethod.email: 'email',
  DeliveryMethod.sms: 'sms',
  DeliveryMethod.webhook: 'webhook',
};

CreateNotificationFromTemplateRequest
_$CreateNotificationFromTemplateRequestFromJson(Map<String, dynamic> json) =>
    CreateNotificationFromTemplateRequest(
      templateId: json['templateId'] as String,
      userId: json['userId'] as String,
      variables: json['variables'] as Map<String, dynamic>,
      language: json['language'] as String?,
      scheduledFor: json['scheduledFor'] == null
          ? null
          : DateTime.parse(json['scheduledFor'] as String),
      groupId: json['groupId'] as String?,
      deliveryMethods: (json['deliveryMethods'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$DeliveryMethodEnumMap, e))
          .toList(),
    );

Map<String, dynamic> _$CreateNotificationFromTemplateRequestToJson(
  CreateNotificationFromTemplateRequest instance,
) => <String, dynamic>{
  'templateId': instance.templateId,
  'userId': instance.userId,
  'variables': instance.variables,
  'language': instance.language,
  'scheduledFor': instance.scheduledFor?.toIso8601String(),
  'groupId': instance.groupId,
  'deliveryMethods': instance.deliveryMethods
      ?.map((e) => _$DeliveryMethodEnumMap[e]!)
      .toList(),
};

UpdateNotificationPreferencesRequest
_$UpdateNotificationPreferencesRequestFromJson(Map<String, dynamic> json) =>
    UpdateNotificationPreferencesRequest(
      enableNotifications: json['enableNotifications'] as bool?,
      enablePushNotifications: json['enablePushNotifications'] as bool?,
      enableEmailNotifications: json['enableEmailNotifications'] as bool?,
      enableSmsNotifications: json['enableSmsNotifications'] as bool?,
      enableInAppNotifications: json['enableInAppNotifications'] as bool?,
      quietHours: json['quietHours'] == null
          ? null
          : QuietHours.fromJson(json['quietHours'] as Map<String, dynamic>),
      categoryPreferences:
          (json['categoryPreferences'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
              $enumDecode(_$NotificationCategoryEnumMap, k),
              CategoryPreferences.fromJson(e as Map<String, dynamic>),
            ),
          ),
      deviceSettings: json['deviceSettings'] == null
          ? null
          : DeviceNotificationSettings.fromJson(
              json['deviceSettings'] as Map<String, dynamic>,
            ),
      frequency: $enumDecodeNullable(
        _$NotificationFrequencyEnumMap,
        json['frequency'],
      ),
      language: json['language'] as String?,
      timezone: json['timezone'] as String?,
    );

Map<String, dynamic> _$UpdateNotificationPreferencesRequestToJson(
  UpdateNotificationPreferencesRequest instance,
) => <String, dynamic>{
  'enableNotifications': instance.enableNotifications,
  'enablePushNotifications': instance.enablePushNotifications,
  'enableEmailNotifications': instance.enableEmailNotifications,
  'enableSmsNotifications': instance.enableSmsNotifications,
  'enableInAppNotifications': instance.enableInAppNotifications,
  'quietHours': instance.quietHours,
  'categoryPreferences': instance.categoryPreferences?.map(
    (k, e) => MapEntry(_$NotificationCategoryEnumMap[k]!, e),
  ),
  'deviceSettings': instance.deviceSettings,
  'frequency': _$NotificationFrequencyEnumMap[instance.frequency],
  'language': instance.language,
  'timezone': instance.timezone,
};

const _$NotificationCategoryEnumMap = {
  NotificationCategory.system: 'system',
  NotificationCategory.quest: 'quest',
  NotificationCategory.achievement: 'achievement',
  NotificationCategory.social: 'social',
  NotificationCategory.reminder: 'reminder',
  NotificationCategory.marketing: 'marketing',
  NotificationCategory.security: 'security',
  NotificationCategory.team: 'team',
};

const _$NotificationFrequencyEnumMap = {
  NotificationFrequency.immediate: 'immediate',
  NotificationFrequency.every15Minutes: 'every15Minutes',
  NotificationFrequency.hourly: 'hourly',
  NotificationFrequency.daily: 'daily',
  NotificationFrequency.weekly: 'weekly',
  NotificationFrequency.never: 'never',
};

MarkNotificationsReadRequest _$MarkNotificationsReadRequestFromJson(
  Map<String, dynamic> json,
) => MarkNotificationsReadRequest(
  notificationIds: (json['notificationIds'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$MarkNotificationsReadRequestToJson(
  MarkNotificationsReadRequest instance,
) => <String, dynamic>{'notificationIds': instance.notificationIds};

GetNotificationsRequest _$GetNotificationsRequestFromJson(
  Map<String, dynamic> json,
) => GetNotificationsRequest(
  status: $enumDecodeNullable(_$NotificationStatusEnumMap, json['status']),
  type: $enumDecodeNullable(_$NotificationTypeEnumMap, json['type']),
  category: $enumDecodeNullable(
    _$NotificationCategoryEnumMap,
    json['category'],
  ),
  priority: $enumDecodeNullable(
    _$NotificationPriorityEnumMap,
    json['priority'],
  ),
  isRead: json['isRead'] as bool?,
  startDate: json['startDate'] == null
      ? null
      : DateTime.parse(json['startDate'] as String),
  endDate: json['endDate'] == null
      ? null
      : DateTime.parse(json['endDate'] as String),
  page: (json['page'] as num?)?.toInt() ?? 1,
  limit: (json['limit'] as num?)?.toInt() ?? 20,
  sortBy: json['sortBy'] as String?,
  sortDirection: $enumDecodeNullable(
    _$SortDirectionEnumMap,
    json['sortDirection'],
  ),
);

Map<String, dynamic> _$GetNotificationsRequestToJson(
  GetNotificationsRequest instance,
) => <String, dynamic>{
  'status': _$NotificationStatusEnumMap[instance.status],
  'type': _$NotificationTypeEnumMap[instance.type],
  'category': _$NotificationCategoryEnumMap[instance.category],
  'priority': _$NotificationPriorityEnumMap[instance.priority],
  'isRead': instance.isRead,
  'startDate': instance.startDate?.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'page': instance.page,
  'limit': instance.limit,
  'sortBy': instance.sortBy,
  'sortDirection': _$SortDirectionEnumMap[instance.sortDirection],
};

const _$NotificationStatusEnumMap = {
  NotificationStatus.pending: 'pending',
  NotificationStatus.scheduled: 'scheduled',
  NotificationStatus.sent: 'sent',
  NotificationStatus.delivered: 'delivered',
  NotificationStatus.read: 'read',
  NotificationStatus.failed: 'failed',
  NotificationStatus.expired: 'expired',
  NotificationStatus.cancelled: 'cancelled',
};

const _$SortDirectionEnumMap = {
  SortDirection.asc: 'asc',
  SortDirection.desc: 'desc',
};

GetNotificationsResponse _$GetNotificationsResponseFromJson(
  Map<String, dynamic> json,
) => GetNotificationsResponse(
  notifications: (json['notifications'] as List<dynamic>)
      .map((e) => Notification.fromJson(e as Map<String, dynamic>))
      .toList(),
  pagination: PaginationInfo.fromJson(
    json['pagination'] as Map<String, dynamic>,
  ),
  unreadCount: (json['unreadCount'] as num).toInt(),
);

Map<String, dynamic> _$GetNotificationsResponseToJson(
  GetNotificationsResponse instance,
) => <String, dynamic>{
  'notifications': instance.notifications,
  'pagination': instance.pagination,
  'unreadCount': instance.unreadCount,
};

NotificationStatsResponse _$NotificationStatsResponseFromJson(
  Map<String, dynamic> json,
) => NotificationStatsResponse(
  totalCount: (json['totalCount'] as num).toInt(),
  unreadCount: (json['unreadCount'] as num).toInt(),
  countByCategory: (json['countByCategory'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(
      $enumDecode(_$NotificationCategoryEnumMap, k),
      (e as num).toInt(),
    ),
  ),
  countByPriority: (json['countByPriority'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(
      $enumDecode(_$NotificationPriorityEnumMap, k),
      (e as num).toInt(),
    ),
  ),
  countByStatus: (json['countByStatus'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(
      $enumDecode(_$NotificationStatusEnumMap, k),
      (e as num).toInt(),
    ),
  ),
  recentActivity: (json['recentActivity'] as List<dynamic>)
      .map((e) => DailyNotificationCount.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$NotificationStatsResponseToJson(
  NotificationStatsResponse instance,
) => <String, dynamic>{
  'totalCount': instance.totalCount,
  'unreadCount': instance.unreadCount,
  'countByCategory': instance.countByCategory.map(
    (k, e) => MapEntry(_$NotificationCategoryEnumMap[k]!, e),
  ),
  'countByPriority': instance.countByPriority.map(
    (k, e) => MapEntry(_$NotificationPriorityEnumMap[k]!, e),
  ),
  'countByStatus': instance.countByStatus.map(
    (k, e) => MapEntry(_$NotificationStatusEnumMap[k]!, e),
  ),
  'recentActivity': instance.recentActivity,
};

DailyNotificationCount _$DailyNotificationCountFromJson(
  Map<String, dynamic> json,
) => DailyNotificationCount(
  date: DateTime.parse(json['date'] as String),
  count: (json['count'] as num).toInt(),
  readCount: (json['readCount'] as num).toInt(),
);

Map<String, dynamic> _$DailyNotificationCountToJson(
  DailyNotificationCount instance,
) => <String, dynamic>{
  'date': instance.date.toIso8601String(),
  'count': instance.count,
  'readCount': instance.readCount,
};

RegisterDeviceRequest _$RegisterDeviceRequestFromJson(
  Map<String, dynamic> json,
) => RegisterDeviceRequest(
  pushToken: json['pushToken'] as String,
  platform: json['platform'] as String,
  appVersion: json['appVersion'] as String,
  timezone: json['timezone'] as String,
  language: json['language'] as String,
  capabilities: DeviceCapabilities.fromJson(
    json['capabilities'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$RegisterDeviceRequestToJson(
  RegisterDeviceRequest instance,
) => <String, dynamic>{
  'pushToken': instance.pushToken,
  'platform': instance.platform,
  'appVersion': instance.appVersion,
  'timezone': instance.timezone,
  'language': instance.language,
  'capabilities': instance.capabilities,
};

DeviceCapabilities _$DeviceCapabilitiesFromJson(Map<String, dynamic> json) =>
    DeviceCapabilities(
      supportsRichNotifications:
          json['supportsRichNotifications'] as bool? ?? false,
      supportsActions: json['supportsActions'] as bool? ?? false,
      maxActions: (json['maxActions'] as num?)?.toInt() ?? 2,
      supportsCustomSounds: json['supportsCustomSounds'] as bool? ?? false,
      supportsVibration: json['supportsVibration'] as bool? ?? false,
      supportsGrouping: json['supportsGrouping'] as bool? ?? false,
    );

Map<String, dynamic> _$DeviceCapabilitiesToJson(DeviceCapabilities instance) =>
    <String, dynamic>{
      'supportsRichNotifications': instance.supportsRichNotifications,
      'supportsActions': instance.supportsActions,
      'maxActions': instance.maxActions,
      'supportsCustomSounds': instance.supportsCustomSounds,
      'supportsVibration': instance.supportsVibration,
      'supportsGrouping': instance.supportsGrouping,
    };
