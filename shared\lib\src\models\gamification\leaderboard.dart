import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'leaderboard.g.dart';

/// Leaderboard categories from CLAUDE.md multi-category leaderboards
enum LeaderboardCategory {
  @JsonValue('total_points')
  totalPoints,
  @JsonValue('monthly_points')
  monthlyPoints,
  @JsonValue('weekly_points')
  weeklyPoints,
  @JsonValue('daily_points')
  dailyPoints,
  @JsonValue('quests_completed')
  questsCompleted,
  @JsonValue('tasks_completed')
  tasksCompleted,
  @JsonValue('current_streak')
  currentStreak,
  @JsonValue('longest_streak')
  longestStreak,
  @JsonValue('achievements_count')
  achievementsCount,
  @JsonValue('collaboration_score')
  collaborationScore,
  @JsonValue('efficiency_rating')
  efficiencyRating,
}

/// Leaderboard time periods
enum LeaderboardPeriod {
  @JsonValue('all_time')
  allTime,
  @JsonValue('yearly')
  yearly,
  @JsonValue('monthly')
  monthly,
  @JsonValue('weekly')
  weekly,
  @JsonValue('daily')
  daily,
}

/// Individual leaderboard entry
@JsonSerializable()
class LeaderboardEntry extends Equatable {
  /// User ID
  final String userId;

  /// User display name
  final String displayName;

  /// User avatar URL
  final String? avatarUrl;

  /// User's current role
  final String role;

  /// Current rank position (1-based)
  final int rank;

  /// Previous rank position (for rank change calculation)
  final int? previousRank;

  /// Score for this leaderboard category
  final double score;

  /// Previous score (for change calculation)
  final double? previousScore;

  /// Additional stats relevant to this category
  final Map<String, dynamic>? stats;

  /// Last update timestamp
  final DateTime lastUpdated;

  const LeaderboardEntry({
    required this.userId,
    required this.displayName,
    this.avatarUrl,
    required this.role,
    required this.rank,
    this.previousRank,
    required this.score,
    this.previousScore,
    this.stats,
    required this.lastUpdated,
  });

  /// Create LeaderboardEntry from JSON
  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) => _$LeaderboardEntryFromJson(json);

  /// Convert LeaderboardEntry to JSON
  Map<String, dynamic> toJson() => _$LeaderboardEntryToJson(this);

  /// Get rank change (positive = moved up, negative = moved down, 0 = no change)
  int? get rankChange {
    if (previousRank == null) return null;
    return previousRank! - rank; // Rank 1 is better than rank 2
  }

  /// Get score change
  double? get scoreChange {
    if (previousScore == null) return null;
    return score - previousScore!;
  }

  /// Check if rank improved
  bool get rankImproved => rankChange != null && rankChange! > 0;

  /// Check if rank declined
  bool get rankDeclined => rankChange != null && rankChange! < 0;

  /// Get rank change description
  String get rankChangeDescription {
    final change = rankChange;
    if (change == null) return 'New';
    if (change > 0) return '+$change';
    if (change < 0) return '$change';
    return 'Same';
  }

  @override
  List<Object?> get props => [
        userId,
        displayName,
        avatarUrl,
        role,
        rank,
        previousRank,
        score,
        previousScore,
        stats,
        lastUpdated,
      ];

  @override
  bool get stringify => true;
}

/// Multi-category leaderboard model with real-time updates
@JsonSerializable()
class Leaderboard extends Equatable {
  /// Unique leaderboard identifier
  final String id;

  /// Leaderboard category
  final LeaderboardCategory category;

  /// Time period for this leaderboard
  final LeaderboardPeriod period;

  /// Leaderboard title
  final String title;

  /// Leaderboard description
  final String description;

  /// Maximum number of entries to display
  final int maxEntries;

  /// Ordered list of leaderboard entries
  final List<LeaderboardEntry> entries;

  /// Total number of participants (may be more than entries shown)
  final int totalParticipants;

  /// Leaderboard metadata
  final Map<String, dynamic>? metadata;

  /// Whether this leaderboard updates in real-time
  final bool isRealTime;

  /// Update frequency in minutes (for non-real-time boards)
  final int? updateFrequencyMinutes;

  /// Last calculation/update timestamp
  final DateTime lastCalculated;

  /// Leaderboard creation timestamp
  final DateTime createdAt;

  /// Next scheduled update (for periodic boards)
  final DateTime? nextUpdate;

  const Leaderboard({
    required this.id,
    required this.category,
    required this.period,
    required this.title,
    required this.description,
    required this.maxEntries,
    required this.entries,
    required this.totalParticipants,
    this.metadata,
    required this.isRealTime,
    this.updateFrequencyMinutes,
    required this.lastCalculated,
    required this.createdAt,
    this.nextUpdate,
  });

  /// Create Leaderboard from JSON
  factory Leaderboard.fromJson(Map<String, dynamic> json) => _$LeaderboardFromJson(json);

  /// Convert Leaderboard to JSON
  Map<String, dynamic> toJson() => _$LeaderboardToJson(this);

  /// Get top N entries
  List<LeaderboardEntry> getTopEntries(int count) {
    return entries.take(count).toList();
  }

  /// Get user's position by user ID
  LeaderboardEntry? getUserEntry(String userId) {
    return entries.where((entry) => entry.userId == userId).firstOrNull;
  }

  /// Get user's rank by user ID
  int? getUserRank(String userId) {
    final entry = getUserEntry(userId);
    return entry?.rank;
  }

  /// Check if user is in top N
  bool isUserInTopN(String userId, int n) {
    final rank = getUserRank(userId);
    return rank != null && rank <= n;
  }

  /// Get entries around a specific rank (context view)
  List<LeaderboardEntry> getEntriesAroundRank(int rank, {int contextSize = 2}) {
    final startIndex = (rank - contextSize - 1).clamp(0, entries.length);
    final endIndex = (rank + contextSize).clamp(0, entries.length);
    return entries.sublist(startIndex, endIndex);
  }

  /// Get entries around a specific user (context view)
  List<LeaderboardEntry> getEntriesAroundUser(String userId, {int contextSize = 2}) {
    final userRank = getUserRank(userId);
    if (userRank == null) return [];
    return getEntriesAroundRank(userRank, contextSize: contextSize);
  }

  /// Get category display name
  String get categoryDisplayName {
    switch (category) {
      case LeaderboardCategory.totalPoints:
        return 'Total Points';
      case LeaderboardCategory.monthlyPoints:
        return 'Monthly Points';
      case LeaderboardCategory.weeklyPoints:
        return 'Weekly Points';
      case LeaderboardCategory.dailyPoints:
        return 'Daily Points';
      case LeaderboardCategory.questsCompleted:
        return 'Quests Completed';
      case LeaderboardCategory.tasksCompleted:
        return 'Tasks Completed';
      case LeaderboardCategory.currentStreak:
        return 'Current Streak';
      case LeaderboardCategory.longestStreak:
        return 'Longest Streak';
      case LeaderboardCategory.achievementsCount:
        return 'Achievements';
      case LeaderboardCategory.collaborationScore:
        return 'Collaboration Score';
      case LeaderboardCategory.efficiencyRating:
        return 'Efficiency Rating';
    }
  }

  /// Get period display name
  String get periodDisplayName {
    switch (period) {
      case LeaderboardPeriod.allTime:
        return 'All Time';
      case LeaderboardPeriod.yearly:
        return 'This Year';
      case LeaderboardPeriod.monthly:
        return 'This Month';
      case LeaderboardPeriod.weekly:
        return 'This Week';
      case LeaderboardPeriod.daily:
        return 'Today';
    }
  }

  /// Check if leaderboard needs update
  bool get needsUpdate {
    if (isRealTime) return false;
    if (updateFrequencyMinutes == null) return false;
    
    final now = DateTime.now();
    final nextUpdateTime = lastCalculated.add(Duration(minutes: updateFrequencyMinutes!));
    return now.isAfter(nextUpdateTime);
  }

  /// Get minutes until next update
  int? get minutesUntilUpdate {
    if (isRealTime || updateFrequencyMinutes == null) return null;
    
    final now = DateTime.now();
    final nextUpdateTime = lastCalculated.add(Duration(minutes: updateFrequencyMinutes!));
    final difference = nextUpdateTime.difference(now);
    
    return difference.isNegative ? 0 : difference.inMinutes;
  }

  /// Create a copy with updated fields
  Leaderboard copyWith({
    String? id,
    LeaderboardCategory? category,
    LeaderboardPeriod? period,
    String? title,
    String? description,
    int? maxEntries,
    List<LeaderboardEntry>? entries,
    int? totalParticipants,
    Map<String, dynamic>? metadata,
    bool? isRealTime,
    int? updateFrequencyMinutes,
    DateTime? lastCalculated,
    DateTime? createdAt,
    DateTime? nextUpdate,
  }) {
    return Leaderboard(
      id: id ?? this.id,
      category: category ?? this.category,
      period: period ?? this.period,
      title: title ?? this.title,
      description: description ?? this.description,
      maxEntries: maxEntries ?? this.maxEntries,
      entries: entries ?? this.entries,
      totalParticipants: totalParticipants ?? this.totalParticipants,
      metadata: metadata ?? this.metadata,
      isRealTime: isRealTime ?? this.isRealTime,
      updateFrequencyMinutes: updateFrequencyMinutes ?? this.updateFrequencyMinutes,
      lastCalculated: lastCalculated ?? this.lastCalculated,
      createdAt: createdAt ?? this.createdAt,
      nextUpdate: nextUpdate ?? this.nextUpdate,
    );
  }

  /// Create empty leaderboard for initialization
  static Leaderboard empty() {
    final now = DateTime.now();
    return Leaderboard(
      id: '',
      category: LeaderboardCategory.totalPoints,
      period: LeaderboardPeriod.allTime,
      title: '',
      description: '',
      maxEntries: 100,
      entries: [],
      totalParticipants: 0,
      isRealTime: false,
      lastCalculated: now,
      createdAt: now,
    );
  }

  @override
  List<Object?> get props => [
        id,
        category,
        period,
        title,
        description,
        maxEntries,
        entries,
        totalParticipants,
        metadata,
        isRealTime,
        updateFrequencyMinutes,
        lastCalculated,
        createdAt,
        nextUpdate,
      ];

  @override
  bool get stringify => true;
}

/// Extension to add firstOrNull method if not available
extension FirstWhereOrNull<T> on Iterable<T> {
  T? get firstOrNull {
    for (T element in this) {
      return element;
    }
    return null;
  }
}