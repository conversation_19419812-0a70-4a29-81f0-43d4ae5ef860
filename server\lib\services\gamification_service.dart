import 'dart:async';
import 'dart:math' as math;
import 'package:shared/shared.dart';
import '../services/database_service.dart';

/// Service for managing gamification features
/// Handles points, achievements, levels, and streaks
class GamificationService {
  final DatabaseService _databaseService;
  
  // Default point values for different activities
  static const int pointsPerTask = 10;
  static const int pointsPerQuest = 50;
  static const int pointsPerStreak = 5;
  static const int pointsPerCollaboration = 15;

  GamificationService(this._databaseService);

  /// Award points to a user for completing a task
  Future<void> awardPointsForTask(String userId, String taskId) async {
    try {
      await _databaseService.awardPoints(
        userId, 
        pointsPerTask, 
        'task_completion', 
        'Task completion'
      );
      
      // Check for achievement unlocks
      await _checkAchievements(userId);
    } catch (e) {
      print('Error awarding points for task: $e');
    }
  }

  /// Award points to a user for completing a quest
  Future<void> awardPointsForQuest(String userId, String questId) async {
    try {
      await _databaseService.awardPoints(
        userId, 
        pointsPerQuest, 
        'quest_completion', 
        'Quest completion'
      );
      
      // Check for achievement unlocks
      await _checkAchievements(userId);
    } catch (e) {
      print('Error awarding points for quest: $e');
    }
  }

  /// Award streak bonus points
  Future<void> awardStreakBonus(String userId, int streakDays) async {
    try {
      final bonusPoints = streakDays * pointsPerStreak;
      
      await _databaseService.awardPoints(
        userId, 
        bonusPoints, 
        'streak_bonus', 
        'Streak bonus: $streakDays days'
      );
      
      // Check for achievement unlocks
      await _checkAchievements(userId);
    } catch (e) {
      print('Error awarding streak bonus: $e');
    }
  }

  /// Get user's current progress and stats
  Future<UserProgress> getUserProgress(String userId) async {
    try {
      final stats = await _databaseService.getUserStats(userId);
      
      if (stats != null) {
        return UserProgress(
          userId: userId,
          totalPoints: stats['total_points'] ?? 0,
          level: UserProgress.calculateLevel(stats['total_points'] ?? 0),
          currentStreak: stats['current_streak'] ?? 0,
          longestStreak: stats['longest_streak'] ?? 0,
          unlockedAchievements: _parseAchievementsList(stats['achievements']),
          questsCompleted: stats['quests_completed'] ?? 0,
          tasksCompleted: stats['tasks_completed'] ?? 0,
          pointsToday: stats['points_today'] ?? 0,
          lastActivity: _parseDateTime(stats['last_activity']),
          updatedAt: DateTime.now(),
        );
      }
      
      // Return default progress for new user
      return UserProgress(
        userId: userId,
        totalPoints: 0,
        level: 1,
        currentStreak: 0,
        longestStreak: 0,
        unlockedAchievements: [],
        questsCompleted: 0,
        tasksCompleted: 0,
        pointsToday: 0,
        lastActivity: null,
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      print('Error getting user progress: $e');
      // Return default progress on error
      return UserProgress(
        userId: userId,
        totalPoints: 0,
        level: 1,
        currentStreak: 0,
        longestStreak: 0,
        unlockedAchievements: [],
        questsCompleted: 0,
        tasksCompleted: 0,
        pointsToday: 0,
        lastActivity: null,
        updatedAt: DateTime.now(),
      );
    }
  }

  /// Get leaderboard for top users
  Future<List<LeaderboardEntry>> getLeaderboardEntries({int limit = 50}) async {
    try {
      final leaderboardData = await _databaseService.getLeaderboard(
        type: 'global_points', 
        limit: limit
      );
      
      final entries = leaderboardData['entries'] as List? ?? [];
      
      return entries.map((entry) => LeaderboardEntry(
        userId: entry['user_id'] ?? '',
        displayName: entry['display_name'] ?? 'Unknown',
        avatarUrl: entry['avatar_url'],
        role: entry['role'] ?? 'Member',
        rank: entry['rank'] ?? 0,
        score: (entry['total_points'] ?? 0).toDouble(),
        stats: {
          'totalPoints': entry['total_points'] ?? 0,
          'level': entry['total_points'] ~/ 100, // Simple level calculation
          'currentStreak': entry['current_streak'] ?? 0,
          'achievementCount': entry['achievement_count'] ?? 0,
        },
        lastUpdated: DateTime.now(),
      )).toList();
    } catch (e) {
      print('Error getting leaderboard: $e');
      return [];
    }
  }

  /// Get leaderboard data as Map for API responses
  Future<Map<String, dynamic>> getLeaderboard({String type = 'global_points', int limit = 50}) async {
    try {
      final entries = await getLeaderboardEntries(limit: limit);
      
      return {
        'type': type,
        'entries': entries.map((entry) => {
          'user_id': entry.userId,
          'display_name': entry.displayName,
          'avatar_url': entry.avatarUrl,
          'rank': entry.rank,
          'score': entry.score,
          'level': entry.stats!['level'],
          'total_points': entry.stats!['totalPoints'],
          'current_streak': entry.stats!['currentStreak'],
          'achievements': entry.stats!['achievementCount'],
        }).toList(),
        'total_count': entries.length,
        'limit': limit,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting leaderboard data: $e');
      return {
        'type': type,
        'entries': <Map<String, dynamic>>[],
        'total_count': 0,
        'limit': limit,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get user's achievement progress
  Future<List<UserAchievement>> getUserAchievements(String userId) async {
    try {
      final achievements = await _databaseService.getUserAchievements(userId);
      
      return achievements.map((achievement) => UserAchievement(
        achievementId: achievement['achievement_id'] ?? '',
        userId: userId,
        currentProgress: achievement['current_progress'] ?? 0,
        status: _parseAchievementStatus(achievement['status']),
        unlockedAt: _parseDateTime(achievement['unlocked_at']),
        lastUpdated: _parseDateTime(achievement['last_updated']) ?? DateTime.now(),
      )).toList();
    } catch (e) {
      print('Error getting user achievements: $e');
      return [];
    }
  }

  /// Check and unlock achievements for user
  Future<void> _checkAchievements(String userId) async {
    try {
      final progress = await getUserProgress(userId);
      final achievements = await _databaseService.getAllAchievements();
      
      for (final achievementData in achievements) {
        final achievementId = achievementData['id'];
        
        // Skip if user already has this achievement
        if (progress.unlockedAchievements.contains(achievementId)) {
          continue;
        }
        
        // Simple achievement checking based on total points
        final pointsRequired = achievementData['points_required'] ?? 0;
        if (progress.totalPoints >= pointsRequired) {
          print('🎉 Achievement unlocked for user $userId: ${achievementData['name']}');
          // Note: Achievement unlocking would be handled by the database service
          // in a production system
        }
      }
    } catch (e) {
      print('Error checking achievements: $e');
    }
  }

  /// Award points for quest creation
  Future<void> awardPointsForQuestCreation(String userId, String questId) async {
    try {
      await _databaseService.awardPoints(
        userId, 
        pointsPerQuest ~/ 2, // Half points for creation
        'quest_creation', 
        'Created quest: $questId'
      );
      
      // Check for achievement unlocks
      await _checkAchievements(userId);
    } catch (e) {
      print('Error awarding points for quest creation: $e');
    }
  }

  /// Award collaboration points
  Future<void> awardCollaborationPoints(String userId, int collaboratorCount) async {
    try {
      final points = collaboratorCount * pointsPerCollaboration;
      await _databaseService.awardPoints(
        userId, 
        points, 
        'collaboration', 
        'Added $collaboratorCount collaborators'
      );
      
      // Check for achievement unlocks
      await _checkAchievements(userId);
    } catch (e) {
      print('Error awarding collaboration points: $e');
    }
  }

  /// Award points for quest completion with custom amount
  Future<void> awardPointsForQuestCompletion(String userId, int points, String questId) async {
    try {
      await _databaseService.awardPoints(
        userId, 
        points, 
        'quest_completion', 
        'Completed quest: $questId'
      );
      
      // Check for achievement unlocks
      await _checkAchievements(userId);
    } catch (e) {
      print('Error awarding points for quest completion: $e');
    }
  }

  /// Check achievements for a user (public method)
  Future<void> checkAchievements(String userId) async {
    await _checkAchievements(userId);
  }

  /// Parse achievement status from string
  AchievementStatus _parseAchievementStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'completed':
        return AchievementStatus.completed;
      case 'in_progress':
        return AchievementStatus.inProgress;
      case 'available':
        return AchievementStatus.available;
      default:
        return AchievementStatus.locked;
    }
  }

  /// Parse DateTime from string or return null
  DateTime? _parseDateTime(dynamic dateString) {
    if (dateString == null) return null;
    try {
      return DateTime.parse(dateString.toString());
    } catch (e) {
      return null;
    }
  }

  /// Parse achievements list from database
  List<String> _parseAchievementsList(dynamic achievementsData) {
    if (achievementsData == null) return [];
    
    if (achievementsData is String) {
      // Simple comma-separated parsing
      return achievementsData.split(',').where((id) => id.isNotEmpty).toList();
    }
    
    if (achievementsData is List) {
      return achievementsData.map((e) => e.toString()).toList();
    }
    
    return [];
  }

  // Additional methods for route compatibility

  /// Get user points (returns user points information)
  Future<Map<String, dynamic>?> getUserPoints(String userId) async {
    try {
      final progress = await getUserProgress(userId);
      
      // Calculate level progression (simplified calculation)
      final pointsForNextLevel = (progress.level + 1) * 100; // 100 points per level
      final pointsForCurrentLevel = progress.level * 100;
      final levelProgressPercentage = progress.totalPoints > pointsForCurrentLevel
          ? ((progress.totalPoints - pointsForCurrentLevel) / (pointsForNextLevel - pointsForCurrentLevel) * 100).clamp(0.0, 100.0)
          : 0.0;
      final pointsToNextLevel = math.max(0, pointsForNextLevel - progress.totalPoints);
      
      return {
        'user_id': userId,
        'total_points': progress.totalPoints,
        'current_level': progress.level,
        'level_progress_percentage': levelProgressPercentage,
        'points_to_next_level': pointsToNextLevel,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting user points: $e');
      return null;
    }
  }

  /// Award points to a user with activity type
  Future<Map<String, dynamic>?> awardPoints(
    String userId, 
    int points, 
    String activityType, 
    String description
  ) async {
    try {
      await _databaseService.awardPoints(userId, points, activityType, description);
      
      // Get updated progress
      final progress = await getUserProgress(userId);
      
      return {
        'user_id': userId,
        'points_awarded': points,
        'activity_type': activityType,
        'description': description,
        'total_points': progress.totalPoints,
        'new_level': progress.level,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error awarding points: $e');
      return null;
    }
  }

  /// Get user statistics
  Future<Map<String, dynamic>?> getUserStats(String userId) async {
    try {
      final progress = await getUserProgress(userId);
      final achievements = await getUserAchievements(userId);
      
      // Calculate level progression
      final pointsForNextLevel = (progress.level + 1) * 100;
      final pointsForCurrentLevel = progress.level * 100;
      final levelProgressPercentage = progress.totalPoints > pointsForCurrentLevel
          ? ((progress.totalPoints - pointsForCurrentLevel) / (pointsForNextLevel - pointsForCurrentLevel) * 100).clamp(0.0, 100.0)
          : 0.0;
      
      return {
        'user_id': userId,
        'total_points': progress.totalPoints,
        'level': progress.level,
        'level_progress': levelProgressPercentage,
        'achievements_count': achievements.length,
        'completed_achievements': achievements.where((a) => a.status == AchievementStatus.completed).length,
        'current_streak': progress.currentStreak,
        'longest_streak': progress.longestStreak,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting user stats: $e');
      return null;
    }
  }

  /// Check achievements and return results with data
  Future<Map<String, dynamic>> checkAchievementsWithData(String userId, Map<String, dynamic> data) async {
    try {
      await _checkAchievements(userId);
      
      // Mock response - in real implementation would return actual unlocked achievements
      return {
        'user_id': userId,
        'achievements_unlocked': <Map<String, dynamic>>[],
        'points_earned': 0,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error checking achievements: $e');
      return {
        'user_id': userId,
        'achievements_unlocked': <Map<String, dynamic>>[],
        'points_earned': 0,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get all available achievements
  Future<Map<String, dynamic>> getAllAchievements() async {
    try {
      // Mock data - in real implementation would fetch from database
      return {
        'achievements': <Map<String, dynamic>>[
          {
            'id': 'first_task',
            'name': 'First Task',
            'description': 'Complete your first task',
            'points_reward': 50,
            'rarity': 'common',
            'icon': 'task_complete',
          },
          {
            'id': 'level_10',
            'name': 'Level 10',
            'description': 'Reach level 10',
            'points_reward': 200,
            'rarity': 'uncommon',
            'icon': 'level_up',
          },
        ],
        'total_count': 2,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting all achievements: $e');
      return {
        'achievements': <Map<String, dynamic>>[],
        'total_count': 0,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get all rewards
  Future<Map<String, dynamic>> getAllRewards() async {
    try {
      // Mock data - in real implementation would fetch from database
      return {
        'rewards': <Map<String, dynamic>>[
          {
            'id': 'reward_badge',
            'name': 'Special Badge',
            'description': 'A special badge for dedicated users',
            'cost_points': 500,
            'type': 'badge',
            'available': true,
          },
          {
            'id': 'reward_title',
            'name': 'Champion Title',
            'description': 'Earn the Champion title',
            'cost_points': 1000,
            'type': 'title',
            'available': true,
          },
        ],
        'total_count': 2,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting all rewards: $e');
      return {
        'rewards': <Map<String, dynamic>>[],
        'total_count': 0,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Purchase a reward
  Future<Map<String, dynamic>?> purchaseReward(String userId, String rewardId) async {
    try {
      final progress = await getUserProgress(userId);
      
      // Mock reward cost
      const rewardCost = 500;
      
      if (progress.totalPoints < rewardCost) {
        return {
          'success': false,
          'error': 'Insufficient points',
          'required_points': rewardCost,
          'user_points': progress.totalPoints,
          'timestamp': DateTime.now().toIso8601String(),
        };
      }
      
      // Deduct points (mock implementation)
      await _databaseService.awardPoints(userId, -rewardCost, 'reward_purchase', 'Purchased reward $rewardId');
      
      return {
        'success': true,
        'reward_id': rewardId,
        'cost_points': rewardCost,
        'remaining_points': progress.totalPoints - rewardCost,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error purchasing reward: $e');
      return null;
    }
  }

  /// Get global activity feed
  Future<Map<String, dynamic>> getGlobalActivity({int limit = 50, int offset = 0}) async {
    try {
      // Mock data - in real implementation would fetch from database
      return {
        'activities': <Map<String, dynamic>>[
          {
            'id': 'activity_001',
            'user_id': 'user_001',
            'user_name': 'John Doe',
            'action': 'completed_quest',
            'description': 'Completed "Daily Fitness Challenge"',
            'points_earned': 100,
            'timestamp': DateTime.now().subtract(const Duration(minutes: 30)).toIso8601String(),
          },
          {
            'id': 'activity_002',
            'user_id': 'user_002',
            'user_name': 'Jane Smith',
            'action': 'achievement_unlocked',
            'description': 'Unlocked "First Task" achievement',
            'points_earned': 50,
            'timestamp': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
          },
        ],
        'pagination': {
          'current_page': (offset ~/ limit) + 1,
          'total_count': 2,
          'has_more': false,
        },
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting global activity: $e');
      return {
        'activities': <Map<String, dynamic>>[],
        'pagination': {
          'current_page': 1,
          'total_count': 0,
          'has_more': false,
        },
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get user activity feed
  Future<Map<String, dynamic>> getUserActivity(String userId, {int limit = 20}) async {
    try {
      // Mock data - in real implementation would fetch from database
      return {
        'user_id': userId,
        'activities': <Map<String, dynamic>>[
          {
            'id': 'user_activity_001',
            'action': 'task_completed',
            'description': 'Completed task "Review documentation"',
            'points_earned': 25,
            'timestamp': DateTime.now().subtract(const Duration(minutes: 15)).toIso8601String(),
          },
          {
            'id': 'user_activity_002',
            'action': 'level_up',
            'description': 'Reached level 5',
            'points_earned': 0,
            'timestamp': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
          },
        ],
        'total_count': 2,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting user activity: $e');
      return {
        'user_id': userId,
        'activities': <Map<String, dynamic>>[],
        'total_count': 0,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
