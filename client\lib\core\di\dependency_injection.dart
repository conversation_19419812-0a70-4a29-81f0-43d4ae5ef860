import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;

import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/websocket_service.dart';
import '../../data/repositories/api_repository.dart';
import '../../data/datasources/remote_data_source.dart';

/// Dependency Injection setup using GetIt
class DependencyInjection {
  static final GetIt instance = GetIt.instance;

  static Future<void> init() async {
    // HTTP Client
    instance.registerLazySingleton<http.Client>(() => http.Client());
    
    // Services
    instance.registerLazySingleton<ApiService>(() => ApiService());
    instance.registerLazySingleton<AuthService>(() => AuthService());
    instance.registerLazySingleton<WebSocketService>(() => WebSocketService());
    
    // Data Sources
    instance.registerLazySingleton<RemoteDataSource>(
      () => RemoteDataSource(
        client: instance<http.Client>(),
        apiService: instance<ApiService>(),
      ),
    );
    
    // Repositories
    instance.registerLazySingleton<ApiRepository>(
      () => ApiRepository(
        remoteDataSource: instance<RemoteDataSource>(),
        authService: instance<AuthService>(),
      ),
    );
    
    debugPrint('✅ Dependency Injection initialized');
  }
}