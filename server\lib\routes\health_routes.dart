/// Health and monitoring routes for the Quester server
library;

import 'dart:convert';
import 'dart:io';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/logging_service.dart';
import '../services/database_service.dart';
import '../utils/response_utils.dart';

/// Health and monitoring routes
class HealthRoutes {
  static Router get router {
    final router = Router();

    // Basic health check
    router.get('/health', _healthCheck);
    
    // Detailed health status
    router.get('/health/detailed', _detailedHealthCheck);
    
    // Error statistics
    router.get('/health/errors', _errorStatistics);
    
    // Recent errors
    router.get('/health/errors/recent', _recentErrors);
    
    // System metrics
    router.get('/health/metrics', _systemMetrics);
    
    // Clear error statistics (admin only)
    router.delete('/health/errors', _clearErrorStats);

    return router;
  }

  /// Basic health check endpoint
  static Response _healthCheck(Request request) {
    try {
      final healthData = {
        'status': 'healthy',
        'timestamp': DateTime.now().toIso8601String(),
        'uptime': _getUptime(),
        'version': '1.0.0',
      };

      return ResponseUtils.success(data: healthData);
    } catch (error) {
      LoggingService.error('Health check failed', error: error);
      return ResponseUtils.error(
        message: 'Health check failed',
        statusCode: 503,
        errorCode: 'HEALTH_CHECK_FAILED',
      );
    }
  }

  /// Detailed health check with component status
  static Future<Response> _detailedHealthCheck(Request request) async {
    try {
      final components = <String, Map<String, dynamic>>{};
      
      // Check database connectivity
      components['database'] = await _checkDatabaseHealth();
      
      // Check memory usage
      components['memory'] = _checkMemoryHealth();
      
      // Check disk space
      components['disk'] = await _checkDiskHealth();
      
      // Check error rates
      components['errors'] = _checkErrorHealth();
      
      // Determine overall status
      final overallStatus = _determineOverallStatus(components);
      
      final healthData = {
        'status': overallStatus,
        'timestamp': DateTime.now().toIso8601String(),
        'uptime': _getUptime(),
        'version': '1.0.0',
        'components': components,
      };

      final statusCode = overallStatus == 'healthy' ? 200 : 503;
      return ResponseUtils.success(data: healthData, statusCode: statusCode);
      
    } catch (error) {
      LoggingService.error('Detailed health check failed', error: error);
      return ResponseUtils.error(
        message: 'Detailed health check failed',
        statusCode: 503,
        errorCode: 'DETAILED_HEALTH_CHECK_FAILED',
      );
    }
  }

  /// Get error statistics
  static Response _errorStatistics(Request request) {
    try {
      final errorStats = LoggingService.getErrorStats();
      return ResponseUtils.success(data: errorStats);
    } catch (error) {
      LoggingService.error('Failed to get error statistics', error: error);
      return ResponseUtils.error(
        message: 'Failed to retrieve error statistics',
        statusCode: 500,
      );
    }
  }

  /// Get recent errors
  static Response _recentErrors(Request request) {
    try {
      final limitParam = request.url.queryParameters['limit'];
      final limit = limitParam != null ? int.tryParse(limitParam) ?? 50 : 50;
      
      final recentErrors = LoggingService.getRecentErrors(limit: limit);
      return ResponseUtils.success(data: {
        'errors': recentErrors,
        'count': recentErrors.length,
        'limit': limit,
      });
    } catch (error) {
      LoggingService.error('Failed to get recent errors', error: error);
      return ResponseUtils.error(
        message: 'Failed to retrieve recent errors',
        statusCode: 500,
      );
    }
  }

  /// Get system metrics
  static Response _systemMetrics(Request request) {
    try {
      final metrics = {
        'memory': _getMemoryMetrics(),
        'uptime': _getUptime(),
        'timestamp': DateTime.now().toIso8601String(),
        'platform': {
          'os': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
          'dart_version': Platform.version,
        },
      };

      return ResponseUtils.success(data: metrics);
    } catch (error) {
      LoggingService.error('Failed to get system metrics', error: error);
      return ResponseUtils.error(
        message: 'Failed to retrieve system metrics',
        statusCode: 500,
      );
    }
  }

  /// Clear error statistics (admin endpoint)
  static Response _clearErrorStats(Request request) {
    try {
      // TODO: Add admin authentication check
      LoggingService.clearErrorStats();
      LoggingService.info('Error statistics cleared', tag: 'Admin');
      
      return ResponseUtils.success(
        message: 'Error statistics cleared successfully',
      );
    } catch (error) {
      LoggingService.error('Failed to clear error statistics', error: error);
      return ResponseUtils.error(
        message: 'Failed to clear error statistics',
        statusCode: 500,
      );
    }
  }

  /// Check database health
  static Future<Map<String, dynamic>> _checkDatabaseHealth() async {
    try {
      // TODO: Implement actual database health check
      // For now, return a mock healthy status
      return {
        'status': 'healthy',
        'response_time_ms': 50,
        'connections': {
          'active': 5,
          'max': 20,
        },
      };
    } catch (error) {
      return {
        'status': 'unhealthy',
        'error': error.toString(),
      };
    }
  }

  /// Check memory health
  static Map<String, dynamic> _checkMemoryHealth() {
    try {
      final memoryMetrics = _getMemoryMetrics();
      final usagePercent = (memoryMetrics['used'] as double) / (memoryMetrics['total'] as double) * 100;
      
      return {
        'status': usagePercent > 90 ? 'critical' : usagePercent > 75 ? 'warning' : 'healthy',
        'usage_percent': usagePercent.round(),
        'metrics': memoryMetrics,
      };
    } catch (error) {
      return {
        'status': 'unknown',
        'error': error.toString(),
      };
    }
  }

  /// Check disk health
  static Future<Map<String, dynamic>> _checkDiskHealth() async {
    try {
      // Simple disk space check for current directory
      final currentDir = Directory.current;
      final stat = await currentDir.stat();
      
      return {
        'status': 'healthy', // Simplified - would need platform-specific implementation
        'path': currentDir.path,
        'last_modified': stat.modified.toIso8601String(),
      };
    } catch (error) {
      return {
        'status': 'unknown',
        'error': error.toString(),
      };
    }
  }

  /// Check error health based on recent error rates
  static Map<String, dynamic> _checkErrorHealth() {
    try {
      final errorStats = LoggingService.getErrorStats();
      final recentErrors = errorStats['recentErrors24h'] as int;
      
      String status;
      if (recentErrors > 100) {
        status = 'critical';
      } else if (recentErrors > 50) {
        status = 'warning';
      } else {
        status = 'healthy';
      }
      
      return {
        'status': status,
        'recent_errors_24h': recentErrors,
        'total_errors': errorStats['totalErrors'],
        'unique_errors': errorStats['uniqueErrors'],
      };
    } catch (error) {
      return {
        'status': 'unknown',
        'error': error.toString(),
      };
    }
  }

  /// Determine overall health status from components
  static String _determineOverallStatus(Map<String, Map<String, dynamic>> components) {
    var hasCritical = false;
    var hasWarning = false;
    
    for (final component in components.values) {
      final status = component['status'] as String;
      if (status == 'critical' || status == 'unhealthy') {
        hasCritical = true;
      } else if (status == 'warning') {
        hasWarning = true;
      }
    }
    
    if (hasCritical) return 'critical';
    if (hasWarning) return 'warning';
    return 'healthy';
  }

  /// Get server uptime
  static Map<String, dynamic> _getUptime() {
    // This is a simplified implementation
    // In a real application, you'd track the actual start time
    final now = DateTime.now();
    final startTime = now.subtract(const Duration(hours: 1)); // Mock start time
    final uptime = now.difference(startTime);
    
    return {
      'seconds': uptime.inSeconds,
      'human_readable': _formatDuration(uptime),
      'start_time': startTime.toIso8601String(),
    };
  }

  /// Get memory metrics
  static Map<String, dynamic> _getMemoryMetrics() {
    // This is a simplified implementation
    // In a real application, you'd use platform-specific APIs
    return {
      'used': 512.0 * 1024 * 1024, // 512 MB in bytes
      'total': 2048.0 * 1024 * 1024, // 2 GB in bytes
      'free': 1536.0 * 1024 * 1024, // 1.5 GB in bytes
    };
  }

  /// Format duration in human-readable format
  static String _formatDuration(Duration duration) {
    final days = duration.inDays;
    final hours = duration.inHours % 24;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    
    final parts = <String>[];
    if (days > 0) parts.add('${days}d');
    if (hours > 0) parts.add('${hours}h');
    if (minutes > 0) parts.add('${minutes}m');
    if (seconds > 0) parts.add('${seconds}s');
    
    return parts.isEmpty ? '0s' : parts.join(' ');
  }
}
