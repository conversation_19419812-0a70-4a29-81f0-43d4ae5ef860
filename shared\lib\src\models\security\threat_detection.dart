import 'package:json_annotation/json_annotation.dart';

part 'threat_detection.g.dart';

/// Threat severity levels
enum ThreatSeverity { low, medium, high, critical }

/// Threat types
enum ThreatType { 
  suspiciousLogin, 
  bruteForce, 
  anomalousAccess, 
  dataExfiltration, 
  privilegeEscalation,
  malwareDetection,
  geolocationAnomaly,
  deviceAnomaly,
  behaviorAnomaly,
  apiAbuse
}

/// Threat status
enum ThreatStatus { detected, investigating, mitigated, resolved, falsePositive }

/// Security threat detection event
@JsonSerializable()
class SecurityThreat {
  final String id;
  @Json<PERSON>ey(name: 'organization_id')
  final String organizationId;
  @JsonKey(name: 'user_id')
  final String? userId;
  @J<PERSON><PERSON><PERSON>(name: 'threat_type')
  final ThreatType threatType;
  @JsonKey(name: 'severity')
  final ThreatSeverity severity;
  @JsonKey(name: 'title')
  final String title;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'source_ip')
  final String? sourceIp;
  @Json<PERSON>ey(name: 'user_agent')
  final String? userAgent;
  @Json<PERSON>ey(name: 'geolocation')
  final Map<String, dynamic>? geolocation;
  @JsonKey(name: 'threat_indicators')
  final List<ThreatIndicator> indicators;
  @JsonKey(name: 'risk_score')
  final double riskScore;
  @JsonKey(name: 'confidence_score')
  final double confidenceScore;
  @JsonKey(name: 'status')
  final ThreatStatus status;
  @JsonKey(name: 'mitigation_actions')
  final List<String> mitigationActions;
  @JsonKey(name: 'assigned_to')
  final String? assignedTo;
  @JsonKey(name: 'resolved_at')
  final DateTime? resolvedAt;
  @JsonKey(name: 'detected_at')
  final DateTime detectedAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'metadata')
  final Map<String, dynamic> metadata;

  const SecurityThreat({
    required this.id,
    required this.organizationId,
    this.userId,
    required this.threatType,
    required this.severity,
    required this.title,
    required this.description,
    this.sourceIp,
    this.userAgent,
    this.geolocation,
    required this.indicators,
    required this.riskScore,
    required this.confidenceScore,
    this.status = ThreatStatus.detected,
    this.mitigationActions = const [],
    this.assignedTo,
    this.resolvedAt,
    required this.detectedAt,
    required this.updatedAt,
    this.metadata = const {},
  });

  factory SecurityThreat.fromJson(Map<String, dynamic> json) =>
      _$SecurityThreatFromJson(json);

  Map<String, dynamic> toJson() => _$SecurityThreatToJson(this);

  /// Check if threat is active
  bool get isActive => status != ThreatStatus.resolved && status != ThreatStatus.falsePositive;

  /// Check if threat is critical
  bool get isCritical => severity == ThreatSeverity.critical;

  /// Get severity color for UI
  String get severityColor {
    switch (severity) {
      case ThreatSeverity.low:
        return '#28a745';
      case ThreatSeverity.medium:
        return '#ffc107';
      case ThreatSeverity.high:
        return '#fd7e14';
      case ThreatSeverity.critical:
        return '#dc3545';
    }
  }

  SecurityThreat copyWith({
    String? id,
    String? organizationId,
    String? userId,
    ThreatType? threatType,
    ThreatSeverity? severity,
    String? title,
    String? description,
    String? sourceIp,
    String? userAgent,
    Map<String, dynamic>? geolocation,
    List<ThreatIndicator>? indicators,
    double? riskScore,
    double? confidenceScore,
    ThreatStatus? status,
    List<String>? mitigationActions,
    String? assignedTo,
    DateTime? resolvedAt,
    DateTime? detectedAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return SecurityThreat(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      userId: userId ?? this.userId,
      threatType: threatType ?? this.threatType,
      severity: severity ?? this.severity,
      title: title ?? this.title,
      description: description ?? this.description,
      sourceIp: sourceIp ?? this.sourceIp,
      userAgent: userAgent ?? this.userAgent,
      geolocation: geolocation ?? this.geolocation,
      indicators: indicators ?? this.indicators,
      riskScore: riskScore ?? this.riskScore,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      status: status ?? this.status,
      mitigationActions: mitigationActions ?? this.mitigationActions,
      assignedTo: assignedTo ?? this.assignedTo,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      detectedAt: detectedAt ?? this.detectedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Threat indicator
@JsonSerializable()
class ThreatIndicator {
  @JsonKey(name: 'type')
  final String type;
  @JsonKey(name: 'value')
  final String value;
  @JsonKey(name: 'severity')
  final ThreatSeverity severity;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'confidence')
  final double confidence;

  const ThreatIndicator({
    required this.type,
    required this.value,
    required this.severity,
    required this.description,
    required this.confidence,
  });

  factory ThreatIndicator.fromJson(Map<String, dynamic> json) =>
      _$ThreatIndicatorFromJson(json);

  Map<String, dynamic> toJson() => _$ThreatIndicatorToJson(this);
}

/// Advanced threat detection configuration
@JsonSerializable()
class ThreatDetectionConfig {
  @JsonKey(name: 'organization_id')
  final String organizationId;
  @JsonKey(name: 'enabled')
  final bool enabled;
  @JsonKey(name: 'sensitivity_level')
  final double sensitivityLevel; // 0.0 - 1.0
  @JsonKey(name: 'auto_mitigation_enabled')
  final bool autoMitigationEnabled;
  @JsonKey(name: 'notification_settings')
  final NotificationSettings notificationSettings;
  @JsonKey(name: 'detection_rules')
  final List<DetectionRule> detectionRules;
  @JsonKey(name: 'whitelist_ips')
  final List<String> whitelistIPs;
  @JsonKey(name: 'blacklist_ips')
  final List<String> blacklistIPs;
  @JsonKey(name: 'geofencing_enabled')
  final bool geofencingEnabled;
  @JsonKey(name: 'allowed_countries')
  final List<String> allowedCountries;
  @JsonKey(name: 'behavioral_analysis_enabled')
  final bool behavioralAnalysisEnabled;
  @JsonKey(name: 'ml_detection_enabled')
  final bool mlDetectionEnabled;

  const ThreatDetectionConfig({
    required this.organizationId,
    this.enabled = true,
    this.sensitivityLevel = 0.7,
    this.autoMitigationEnabled = false,
    required this.notificationSettings,
    this.detectionRules = const [],
    this.whitelistIPs = const [],
    this.blacklistIPs = const [],
    this.geofencingEnabled = false,
    this.allowedCountries = const [],
    this.behavioralAnalysisEnabled = true,
    this.mlDetectionEnabled = false,
  });

  factory ThreatDetectionConfig.fromJson(Map<String, dynamic> json) =>
      _$ThreatDetectionConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ThreatDetectionConfigToJson(this);

  ThreatDetectionConfig copyWith({
    String? organizationId,
    bool? enabled,
    double? sensitivityLevel,
    bool? autoMitigationEnabled,
    NotificationSettings? notificationSettings,
    List<DetectionRule>? detectionRules,
    List<String>? whitelistIPs,
    List<String>? blacklistIPs,
    bool? geofencingEnabled,
    List<String>? allowedCountries,
    bool? behavioralAnalysisEnabled,
    bool? mlDetectionEnabled,
  }) {
    return ThreatDetectionConfig(
      organizationId: organizationId ?? this.organizationId,
      enabled: enabled ?? this.enabled,
      sensitivityLevel: sensitivityLevel ?? this.sensitivityLevel,
      autoMitigationEnabled: autoMitigationEnabled ?? this.autoMitigationEnabled,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      detectionRules: detectionRules ?? this.detectionRules,
      whitelistIPs: whitelistIPs ?? this.whitelistIPs,
      blacklistIPs: blacklistIPs ?? this.blacklistIPs,
      geofencingEnabled: geofencingEnabled ?? this.geofencingEnabled,
      allowedCountries: allowedCountries ?? this.allowedCountries,
      behavioralAnalysisEnabled: behavioralAnalysisEnabled ?? this.behavioralAnalysisEnabled,
      mlDetectionEnabled: mlDetectionEnabled ?? this.mlDetectionEnabled,
    );
  }
}

/// Notification settings for threats
@JsonSerializable()
class NotificationSettings {
  @JsonKey(name: 'email_enabled')
  final bool emailEnabled;
  @JsonKey(name: 'email_recipients')
  final List<String> emailRecipients;
  @JsonKey(name: 'slack_enabled')
  final bool slackEnabled;
  @JsonKey(name: 'slack_webhook')
  final String? slackWebhook;
  @JsonKey(name: 'sms_enabled')
  final bool smsEnabled;
  @JsonKey(name: 'sms_numbers')
  final List<String> smsNumbers;
  @JsonKey(name: 'severity_threshold')
  final ThreatSeverity severityThreshold;

  const NotificationSettings({
    this.emailEnabled = true,
    this.emailRecipients = const [],
    this.slackEnabled = false,
    this.slackWebhook,
    this.smsEnabled = false,
    this.smsNumbers = const [],
    this.severityThreshold = ThreatSeverity.medium,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSettingsToJson(this);
}

/// Custom detection rule
@JsonSerializable()
class DetectionRule {
  @JsonKey(name: 'id')
  final String id;
  @JsonKey(name: 'name')
  final String name;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'rule_type')
  final String ruleType;
  @JsonKey(name: 'conditions')
  final Map<String, dynamic> conditions;
  @JsonKey(name: 'actions')
  final List<String> actions;
  @JsonKey(name: 'severity')
  final ThreatSeverity severity;
  @JsonKey(name: 'enabled')
  final bool enabled;

  const DetectionRule({
    required this.id,
    required this.name,
    required this.description,
    required this.ruleType,
    required this.conditions,
    required this.actions,
    required this.severity,
    this.enabled = true,
  });

  factory DetectionRule.fromJson(Map<String, dynamic> json) =>
      _$DetectionRuleFromJson(json);

  Map<String, dynamic> toJson() => _$DetectionRuleToJson(this);
}

/// Threat detection statistics
@JsonSerializable()
class ThreatDetectionStats {
  @JsonKey(name: 'organization_id')
  final String organizationId;
  @JsonKey(name: 'total_threats')
  final int totalThreats;
  @JsonKey(name: 'active_threats')
  final int activeThreats;
  @JsonKey(name: 'critical_threats')
  final int criticalThreats;
  @JsonKey(name: 'threats_by_type')
  final Map<String, int> threatsByType;
  @JsonKey(name: 'threats_by_severity')
  final Map<String, int> threatsBySeverity;
  @JsonKey(name: 'average_response_time')
  final Duration averageResponseTime;
  @JsonKey(name: 'false_positive_rate')
  final double falsePositiveRate;
  @JsonKey(name: 'detection_accuracy')
  final double detectionAccuracy;
  @JsonKey(name: 'generated_at')
  final DateTime generatedAt;

  const ThreatDetectionStats({
    required this.organizationId,
    required this.totalThreats,
    required this.activeThreats,
    required this.criticalThreats,
    required this.threatsByType,
    required this.threatsBySeverity,
    required this.averageResponseTime,
    required this.falsePositiveRate,
    required this.detectionAccuracy,
    required this.generatedAt,
  });

  factory ThreatDetectionStats.fromJson(Map<String, dynamic> json) =>
      _$ThreatDetectionStatsFromJson(json);

  Map<String, dynamic> toJson() => _$ThreatDetectionStatsToJson(this);
}