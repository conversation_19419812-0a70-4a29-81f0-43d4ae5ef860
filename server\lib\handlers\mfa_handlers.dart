import 'dart:convert';
import 'dart:math';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/database_service.dart';
import '../services/mfa_policy_engine.dart' as policy;
import '../services/totp_service.dart';
import '../services/mfa_delivery_service.dart';
import '../services/backup_codes_service.dart';
import '../services/trusted_device_service.dart';
import 'package:shared/shared.dart' as shared;


class MFAHandlers {
  final DatabaseService _databaseService;
  final policy.MFAPolicyEngine _policyEngine;
  final TOTPService _totpService;
  final MFADeliveryService _deliveryService;
  final BackupCodesService _backupCodesService;
  final TrustedDeviceService _trustedDeviceService;

  MFAHandlers(
    this._databaseService,
    this._policyEngine,
    this._totpService,
    this._deliveryService,
    this._backupCodesService,
    this._trustedDeviceService,
  );

  Router get router {
    final router = Router();

    // MFA Setup and Configuration
    router.get('/mfa/setup/status/<userId>', _getMFASetupStatus);
    router.post('/mfa/setup/totp/generate', _generateTOTPSetup);
    router.post('/mfa/setup/totp/verify', _verifyTOTPSetup);
    router.post('/mfa/setup/phone/verify', _verifyPhoneForSMS);
    router.post('/mfa/setup/email/verify', _verifyEmailForMFA);

    // MFA Authentication Flow
    router.post('/mfa/evaluate', _evaluateMFARequirement);
    router.post('/mfa/challenge/create', _createMFAChallenge);
    router.post('/mfa/challenge/verify', _verifyMFAChallenge);
    router.get('/mfa/challenge/status/<challengeId>', _getChallengeStatus);

    // Backup Codes Management
    router.get('/mfa/backup-codes/<userId>', _getBackupCodesStatus);
    router.post('/mfa/backup-codes/<userId>/generate', _generateBackupCodes);
    router.post('/mfa/backup-codes/<userId>/validate', _validateBackupCode);
    router.delete('/mfa/backup-codes/<userId>/revoke', _revokeBackupCodes);

    // Trusted Devices Management
    router.get('/mfa/devices/<userId>', _getTrustedDevices);
    router.post('/mfa/devices/<userId>/register', _registerTrustedDevice);
    router.post('/mfa/devices/<userId>/verify', _verifyTrustedDevice);
    router.post('/mfa/devices/<userId>/<deviceId>/trust', _trustDevice);
    router.delete('/mfa/devices/<userId>/<deviceId>', _revokeTrustedDevice);
    router.get('/mfa/devices/<userId>/stats', _getDeviceStats);

    // MFA User Settings
    router.get('/mfa/settings/<userId>', _getMFASettings);
    router.put('/mfa/settings/<userId>', _updateMFASettings);
    router.delete('/mfa/settings/<userId>/disable', _disableMFA);

    // MFA Recovery
    router.post('/mfa/recovery/initiate', _initiateAccountRecovery);
    router.post('/mfa/recovery/verify', _verifyAccountRecovery);
    router.post('/mfa/recovery/complete', _completeAccountRecovery);

    // Administrative Functions
    router.get('/mfa/admin/policies', _getMFAPolicies);
    router.post('/mfa/admin/policies', _createMFAPolicy);
    router.put('/mfa/admin/policies/<policyId>', _updateMFAPolicy);
    router.delete('/mfa/admin/policies/<policyId>', _deleteMFAPolicy);

    return router;
  }

  // MFA Setup Endpoints
  Future<Response> _getMFASetupStatus(Request request) async {
    try {
      final userId = request.params['userId']!;
      
      // Get TOTP setup status  
      final totpSetup = await _getTOTPSetupStatus(userId);
      
      // Get backup codes status
      final backupCodesStatus = await _backupCodesService.getActiveCodeCount(userId);
      
      // Get trusted devices count
      final trustedDevices = await _trustedDeviceService.getUserDevices(userId, activeOnly: true);
      
      // Get user's phone and email for MFA delivery
      final userInfo = await _databaseService.query(
        'SELECT phone, email FROM users WHERE id = @user_id',
        {'user_id': userId},
      );
      
      final setupStatus = {
        'user_id': userId,
        'totp_enabled': totpSetup['is_configured'] as bool,
        'totp_verified': totpSetup['is_enabled'] as bool,
        'backup_codes_count': backupCodesStatus,
        'trusted_devices_count': trustedDevices.length,
        'phone_configured': userInfo.isNotEmpty && userInfo.first['phone'] != null,
        'email_configured': userInfo.isNotEmpty && userInfo.first['email'] != null,
        'setup_complete': totpSetup['is_enabled'] == true && backupCodesStatus > 0,
        'last_updated': DateTime.now().toIso8601String(),
      };

      return Response.ok(
        jsonEncode({'success': true, 'data': setupStatus}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _generateTOTPSetup(Request request) async {
    try {
      final body = jsonDecode(await request.readAsString());
      final userId = body['user_id'] as String;
      final issuer = body['issuer'] as String? ?? 'Quester';
      
      // Get user email for TOTP setup
      final userInfo = await _databaseService.query(
        'SELECT email FROM users WHERE id = @user_id',
        {'user_id': userId},
      );
      
      if (userInfo.isEmpty) {
        throw Exception('User not found');
      }
      
      final userEmail = userInfo.first['email'] as String;
      
      final totpSetup = await _totpService.generateSecret(
        userId: userId,
        userEmail: userEmail,
        issuer: issuer,
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': totpSetup.toJson(),
          'message': 'TOTP setup generated successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _verifyTOTPSetup(Request request) async {
    try {
      final body = jsonDecode(await request.readAsString());
      final userId = body['user_id'] as String;
      final verificationCode = body['verification_code'] as String;
      
      final result = await _totpService.verifyTOTPSetup(
        userId: userId,
        verificationCode: verificationCode,
      );
      
      if (result) {
        return Response.ok(
          jsonEncode({
            'success': true,
            'data': {'verified': true},
            'message': 'TOTP setup verified successfully'
          }),
          headers: {'Content-Type': 'application/json'},
        );
      } else {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Invalid verification code'
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _verifyPhoneForSMS(Request request) async {
    try {
      final body = jsonDecode(await request.readAsString());
      final userId = body['user_id'] as String;
      final phoneNumber = body['phone_number'] as String;
      
      // Generate and send verification code
      final verificationCode = _generateVerificationCode();
      await _deliveryService.sendSMSCode(
        userId: userId,
        phoneNumber: phoneNumber,
      );
      
      // Store verification in database temporarily
      await _storePhoneVerification(userId, phoneNumber, verificationCode);
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Verification code sent to phone',
          'phone_hint': _maskPhoneNumber(phoneNumber),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _verifyEmailForMFA(Request request) async {
    try {
      final body = jsonDecode(await request.readAsString());
      final userId = body['user_id'] as String;
      final email = body['email'] as String;
      
      // Generate and send verification code
      final verificationCode = _generateVerificationCode();
      await _deliveryService.sendEmailCode(
        userId: userId,
        email: email,
      );
      
      // Store verification in database temporarily
      await _storeEmailVerification(userId, email, verificationCode);
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Verification code sent to email',
          'email_hint': _maskEmail(email),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // MFA Authentication Flow Endpoints
  Future<Response> _evaluateMFARequirement(Request request) async {
    try {
      final body = jsonDecode(await request.readAsString());
      final userId = body['user_id'] as String;
      final organizationId = body['organization_id'] as String?;
      final deviceFingerprint = body['device_fingerprint'] as String?;
      final ipAddress = _getClientIP(request);
      final userAgent = request.headers['user-agent'];
      final context = body['context'] as Map<String, dynamic>? ?? {};
      
      final result = await _policyEngine.evaluateMFARequirement(
        userId,
        organizationId: organizationId,
        deviceFingerprint: deviceFingerprint,
        ipAddress: ipAddress,
        userAgent: userAgent,
        context: context,
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': result.toJson(),
          'message': result.isMFARequired ? 'MFA required' : 'MFA not required'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _createMFAChallenge(Request request) async {
    try {
      final body = jsonDecode(await request.readAsString());
      final userId = body['user_id'] as String;
      final methodName = body['method'] as String;
      
      // Convert from shared MFAMethod to policy engine MFAMethod
      final policyMethod = _convertToPolicyMethod(methodName);
      final destination = body['destination'] as String?;
      final validDurationMinutes = body['valid_duration_minutes'] as int? ?? 5;
      
      final challenge = await _policyEngine.createMFAChallenge(
        userId,
        policyMethod,
        destination: destination,
        validDuration: Duration(minutes: validDurationMinutes),
        metadata: body['metadata'] as Map<String, dynamic>? ?? {},
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'challenge_id': challenge.id,
            'method': challenge.method.name,
            'destination_hint': challenge.destinationHint,
            'expires_at': challenge.expiresAt.toIso8601String(),
            'max_attempts': challenge.maxAttempts,
          },
          'message': 'MFA challenge created successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _verifyMFAChallenge(Request request) async {
    try {
      final body = jsonDecode(await request.readAsString());
      
      final challengeId = body['challenge_id'] as String;
      final userId = body['user_id'] as String;
      final methodName = body['method'] as String;
      final verificationCode = body['verification_code'] as String;
      final deviceFingerprint = body['device_fingerprint'] as String?;
      final trustDevice = body['trust_device'] as bool? ?? false;
      
      // Convert to policy engine MFAMethod
      final policyMethod = _convertToPolicyMethod(methodName);
      
      final verificationRequest = policy.MFAVerificationRequest(
        challengeId: challengeId,
        userId: userId,
        method: policyMethod,
        verificationCode: verificationCode,
        deviceFingerprint: deviceFingerprint,
        ipAddress: _getClientIP(request),
        userAgent: request.headers['user-agent'],
        trustDevice: trustDevice,
      );
      
      final result = await _policyEngine.verifyMFAChallenge(verificationRequest);
      
      if (result.isValid && result.isCompleted) {
        return Response.ok(
          jsonEncode({
            'success': true,
            'data': result.toJson(),
            'message': 'MFA verification successful'
          }),
          headers: {'Content-Type': 'application/json'},
        );
      } else {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': result.errorMessage ?? 'Verification failed',
            'attempts_remaining': result.attemptsRemaining,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getChallengeStatus(Request request) async {
    try {
      final challengeId = request.params['challengeId']!;
      
      // This would query the challenge from the database
      // For now, return a placeholder response
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'challenge_id': challengeId,
            'status': 'active',
            'message': 'Challenge status retrieved'
          }
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Backup Codes Endpoints
  Future<Response> _getBackupCodesStatus(Request request) async {
    try {
      final userId = request.params['userId']!;
      
      final activeCount = await _backupCodesService.getActiveCodeCount(userId);
      final codes = await _backupCodesService.getUserBackupCodes(userId);
      
      final status = {
        'user_id': userId,
        'has_backup_codes': activeCount > 0,
        'active_codes': activeCount,
        'used_codes': codes.where((c) => c.isUsed).length,
        'total_codes': codes.length,
        'last_generated': codes.isNotEmpty 
            ? codes.map((c) => c.createdAt).reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
            : null,
        'low_codes_warning': activeCount <= 2 && activeCount > 0,
      };
      
      return Response.ok(
        jsonEncode({'success': true, 'data': status}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _generateBackupCodes(Request request) async {
    try {
      final userId = request.params['userId']!;
      final body = jsonDecode(await request.readAsString());
      
      final config = body['config'] != null
          ? _convertBackupCodeConfig(shared.BackupCodeGenerationConfig.fromJson(body['config']))
          : _createDefaultBackupCodeConfig();      final codeSet = await _backupCodesService.generateBackupCodes(
        userId,
        config: config,
        ipAddress: _getClientIP(request),
        userAgent: request.headers['user-agent'],
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': codeSet.toJson(),
          'message': 'Backup codes generated successfully',
          'warning': 'Store these codes securely. They will not be shown again.'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _validateBackupCode(Request request) async {
    try {
      final userId = request.params['userId']!;
      final body = jsonDecode(await request.readAsString());
      final code = body['code'] as String;
      final consumeOnUse = body['consume_on_use'] as bool? ?? true;
      
      final result = await _backupCodesService.validateBackupCode(
        userId,
        code,
        ipAddress: _getClientIP(request),
        userAgent: request.headers['user-agent'],
        consumeOnUse: consumeOnUse,
      );
      
      return Response.ok(
        jsonEncode({
          'success': result.isValid,
          'data': result.toJson(),
          'message': result.isValid ? 'Backup code validated' : 'Invalid backup code'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _revokeBackupCodes(Request request) async {
    try {
      final userId = request.params['userId']!;
      final body = jsonDecode(await request.readAsString());
      final specificCodes = body['specific_codes'] as List<String>?;
      final reason = body['reason'] as String? ?? 'Manual revocation';
      
      await _backupCodesService.revokeBackupCodes(
        userId,
        specificCodes: specificCodes,
        reason: reason,
        ipAddress: _getClientIP(request),
        userAgent: request.headers['user-agent'],
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': specificCodes != null 
              ? 'Specific backup codes revoked'
              : 'All backup codes revoked'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Trusted Devices Endpoints
  Future<Response> _getTrustedDevices(Request request) async {
    try {
      final userId = request.params['userId']!;
      final activeOnly = request.url.queryParameters['active_only'] == 'true';
      
      final devices = await _trustedDeviceService.getUserDevices(userId, activeOnly: activeOnly);
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': devices.map((d) => d.toJson()).toList(),
          'count': devices.length,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _registerTrustedDevice(Request request) async {
    try {
      final userId = request.params['userId']!;
      final body = jsonDecode(await request.readAsString());
      
      final registrationRequest = DeviceRegistrationRequest(
        deviceFingerprint: body['device_fingerprint'] as String,
        deviceName: body['device_name'] as String,
        deviceType: DeviceType.values.byName(body['device_type'] as String? ?? 'unknown'),
        deviceModel: body['device_model'] as String?,
        operatingSystem: body['operating_system'] as String?,
        browserInfo: body['browser_info'] as String?,
        ipAddress: _getClientIP(request) ?? '127.0.0.1',
        userAgent: request.headers['user-agent'],
        trustImmediately: body['trust_immediately'] as bool? ?? false,
        trustDuration: body['trust_duration_hours'] != null 
            ? Duration(hours: body['trust_duration_hours'] as int)
            : null,
        metadata: body['metadata'] as Map<String, dynamic>? ?? {},
      );
      
      final device = await _trustedDeviceService.registerDevice(userId, registrationRequest);
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': device.toJson(),
          'message': 'Device registered successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _verifyTrustedDevice(Request request) async {
    try {
      final userId = request.params['userId']!;
      final body = jsonDecode(await request.readAsString());
      final deviceFingerprint = body['device_fingerprint'] as String;
      
      final result = await _trustedDeviceService.verifyDevice(
        userId,
        deviceFingerprint,
        ipAddress: _getClientIP(request),
        userAgent: request.headers['user-agent'],
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': result.toJson(),
          'message': result.warningMessage ?? (result.isTrusted ? 'Device verified successfully' : 'Device verification required')
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _trustDevice(Request request) async {
    try {
      final userId = request.params['userId']!;
      final deviceId = request.params['deviceId']!;
      final body = jsonDecode(await request.readAsString());
      
      final trustDurationHours = body['trust_duration_hours'] as int?;
      final reason = body['reason'] as String? ?? 'Manual trust';
      final trustedBy = body['trusted_by'] as String?;
      
      await _trustedDeviceService.trustDevice(
        userId,
        deviceId,
        trustDuration: trustDurationHours != null ? Duration(hours: trustDurationHours) : null,
        trustedBy: trustedBy,
        reason: reason,
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Device trusted successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _revokeTrustedDevice(Request request) async {
    try {
      final userId = request.params['userId']!;
      final deviceId = request.params['deviceId']!;
      final body = await request.readAsString();
      
      String reason = 'Manual revocation';
      String? revokedBy;
      
      if (body.isNotEmpty) {
        final data = jsonDecode(body);
        reason = data['reason'] as String? ?? reason;
        revokedBy = data['revoked_by'] as String?;
      }
      
      await _trustedDeviceService.revokeDevice(
        userId,
        deviceId,
        revokedBy: revokedBy,
        reason: reason,
      );
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Device revoked successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getDeviceStats(Request request) async {
    try {
      final userId = request.params['userId']!;
      
      final devices = await _trustedDeviceService.getUserDevices(userId);
      
      final stats = {
        'total_devices': devices.length,
        'active_devices': devices.where((d) => d.status == DeviceStatus.active).length,
        'trusted_devices': devices.where((d) => d.trustedAt != null && d.status == DeviceStatus.active).length,
        'suspicious_devices': devices.where((d) => d.status == DeviceStatus.suspicious).length,
        'revoked_devices': devices.where((d) => d.status == DeviceStatus.revoked).length,
        'last_activity': devices.isNotEmpty 
            ? devices.map((d) => d.lastSeen).reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
            : null,
      };
      
      return Response.ok(
        jsonEncode({'success': true, 'data': stats}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // MFA Settings Endpoints
  Future<Response> _getMFASettings(Request request) async {
    try {
      final userId = request.params['userId']!;
      
      // Get comprehensive MFA settings for the user
      final totpSetup = await _getTOTPSetupStatus(userId);
      final backupCodesCount = await _backupCodesService.getActiveCodeCount(userId);
      final trustedDevices = await _trustedDeviceService.getUserDevices(userId, activeOnly: true);
      
      final userInfo = await _databaseService.query(
        'SELECT phone, email FROM users WHERE id = @user_id',
        {'user_id': userId},
      );
      
      final settings = {
        'user_id': userId,
        'mfa_enabled': totpSetup['is_enabled'] == true,
        'totp_configured': totpSetup['is_configured'] == true,
        'sms_configured': userInfo.isNotEmpty && userInfo.first['phone'] != null,
        'email_configured': userInfo.isNotEmpty && userInfo.first['email'] != null,
        'backup_codes_available': backupCodesCount > 0,
        'backup_codes_count': backupCodesCount,
        'trusted_devices_count': trustedDevices.length,
        'recovery_options': _buildRecoveryOptions(totpSetup, backupCodesCount, userInfo),
        'last_updated': DateTime.now().toIso8601String(),
      };
      
      return Response.ok(
        jsonEncode({'success': true, 'data': settings}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _updateMFASettings(Request request) async {
    try {
      // This would update user's MFA preferences
      // For now, return success response
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'MFA settings updated successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _disableMFA(Request request) async {
    try {
      // In a real implementation, this would require additional verification
      // and would disable all MFA methods for the user
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'MFA disabled successfully',
          'warning': 'Account security has been reduced. Consider re-enabling MFA.'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Recovery Endpoints
  Future<Response> _initiateAccountRecovery(Request request) async {
    try {
      // This would initiate account recovery process
      return Response.ok(
        jsonEncode({
          'success': true,
          'recovery_id': 'recovery_${DateTime.now().millisecondsSinceEpoch}',
          'message': 'Account recovery initiated'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _verifyAccountRecovery(Request request) async {
    try {
      // This would verify the recovery process
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Recovery verification successful'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _completeAccountRecovery(Request request) async {
    try {
      // This would complete the recovery and allow MFA reset
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Account recovery completed successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Administrative Endpoints
  Future<Response> _getMFAPolicies(Request request) async {
    try {
      // This would return MFA policies for administrators
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': [],
          'message': 'MFA policies retrieved'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _createMFAPolicy(Request request) async {
    try {
      // This would create a new MFA policy
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'MFA policy created successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _updateMFAPolicy(Request request) async {
    try {
      // This would update an existing MFA policy
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'MFA policy updated successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _deleteMFAPolicy(Request request) async {
    try {
      // This would delete an MFA policy
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'MFA policy deleted successfully'
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Helper methods
  String? _getClientIP(Request request) {
    try {
      // Try to get from headers first
      final xForwardedFor = request.headers['x-forwarded-for'];
      if (xForwardedFor != null && xForwardedFor.isNotEmpty) {
        return xForwardedFor.split(',').first.trim();
      }
      
      final xRealIP = request.headers['x-real-ip'];
      if (xRealIP != null && xRealIP.isNotEmpty) {
        return xRealIP;
      }
      
      // Fallback - this might not work but we'll handle the error
      return null;
    } catch (e) {
      return null;
    }
  }

  String _generateVerificationCode({int length = 6}) {
    final random = Random.secure();
    final code = StringBuffer();
    for (int i = 0; i < length; i++) {
      code.write(random.nextInt(10));
    }
    return code.toString();
  }

  String _maskPhoneNumber(String phone) {
    if (phone.length < 4) return phone;
    return '***-***-${phone.substring(phone.length - 4)}';
  }

  String _maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2 || parts[0].length < 2) return email;
    return '${parts[0].substring(0, 2)}***@${parts[1]}';
  }

  List<String> _buildRecoveryOptions(dynamic totpSetup, int backupCodesCount, List<Map<String, dynamic>> userInfo) {
    final options = <String>[];
    
    if (totpSetup?.isVerified == true) {
      options.add('totp_app');
    }
    
    if (backupCodesCount > 0) {
      options.add('backup_codes');
    }
    
    if (userInfo.isNotEmpty && userInfo.first['phone'] != null) {
      options.add('sms');
    }
    
    if (userInfo.isNotEmpty && userInfo.first['email'] != null) {
      options.add('email');
    }
    
    return options;
  }

  Future<void> _storePhoneVerification(String userId, String phoneNumber, String verificationCode) async {
    // Store phone verification temporarily
    await _databaseService.execute('''
      INSERT INTO temp_verifications (id, user_id, type, destination, code, expires_at)
      VALUES (@id, @user_id, 'phone', @destination, @code, NOW() + INTERVAL '10 minutes')
      ON CONFLICT (user_id, type) DO UPDATE SET
        destination = EXCLUDED.destination,
        code = EXCLUDED.code,
        expires_at = EXCLUDED.expires_at
    ''', parameters: {
      'id': 'phone_${userId}_${DateTime.now().millisecondsSinceEpoch}',
      'user_id': userId,
      'destination': phoneNumber,
      'code': verificationCode,
    });
  }

  Future<void> _storeEmailVerification(String userId, String email, String verificationCode) async {
    // Store email verification temporarily
    await _databaseService.execute('''
      INSERT INTO temp_verifications (id, user_id, type, destination, code, expires_at)
      VALUES (@id, @user_id, 'email', @destination, @code, NOW() + INTERVAL '10 minutes')
      ON CONFLICT (user_id, type) DO UPDATE SET
        destination = EXCLUDED.destination,
        code = EXCLUDED.code,
        expires_at = EXCLUDED.expires_at
    ''', parameters: {
      'id': 'email_${userId}_${DateTime.now().millisecondsSinceEpoch}',
      'user_id': userId,
      'destination': email,
      'code': verificationCode,
    });
  }

  // Helper method to get TOTP setup status
  Future<Map<String, dynamic>> _getTOTPSetupStatus(String userId) async {
    try {
      final result = await _databaseService.query(
        'SELECT secret_key, is_enabled, verified_at FROM user_mfa_settings WHERE user_id = @user_id',
        {'user_id': userId},
      );
      
      if (result.isEmpty) {
        return {
          'is_configured': false,
          'is_enabled': false,
          'verified_at': null,
        };
      }
      
      final row = result.first;
      return {
        'is_configured': row['secret_key'] != null,
        'is_enabled': row['is_enabled'] ?? false,
        'verified_at': row['verified_at'],
      };
    } catch (e) {
      return {
        'is_configured': false,
        'is_enabled': false,
        'verified_at': null,
      };
    }
  }

  // Helper methods for converting between shared and server models
  BackupCodeGenerationConfig _convertBackupCodeConfig(shared.BackupCodeGenerationConfig sharedConfig) {
    return BackupCodeGenerationConfig(
      codeCount: sharedConfig.codeCount,
      codeLength: sharedConfig.codeLength,
      includeHyphens: sharedConfig.includeHyphens,
      charset: sharedConfig.charset,
      maxCodesPerUser: sharedConfig.maxCodesPerUser,
      codeLifetime: sharedConfig.codeLifetime,
      revokeExistingCodes: sharedConfig.revokeExistingCodes,
    );
  }

  BackupCodeGenerationConfig _createDefaultBackupCodeConfig() {
    return const BackupCodeGenerationConfig();
  }

  // Helper method to convert between shared and policy engine MFAMethod enums
  policy.MFAMethod _convertToPolicyMethod(String methodName) {
    switch (methodName) {
      case 'totp':
        return policy.MFAMethod.totp;
      case 'sms':
        return policy.MFAMethod.sms;
      case 'email':
        return policy.MFAMethod.email;
      case 'backupCodes':
      case 'backupCode':
        return policy.MFAMethod.backupCode;
      case 'trustedDevice':
        return policy.MFAMethod.trustedDevice;
      default:
        return policy.MFAMethod.totp;
    }
  }
}

extension MFAHandlersRegistration on DatabaseService {
  MFAHandlers createMFAHandlers(
    policy.MFAPolicyEngine policyEngine,
    TOTPService totpService,
    MFADeliveryService deliveryService,
    BackupCodesService backupCodesService,
    TrustedDeviceService trustedDeviceService,
  ) => MFAHandlers(
    this,
    policyEngine,
    totpService,
    deliveryService,
    backupCodesService,
    trustedDeviceService,
  );
}