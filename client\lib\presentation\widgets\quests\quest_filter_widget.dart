import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Widget for filtering quests by various criteria
class QuestFilterWidget extends StatelessWidget {
  /// Currently selected priority filter
  final QuestPriority? selectedPriority;
  
  /// Currently selected type filter
  final QuestType? selectedType;
  
  /// Currently selected status filter
  final QuestStatus? selectedStatus;
  
  /// Date range filter
  final DateTimeRange? dateRange;
  
  /// Callback when priority filter changes
  final Function(QuestPriority?)? onPriorityChanged;
  
  /// Callback when type filter changes
  final Function(QuestType?)? onTypeChanged;
  
  /// Callback when status filter changes
  final Function(QuestStatus?)? onStatusChanged;
  
  /// Callback when date range changes
  final Function(DateTimeRange?)? onDateRangeChanged;
  
  /// Callback to clear all filters
  final VoidCallback? onClearFilters;

  const QuestFilterWidget({
    super.key,
    this.selectedPriority,
    this.selectedType,
    this.selectedStatus,
    this.dateRange,
    this.onPriorityChanged,
    this.onTypeChanged,
    this.onStatusChanged,
    this.onDateRangeChanged,
    this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filters',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (_hasActiveFilters())
                TextButton(
                  onPressed: onClearFilters,
                  child: const Text('Clear All'),
                ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Priority Filter
          _buildFilterSection(
            context,
            title: 'Priority',
            child: _buildPriorityFilter(context),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Type Filter
          _buildFilterSection(
            context,
            title: 'Type',
            child: _buildTypeFilter(context),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Status Filter
          _buildFilterSection(
            context,
            title: 'Status',
            child: _buildStatusFilter(context),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Date Range Filter
          _buildFilterSection(
            context,
            title: 'Due Date',
            child: _buildDateRangeFilter(context),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        child,
      ],
    );
  }

  Widget _buildPriorityFilter(BuildContext context) {
    return Wrap(
      spacing: AppConstants.smallPadding,
      runSpacing: AppConstants.smallPadding,
      children: [
        _buildFilterChip(
          context,
          label: 'All',
          isSelected: selectedPriority == null,
          onTap: () => onPriorityChanged?.call(null),
        ),
        ...QuestPriority.values.map((priority) =>
          _buildFilterChip(
            context,
            label: priority.name.toUpperCase(),
            isSelected: selectedPriority == priority,
            color: _getPriorityColor(priority),
            onTap: () => onPriorityChanged?.call(priority),
          ),
        ),
      ],
    );
  }

  Widget _buildTypeFilter(BuildContext context) {
    return Wrap(
      spacing: AppConstants.smallPadding,
      runSpacing: AppConstants.smallPadding,
      children: [
        _buildFilterChip(
          context,
          label: 'All',
          isSelected: selectedType == null,
          onTap: () => onTypeChanged?.call(null),
        ),
        ...QuestType.values.map((type) =>
          _buildFilterChip(
            context,
            label: _getTypeDisplayName(type),
            isSelected: selectedType == type,
            icon: _getTypeIcon(type),
            onTap: () => onTypeChanged?.call(type),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusFilter(BuildContext context) {
    return Wrap(
      spacing: AppConstants.smallPadding,
      runSpacing: AppConstants.smallPadding,
      children: [
        _buildFilterChip(
          context,
          label: 'All',
          isSelected: selectedStatus == null,
          onTap: () => onStatusChanged?.call(null),
        ),
        ...QuestStatus.values.map((status) =>
          _buildFilterChip(
            context,
            label: status.name.toUpperCase(),
            isSelected: selectedStatus == status,
            color: _getStatusColor(status),
            icon: _getStatusIcon(status),
            onTap: () => onStatusChanged?.call(status),
          ),
        ),
      ],
    );
  }

  Widget _buildDateRangeFilter(BuildContext context) {
    return Column(
      children: [
        OutlinedButton.icon(
          onPressed: () => _showDateRangePicker(context),
          icon: const Icon(Icons.date_range),
          label: Text(
            dateRange != null
                ? '${_formatDate(dateRange!.start)} - ${_formatDate(dateRange!.end)}'
                : 'Select Date Range',
          ),
        ),
        if (dateRange != null) ...[
          const SizedBox(height: AppConstants.smallPadding),
          TextButton(
            onPressed: () => onDateRangeChanged?.call(null),
            child: const Text('Clear Date Range'),
          ),
        ],
      ],
    );
  }

  Widget _buildFilterChip(
    BuildContext context, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    Color? color,
    IconData? icon,
  }) {
    final effectiveColor = color ?? Theme.of(context).colorScheme.primary;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? effectiveColor.withOpacity(0.2)
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
          border: Border.all(
            color: isSelected
                ? effectiveColor
                : Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 16,
                color: isSelected
                    ? effectiveColor
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? effectiveColor
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _hasActiveFilters() {
    return selectedPriority != null ||
           selectedType != null ||
           selectedStatus != null ||
           dateRange != null;
  }

  Color _getPriorityColor(QuestPriority priority) {
    switch (priority) {
      case QuestPriority.low:
        return Colors.green;
      case QuestPriority.medium:
        return Colors.orange;
      case QuestPriority.high:
        return Colors.red;
      case QuestPriority.urgent:
        return Colors.purple;
    }
  }

  Color _getStatusColor(QuestStatus status) {
    switch (status) {
      case QuestStatus.draft:
        return Colors.grey;
      case QuestStatus.active:
        return Colors.blue;
      case QuestStatus.paused:
        return Colors.orange;
      case QuestStatus.completed:
        return Colors.green;
      case QuestStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(QuestStatus status) {
    switch (status) {
      case QuestStatus.draft:
        return Icons.edit;
      case QuestStatus.active:
        return Icons.play_arrow;
      case QuestStatus.paused:
        return Icons.pause;
      case QuestStatus.completed:
        return Icons.check_circle;
      case QuestStatus.cancelled:
        return Icons.cancel;
    }
  }

  IconData _getTypeIcon(QuestType type) {
    switch (type) {
      case QuestType.personal:
        return Icons.person;
      case QuestType.team:
        return Icons.group;
      case QuestType.learning:
        return Icons.school;
      case QuestType.work:
        return Icons.work;
      case QuestType.health:
        return Icons.favorite;
      case QuestType.creative:
        return Icons.palette;
      case QuestType.social:
        return Icons.people;
      case QuestType.other:
        return Icons.explore;
    }
  }

  String _getTypeDisplayName(QuestType type) {
    switch (type) {
      case QuestType.personal:
        return 'Personal';
      case QuestType.team:
        return 'Team';
      case QuestType.learning:
        return 'Learning';
      case QuestType.work:
        return 'Work';
      case QuestType.health:
        return 'Health';
      case QuestType.creative:
        return 'Creative';
      case QuestType.social:
        return 'Social';
      case QuestType.other:
        return 'Other';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDateRangePicker(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: dateRange,
    );
    
    if (picked != null) {
      onDateRangeChanged?.call(picked);
    }
  }
}

/// Compact version of quest filter widget for smaller spaces
class CompactQuestFilterWidget extends StatelessWidget {
  /// Currently selected priority filter
  final QuestPriority? selectedPriority;
  
  /// Currently selected type filter
  final QuestType? selectedType;
  
  /// Callback when priority filter changes
  final Function(QuestPriority?)? onPriorityChanged;
  
  /// Callback when type filter changes
  final Function(QuestType?)? onTypeChanged;

  const CompactQuestFilterWidget({
    super.key,
    this.selectedPriority,
    this.selectedType,
    this.onPriorityChanged,
    this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: DropdownButtonFormField<QuestPriority?>(
            value: selectedPriority,
            decoration: const InputDecoration(
              labelText: 'Priority',
              border: OutlineInputBorder(),
              isDense: true,
            ),
            items: [
              const DropdownMenuItem(value: null, child: Text('All')),
              ...QuestPriority.values.map((priority) =>
                DropdownMenuItem(
                  value: priority,
                  child: Text(priority.name.toUpperCase()),
                ),
              ),
            ],
            onChanged: onPriorityChanged,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: DropdownButtonFormField<QuestType?>(
            value: selectedType,
            decoration: const InputDecoration(
              labelText: 'Type',
              border: OutlineInputBorder(),
              isDense: true,
            ),
            items: [
              const DropdownMenuItem(value: null, child: Text('All')),
              ...QuestType.values.map((type) =>
                DropdownMenuItem(
                  value: type,
                  child: Text(_getTypeDisplayName(type)),
                ),
              ),
            ],
            onChanged: onTypeChanged,
          ),
        ),
      ],
    );
  }

  String _getTypeDisplayName(QuestType type) {
    switch (type) {
      case QuestType.personal:
        return 'Personal';
      case QuestType.team:
        return 'Team';
      case QuestType.learning:
        return 'Learning';
      case QuestType.work:
        return 'Work';
      case QuestType.health:
        return 'Health';
      case QuestType.creative:
        return 'Creative';
      case QuestType.social:
        return 'Social';
      case QuestType.other:
        return 'Other';
    }
  }
}
