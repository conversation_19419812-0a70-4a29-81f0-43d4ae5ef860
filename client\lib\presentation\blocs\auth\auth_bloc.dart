import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/api_repository.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();
  
  @override
  List<Object?> get props => [];
}

class LoginRequested extends AuthEvent {
  final String email;
  final String password;
  
  const LoginRequested({required this.email, required this.password});
  
  @override
  List<Object?> get props => [email, password];
}

class RegisterRequested extends AuthEvent {
  final String email;
  final String password;
  final String name;
  
  const RegisterRequested({required this.email, required this.password, required this.name});
  
  @override
  List<Object?> get props => [email, password, name];
}

class LogoutRequested extends AuthEvent {
  const LogoutRequested();
}

class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

class PasswordResetRequested extends AuthEvent {
  final String email;

  const PasswordResetRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class PasswordChangeRequested extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  const PasswordChangeRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

class ProfileUpdateRequested extends AuthEvent {
  final String displayName;
  final String? avatarUrl;

  const ProfileUpdateRequested({
    required this.displayName,
    this.avatarUrl,
  });

  @override
  List<Object?> get props => [displayName, avatarUrl];
}

class RefreshTokenRequested extends AuthEvent {
  const RefreshTokenRequested();
}

// States
abstract class AuthState extends Equatable {
  const AuthState();
  
  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final User user;
  
  const AuthAuthenticated({required this.user});
  
  @override
  List<Object?> get props => [user];
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  final String message;

  const AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

class PasswordResetSent extends AuthState {
  final String email;

  const PasswordResetSent({required this.email});

  @override
  List<Object?> get props => [email];
}

class PasswordChanged extends AuthState {
  const PasswordChanged();
}

class ProfileUpdated extends AuthState {
  final User user;

  const ProfileUpdated({required this.user});

  @override
  List<Object?> get props => [user];
}

// BLoC
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final ApiRepository repository;
  
  AuthBloc({required this.repository}) : super(const AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<RegisterRequested>(_onRegisterRequested);
    on<LogoutRequested>(_onLogoutRequested);
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<PasswordResetRequested>(_onPasswordResetRequested);
    on<PasswordChangeRequested>(_onPasswordChangeRequested);
    on<ProfileUpdateRequested>(_onProfileUpdateRequested);
    on<RefreshTokenRequested>(_onRefreshTokenRequested);
  }

  Future<void> _onLoginRequested(LoginRequested event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());
    
    final response = await repository.login(event.email, event.password);
    
    if (response.isSuccess && response.data != null) {
      emit(AuthAuthenticated(user: response.data!));
    } else {
      emit(AuthError(message: response.error ?? 'Login failed'));
    }
  }

  Future<void> _onRegisterRequested(RegisterRequested event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());
    
    final response = await repository.register(event.email, event.password, event.name);
    
    if (response.isSuccess && response.data != null) {
      emit(AuthAuthenticated(user: response.data!));
    } else {
      emit(AuthError(message: response.error ?? 'Registration failed'));
    }
  }

  Future<void> _onLogoutRequested(LogoutRequested event, Emitter<AuthState> emit) async {
    await repository.logout();
    emit(const AuthUnauthenticated());
  }

  Future<void> _onAuthCheckRequested(AuthCheckRequested event, Emitter<AuthState> emit) async {
    try {
      final response = await repository.checkAuthStatus();
      if (response.isSuccess && response.data != null) {
        final user = User.fromJson(response.data!);
        emit(AuthAuthenticated(user: user));
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(const AuthUnauthenticated());
    }
  }

  Future<void> _onPasswordResetRequested(PasswordResetRequested event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());

    try {
      final response = await repository.requestPasswordReset(event.email);
      if (response.isSuccess) {
        emit(PasswordResetSent(email: event.email));
      } else {
        emit(AuthError(message: response.error ?? 'Password reset failed'));
      }
    } catch (e) {
      emit(AuthError(message: 'Password reset failed: $e'));
    }
  }

  Future<void> _onPasswordChangeRequested(PasswordChangeRequested event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());

    try {
      final response = await repository.changePassword(
        event.currentPassword,
        event.newPassword,
      );
      if (response.isSuccess) {
        emit(const PasswordChanged());
      } else {
        emit(AuthError(message: response.error ?? 'Password change failed'));
      }
    } catch (e) {
      emit(AuthError(message: 'Password change failed: $e'));
    }
  }

  Future<void> _onProfileUpdateRequested(ProfileUpdateRequested event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());

    try {
      final response = await repository.updateProfile({
        'displayName': event.displayName,
        'avatarUrl': event.avatarUrl,
      });
      if (response.isSuccess && response.data != null) {
        emit(ProfileUpdated(user: response.data!));
        emit(AuthAuthenticated(user: response.data!));
      } else {
        emit(AuthError(message: response.error ?? 'Profile update failed'));
      }
    } catch (e) {
      emit(AuthError(message: 'Profile update failed: $e'));
    }
  }

  Future<void> _onRefreshTokenRequested(RefreshTokenRequested event, Emitter<AuthState> emit) async {
    try {
      final response = await repository.refreshToken();
      if (response.isSuccess && response.data != null) {
        // For refresh token, we need to get the current user from auth service
        final currentUser = repository.authService.currentUser;
        if (currentUser != null) {
          emit(AuthAuthenticated(user: currentUser));
        } else {
          emit(const AuthUnauthenticated());
        }
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(const AuthUnauthenticated());
    }
  }
}