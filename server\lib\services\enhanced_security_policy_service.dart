/// Enhanced Security Policy Enforcement Service
library;

import '../services/database_service.dart';
import 'threat_detection_service.dart';

/// Policy enforcement result
class PolicyEnforcementResult {
  final bool allowed;
  final List<String> violations;
  final List<String> warnings;
  final double riskScore;
  final Map<String, dynamic> requiredActions;
  final String? blockReason;

  const PolicyEnforcementResult({
    required this.allowed,
    this.violations = const [],
    this.warnings = const [],
    required this.riskScore,
    this.requiredActions = const {},
    this.blockReason,
  });

  bool get hasViolations => violations.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get requiresAction => requiredActions.isNotEmpty;
}

/// Enhanced security policy enforcement service
class EnhancedSecurityPolicyService {
  // ignore: unused_field
  final DatabaseService _databaseService;
  // ignore: unused_field
  final ThreatDetectionService _threatDetectionService;

  // Policy enforcement thresholds
  static const double _criticalRiskThreshold = 0.9;
  static const double _highRiskThreshold = 0.7;
  // ignore: unused_field
  static const double _mediumRiskThreshold = 0.5;

  // Policy categories
  static const String _authenticationPolicy = 'authentication';
  // ignore: unused_field
  static const String _accessControlPolicy = 'access_control';
  static const String _dataProtectionPolicy = 'data_protection';
  static const String _networkSecurityPolicy = 'network_security';
  // ignore: unused_field
  static const String _deviceSecurityPolicy = 'device_security';

  EnhancedSecurityPolicyService(this._databaseService, this._threatDetectionService);

  /// Evaluate comprehensive security policies for user action
  Future<PolicyEnforcementResult> evaluateSecurityPolicies({
    required String organizationId,
    required String userId,
    required String action,
    required Map<String, dynamic> context,
  }) async {
    try {
      final violations = <String>[];
      final warnings = <String>[];
      final requiredActions = <String, dynamic>{};
      double totalRiskScore = 0.0;

      // Get organization security policies
      final securityPolicies = await _getOrganizationSecurityPolicies(organizationId);
      
      // Evaluate each policy category
      for (final policy in securityPolicies) {
        final result = await _evaluatePolicy(policy, userId, action, context);
        
        violations.addAll(result.violations);
        warnings.addAll(result.warnings);
        totalRiskScore += result.riskScore;
        
        if (result.requiredActions.isNotEmpty) {
          requiredActions.addAll(result.requiredActions);
        }
      }

      // Normalize risk score
      totalRiskScore = totalRiskScore / securityPolicies.length.clamp(1, double.infinity);

      // Determine if action should be allowed
      final allowed = _determineAllowedStatus(violations, totalRiskScore);
      final blockReason = allowed ? null : _generateBlockReason(violations, totalRiskScore);

      return PolicyEnforcementResult(
        allowed: allowed,
        violations: violations,
        warnings: warnings,
        riskScore: totalRiskScore,
        requiredActions: requiredActions,
        blockReason: blockReason,
      );

    } catch (e) {
      print('Error evaluating security policies: $e');
      return const PolicyEnforcementResult(
        allowed: true,
        riskScore: 0.0,
      );
    }
  }

  /// Evaluate authentication security policies
  Future<PolicyEnforcementResult> evaluateAuthenticationPolicy({
    required String organizationId,
    required String userId,
    required String sourceIp,
    required Map<String, dynamic> loginContext,
  }) async {
    try {
      final violations = <String>[];
      final warnings = <String>[];
      final requiredActions = <String, dynamic>{};
      double riskScore = 0.0;

      // Get authentication policies
      final authPolicies = await _getAuthenticationPolicies(organizationId);

      // Evaluate password policy
      if (loginContext.containsKey('password_strength')) {
        final passwordStrength = loginContext['password_strength'] as double;
        if (passwordStrength < authPolicies['min_password_strength']) {
          violations.add('Password does not meet strength requirements');
          riskScore += 0.3;
        }
      }

      // Evaluate MFA requirements
      if (authPolicies['mfa_required'] == true) {
        final mfaProvided = loginContext['mfa_provided'] as bool? ?? false;
        if (!mfaProvided) {
          requiredActions['mfa_required'] = {
            'type': 'mfa_challenge',
            'methods': authPolicies['allowed_mfa_methods'],
            'message': 'Multi-factor authentication is required',
          };
          riskScore += 0.4;
        }
      }

      // Evaluate session policies
      final lastLoginTime = loginContext['last_login'] as String?;
      if (lastLoginTime != null) {
        final lastLogin = DateTime.parse(lastLoginTime);
        final daysSinceLastLogin = DateTime.now().difference(lastLogin).inDays;
        
        if (daysSinceLastLogin > authPolicies['max_idle_days']) {
          violations.add('Account has been idle beyond policy limits');
          requiredActions['identity_verification'] = {
            'type': 'identity_verification',
            'reason': 'Extended idle period',
          };
          riskScore += 0.2;
        }
      }

      // Evaluate device trust
      final deviceTrusted = loginContext['device_trusted'] as bool? ?? false;
      if (!deviceTrusted && authPolicies['require_trusted_devices'] == true) {
        warnings.add('Login from untrusted device');
        requiredActions['device_verification'] = {
          'type': 'device_verification',
          'message': 'Please verify this device for future logins',
        };
        riskScore += 0.15;
      }

      // Check for concurrent sessions
      final activeSessions = loginContext['active_sessions'] as int? ?? 0;
      if (activeSessions >= authPolicies['max_concurrent_sessions']) {
        violations.add('Maximum concurrent sessions exceeded');
        riskScore += 0.25;
      }

      final allowed = violations.isEmpty && riskScore < _highRiskThreshold;

      return PolicyEnforcementResult(
        allowed: allowed,
        violations: violations,
        warnings: warnings,
        riskScore: riskScore.clamp(0.0, 1.0),
        requiredActions: requiredActions,
        blockReason: allowed ? null : 'Authentication policy violations detected',
      );

    } catch (e) {
      print('Error evaluating authentication policy: $e');
      return const PolicyEnforcementResult(
        allowed: true,
        riskScore: 0.0,
      );
    }
  }

  /// Evaluate data access policies
  Future<PolicyEnforcementResult> evaluateDataAccessPolicy({
    required String organizationId,
    required String userId,
    required String resourceType,
    required String action,
    required Map<String, dynamic> resourceContext,
  }) async {
    try {
      final violations = <String>[];
      final warnings = <String>[];
      final requiredActions = <String, dynamic>{};
      double riskScore = 0.0;

      // Get data protection policies
      final dataPolicies = await _getDataProtectionPolicies(organizationId);

      // Check data classification requirements
      final dataClassification = resourceContext['classification'] as String? ?? 'public';
      final userClearanceLevel = resourceContext['user_clearance'] as String? ?? 'basic';

      if (!_hasDataClearance(userClearanceLevel, dataClassification)) {
        violations.add('Insufficient clearance level for data classification');
        riskScore += 0.6;
      }

      // Check for data export restrictions
      if (action == 'export' || action == 'download') {
        if (dataPolicies['export_restrictions'].contains(dataClassification)) {
          violations.add('Data export not allowed for this classification level');
          riskScore += 0.5;
        }

        // Check for bulk export limits
        final requestedCount = resourceContext['item_count'] as int? ?? 1;
        if (requestedCount > dataPolicies['max_bulk_export']) {
          violations.add('Bulk export limit exceeded');
          riskScore += 0.3;
        }
      }

      // Check time-based access restrictions
      final now = DateTime.now();
      final businessHours = dataPolicies['business_hours_only'] as bool? ?? false;
      if (businessHours && _isOutsideBusinessHours(now)) {
        warnings.add('Accessing sensitive data outside business hours');
        requiredActions['business_justification'] = {
          'type': 'justification_required',
          'message': 'Please provide business justification for after-hours access',
        };
        riskScore += 0.2;
      }

      // Check for data retention policies
      final dataAge = resourceContext['data_age_days'] as int? ?? 0;
      final maxRetentionDays = dataPolicies['max_retention_days'] as int? ?? 2555; // ~7 years
      if (dataAge > maxRetentionDays) {
        violations.add('Data exceeds retention policy limits');
        riskScore += 0.4;
      }

      // Evaluate geographic restrictions
      final userLocation = resourceContext['user_location'] as String?;
      if (userLocation != null) {
        final restrictedCountries = dataPolicies['geographic_restrictions'] as List<String>? ?? [];
        if (restrictedCountries.contains(userLocation)) {
          violations.add('Data access restricted from this geographic location');
          riskScore += 0.7;
        }
      }

      final allowed = violations.isEmpty && riskScore < _highRiskThreshold;

      return PolicyEnforcementResult(
        allowed: allowed,
        violations: violations,
        warnings: warnings,
        riskScore: riskScore.clamp(0.0, 1.0),
        requiredActions: requiredActions,
        blockReason: allowed ? null : 'Data access policy violations detected',
      );

    } catch (e) {
      print('Error evaluating data access policy: $e');
      return const PolicyEnforcementResult(
        allowed: true,
        riskScore: 0.0,
      );
    }
  }

  /// Evaluate network security policies
  Future<PolicyEnforcementResult> evaluateNetworkSecurityPolicy({
    required String organizationId,
    required String sourceIp,
    required String userAgent,
    required Map<String, dynamic> networkContext,
  }) async {
    try {
      final violations = <String>[];
      final warnings = <String>[];
      final requiredActions = <String, dynamic>{};
      double riskScore = 0.0;

      // Get network security policies
      final networkPolicies = await _getNetworkSecurityPolicies(organizationId);

      // Check IP whitelist/blacklist
      if (_isIPBlacklisted(sourceIp, networkPolicies['blacklist_ips'] ?? [])) {
        violations.add('Source IP is blacklisted');
        riskScore += 0.9;
      }

      final whitelistEnabled = networkPolicies['whitelist_enabled'] as bool? ?? false;
      if (whitelistEnabled && !_isIPWhitelisted(sourceIp, networkPolicies['whitelist_ips'] ?? [])) {
        violations.add('Source IP not in whitelist');
        riskScore += 0.6;
      }

      // Check for VPN/Proxy usage
      final isVPN = networkContext['is_vpn'] as bool? ?? false;
      final isTor = networkContext['is_tor'] as bool? ?? false;
      final vpnAllowed = networkPolicies['vpn_allowed'] as bool? ?? true;
      final torAllowed = networkPolicies['tor_allowed'] as bool? ?? false;

      if (isVPN && !vpnAllowed) {
        violations.add('VPN usage is not permitted');
        riskScore += 0.5;
      }

      if (isTor && !torAllowed) {
        violations.add('Tor usage is not permitted');
        riskScore += 0.8;
      }

      // Check user agent restrictions
      if (_isSuspiciousUserAgent(userAgent)) {
        warnings.add('Unusual user agent detected');
        riskScore += 0.2;
      }

      // Check for rate limiting violations
      final requestRate = networkContext['requests_per_minute'] as int? ?? 0;
      final maxRate = networkPolicies['max_requests_per_minute'] as int? ?? 100;
      if (requestRate > maxRate) {
        violations.add('Request rate limit exceeded');
        riskScore += 0.4;
      }

      final allowed = violations.isEmpty && riskScore < _highRiskThreshold;

      return PolicyEnforcementResult(
        allowed: allowed,
        violations: violations,
        warnings: warnings,
        riskScore: riskScore.clamp(0.0, 1.0),
        requiredActions: requiredActions,
        blockReason: allowed ? null : 'Network security policy violations detected',
      );

    } catch (e) {
      print('Error evaluating network security policy: $e');
      return const PolicyEnforcementResult(
        allowed: true,
        riskScore: 0.0,
      );
    }
  }

  /// Generate comprehensive security report
  Future<Map<String, dynamic>> generateSecurityComplianceReport(String organizationId) async {
    try {
      final report = <String, dynamic>{
        'organization_id': organizationId,
        'report_generated_at': DateTime.now().toIso8601String(),
        'compliance_score': 87.5,
        'policy_categories': {},
        'violations_summary': {},
        'recommendations': [],
        'risk_assessment': {},
      };

      // Mock compliance data - in production this would analyze actual policy violations
      report['policy_categories'] = {
        'authentication': {
          'compliance_score': 92,
          'active_policies': 8,
          'violations_24h': 3,
          'status': 'compliant',
        },
        'data_protection': {
          'compliance_score': 85,
          'active_policies': 12,
          'violations_24h': 7,
          'status': 'compliant',
        },
        'network_security': {
          'compliance_score': 89,
          'active_policies': 6,
          'violations_24h': 2,
          'status': 'compliant',
        },
        'device_security': {
          'compliance_score': 82,
          'active_policies': 5,
          'violations_24h': 9,
          'status': 'needs_attention',
        },
      };

      report['violations_summary'] = {
        'total_violations_24h': 21,
        'critical_violations': 2,
        'high_violations': 7,
        'medium_violations': 8,
        'low_violations': 4,
        'top_violation_types': [
          {'type': 'Weak Password', 'count': 6},
          {'type': 'Untrusted Device', 'count': 4},
          {'type': 'Geographic Anomaly', 'count': 3},
        ],
      };

      report['recommendations'] = [
        {
          'priority': 'high',
          'category': 'authentication',
          'title': 'Strengthen Password Policy',
          'description': 'Consider requiring longer passwords and additional complexity',
          'impact': 'Reduces brute force attack success rate by 60%',
        },
        {
          'priority': 'medium',
          'category': 'device_security',
          'title': 'Enable Automatic Device Trust',
          'description': 'Configure automatic device trust for frequently used devices',
          'impact': 'Reduces user friction while maintaining security',
        },
        {
          'priority': 'low',
          'category': 'network_security',
          'title': 'Review Geographic Restrictions',
          'description': 'Consider allowing access from additional trusted countries',
          'impact': 'Improves user experience for remote workers',
        },
      ];

      report['risk_assessment'] = {
        'overall_risk_level': 'medium',
        'risk_score': 0.35,
        'key_risks': [
          {
            'risk': 'Account Takeover',
            'probability': 'medium',
            'impact': 'high',
            'mitigation': 'Strengthen MFA requirements',
          },
          {
            'risk': 'Data Exfiltration',
            'probability': 'low',
            'impact': 'critical',
            'mitigation': 'Enhanced DLP monitoring',
          },
        ],
      };

      return report;

    } catch (e) {
      print('Error generating security compliance report: $e');
      return {
        'organization_id': organizationId,
        'error': 'Failed to generate report',
        'report_generated_at': DateTime.now().toIso8601String(),
      };
    }
  }

  // Helper methods

  /// Get organization security policies
  Future<List<Map<String, dynamic>>> _getOrganizationSecurityPolicies(String organizationId) async {
    // Mock policies - in production this would query the database
    return [
      {
        'category': _authenticationPolicy,
        'enabled': true,
        'settings': await _getAuthenticationPolicies(organizationId),
      },
      {
        'category': _dataProtectionPolicy,
        'enabled': true,
        'settings': await _getDataProtectionPolicies(organizationId),
      },
      {
        'category': _networkSecurityPolicy,
        'enabled': true,
        'settings': await _getNetworkSecurityPolicies(organizationId),
      },
    ];
  }

  /// Get authentication policies
  Future<Map<String, dynamic>> _getAuthenticationPolicies(String organizationId) async {
    return {
      'mfa_required': true,
      'allowed_mfa_methods': ['totp', 'sms', 'email'],
      'min_password_strength': 0.7,
      'max_idle_days': 90,
      'require_trusted_devices': false,
      'max_concurrent_sessions': 3,
      'session_timeout_minutes': 480,
    };
  }

  /// Get data protection policies
  Future<Map<String, dynamic>> _getDataProtectionPolicies(String organizationId) async {
    return {
      'export_restrictions': ['confidential', 'restricted'],
      'max_bulk_export': 1000,
      'business_hours_only': false,
      'max_retention_days': 2555,
      'geographic_restrictions': ['CN', 'RU', 'KP', 'IR'],
      'classification_levels': ['public', 'internal', 'confidential', 'restricted'],
    };
  }

  /// Get network security policies
  Future<Map<String, dynamic>> _getNetworkSecurityPolicies(String organizationId) async {
    return {
      'blacklist_ips': ['*********/24', '***********/24'],
      'whitelist_enabled': false,
      'whitelist_ips': ['************/24'],
      'vpn_allowed': true,
      'tor_allowed': false,
      'max_requests_per_minute': 100,
    };
  }

  /// Evaluate individual policy
  Future<PolicyEnforcementResult> _evaluatePolicy(
    Map<String, dynamic> policy,
    String userId,
    String action,
    Map<String, dynamic> context,
  ) async {
    final category = policy['category'] as String;

    switch (category) {
      case _authenticationPolicy:
        return await evaluateAuthenticationPolicy(
          organizationId: context['organization_id'],
          userId: userId,
          sourceIp: context['source_ip'] ?? '0.0.0.0',
          loginContext: context,
        );
      // Add other policy evaluations as needed
      default:
        return const PolicyEnforcementResult(allowed: true, riskScore: 0.0);
    }
  }

  /// Determine if action should be allowed
  bool _determineAllowedStatus(List<String> violations, double riskScore) {
    return violations.isEmpty && riskScore < _criticalRiskThreshold;
  }

  /// Generate block reason
  String _generateBlockReason(List<String> violations, double riskScore) {
    if (violations.isNotEmpty) {
      return 'Policy violations: ${violations.join(', ')}';
    }
    if (riskScore >= _criticalRiskThreshold) {
      return 'Risk score too high: ${(riskScore * 100).toStringAsFixed(1)}%';
    }
    return 'Security policy violation';
  }

  /// Check data clearance level
  bool _hasDataClearance(String userClearance, String dataClassification) {
    const clearanceLevels = ['basic', 'internal', 'confidential', 'restricted'];
    const classificationLevels = ['public', 'internal', 'confidential', 'restricted'];

    final userLevel = clearanceLevels.indexOf(userClearance);
    final dataLevel = classificationLevels.indexOf(dataClassification);

    return userLevel >= dataLevel;
  }

  /// Check if current time is outside business hours
  bool _isOutsideBusinessHours(DateTime time) {
    final hour = time.hour;
    final isWeekend = time.weekday == DateTime.saturday || time.weekday == DateTime.sunday;
    return isWeekend || hour < 9 || hour > 17;
  }

  /// Check if IP is blacklisted
  bool _isIPBlacklisted(String ip, List<String> blacklist) {
    // Simple check - in production this would handle CIDR ranges properly
    return blacklist.any((range) => ip.startsWith(range.split('/')[0].substring(0, 8)));
  }

  /// Check if IP is whitelisted
  bool _isIPWhitelisted(String ip, List<String> whitelist) {
    // Simple check - in production this would handle CIDR ranges properly
    return whitelist.any((range) => ip.startsWith(range.split('/')[0].substring(0, 8)));
  }

  /// Check for suspicious user agent
  bool _isSuspiciousUserAgent(String userAgent) {
    final suspicious = ['bot', 'crawler', 'scraper', 'automated'];
    return suspicious.any((term) => userAgent.toLowerCase().contains(term));
  }
}