-- Migration: Security Enhancements for Existing Tables
-- Version: 1.0.0
-- Date: 2025-08-21
-- Description: Add security-related columns and constraints to existing tables for SSO integration

BEGIN;

-- =============================================================================
-- USERS TABLE ENHANCEMENTS
-- =============================================================================

-- Add security-related columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS sso_external_id VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS sso_provider_id UUID REFERENCES sso_providers(id) ON DELETE SET NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_ip INET;
ALTER TABLE users ADD COLUMN IF NOT EXISTS login_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked_until TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_enabled BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_secret TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS backup_codes TEXT[];
ALTER TABLE users ADD COLUMN IF NOT EXISTS trusted_devices JSONB DEFAULT '[]'::jsonb;
ALTER TABLE users ADD COLUMN IF NOT EXISTS security_preferences JSONB DEFAULT '{}'::jsonb;
ALTER TABLE users ADD COLUMN IF NOT EXISTS two_factor_recovery_codes TEXT[];
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_verified_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS requires_password_change BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS security_questions JSONB DEFAULT '[]'::jsonb;

-- Add indexes for security-related queries on users table
CREATE INDEX IF NOT EXISTS idx_users_sso_external_id ON users(sso_external_id) WHERE sso_external_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_sso_provider_id ON users(sso_provider_id) WHERE sso_provider_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_last_login_at ON users(last_login_at) WHERE last_login_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_account_locked ON users(account_locked_until) WHERE account_locked_until IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_failed_attempts ON users(failed_login_attempts) WHERE failed_login_attempts > 0;
CREATE INDEX IF NOT EXISTS idx_users_mfa_enabled ON users(mfa_enabled) WHERE mfa_enabled = true;
CREATE INDEX IF NOT EXISTS idx_users_password_changed ON users(password_changed_at);
CREATE INDEX IF NOT EXISTS idx_users_requires_password_change ON users(requires_password_change) WHERE requires_password_change = true;

-- Add unique constraint for SSO external ID per provider
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_sso_unique 
ON users(sso_provider_id, sso_external_id) 
WHERE sso_provider_id IS NOT NULL AND sso_external_id IS NOT NULL;

-- =============================================================================
-- ORGANIZATIONS TABLE ENHANCEMENTS  
-- =============================================================================

-- Add security policy and compliance columns to organizations table
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS security_policy_id UUID REFERENCES organization_security_policies(id) ON DELETE SET NULL;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS sso_enabled BOOLEAN DEFAULT false;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS sso_required BOOLEAN DEFAULT false;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS mfa_required BOOLEAN DEFAULT false;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS ip_whitelist INET[];
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS allowed_countries CHAR(2)[];
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS session_timeout_minutes INTEGER DEFAULT 480; -- 8 hours
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS max_concurrent_sessions INTEGER DEFAULT 5;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS audit_retention_days INTEGER DEFAULT 365;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS compliance_standards TEXT[] DEFAULT '{}';
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS security_contact_email VARCHAR(255);
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS last_security_review TIMESTAMP WITH TIME ZONE;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS risk_level VARCHAR(20) DEFAULT 'medium' CHECK (risk_level IN ('low', 'medium', 'high', 'critical'));

-- Add indexes for organizations security queries
CREATE INDEX IF NOT EXISTS idx_organizations_security_policy ON organizations(security_policy_id) WHERE security_policy_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_organizations_sso_enabled ON organizations(sso_enabled) WHERE sso_enabled = true;
CREATE INDEX IF NOT EXISTS idx_organizations_mfa_required ON organizations(mfa_required) WHERE mfa_required = true;
CREATE INDEX IF NOT EXISTS idx_organizations_risk_level ON organizations(risk_level);
CREATE INDEX IF NOT EXISTS idx_organizations_security_review ON organizations(last_security_review) WHERE last_security_review IS NOT NULL;

-- =============================================================================
-- SESSIONS TABLE ENHANCEMENTS (if sessions table exists)
-- =============================================================================

-- Check if sessions table exists, if not create it with security features
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'sessions') THEN
        CREATE TABLE sessions (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            session_token VARCHAR(255) NOT NULL UNIQUE,
            refresh_token VARCHAR(255),
            device_fingerprint VARCHAR(255),
            user_agent TEXT,
            ip_address INET,
            geo_location JSONB DEFAULT '{}'::jsonb,
            is_active BOOLEAN DEFAULT true,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            terminated_at TIMESTAMP WITH TIME ZONE,
            termination_reason VARCHAR(50)
        );
        
        -- Indexes for sessions table
        CREATE INDEX idx_sessions_user_id ON sessions(user_id);
        CREATE INDEX idx_sessions_token ON sessions(session_token);
        CREATE INDEX idx_sessions_active ON sessions(is_active, expires_at) WHERE is_active = true;
        CREATE INDEX idx_sessions_user_active ON sessions(user_id, is_active) WHERE is_active = true;
        CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
        CREATE INDEX idx_sessions_device ON sessions(device_fingerprint) WHERE device_fingerprint IS NOT NULL;
        CREATE INDEX idx_sessions_ip ON sessions(ip_address) WHERE ip_address IS NOT NULL;
    ELSE
        -- Add security columns to existing sessions table
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS device_fingerprint VARCHAR(255);
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS user_agent TEXT;
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS ip_address INET;
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS geo_location JSONB DEFAULT '{}'::jsonb;
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS terminated_at TIMESTAMP WITH TIME ZONE;
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS termination_reason VARCHAR(50);
        ALTER TABLE sessions ADD COLUMN IF NOT EXISTS risk_score INTEGER DEFAULT 0;
        
        -- Add indexes if they don't exist
        CREATE INDEX IF NOT EXISTS idx_sessions_device ON sessions(device_fingerprint) WHERE device_fingerprint IS NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_sessions_ip ON sessions(ip_address) WHERE ip_address IS NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_sessions_last_activity ON sessions(last_activity_at);
        CREATE INDEX IF NOT EXISTS idx_sessions_risk_score ON sessions(risk_score) WHERE risk_score > 0;
    END IF;
END $$;

-- =============================================================================
-- QUESTS TABLE SECURITY ENHANCEMENTS
-- =============================================================================

-- Add security-related columns to quests table for access control
ALTER TABLE quests ADD COLUMN IF NOT EXISTS access_policy JSONB DEFAULT '{}'::jsonb;
ALTER TABLE quests ADD COLUMN IF NOT EXISTS requires_mfa BOOLEAN DEFAULT false;
ALTER TABLE quests ADD COLUMN IF NOT EXISTS allowed_roles TEXT[];
ALTER TABLE quests ADD COLUMN IF NOT EXISTS geo_restrictions CHAR(2)[];
ALTER TABLE quests ADD COLUMN IF NOT EXISTS ip_restrictions INET[];
ALTER TABLE quests ADD COLUMN IF NOT EXISTS security_classification VARCHAR(20) DEFAULT 'public' CHECK (security_classification IN ('public', 'internal', 'confidential', 'restricted'));
ALTER TABLE quests ADD COLUMN IF NOT EXISTS data_classification VARCHAR(20) DEFAULT 'general' CHECK (data_classification IN ('general', 'sensitive', 'personal', 'financial', 'health'));

-- Add indexes for quest security queries
CREATE INDEX IF NOT EXISTS idx_quests_requires_mfa ON quests(requires_mfa) WHERE requires_mfa = true;
CREATE INDEX IF NOT EXISTS idx_quests_security_classification ON quests(security_classification);
CREATE INDEX IF NOT EXISTS idx_quests_data_classification ON quests(data_classification);

-- =============================================================================
-- TASKS TABLE SECURITY ENHANCEMENTS  
-- =============================================================================

-- Add security-related columns to tasks table for access control
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS access_policy JSONB DEFAULT '{}'::jsonb;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS requires_mfa BOOLEAN DEFAULT false;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS allowed_roles TEXT[];
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS security_classification VARCHAR(20) DEFAULT 'public' CHECK (security_classification IN ('public', 'internal', 'confidential', 'restricted'));
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS data_classification VARCHAR(20) DEFAULT 'general' CHECK (data_classification IN ('general', 'sensitive', 'personal', 'financial', 'health'));
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS audit_required BOOLEAN DEFAULT false;

-- Add indexes for task security queries  
CREATE INDEX IF NOT EXISTS idx_tasks_requires_mfa ON tasks(requires_mfa) WHERE requires_mfa = true;
CREATE INDEX IF NOT EXISTS idx_tasks_security_classification ON tasks(security_classification);
CREATE INDEX IF NOT EXISTS idx_tasks_audit_required ON tasks(audit_required) WHERE audit_required = true;

-- =============================================================================
-- ROLES AND PERMISSIONS ENHANCEMENTS
-- =============================================================================

-- Check if roles table exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'roles') THEN
        CREATE TABLE roles (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            permissions TEXT[] DEFAULT '{}',
            is_system_role BOOLEAN DEFAULT false,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_by UUID REFERENCES users(id),
            
            UNIQUE(organization_id, name)
        );
        
        CREATE INDEX idx_roles_organization ON roles(organization_id);
        CREATE INDEX idx_roles_active ON roles(is_active) WHERE is_active = true;
        CREATE INDEX idx_roles_system ON roles(is_system_role) WHERE is_system_role = true;
    ELSE
        -- Add security columns to existing roles table
        ALTER TABLE roles ADD COLUMN IF NOT EXISTS is_system_role BOOLEAN DEFAULT false;
        ALTER TABLE roles ADD COLUMN IF NOT EXISTS security_clearance_level INTEGER DEFAULT 1;
        ALTER TABLE roles ADD COLUMN IF NOT EXISTS requires_mfa BOOLEAN DEFAULT false;
        ALTER TABLE roles ADD COLUMN IF NOT EXISTS session_timeout_override INTEGER;
        ALTER TABLE roles ADD COLUMN IF NOT EXISTS ip_restrictions INET[];
        ALTER TABLE roles ADD COLUMN IF NOT EXISTS time_restrictions JSONB DEFAULT '[]'::jsonb;
        
        CREATE INDEX IF NOT EXISTS idx_roles_system ON roles(is_system_role) WHERE is_system_role = true;
        CREATE INDEX IF NOT EXISTS idx_roles_clearance ON roles(security_clearance_level);
        CREATE INDEX IF NOT EXISTS idx_roles_mfa_required ON roles(requires_mfa) WHERE requires_mfa = true;
    END IF;
END $$;

-- Check if user_roles table exists, if not create it
DO $$  
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_roles') THEN
        CREATE TABLE user_roles (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
            organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
            granted_by UUID REFERENCES users(id),
            granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE,
            is_active BOOLEAN DEFAULT true,
            
            UNIQUE(user_id, role_id, organization_id)
        );
        
        CREATE INDEX idx_user_roles_user ON user_roles(user_id);
        CREATE INDEX idx_user_roles_role ON user_roles(role_id);
        CREATE INDEX idx_user_roles_org ON user_roles(organization_id);
        CREATE INDEX idx_user_roles_active ON user_roles(is_active, expires_at) WHERE is_active = true;
    ELSE
        -- Add security columns to existing user_roles table
        ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE;
        ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS granted_by UUID REFERENCES users(id);
        ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS revoked_at TIMESTAMP WITH TIME ZONE;
        ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS revoked_by UUID REFERENCES users(id);
        ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS revocation_reason TEXT;
        
        CREATE INDEX IF NOT EXISTS idx_user_roles_expires ON user_roles(expires_at) WHERE expires_at IS NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_user_roles_revoked ON user_roles(revoked_at) WHERE revoked_at IS NOT NULL;
    END IF;
END $$;

-- =============================================================================
-- AUDIT AND COMPLIANCE ENHANCEMENTS
-- =============================================================================

-- Add audit columns to key tables for compliance tracking
DO $$
DECLARE
    table_record RECORD;
    audit_columns TEXT[] := ARRAY['audit_created_by', 'audit_updated_by', 'audit_deleted_by', 'audit_deleted_at', 'audit_version'];
    audit_column TEXT;
BEGIN
    -- List of tables that should have audit columns
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE' 
        AND table_name IN ('users', 'organizations', 'quests', 'tasks', 'roles', 'user_roles')
    LOOP
        -- Add audit columns if they don't exist
        FOREACH audit_column IN ARRAY audit_columns
        LOOP
            EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS %I UUID REFERENCES users(id)', table_record.table_name, audit_column || '_by')
            WHERE audit_column LIKE '%_by';
            
            EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS %I TIMESTAMP WITH TIME ZONE', table_record.table_name, audit_column || '_at') 
            WHERE audit_column LIKE '%_at';
            
            EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS %I INTEGER DEFAULT 1', table_record.table_name, audit_column)
            WHERE audit_column = 'audit_version';
        END LOOP;
        
        -- Add soft delete column
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE', table_record.table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS deleted_by UUID REFERENCES users(id)', table_record.table_name);
        
        -- Create indexes for audit queries
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_deleted_at ON %I(deleted_at) WHERE deleted_at IS NOT NULL', table_record.table_name, table_record.table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_audit_version ON %I(audit_version)', table_record.table_name, table_record.table_name);
    END LOOP;
END $$;

-- =============================================================================
-- UPDATE TRIGGERS FOR ENHANCED TABLES
-- =============================================================================

-- Create or update triggers for timestamp updates on enhanced tables
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        AND table_name IN ('users', 'organizations', 'sessions', 'quests', 'tasks', 'roles', 'user_roles')
    LOOP
        -- Create trigger for updating updated_at timestamp
        EXECUTE format('DROP TRIGGER IF EXISTS trigger_update_updated_at_%I ON %I', table_record.table_name, table_record.table_name);
        EXECUTE format('CREATE TRIGGER trigger_update_updated_at_%I 
                       BEFORE UPDATE ON %I 
                       FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', 
                       table_record.table_name, table_record.table_name);
    END LOOP;
END $$;

-- =============================================================================
-- SECURITY VIEWS FOR ENHANCED DATA ACCESS
-- =============================================================================

-- View for user security summary
CREATE OR REPLACE VIEW user_security_summary AS
SELECT 
    u.id,
    u.email,
    u.name,
    u.mfa_enabled,
    u.last_login_at,
    u.login_count,
    u.failed_login_attempts,
    u.account_locked_until,
    u.password_changed_at,
    u.requires_password_change,
    sp.provider_name as sso_provider,
    CASE 
        WHEN u.account_locked_until > NOW() THEN 'locked'
        WHEN u.failed_login_attempts >= 5 THEN 'at_risk'
        WHEN u.mfa_enabled THEN 'secure'
        ELSE 'basic'
    END as security_status,
    COALESCE(array_length(u.trusted_devices::json[]::text[], 1), 0) as trusted_device_count
FROM users u
LEFT JOIN sso_providers sp ON u.sso_provider_id = sp.id
WHERE u.deleted_at IS NULL;

-- View for organization security overview
CREATE OR REPLACE VIEW organization_security_overview AS
SELECT 
    o.id,
    o.name,
    o.sso_enabled,
    o.sso_required, 
    o.mfa_required,
    o.risk_level,
    o.session_timeout_minutes,
    o.audit_retention_days,
    o.last_security_review,
    osp.name as security_policy_name,
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT CASE WHEN u.mfa_enabled THEN u.id END) as mfa_users,
    COUNT(DISTINCT CASE WHEN u.last_login_at > NOW() - INTERVAL '30 days' THEN u.id END) as active_users,
    COUNT(DISTINCT CASE WHEN u.account_locked_until > NOW() THEN u.id END) as locked_users
FROM organizations o
LEFT JOIN organization_security_policies osp ON o.security_policy_id = osp.id
LEFT JOIN users u ON u.organization_id = o.id AND u.deleted_at IS NULL
WHERE o.deleted_at IS NULL
GROUP BY o.id, o.name, o.sso_enabled, o.sso_required, o.mfa_required, 
         o.risk_level, o.session_timeout_minutes, o.audit_retention_days, 
         o.last_security_review, osp.name;

-- View for active sessions with security context
CREATE OR REPLACE VIEW active_sessions_security AS
SELECT 
    s.id,
    s.user_id,
    u.email as user_email,
    u.name as user_name,
    s.ip_address,
    s.device_fingerprint,
    s.user_agent,
    s.geo_location,
    s.created_at as session_started,
    s.last_activity_at,
    s.expires_at,
    s.risk_score,
    EXTRACT(EPOCH FROM (NOW() - s.last_activity_at))/60 as idle_minutes,
    CASE 
        WHEN s.risk_score > 80 THEN 'high_risk'
        WHEN s.risk_score > 50 THEN 'medium_risk' 
        WHEN s.geo_location->>'country' != u.security_preferences->>'primary_country' THEN 'location_anomaly'
        ELSE 'normal'
    END as risk_status
FROM sessions s
JOIN users u ON s.user_id = u.id
WHERE s.is_active = true 
AND s.expires_at > NOW()
AND u.deleted_at IS NULL;

-- =============================================================================
-- PERFORMANCE OPTIMIZATIONS
-- =============================================================================

-- Create materialized view for security dashboard data
CREATE MATERIALIZED VIEW IF NOT EXISTS security_dashboard_stats AS
SELECT 
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT CASE WHEN u.mfa_enabled THEN u.id END) as mfa_enabled_users,
    COUNT(DISTINCT CASE WHEN u.last_login_at > NOW() - INTERVAL '30 days' THEN u.id END) as active_users_30d,
    COUNT(DISTINCT CASE WHEN u.account_locked_until > NOW() THEN u.id END) as currently_locked_users,
    COUNT(DISTINCT s.id) as active_sessions,
    COUNT(DISTINCT CASE WHEN s.risk_score > 50 THEN s.id END) as high_risk_sessions,
    COUNT(DISTINCT o.id) as total_organizations,
    COUNT(DISTINCT CASE WHEN o.sso_enabled THEN o.id END) as sso_enabled_orgs,
    COUNT(DISTINCT sal.id) as audit_events_24h,
    COUNT(DISTINCT CASE WHEN sal.risk_score > 7 THEN sal.id END) as high_risk_events_24h,
    NOW() as last_updated
FROM users u
LEFT JOIN sessions s ON u.id = s.user_id AND s.is_active = true AND s.expires_at > NOW()  
LEFT JOIN organizations o ON u.organization_id = o.id
LEFT JOIN security_audit_logs sal ON sal.created_at > NOW() - INTERVAL '24 hours'
WHERE u.deleted_at IS NULL AND o.deleted_at IS NULL;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_security_dashboard_stats_last_updated 
ON security_dashboard_stats(last_updated);

-- Function to refresh security dashboard stats
CREATE OR REPLACE FUNCTION refresh_security_dashboard_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW security_dashboard_stats;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- CONSTRAINTS AND VALIDATIONS
-- =============================================================================

-- Add check constraints for security-related fields
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_failed_login_attempts 
    CHECK (failed_login_attempts >= 0 AND failed_login_attempts <= 100);

ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_login_count 
    CHECK (login_count >= 0);

ALTER TABLE organizations ADD CONSTRAINT IF NOT EXISTS check_session_timeout 
    CHECK (session_timeout_minutes >= 15 AND session_timeout_minutes <= 1440);

ALTER TABLE organizations ADD CONSTRAINT IF NOT EXISTS check_max_concurrent_sessions 
    CHECK (max_concurrent_sessions >= 1 AND max_concurrent_sessions <= 50);

ALTER TABLE organizations ADD CONSTRAINT IF NOT EXISTS check_audit_retention 
    CHECK (audit_retention_days >= 30 AND audit_retention_days <= 2555);

-- Add foreign key constraints where missing
DO $$
BEGIN
    -- Add organization reference to users if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'users' 
        AND constraint_name LIKE '%organization%'
    ) THEN
        ALTER TABLE users ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);
        CREATE INDEX IF NOT EXISTS idx_users_organization ON users(organization_id);
    END IF;
END $$;

COMMIT;