import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../common/enhanced_animations.dart';
import '../common/adaptive_layout.dart';
import '../common/enhanced_card.dart';
import '../gamification/enhanced_gamification.dart';

/// Enhanced dashboard with real-time analytics and gamification
class EnhancedDashboard extends StatefulWidget {
  final Map<String, dynamic> userStats;
  final List<Map<String, dynamic>> recentActivities;
  final List<Map<String, dynamic>> achievements;
  final bool isLoading;

  const EnhancedDashboard({
    super.key,
    required this.userStats,
    this.recentActivities = const [],
    this.achievements = const [],
    this.isLoading = false,
  });

  @override
  State<EnhancedDashboard> createState() => _EnhancedDashboardState();
}

class _EnhancedDashboardState extends State<EnhancedDashboard>
    with TickerProviderStateMixin {
  late AnimationController _staggerController;
  late List<Animation<double>> _staggerAnimations;

  @override
  void initState() {
    super.initState();
    _staggerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _staggerAnimations = StaggeredAnimationController.createStaggeredAnimations(
      controller: _staggerController,
      itemCount: 6,
    );
    
    _staggerController.forward();
  }

  @override
  void dispose() {
    _staggerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    return AdaptiveContainer(
      child: SingleChildScrollView(
        padding: AdaptiveSpacing.padding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(),
            SizedBox(height: AdaptiveSpacing.large(context)),
            _buildStatsOverview(),
            SizedBox(height: AdaptiveSpacing.large(context)),
            _buildProgressSection(),
            SizedBox(height: AdaptiveSpacing.large(context)),
            _buildActivitySection(),
            SizedBox(height: AdaptiveSpacing.large(context)),
            _buildAchievementsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return AdaptiveContainer(
      child: Column(
        children: [
          _buildSkeletonCard(height: 120),
          SizedBox(height: AdaptiveSpacing.medium(context)),
          AdaptiveGrid(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: List.generate(4, (index) => _buildSkeletonCard()),
          ),
          SizedBox(height: AdaptiveSpacing.medium(context)),
          _buildSkeletonCard(height: 200),
        ],
      ),
    );
  }

  Widget _buildSkeletonCard({double height = 100}) {
    return ShimmerLoading(
      child: EnhancedCard(
        variant: CardVariant.filled,
        height: height,
        child: Container(),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final theme = Theme.of(context);
    final userName = widget.userStats['displayName'] ?? 'Quester';
    final currentLevel = widget.userStats['level'] ?? 1;
    final currentXP = widget.userStats['experience'] ?? 0;
    final nextLevelXP = widget.userStats['nextLevelExperience'] ?? 1000;

    return SlideInAnimation(
      delay: Duration(milliseconds: _staggerAnimations[0].value.toInt() * 100),
      child: EnhancedCard(
        variant: CardVariant.gradient,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back, $userName!',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Ready to continue your quest journey?',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 16),
                  EnhancedXPBar(
                    currentXP: currentXP,
                    maxXP: nextLevelXP,
                    level: currentLevel,
                    height: 12,
                  ),
                ],
              ),
            ),
            if (AdaptiveLayout.isTablet(context) || AdaptiveLayout.isDesktop(context))
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.primary.withValues(alpha: 0.7),
                    ],
                  ),
                ),
                child: Icon(
                  Icons.person,
                  size: 40,
                  color: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsOverview() {
    final stats = [
      {
        'title': 'Total Points',
        'value': widget.userStats['totalPoints'] ?? 0,
        'icon': Icons.stars,
        'color': AppTheme.successColor,
        'trend': '+12%',
      },
      {
        'title': 'Quests Completed',
        'value': widget.userStats['completedQuests'] ?? 0,
        'icon': Icons.task_alt,
        'color': AppTheme.infoColor,
        'trend': '+8%',
      },
      {
        'title': 'Current Streak',
        'value': widget.userStats['currentStreak'] ?? 0,
        'icon': Icons.local_fire_department,
        'color': AppTheme.warningColor,
        'trend': '+2',
      },
      {
        'title': 'Achievements',
        'value': widget.userStats['achievementCount'] ?? 0,
        'icon': Icons.emoji_events,
        'color': AppTheme.legendaryColor,
        'trend': '+1',
      },
    ];

    return AdaptiveGrid(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mobileColumns: 2,
      tabletColumns: 4,
      desktopColumns: 4,
      childAspectRatio: AdaptiveLayout.isMobile(context) ? 1.2 : 1.0,
      children: stats.asMap().entries.map((entry) {
        final index = entry.key;
        final stat = entry.value;
        
        return SlideInAnimation(
          delay: Duration(milliseconds: (index + 1) * 100),
          child: _buildStatCard(stat),
        );
      }).toList(),
    );
  }

  Widget _buildStatCard(Map<String, dynamic> stat) {
    final theme = Theme.of(context);
    final color = stat['color'] as Color;

    return EnhancedCard(
      variant: CardVariant.elevated,
      enableHoverEffect: true,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              stat['icon'] as IconData,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          AnimatedCounter(
            value: stat['value'] as int,
            textStyle: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            stat['title'] as String,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: AppTheme.successColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              stat['trend'] as String,
              style: theme.textTheme.labelSmall?.copyWith(
                color: AppTheme.successColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    final theme = Theme.of(context);
    final weeklyGoal = widget.userStats['weeklyGoal'] ?? 100;
    final weeklyProgress = widget.userStats['weeklyProgress'] ?? 0;
    final dailyGoal = widget.userStats['dailyGoal'] ?? 20;
    final dailyProgress = widget.userStats['dailyProgress'] ?? 0;

    return SlideInAnimation(
      delay: const Duration(milliseconds: 500),
      child: EnhancedCard(
        variant: CardVariant.elevated,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Progress Goals',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildProgressItem(
              'Weekly Goal',
              weeklyProgress,
              weeklyGoal,
              AppTheme.infoColor,
              Icons.calendar_view_week,
            ),
            const SizedBox(height: 16),
            _buildProgressItem(
              'Daily Goal',
              dailyProgress,
              dailyGoal,
              AppTheme.successColor,
              Icons.today,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem(
    String label,
    int current,
    int target,
    Color color,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Text(
              label,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Text(
              '$current / $target',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        EnhancedXPBar(
          currentXP: current,
          maxXP: target,
          level: 0,
          height: 8,
          primaryColor: color,
          showParticles: false,
        ),
      ],
    );
  }

  Widget _buildActivitySection() {
    final theme = Theme.of(context);

    return SlideInAnimation(
      delay: const Duration(milliseconds: 600),
      child: EnhancedCard(
        variant: CardVariant.elevated,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Activity',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to full activity log
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (widget.recentActivities.isEmpty)
              _buildEmptyState('No recent activity', 'Complete quests to see your progress!')
            else
              ...widget.recentActivities.take(5).map((activity) =>
                _buildActivityItem(activity)),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> activity) {
    final theme = Theme.of(context);
    final type = activity['type'] ?? 'unknown';
    final title = activity['title'] ?? 'Activity';
    final timestamp = activity['timestamp'] ?? DateTime.now();
    final points = activity['points'] ?? 0;

    IconData icon;
    Color color;
    
    switch (type) {
      case 'quest_completed':
        icon = Icons.task_alt;
        color = AppTheme.successColor;
        break;
      case 'achievement_unlocked':
        icon = Icons.emoji_events;
        color = AppTheme.legendaryColor;
        break;
      case 'level_up':
        icon = Icons.trending_up;
        color = AppTheme.infoColor;
        break;
      default:
        icon = Icons.circle;
        color = theme.colorScheme.primary;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 16, color: color),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _formatTimestamp(timestamp),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          if (points > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '+$points',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAchievementsSection() {
    final theme = Theme.of(context);

    return SlideInAnimation(
      delay: const Duration(milliseconds: 700),
      child: EnhancedCard(
        variant: CardVariant.elevated,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Achievements',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to achievements page
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (widget.achievements.isEmpty)
              _buildEmptyState('No achievements yet', 'Complete quests to earn achievements!')
            else
              SizedBox(
                height: 200,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: widget.achievements.length,
                  itemBuilder: (context, index) {
                    final achievement = widget.achievements[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: SizedBox(
                        width: 160,
                        child: Achievement3DShowcase(
                          title: achievement['title'] ?? '',
                          description: achievement['description'] ?? '',
                          icon: _getAchievementIcon(achievement['icon']),
                          rarity: achievement['rarity'] ?? 'common',
                          isUnlocked: achievement['isUnlocked'] ?? false,
                          unlockedAt: achievement['unlockedAt'],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 48,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  IconData _getAchievementIcon(String? iconName) {
    switch (iconName) {
      case 'trophy':
        return Icons.emoji_events;
      case 'star':
        return Icons.star;
      case 'medal':
        return Icons.military_tech;
      case 'crown':
        return Icons.workspace_premium;
      case 'diamond':
        return Icons.diamond;
      default:
        return Icons.emoji_events;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
