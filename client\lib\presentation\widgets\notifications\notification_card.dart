import 'package:flutter/material.dart';
import 'package:shared/shared.dart' hide Notification;
import 'package:shared/src/models/notifications/notification.dart' as NotificationModel;
import '../common/responsive_builder.dart';

/// Widget for displaying a notification in a card format
class NotificationCard extends StatelessWidget {
  /// The notification to display
  final NotificationModel.Notification notification;
  
  /// Callback when the notification is tapped
  final VoidCallback? onTap;
  
  /// Callback when the notification is marked as read
  final VoidCallback? onMarkAsRead;
  
  /// Callback when the notification is deleted
  final VoidCallback? onDelete;
  
  /// Whether to show action buttons
  final bool showActions;
  
  /// Whether to show the timestamp
  final bool showTimestamp;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
    this.onDelete,
    this.showActions = true,
    this.showTimestamp = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: notification.isRead ? 1 : 3,
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            border: notification.isRead 
                ? null 
                : Border.all(
                    color: _getPriorityColor(context, notification.priority),
                    width: 2,
                  ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: ResponsiveBuilder(
              mobile: _buildMobileLayout,
              tablet: _buildTabletLayout,
              desktop: _buildDesktopLayout,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: AppConstants.smallPadding),
        _buildContent(context),
        if (showTimestamp) ...[
          const SizedBox(height: AppConstants.smallPadding),
          _buildTimestamp(context),
        ],
        if (showActions) ...[
          const SizedBox(height: AppConstants.defaultPadding),
          _buildActions(context),
        ],
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildIcon(context),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: AppConstants.smallPadding),
              _buildContent(context),
              if (showTimestamp) ...[
                const SizedBox(height: AppConstants.smallPadding),
                _buildTimestamp(context),
              ],
            ],
          ),
        ),
        if (showActions) ...[
          const SizedBox(width: AppConstants.defaultPadding),
          _buildActionsColumn(context),
        ],
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildIcon(context),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(child: _buildHeader(context)),
                  if (showTimestamp) _buildTimestamp(context),
                ],
              ),
              const SizedBox(height: AppConstants.smallPadding),
              _buildContent(context),
            ],
          ),
        ),
        if (showActions) ...[
          const SizedBox(width: AppConstants.defaultPadding),
          _buildActionsRow(context),
        ],
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            notification.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
              color: notification.isRead 
                  ? Theme.of(context).colorScheme.onSurface.withOpacity(0.7)
                  : Theme.of(context).colorScheme.onSurface,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        _buildPriorityIndicator(context),
        if (!notification.isRead) ...[
          const SizedBox(width: AppConstants.smallPadding),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          notification.body,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: notification.isRead 
                ? Theme.of(context).colorScheme.onSurface.withOpacity(0.6)
                : Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        if (notification.actionText != null && notification.actionUrl != null) ...[
          const SizedBox(height: AppConstants.smallPadding),
          TextButton(
            onPressed: () => _handleActionTap(context),
            child: Text(notification.actionText!),
          ),
        ],
      ],
    );
  }

  Widget _buildIcon(BuildContext context) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: _getCategoryColor(context, notification.category).withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Icon(
        _getCategoryIcon(notification.category),
        color: _getCategoryColor(context, notification.category),
        size: 24,
      ),
    );
  }

  Widget _buildPriorityIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getPriorityColor(context, notification.priority).withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(
          color: _getPriorityColor(context, notification.priority),
          width: 1,
        ),
      ),
      child: Text(
        notification.priority.name.toUpperCase(),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getPriorityColor(context, notification.priority),
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildTimestamp(BuildContext context) {
    return Text(
      _formatTimestamp(notification.createdAt),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (!notification.isRead)
          TextButton.icon(
            onPressed: onMarkAsRead,
            icon: const Icon(Icons.mark_email_read, size: 16),
            label: const Text('Mark as Read'),
          ),
        const SizedBox(width: AppConstants.smallPadding),
        TextButton.icon(
          onPressed: onDelete,
          icon: const Icon(Icons.delete_outline, size: 16),
          label: const Text('Delete'),
          style: TextButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildActionsColumn(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!notification.isRead)
          IconButton(
            onPressed: onMarkAsRead,
            icon: const Icon(Icons.mark_email_read),
            tooltip: 'Mark as Read',
          ),
        IconButton(
          onPressed: onDelete,
          icon: const Icon(Icons.delete_outline),
          tooltip: 'Delete',
          color: Theme.of(context).colorScheme.error,
        ),
      ],
    );
  }

  Widget _buildActionsRow(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!notification.isRead)
          IconButton(
            onPressed: onMarkAsRead,
            icon: const Icon(Icons.mark_email_read),
            tooltip: 'Mark as Read',
          ),
        IconButton(
          onPressed: onDelete,
          icon: const Icon(Icons.delete_outline),
          tooltip: 'Delete',
          color: Theme.of(context).colorScheme.error,
        ),
      ],
    );
  }

  Color _getPriorityColor(BuildContext context, NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.green;
      case NotificationPriority.normal:
        return Theme.of(context).colorScheme.primary;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return Colors.red;
    }
  }

  Color _getCategoryColor(BuildContext context, NotificationCategory category) {
    switch (category) {
      case NotificationCategory.system:
        return Colors.grey;
      case NotificationCategory.quest:
        return Colors.blue;
      case NotificationCategory.achievement:
        return Colors.amber;
      case NotificationCategory.social:
        return Colors.green;
      case NotificationCategory.reminder:
        return Colors.orange;
      case NotificationCategory.marketing:
        return Colors.purple;
      case NotificationCategory.security:
        return Colors.red;
      case NotificationCategory.team:
        return Colors.teal;
    }
  }

  IconData _getCategoryIcon(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.system:
        return Icons.settings;
      case NotificationCategory.quest:
        return Icons.flag;
      case NotificationCategory.achievement:
        return Icons.emoji_events;
      case NotificationCategory.social:
        return Icons.people;
      case NotificationCategory.reminder:
        return Icons.alarm;
      case NotificationCategory.marketing:
        return Icons.campaign;
      case NotificationCategory.security:
        return Icons.security;
      case NotificationCategory.team:
        return Icons.group;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  void _handleActionTap(BuildContext context) {
    if (notification.actionUrl != null) {
      // TODO: Handle navigation to action URL
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Navigate to: ${notification.actionUrl}'),
        ),
      );
    }
  }
}
