// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestProgress _$QuestProgressFromJson(Map<String, dynamic> json) =>
    QuestProgress(
      id: json['id'] as String,
      questId: json['questId'] as String,
      userId: json['userId'] as String,
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      pointsEarned: (json['pointsEarned'] as num).toInt(),
      totalPointsAvailable: (json['totalPointsAvailable'] as num).toInt(),
      tasksCompleted: (json['tasksCompleted'] as num).toInt(),
      totalTasks: (json['totalTasks'] as num).toInt(),
      timeSpentMinutes: (json['timeSpentMinutes'] as num).toInt(),
      lastMilestone: $enumDecodeNullable(
        _$ProgressMilestoneEnumMap,
        json['lastMilestone'],
      ),
      milestoneTimestamps: (json['milestoneTimestamps'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(k, DateTime.parse(e as String))),
      dailyProgress: (json['dailyProgress'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      currentStreak: (json['currentStreak'] as num).toInt(),
      longestStreak: (json['longestStreak'] as num).toInt(),
      isActive: json['isActive'] as bool,
      metadata: json['metadata'] as Map<String, dynamic>?,
      startedAt: DateTime.parse(json['startedAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$QuestProgressToJson(QuestProgress instance) =>
    <String, dynamic>{
      'id': instance.id,
      'questId': instance.questId,
      'userId': instance.userId,
      'progressPercentage': instance.progressPercentage,
      'pointsEarned': instance.pointsEarned,
      'totalPointsAvailable': instance.totalPointsAvailable,
      'tasksCompleted': instance.tasksCompleted,
      'totalTasks': instance.totalTasks,
      'timeSpentMinutes': instance.timeSpentMinutes,
      'lastMilestone': _$ProgressMilestoneEnumMap[instance.lastMilestone],
      'milestoneTimestamps': instance.milestoneTimestamps.map(
        (k, e) => MapEntry(k, e.toIso8601String()),
      ),
      'dailyProgress': instance.dailyProgress,
      'currentStreak': instance.currentStreak,
      'longestStreak': instance.longestStreak,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
      'startedAt': instance.startedAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
    };

const _$ProgressMilestoneEnumMap = {
  ProgressMilestone.started: 'started',
  ProgressMilestone.quarter: 'quarter',
  ProgressMilestone.half: 'half',
  ProgressMilestone.threeQuarters: 'three_quarters',
  ProgressMilestone.nearComplete: 'near_complete',
  ProgressMilestone.completed: 'completed',
};
