-- Organizations schema for Quester platform
-- Must run before enterprise schema that references organizations

-- Create organizations table
CREATE TABLE IF NOT EXISTS quester.organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    logo_url VARCHAR(500),
    website_url VARCHAR(500),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    timezone VARCHAR(50) DEFAULT 'UTC',
    currency VARCHAR(3) DEFAULT 'USD',
    is_active BOOLEAN DEFAULT true,
    subscription_plan VARCHAR(50) DEFAULT 'free',
    subscription_expires_at TIMESTAMPTZ,
    max_users INTEGER DEFAULT 10,
    max_quests INTEGER DEFAULT 50,
    features JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create organization members table (linking users to organizations)
CREATE TABLE IF NOT EXISTS quester.organization_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL DEFAULT 'member',
    permissions JSONB DEFAULT '{}',
    invited_by UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    invited_at TIMESTAMPTZ,
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(organization_id, user_id)
);

-- Create organization roles table
CREATE TABLE IF NOT EXISTS quester.organization_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '{}',
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(organization_id, name)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON quester.organizations(slug);
CREATE INDEX IF NOT EXISTS idx_organizations_active ON quester.organizations(is_active);
CREATE INDEX IF NOT EXISTS idx_org_members_org_id ON quester.organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_org_members_user_id ON quester.organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_org_members_active ON quester.organization_members(is_active);
CREATE INDEX IF NOT EXISTS idx_org_roles_org_id ON quester.organization_roles(organization_id);

-- Insert default organization for development
INSERT INTO quester.organizations (
    name,
    slug,
    description,
    subscription_plan,
    max_users,
    max_quests
) VALUES (
    'Default Organization',
    'default',
    'Default organization for Quester platform',
    'enterprise',
    1000,
    1000
) ON CONFLICT (slug) DO NOTHING;

-- Insert default roles for all organizations
INSERT INTO quester.organization_roles (organization_id, name, description, permissions, is_system_role)
SELECT 
    o.id,
    'admin',
    'Organization Administrator',
    '{"manage_organization": true, "manage_users": true, "manage_quests": true, "view_analytics": true}',
    true
FROM quester.organizations o
WHERE NOT EXISTS (
    SELECT 1 FROM quester.organization_roles r 
    WHERE r.organization_id = o.id AND r.name = 'admin'
);

INSERT INTO quester.organization_roles (organization_id, name, description, permissions, is_system_role)
SELECT 
    o.id,
    'member',
    'Organization Member',
    '{"view_quests": true, "participate": true, "view_profile": true}',
    true
FROM quester.organizations o
WHERE NOT EXISTS (
    SELECT 1 FROM quester.organization_roles r 
    WHERE r.organization_id = o.id AND r.name = 'member'
);

-- Grant permissions
GRANT ALL PRIVILEGES ON quester.organizations TO quester;
GRANT ALL PRIVILEGES ON quester.organization_members TO quester;
GRANT ALL PRIVILEGES ON quester.organization_roles TO quester;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Organizations schema created successfully!';
    RAISE NOTICE 'Tables created: organizations, organization_members, organization_roles';
END $$;
