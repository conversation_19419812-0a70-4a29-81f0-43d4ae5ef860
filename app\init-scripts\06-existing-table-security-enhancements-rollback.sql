-- Rollback: Security Enhancements for Existing Tables
-- Version: 1.0.0
-- Date: 2025-08-21
-- Description: Rollback security-related columns and constraints from existing tables

BEGIN;

-- =============================================================================
-- DROP MATERIALIZED VIEWS AND FUNCTIONS
-- =============================================================================

-- Drop security dashboard materialized view and function
DROP FUNCTION IF EXISTS refresh_security_dashboard_stats();
DROP MATERIALIZED VIEW IF EXISTS security_dashboard_stats;

-- Drop security views
DROP VIEW IF EXISTS active_sessions_security;
DROP VIEW IF EXISTS organization_security_overview;
DROP VIEW IF EXISTS user_security_summary;

-- =============================================================================
-- REMOVE TRIGGERS
-- =============================================================================

-- Remove update triggers from enhanced tables
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        AND table_name IN ('users', 'organizations', 'sessions', 'quests', 'tasks', 'roles', 'user_roles')
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS trigger_update_updated_at_%I ON %I', table_record.table_name, table_record.table_name);
    END LOOP;
END $$;

-- =============================================================================
-- REMOVE CONSTRAINTS
-- =============================================================================

-- Remove check constraints from users table
ALTER TABLE users DROP CONSTRAINT IF EXISTS check_failed_login_attempts;
ALTER TABLE users DROP CONSTRAINT IF EXISTS check_login_count;

-- Remove check constraints from organizations table
ALTER TABLE organizations DROP CONSTRAINT IF EXISTS check_session_timeout;
ALTER TABLE organizations DROP CONSTRAINT IF EXISTS check_max_concurrent_sessions;
ALTER TABLE organizations DROP CONSTRAINT IF EXISTS check_audit_retention;

-- =============================================================================
-- DROP INDEXES CREATED FOR SECURITY ENHANCEMENTS
-- =============================================================================

-- Drop indexes from users table
DROP INDEX IF EXISTS idx_users_sso_external_id;
DROP INDEX IF EXISTS idx_users_sso_provider_id;
DROP INDEX IF EXISTS idx_users_last_login_at;
DROP INDEX IF EXISTS idx_users_account_locked;
DROP INDEX IF EXISTS idx_users_failed_attempts;
DROP INDEX IF EXISTS idx_users_mfa_enabled;
DROP INDEX IF EXISTS idx_users_password_changed;
DROP INDEX IF EXISTS idx_users_requires_password_change;
DROP INDEX IF EXISTS idx_users_sso_unique;

-- Drop indexes from organizations table
DROP INDEX IF EXISTS idx_organizations_security_policy;
DROP INDEX IF EXISTS idx_organizations_sso_enabled;
DROP INDEX IF EXISTS idx_organizations_mfa_required;
DROP INDEX IF EXISTS idx_organizations_risk_level;
DROP INDEX IF EXISTS idx_organizations_security_review;

-- Drop indexes from sessions table
DROP INDEX IF EXISTS idx_sessions_device;
DROP INDEX IF EXISTS idx_sessions_ip;
DROP INDEX IF EXISTS idx_sessions_last_activity;
DROP INDEX IF EXISTS idx_sessions_risk_score;

-- Drop indexes from quests table
DROP INDEX IF EXISTS idx_quests_requires_mfa;
DROP INDEX IF EXISTS idx_quests_security_classification;
DROP INDEX IF EXISTS idx_quests_data_classification;

-- Drop indexes from tasks table
DROP INDEX IF EXISTS idx_tasks_requires_mfa;
DROP INDEX IF EXISTS idx_tasks_security_classification;
DROP INDEX IF EXISTS idx_tasks_audit_required;

-- Drop indexes from roles table
DROP INDEX IF EXISTS idx_roles_system;
DROP INDEX IF EXISTS idx_roles_clearance;
DROP INDEX IF EXISTS idx_roles_mfa_required;

-- Drop indexes from user_roles table
DROP INDEX IF EXISTS idx_user_roles_expires;
DROP INDEX IF EXISTS idx_user_roles_revoked;

-- Drop audit indexes from all tables
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE' 
        AND table_name IN ('users', 'organizations', 'quests', 'tasks', 'roles', 'user_roles')
    LOOP
        EXECUTE format('DROP INDEX IF EXISTS idx_%I_deleted_at', table_record.table_name);
        EXECUTE format('DROP INDEX IF EXISTS idx_%I_audit_version', table_record.table_name);
    END LOOP;
END $$;

-- =============================================================================
-- REMOVE COLUMNS FROM USERS TABLE
-- =============================================================================

ALTER TABLE users DROP COLUMN IF EXISTS sso_external_id;
ALTER TABLE users DROP COLUMN IF EXISTS sso_provider_id;
ALTER TABLE users DROP COLUMN IF EXISTS last_login_at;
ALTER TABLE users DROP COLUMN IF EXISTS last_login_ip;
ALTER TABLE users DROP COLUMN IF EXISTS login_count;
ALTER TABLE users DROP COLUMN IF EXISTS failed_login_attempts;
ALTER TABLE users DROP COLUMN IF EXISTS account_locked_until;
ALTER TABLE users DROP COLUMN IF EXISTS password_changed_at;
ALTER TABLE users DROP COLUMN IF EXISTS mfa_enabled;
ALTER TABLE users DROP COLUMN IF EXISTS mfa_secret;
ALTER TABLE users DROP COLUMN IF EXISTS backup_codes;
ALTER TABLE users DROP COLUMN IF EXISTS trusted_devices;
ALTER TABLE users DROP COLUMN IF EXISTS security_preferences;
ALTER TABLE users DROP COLUMN IF EXISTS two_factor_recovery_codes;
ALTER TABLE users DROP COLUMN IF EXISTS email_verified_at;
ALTER TABLE users DROP COLUMN IF EXISTS phone_verified_at;
ALTER TABLE users DROP COLUMN IF EXISTS requires_password_change;
ALTER TABLE users DROP COLUMN IF EXISTS security_questions;

-- Remove audit columns from users table
ALTER TABLE users DROP COLUMN IF EXISTS audit_created_by;
ALTER TABLE users DROP COLUMN IF EXISTS audit_updated_by;
ALTER TABLE users DROP COLUMN IF EXISTS audit_deleted_by;
ALTER TABLE users DROP COLUMN IF EXISTS audit_deleted_at;
ALTER TABLE users DROP COLUMN IF EXISTS audit_version;
ALTER TABLE users DROP COLUMN IF EXISTS deleted_at;
ALTER TABLE users DROP COLUMN IF EXISTS deleted_by;

-- =============================================================================
-- REMOVE COLUMNS FROM ORGANIZATIONS TABLE
-- =============================================================================

ALTER TABLE organizations DROP COLUMN IF EXISTS security_policy_id;
ALTER TABLE organizations DROP COLUMN IF EXISTS sso_enabled;
ALTER TABLE organizations DROP COLUMN IF EXISTS sso_required;
ALTER TABLE organizations DROP COLUMN IF EXISTS mfa_required;
ALTER TABLE organizations DROP COLUMN IF EXISTS ip_whitelist;
ALTER TABLE organizations DROP COLUMN IF EXISTS allowed_countries;
ALTER TABLE organizations DROP COLUMN IF EXISTS session_timeout_minutes;
ALTER TABLE organizations DROP COLUMN IF EXISTS max_concurrent_sessions;
ALTER TABLE organizations DROP COLUMN IF EXISTS audit_retention_days;
ALTER TABLE organizations DROP COLUMN IF EXISTS compliance_standards;
ALTER TABLE organizations DROP COLUMN IF EXISTS security_contact_email;
ALTER TABLE organizations DROP COLUMN IF EXISTS last_security_review;
ALTER TABLE organizations DROP COLUMN IF EXISTS risk_level;

-- Remove audit columns from organizations table
ALTER TABLE organizations DROP COLUMN IF EXISTS audit_created_by;
ALTER TABLE organizations DROP COLUMN IF EXISTS audit_updated_by;
ALTER TABLE organizations DROP COLUMN IF EXISTS audit_deleted_by;
ALTER TABLE organizations DROP COLUMN IF EXISTS audit_deleted_at;
ALTER TABLE organizations DROP COLUMN IF EXISTS audit_version;
ALTER TABLE organizations DROP COLUMN IF EXISTS deleted_at;
ALTER TABLE organizations DROP COLUMN IF EXISTS deleted_by;

-- =============================================================================
-- REMOVE COLUMNS FROM SESSIONS TABLE
-- =============================================================================

-- Only remove columns if sessions table exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'sessions') THEN
        ALTER TABLE sessions DROP COLUMN IF EXISTS device_fingerprint;
        ALTER TABLE sessions DROP COLUMN IF EXISTS user_agent;
        ALTER TABLE sessions DROP COLUMN IF EXISTS ip_address;
        ALTER TABLE sessions DROP COLUMN IF EXISTS geo_location;
        ALTER TABLE sessions DROP COLUMN IF EXISTS last_activity_at;
        ALTER TABLE sessions DROP COLUMN IF EXISTS terminated_at;
        ALTER TABLE sessions DROP COLUMN IF EXISTS termination_reason;
        ALTER TABLE sessions DROP COLUMN IF EXISTS risk_score;
        
        -- Remove audit columns from sessions table
        ALTER TABLE sessions DROP COLUMN IF EXISTS audit_created_by;
        ALTER TABLE sessions DROP COLUMN IF EXISTS audit_updated_by;
        ALTER TABLE sessions DROP COLUMN IF EXISTS audit_deleted_by;
        ALTER TABLE sessions DROP COLUMN IF EXISTS audit_deleted_at;
        ALTER TABLE sessions DROP COLUMN IF EXISTS audit_version;
        ALTER TABLE sessions DROP COLUMN IF EXISTS deleted_at;
        ALTER TABLE sessions DROP COLUMN IF EXISTS deleted_by;
        
        -- Drop sessions table if it was created by our script
        -- (This is a judgment call - you may want to keep the table if it has data)
        -- DROP TABLE IF EXISTS sessions;
    END IF;
END $$;

-- =============================================================================
-- REMOVE COLUMNS FROM QUESTS TABLE
-- =============================================================================

ALTER TABLE quests DROP COLUMN IF EXISTS access_policy;
ALTER TABLE quests DROP COLUMN IF EXISTS requires_mfa;
ALTER TABLE quests DROP COLUMN IF EXISTS allowed_roles;
ALTER TABLE quests DROP COLUMN IF EXISTS geo_restrictions;
ALTER TABLE quests DROP COLUMN IF EXISTS ip_restrictions;
ALTER TABLE quests DROP COLUMN IF EXISTS security_classification;
ALTER TABLE quests DROP COLUMN IF EXISTS data_classification;

-- Remove audit columns from quests table
ALTER TABLE quests DROP COLUMN IF EXISTS audit_created_by;
ALTER TABLE quests DROP COLUMN IF EXISTS audit_updated_by;
ALTER TABLE quests DROP COLUMN IF EXISTS audit_deleted_by;
ALTER TABLE quests DROP COLUMN IF EXISTS audit_deleted_at;
ALTER TABLE quests DROP COLUMN IF EXISTS audit_version;
ALTER TABLE quests DROP COLUMN IF EXISTS deleted_at;
ALTER TABLE quests DROP COLUMN IF EXISTS deleted_by;

-- =============================================================================
-- REMOVE COLUMNS FROM TASKS TABLE
-- =============================================================================

ALTER TABLE tasks DROP COLUMN IF EXISTS access_policy;
ALTER TABLE tasks DROP COLUMN IF EXISTS requires_mfa;
ALTER TABLE tasks DROP COLUMN IF EXISTS allowed_roles;
ALTER TABLE tasks DROP COLUMN IF EXISTS security_classification;
ALTER TABLE tasks DROP COLUMN IF EXISTS data_classification;
ALTER TABLE tasks DROP COLUMN IF EXISTS audit_required;

-- Remove audit columns from tasks table
ALTER TABLE tasks DROP COLUMN IF EXISTS audit_created_by;
ALTER TABLE tasks DROP COLUMN IF EXISTS audit_updated_by;
ALTER TABLE tasks DROP COLUMN IF EXISTS audit_deleted_by;
ALTER TABLE tasks DROP COLUMN IF EXISTS audit_deleted_at;
ALTER TABLE tasks DROP COLUMN IF EXISTS audit_version;
ALTER TABLE tasks DROP COLUMN IF EXISTS deleted_at;
ALTER TABLE tasks DROP COLUMN IF EXISTS deleted_by;

-- =============================================================================
-- REMOVE COLUMNS FROM ROLES TABLE
-- =============================================================================

-- Only remove columns if roles table exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'roles') THEN
        ALTER TABLE roles DROP COLUMN IF EXISTS is_system_role;
        ALTER TABLE roles DROP COLUMN IF EXISTS security_clearance_level;
        ALTER TABLE roles DROP COLUMN IF EXISTS requires_mfa;
        ALTER TABLE roles DROP COLUMN IF EXISTS session_timeout_override;
        ALTER TABLE roles DROP COLUMN IF EXISTS ip_restrictions;
        ALTER TABLE roles DROP COLUMN IF EXISTS time_restrictions;
        
        -- Remove audit columns from roles table
        ALTER TABLE roles DROP COLUMN IF EXISTS audit_created_by;
        ALTER TABLE roles DROP COLUMN IF EXISTS audit_updated_by;
        ALTER TABLE roles DROP COLUMN IF EXISTS audit_deleted_by;
        ALTER TABLE roles DROP COLUMN IF EXISTS audit_deleted_at;
        ALTER TABLE roles DROP COLUMN IF EXISTS audit_version;
        ALTER TABLE roles DROP COLUMN IF EXISTS deleted_at;
        ALTER TABLE roles DROP COLUMN IF EXISTS deleted_by;
        
        -- Drop roles table if it was created by our script
        -- (This is a judgment call - you may want to keep the table if it has data)
        -- DROP TABLE IF EXISTS roles;
    END IF;
END $$;

-- =============================================================================
-- REMOVE COLUMNS FROM USER_ROLES TABLE
-- =============================================================================

-- Only remove columns if user_roles table exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_roles') THEN
        ALTER TABLE user_roles DROP COLUMN IF EXISTS expires_at;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS granted_by;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS granted_at;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS revoked_at;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS revoked_by;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS revocation_reason;
        
        -- Remove audit columns from user_roles table
        ALTER TABLE user_roles DROP COLUMN IF EXISTS audit_created_by;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS audit_updated_by;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS audit_deleted_by;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS audit_deleted_at;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS audit_version;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS deleted_at;
        ALTER TABLE user_roles DROP COLUMN IF EXISTS deleted_by;
        
        -- Drop user_roles table if it was created by our script  
        -- (This is a judgment call - you may want to keep the table if it has data)
        -- DROP TABLE IF EXISTS user_roles;
    END IF;
END $$;

-- =============================================================================
-- CLEANUP NOTES
-- =============================================================================

-- Note: This rollback script removes all security enhancements added to existing tables.
-- Some tables (sessions, roles, user_roles) may have been created by this migration
-- and could be completely dropped if they contain no important data.
-- 
-- Before running this rollback in production:
-- 1. Ensure you have a complete database backup
-- 2. Review which tables contain important data that should be preserved
-- 3. Consider whether partial rollback is more appropriate
-- 4. Test the rollback on a non-production environment first
-- 
-- Tables that may be completely dropped if created by our migration:
-- - sessions (if created new)  
-- - roles (if created new)
-- - user_roles (if created new)
--
-- To completely remove these tables, uncomment the DROP TABLE statements above.

COMMIT;