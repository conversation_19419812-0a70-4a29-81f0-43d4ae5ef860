import 'package:equatable/equatable.dart';

/// SSO provider types
enum SSOProviderType {
  saml,
  oauth2,
  oidc;

  String get displayName {
    switch (this) {
      case SSOProviderType.saml:
        return 'SAML 2.0';
      case SSOProviderType.oauth2:
        return 'OAuth 2.0';
      case SSOProviderType.oidc:
        return 'OpenID Connect';
    }
  }
}

/// SSO Provider Configuration model for database table: sso_providers
class SSOProvider extends Equatable {
  /// Unique provider identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Provider name
  final String providerName;

  /// Provider type
  final SSOProviderType providerType;

  /// Provider configuration (JSONB)
  final Map<String, dynamic> providerConfig;

  /// Metadata URL
  final String? metadataUrl;

  /// Entity ID
  final String? entityId;

  /// Certificate
  final String? certificate;

  /// Whether provider is active
  final bool isActive;

  /// Whether this is the primary provider
  final bool isPrimary;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Created by user ID
  final String? createdBy;

  const SSOProvider({
    required this.id,
    required this.organizationId,
    required this.providerName,
    required this.providerType,
    required this.providerConfig,
    this.metadataUrl,
    this.entityId,
    this.certificate,
    this.isActive = true,
    this.isPrimary = false,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  /// Create empty SSOProvider for testing
  factory SSOProvider.empty() {
    return SSOProvider(
      id: '',
      organizationId: '',
      providerName: '',
      providerType: SSOProviderType.oauth2,
      providerConfig: {},
      isActive: true,
      isPrimary: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create SSOProvider from JSON
  factory SSOProvider.fromJson(Map<String, dynamic> json) {
    return SSOProvider(
      id: json['id'] as String,
      organizationId: json['organization_id'] as String,
      providerName: json['provider_name'] as String,
      providerType: SSOProviderType.values.firstWhere(
        (e) => e.name == json['provider_type'],
        orElse: () => SSOProviderType.oauth2,
      ),
      providerConfig: Map<String, dynamic>.from(json['provider_config'] as Map),
      metadataUrl: json['metadata_url'] as String?,
      entityId: json['entity_id'] as String?,
      certificate: json['certificate'] as String?,
      isActive: json['is_active'] as bool,
      isPrimary: json['is_primary'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String?,
    );
  }

  /// Convert SSOProvider to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organization_id': organizationId,
      'provider_name': providerName,
      'provider_type': providerType.name,
      'provider_config': providerConfig,
      'metadata_url': metadataUrl,
      'entity_id': entityId,
      'certificate': certificate,
      'is_active': isActive,
      'is_primary': isPrimary,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  /// Create a copy with updated fields
  SSOProvider copyWith({
    String? id,
    String? organizationId,
    String? providerName,
    SSOProviderType? providerType,
    Map<String, dynamic>? providerConfig,
    String? metadataUrl,
    String? entityId,
    String? certificate,
    bool? isActive,
    bool? isPrimary,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return SSOProvider(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      providerName: providerName ?? this.providerName,
      providerType: providerType ?? this.providerType,
      providerConfig: providerConfig ?? this.providerConfig,
      metadataUrl: metadataUrl ?? this.metadataUrl,
      entityId: entityId ?? this.entityId,
      certificate: certificate ?? this.certificate,
      isActive: isActive ?? this.isActive,
      isPrimary: isPrimary ?? this.isPrimary,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  /// Check if provider configuration is valid
  bool get isConfigurationValid {
    switch (providerType) {
      case SSOProviderType.saml:
        return providerConfig.containsKey('entity_id') &&
               providerConfig.containsKey('sso_url') &&
               providerConfig.containsKey('certificate');
      case SSOProviderType.oauth2:
        return providerConfig.containsKey('client_id') &&
               providerConfig.containsKey('client_secret') &&
               providerConfig.containsKey('authorization_url') &&
               providerConfig.containsKey('token_url');
      case SSOProviderType.oidc:
        return providerConfig.containsKey('client_id') &&
               providerConfig.containsKey('client_secret') &&
               providerConfig.containsKey('discovery_url');
    }
  }

  /// Get required configuration keys for provider type
  List<String> get requiredConfigKeys {
    switch (providerType) {
      case SSOProviderType.saml:
        return ['entity_id', 'sso_url', 'certificate', 'signature_algorithm'];
      case SSOProviderType.oauth2:
        return ['client_id', 'client_secret', 'authorization_url', 'token_url'];
      case SSOProviderType.oidc:
        return ['client_id', 'client_secret', 'discovery_url'];
    }
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        providerName,
        providerType,
        providerConfig,
        metadataUrl,
        entityId,
        certificate,
        isActive,
        isPrimary,
        createdAt,
        updatedAt,
        createdBy,
      ];

  @override
  bool get stringify => true;
}