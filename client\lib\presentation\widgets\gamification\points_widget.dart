import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/gamification/gamification_bloc.dart';
import '../common/responsive_builder.dart';

/// Widget to display user points and level information
class PointsWidget extends StatelessWidget {
  /// Whether to show detailed information
  final bool showDetails;
  
  /// Custom size for the widget
  final Size? size;
  
  /// Whether to show level progress
  final bool showLevelProgress;
  
  /// Custom background color
  final Color? backgroundColor;

  const PointsWidget({
    super.key,
    this.showDetails = true,
    this.size,
    this.showLevelProgress = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoaded && state.userPoints != null) {
          return _buildPointsCard(context, state.userPoints!);
        } else if (state is GamificationLoading) {
          return _buildLoadingCard(context);
        } else {
          return _buildEmptyCard(context);
        }
      },
    );
  }

  /// Build points card with user data
  Widget _buildPointsCard(BuildContext context, UserPoints userPoints) {
    return ResponsiveContainer(
      child: Container(
        width: size?.width,
        height: size?.height,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              backgroundColor ?? Theme.of(context).colorScheme.primary,
              (backgroundColor ?? Theme.of(context).colorScheme.primary).withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
          boxShadow: [
            BoxShadow(
              color: (backgroundColor ?? Theme.of(context).colorScheme.primary).withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context, userPoints),
            if (showDetails) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              _buildPointsBreakdown(context, userPoints),
            ],
            if (showLevelProgress) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              _buildLevelProgress(context, userPoints),
            ],
          ],
        ),
      ),
    );
  }

  /// Build header with total points and level
  Widget _buildHeader(BuildContext context, UserPoints userPoints) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          child: Icon(
            Icons.stars,
            color: Theme.of(context).colorScheme.onPrimary,
            size: context.responsive(mobile: 24, tablet: 28, desktop: 32),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${userPoints.totalPoints}',
                style: context.responsive<TextStyle?>(
                  mobile: Theme.of(context).textTheme.headlineMedium,
                  tablet: Theme.of(context).textTheme.headlineLarge,
                  desktop: Theme.of(context).textTheme.displaySmall,
                )?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Total Points',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
            borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.trending_up,
                color: Theme.of(context).colorScheme.onPrimary,
                size: 16,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                'Level ${userPoints.currentLevel}',
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build points breakdown
  Widget _buildPointsBreakdown(BuildContext context, UserPoints userPoints) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildPointsStat(
              context,
              'Today',
              userPoints.dailyPoints.toString(),
              Icons.today,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.3),
          ),
          Expanded(
            child: _buildPointsStat(
              context,
              'This Week',
              userPoints.weeklyPoints.toString(),
              Icons.date_range,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.3),
          ),
          Expanded(
            child: _buildPointsStat(
              context,
              'This Month',
              userPoints.monthlyPoints.toString(),
              Icons.calendar_month,
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual points stat
  Widget _buildPointsStat(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
          size: 20,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Theme.of(context).colorScheme.onPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build level progress bar
  Widget _buildLevelProgress(BuildContext context, UserPoints userPoints) {
    final currentLevelPoints = userPoints.currentLevelPoints;
    final nextLevelPoints = userPoints.pointsToNextLevel;
    final progress = currentLevelPoints / (currentLevelPoints + nextLevelPoints);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Level ${userPoints.currentLevel}',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
              ),
            ),
            Text(
              'Level ${userPoints.currentLevel + 1}',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.smallPadding),
        ClipRRect(
          borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Theme.of(context).colorScheme.onPrimary.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.onPrimary,
            ),
            minHeight: 8,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          '$nextLevelPoints points to next level',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  /// Build loading card
  Widget _buildLoadingCard(BuildContext context) {
    return Container(
      width: size?.width,
      height: size?.height ?? 120,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Build empty card
  Widget _buildEmptyCard(BuildContext context) {
    return Container(
      width: size?.width,
      height: size?.height ?? 120,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.stars_outlined,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              size: 32,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'No points data',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Compact points widget for smaller spaces
class CompactPointsWidget extends StatelessWidget {
  const CompactPointsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoaded && state.userPoints != null) {
          final userPoints = state.userPoints!;
          return Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.smallPadding,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary,
                ],
              ),
              borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.stars,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 20,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  '${userPoints.totalPoints}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.smallPadding,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                  ),
                  child: Text(
                    'L${userPoints.currentLevel}',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}
