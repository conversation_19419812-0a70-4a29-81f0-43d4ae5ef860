import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../core/theme/app_theme.dart';
import '../common/enhanced_animations.dart';
import '../common/adaptive_layout.dart';
import '../common/enhanced_card.dart';

/// Enhanced XP bar with particle effects and smooth animations
class EnhancedXPBar extends StatefulWidget {
  final int currentXP;
  final int maxXP;
  final int level;
  final Color? primaryColor;
  final Color? backgroundColor;
  final double height;
  final bool showParticles;
  final VoidCallback? onLevelUp;

  const EnhancedXPBar({
    super.key,
    required this.currentXP,
    required this.maxXP,
    required this.level,
    this.primaryColor,
    this.backgroundColor,
    this.height = 12.0,
    this.showParticles = true,
    this.onLevelUp,
  });

  @override
  State<EnhancedXPBar> createState() => _EnhancedXPBarState();
}

class _EnhancedXPBarState extends State<EnhancedXPBar>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _particleController;
  late Animation<double> _progressAnimation;
  late Animation<double> _glowAnimation;
  
  int _previousLevel = 0;
  bool _showLevelUpEffect = false;

  @override
  void initState() {
    super.initState();
    _previousLevel = widget.level;
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.currentXP / widget.maxXP,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: GameCurves.slideIn,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeInOut,
    ));
    
    _progressController.forward();
  }

  @override
  void didUpdateWidget(EnhancedXPBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.level != widget.level) {
      _showLevelUpEffect = true;
      _particleController.forward().then((_) {
        _particleController.reset();
        setState(() => _showLevelUpEffect = false);
      });
      widget.onLevelUp?.call();
    }
    
    if (oldWidget.currentXP != widget.currentXP || oldWidget.maxXP != widget.maxXP) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.currentXP / widget.maxXP,
      ).animate(CurvedAnimation(
        parent: _progressController,
        curve: GameCurves.slideIn,
      ));
      _progressController.reset();
      _progressController.forward();
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = widget.primaryColor ?? theme.colorScheme.primary;
    final backgroundColor = widget.backgroundColor ?? 
        theme.colorScheme.surfaceContainerHighest;

    return AdaptiveLayout(
      mobile: _buildMobileLayout(theme, primaryColor, backgroundColor),
      tablet: _buildTabletLayout(theme, primaryColor, backgroundColor),
      desktop: _buildDesktopLayout(theme, primaryColor, backgroundColor),
    );
  }

  Widget _buildMobileLayout(ThemeData theme, Color primaryColor, Color backgroundColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Level ${widget.level}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            Text(
              '${widget.currentXP} / ${widget.maxXP} XP',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        _buildProgressBar(theme, primaryColor, backgroundColor),
      ],
    );
  }

  Widget _buildTabletLayout(ThemeData theme, Color primaryColor, Color backgroundColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            'LV ${widget.level}',
            style: theme.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: _buildProgressBar(theme, primaryColor, backgroundColor)),
        const SizedBox(width: 16),
        Text(
          '${widget.currentXP} / ${widget.maxXP}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(ThemeData theme, Color primaryColor, Color backgroundColor) {
    return _buildTabletLayout(theme, primaryColor, backgroundColor);
  }

  Widget _buildProgressBar(ThemeData theme, Color primaryColor, Color backgroundColor) {
    return AnimatedBuilder(
      animation: Listenable.merge([_progressAnimation, _glowAnimation]),
      builder: (context, child) {
        return Stack(
          children: [
            Container(
              height: widget.height,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(widget.height / 2),
              ),
            ),
            Container(
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.height / 2),
                gradient: LinearGradient(
                  colors: [
                    primaryColor,
                    primaryColor.withValues(alpha: 0.8),
                  ],
                ),
                boxShadow: _showLevelUpEffect ? [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: _glowAnimation.value * 0.6),
                    blurRadius: 20 * _glowAnimation.value,
                    spreadRadius: 2 * _glowAnimation.value,
                  ),
                ] : null,
              ),
              child: FractionallySizedBox(
                widthFactor: _progressAnimation.value.clamp(0.0, 1.0),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(widget.height / 2),
                  ),
                ),
              ),
            ),
            if (widget.showParticles && _showLevelUpEffect)
              ..._buildParticles(primaryColor),
          ],
        );
      },
    );
  }

  List<Widget> _buildParticles(Color color) {
    return List.generate(8, (index) {
      final angle = (index * 45.0) * (math.pi / 180);
      final distance = 30.0 * _glowAnimation.value;
      
      return Positioned(
        left: (widget.height / 2) + (math.cos(angle) * distance),
        top: (widget.height / 2) + (math.sin(angle) * distance),
        child: Transform.scale(
          scale: (1.0 - _glowAnimation.value) * 0.5,
          child: Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
        ),
      );
    });
  }
}

/// Enhanced achievement showcase with 3D effects
class Achievement3DShowcase extends StatefulWidget {
  final String title;
  final String description;
  final IconData icon;
  final String rarity;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final VoidCallback? onTap;

  const Achievement3DShowcase({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.rarity,
    required this.isUnlocked,
    this.unlockedAt,
    this.onTap,
  });

  @override
  State<Achievement3DShowcase> createState() => _Achievement3DShowcaseState();
}

class _Achievement3DShowcaseState extends State<Achievement3DShowcase>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleHover(bool isHovered) {
    setState(() => _isHovered = isHovered);
    if (isHovered) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final rarityColor = AppTheme.getRarityColor(widget.rarity);

    return MouseRegion(
      onEnter: (_) => _handleHover(true),
      onExit: (_) => _handleHover(false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(_rotationAnimation.value)
                ..scale(_scaleAnimation.value),
              child: EnhancedCard(
                variant: CardVariant.glass,
                padding: const EdgeInsets.all(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    rarityColor.withValues(alpha: 0.1),
                    rarityColor.withValues(alpha: 0.05),
                  ],
                ),
                customShadows: [
                  BoxShadow(
                    color: rarityColor.withValues(alpha: 0.3),
                    blurRadius: _isHovered ? 20 : 10,
                    spreadRadius: _isHovered ? 2 : 0,
                  ),
                ],
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            rarityColor,
                            rarityColor.withValues(alpha: 0.7),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: rarityColor.withValues(alpha: 0.4),
                            blurRadius: 15,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Icon(
                        widget.icon,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      widget.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: widget.isUnlocked 
                            ? theme.colorScheme.onSurface
                            : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: widget.isUnlocked 
                            ? theme.colorScheme.onSurface.withValues(alpha: 0.7)
                            : theme.colorScheme.onSurface.withValues(alpha: 0.4),
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: rarityColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        widget.rarity.toUpperCase(),
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: rarityColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
