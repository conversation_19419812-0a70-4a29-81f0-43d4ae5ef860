-- Authentication enhancements for user login/registration
-- Adds necessary columns for authentication functionality

-- Add password hash column for authentication
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- Add additional user profile columns
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS first_name <PERSON><PERSON><PERSON><PERSON>(100);
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS last_name <PERSON><PERSON><PERSON><PERSON>(100);

-- Add authentication-related columns
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT false;
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255);
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS password_reset_token VARCHAR(255);
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS password_reset_expires TIMESTAMP WITH TIME ZONE;
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS last_login TIMES<PERSON>MP WITH TIME ZONE;
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0;
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP WITH TIME ZONE;

-- Add two-factor authentication columns
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT false;
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS two_factor_secret VARCHAR(255);
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS two_factor_backup_codes TEXT[];

-- Add user status enum (if not exists)
DO $$ BEGIN
    CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'pending_verification');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add status column
ALTER TABLE quester.users ADD COLUMN IF NOT EXISTS status user_status DEFAULT 'pending_verification';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON quester.users(email_verified);
CREATE INDEX IF NOT EXISTS idx_users_status ON quester.users(status);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON quester.users(last_login);

-- Create auth sessions table for session management
CREATE TABLE IF NOT EXISTS quester.auth_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    refresh_expires_at TIMESTAMP WITH TIME ZONE,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for auth sessions
CREATE INDEX IF NOT EXISTS idx_auth_sessions_user_id ON quester.auth_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_token_hash ON quester.auth_sessions(token_hash);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_expires_at ON quester.auth_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_active ON quester.auth_sessions(is_active);

-- Create auth events table for audit logging
CREATE TABLE IF NOT EXISTS quester.auth_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    success BOOLEAN NOT NULL,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for auth events
CREATE INDEX IF NOT EXISTS idx_auth_events_user_id ON quester.auth_events(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_events_type ON quester.auth_events(event_type);
CREATE INDEX IF NOT EXISTS idx_auth_events_created_at ON quester.auth_events(created_at);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON quester.auth_sessions TO quester;
GRANT SELECT, INSERT, UPDATE, DELETE ON quester.auth_events TO quester;

-- Notification
DO $$
BEGIN
    RAISE NOTICE 'Authentication enhancements applied successfully!';
    RAISE NOTICE 'Added columns: password_hash, first_name, last_name, email_verified, status, two_factor_enabled';
    RAISE NOTICE 'Created tables: auth_sessions, auth_events';
    RAISE NOTICE 'Created indexes for performance optimization';
END $$;
