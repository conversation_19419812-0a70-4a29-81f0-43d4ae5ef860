import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../core/user.dart';

part 'admin_user.g.dart';

/// System administrator privilege levels
enum AdminLevel {
  @JsonValue('super_admin')
  superAdmin, // Full system access across all organizations
  @JsonValue('system_admin')
  systemAdmin, // System-level administration
  @JsonValue('organization_admin')
  organizationAdmin, // Organization-level administration
  @JsonValue('support_admin')
  supportAdmin, // Support and user assistance
}

/// Admin user model with extended permissions and access
@JsonSerializable()
class AdminUser extends Equatable {
  /// Base user information
  final User user;

  /// Administrative privilege level
  final AdminLevel adminLevel;

  /// System permissions
  final Set<SystemPermission> systemPermissions;

  /// Organizations this admin has access to (null = all organizations)
  final List<String>? organizationAccess;

  /// Admin-specific settings and preferences
  final AdminSettings adminSettings;

  /// Last admin action timestamp
  final DateTime? lastAdminActivity;

  /// Admin account creation timestamp
  final DateTime adminCreatedAt;

  /// Admin account status
  final AdminStatus adminStatus;

  /// Admin metadata and notes
  final Map<String, dynamic>? adminMetadata;

  const AdminUser({
    required this.user,
    required this.adminLevel,
    required this.systemPermissions,
    this.organizationAccess,
    required this.adminSettings,
    this.lastAdminActivity,
    required this.adminCreatedAt,
    required this.adminStatus,
    this.adminMetadata,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) => _$AdminUserFromJson(json);
  Map<String, dynamic> toJson() => _$AdminUserToJson(this);

  /// Check if admin has specific system permission
  bool hasSystemPermission(SystemPermission permission) {
    return systemPermissions.contains(permission);
  }

  /// Check if admin has access to specific organization
  bool hasOrganizationAccess(String organizationId) {
    return organizationAccess == null || organizationAccess!.contains(organizationId);
  }

  /// Check if this is a super admin with full access
  bool get isSuperAdmin => adminLevel == AdminLevel.superAdmin;

  /// Check if admin can manage users globally
  bool get canManageUsers {
    return hasSystemPermission(SystemPermission.userManagement) ||
           isSuperAdmin;
  }

  /// Check if admin can access system logs
  bool get canAccessSystemLogs {
    return hasSystemPermission(SystemPermission.systemLogs) ||
           isSuperAdmin;
  }

  /// Check if admin can manage billing
  bool get canManageBilling {
    return hasSystemPermission(SystemPermission.billingManagement) ||
           isSuperAdmin;
  }

  /// Get admin display name with level
  String get adminDisplayName {
    return '${user.displayName} (${adminLevel.name.replaceAll('_', ' ').toUpperCase()})';
  }

  AdminUser copyWith({
    User? user,
    AdminLevel? adminLevel,
    Set<SystemPermission>? systemPermissions,
    List<String>? organizationAccess,
    AdminSettings? adminSettings,
    DateTime? lastAdminActivity,
    DateTime? adminCreatedAt,
    AdminStatus? adminStatus,
    Map<String, dynamic>? adminMetadata,
  }) {
    return AdminUser(
      user: user ?? this.user,
      adminLevel: adminLevel ?? this.adminLevel,
      systemPermissions: systemPermissions ?? this.systemPermissions,
      organizationAccess: organizationAccess ?? this.organizationAccess,
      adminSettings: adminSettings ?? this.adminSettings,
      lastAdminActivity: lastAdminActivity ?? this.lastAdminActivity,
      adminCreatedAt: adminCreatedAt ?? this.adminCreatedAt,
      adminStatus: adminStatus ?? this.adminStatus,
      adminMetadata: adminMetadata ?? this.adminMetadata,
    );
  }

  @override
  List<Object?> get props => [
        user,
        adminLevel,
        systemPermissions,
        organizationAccess,
        adminSettings,
        lastAdminActivity,
        adminCreatedAt,
        adminStatus,
        adminMetadata,
      ];

  @override
  bool get stringify => true;
}

/// System-level permissions for administrators
enum SystemPermission {
  // User management
  @JsonValue('user_management')
  userManagement,
  @JsonValue('user_impersonation')
  userImpersonation,
  @JsonValue('user_data_export')
  userDataExport,

  // Organization management
  @JsonValue('organization_management')
  organizationManagement,
  @JsonValue('organization_billing')
  organizationBilling,
  @JsonValue('organization_deletion')
  organizationDeletion,

  // System administration
  @JsonValue('system_logs')
  systemLogs,
  @JsonValue('system_health')
  systemHealth,
  @JsonValue('system_maintenance')
  systemMaintenance,
  @JsonValue('database_access')
  databaseAccess,

  // Billing and subscriptions
  @JsonValue('billing_management')
  billingManagement,
  @JsonValue('subscription_management')
  subscriptionManagement,
  @JsonValue('payment_processing')
  paymentProcessing,

  // Support and assistance
  @JsonValue('support_tickets')
  supportTickets,
  @JsonValue('user_assistance')
  userAssistance,
  @JsonValue('chat_support')
  chatSupport,

  // Analytics and reporting
  @JsonValue('global_analytics')
  globalAnalytics,
  @JsonValue('system_reports')
  systemReports,
  @JsonValue('data_exports')
  dataExports,

  // Feature management
  @JsonValue('feature_flags')
  featureFlags,
  @JsonValue('ab_testing')
  abTesting,
  @JsonValue('configuration_management')
  configurationManagement,
}

/// Admin account status
enum AdminStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
  @JsonValue('pending_review')
  pendingReview,
}

/// Admin-specific settings and preferences
@JsonSerializable()
class AdminSettings extends Equatable {
  /// Dashboard layout preferences
  final Map<String, dynamic> dashboardLayout;

  /// Notification preferences for admin activities
  final AdminNotificationSettings notifications;

  /// Security settings
  final AdminSecuritySettings security;

  /// Display preferences
  final AdminDisplaySettings display;

  const AdminSettings({
    required this.dashboardLayout,
    required this.notifications,
    required this.security,
    required this.display,
  });

  factory AdminSettings.fromJson(Map<String, dynamic> json) => 
      _$AdminSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$AdminSettingsToJson(this);

  /// Create default admin settings
  factory AdminSettings.defaults() {
    return AdminSettings(
      dashboardLayout: {
        'widgets': ['system_health', 'recent_users', 'organization_stats'],
        'layout': 'grid',
        'columns': 3,
      },
      notifications: AdminNotificationSettings.defaults(),
      security: AdminSecuritySettings.defaults(),
      display: AdminDisplaySettings.defaults(),
    );
  }

  @override
  List<Object?> get props => [dashboardLayout, notifications, security, display];

  @override
  bool get stringify => true;
}

/// Admin notification settings
@JsonSerializable()
class AdminNotificationSettings extends Equatable {
  /// System health alerts
  final bool systemHealthAlerts;

  /// User activity alerts
  final bool userActivityAlerts;

  /// Security incident alerts
  final bool securityAlerts;

  /// Billing and payment alerts
  final bool billingAlerts;

  /// Support ticket alerts
  final bool supportTicketAlerts;

  /// Email notifications enabled
  final bool emailNotifications;

  /// Push notifications enabled
  final bool pushNotifications;

  const AdminNotificationSettings({
    required this.systemHealthAlerts,
    required this.userActivityAlerts,
    required this.securityAlerts,
    required this.billingAlerts,
    required this.supportTicketAlerts,
    required this.emailNotifications,
    required this.pushNotifications,
  });

  factory AdminNotificationSettings.fromJson(Map<String, dynamic> json) => 
      _$AdminNotificationSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$AdminNotificationSettingsToJson(this);

  factory AdminNotificationSettings.defaults() {
    return AdminNotificationSettings(
      systemHealthAlerts: true,
      userActivityAlerts: false,
      securityAlerts: true,
      billingAlerts: true,
      supportTicketAlerts: true,
      emailNotifications: true,
      pushNotifications: false,
    );
  }

  @override
  List<Object?> get props => [
        systemHealthAlerts,
        userActivityAlerts,
        securityAlerts,
        billingAlerts,
        supportTicketAlerts,
        emailNotifications,
        pushNotifications,
      ];

  @override
  bool get stringify => true;
}

/// Admin security settings
@JsonSerializable()
class AdminSecuritySettings extends Equatable {
  /// Require two-factor authentication
  final bool requireTwoFactor;

  /// Session timeout in minutes
  final int sessionTimeoutMinutes;

  /// Require password confirmation for sensitive actions
  final bool requirePasswordConfirmation;

  /// Log all admin actions
  final bool logAllActions;

  /// IP address restrictions (empty = no restrictions)
  final List<String> allowedIpRanges;

  const AdminSecuritySettings({
    required this.requireTwoFactor,
    required this.sessionTimeoutMinutes,
    required this.requirePasswordConfirmation,
    required this.logAllActions,
    required this.allowedIpRanges,
  });

  factory AdminSecuritySettings.fromJson(Map<String, dynamic> json) => 
      _$AdminSecuritySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$AdminSecuritySettingsToJson(this);

  factory AdminSecuritySettings.defaults() {
    return AdminSecuritySettings(
      requireTwoFactor: true,
      sessionTimeoutMinutes: 120, // 2 hours
      requirePasswordConfirmation: true,
      logAllActions: true,
      allowedIpRanges: [], // No IP restrictions by default
    );
  }

  @override
  List<Object?> get props => [
        requireTwoFactor,
        sessionTimeoutMinutes,
        requirePasswordConfirmation,
        logAllActions,
        allowedIpRanges,
      ];

  @override
  bool get stringify => true;
}

/// Admin display settings
@JsonSerializable()
class AdminDisplaySettings extends Equatable {
  /// Theme preference
  final String theme; // 'light', 'dark', 'auto'

  /// Default page size for lists
  final int defaultPageSize;

  /// Show advanced features by default
  final bool showAdvancedFeatures;

  /// Timezone for date displays
  final String timezone;

  /// Language preference
  final String language;

  const AdminDisplaySettings({
    required this.theme,
    required this.defaultPageSize,
    required this.showAdvancedFeatures,
    required this.timezone,
    required this.language,
  });

  factory AdminDisplaySettings.fromJson(Map<String, dynamic> json) => 
      _$AdminDisplaySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$AdminDisplaySettingsToJson(this);

  factory AdminDisplaySettings.defaults() {
    return AdminDisplaySettings(
      theme: 'auto',
      defaultPageSize: 25,
      showAdvancedFeatures: false,
      timezone: 'UTC',
      language: 'en',
    );
  }

  @override
  List<Object?> get props => [
        theme,
        defaultPageSize,
        showAdvancedFeatures,
        timezone,
        language,
      ];

  @override
  bool get stringify => true;
}