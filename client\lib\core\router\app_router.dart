import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../config/app_config.dart';
import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/auth/register_screen.dart';
import '../../presentation/screens/home/<USER>';
import '../../presentation/screens/gamification/gamification_screen.dart';
import '../../presentation/screens/gamification/achievements_screen.dart';
import '../../presentation/screens/gamification/leaderboard_screen.dart';
import '../../presentation/screens/messaging/chat_list_screen.dart';
import '../../presentation/screens/messaging/chat_screen.dart';
import '../../presentation/screens/quests/quest_list_screen.dart';
import '../../presentation/screens/freelancing/freelancing_screen.dart';
import '../../presentation/screens/learning/learning_screen.dart';
import '../../presentation/screens/analytics/analytics_screen.dart';
import '../../presentation/screens/collaboration/collaboration_screen.dart';
import '../../presentation/screens/enterprise/enterprise_screen.dart';

/// Application router configuration
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppConfig.homeRoute,
    routes: [
      // Authentication routes
      GoRoute(
        path: AppConfig.loginRoute,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppConfig.registerRoute,
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      
      // Main app routes
      GoRoute(
        path: AppConfig.homeRoute,
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: AppConfig.gamificationRoute,
        name: 'gamification',
        builder: (context, state) => const GamificationScreen(),
      ),
      GoRoute(
        path: '/achievements',
        name: 'achievements',
        builder: (context, state) => const AchievementsScreen(),
      ),
      GoRoute(
        path: '/leaderboard',
        name: 'leaderboard',
        builder: (context, state) => const LeaderboardScreen(),
      ),
      GoRoute(
        path: '/messages',
        name: 'messages',
        builder: (context, state) => const ChatListScreen(),
      ),
      GoRoute(
        path: '/chat',
        name: 'chat',
        builder: (context, state) {
          final args = state.extra as Map<String, dynamic>? ?? {};
          return ChatScreen(
            chatId: args['chatId'] ?? '',
            chatName: args['chatName'],
          );
        },
      ),
      GoRoute(
        path: '/quests',
        name: 'quests',
        builder: (context, state) => const QuestListScreen(),
      ),
      GoRoute(
        path: AppConfig.freelancingRoute,
        name: 'freelancing',
        builder: (context, state) => const FreelancingScreen(),
      ),
      GoRoute(
        path: AppConfig.learningRoute,
        name: 'learning',
        builder: (context, state) => const LearningScreen(),
      ),
      GoRoute(
        path: AppConfig.analyticsRoute,
        name: 'analytics',
        builder: (context, state) => const AnalyticsScreen(),
      ),
      GoRoute(
        path: AppConfig.collaborationRoute,
        name: 'collaboration',
        builder: (context, state) => const CollaborationScreen(),
      ),
      GoRoute(
        path: AppConfig.enterpriseRoute,
        name: 'enterprise',
        builder: (context, state) => const EnterpriseScreen(),
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: ${state.error}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}