import 'package:equatable/equatable.dart';

/// Organization member status
enum OrganizationMemberStatus {
  active,
  inactive,
  invited,
  suspended,
  leftOrganization,
}

/// Organization member model
class OrganizationMember extends Equatable {
  /// Unique member identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// User ID
  final String userId;

  /// User email
  final String email;

  /// User display name
  final String displayName;

  /// User avatar URL
  final String? avatarUrl;

  /// Member status
  final OrganizationMemberStatus status;

  /// Member roles (can have multiple roles)
  final List<String> roleIds;

  /// Direct permissions (in addition to role permissions)
  final Map<String, dynamic>? directPermissions;

  /// Team memberships
  final List<String> teamIds;

  /// Member title/position
  final String? title;

  /// Member department
  final String? department;

  /// Member location
  final String? location;

  /// Member timezone
  final String? timezone;

  /// Last login timestamp
  final DateTime? lastLoginAt;

  /// Invitation details
  final Map<String, dynamic>? invitationDetails;

  /// Member metadata
  final Map<String, dynamic>? metadata;

  /// Join timestamp
  final DateTime joinedAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Left organization timestamp
  final DateTime? leftAt;

  const OrganizationMember({
    required this.id,
    required this.organizationId,
    required this.userId,
    required this.email,
    required this.displayName,
    this.avatarUrl,
    required this.status,
    required this.roleIds,
    this.directPermissions,
    required this.teamIds,
    this.title,
    this.department,
    this.location,
    this.timezone,
    this.lastLoginAt,
    this.invitationDetails,
    this.metadata,
    required this.joinedAt,
    required this.updatedAt,
    this.leftAt,
  });

  /// Create OrganizationMember from JSON
  factory OrganizationMember.fromJson(Map<String, dynamic> json) {
    return OrganizationMember(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      userId: json['userId'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      status: OrganizationMemberStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => OrganizationMemberStatus.active,
      ),
      roleIds: List<String>.from(json['roleIds'] as List),
      directPermissions: json['directPermissions'] != null
          ? Map<String, dynamic>.from(json['directPermissions'] as Map)
          : null,
      teamIds: List<String>.from(json['teamIds'] as List),
      title: json['title'] as String?,
      department: json['department'] as String?,
      location: json['location'] as String?,
      timezone: json['timezone'] as String?,
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
      invitationDetails: json['invitationDetails'] != null
          ? Map<String, dynamic>.from(json['invitationDetails'] as Map)
          : null,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      leftAt: json['leftAt'] != null
          ? DateTime.parse(json['leftAt'] as String)
          : null,
    );
  }

  /// Convert OrganizationMember to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'userId': userId,
      'email': email,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'status': status.name,
      'roleIds': roleIds,
      'directPermissions': directPermissions,
      'teamIds': teamIds,
      'title': title,
      'department': department,
      'location': location,
      'timezone': timezone,
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'invitationDetails': invitationDetails,
      'metadata': metadata,
      'joinedAt': joinedAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'leftAt': leftAt?.toIso8601String(),
    };
  }

  /// Check if member is active
  bool get isActive => status == OrganizationMemberStatus.active;

  /// Check if member is pending invitation
  bool get isPendingInvitation => status == OrganizationMemberStatus.invited;

  /// Check if member is suspended
  bool get isSuspended => status == OrganizationMemberStatus.suspended;

  /// Check if member has left
  bool get hasLeft => status == OrganizationMemberStatus.leftOrganization;

  /// Get member tenure in days
  int get tenureDays {
    final endDate = leftAt ?? DateTime.now();
    return endDate.difference(joinedAt).inDays;
  }

  /// Get days since last login
  int? get daysSinceLastLogin {
    if (lastLoginAt == null) return null;
    return DateTime.now().difference(lastLoginAt!).inDays;
  }

  /// Check if member is recently active
  bool get isRecentlyActive {
    if (lastLoginAt == null) return false;
    return DateTime.now().difference(lastLoginAt!).inDays <= 7;
  }

  /// Check if member has specific role
  bool hasRole(String roleId) {
    return roleIds.contains(roleId);
  }

  /// Check if member has any of the given roles
  bool hasAnyRole(List<String> roleIds) {
    return roleIds.any(this.roleIds.contains);
  }

  /// Check if member is in specific team
  bool isInTeam(String teamId) {
    return teamIds.contains(teamId);
  }

  /// Get invitation age in days
  int? get invitationAgeDays {
    if (status != OrganizationMemberStatus.invited || invitationDetails == null) {
      return null;
    }
    final invitedAt = invitationDetails!['invitedAt'];
    if (invitedAt == null) return null;
    return DateTime.now().difference(DateTime.parse(invitedAt)).inDays;
  }

  /// Check if invitation has expired
  bool get isInvitationExpired {
    if (status != OrganizationMemberStatus.invited) return false;
    final ageDays = invitationAgeDays;
    if (ageDays == null) return false;
    return ageDays > 30; // 30 days expiration
  }

  /// Get member initials for avatar
  String get initials {
    final parts = displayName.split(' ');
    if (parts.length == 1) {
      return parts[0].substring(0, 1).toUpperCase();
    }
    return '${parts[0].substring(0, 1).toUpperCase()}${parts[1].substring(0, 1).toUpperCase()}';
  }

  /// Create a copy with updated fields
  OrganizationMember copyWith({
    String? id,
    String? organizationId,
    String? userId,
    String? email,
    String? displayName,
    String? avatarUrl,
    OrganizationMemberStatus? status,
    List<String>? roleIds,
    Map<String, dynamic>? directPermissions,
    List<String>? teamIds,
    String? title,
    String? department,
    String? location,
    String? timezone,
    DateTime? lastLoginAt,
    Map<String, dynamic>? invitationDetails,
    Map<String, dynamic>? metadata,
    DateTime? joinedAt,
    DateTime? updatedAt,
    DateTime? leftAt,
  }) {
    return OrganizationMember(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      status: status ?? this.status,
      roleIds: roleIds ?? this.roleIds,
      directPermissions: directPermissions ?? this.directPermissions,
      teamIds: teamIds ?? this.teamIds,
      title: title ?? this.title,
      department: department ?? this.department,
      location: location ?? this.location,
      timezone: timezone ?? this.timezone,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      invitationDetails: invitationDetails ?? this.invitationDetails,
      metadata: metadata ?? this.metadata,
      joinedAt: joinedAt ?? this.joinedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      leftAt: leftAt ?? this.leftAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        userId,
        email,
        displayName,
        avatarUrl,
        status,
        roleIds,
        directPermissions,
        teamIds,
        title,
        department,
        location,
        timezone,
        lastLoginAt,
        invitationDetails,
        metadata,
        joinedAt,
        updatedAt,
        leftAt,
      ];

  @override
  bool get stringify => true;
}
