import 'package:equatable/equatable.dart';

/// SSO provider types
enum SSOProviderService {
  saml2,
  oauth2,
  activeDirectory,
  google,
  microsoft,
  okta,
  auth0,
}

/// SSO configuration status
enum SSOStatus {
  disabled,
  enabled,
  testing,
  error,
}

/// Single Sign-On configuration model
class SSOConfiguration extends Equatable {
  /// Unique SSO configuration identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// SSO provider type
  final SSOProviderService provider;

  /// Configuration name
  final String name;

  /// Configuration description
  final String? description;

  /// SSO status
  final SSOStatus status;

  /// Provider-specific configuration
  final Map<String, dynamic> providerConfig;

  /// Attribute mappings for user data
  final Map<String, String> attributeMappings;

  /// Auto-provisioning settings
  final Map<String, dynamic> autoProvisioningConfig;

  /// Security settings
  final Map<String, dynamic> securitySettings;

  /// Domain restrictions
  final List<String>? allowedDomains;

  /// Whether this is the default SSO for the organization
  final bool isDefault;

  /// Whether auto-provisioning is enabled
  final bool autoProvisioningEnabled;

  /// Whether JIT (Just-In-Time) provisioning is enabled
  final bool jitProvisioningEnabled;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Last successful authentication
  final DateTime? lastUsedAt;

  const SSOConfiguration({
    required this.id,
    required this.organizationId,
    required this.provider,
    required this.name,
    this.description,
    required this.status,
    required this.providerConfig,
    required this.attributeMappings,
    required this.autoProvisioningConfig,
    required this.securitySettings,
    this.allowedDomains,
    required this.isDefault,
    required this.autoProvisioningEnabled,
    required this.jitProvisioningEnabled,
    required this.createdAt,
    required this.updatedAt,
    this.lastUsedAt,
  });

  /// Create SSOConfiguration from JSON
  factory SSOConfiguration.fromJson(Map<String, dynamic> json) {
    return SSOConfiguration(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      provider: SSOProviderService.values.firstWhere(
        (e) => e.name == json['provider'],
        orElse: () => SSOProviderService.oauth2,
      ),
      name: json['name'] as String,
      description: json['description'] as String?,
      status: SSOStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SSOStatus.disabled,
      ),
      providerConfig: Map<String, dynamic>.from(json['providerConfig'] as Map),
      attributeMappings: Map<String, String>.from(json['attributeMappings'] as Map),
      autoProvisioningConfig: Map<String, dynamic>.from(json['autoProvisioningConfig'] as Map),
      securitySettings: Map<String, dynamic>.from(json['securitySettings'] as Map),
      allowedDomains: json['allowedDomains'] != null
          ? List<String>.from(json['allowedDomains'] as List)
          : null,
      isDefault: json['isDefault'] as bool,
      autoProvisioningEnabled: json['autoProvisioningEnabled'] as bool,
      jitProvisioningEnabled: json['jitProvisioningEnabled'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastUsedAt: json['lastUsedAt'] != null
          ? DateTime.parse(json['lastUsedAt'] as String)
          : null,
    );
  }

  /// Convert SSOConfiguration to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'provider': provider.name,
      'name': name,
      'description': description,
      'status': status.name,
      'providerConfig': providerConfig,
      'attributeMappings': attributeMappings,
      'autoProvisioningConfig': autoProvisioningConfig,
      'securitySettings': securitySettings,
      'allowedDomains': allowedDomains,
      'isDefault': isDefault,
      'autoProvisioningEnabled': autoProvisioningEnabled,
      'jitProvisioningEnabled': jitProvisioningEnabled,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastUsedAt': lastUsedAt?.toIso8601String(),
    };
  }

  /// Check if SSO is active
  bool get isActive => status == SSOStatus.enabled;

  /// Check if SSO is in testing mode
  bool get isTesting => status == SSOStatus.testing;

  /// Get provider display name
  String get providerDisplayName {
    switch (provider) {
      case SSOProviderService.saml2:
        return 'SAML 2.0';
      case SSOProviderService.oauth2:
        return 'OAuth 2.0';
      case SSOProviderService.activeDirectory:
        return 'Active Directory';
      case SSOProviderService.google:
        return 'Google';
      case SSOProviderService.microsoft:
        return 'Microsoft';
      case SSOProviderService.okta:
        return 'Okta';
      case SSOProviderService.auth0:
        return 'Auth0';
    }
  }

  /// Get configuration validation status
  bool get isConfigurationValid {
    switch (provider) {
      case SSOProviderService.saml2:
        return providerConfig.containsKey('entityId') &&
               providerConfig.containsKey('ssoUrl') &&
               providerConfig.containsKey('certificate');
      case SSOProviderService.oauth2:
        return providerConfig.containsKey('clientId') &&
               providerConfig.containsKey('clientSecret') &&
               providerConfig.containsKey('authorizationUrl') &&
               providerConfig.containsKey('tokenUrl');
      case SSOProviderService.activeDirectory:
        return providerConfig.containsKey('tenantId') &&
               providerConfig.containsKey('clientId') &&
               providerConfig.containsKey('clientSecret');
      default:
        return providerConfig.containsKey('clientId') &&
               providerConfig.containsKey('clientSecret');
    }
  }

  /// Get required configuration keys for provider
  List<String> get requiredConfigKeys {
    switch (provider) {
      case SSOProviderService.saml2:
        return ['entityId', 'ssoUrl', 'certificate', 'signatureAlgorithm'];
      case SSOProviderService.oauth2:
        return ['clientId', 'clientSecret', 'authorizationUrl', 'tokenUrl', 'userInfoUrl'];
      case SSOProviderService.activeDirectory:
        return ['tenantId', 'clientId', 'clientSecret', 'resource'];
      case SSOProviderService.google:
        return ['clientId', 'clientSecret', 'redirectUri'];
      case SSOProviderService.microsoft:
        return ['tenantId', 'clientId', 'clientSecret', 'redirectUri'];
      case SSOProviderService.okta:
        return ['domain', 'clientId', 'clientSecret', 'redirectUri'];
      case SSOProviderService.auth0:
        return ['domain', 'clientId', 'clientSecret', 'redirectUri'];
    }
  }

  /// Get default attribute mappings for provider
  Map<String, String> get defaultAttributeMappings {
    switch (provider) {
      case SSOProviderService.saml2:
        return {
          'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
          'firstName': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
          'lastName': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
          'displayName': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name',
        };
      default:
        return {
          'email': 'email',
          'firstName': 'given_name',
          'lastName': 'family_name',
          'displayName': 'name',
        };
    }
  }

  /// Create a copy with updated fields
  SSOConfiguration copyWith({
    String? id,
    String? organizationId,
    SSOProviderService? provider,
    String? name,
    String? description,
    SSOStatus? status,
    Map<String, dynamic>? providerConfig,
    Map<String, String>? attributeMappings,
    Map<String, dynamic>? autoProvisioningConfig,
    Map<String, dynamic>? securitySettings,
    List<String>? allowedDomains,
    bool? isDefault,
    bool? autoProvisioningEnabled,
    bool? jitProvisioningEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastUsedAt,
  }) {
    return SSOConfiguration(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      provider: provider ?? this.provider,
      name: name ?? this.name,
      description: description ?? this.description,
      status: status ?? this.status,
      providerConfig: providerConfig ?? this.providerConfig,
      attributeMappings: attributeMappings ?? this.attributeMappings,
      autoProvisioningConfig: autoProvisioningConfig ?? this.autoProvisioningConfig,
      securitySettings: securitySettings ?? this.securitySettings,
      allowedDomains: allowedDomains ?? this.allowedDomains,
      isDefault: isDefault ?? this.isDefault,
      autoProvisioningEnabled: autoProvisioningEnabled ?? this.autoProvisioningEnabled,
      jitProvisioningEnabled: jitProvisioningEnabled ?? this.jitProvisioningEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        provider,
        name,
        description,
        status,
        providerConfig,
        attributeMappings,
        autoProvisioningConfig,
        securitySettings,
        allowedDomains,
        isDefault,
        autoProvisioningEnabled,
        jitProvisioningEnabled,
        createdAt,
        updatedAt,
        lastUsedAt,
      ];

  @override
  bool get stringify => true;

  /// Create default configurations for common providers
  static SSOConfiguration createGoogleConfig(String organizationId) {
    return SSOConfiguration(
      id: 'google-${DateTime.now().millisecondsSinceEpoch}',
      organizationId: organizationId,
      provider: SSOProviderService.google,
      name: 'Google OAuth',
      description: 'Google OAuth 2.0 integration',
      status: SSOStatus.disabled,
      providerConfig: {
        'clientId': '',
        'clientSecret': '',
        'redirectUri': 'https://your-domain.com/auth/google/callback',
        'scope': 'openid profile email',
      },
      attributeMappings: {
        'email': 'email',
        'firstName': 'given_name',
        'lastName': 'family_name',
        'displayName': 'name',
        'picture': 'picture',
      },
      autoProvisioningConfig: {
        'defaultRole': 'member',
        'defaultTeams': [],
        'createTeamsFromGroups': false,
      },
      securitySettings: {
        'enforceEmailVerification': true,
        'allowedDomains': [],
        'sessionTimeout': 86400, // 24 hours
      },
      isDefault: false,
      autoProvisioningEnabled: true,
      jitProvisioningEnabled: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static SSOConfiguration createMicrosoftConfig(String organizationId) {
    return SSOConfiguration(
      id: 'microsoft-${DateTime.now().millisecondsSinceEpoch}',
      organizationId: organizationId,
      provider: SSOProviderService.microsoft,
      name: 'Microsoft Azure AD',
      description: 'Microsoft Azure Active Directory integration',
      status: SSOStatus.disabled,
      providerConfig: {
        'tenantId': '',
        'clientId': '',
        'clientSecret': '',
        'redirectUri': 'https://your-domain.com/auth/microsoft/callback',
        'scope': 'openid profile email User.Read',
      },
      attributeMappings: {
        'email': 'mail',
        'firstName': 'givenName',
        'lastName': 'surname',
        'displayName': 'displayName',
      },
      autoProvisioningConfig: {
        'defaultRole': 'member',
        'defaultTeams': [],
        'syncGroupMembership': true,
      },
      securitySettings: {
        'enforceEmailVerification': true,
        'allowedDomains': [],
        'sessionTimeout': 86400,
      },
      isDefault: false,
      autoProvisioningEnabled: true,
      jitProvisioningEnabled: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static SSOConfiguration createSAMLConfig(String organizationId) {
    return SSOConfiguration(
      id: 'saml-${DateTime.now().millisecondsSinceEpoch}',
      organizationId: organizationId,
      provider: SSOProviderService.saml2,
      name: 'SAML 2.0',
      description: 'SAML 2.0 Single Sign-On',
      status: SSOStatus.disabled,
      providerConfig: {
        'entityId': 'https://your-domain.com/saml/metadata',
        'ssoUrl': '',
        'sloUrl': '',
        'certificate': '',
        'signatureAlgorithm': 'RSA-SHA256',
        'nameIdFormat': 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
      },
      attributeMappings: {
        'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
        'firstName': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
        'lastName': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
        'displayName': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name',
      },
      autoProvisioningConfig: {
        'defaultRole': 'member',
        'defaultTeams': [],
        'useGroups': true,
        'groupAttribute': 'http://schemas.xmlsoap.org/claims/Group',
      },
      securitySettings: {
        'enforceSignedAssertions': true,
        'enforceSignedResponses': true,
        'allowedClockDrift': 300, // 5 minutes
        'sessionTimeout': 86400,
      },
      isDefault: false,
      autoProvisioningEnabled: true,
      jitProvisioningEnabled: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
