import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/collaboration_service.dart';

/// Collaboration routes handler
class CollaborationRoutes {
  static late CollaborationService _collaborationService;

  static void initialize(CollaborationService collaborationService) {
    _collaborationService = collaborationService;
  }

  static Router createRouter() {
    final router = Router()
      // Team quest management
      ..post('/team-quest/create', _createTeamQuestHandler)
      ..post('/team-quest/<questId>/join', _joinTeamQuestHandler)
      ..get('/team-quest/<questId>/status', _getTeamQuestStatusHandler)
      
      // Team task management
      ..post('/team-quest/<questId>/complete-task', _completeTeamTaskHandler)
      
      // Team communication
      ..post('/team-quest/<questId>/message', _sendTeamMessageHandler)
      ..get('/team-quest/<questId>/messages', _getTeamMessagesHandler)
      
      // User's team activities
      ..get('/user/<userId>/team-quests', _getUserTeamQuestsHandler);

    return router;
  }

  static Future<Response> _createTeamQuestHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final creatorId = data['creator_id'] as String;
      final title = data['title'] as String;
      final description = data['description'] as String;
      final invitedUserIds = List<String>.from(data['invited_users'] ?? []);
      final maxParticipants = data['max_participants'] as int? ?? 10;
      
      final result = await _collaborationService.createTeamQuest(
        creatorId: creatorId,
        title: title,
        description: description,
        invitedUserIds: invitedUserIds,
        maxParticipants: maxParticipants,
      );
      
      if (result == null) {
        return Response.internalServerError(
          body: jsonEncode({'error': 'Failed to create team quest'}),
          headers: {'content-type': 'application/json'},
        );
      }
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create team quest: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _joinTeamQuestHandler(Request request) async {
    final questId = request.params['questId']!;
    
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      final userId = data['user_id'] as String;
      
      final result = await _collaborationService.joinTeamQuest(
        questId: questId,
        userId: userId,
      );
      
      if (result == null) {
        return Response.internalServerError(
          body: jsonEncode({'error': 'Failed to join team quest'}),
          headers: {'content-type': 'application/json'},
        );
      }
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to join team quest: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getTeamQuestStatusHandler(Request request) async {
    final questId = request.params['questId']!;
    
    try {
      final result = await _collaborationService.getTeamQuestStatus(questId);
      
      if (result == null) {
        return Response.notFound(
          jsonEncode({'error': 'Team quest not found'}),
        );
      }
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get team quest status: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _completeTeamTaskHandler(Request request) async {
    final questId = request.params['questId']!;
    
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      final userId = data['user_id'] as String;
      final taskDescription = data['task_description'] as String;
      
      final result = await _collaborationService.completeTeamTask(
        questId: questId,
        userId: userId,
        taskDescription: taskDescription,
      );
      
      if (result == null) {
        return Response.internalServerError(
          body: jsonEncode({'error': 'Failed to complete team task'}),
          headers: {'content-type': 'application/json'},
        );
      }
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to complete team task: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _sendTeamMessageHandler(Request request) async {
    final questId = request.params['questId']!;
    
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      final senderId = data['sender_id'] as String;
      final message = data['message'] as String;
      
      final result = await _collaborationService.sendTeamMessage(
        questId: questId,
        senderId: senderId,
        message: message,
      );
      
      if (result == null) {
        return Response.internalServerError(
          body: jsonEncode({'error': 'Failed to send team message'}),
          headers: {'content-type': 'application/json'},
        );
      }
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to send team message: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getTeamMessagesHandler(Request request) async {
    final questId = request.params['questId']!;
    final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
    final offset = int.tryParse(request.url.queryParameters['offset'] ?? '0') ?? 0;
    
    try {
      final result = await _collaborationService.getTeamMessages(
        questId: questId,
        limit: limit,
        offset: offset,
      );
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get team messages: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserTeamQuestsHandler(Request request) async {
    final userId = request.params['userId']!;
    final status = request.url.queryParameters['status'];
    
    try {
      final result = await _collaborationService.getUserTeamQuests(
        userId: userId,
        status: status,
      );
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get user team quests: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }
}