// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
  id: json['id'] as String,
  chatId: json['chatId'] as String,
  senderId: json['senderId'] as String,
  senderName: json['senderName'] as String,
  senderAvatar: json['senderAvatar'] as String?,
  content: json['content'] as String,
  type: $enumDecode(_$MessageTypeEnumMap, json['type']),
  status: $enumDecode(_$MessageStatusEnumMap, json['status']),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  deliveredAt: json['deliveredAt'] == null
      ? null
      : DateTime.parse(json['deliveredAt'] as String),
  readAt: json['readAt'] == null
      ? null
      : DateTime.parse(json['readAt'] as String),
  replyToId: json['replyToId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
  reactions:
      (json['reactions'] as List<dynamic>?)
          ?.map((e) => MessageReaction.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  readBy:
      (json['readBy'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isEdited: json['isEdited'] as bool? ?? false,
  isDeleted: json['isDeleted'] as bool? ?? false,
  priority:
      $enumDecodeNullable(_$MessagePriorityEnumMap, json['priority']) ??
      MessagePriority.normal,
);

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
  'id': instance.id,
  'chatId': instance.chatId,
  'senderId': instance.senderId,
  'senderName': instance.senderName,
  'senderAvatar': instance.senderAvatar,
  'content': instance.content,
  'type': _$MessageTypeEnumMap[instance.type]!,
  'status': _$MessageStatusEnumMap[instance.status]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'deliveredAt': instance.deliveredAt?.toIso8601String(),
  'readAt': instance.readAt?.toIso8601String(),
  'replyToId': instance.replyToId,
  'metadata': instance.metadata,
  'reactions': instance.reactions,
  'readBy': instance.readBy,
  'isEdited': instance.isEdited,
  'isDeleted': instance.isDeleted,
  'priority': _$MessagePriorityEnumMap[instance.priority]!,
};

const _$MessageTypeEnumMap = {
  MessageType.text: 'text',
  MessageType.image: 'image',
  MessageType.file: 'file',
  MessageType.audio: 'audio',
  MessageType.video: 'video',
  MessageType.system: 'system',
  MessageType.questInvite: 'quest_invite',
  MessageType.achievement: 'achievement',
  MessageType.reaction: 'reaction',
};

const _$MessageStatusEnumMap = {
  MessageStatus.pending: 'pending',
  MessageStatus.sent: 'sent',
  MessageStatus.delivered: 'delivered',
  MessageStatus.read: 'read',
  MessageStatus.failed: 'failed',
};

const _$MessagePriorityEnumMap = {
  MessagePriority.low: 'low',
  MessagePriority.normal: 'normal',
  MessagePriority.high: 'high',
  MessagePriority.urgent: 'urgent',
};

MessageReaction _$MessageReactionFromJson(Map<String, dynamic> json) =>
    MessageReaction(
      emoji: json['emoji'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$MessageReactionToJson(MessageReaction instance) =>
    <String, dynamic>{
      'emoji': instance.emoji,
      'userId': instance.userId,
      'userName': instance.userName,
      'createdAt': instance.createdAt.toIso8601String(),
    };

Chat _$ChatFromJson(Map<String, dynamic> json) => Chat(
  id: json['id'] as String,
  name: json['name'] as String?,
  description: json['description'] as String?,
  type: $enumDecode(_$ChatTypeEnumMap, json['type']),
  createdBy: json['createdBy'] as String,
  participantIds: (json['participantIds'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  settings: ChatSettings.fromJson(json['settings'] as Map<String, dynamic>),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  lastMessageId: json['lastMessageId'] as String?,
  lastMessageAt: json['lastMessageAt'] == null
      ? null
      : DateTime.parse(json['lastMessageAt'] as String),
  isArchived: json['isArchived'] as bool? ?? false,
  isMuted: json['isMuted'] as bool? ?? false,
  avatarUrl: json['avatarUrl'] as String?,
);

Map<String, dynamic> _$ChatToJson(Chat instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'type': _$ChatTypeEnumMap[instance.type]!,
  'createdBy': instance.createdBy,
  'participantIds': instance.participantIds,
  'settings': instance.settings,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'lastMessageId': instance.lastMessageId,
  'lastMessageAt': instance.lastMessageAt?.toIso8601String(),
  'isArchived': instance.isArchived,
  'isMuted': instance.isMuted,
  'avatarUrl': instance.avatarUrl,
};

const _$ChatTypeEnumMap = {
  ChatType.direct: 'direct',
  ChatType.group: 'group',
  ChatType.quest: 'quest',
  ChatType.team: 'team',
  ChatType.public: 'public',
};

ChatSettings _$ChatSettingsFromJson(Map<String, dynamic> json) => ChatSettings(
  allowMemberInvites: json['allowMemberInvites'] as bool? ?? true,
  allowMessageEditing: json['allowMessageEditing'] as bool? ?? true,
  allowMessageDeletion: json['allowMessageDeletion'] as bool? ?? false,
  allowFileUploads: json['allowFileUploads'] as bool? ?? true,
  maxFileSize: (json['maxFileSize'] as num?)?.toInt() ?? 25 * 1024 * 1024,
  allowReactions: json['allowReactions'] as bool? ?? true,
  allowThreading: json['allowThreading'] as bool? ?? true,
);

Map<String, dynamic> _$ChatSettingsToJson(ChatSettings instance) =>
    <String, dynamic>{
      'allowMemberInvites': instance.allowMemberInvites,
      'allowMessageEditing': instance.allowMessageEditing,
      'allowMessageDeletion': instance.allowMessageDeletion,
      'allowFileUploads': instance.allowFileUploads,
      'maxFileSize': instance.maxFileSize,
      'allowReactions': instance.allowReactions,
      'allowThreading': instance.allowThreading,
    };
