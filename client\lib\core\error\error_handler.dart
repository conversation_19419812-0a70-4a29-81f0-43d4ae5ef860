import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:developer' as developer;

/// Comprehensive error handler for the application
class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  // Simple logging using developer.log
  final List<ErrorReporter> _reporters = [];
  final Map<Type, ErrorProcessor> _processors = {};

  /// Initialize error handling
  Future<void> initialize() async {
    // Set up Flutter error handling
    FlutterError.onError = _handleFlutterError;
    
    // Set up platform dispatcher error handling
    PlatformDispatcher.instance.onError = _handlePlatformError;
    
    // Set up zone error handling for async errors
    runZonedGuarded(() {
      // Application initialization would go here
    }, _handleZoneError);

    developer.log('ErrorHandler initialized');
  }

  /// Add error reporter
  void addReporter(ErrorReporter reporter) {
    _reporters.add(reporter);
  }

  /// Add error processor for specific error types
  void addProcessor<T extends Exception>(ErrorProcessor<T> processor) {
    _processors[T] = processor;
  }

  /// Handle Flutter framework errors
  void _handleFlutterError(FlutterErrorDetails details) {
    developer.log(
      'Flutter Error: ${details.exception}',
      error: details.exception,
      stackTrace: details.stack,
    );

    // Report to external services
    _reportError(
      details.exception,
      details.stack,
      context: {'type': 'flutter_error', 'library': details.library},
    );

    // In debug mode, show the red screen
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }

  /// Handle platform-specific errors
  bool _handlePlatformError(Object error, StackTrace stack) {
    developer.log(
      'Platform Error: $error',
      error: error,
      stackTrace: stack,
    );

    _reportError(error, stack, context: {'type': 'platform_error'});
    return true; // Handled
  }

  /// Handle zone errors (async errors)
  void _handleZoneError(Object error, StackTrace stack) {
    developer.log(
      'Zone Error: $error',
      error: error,
      stackTrace: stack,
    );

    _reportError(error, stack, context: {'type': 'zone_error'});
  }

  /// Handle application errors with context
  Future<void> handleError(
    Object error,
    StackTrace? stackTrace, {
    String? context,
    Map<String, dynamic>? metadata,
    ErrorSeverity severity = ErrorSeverity.error,
  }) async {
    final stack = stackTrace ?? StackTrace.current;
    
    developer.log(
      'Application Error: $error (${severity.name})',
      error: error,
      stackTrace: stack,
    );

    // Process error with specific processor if available
    final processor = _processors[error.runtimeType];
    if (processor != null) {
      await processor.process(error as Exception, stack, metadata);
    }

    // Report error
    await _reportError(
      error,
      stack,
      context: {
        'severity': severity.name,
        if (context != null) 'context': context,
        if (metadata != null) ...metadata,
      },
    );
  }

  /// Handle network errors specifically
  Future<void> handleNetworkError(
    Object error,
    StackTrace stackTrace, {
    String? endpoint,
    int? statusCode,
    Map<String, dynamic>? requestData,
  }) async {
    await handleError(
      error,
      stackTrace,
      context: 'network_request',
      metadata: {
        if (endpoint != null) 'endpoint': endpoint,
        if (statusCode != null) 'status_code': statusCode,
        if (requestData != null) 'request_data': requestData,
      },
      severity: _getNetworkErrorSeverity(error, statusCode),
    );
  }

  /// Handle BLoC errors
  Future<void> handleBlocError(
    Object error,
    StackTrace stackTrace, {
    String? blocName,
    String? event,
    String? state,
  }) async {
    await handleError(
      error,
      stackTrace,
      context: 'bloc_error',
      metadata: {
        if (blocName != null) 'bloc': blocName,
        if (event != null) 'event': event,
        if (state != null) 'state': state,
      },
      severity: ErrorSeverity.error,
    );
  }

  /// Report error to external services
  Future<void> _reportError(
    Object error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? context,
  }) async {
    for (final reporter in _reporters) {
      try {
        await reporter.report(error, stackTrace, context);
      } catch (e) {
        developer.log('Failed to report error: $e');
      }
    }
  }

  // LogLevel method removed - using simple developer.log instead

  /// Determine network error severity
  ErrorSeverity _getNetworkErrorSeverity(Object error, int? statusCode) {
    if (error is SocketException || error is TimeoutException) {
      return ErrorSeverity.warning;
    }
    
    if (statusCode != null) {
      if (statusCode >= 500) return ErrorSeverity.error;
      if (statusCode >= 400) return ErrorSeverity.warning;
    }
    
    return ErrorSeverity.error;
  }

  /// Get error summary for user display
  String getUserFriendlyMessage(Object error) {
    if (error is SocketException) {
      return 'Network connection error. Please check your internet connection.';
    }
    
    if (error is TimeoutException) {
      return 'Request timed out. Please try again.';
    }
    
    if (error is FormatException) {
      return 'Data format error. Please try again later.';
    }
    
    if (error is PlatformException) {
      return error.message ?? 'Platform error occurred.';
    }
    
    // Generic message for unknown errors
    return 'An unexpected error occurred. Please try again.';
  }
}

/// Error severity levels
enum ErrorSeverity {
  info,
  warning,
  error,
  critical,
}

/// Abstract error reporter interface
abstract class ErrorReporter {
  Future<void> report(
    Object error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  );
}

/// Abstract error processor interface
abstract class ErrorProcessor<T extends Exception> {
  Future<void> process(
    T error,
    StackTrace stackTrace,
    Map<String, dynamic>? metadata,
  );
}

/// Console error reporter
class ConsoleErrorReporter implements ErrorReporter {
  @override
  Future<void> report(
    Object error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  ) async {
    print('🚨 Error Report:');
    print('Error: $error');
    if (stackTrace != null) {
      print('Stack: $stackTrace');
    }
    if (context != null) {
      print('Context: $context');
    }
  }
}

/// File error reporter
class FileErrorReporter implements ErrorReporter {
  final String filePath;
  
  FileErrorReporter(this.filePath);

  @override
  Future<void> report(
    Object error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  ) async {
    try {
      final file = File(filePath);
      final report = {
        'timestamp': DateTime.now().toIso8601String(),
        'error': error.toString(),
        'stackTrace': stackTrace?.toString(),
        'context': context,
      };
      
      await file.writeAsString(
        '${report.toString()}\n',
        mode: FileMode.append,
      );
    } catch (e) {
      print('Failed to write error report to file: $e');
    }
  }
}

/// Network error processor
class NetworkErrorProcessor implements ErrorProcessor<SocketException> {
  @override
  Future<void> process(
    SocketException error,
    StackTrace stackTrace,
    Map<String, dynamic>? metadata,
  ) async {
    // Could implement retry logic, offline mode, etc.
    print('Processing network error: ${error.message}');
  }
}
