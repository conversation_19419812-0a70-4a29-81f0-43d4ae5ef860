import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Performance monitoring and optimization utilities
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, Stopwatch> _timers = {};
  final Map<String, List<int>> _metrics = {};
  final List<PerformanceEvent> _events = [];
  
  bool _isEnabled = kDebugMode;
  Timer? _memoryTimer;
  Timer? _fpsTimer;

  /// Initialize performance monitoring
  void initialize() {
    if (!_isEnabled) return;

    // Start memory monitoring
    _startMemoryMonitoring();
    
    // Start FPS monitoring
    _startFPSMonitoring();
    
    // Log initialization
    _logEvent(PerformanceEvent(
      name: 'performance_monitor_initialized',
      timestamp: DateTime.now(),
      type: PerformanceEventType.system,
    ));
  }

  /// Start timing an operation
  void startTimer(String name) {
    if (!_isEnabled) return;
    
    _timers[name] = Stopwatch()..start();
  }

  /// Stop timing an operation and record the duration
  int stopTimer(String name) {
    if (!_isEnabled) return 0;
    
    final timer = _timers[name];
    if (timer == null) return 0;
    
    timer.stop();
    final duration = timer.elapsedMilliseconds;
    
    _recordMetric(name, duration);
    _timers.remove(name);
    
    _logEvent(PerformanceEvent(
      name: name,
      timestamp: DateTime.now(),
      type: PerformanceEventType.timing,
      duration: duration,
    ));
    
    return duration;
  }

  /// Time a function execution
  Future<T> timeFunction<T>(String name, Future<T> Function() function) async {
    if (!_isEnabled) return await function();
    
    startTimer(name);
    try {
      final result = await function();
      return result;
    } finally {
      stopTimer(name);
    }
  }

  /// Time a synchronous function execution
  T timeFunctionSync<T>(String name, T Function() function) {
    if (!_isEnabled) return function();
    
    startTimer(name);
    try {
      final result = function();
      return result;
    } finally {
      stopTimer(name);
    }
  }

  /// Record a custom metric
  void recordMetric(String name, int value) {
    if (!_isEnabled) return;
    
    _recordMetric(name, value);
    
    _logEvent(PerformanceEvent(
      name: name,
      timestamp: DateTime.now(),
      type: PerformanceEventType.metric,
      value: value,
    ));
  }

  /// Record memory usage
  void recordMemoryUsage() {
    if (!_isEnabled) return;
    
    // This would require platform-specific implementation
    // For now, we'll use a placeholder
    final memoryUsage = _getMemoryUsage();
    recordMetric('memory_usage_mb', memoryUsage);
  }

  /// Log a performance event
  void logEvent(String name, {
    PerformanceEventType type = PerformanceEventType.custom,
    Map<String, dynamic>? data,
  }) {
    if (!_isEnabled) return;
    
    _logEvent(PerformanceEvent(
      name: name,
      timestamp: DateTime.now(),
      type: type,
      data: data,
    ));
  }

  /// Get performance metrics for a specific operation
  List<int> getMetrics(String name) {
    return _metrics[name] ?? [];
  }

  /// Get average performance for an operation
  double getAverageMetric(String name) {
    final metrics = _metrics[name];
    if (metrics == null || metrics.isEmpty) return 0.0;
    
    return metrics.reduce((a, b) => a + b) / metrics.length;
  }

  /// Get performance summary
  PerformanceSummary getSummary() {
    final summary = <String, PerformanceMetricSummary>{};
    
    for (final entry in _metrics.entries) {
      final values = entry.value;
      if (values.isEmpty) continue;
      
      values.sort();
      final min = values.first;
      final max = values.last;
      final avg = values.reduce((a, b) => a + b) / values.length;
      final median = values[values.length ~/ 2];
      
      summary[entry.key] = PerformanceMetricSummary(
        name: entry.key,
        count: values.length,
        min: min,
        max: max,
        average: avg,
        median: median.toDouble(),
      );
    }
    
    return PerformanceSummary(
      metrics: summary,
      events: List.from(_events),
      generatedAt: DateTime.now(),
    );
  }

  /// Export performance data
  Map<String, dynamic> exportData() {
    final summary = getSummary();
    
    return {
      'summary': {
        'metrics': summary.metrics.map((key, value) => MapEntry(key, {
          'name': value.name,
          'count': value.count,
          'min': value.min,
          'max': value.max,
          'average': value.average,
          'median': value.median,
        })),
        'generatedAt': summary.generatedAt.toIso8601String(),
      },
      'events': summary.events.map((event) => {
        'name': event.name,
        'timestamp': event.timestamp.toIso8601String(),
        'type': event.type.name,
        'duration': event.duration,
        'value': event.value,
        'data': event.data,
      }).toList(),
    };
  }

  /// Clear all performance data
  void clear() {
    _timers.clear();
    _metrics.clear();
    _events.clear();
  }

  /// Enable or disable performance monitoring
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    
    if (!enabled) {
      _memoryTimer?.cancel();
      _fpsTimer?.cancel();
      clear();
    } else {
      initialize();
    }
  }

  /// Check if monitoring is enabled
  bool get isEnabled => _isEnabled;

  /// Start memory monitoring
  void _startMemoryMonitoring() {
    _memoryTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      recordMemoryUsage();
    });
  }

  /// Start FPS monitoring
  void _startFPSMonitoring() {
    // This would require more sophisticated FPS tracking
    // For now, we'll use a placeholder
    _fpsTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final fps = _getCurrentFPS();
      recordMetric('fps', fps);
    });
  }

  /// Record a metric value
  void _recordMetric(String name, int value) {
    _metrics.putIfAbsent(name, () => []).add(value);
    
    // Keep only last 100 values to prevent memory issues
    if (_metrics[name]!.length > 100) {
      _metrics[name]!.removeAt(0);
    }
  }

  /// Log a performance event
  void _logEvent(PerformanceEvent event) {
    _events.add(event);
    
    // Keep only last 500 events
    if (_events.length > 500) {
      _events.removeAt(0);
    }
    
    // Log to developer console in debug mode
    if (kDebugMode) {
      developer.log(
        'Performance: ${event.name}',
        name: 'PerformanceMonitor',
        time: event.timestamp,
      );
    }
  }

  /// Get current memory usage (placeholder implementation)
  int _getMemoryUsage() {
    // This would require platform-specific implementation
    // For now, return a mock value
    return 50 + (DateTime.now().millisecondsSinceEpoch % 100);
  }

  /// Get current FPS (placeholder implementation)
  int _getCurrentFPS() {
    // This would require frame timing analysis
    // For now, return a mock value
    return 58 + (DateTime.now().millisecondsSinceEpoch % 5);
  }

  /// Dispose resources
  void dispose() {
    _memoryTimer?.cancel();
    _fpsTimer?.cancel();
    clear();
  }
}

/// Performance event types
enum PerformanceEventType {
  timing,
  metric,
  system,
  custom,
  error,
}

/// Performance event data
class PerformanceEvent {
  final String name;
  final DateTime timestamp;
  final PerformanceEventType type;
  final int? duration;
  final int? value;
  final Map<String, dynamic>? data;

  const PerformanceEvent({
    required this.name,
    required this.timestamp,
    required this.type,
    this.duration,
    this.value,
    this.data,
  });
}

/// Performance metric summary
class PerformanceMetricSummary {
  final String name;
  final int count;
  final int min;
  final int max;
  final double average;
  final double median;

  const PerformanceMetricSummary({
    required this.name,
    required this.count,
    required this.min,
    required this.max,
    required this.average,
    required this.median,
  });
}

/// Complete performance summary
class PerformanceSummary {
  final Map<String, PerformanceMetricSummary> metrics;
  final List<PerformanceEvent> events;
  final DateTime generatedAt;

  const PerformanceSummary({
    required this.metrics,
    required this.events,
    required this.generatedAt,
  });
}

/// Performance monitoring extensions
extension PerformanceExtensions<T> on Future<T> {
  /// Time this future execution
  Future<T> timed(String name) {
    return PerformanceMonitor().timeFunction(name, () => this);
  }
}

/// Widget performance monitoring mixin
mixin PerformanceMonitorMixin {
  final PerformanceMonitor _monitor = PerformanceMonitor();

  /// Time a widget build
  void timeBuild(String widgetName, VoidCallback buildFunction) {
    _monitor.timeFunctionSync('${widgetName}_build', buildFunction);
  }

  /// Record widget lifecycle event
  void recordLifecycleEvent(String widgetName, String event) {
    _monitor.logEvent('${widgetName}_$event', type: PerformanceEventType.system);
  }
}
