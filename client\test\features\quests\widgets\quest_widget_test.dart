import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quester_client/presentation/widgets/quest/quest_card_widget.dart';

void main() {
  group('QuestCard Widget Tests', () {
    testWidgets('displays quest card with direct parameters', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(
              title: 'Test Quest',
              description: 'This is a test quest',
              difficulty: 'medium',
              status: 'pending',
              progress: 0.0,
              points: 100,
              tags: ['Learning'],
            ),
          ),
        ),
      );

      // Verify quest title is displayed
      expect(find.text('Test Quest'), findsOneWidget);
      
      // Verify quest description is displayed
      expect(find.text('This is a test quest'), findsOneWidget);
      
      // Verify category is displayed
      expect(find.text('Learning'), findsOneWidget);
      
      // Verify difficulty is displayed
      expect(find.text('Medium'), findsOneWidget);
    });

    testWidgets('displays quest card with different parameters', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Quest<PERSON>ard(
              title: 'Mock Quest',
              description: 'Mock quest description',
              difficulty: 'hard',
              status: 'in_progress',
              progress: 0.6,
              points: 200,
              tags: const ['Fitness'],
            ),
          ),
        ),
      );

      // Verify quest information is displayed
      expect(find.text('Mock Quest'), findsOneWidget);
      expect(find.text('Mock quest description'), findsOneWidget);
      expect(find.byType(QuestCard), findsOneWidget);
    });

    testWidgets('displays quest progress correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(
              title: 'Progress Quest',
              description: 'Quest with progress',
              difficulty: 'medium',
              status: 'in_progress',
              progress: 0.75,
              points: 150,
            ),
          ),
        ),
      );

      // Verify quest title is displayed
      expect(find.text('Progress Quest'), findsOneWidget);

      // Verify quest card is displayed
      expect(find.byType(QuestCard), findsOneWidget);
    });

    testWidgets('displays rewards correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(
              title: 'Reward Quest',
              description: 'Quest with rewards',
              difficulty: 'easy',
              status: 'pending',
              progress: 0.0,
              points: 250,
            ),
          ),
        ),
      );

      // Verify quest card is displayed
      expect(find.text('Reward Quest'), findsOneWidget);
      expect(find.byType(QuestCard), findsOneWidget);
    });
  });
}
