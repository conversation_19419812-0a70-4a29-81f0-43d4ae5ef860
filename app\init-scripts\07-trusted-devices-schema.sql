-- Trusted Device Management Schema for MFA System
-- Handles device registration, verification, and risk assessment

-- Device status enum
CREATE TYPE device_status AS ENUM ('active', 'revoked', 'expired', 'suspicious');

-- Device trust level enum  
CREATE TYPE device_trust_level AS ENUM ('low', 'medium', 'high', 'verified');

-- Device type enum
CREATE TYPE device_type AS ENUM ('mobile', 'desktop', 'tablet', 'unknown');

-- Trusted devices table
CREATE TABLE IF NOT EXISTS trusted_devices (
    id VARCHAR(64) PRIMARY KEY,
    user_id UUID NOT NULL,
    device_fingerprint VARCHAR(256) NOT NULL,
    device_name VARCHAR(100) NOT NULL,
    device_type device_type NOT NULL DEFAULT 'unknown',
    device_model VARCHAR(100) NULL,
    operating_system VARCHAR(100) NULL,
    browser_info VARCHAR(200) NULL,
    ip_address INET NOT NULL,
    user_agent TEXT NULL,
    status device_status NOT NULL DEFAULT 'active',
    trust_level device_trust_level NOT NULL DEFAULT 'low',
    first_seen TIMES<PERSON>MP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    trusted_at TIMESTAMP WITH TIME ZONE NULL,
    expires_at TIMESTAMP WITH TIME ZONE NULL,
    access_count INTEGER NOT NULL DEFAULT 1,
    metadata JSONB NOT NULL DEFAULT '{}',
    revoked_by VARCHAR(255) NULL,
    revocation_reason TEXT NULL,
    revoked_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    CONSTRAINT fk_trusted_devices_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_trusted_devices_fingerprint 
        CHECK (LENGTH(device_fingerprint) >= 10 AND LENGTH(device_fingerprint) <= 256),
    
    CONSTRAINT chk_trusted_devices_name 
        CHECK (LENGTH(device_name) >= 1 AND LENGTH(device_name) <= 100),
    
    CONSTRAINT chk_trusted_devices_dates 
        CHECK (last_seen >= first_seen),
        
    CONSTRAINT chk_trusted_devices_trust_dates 
        CHECK (trusted_at IS NULL OR trusted_at >= first_seen),
        
    CONSTRAINT chk_trusted_devices_expiry 
        CHECK (expires_at IS NULL OR expires_at > trusted_at),
    
    CONSTRAINT chk_trusted_devices_access_count 
        CHECK (access_count >= 1),
    
    CONSTRAINT chk_trusted_devices_revocation 
        CHECK (
            (status != 'revoked') OR 
            (status = 'revoked' AND revoked_at IS NOT NULL AND revocation_reason IS NOT NULL)
        )
);

-- Device access log table for audit trail
CREATE TABLE IF NOT EXISTS device_access_log (
    id VARCHAR(64) PRIMARY KEY,
    device_id VARCHAR(64) NOT NULL,
    user_id UUID NOT NULL,
    access_type VARCHAR(50) NOT NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    accessed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    metadata JSONB NOT NULL DEFAULT '{}',
    
    CONSTRAINT fk_device_access_device 
        FOREIGN KEY (device_id) REFERENCES trusted_devices(id) ON DELETE CASCADE,
    
    CONSTRAINT fk_device_access_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes for trusted devices
CREATE UNIQUE INDEX IF NOT EXISTS idx_trusted_devices_user_fingerprint 
    ON trusted_devices(user_id, device_fingerprint);

CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_id 
    ON trusted_devices(user_id);

CREATE INDEX IF NOT EXISTS idx_trusted_devices_status 
    ON trusted_devices(status) 
    WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_active 
    ON trusted_devices(user_id, status) 
    WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_trusted_devices_expires_at 
    ON trusted_devices(expires_at) 
    WHERE expires_at IS NOT NULL AND status = 'active';

CREATE INDEX IF NOT EXISTS idx_trusted_devices_last_seen 
    ON trusted_devices(last_seen);

CREATE INDEX IF NOT EXISTS idx_trusted_devices_trust_level 
    ON trusted_devices(trust_level, status);

-- Indexes for device access log
CREATE INDEX IF NOT EXISTS idx_device_access_device_id 
    ON device_access_log(device_id);

CREATE INDEX IF NOT EXISTS idx_device_access_user_id 
    ON device_access_log(user_id);

CREATE INDEX IF NOT EXISTS idx_device_access_accessed_at 
    ON device_access_log(accessed_at);

CREATE INDEX IF NOT EXISTS idx_device_access_type 
    ON device_access_log(access_type);

-- Trusted device statistics view
CREATE OR REPLACE VIEW trusted_device_stats AS
SELECT 
    user_id,
    COUNT(*) as total_devices,
    COUNT(*) FILTER (WHERE status = 'active') as active_devices,
    COUNT(*) FILTER (WHERE status = 'revoked') as revoked_devices,
    COUNT(*) FILTER (WHERE status = 'expired') as expired_devices,
    COUNT(*) FILTER (WHERE status = 'suspicious') as suspicious_devices,
    COUNT(*) FILTER (WHERE trust_level = 'high' OR trust_level = 'verified') as highly_trusted_devices,
    MAX(last_seen) as last_device_activity,
    MIN(first_seen) as first_device_registered,
    AVG(access_count) as avg_access_count
FROM trusted_devices 
GROUP BY user_id;

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_trusted_device_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update timestamp
DROP TRIGGER IF EXISTS trigger_update_trusted_device_timestamp ON trusted_devices;
CREATE TRIGGER trigger_update_trusted_device_timestamp
    BEFORE UPDATE ON trusted_devices
    FOR EACH ROW
    EXECUTE FUNCTION update_trusted_device_timestamp();

-- Function to cleanup expired devices
CREATE OR REPLACE FUNCTION cleanup_expired_trusted_devices()
RETURNS INTEGER AS $$
DECLARE
    affected_count INTEGER;
BEGIN
    UPDATE trusted_devices 
    SET status = 'expired'
    WHERE expires_at < NOW() 
      AND status = 'active';
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO device_access_log (id, device_id, user_id, access_type, accessed_at, metadata)
    SELECT 
        'cleanup_' || extract(epoch from now()) || '_' || id,
        id,
        user_id,
        'automatic_expiry',
        NOW(),
        '{"cleanup_reason": "expired", "expired_at": "' || expires_at::text || '"}'::jsonb
    FROM trusted_devices 
    WHERE status = 'expired' AND expires_at < NOW();
    
    RETURN affected_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user device summary
CREATE OR REPLACE FUNCTION get_user_device_summary(p_user_id UUID)
RETURNS TABLE (
    total_devices INTEGER,
    active_devices INTEGER,
    trusted_devices INTEGER,
    last_activity TIMESTAMP WITH TIME ZONE,
    needs_cleanup BOOLEAN,
    security_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(stats.total_devices, 0) as total_devices,
        COALESCE(stats.active_devices, 0) as active_devices,
        COALESCE(stats.highly_trusted_devices, 0) as trusted_devices,
        stats.last_device_activity,
        COALESCE(stats.suspicious_devices > 0 OR stats.expired_devices > 2, false) as needs_cleanup,
        CASE 
            WHEN stats.suspicious_devices > 0 THEN 0.3
            WHEN stats.highly_trusted_devices::NUMERIC / GREATEST(stats.active_devices, 1) > 0.7 THEN 0.9
            WHEN stats.active_devices <= 3 THEN 0.8
            ELSE 0.6
        END as security_score
    FROM trusted_device_stats stats
    WHERE stats.user_id = p_user_id;
    
    -- If no records found, return default values
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 0, 0, 0, NULL::TIMESTAMP WITH TIME ZONE, false, 1.0::NUMERIC;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to detect suspicious device patterns
CREATE OR REPLACE FUNCTION detect_suspicious_device_activity(p_user_id UUID)
RETURNS TABLE (
    device_id VARCHAR(64),
    device_name VARCHAR(100),
    risk_factors TEXT[],
    risk_score NUMERIC,
    recommended_action TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH device_risk_analysis AS (
        SELECT 
            d.id,
            d.device_name,
            d.ip_address,
            d.access_count,
            d.last_seen,
            d.first_seen,
            -- Calculate various risk factors
            CASE WHEN d.access_count > 100 AND d.first_seen > NOW() - INTERVAL '1 day' 
                 THEN ARRAY['high_frequency_access'] ELSE ARRAY[]::TEXT[] END ||
            CASE WHEN EXISTS (
                SELECT 1 FROM trusted_devices d2 
                WHERE d2.user_id = p_user_id 
                  AND d2.ip_address = d.ip_address 
                  AND d2.id != d.id 
                  AND d2.first_seen > NOW() - INTERVAL '1 hour'
            ) THEN ARRAY['multiple_devices_same_ip'] ELSE ARRAY[]::TEXT[] END ||
            CASE WHEN d.device_type = 'unknown' 
                 THEN ARRAY['unknown_device_type'] ELSE ARRAY[]::TEXT[] END ||
            CASE WHEN d.trust_level = 'low' AND d.access_count > 10 
                 THEN ARRAY['untrusted_high_usage'] ELSE ARRAY[]::TEXT[] END
            as risk_factors_array
        FROM trusted_devices d
        WHERE d.user_id = p_user_id 
          AND d.status IN ('active', 'suspicious')
    )
    SELECT 
        dra.id,
        dra.device_name,
        dra.risk_factors_array,
        CASE 
            WHEN array_length(dra.risk_factors_array, 1) IS NULL THEN 0.0
            WHEN array_length(dra.risk_factors_array, 1) = 1 THEN 0.3
            WHEN array_length(dra.risk_factors_array, 1) = 2 THEN 0.6
            ELSE 0.9
        END as risk_score,
        CASE 
            WHEN array_length(dra.risk_factors_array, 1) IS NULL THEN 'No action needed'
            WHEN array_length(dra.risk_factors_array, 1) = 1 THEN 'Monitor device activity'
            WHEN array_length(dra.risk_factors_array, 1) = 2 THEN 'Review device and consider MFA requirement'
            ELSE 'Revoke device access immediately'
        END as recommended_action
    FROM device_risk_analysis dra
    WHERE array_length(dra.risk_factors_array, 1) > 0
    ORDER BY array_length(dra.risk_factors_array, 1) DESC, dra.last_seen DESC;
END;
$$ LANGUAGE plpgsql;

-- Security policies for trusted devices (Row Level Security)
ALTER TABLE trusted_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_access_log ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own devices
CREATE POLICY trusted_devices_user_access ON trusted_devices
    FOR ALL 
    TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

-- Policy: Admins can access all devices
CREATE POLICY trusted_devices_admin_access ON trusted_devices
    FOR ALL 
    TO admin_users
    USING (true);

-- Policy: Users can only access their own device logs
CREATE POLICY device_access_log_user_access ON device_access_log
    FOR ALL 
    TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

-- Policy: Admins can access all device logs
CREATE POLICY device_access_log_admin_access ON device_access_log
    FOR ALL 
    TO admin_users
    USING (true);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON trusted_devices TO authenticated_users;
GRANT ALL ON trusted_devices TO admin_users;
GRANT SELECT, INSERT ON device_access_log TO authenticated_users;
GRANT ALL ON device_access_log TO admin_users;
GRANT SELECT ON trusted_device_stats TO authenticated_users;
GRANT ALL ON trusted_device_stats TO admin_users;

-- Insert initial test data for development
DO $$
DECLARE
    test_user_id UUID;
    test_device_id VARCHAR(64);
BEGIN
    -- Only insert test data if in development mode
    IF current_setting('app.environment', true) = 'development' THEN
        -- Get or create a test user
        SELECT id INTO test_user_id FROM users WHERE email = '<EMAIL>' LIMIT 1;
        
        IF test_user_id IS NOT NULL THEN
            -- Generate a test device ID
            test_device_id := 'device_' || extract(epoch from now()) || '_test001';
            
            INSERT INTO trusted_devices (
                id, 
                user_id, 
                device_fingerprint,
                device_name,
                device_type,
                operating_system,
                browser_info,
                ip_address,
                user_agent,
                status,
                trust_level,
                first_seen,
                last_seen,
                trusted_at,
                expires_at,
                metadata
            ) VALUES (
                test_device_id,
                test_user_id,
                encode(sha256('test_fingerprint_001'::bytea), 'hex'),
                'Test Device 1',
                'desktop',
                'Windows 11',
                'Chrome 120.0',
                '127.0.0.1',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'active',
                'high',
                NOW(),
                NOW(),
                NOW(),
                NOW() + INTERVAL '30 days',
                '{"test_device": true, "auto_generated": true}'::jsonb
            ) ON CONFLICT (user_id, device_fingerprint) DO NOTHING;
            
            -- Add a device access log entry
            INSERT INTO device_access_log (
                id,
                device_id,
                user_id,
                access_type,
                ip_address,
                user_agent,
                accessed_at,
                metadata
            ) VALUES (
                'access_' || extract(epoch from now()) || '_test001',
                test_device_id,
                test_user_id,
                'device_registered',
                '127.0.0.1',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                NOW(),
                '{"test_access": true, "registration": true}'::jsonb
            ) ON CONFLICT (id) DO NOTHING;
            
            RAISE NOTICE 'Inserted test trusted device for development';
        END IF;
    END IF;
END $$;

-- Function to automatically cleanup old access logs (keep last 1000 per device)
CREATE OR REPLACE FUNCTION cleanup_old_device_access_logs()
RETURNS INTEGER AS $$
DECLARE
    affected_count INTEGER := 0;
    device_record RECORD;
BEGIN
    -- For each device, keep only the latest 1000 access log entries
    FOR device_record IN 
        SELECT DISTINCT device_id FROM device_access_log
    LOOP
        DELETE FROM device_access_log
        WHERE device_id = device_record.device_id
          AND id NOT IN (
              SELECT id FROM device_access_log
              WHERE device_id = device_record.device_id
              ORDER BY accessed_at DESC
              LIMIT 1000
          );
        
        GET DIAGNOSTICS affected_count = affected_count + ROW_COUNT;
    END LOOP;
    
    RETURN affected_count;
END;
$$ LANGUAGE plpgsql;

-- Log the completion
DO $$
BEGIN
    RAISE NOTICE 'Trusted devices schema initialized successfully';
    RAISE NOTICE 'Tables: trusted_devices, device_access_log';
    RAISE NOTICE 'Views: trusted_device_stats';
    RAISE NOTICE 'Enums: device_status, device_trust_level, device_type';
    RAISE NOTICE 'Functions: update_trusted_device_timestamp, cleanup_expired_trusted_devices, get_user_device_summary, detect_suspicious_device_activity, cleanup_old_device_access_logs';
    RAISE NOTICE 'Security: Row Level Security enabled with user and admin policies';
END $$;