# Quester API Documentation

This document provides comprehensive documentation for the Quester platform REST API.

## Base URL

```
Development: http://localhost:8080
Production: https://api.quester.com
```

## Authentication

All API endpoints require authentication unless otherwise specified.

### Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Authentication Endpoints

#### POST /auth/login
Login with email and password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "displayName": "<PERSON> Doe"
  },
  "accessToken": "jwt_token_here",
  "refreshToken": "refresh_token_here"
}
```

#### POST /auth/register
Register a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "displayName": "John Doe"
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

## Quest Management

### GET /api/v1/gamification/quests
Retrieve user's quests with optional filtering.

**Query Parameters:**
- `status` (optional): Filter by quest status (draft, active, completed, archived)
- `category` (optional): Filter by category (personal, work, learning, health, social)
- `priority` (optional): Filter by priority (low, medium, high, urgent)
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "success": true,
  "quests": [
    {
      "id": "quest_123",
      "title": "Complete Flutter Tutorial",
      "description": "Learn Flutter by building a todo app",
      "status": "active",
      "priority": "high",
      "difficulty": "intermediate",
      "category": "learning",
      "basePoints": 100,
      "totalPoints": 150,
      "earnedPoints": 75,
      "progressPercentage": 50.0,
      "createdAt": "2023-12-01T10:00:00Z",
      "updatedAt": "2023-12-01T15:30:00Z"
    }
  ],
  "total": 1,
  "pagination": {
    "limit": 50,
    "offset": 0,
    "hasMore": false
  }
}
```

### POST /api/v1/gamification/quests
Create a new quest.

**Request:**
```json
{
  "title": "Complete Flutter Tutorial",
  "description": "Learn Flutter by building a todo app",
  "priority": "high",
  "difficulty": "intermediate",
  "category": "learning",
  "estimatedHours": 10,
  "deadline": "2023-12-31T23:59:59Z",
  "participantIds": [],
  "tags": ["flutter", "tutorial", "mobile"]
}
```

**Response:**
```json
{
  "success": true,
  "quest": {
    "id": "quest_123",
    "title": "Complete Flutter Tutorial",
    "status": "draft",
    "basePoints": 100,
    "createdAt": "2023-12-01T10:00:00Z"
  }
}
```

### PUT /api/v1/gamification/quests/{questId}
Update an existing quest.

### DELETE /api/v1/gamification/quests/{questId}
Delete a quest.

### GET /api/v1/gamification/quest-templates
Get available quest templates.

**Response:**
```json
{
  "success": true,
  "templates": [
    {
      "id": "template_fitness",
      "title": "Daily Fitness Challenge",
      "description": "Complete daily workout routines",
      "category": "health",
      "difficulty": "intermediate",
      "basePoints": 150,
      "taskTitles": ["Morning stretches", "30-minute workout", "Track progress"],
      "tags": ["fitness", "health", "daily"]
    }
  ]
}
```

## User Management

### GET /api/v1/users/profile
Get current user profile.

### PUT /api/v1/users/profile
Update user profile.

### GET /api/v1/users/achievements
Get user achievements.

### GET /api/v1/users/leaderboard
Get leaderboard data.

## Analytics

### GET /analytics/user-behavior
Get user behavior analytics.

**Query Parameters:**
- `startDate`: Start date for analytics (ISO 8601)
- `endDate`: End date for analytics (ISO 8601)
- `metrics`: Comma-separated list of metrics

### GET /analytics/quest-performance
Get quest performance metrics.

### GET /analytics/engagement
Get user engagement analytics.

## Enterprise Features

### Organizations

#### GET /api/v1/enterprise/organizations
Get user's organizations.

#### POST /api/v1/enterprise/organizations
Create a new organization.

#### GET /api/v1/enterprise/organizations/{orgId}/members
Get organization members.

### Compliance

#### GET /api/v1/enterprise/compliance/{orgId}/audit-events
Get audit events for compliance.

#### GET /api/v1/enterprise/compliance/{orgId}/reports
Get compliance reports.

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  }
}
```

### Common Error Codes
- `UNAUTHORIZED`: Invalid or missing authentication
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `VALIDATION_ERROR`: Request validation failed
- `RATE_LIMITED`: Too many requests
- `INTERNAL_ERROR`: Server error

## Rate Limiting

API requests are rate limited:
- **Standard users**: 300 requests per minute
- **Premium users**: 1000 requests per minute
- **Enterprise users**: 5000 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 300
X-RateLimit-Remaining: 299
X-RateLimit-Reset: 1640995200
```

## Webhooks

Configure webhooks to receive real-time notifications:

### Events
- `quest.created`
- `quest.completed`
- `achievement.earned`
- `user.level_up`

### Webhook Payload
```json
{
  "event": "quest.completed",
  "timestamp": "2023-12-01T10:00:00Z",
  "data": {
    "questId": "quest_123",
    "userId": "user_456",
    "pointsEarned": 150
  }
}
```

## SDKs and Libraries

- **Dart/Flutter**: Use the shared package for type-safe API calls
- **JavaScript**: Official SDK available on npm
- **Python**: Community SDK available on PyPI

## Support

- **Documentation**: https://docs.quester.com
- **API Status**: https://status.quester.com
- **Support**: <EMAIL>
