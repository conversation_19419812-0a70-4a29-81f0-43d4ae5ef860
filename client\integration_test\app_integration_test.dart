import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:quester_client/main.dart' as app;

/// Comprehensive integration tests for the Quester app
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Quester App Integration Tests', () {
    testWidgets('Complete user journey: Registration to Quest Completion', (tester) async {
      // Start the app
      await tester.pumpWidget(const app.QuesterApp());
      await tester.pumpAndSettle();

      // Test 1: User Registration Flow
      await _testUserRegistration(tester);
      
      // Test 2: Profile Setup
      await _testProfileSetup(tester);
      
      // Test 3: Quest Creation and Management
      await _testQuestManagement(tester);
      
      // Test 4: Task Management
      await _testTaskManagement(tester);
      
      // Test 5: Gamification Features
      await _testGamificationFeatures(tester);
      
      // Test 6: Messaging System
      await _testMessagingSystem(tester);
      
      // Test 7: Notification System
      await _testNotificationSystem(tester);
      
      // Test 8: Dashboard Analytics
      await _testDashboardAnalytics(tester);
    });

    testWidgets('Authentication flow with error handling', (tester) async {
      await tester.pumpWidget(const app.QuesterApp());
      await tester.pumpAndSettle();

      // Test invalid login
      await _testInvalidLogin(tester);
      
      // Test password reset flow
      await _testPasswordReset(tester);
      
      // Test social login (if available)
      await _testSocialLogin(tester);
    });

    testWidgets('Responsive design across different screen sizes', (tester) async {
      // Test mobile layout
      await _testMobileLayout(tester);
      
      // Test tablet layout
      await _testTabletLayout(tester);
      
      // Test desktop layout
      await _testDesktopLayout(tester);
    });

    testWidgets('Performance and memory usage', (tester) async {
      await tester.pumpWidget(const app.QuesterApp());
      await tester.pumpAndSettle();

      // Test app startup performance
      await _testStartupPerformance(tester);
      
      // Test memory usage during heavy operations
      await _testMemoryUsage(tester);
      
      // Test scroll performance
      await _testScrollPerformance(tester);
    });

    testWidgets('Offline functionality and data persistence', (tester) async {
      await tester.pumpWidget(const app.QuesterApp());
      await tester.pumpAndSettle();

      // Test offline quest creation
      await _testOfflineQuestCreation(tester);
      
      // Test data synchronization when back online
      await _testDataSynchronization(tester);
      
      // Test cached data access
      await _testCachedDataAccess(tester);
    });
  });
}

/// Test user registration flow
Future<void> _testUserRegistration(WidgetTester tester) async {
  // Navigate to registration screen
  await tester.tap(find.text('Sign Up'));
  await tester.pumpAndSettle();

  // Fill registration form
  await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
  await tester.enterText(find.byKey(const Key('password_field')), 'TestPassword123!');
  await tester.enterText(find.byKey(const Key('confirm_password_field')), 'TestPassword123!');
  await tester.enterText(find.byKey(const Key('display_name_field')), 'Test User');

  // Submit registration
  await tester.tap(find.byKey(const Key('register_button')));
  await tester.pumpAndSettle();

  // Verify successful registration
  expect(find.text('Welcome to Quester!'), findsOneWidget);
}

/// Test profile setup flow
Future<void> _testProfileSetup(WidgetTester tester) async {
  // Navigate to profile screen
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Update profile information
  await tester.tap(find.byKey(const Key('edit_profile_button')));
  await tester.pumpAndSettle();

  await tester.enterText(find.byKey(const Key('bio_field')), 'I love completing quests!');
  
  // Save profile changes
  await tester.tap(find.byKey(const Key('save_profile_button')));
  await tester.pumpAndSettle();

  // Verify profile updated
  expect(find.text('Profile updated successfully'), findsOneWidget);
}

/// Test quest management functionality
Future<void> _testQuestManagement(WidgetTester tester) async {
  // Navigate to quests screen
  await tester.tap(find.byIcon(Icons.flag));
  await tester.pumpAndSettle();

  // Create new quest
  await tester.tap(find.byKey(const Key('create_quest_fab')));
  await tester.pumpAndSettle();

  await tester.enterText(find.byKey(const Key('quest_title_field')), 'My First Quest');
  await tester.enterText(find.byKey(const Key('quest_description_field')), 'Complete daily tasks');
  
  // Set quest priority
  await tester.tap(find.byKey(const Key('priority_dropdown')));
  await tester.pumpAndSettle();
  await tester.tap(find.text('High'));
  await tester.pumpAndSettle();

  // Save quest
  await tester.tap(find.byKey(const Key('save_quest_button')));
  await tester.pumpAndSettle();

  // Verify quest created
  expect(find.text('My First Quest'), findsOneWidget);
  expect(find.text('Quest created successfully'), findsOneWidget);
}

/// Test task management functionality
Future<void> _testTaskManagement(WidgetTester tester) async {
  // Tap on the created quest to open details
  await tester.tap(find.text('My First Quest'));
  await tester.pumpAndSettle();

  // Add task to quest
  await tester.tap(find.byKey(const Key('add_task_button')));
  await tester.pumpAndSettle();

  await tester.enterText(find.byKey(const Key('task_title_field')), 'Complete morning routine');
  await tester.enterText(find.byKey(const Key('task_description_field')), 'Exercise and meditation');

  // Save task
  await tester.tap(find.byKey(const Key('save_task_button')));
  await tester.pumpAndSettle();

  // Mark task as complete
  await tester.tap(find.byKey(const Key('task_checkbox_0')));
  await tester.pumpAndSettle();

  // Verify task completion
  expect(find.text('Task completed!'), findsOneWidget);
}

/// Test gamification features
Future<void> _testGamificationFeatures(WidgetTester tester) async {
  // Navigate to achievements screen
  await tester.tap(find.byIcon(Icons.emoji_events));
  await tester.pumpAndSettle();

  // Verify achievement unlocked
  expect(find.text('First Task Master'), findsOneWidget);

  // Navigate to leaderboard
  await tester.tap(find.byKey(const Key('leaderboard_tab')));
  await tester.pumpAndSettle();

  // Verify user appears on leaderboard
  expect(find.text('Test User'), findsOneWidget);

  // Check points and level
  await tester.tap(find.byKey(const Key('profile_tab')));
  await tester.pumpAndSettle();

  expect(find.textContaining('Points:'), findsOneWidget);
  expect(find.textContaining('Level:'), findsOneWidget);
}

/// Test messaging system
Future<void> _testMessagingSystem(WidgetTester tester) async {
  // Navigate to messages screen
  await tester.tap(find.byIcon(Icons.message));
  await tester.pumpAndSettle();

  // Start new conversation (if other users exist)
  if (find.byKey(const Key('new_message_fab')).evaluate().isNotEmpty) {
    await tester.tap(find.byKey(const Key('new_message_fab')));
    await tester.pumpAndSettle();

    // Send a message
    await tester.enterText(find.byKey(const Key('message_input')), 'Hello from integration test!');
    await tester.tap(find.byKey(const Key('send_message_button')));
    await tester.pumpAndSettle();

    // Verify message sent
    expect(find.text('Hello from integration test!'), findsOneWidget);
  }
}

/// Test notification system
Future<void> _testNotificationSystem(WidgetTester tester) async {
  // Navigate to notifications screen
  await tester.tap(find.byIcon(Icons.notifications));
  await tester.pumpAndSettle();

  // Check for quest completion notification
  expect(find.textContaining('Quest completed'), findsWidgets);

  // Test notification preferences
  await tester.tap(find.byKey(const Key('notification_settings_button')));
  await tester.pumpAndSettle();

  // Toggle notification setting
  await tester.tap(find.byKey(const Key('push_notifications_toggle')));
  await tester.pumpAndSettle();

  // Save preferences
  await tester.tap(find.byKey(const Key('save_preferences_button')));
  await tester.pumpAndSettle();

  expect(find.text('Preferences updated'), findsOneWidget);
}

/// Test dashboard analytics
Future<void> _testDashboardAnalytics(WidgetTester tester) async {
  // Navigate to dashboard
  await tester.tap(find.byIcon(Icons.dashboard));
  await tester.pumpAndSettle();

  // Verify analytics widgets are present
  expect(find.textContaining('Completed Today'), findsOneWidget);
  expect(find.textContaining('Total Points'), findsOneWidget);
  expect(find.textContaining('Active Quests'), findsOneWidget);
  expect(find.textContaining('Achievements'), findsOneWidget);

  // Test widget customization
  await tester.longPress(find.byKey(const Key('analytics_widget_0')));
  await tester.pumpAndSettle();

  if (find.text('Customize Widget').evaluate().isNotEmpty) {
    await tester.tap(find.text('Customize Widget'));
    await tester.pumpAndSettle();

    // Make some customization
    await tester.tap(find.byKey(const Key('widget_color_option_1')));
    await tester.tap(find.byKey(const Key('save_customization_button')));
    await tester.pumpAndSettle();
  }
}

/// Test invalid login scenarios
Future<void> _testInvalidLogin(WidgetTester tester) async {
  // Try login with invalid credentials
  await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
  await tester.enterText(find.byKey(const Key('password_field')), 'wrongpassword');
  await tester.tap(find.byKey(const Key('login_button')));
  await tester.pumpAndSettle();

  // Verify error message
  expect(find.text('Invalid credentials'), findsOneWidget);
}

/// Test password reset flow
Future<void> _testPasswordReset(WidgetTester tester) async {
  await tester.tap(find.text('Forgot Password?'));
  await tester.pumpAndSettle();

  await tester.enterText(find.byKey(const Key('reset_email_field')), '<EMAIL>');
  await tester.tap(find.byKey(const Key('send_reset_button')));
  await tester.pumpAndSettle();

  expect(find.text('Password reset email sent'), findsOneWidget);
}

/// Test social login
Future<void> _testSocialLogin(WidgetTester tester) async {
  if (find.byKey(const Key('google_login_button')).evaluate().isNotEmpty) {
    await tester.tap(find.byKey(const Key('google_login_button')));
    await tester.pumpAndSettle();
    // Note: Actual social login would require additional setup
  }
}

/// Test mobile layout responsiveness
Future<void> _testMobileLayout(WidgetTester tester) async {
  await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone SE size
  await tester.pumpWidget(const app.QuesterApp());
  await tester.pumpAndSettle();

  // Verify mobile-specific UI elements
  expect(find.byType(BottomNavigationBar), findsOneWidget);
  expect(find.byType(Drawer), findsNothing); // Should use bottom nav on mobile
}

/// Test tablet layout responsiveness
Future<void> _testTabletLayout(WidgetTester tester) async {
  await tester.binding.setSurfaceSize(const Size(768, 1024)); // iPad size
  await tester.pumpWidget(const app.QuesterApp());
  await tester.pumpAndSettle();

  // Verify tablet-specific UI elements
  expect(find.byType(NavigationRail), findsOneWidget);
}

/// Test desktop layout responsiveness
Future<void> _testDesktopLayout(WidgetTester tester) async {
  await tester.binding.setSurfaceSize(const Size(1920, 1080)); // Desktop size
  await tester.pumpWidget(const app.QuesterApp());
  await tester.pumpAndSettle();

  // Verify desktop-specific UI elements
  expect(find.byType(NavigationRail), findsOneWidget);
  // Should have expanded content areas
}

/// Test app startup performance
Future<void> _testStartupPerformance(WidgetTester tester) async {
  final stopwatch = Stopwatch()..start();
  
  await tester.pumpWidget(const app.QuesterApp());
  await tester.pumpAndSettle();

  stopwatch.stop();
  
  // Startup should be under 3 seconds
  expect(stopwatch.elapsedMilliseconds, lessThan(3000));
}

/// Test memory usage during operations
Future<void> _testMemoryUsage(WidgetTester tester) async {
  // This would require additional memory profiling tools
  // For now, we'll test that the app doesn't crash during heavy operations
  
  // Create multiple quests rapidly
  for (int i = 0; i < 10; i++) {
    await tester.tap(find.byKey(const Key('create_quest_fab')));
    await tester.pumpAndSettle();
    
    await tester.enterText(find.byKey(const Key('quest_title_field')), 'Quest $i');
    await tester.tap(find.byKey(const Key('save_quest_button')));
    await tester.pumpAndSettle();
  }
  
  // App should still be responsive
  expect(find.byType(MaterialApp), findsOneWidget);
}

/// Test scroll performance
Future<void> _testScrollPerformance(WidgetTester tester) async {
  // Navigate to a scrollable list
  await tester.tap(find.byIcon(Icons.flag));
  await tester.pumpAndSettle();

  // Perform rapid scrolling
  final listFinder = find.byType(ListView);
  if (listFinder.evaluate().isNotEmpty) {
    await tester.fling(listFinder, const Offset(0, -500), 1000);
    await tester.pumpAndSettle();
    
    await tester.fling(listFinder, const Offset(0, 500), 1000);
    await tester.pumpAndSettle();
  }
  
  // App should remain responsive
  expect(find.byType(MaterialApp), findsOneWidget);
}

/// Test offline quest creation
Future<void> _testOfflineQuestCreation(WidgetTester tester) async {
  // Simulate offline mode (would require network mocking)
  // For now, test that UI still works
  
  await tester.tap(find.byKey(const Key('create_quest_fab')));
  await tester.pumpAndSettle();
  
  await tester.enterText(find.byKey(const Key('quest_title_field')), 'Offline Quest');
  await tester.tap(find.byKey(const Key('save_quest_button')));
  await tester.pumpAndSettle();
  
  // Should show offline indicator or queue for sync
  expect(find.textContaining('Offline'), findsWidgets);
}

/// Test data synchronization
Future<void> _testDataSynchronization(WidgetTester tester) async {
  // Simulate coming back online
  // This would require network state management
  
  // Verify sync indicator appears
  if (find.byKey(const Key('sync_indicator')).evaluate().isNotEmpty) {
    expect(find.byKey(const Key('sync_indicator')), findsOneWidget);
  }
}

/// Test cached data access
Future<void> _testCachedDataAccess(WidgetTester tester) async {
  // Navigate away and back to verify data persistence
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();
  
  await tester.tap(find.byIcon(Icons.flag));
  await tester.pumpAndSettle();
  
  // Previously created quests should still be visible
  expect(find.text('My First Quest'), findsOneWidget);
}
