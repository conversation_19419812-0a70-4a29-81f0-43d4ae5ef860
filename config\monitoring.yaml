# Comprehensive Monitoring Configuration for <PERSON><PERSON>
# This file defines monitoring rules, thresholds, and integrations

# Global monitoring settings
global:
  enabled: true
  collection_interval: 30s
  retention_period: 7d
  max_metrics_per_series: 1000

# Metric collection configuration
metrics:
  # System metrics
  system:
    enabled: true
    collect_interval: 30s
    metrics:
      - cpu_usage_percent
      - memory_usage_percent
      - memory_usage_mb
      - disk_usage_percent
      - network_bytes_in
      - network_bytes_out
      - open_file_descriptors
      - thread_count

  # Application metrics
  application:
    enabled: true
    collect_interval: 30s
    metrics:
      - active_users
      - requests_per_minute
      - avg_response_time_ms
      - error_rate_percent
      - cache_hit_ratio
      - database_connections
      - database_health_score
      - avg_query_time_ms

  # Business metrics
  business:
    enabled: true
    collect_interval: 60s
    metrics:
      - quests_created_per_hour
      - tasks_completed_per_hour
      - achievements_unlocked_per_hour
      - user_registrations_per_hour
      - active_sessions

  # Performance metrics
  performance:
    enabled: true
    collect_interval: 15s
    metrics:
      - slow_queries_count
      - query_cache_hit_ratio
      - cache_size
      - overall_health_score

# Alert rules configuration
alerts:
  # Critical alerts
  critical:
    - name: "High Error Rate"
      description: "Error rate exceeds 5%"
      metric: error_rate_percent
      condition: "> 5.0"
      duration: 2m
      cooldown: 2m
      severity: critical
      enabled: true

    - name: "Database Health Critical"
      description: "Database health score below 80%"
      metric: database_health_score
      condition: "< 0.8"
      duration: 1m
      cooldown: 1m
      severity: critical
      enabled: true

    - name: "System Overload"
      description: "CPU usage above 90% for extended period"
      metric: cpu_usage_percent
      condition: "> 90.0"
      duration: 5m
      cooldown: 10m
      severity: critical
      enabled: true

  # Warning alerts
  warning:
    - name: "High Response Time"
      description: "Average response time exceeds 500ms"
      metric: avg_response_time_ms
      condition: "> 500"
      duration: 5m
      cooldown: 5m
      severity: warning
      enabled: true

    - name: "High Memory Usage"
      description: "Memory usage exceeds 85%"
      metric: memory_usage_percent
      condition: "> 85.0"
      duration: 10m
      cooldown: 10m
      severity: warning
      enabled: true

    - name: "Low Cache Hit Ratio"
      description: "Cache hit ratio below 70%"
      metric: cache_hit_ratio
      condition: "< 0.7"
      duration: 15m
      cooldown: 15m
      severity: warning
      enabled: true

    - name: "High Disk Usage"
      description: "Disk usage exceeds 80%"
      metric: disk_usage_percent
      condition: "> 80.0"
      duration: 30m
      cooldown: 60m
      severity: warning
      enabled: true

  # Info alerts
  info:
    - name: "Slow Query Detection"
      description: "Slow queries detected"
      metric: slow_queries_count
      condition: "> 10"
      duration: 10m
      cooldown: 30m
      severity: info
      enabled: true

    - name: "User Activity Spike"
      description: "Unusual increase in user activity"
      metric: active_users
      condition: "> 1000"
      duration: 5m
      cooldown: 30m
      severity: info
      enabled: false

# Health check configuration
health_checks:
  enabled: true
  interval: 60s
  timeout: 10s
  
  checks:
    - name: "Database Connection"
      type: database
      enabled: true
      timeout: 5s
      
    - name: "Redis Connection"
      type: redis
      enabled: true
      timeout: 3s
      
    - name: "External API Health"
      type: http
      url: "https://api.sendgrid.com/v3/user/profile"
      method: GET
      headers:
        Authorization: "Bearer ${SENDGRID_API_KEY}"
      expected_status: 200
      enabled: true
      timeout: 10s
      
    - name: "Twilio API Health"
      type: http
      url: "https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}.json"
      method: GET
      auth:
        type: basic
        username: "${TWILIO_ACCOUNT_SID}"
        password: "${TWILIO_AUTH_TOKEN}"
      expected_status: 200
      enabled: true
      timeout: 10s

# Notification channels
notifications:
  # Slack integration
  slack:
    enabled: false
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#alerts"
    username: "Quester Monitor"
    icon_emoji: ":warning:"
    
    # Alert severity mapping
    severity_mapping:
      critical: 
        color: "danger"
        mention: "@channel"
      warning:
        color: "warning"
        mention: "@here"
      info:
        color: "good"
        mention: ""

  # Discord integration
  discord:
    enabled: false
    webhook_url: "${DISCORD_WEBHOOK_URL}"
    username: "Quester Monitor"
    avatar_url: "https://quester.app/assets/logo.png"
    
    # Alert severity color mapping
    severity_colors:
      critical: 0xFF0000  # Red
      warning: 0xFFA500   # Orange
      info: 0x0099FF      # Blue

  # Email notifications
  email:
    enabled: false
    smtp_host: "${SMTP_HOST}"
    smtp_port: 587
    smtp_user: "${SMTP_USER}"
    smtp_password: "${SMTP_PASSWORD}"
    from_email: "<EMAIL>"
    from_name: "Quester Monitoring"
    
    recipients:
      critical:
        - "<EMAIL>"
        - "<EMAIL>"
      warning:
        - "<EMAIL>"
      info:
        - "<EMAIL>"

  # PagerDuty integration
  pagerduty:
    enabled: false
    integration_key: "${PAGERDUTY_INTEGRATION_KEY}"
    severity_mapping:
      critical: "critical"
      warning: "warning"
      info: "info"

# Log analysis configuration
log_analysis:
  enabled: true
  analysis_interval: 10m
  log_sources:
    - path: "/var/log/quester/app.log"
      format: "json"
      enabled: true
    - path: "/var/log/quester/error.log"
      format: "text"
      enabled: true
  
  patterns:
    - name: "High Error Rate"
      pattern: "ERROR"
      threshold: 10
      window: "10m"
      severity: warning
      
    - name: "Database Connection Errors"
      pattern: "database.*connection.*failed"
      threshold: 5
      window: "5m"
      severity: critical
      
    - name: "Authentication Failures"
      pattern: "authentication.*failed"
      threshold: 20
      window: "10m"
      severity: warning

# Dashboard configuration
dashboard:
  enabled: true
  refresh_interval: 30s
  
  # Dashboard sections
  sections:
    - name: "System Overview"
      metrics:
        - cpu_usage_percent
        - memory_usage_percent
        - disk_usage_percent
        - overall_health_score
      chart_type: "gauge"
      
    - name: "Application Performance"
      metrics:
        - avg_response_time_ms
        - requests_per_minute
        - error_rate_percent
        - cache_hit_ratio
      chart_type: "line"
      time_range: "1h"
      
    - name: "Database Performance"
      metrics:
        - database_connections
        - avg_query_time_ms
        - slow_queries_count
        - database_health_score
      chart_type: "line"
      time_range: "1h"
      
    - name: "Business Metrics"
      metrics:
        - active_users
        - quests_created_per_hour
        - tasks_completed_per_hour
        - achievements_unlocked_per_hour
      chart_type: "bar"
      time_range: "24h"

# External integrations
integrations:
  # Prometheus metrics export
  prometheus:
    enabled: false
    port: 9090
    path: "/metrics"
    
  # Grafana dashboard
  grafana:
    enabled: false
    url: "${GRAFANA_URL}"
    api_key: "${GRAFANA_API_KEY}"
    dashboard_id: "quester-monitoring"
    
  # Datadog integration
  datadog:
    enabled: false
    api_key: "${DATADOG_API_KEY}"
    app_key: "${DATADOG_APP_KEY}"
    tags:
      - "env:production"
      - "service:quester"
      - "version:1.0.0"
    
  # New Relic integration
  newrelic:
    enabled: false
    license_key: "${NEWRELIC_LICENSE_KEY}"
    app_name: "Quester"

# Security monitoring
security:
  enabled: true
  
  # Suspicious activity detection
  suspicious_activity:
    - name: "Multiple Failed Logins"
      pattern: "login.*failed"
      threshold: 5
      window: "5m"
      action: "alert"
      
    - name: "Unusual API Usage"
      metric: requests_per_minute
      condition: "> 1000"
      duration: "2m"
      action: "alert"
      
    - name: "High Error Rate from Single IP"
      pattern: "error.*from.*ip"
      threshold: 20
      window: "10m"
      action: "block"

# Performance optimization
optimization:
  enabled: true
  
  # Auto-scaling triggers
  auto_scaling:
    - name: "High CPU Usage"
      metric: cpu_usage_percent
      scale_up_threshold: 80.0
      scale_down_threshold: 30.0
      cooldown: "10m"
      
    - name: "High Memory Usage"
      metric: memory_usage_percent
      scale_up_threshold: 85.0
      scale_down_threshold: 40.0
      cooldown: "15m"
  
  # Cache optimization
  cache_optimization:
    enabled: true
    target_hit_ratio: 0.85
    auto_adjust_ttl: true
    
  # Query optimization
  query_optimization:
    enabled: true
    slow_query_threshold: 1000ms
    auto_index_suggestions: true
