-- Comprehensive Gamification Schema for Quester Platform
-- This script creates all tables, enums, and initial data for the gamification system
-- Based on the successful mock implementation from server v2.1.0

-- Create extensions for advanced features
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create enums for gamification system
CREATE TYPE user_role AS ENUM (
    'novice',
    'explorer', 
    'adventurer',
    'hero',
    'legend',
    'mythic'
);

CREATE TYPE achievement_category AS ENUM (
    'quest_completion',
    'collaboration',
    'streak_building',
    'quality_work',
    'leadership',
    'innovation',
    'consistency',
    'special_events'
);

CREATE TYPE achievement_rarity AS ENUM (
    'common',
    'uncommon',
    'rare',
    'epic',
    'legendary'
);

CREATE TYPE reward_type AS ENUM (
    'cosmetic',
    'privilege',
    'title',
    'badge',
    'avatar_frame',
    'theme'
);

CREATE TYPE activity_type AS ENUM (
    'task_completion',
    'quest_creation',
    'quest_completion',
    'collaboration',
    'mentoring',
    'quality_work',
    'innovation',
    'consistency',
    'special_event',
    'achievement_unlock',
    'reward_purchase'
);

CREATE TYPE leaderboard_type AS ENUM (
    'global_points',
    'monthly_points',
    'quest_completion',
    'collaboration',
    'streak_days',
    'achievement_count',
    'quality_score',
    'innovation_score',
    'leadership_score',
    'weekly_points',
    'daily_points'
);

-- Core users table (extends basic user concept)
CREATE TABLE IF NOT EXISTS quester.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- User points and role progression
CREATE TABLE IF NOT EXISTS quester.user_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE UNIQUE,
    total_points INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    role user_role DEFAULT 'novice',
    points_to_next_level INTEGER DEFAULT 500,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT positive_points CHECK (total_points >= 0),
    CONSTRAINT positive_level CHECK (current_level >= 1)
);

-- Achievements system
CREATE TABLE IF NOT EXISTS quester.achievements (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category achievement_category NOT NULL,
    rarity achievement_rarity NOT NULL,
    points_reward INTEGER NOT NULL,
    icon_url TEXT,
    requirements JSONB DEFAULT '{}',
    is_hidden BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT positive_points_reward CHECK (points_reward > 0)
);

-- User achievements (earned achievements)
CREATE TABLE IF NOT EXISTS quester.user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    achievement_id VARCHAR(100) REFERENCES quester.achievements(id) ON DELETE CASCADE,
    earned_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    points_awarded INTEGER NOT NULL,
    
    UNIQUE(user_id, achievement_id),
    CONSTRAINT positive_points_awarded CHECK (points_awarded > 0)
);

-- Rewards system
CREATE TABLE IF NOT EXISTS quester.rewards (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    type reward_type NOT NULL,
    point_cost INTEGER NOT NULL,
    requirements JSONB DEFAULT '{}',
    icon_url TEXT,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT positive_point_cost CHECK (point_cost > 0)
);

-- User rewards (purchased rewards)
CREATE TABLE IF NOT EXISTS quester.user_rewards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    reward_id VARCHAR(100) REFERENCES quester.rewards(id) ON DELETE CASCADE,
    purchased_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    points_spent INTEGER NOT NULL,
    
    UNIQUE(user_id, reward_id),
    CONSTRAINT positive_points_spent CHECK (points_spent > 0)
);

-- Streaks tracking
CREATE TABLE IF NOT EXISTS quester.streaks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE UNIQUE,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_activity_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT non_negative_current_streak CHECK (current_streak >= 0),
    CONSTRAINT non_negative_longest_streak CHECK (longest_streak >= 0),
    CONSTRAINT longest_streak_valid CHECK (longest_streak >= current_streak)
);

-- Leaderboards
CREATE TABLE IF NOT EXISTS quester.leaderboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    leaderboard_type leaderboard_type NOT NULL,
    score INTEGER NOT NULL DEFAULT 0,
    rank INTEGER,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, leaderboard_type),
    CONSTRAINT positive_score CHECK (score >= 0),
    CONSTRAINT positive_rank CHECK (rank > 0)
);

-- Activity log for tracking all gamification events
CREATE TABLE IF NOT EXISTS quester.activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    activity_type activity_type NOT NULL,
    points_earned INTEGER DEFAULT 0,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT non_negative_points CHECK (points_earned >= 0)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_points_user_id ON quester.user_points(user_id);
CREATE INDEX IF NOT EXISTS idx_user_points_total_points ON quester.user_points(total_points DESC);
CREATE INDEX IF NOT EXISTS idx_user_points_role ON quester.user_points(role);

CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON quester.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_earned_date ON quester.user_achievements(earned_date DESC);

CREATE INDEX IF NOT EXISTS idx_achievements_category ON quester.achievements(category);
CREATE INDEX IF NOT EXISTS idx_achievements_rarity ON quester.achievements(rarity);

CREATE INDEX IF NOT EXISTS idx_user_rewards_user_id ON quester.user_rewards(user_id);
CREATE INDEX IF NOT EXISTS idx_user_rewards_purchased_date ON quester.user_rewards(purchased_date DESC);

CREATE INDEX IF NOT EXISTS idx_streaks_user_id ON quester.streaks(user_id);
CREATE INDEX IF NOT EXISTS idx_streaks_current_streak ON quester.streaks(current_streak DESC);

CREATE INDEX IF NOT EXISTS idx_leaderboards_type_rank ON quester.leaderboards(leaderboard_type, rank);
CREATE INDEX IF NOT EXISTS idx_leaderboards_user_id ON quester.leaderboards(user_id);
CREATE INDEX IF NOT EXISTS idx_leaderboards_score ON quester.leaderboards(score DESC);

CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON quester.activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_created_at ON quester.activity_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_log_activity_type ON quester.activity_log(activity_type);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION quester.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON quester.users 
    FOR EACH ROW EXECUTE FUNCTION quester.update_updated_at_column();

CREATE TRIGGER update_user_points_updated_at 
    BEFORE UPDATE ON quester.user_points 
    FOR EACH ROW EXECUTE FUNCTION quester.update_updated_at_column();

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA quester TO quester;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA quester TO quester;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA quester TO quester;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Quester gamification schema created successfully!';
    RAISE NOTICE 'Tables created: users, user_points, achievements, user_achievements, rewards, user_rewards, streaks, leaderboards, activity_log';
    RAISE NOTICE 'Enums created: user_role, achievement_category, achievement_rarity, reward_type, activity_type, leaderboard_type';
    RAISE NOTICE 'Indexes and triggers created for optimal performance';
END $$;
