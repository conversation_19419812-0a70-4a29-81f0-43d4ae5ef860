import 'package:equatable/equatable.dart';

/// <PERSON>gin method types
enum LoginMethod {
  password,
  sso,
  mfa,
  apiKey;

  String get displayName {
    switch (this) {
      case LoginMethod.password:
        return 'Password';
      case LoginMethod.sso:
        return 'Single Sign-On';
      case LoginMethod.mfa:
        return 'Multi-Factor Authentication';
      case LoginMethod.apiKey:
        return 'API Key';
    }
  }
}

/// Device information
class DeviceInfo extends Equatable {
  final String? deviceType;
  final String? os;
  final String? osVersion;
  final String? browser;
  final String? browserVersion;
  final String? screenResolution;
  final String? timezone;
  final String? language;

  const DeviceInfo({
    this.deviceType,
    this.os,
    this.osVersion,
    this.browser,
    this.browserVersion,
    this.screenResolution,
    this.timezone,
    this.language,
  });

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      deviceType: json['device_type'] as String?,
      os: json['os'] as String?,
      osVersion: json['os_version'] as String?,
      browser: json['browser'] as String?,
      browserVersion: json['browser_version'] as String?,
      screenResolution: json['screen_resolution'] as String?,
      timezone: json['timezone'] as String?,
      language: json['language'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_type': deviceType,
      'os': os,
      'os_version': osVersion,
      'browser': browser,
      'browser_version': browserVersion,
      'screen_resolution': screenResolution,
      'timezone': timezone,
      'language': language,
    };
  }

  String get displayInfo {
    final parts = <String>[];
    if (os != null) {
      if (osVersion != null) {
        parts.add('$os $osVersion');
      } else {
        parts.add(os!);
      }
    }
    if (browser != null) {
      if (browserVersion != null) {
        parts.add('$browser $browserVersion');
      } else {
        parts.add(browser!);
      }
    }
    return parts.join(' • ');
  }

  @override
  List<Object?> get props => [
        deviceType,
        os,
        osVersion,
        browser,
        browserVersion,
        screenResolution,
        timezone,
        language,
      ];
}

/// Geographic location information (reused from security_audit_log.dart)
class SessionGeoLocation extends Equatable {
  final String? country;
  final String? countryCode;
  final String? region;
  final String? city;
  final double? latitude;
  final double? longitude;
  final String? timezone;
  final String? isp;

  const SessionGeoLocation({
    this.country,
    this.countryCode,
    this.region,
    this.city,
    this.latitude,
    this.longitude,
    this.timezone,
    this.isp,
  });

  factory SessionGeoLocation.fromJson(Map<String, dynamic> json) {
    return SessionGeoLocation(
      country: json['country'] as String?,
      countryCode: json['country_code'] as String?,
      region: json['region'] as String?,
      city: json['city'] as String?,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      timezone: json['timezone'] as String?,
      isp: json['isp'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'country_code': countryCode,
      'region': region,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'isp': isp,
    };
  }

  String get displayLocation {
    final parts = <String>[];
    if (city != null) parts.add(city!);
    if (region != null) parts.add(region!);
    if (country != null) parts.add(country!);
    return parts.join(', ');
  }

  @override
  List<Object?> get props => [
        country,
        countryCode,
        region,
        city,
        latitude,
        longitude,
        timezone,
        isp,
      ];
}

/// User Session Enhanced model for database table: user_sessions_enhanced
class UserSessionEnhanced extends Equatable {
  /// Unique session identifier
  final String id;

  /// User ID
  final String userId;

  /// Organization ID
  final String? organizationId;

  /// Hashed session token
  final String sessionTokenHash;

  /// Hashed refresh token
  final String? refreshTokenHash;

  /// Device fingerprint
  final String? deviceFingerprint;

  /// Device information
  final DeviceInfo? deviceInfo;

  /// IP address
  final String ipAddress;

  /// User agent
  final String? userAgent;

  /// Geographic location
  final SessionGeoLocation? geoLocation;

  /// Whether device is trusted
  final bool isTrustedDevice;

  /// Login method used
  final LoginMethod? loginMethod;

  /// Whether MFA was verified
  final bool mfaVerified;

  /// Last activity timestamp
  final DateTime lastActivity;

  /// Session expiration time
  final DateTime expiresAt;

  /// Absolute session expiration time
  final DateTime absoluteExpiresAt;

  /// Whether session is active
  final bool isActive;

  /// Logout reason
  final String? logoutReason;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const UserSessionEnhanced({
    required this.id,
    required this.userId,
    this.organizationId,
    required this.sessionTokenHash,
    this.refreshTokenHash,
    this.deviceFingerprint,
    this.deviceInfo,
    required this.ipAddress,
    this.userAgent,
    this.geoLocation,
    required this.isTrustedDevice,
    this.loginMethod,
    required this.mfaVerified,
    required this.lastActivity,
    required this.expiresAt,
    required this.absoluteExpiresAt,
    required this.isActive,
    this.logoutReason,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create empty UserSessionEnhanced for testing
  factory UserSessionEnhanced.empty() {
    final now = DateTime.now();
    return UserSessionEnhanced(
      id: '',
      userId: '',
      sessionTokenHash: '',
      ipAddress: '',
      isTrustedDevice: false,
      mfaVerified: false,
      lastActivity: now,
      expiresAt: now.add(const Duration(hours: 8)),
      absoluteExpiresAt: now.add(const Duration(hours: 24)),
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Create UserSessionEnhanced from JSON
  factory UserSessionEnhanced.fromJson(Map<String, dynamic> json) {
    return UserSessionEnhanced(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      organizationId: json['organization_id'] as String?,
      sessionTokenHash: json['session_token_hash'] as String,
      refreshTokenHash: json['refresh_token_hash'] as String?,
      deviceFingerprint: json['device_fingerprint'] as String?,
      deviceInfo: json['device_info'] != null
          ? DeviceInfo.fromJson(json['device_info'] as Map<String, dynamic>)
          : null,
      ipAddress: json['ip_address'] as String,
      userAgent: json['user_agent'] as String?,
      geoLocation: json['geo_location'] != null
          ? SessionGeoLocation.fromJson(json['geo_location'] as Map<String, dynamic>)
          : null,
      isTrustedDevice: json['is_trusted_device'] as bool,
      loginMethod: json['login_method'] != null
          ? LoginMethod.values.firstWhere(
              (e) => e.name == json['login_method'],
              orElse: () => LoginMethod.password,
            )
          : null,
      mfaVerified: json['mfa_verified'] as bool,
      lastActivity: DateTime.parse(json['last_activity'] as String),
      expiresAt: DateTime.parse(json['expires_at'] as String),
      absoluteExpiresAt: DateTime.parse(json['absolute_expires_at'] as String),
      isActive: json['is_active'] as bool,
      logoutReason: json['logout_reason'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert UserSessionEnhanced to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'organization_id': organizationId,
      'session_token_hash': sessionTokenHash,
      'refresh_token_hash': refreshTokenHash,
      'device_fingerprint': deviceFingerprint,
      'device_info': deviceInfo?.toJson(),
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'geo_location': geoLocation?.toJson(),
      'is_trusted_device': isTrustedDevice,
      'login_method': loginMethod?.name,
      'mfa_verified': mfaVerified,
      'last_activity': lastActivity.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'absolute_expires_at': absoluteExpiresAt.toIso8601String(),
      'is_active': isActive,
      'logout_reason': logoutReason,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  UserSessionEnhanced copyWith({
    String? id,
    String? userId,
    String? organizationId,
    String? sessionTokenHash,
    String? refreshTokenHash,
    String? deviceFingerprint,
    DeviceInfo? deviceInfo,
    String? ipAddress,
    String? userAgent,
    SessionGeoLocation? geoLocation,
    bool? isTrustedDevice,
    LoginMethod? loginMethod,
    bool? mfaVerified,
    DateTime? lastActivity,
    DateTime? expiresAt,
    DateTime? absoluteExpiresAt,
    bool? isActive,
    String? logoutReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserSessionEnhanced(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      organizationId: organizationId ?? this.organizationId,
      sessionTokenHash: sessionTokenHash ?? this.sessionTokenHash,
      refreshTokenHash: refreshTokenHash ?? this.refreshTokenHash,
      deviceFingerprint: deviceFingerprint ?? this.deviceFingerprint,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      geoLocation: geoLocation ?? this.geoLocation,
      isTrustedDevice: isTrustedDevice ?? this.isTrustedDevice,
      loginMethod: loginMethod ?? this.loginMethod,
      mfaVerified: mfaVerified ?? this.mfaVerified,
      lastActivity: lastActivity ?? this.lastActivity,
      expiresAt: expiresAt ?? this.expiresAt,
      absoluteExpiresAt: absoluteExpiresAt ?? this.absoluteExpiresAt,
      isActive: isActive ?? this.isActive,
      logoutReason: logoutReason ?? this.logoutReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if session is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if session is absolutely expired
  bool get isAbsoluteExpired => DateTime.now().isAfter(absoluteExpiresAt);

  /// Check if session is idle
  bool get isIdle {
    final idleLimit = DateTime.now().subtract(const Duration(minutes: 30));
    return lastActivity.isBefore(idleLimit);
  }

  /// Get session duration
  Duration get sessionDuration => DateTime.now().difference(createdAt);

  /// Get time since last activity
  Duration get timeSinceLastActivity => DateTime.now().difference(lastActivity);

  /// Get display name for device
  String get deviceDisplayName {
    if (deviceInfo != null && deviceInfo!.displayInfo.isNotEmpty) {
      return deviceInfo!.displayInfo;
    }
    if (userAgent != null) {
      final ua = userAgent!.toLowerCase();
      if (ua.contains('mobile') || ua.contains('android') || ua.contains('iphone')) {
        return 'Mobile Device';
      } else if (ua.contains('tablet') || ua.contains('ipad')) {
        return 'Tablet';
      } else {
        return 'Desktop';
      }
    }
    return 'Unknown Device';
  }

  /// Get location display name
  String get locationDisplayName {
    if (geoLocation != null) {
      return geoLocation!.displayLocation;
    }
    return 'Unknown Location';
  }

  /// Get session status
  String get status {
    if (!isActive) return 'Inactive';
    if (isAbsoluteExpired) return 'Expired';
    if (isExpired) return 'Idle Timeout';
    if (isIdle) return 'Idle';
    return 'Active';
  }

  /// Check if session needs MFA verification
  bool get needsMFAVerification => isActive && !mfaVerified;

  /// Get remaining session time
  Duration get remainingTime {
    final now = DateTime.now();
    if (now.isAfter(absoluteExpiresAt)) return Duration.zero;
    return absoluteExpiresAt.difference(now);
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        organizationId,
        sessionTokenHash,
        refreshTokenHash,
        deviceFingerprint,
        deviceInfo,
        ipAddress,
        userAgent,
        geoLocation,
        isTrustedDevice,
        loginMethod,
        mfaVerified,
        lastActivity,
        expiresAt,
        absoluteExpiresAt,
        isActive,
        logoutReason,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}