import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../common/responsive_builder.dart';

/// Widget for displaying comprehensive performance metrics
class MetricsWidget extends StatefulWidget {
  /// List of metrics to display
  final List<Metric> metrics;
  
  /// Time period for metrics
  final MetricTimePeriod timePeriod;
  
  /// Callback when time period changes
  final Function(MetricTimePeriod)? onTimePeriodChanged;
  
  /// Whether to show comparison with previous period
  final bool showComparison;

  const MetricsWidget({
    super.key,
    required this.metrics,
    this.timePeriod = MetricTimePeriod.week,
    this.onTimePeriodChanged,
    this.showComparison = true,
  });

  @override
  State<MetricsWidget> createState() => _MetricsWidgetState();
}

class _MetricsWidgetState extends State<MetricsWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(context),
        const SizedBox(height: AppConstants.defaultPadding),
        Expanded(
          child: ResponsiveBuilder(
            mobile: _buildMobileLayout,
            tablet: _buildTabletLayout,
            desktop: _buildDesktopLayout,
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Text(
          'Performance Metrics',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        DropdownButton<MetricTimePeriod>(
          value: widget.timePeriod,
          onChanged: (period) {
            if (period != null) {
              widget.onTimePeriodChanged?.call(period);
            }
          },
          items: MetricTimePeriod.values.map((period) =>
            DropdownMenuItem(
              value: period,
              child: Text(_getTimePeriodLabel(period)),
            ),
          ).toList(),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return ListView.builder(
      itemCount: widget.metrics.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
          child: MetricCard(
            metric: widget.metrics[index],
            showComparison: widget.showComparison,
          ),
        );
      },
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
        childAspectRatio: 1.5,
      ),
      itemCount: widget.metrics.length,
      itemBuilder: (context, index) {
        return MetricCard(
          metric: widget.metrics[index],
          showComparison: widget.showComparison,
        );
      },
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
        childAspectRatio: 1.2,
      ),
      itemCount: widget.metrics.length,
      itemBuilder: (context, index) {
        return MetricCard(
          metric: widget.metrics[index],
          showComparison: widget.showComparison,
        );
      },
    );
  }

  String _getTimePeriodLabel(MetricTimePeriod period) {
    switch (period) {
      case MetricTimePeriod.day:
        return 'Today';
      case MetricTimePeriod.week:
        return 'This Week';
      case MetricTimePeriod.month:
        return 'This Month';
      case MetricTimePeriod.quarter:
        return 'This Quarter';
      case MetricTimePeriod.year:
        return 'This Year';
    }
  }
}

/// Individual metric card widget
class MetricCard extends StatelessWidget {
  final Metric metric;
  final bool showComparison;

  const MetricCard({
    super.key,
    required this.metric,
    this.showComparison = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildValue(context),
            if (showComparison && metric.previousValue != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              _buildComparison(context),
            ],
            const Spacer(),
            _buildTrend(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getMetricColor(metric.type).withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
          ),
          child: Icon(
            _getMetricIcon(metric.type),
            color: _getMetricColor(metric.type),
            size: 20,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: Text(
            metric.name,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildValue(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _formatValue(metric.value, metric.unit),
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: _getMetricColor(metric.type),
          ),
        ),
        if (metric.description.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            metric.description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildComparison(BuildContext context) {
    if (metric.previousValue == null) return const SizedBox.shrink();

    final change = metric.value - metric.previousValue!;
    final percentChange = metric.previousValue! != 0 
        ? (change / metric.previousValue!) * 100 
        : 0.0;
    
    final isPositive = change > 0;
    final isNegative = change < 0;
    
    Color changeColor;
    IconData changeIcon;
    
    if (isPositive) {
      changeColor = metric.type == MetricType.error || metric.type == MetricType.cost
          ? Colors.red
          : Colors.green;
      changeIcon = Icons.trending_up;
    } else if (isNegative) {
      changeColor = metric.type == MetricType.error || metric.type == MetricType.cost
          ? Colors.green
          : Colors.red;
      changeIcon = Icons.trending_down;
    } else {
      changeColor = Colors.grey;
      changeIcon = Icons.trending_flat;
    }

    return Row(
      children: [
        Icon(
          changeIcon,
          color: changeColor,
          size: 16,
        ),
        const SizedBox(width: 4),
        Text(
          '${percentChange >= 0 ? '+' : ''}${percentChange.toStringAsFixed(1)}%',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: changeColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          'vs previous period',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
        ),
      ],
    );
  }

  Widget _buildTrend(BuildContext context) {
    if (metric.trendData.isEmpty) return const SizedBox.shrink();

    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 4),
            Text(
              'Trend Chart',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getMetricColor(MetricType type) {
    switch (type) {
      case MetricType.performance:
        return Colors.blue;
      case MetricType.engagement:
        return Colors.green;
      case MetricType.conversion:
        return Colors.orange;
      case MetricType.revenue:
        return Colors.purple;
      case MetricType.cost:
        return Colors.red;
      case MetricType.error:
        return Colors.red;
      case MetricType.user:
        return Colors.teal;
      case MetricType.system:
        return Colors.grey;
    }
  }

  IconData _getMetricIcon(MetricType type) {
    switch (type) {
      case MetricType.performance:
        return Icons.speed;
      case MetricType.engagement:
        return Icons.favorite;
      case MetricType.conversion:
        return Icons.trending_up;
      case MetricType.revenue:
        return Icons.attach_money;
      case MetricType.cost:
        return Icons.money_off;
      case MetricType.error:
        return Icons.error;
      case MetricType.user:
        return Icons.people;
      case MetricType.system:
        return Icons.computer;
    }
  }

  String _formatValue(double value, MetricUnit unit) {
    switch (unit) {
      case MetricUnit.number:
        return value.toInt().toString();
      case MetricUnit.percentage:
        return '${value.toStringAsFixed(1)}%';
      case MetricUnit.currency:
        return '\$${value.toStringAsFixed(2)}';
      case MetricUnit.time:
        return '${value.toStringAsFixed(1)}s';
      case MetricUnit.bytes:
        return _formatBytes(value);
    }
  }

  String _formatBytes(double bytes) {
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var i = 0;
    while (bytes >= 1024 && i < suffixes.length - 1) {
      bytes /= 1024;
      i++;
    }
    return '${bytes.toStringAsFixed(1)} ${suffixes[i]}';
  }
}

/// Widget for displaying metric insights and recommendations
class MetricInsightsWidget extends StatelessWidget {
  final List<MetricInsight> insights;

  const MetricInsightsWidget({
    super.key,
    required this.insights,
  });

  @override
  Widget build(BuildContext context) {
    if (insights.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.insights,
                  size: 48,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'No insights available',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'Insights will appear here as data is collected',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: insights.length,
      itemBuilder: (context, index) {
        final insight = insights[index];
        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getInsightColor(insight.type).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                      ),
                      child: Icon(
                        _getInsightIcon(insight.type),
                        color: _getInsightColor(insight.type),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Text(
                        insight.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Chip(
                      label: Text(
                        insight.type.name.toUpperCase(),
                        style: const TextStyle(fontSize: 10),
                      ),
                      backgroundColor: _getInsightColor(insight.type).withOpacity(0.2),
                      side: BorderSide(color: _getInsightColor(insight.type)),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  insight.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (insight.recommendation.isNotEmpty) ...[
                  const SizedBox(height: AppConstants.defaultPadding),
                  Container(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Expanded(
                          child: Text(
                            insight.recommendation,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getInsightColor(InsightType type) {
    switch (type) {
      case InsightType.positive:
        return Colors.green;
      case InsightType.warning:
        return Colors.orange;
      case InsightType.critical:
        return Colors.red;
      case InsightType.neutral:
        return Colors.blue;
    }
  }

  IconData _getInsightIcon(InsightType type) {
    switch (type) {
      case InsightType.positive:
        return Icons.trending_up;
      case InsightType.warning:
        return Icons.warning;
      case InsightType.critical:
        return Icons.error;
      case InsightType.neutral:
        return Icons.info;
    }
  }
}

/// Time period options for metrics
enum MetricTimePeriod {
  day,
  week,
  month,
  quarter,
  year,
}

/// Types of metrics
enum MetricType {
  performance,
  engagement,
  conversion,
  revenue,
  cost,
  error,
  user,
  system,
}

/// Units for metric values
enum MetricUnit {
  number,
  percentage,
  currency,
  time,
  bytes,
}

/// Types of insights
enum InsightType {
  positive,
  warning,
  critical,
  neutral,
}
