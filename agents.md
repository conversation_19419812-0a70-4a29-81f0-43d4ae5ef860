# Quester - Gamified Quest & Task Management Platform
**Agent OS Compatible Architecture Document**  
**Version 2.0** | **Last Updated: August 25, 2025** | **Status: Active Development**

## 🎯 Project Overview

Quester is a modern gamified quest and task management platform that combines proven psychological game mechanics with contemporary architecture. The platform features a monorepo structure with three core packages working in perfect synchronization.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────────────┐
│                           QUESTER ECOSYSTEM                         │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐    │
│  │     CLIENT      │  │     SERVER      │  │     SHARED      │    │
│  │   (Flutter)     │◄─┤    (Dart)      │◄─┤    Package      │    │
│  │                 │  │                 │  │                 │    │
│  │ • Flutter Web   │  │ • Shelf Server  │  │ • Models        │    │
│  │ • BLo<PERSON> Pattern  │  │ • REST APIs     │  │ • Utilities     │    │
│  │ • Responsive UI │  │ • WebSocket     │  │ • Constants     │    │
│  │ • Real-time     │  │ • Middleware    │  │ • Contracts     │    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘    │
│                                                                     │
├─────────────────────────────────────────────────────────────────────┤
│                        INFRASTRUCTURE                               │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐  │
│  │ PostgreSQL  │  │    Redis    │  │    Nginx    │  │  Docker  │  │
│  │ Database    │  │   Cache     │  │   Proxy     │  │ Container│  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘  │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

---

## 📦 Package Architecture

### 1. 🔗 Shared Package (Foundation)
**Path:** `shared/`  
**Purpose:** Single source of truth for models, utilities, and contracts

#### Core Components:
- **Models**: `User`, `Quest`, `Task`, `Achievement`, `Message`, `Notification`
- **DTOs**: Data Transfer Objects with JSON serialization
- **Contracts**: Interface definitions for client-server synchronization
- **Constants**: API endpoints, WebSocket events, error codes
- **Utilities**: Crypto, validation, date handling, extensions
- **Services**: WebSocket handling, encryption, validation

```dart
// Example: Shared model with gamification features
@JsonSerializable()
class User extends Equatable {
  final String id;
  final String username;
  final int totalPoints;
  final int level;
  final int experience;
  final int streak;
  final List<String> achievements;
  // ...gamification logic
}
```

### 2. 🖥️ Server Package (Backend)
**Path:** `server/`  
**Purpose:** Dart-based HTTP server with Shelf framework

#### Architecture Layers:
- **Handlers**: Route-specific request processing
- **Services**: Business logic and data processing
- **Repositories**: Data access layer
- **Middleware**: Authentication, CORS, logging
- **WebSocket**: Real-time communication

```dart
// Example: Gamification service
class GamificationService {
  Future<void> awardPoints(String userId, int points) async {
    // Award points and check for level ups
    // Trigger achievement evaluations
    // Send real-time updates via WebSocket
  }
}
```

### 3. 📱 Client Package (Frontend)
**Path:** `client/`  
**Purpose:** Flutter web application with BLoC state management

#### Architecture Layers:
- **BLoC**: State management with events and states
- **Repositories**: Data access via HTTP/WebSocket
- **Services**: API communication and local storage
- **Presentation**: Pages, components, layouts
- **Utils**: Theming, responsive design, utilities

```dart
// Example: Quest BLoC with gamification
class QuestBloc extends Bloc<QuestEvent, QuestState> {
  QuestBloc() : super(QuestInitial()) {
    on<CompleteQuest>(_onCompleteQuest);
  }

  void _onCompleteQuest(CompleteQuest event, Emitter<QuestState> emit) {
    // Update quest status
    // Award points and achievements
    // Show celebration UI
  }
}
```

---

## 🎮 Gamification Features

### Core Game Mechanics
- **🌟 Point System**: Dynamic point calculation with bonuses
- **🏆 Achievement System**: Progress-based and skill-based rewards  
- **📊 Leaderboards**: Global and category-specific rankings
- **🔥 Streak System**: Daily activity incentives
- **🎁 Reward System**: Virtual badges and profile customizations

### Gamification Models
```dart
@JsonSerializable()
class Achievement {
  final String id;
  final String name;
  final AchievementType type;
  final int pointsRequired;
  final String iconUrl;
  final AchievementRarity rarity;
}

@JsonSerializable()
class UserProgress {
  final String userId;
  final int totalPoints;
  final int level;
  final int currentStreak;
  final List<String> unlockedAchievements;
}
```

---

## 🚀 Real-time Features

### WebSocket Integration
- **Live Updates**: Task completion, quest progress, user activity
- **Notifications**: Achievement unlocks, invitations, messages
- **Presence**: User online status and activity tracking
- **Collaboration**: Real-time quest updates and team coordination

### Event-Driven Architecture
```dart
// WebSocket event types
enum WebSocketEventType {
  taskUpdated,
  questCompleted,
  achievementUnlocked,
  userStatusChanged,
  notificationReceived,
}
```

---

## 🔧 Development Workflow

### 1. Shared Package Development
```bash
cd shared/
# Add new models or utilities
dart run build_runner build  # Generate JSON serialization
dart test                    # Run tests
```

### 2. Server Development  
```bash
cd server/
# Implement business logic
dart run bin/server.dart     # Start development server
dart test                    # Run API tests
```

### 3. Client Development
```bash
cd client/
# Build Flutter UI components
flutter run -d web-server    # Start development server
flutter test                 # Run widget tests
```

---

## 🐳 Docker Infrastructure

### Development Environment
- **Client**: Flutter web server on port 3000
- **Server**: Dart HTTP server on port 8080  
- **PostgreSQL**: Database on port 5432
- **Redis**: Cache and session store on port 6379
- **Nginx**: Reverse proxy on port 80
- **pgAdmin**: Database management on port 5050

### Quick Start Commands
```bash
# Setup and start development environment
bash auto-setup.sh
bash docker.sh dev start

# Monitor services
bash docker.sh health
bash docker.sh logs --follow
```

---

## 🗃️ Database Schema

### Core Tables
- **users**: User profiles with gamification stats
- **quests**: Quest definitions and metadata  
- **tasks**: Individual task items
- **achievements**: Achievement definitions
- **user_achievements**: User achievement unlocks
- **user_progress**: Gamification tracking
- **messages**: Real-time communication
- **notifications**: System notifications

---

## 📋 Feature Implementation Status

### ✅ Completed Features
- [x] **Project Architecture**: Monorepo with shared package
- [x] **Models & DTOs**: Complete data models with JSON serialization
- [x] **WebSocket Framework**: Real-time communication infrastructure  
- [x] **Docker Setup**: Full development environment
- [x] **Authentication Models**: User management foundation
- [x] **Gamification Models**: Points, achievements, progress tracking

### 🚧 In Progress  
- [ ] **Database Integration**: PostgreSQL with proper schemas
- [ ] **API Implementation**: REST endpoints with business logic
- [ ] **UI Development**: Flutter components and pages
- [ ] **Real-time Features**: WebSocket event handling
- [ ] **Authentication**: JWT-based auth system

### 📋 Planned Features
- [ ] **Advanced Gamification**: Achievement engine, leaderboards
- [ ] **Social Features**: Team quests, messaging, invitations
- [ ] **Analytics**: Progress tracking, performance insights
- [ ] **Mobile Apps**: Native iOS and Android versions

---

## 🔒 Security & Performance

### Security Measures
- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive data validation
- **CORS Configuration**: Proper cross-origin resource sharing
- **Rate Limiting**: API endpoint protection
- **Data Encryption**: Sensitive data protection

### Performance Optimizations
- **Redis Caching**: Session and frequently accessed data
- **Database Indexing**: Optimized query performance
- **CDN Integration**: Static asset delivery
- **WebSocket Efficiency**: Real-time update optimization
- **Flutter Optimization**: Web performance best practices

---

## 📚 Agent OS Integration

This document is designed for Agent OS workflows:

### Supported Commands
```bash
# Create new features
@~/.agent-os/instructions/core/create-spec.md

# Analyze and update architecture  
@~/.agent-os/instructions/core/analyze-product.md

# Implement specific features
@~/.agent-os/instructions/features/implement-feature.md
```

### Development Patterns
- **Contract-First**: Define shared contracts before implementation
- **Type Safety**: Comprehensive typing across all packages
- **Event-Driven**: WebSocket events for real-time updates
- **Testable**: Unit tests and integration tests
- **Scalable**: Microservice-ready architecture

---

## 📈 Next Development Phases

### Phase 1: Core Foundation (Current)
- Database integration with PostgreSQL
- Basic API endpoints implementation
- Flutter UI foundation with BLoC

### Phase 2: Gamification Engine
- Points and achievement system
- User progress tracking
- Basic leaderboards

### Phase 3: Real-time Features  
- WebSocket event implementation
- Live notifications
- Collaborative quest updates

### Phase 4: Advanced Features
- Social features and team management
- Analytics and insights
- Mobile app development

---

**Document Status**: ✅ Active Development | **Architecture**: Synchronized  
**Next Review**: September 1, 2025 | **Agent OS**: Compatible