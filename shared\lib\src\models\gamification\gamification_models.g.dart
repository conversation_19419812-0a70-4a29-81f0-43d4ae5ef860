// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gamification_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  type: $enumDecode(_$AchievementTypeEnumMap, json['type']),
  rarity: $enumDecode(_$AchievementRarityEnumMap, json['rarity']),
  iconUrl: json['iconUrl'] as String,
  pointsAwarded: (json['pointsAwarded'] as num).toInt(),
  progressRequired: (json['progressRequired'] as num).toInt(),
  isActive: json['isActive'] as bool? ?? true,
  createdAt: DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$AchievementTypeEnumMap[instance.type]!,
      'rarity': _$AchievementRarityEnumMap[instance.rarity]!,
      'iconUrl': instance.iconUrl,
      'pointsAwarded': instance.pointsAwarded,
      'progressRequired': instance.progressRequired,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$AchievementTypeEnumMap = {
  AchievementType.progress: 'progress',
  AchievementType.skill: 'skill',
  AchievementType.consistency: 'consistency',
  AchievementType.collaboration: 'collaboration',
  AchievementType.special: 'special',
};

const _$AchievementRarityEnumMap = {
  AchievementRarity.common: 'common',
  AchievementRarity.uncommon: 'uncommon',
  AchievementRarity.rare: 'rare',
  AchievementRarity.epic: 'epic',
  AchievementRarity.legendary: 'legendary',
};

UserAchievement _$UserAchievementFromJson(Map<String, dynamic> json) =>
    UserAchievement(
      achievementId: json['achievementId'] as String,
      userId: json['userId'] as String,
      currentProgress: (json['currentProgress'] as num).toInt(),
      status: $enumDecode(_$AchievementStatusEnumMap, json['status']),
      unlockedAt: json['unlockedAt'] == null
          ? null
          : DateTime.parse(json['unlockedAt'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$UserAchievementToJson(UserAchievement instance) =>
    <String, dynamic>{
      'achievementId': instance.achievementId,
      'userId': instance.userId,
      'currentProgress': instance.currentProgress,
      'status': _$AchievementStatusEnumMap[instance.status]!,
      'unlockedAt': instance.unlockedAt?.toIso8601String(),
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$AchievementStatusEnumMap = {
  AchievementStatus.locked: 'locked',
  AchievementStatus.available: 'available',
  AchievementStatus.inProgress: 'in_progress',
  AchievementStatus.completed: 'completed',
};

UserProgress _$UserProgressFromJson(Map<String, dynamic> json) => UserProgress(
  userId: json['userId'] as String,
  totalPoints: (json['totalPoints'] as num).toInt(),
  level: (json['level'] as num).toInt(),
  currentStreak: (json['currentStreak'] as num).toInt(),
  longestStreak: (json['longestStreak'] as num).toInt(),
  unlockedAchievements: (json['unlockedAchievements'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  questsCompleted: (json['questsCompleted'] as num).toInt(),
  tasksCompleted: (json['tasksCompleted'] as num).toInt(),
  pointsToday: (json['pointsToday'] as num).toInt(),
  lastActivity: json['lastActivity'] == null
      ? null
      : DateTime.parse(json['lastActivity'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$UserProgressToJson(UserProgress instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'totalPoints': instance.totalPoints,
      'level': instance.level,
      'currentStreak': instance.currentStreak,
      'longestStreak': instance.longestStreak,
      'unlockedAchievements': instance.unlockedAchievements,
      'questsCompleted': instance.questsCompleted,
      'tasksCompleted': instance.tasksCompleted,
      'pointsToday': instance.pointsToday,
      'lastActivity': instance.lastActivity?.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

PointTransaction _$PointTransactionFromJson(Map<String, dynamic> json) =>
    PointTransaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      points: (json['points'] as num).toInt(),
      type: $enumDecode(_$PointTransactionTypeEnumMap, json['type']),
      referenceId: json['referenceId'] as String?,
      description: json['description'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$PointTransactionToJson(PointTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'points': instance.points,
      'type': _$PointTransactionTypeEnumMap[instance.type]!,
      'referenceId': instance.referenceId,
      'description': instance.description,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$PointTransactionTypeEnumMap = {
  PointTransactionType.taskCompletion: 'task_completion',
  PointTransactionType.questCompletion: 'quest_completion',
  PointTransactionType.achievementUnlock: 'achievement_unlock',
  PointTransactionType.streakBonus: 'streak_bonus',
  PointTransactionType.collaborationBonus: 'collaboration_bonus',
  PointTransactionType.adminAdjustment: 'admin_adjustment',
};

LeaderboardEntry _$LeaderboardEntryFromJson(Map<String, dynamic> json) =>
    LeaderboardEntry(
      userId: json['userId'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      rank: (json['rank'] as num).toInt(),
      totalPoints: (json['totalPoints'] as num).toInt(),
      level: (json['level'] as num).toInt(),
      currentStreak: (json['currentStreak'] as num).toInt(),
      achievementCount: (json['achievementCount'] as num).toInt(),
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
    );

Map<String, dynamic> _$LeaderboardEntryToJson(LeaderboardEntry instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'displayName': instance.displayName,
      'avatarUrl': instance.avatarUrl,
      'rank': instance.rank,
      'totalPoints': instance.totalPoints,
      'level': instance.level,
      'currentStreak': instance.currentStreak,
      'achievementCount': instance.achievementCount,
      'calculatedAt': instance.calculatedAt.toIso8601String(),
    };
