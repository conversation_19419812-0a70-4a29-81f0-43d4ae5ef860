import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/api_repository.dart';

// Events
abstract class GamificationEvent extends Equatable {
  const GamificationEvent();
  
  @override
  List<Object?> get props => [];
}

class LoadUserPoints extends GamificationEvent {
  const LoadUserPoints();
}

class LoadUserStats extends GamificationEvent {
  const LoadUserStats();
}

class LoadUserAchievements extends GamificationEvent {
  const LoadUserAchievements();
}

class LoadLeaderboard extends GamificationEvent {
  const LoadLeaderboard();
}

class LoadRewards extends GamificationEvent {
  const LoadRewards();
}

class EarnPoints extends GamificationEvent {
  final int points;
  final String reason;
  final String? questId;
  final String? taskId;

  const EarnPoints({
    required this.points,
    required this.reason,
    this.questId,
    this.taskId,
  });

  @override
  List<Object?> get props => [points, reason, questId, taskId];
}

class UnlockAchievement extends GamificationEvent {
  final String achievementId;

  const UnlockAchievement({required this.achievementId});

  @override
  List<Object?> get props => [achievementId];
}

class ClaimReward extends GamificationEvent {
  final String rewardId;

  const ClaimReward({required this.rewardId});

  @override
  List<Object?> get props => [rewardId];
}

class UpdateStreak extends GamificationEvent {
  final int streakCount;
  final String streakType;

  const UpdateStreak({
    required this.streakCount,
    required this.streakType,
  });

  @override
  List<Object?> get props => [streakCount, streakType];
}

class LoadGamificationDashboard extends GamificationEvent {
  const LoadGamificationDashboard();
}

class RefreshGamificationData extends GamificationEvent {
  const RefreshGamificationData();
}

// States
abstract class GamificationState extends Equatable {
  const GamificationState();
  
  @override
  List<Object?> get props => [];
}

class GamificationInitial extends GamificationState {
  const GamificationInitial();
}

class GamificationLoading extends GamificationState {
  const GamificationLoading();
}

class GamificationLoaded extends GamificationState {
  final UserPoints? userPoints;
  final Map<String, dynamic>? userStats;
  final List<Achievement>? achievements;
  final List<Map<String, dynamic>>? leaderboard;
  final List<Map<String, dynamic>>? rewards;
  
  const GamificationLoaded({
    this.userPoints,
    this.userStats,
    this.achievements,
    this.leaderboard,
    this.rewards,
  });
  
  @override
  List<Object?> get props => [userPoints, userStats, achievements, leaderboard, rewards];
  
  GamificationLoaded copyWith({
    UserPoints? userPoints,
    Map<String, dynamic>? userStats,
    List<Achievement>? achievements,
    List<Map<String, dynamic>>? leaderboard,
    List<Map<String, dynamic>>? rewards,
  }) {
    return GamificationLoaded(
      userPoints: userPoints ?? this.userPoints,
      userStats: userStats ?? this.userStats,
      achievements: achievements ?? this.achievements,
      leaderboard: leaderboard ?? this.leaderboard,
      rewards: rewards ?? this.rewards,
    );
  }
}

class GamificationError extends GamificationState {
  final String message;

  const GamificationError({required this.message});

  @override
  List<Object?> get props => [message];
}

class PointsEarned extends GamificationState {
  final int points;
  final String reason;
  final int totalPoints;

  const PointsEarned({
    required this.points,
    required this.reason,
    required this.totalPoints,
  });

  @override
  List<Object?> get props => [points, reason, totalPoints];
}

class AchievementUnlocked extends GamificationState {
  final Achievement achievement;

  const AchievementUnlocked({required this.achievement});

  @override
  List<Object?> get props => [achievement];
}

class RewardClaimed extends GamificationState {
  final String rewardId;
  final String rewardName;

  const RewardClaimed({
    required this.rewardId,
    required this.rewardName,
  });

  @override
  List<Object?> get props => [rewardId, rewardName];
}

class StreakUpdated extends GamificationState {
  final int streakCount;
  final String streakType;
  final bool isNewRecord;

  const StreakUpdated({
    required this.streakCount,
    required this.streakType,
    required this.isNewRecord,
  });

  @override
  List<Object?> get props => [streakCount, streakType, isNewRecord];
}

class LevelUp extends GamificationState {
  final int newLevel;
  final int previousLevel;
  final List<String> unlockedFeatures;

  const LevelUp({
    required this.newLevel,
    required this.previousLevel,
    required this.unlockedFeatures,
  });

  @override
  List<Object?> get props => [newLevel, previousLevel, unlockedFeatures];
}

// BLoC
class GamificationBloc extends Bloc<GamificationEvent, GamificationState> {
  final ApiRepository repository;
  
  GamificationBloc({required this.repository}) : super(const GamificationInitial()) {
    on<LoadUserPoints>(_onLoadUserPoints);
    on<LoadUserStats>(_onLoadUserStats);
    on<LoadUserAchievements>(_onLoadUserAchievements);
    on<LoadLeaderboard>(_onLoadLeaderboard);
    on<LoadRewards>(_onLoadRewards);
    on<EarnPoints>(_onEarnPoints);
    on<UnlockAchievement>(_onUnlockAchievement);
    on<ClaimReward>(_onClaimReward);
    on<UpdateStreak>(_onUpdateStreak);
    on<LoadGamificationDashboard>(_onLoadGamificationDashboard);
    on<RefreshGamificationData>(_onRefreshGamificationData);
  }

  Future<void> _onLoadUserPoints(LoadUserPoints event, Emitter<GamificationState> emit) async {
    emit(const GamificationLoading());
    
    final response = await repository.getUserPoints();
    
    if (response.isSuccess && response.data != null) {
      if (state is GamificationLoaded) {
        emit((state as GamificationLoaded).copyWith(userPoints: response.data));
      } else {
        emit(GamificationLoaded(userPoints: response.data));
      }
    } else {
      emit(GamificationError(message: response.error ?? 'Failed to load user points'));
    }
  }

  Future<void> _onLoadUserStats(LoadUserStats event, Emitter<GamificationState> emit) async {
    if (state is! GamificationLoading) {
      emit(const GamificationLoading());
    }
    
    final response = await repository.getUserStats();
    
    if (response.isSuccess && response.data != null) {
      if (state is GamificationLoaded) {
        emit((state as GamificationLoaded).copyWith(userStats: response.data));
      } else {
        emit(GamificationLoaded(userStats: response.data));
      }
    } else {
      emit(GamificationError(message: response.error ?? 'Failed to load user stats'));
    }
  }

  Future<void> _onLoadUserAchievements(LoadUserAchievements event, Emitter<GamificationState> emit) async {
    if (state is! GamificationLoading) {
      emit(const GamificationLoading());
    }
    
    final response = await repository.getUserAchievements();
    
    if (response.isSuccess && response.data != null) {
      if (state is GamificationLoaded) {
        emit((state as GamificationLoaded).copyWith(achievements: response.data));
      } else {
        emit(GamificationLoaded(achievements: response.data));
      }
    } else {
      emit(GamificationError(message: response.error ?? 'Failed to load achievements'));
    }
  }

  Future<void> _onLoadLeaderboard(LoadLeaderboard event, Emitter<GamificationState> emit) async {
    if (state is! GamificationLoading) {
      emit(const GamificationLoading());
    }
    
    final response = await repository.getLeaderboard();
    
    if (response.isSuccess && response.data != null) {
      if (state is GamificationLoaded) {
        emit((state as GamificationLoaded).copyWith(leaderboard: response.data));
      } else {
        emit(GamificationLoaded(leaderboard: response.data));
      }
    } else {
      emit(GamificationError(message: response.error ?? 'Failed to load leaderboard'));
    }
  }

  Future<void> _onLoadRewards(LoadRewards event, Emitter<GamificationState> emit) async {
    if (state is! GamificationLoading) {
      emit(const GamificationLoading());
    }

    final response = await repository.getRewards();

    if (response.isSuccess && response.data != null) {
      if (state is GamificationLoaded) {
        emit((state as GamificationLoaded).copyWith(rewards: response.data));
      } else {
        emit(GamificationLoaded(rewards: response.data));
      }
    } else {
      emit(GamificationError(message: response.error ?? 'Failed to load rewards'));
    }
  }

  Future<void> _onEarnPoints(EarnPoints event, Emitter<GamificationState> emit) async {
    try {
      // TODO: Call API to earn points
      // final response = await repository.earnPoints(event.points, event.reason, event.questId, event.taskId);

      // For now, simulate earning points
      final currentState = state;
      int currentPoints = 0;
      if (currentState is GamificationLoaded && currentState.userPoints != null) {
        currentPoints = currentState.userPoints!.totalPoints;
      }

      final newTotalPoints = currentPoints + event.points;

      // Check for level up
      final currentLevel = _calculateLevel(currentPoints);
      final newLevel = _calculateLevel(newTotalPoints);

      emit(PointsEarned(
        points: event.points,
        reason: event.reason,
        totalPoints: newTotalPoints,
      ));

      // Check if user leveled up
      if (newLevel > currentLevel) {
        emit(LevelUp(
          newLevel: newLevel,
          previousLevel: currentLevel,
          unlockedFeatures: _getUnlockedFeatures(newLevel),
        ));
      }

      // Reload user points to get updated data
      add(const LoadUserPoints());
    } catch (e) {
      emit(GamificationError(message: 'Failed to earn points: $e'));
    }
  }

  Future<void> _onUnlockAchievement(UnlockAchievement event, Emitter<GamificationState> emit) async {
    try {
      // TODO: Call API to unlock achievement
      // final response = await repository.unlockAchievement(event.achievementId);

      // For now, simulate achievement unlock
      final achievement = Achievement(
        id: event.achievementId,
        name: 'Sample Achievement',
        description: 'You unlocked an achievement!',
        type: AchievementType.progress,
        rarity: AchievementRarity.common,
        iconUrl: 'assets/icons/achievement.png',
        pointsAwarded: 100,
        progressRequired: 1,
        isActive: true,
        createdAt: DateTime.now(),
      );

      emit(AchievementUnlocked(achievement: achievement));

      // Reload achievements to get updated data
      add(const LoadUserAchievements());
    } catch (e) {
      emit(GamificationError(message: 'Failed to unlock achievement: $e'));
    }
  }

  Future<void> _onClaimReward(ClaimReward event, Emitter<GamificationState> emit) async {
    try {
      // TODO: Call API to claim reward
      // final response = await repository.claimReward(event.rewardId);

      emit(RewardClaimed(
        rewardId: event.rewardId,
        rewardName: 'Sample Reward',
      ));

      // Reload rewards to get updated data
      add(const LoadRewards());
    } catch (e) {
      emit(GamificationError(message: 'Failed to claim reward: $e'));
    }
  }

  Future<void> _onUpdateStreak(UpdateStreak event, Emitter<GamificationState> emit) async {
    try {
      // TODO: Call API to update streak
      // final response = await repository.updateStreak(event.streakCount, event.streakType);

      // For now, simulate streak update
      final isNewRecord = event.streakCount > 0; // Simplified logic

      emit(StreakUpdated(
        streakCount: event.streakCount,
        streakType: event.streakType,
        isNewRecord: isNewRecord,
      ));

      // Reload user stats to get updated data
      add(const LoadUserStats());
    } catch (e) {
      emit(GamificationError(message: 'Failed to update streak: $e'));
    }
  }

  Future<void> _onLoadGamificationDashboard(LoadGamificationDashboard event, Emitter<GamificationState> emit) async {
    emit(const GamificationLoading());

    try {
      // Load all gamification data in parallel
      final futures = await Future.wait([
        repository.getUserPoints(),
        repository.getUserStats(),
        repository.getUserAchievements(),
        repository.getLeaderboard(),
        repository.getRewards(),
      ]);

      final pointsResponse = futures[0];
      final statsResponse = futures[1];
      final achievementsResponse = futures[2];
      final leaderboardResponse = futures[3];
      final rewardsResponse = futures[4];

      // For now, provide mock data since API methods are not implemented
      final now = DateTime.now();
      emit(GamificationLoaded(
        userPoints: UserPoints(
          userId: 'current_user',
          totalPoints: 1250,
          availablePoints: 850,
          spentPoints: 400,
          currentLevel: 5,
          currentLevelPoints: 250,
          pointsToNextLevel: 250,
          dailyPoints: 50,
          weeklyPoints: 200,
          monthlyPoints: 800,
          bestDailyPoints: 150,
          streakMultiplier: 1.2,
          roleMultiplier: 1.1,
          achievementBonusPoints: 100,
          dailyHistory: {
            now.subtract(Duration(days: 1)).toIso8601String().split('T')[0]: 50,
            now.subtract(Duration(days: 2)).toIso8601String().split('T')[0]: 75,
          },
          recentTransactions: [],
          lastUpdated: now,
          createdAt: now.subtract(Duration(days: 30)),
        ),
        userStats: {
          'level': 5,
          'experience': 1250,
          'nextLevelExp': 1500,
          'questsCompleted': 15,
          'achievementsUnlocked': 8,
        },
        achievements: [
          Achievement(
            id: 'first_quest',
            name: 'First Quest Complete',
            description: 'Complete your first quest',
            type: AchievementType.progress,
            rarity: AchievementRarity.common,
            iconUrl: 'assets/icons/first_quest.png',
            pointsAwarded: 10,
            progressRequired: 1,
            isActive: true,
            createdAt: DateTime.now(),
          ),
        ],
        leaderboard: [
          {'rank': 1, 'username': 'TopPlayer', 'points': 5000},
          {'rank': 2, 'username': 'current_user', 'points': 1250},
          {'rank': 3, 'username': 'Player3', 'points': 900},
        ],
        rewards: [
          {'id': 'badge_1', 'name': 'Gold Badge', 'cost': 500, 'available': true},
          {'id': 'theme_1', 'name': 'Dark Theme', 'cost': 200, 'available': true},
        ],
      ));
    } catch (e) {
      emit(GamificationError(message: 'Failed to load gamification dashboard: $e'));
    }
  }

  Future<void> _onRefreshGamificationData(RefreshGamificationData event, Emitter<GamificationState> emit) async {
    // Refresh all data without showing loading state
    add(const LoadGamificationDashboard());
  }

  // Helper methods
  int _calculateLevel(int points) {
    // Simple level calculation: every 1000 points = 1 level
    return (points / 1000).floor() + 1;
  }

  List<String> _getUnlockedFeatures(int level) {
    // Return features unlocked at this level
    final features = <String>[];
    if (level >= 5) features.add('Custom Avatar');
    if (level >= 10) features.add('Team Creation');
    if (level >= 15) features.add('Advanced Analytics');
    if (level >= 20) features.add('Premium Themes');
    return features;
  }
}