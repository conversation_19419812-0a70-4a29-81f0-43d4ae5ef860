// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateTaskDto _$CreateTaskDtoFromJson(Map<String, dynamic> json) =>
    CreateTaskDto(
      title: json['title'] as String,
      description: json['description'] as String,
      questId: json['questId'] as String?,
      assignedToId: json['assignedToId'] as String?,
      priority: $enumDecode(_$TaskPriorityEnumMap, json['priority']),
      complexity: $enumDecode(_$TaskComplexityEnumMap, json['complexity']),
      estimatedMinutes: (json['estimatedMinutes'] as num?)?.toInt(),
      deadline: json['deadline'] == null
          ? null
          : DateTime.parse(json['deadline'] as String),
      dependencies: (json['dependencies'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CreateTaskDtoToJson(CreateTaskDto instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'questId': instance.questId,
      'assignedToId': instance.assignedToId,
      'priority': _$TaskPriorityEnumMap[instance.priority]!,
      'complexity': _$TaskComplexityEnumMap[instance.complexity]!,
      'estimatedMinutes': instance.estimatedMinutes,
      'deadline': instance.deadline?.toIso8601String(),
      'dependencies': instance.dependencies,
      'tags': instance.tags,
      'metadata': instance.metadata,
    };

const _$TaskPriorityEnumMap = {
  TaskPriority.low: 'low',
  TaskPriority.medium: 'medium',
  TaskPriority.high: 'high',
  TaskPriority.critical: 'critical',
};

const _$TaskComplexityEnumMap = {
  TaskComplexity.simple: 'simple',
  TaskComplexity.moderate: 'moderate',
  TaskComplexity.complex: 'complex',
  TaskComplexity.veryComplex: 'very_complex',
};

UpdateTaskDto _$UpdateTaskDtoFromJson(Map<String, dynamic> json) =>
    UpdateTaskDto(
      title: json['title'] as String?,
      description: json['description'] as String?,
      questId: json['questId'] as String?,
      assignedToId: json['assignedToId'] as String?,
      status: $enumDecodeNullable(_$TaskStatusEnumMap, json['status']),
      priority: $enumDecodeNullable(_$TaskPriorityEnumMap, json['priority']),
      complexity: $enumDecodeNullable(
        _$TaskComplexityEnumMap,
        json['complexity'],
      ),
      estimatedMinutes: (json['estimatedMinutes'] as num?)?.toInt(),
      actualMinutes: (json['actualMinutes'] as num?)?.toInt(),
      deadline: json['deadline'] == null
          ? null
          : DateTime.parse(json['deadline'] as String),
      dependencies: (json['dependencies'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      comments: (json['comments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UpdateTaskDtoToJson(UpdateTaskDto instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'questId': instance.questId,
      'assignedToId': instance.assignedToId,
      'status': _$TaskStatusEnumMap[instance.status],
      'priority': _$TaskPriorityEnumMap[instance.priority],
      'complexity': _$TaskComplexityEnumMap[instance.complexity],
      'estimatedMinutes': instance.estimatedMinutes,
      'actualMinutes': instance.actualMinutes,
      'deadline': instance.deadline?.toIso8601String(),
      'dependencies': instance.dependencies,
      'tags': instance.tags,
      'attachments': instance.attachments,
      'comments': instance.comments,
      'metadata': instance.metadata,
    };

const _$TaskStatusEnumMap = {
  TaskStatus.todo: 'todo',
  TaskStatus.pending: 'pending',
  TaskStatus.inProgress: 'in_progress',
  TaskStatus.completed: 'completed',
  TaskStatus.cancelled: 'cancelled',
  TaskStatus.blocked: 'blocked',
};

TaskFilterDto _$TaskFilterDtoFromJson(Map<String, dynamic> json) =>
    TaskFilterDto(
      statuses: (json['statuses'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$TaskStatusEnumMap, e))
          .toList(),
      priorities: (json['priorities'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$TaskPriorityEnumMap, e))
          .toList(),
      complexities: (json['complexities'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$TaskComplexityEnumMap, e))
          .toList(),
      questId: json['questId'] as String?,
      createdById: json['createdById'] as String?,
      assignedToId: json['assignedToId'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      createdAfter: json['createdAfter'] == null
          ? null
          : DateTime.parse(json['createdAfter'] as String),
      createdBefore: json['createdBefore'] == null
          ? null
          : DateTime.parse(json['createdBefore'] as String),
      deadlineAfter: json['deadlineAfter'] == null
          ? null
          : DateTime.parse(json['deadlineAfter'] as String),
      deadlineBefore: json['deadlineBefore'] == null
          ? null
          : DateTime.parse(json['deadlineBefore'] as String),
      isOverdue: json['isOverdue'] as bool?,
      hasAttachments: json['hasAttachments'] as bool?,
      hasDependencies: json['hasDependencies'] as bool?,
      search: json['search'] as String?,
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
      sortBy: json['sortBy'] as String?,
      sortOrder: json['sortOrder'] as String?,
    );

Map<String, dynamic> _$TaskFilterDtoToJson(
  TaskFilterDto instance,
) => <String, dynamic>{
  'statuses': instance.statuses?.map((e) => _$TaskStatusEnumMap[e]!).toList(),
  'priorities': instance.priorities
      ?.map((e) => _$TaskPriorityEnumMap[e]!)
      .toList(),
  'complexities': instance.complexities
      ?.map((e) => _$TaskComplexityEnumMap[e]!)
      .toList(),
  'questId': instance.questId,
  'createdById': instance.createdById,
  'assignedToId': instance.assignedToId,
  'tags': instance.tags,
  'createdAfter': instance.createdAfter?.toIso8601String(),
  'createdBefore': instance.createdBefore?.toIso8601String(),
  'deadlineAfter': instance.deadlineAfter?.toIso8601String(),
  'deadlineBefore': instance.deadlineBefore?.toIso8601String(),
  'isOverdue': instance.isOverdue,
  'hasAttachments': instance.hasAttachments,
  'hasDependencies': instance.hasDependencies,
  'search': instance.search,
  'limit': instance.limit,
  'offset': instance.offset,
  'sortBy': instance.sortBy,
  'sortOrder': instance.sortOrder,
};

TaskListResponseDto _$TaskListResponseDtoFromJson(Map<String, dynamic> json) =>
    TaskListResponseDto(
      tasks: (json['tasks'] as List<dynamic>)
          .map((e) => Task.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['totalCount'] as num).toInt(),
      pageCount: (json['pageCount'] as num).toInt(),
      currentPage: (json['currentPage'] as num).toInt(),
      hasNext: json['hasNext'] as bool,
      hasPrevious: json['hasPrevious'] as bool,
    );

Map<String, dynamic> _$TaskListResponseDtoToJson(
  TaskListResponseDto instance,
) => <String, dynamic>{
  'tasks': instance.tasks,
  'totalCount': instance.totalCount,
  'pageCount': instance.pageCount,
  'currentPage': instance.currentPage,
  'hasNext': instance.hasNext,
  'hasPrevious': instance.hasPrevious,
};

TaskStatsDto _$TaskStatsDtoFromJson(Map<String, dynamic> json) => TaskStatsDto(
  totalTasks: (json['totalTasks'] as num).toInt(),
  todoTasks: (json['todoTasks'] as num).toInt(),
  inProgressTasks: (json['inProgressTasks'] as num).toInt(),
  completedTasks: (json['completedTasks'] as num).toInt(),
  blockedTasks: (json['blockedTasks'] as num).toInt(),
  overdueTasks: (json['overdueTasks'] as num).toInt(),
  todayDeadlines: (json['todayDeadlines'] as num).toInt(),
  completionRate: (json['completionRate'] as num).toDouble(),
  averageCompletionTime: (json['averageCompletionTime'] as num).toDouble(),
  totalPointsEarned: (json['totalPointsEarned'] as num).toInt(),
  statusBreakdown: Map<String, int>.from(json['statusBreakdown'] as Map),
  priorityBreakdown: Map<String, int>.from(json['priorityBreakdown'] as Map),
  complexityBreakdown: Map<String, int>.from(
    json['complexityBreakdown'] as Map,
  ),
);

Map<String, dynamic> _$TaskStatsDtoToJson(TaskStatsDto instance) =>
    <String, dynamic>{
      'totalTasks': instance.totalTasks,
      'todoTasks': instance.todoTasks,
      'inProgressTasks': instance.inProgressTasks,
      'completedTasks': instance.completedTasks,
      'blockedTasks': instance.blockedTasks,
      'overdueTasks': instance.overdueTasks,
      'todayDeadlines': instance.todayDeadlines,
      'completionRate': instance.completionRate,
      'averageCompletionTime': instance.averageCompletionTime,
      'totalPointsEarned': instance.totalPointsEarned,
      'statusBreakdown': instance.statusBreakdown,
      'priorityBreakdown': instance.priorityBreakdown,
      'complexityBreakdown': instance.complexityBreakdown,
    };

TaskCompletionDto _$TaskCompletionDtoFromJson(Map<String, dynamic> json) =>
    TaskCompletionDto(
      taskId: json['taskId'] as String,
      actualMinutes: (json['actualMinutes'] as num).toInt(),
      completionNotes: json['completionNotes'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      completionData: json['completionData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$TaskCompletionDtoToJson(TaskCompletionDto instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'actualMinutes': instance.actualMinutes,
      'completionNotes': instance.completionNotes,
      'attachments': instance.attachments,
      'completionData': instance.completionData,
    };

BulkTaskUpdateDto _$BulkTaskUpdateDtoFromJson(Map<String, dynamic> json) =>
    BulkTaskUpdateDto(
      taskIds: (json['taskIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      status: $enumDecodeNullable(_$TaskStatusEnumMap, json['status']),
      priority: $enumDecodeNullable(_$TaskPriorityEnumMap, json['priority']),
      assignedToId: json['assignedToId'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      deadline: json['deadline'] == null
          ? null
          : DateTime.parse(json['deadline'] as String),
    );

Map<String, dynamic> _$BulkTaskUpdateDtoToJson(BulkTaskUpdateDto instance) =>
    <String, dynamic>{
      'taskIds': instance.taskIds,
      'status': _$TaskStatusEnumMap[instance.status],
      'priority': _$TaskPriorityEnumMap[instance.priority],
      'assignedToId': instance.assignedToId,
      'tags': instance.tags,
      'deadline': instance.deadline?.toIso8601String(),
    };

TaskCommentDto _$TaskCommentDtoFromJson(Map<String, dynamic> json) =>
    TaskCommentDto(
      taskId: json['taskId'] as String,
      comment: json['comment'] as String,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      replyToCommentId: json['replyToCommentId'] as String?,
    );

Map<String, dynamic> _$TaskCommentDtoToJson(TaskCommentDto instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'comment': instance.comment,
      'attachments': instance.attachments,
      'replyToCommentId': instance.replyToCommentId,
    };
