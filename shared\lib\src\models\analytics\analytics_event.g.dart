// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalyticsEvent _$AnalyticsEventFromJson(Map<String, dynamic> json) =>
    AnalyticsEvent(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      userId: json['userId'] as String?,
      sessionId: json['sessionId'] as String?,
      eventType: $enumDecode(_$AnalyticsEventTypeEnumMap, json['eventType']),
      eventName: json['eventName'] as String,
      eventData: json['eventData'] as Map<String, dynamic>,
      eventProperties: json['eventProperties'] as Map<String, dynamic>?,
      userAgent: json['userAgent'] as String?,
      ipAddress: json['ipAddress'] as String?,
      referrer: json['referrer'] as String?,
      pageUrl: json['pageUrl'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$AnalyticsEventToJson(AnalyticsEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'userId': instance.userId,
      'sessionId': instance.sessionId,
      'eventType': _$AnalyticsEventTypeEnumMap[instance.eventType]!,
      'eventName': instance.eventName,
      'eventData': instance.eventData,
      'eventProperties': instance.eventProperties,
      'userAgent': instance.userAgent,
      'ipAddress': instance.ipAddress,
      'referrer': instance.referrer,
      'pageUrl': instance.pageUrl,
      'timestamp': instance.timestamp.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$AnalyticsEventTypeEnumMap = {
  AnalyticsEventType.pageView: 'page_view',
  AnalyticsEventType.buttonClick: 'button_click',
  AnalyticsEventType.taskAction: 'task_action',
  AnalyticsEventType.questAction: 'quest_action',
  AnalyticsEventType.achievementUnlock: 'achievement_unlock',
  AnalyticsEventType.rewardPurchase: 'reward_purchase',
  AnalyticsEventType.login: 'login',
  AnalyticsEventType.logout: 'logout',
  AnalyticsEventType.search: 'search',
  AnalyticsEventType.export: 'export',
  AnalyticsEventType.reportGenerate: 'report_generate',
  AnalyticsEventType.dashboardView: 'dashboard_view',
  AnalyticsEventType.collaborationAction: 'collaboration_action',
  AnalyticsEventType.gamificationInteraction: 'gamification_interaction',
  AnalyticsEventType.enterpriseAction: 'enterprise_action',
  AnalyticsEventType.apiCall: 'api_call',
};

AnalyticsEventRequest _$AnalyticsEventRequestFromJson(
  Map<String, dynamic> json,
) => AnalyticsEventRequest(
  organizationId: json['organizationId'] as String,
  userId: json['userId'] as String?,
  sessionId: json['sessionId'] as String?,
  eventType: $enumDecode(_$AnalyticsEventTypeEnumMap, json['eventType']),
  eventName: json['eventName'] as String,
  eventData: json['eventData'] as Map<String, dynamic>,
  eventProperties: json['eventProperties'] as Map<String, dynamic>?,
  pageUrl: json['pageUrl'] as String?,
  referrer: json['referrer'] as String?,
);

Map<String, dynamic> _$AnalyticsEventRequestToJson(
  AnalyticsEventRequest instance,
) => <String, dynamic>{
  'organizationId': instance.organizationId,
  'userId': instance.userId,
  'sessionId': instance.sessionId,
  'eventType': _$AnalyticsEventTypeEnumMap[instance.eventType]!,
  'eventName': instance.eventName,
  'eventData': instance.eventData,
  'eventProperties': instance.eventProperties,
  'pageUrl': instance.pageUrl,
  'referrer': instance.referrer,
};

AnalyticsEventsBatch _$AnalyticsEventsBatchFromJson(
  Map<String, dynamic> json,
) => AnalyticsEventsBatch(
  organizationId: json['organizationId'] as String,
  events: (json['events'] as List<dynamic>)
      .map((e) => AnalyticsEventRequest.fromJson(e as Map<String, dynamic>))
      .toList(),
  timestamp: DateTime.parse(json['timestamp'] as String),
  clientInfo: json['clientInfo'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$AnalyticsEventsBatchToJson(
  AnalyticsEventsBatch instance,
) => <String, dynamic>{
  'organizationId': instance.organizationId,
  'events': instance.events,
  'timestamp': instance.timestamp.toIso8601String(),
  'clientInfo': instance.clientInfo,
};
