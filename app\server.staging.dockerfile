# Staging Dockerfile for Quester Server
FROM dart:stable AS build

WORKDIR /app
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY server/ ./server/

# Set working directory to server and build
WORKDIR /app/server
RUN dart pub get
RUN dart compile exe bin/server.dart -o bin/server

FROM debian:bookworm-slim AS staging
RUN apt-get update && apt-get install -y wget ca-certificates && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY --from=build /app/server/bin/server ./

RUN groupadd -r quester && useradd -r -g quester quester -m
RUN chown -R quester:quester /app
USER quester

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

CMD ["./server"]
