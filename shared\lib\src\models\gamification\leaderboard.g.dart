// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leaderboard.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeaderboardEntry _$LeaderboardEntryFromJson(Map<String, dynamic> json) =>
    LeaderboardEntry(
      userId: json['userId'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      role: json['role'] as String,
      rank: (json['rank'] as num).toInt(),
      previousRank: (json['previousRank'] as num?)?.toInt(),
      score: (json['score'] as num).toDouble(),
      previousScore: (json['previousScore'] as num?)?.toDouble(),
      stats: json['stats'] as Map<String, dynamic>?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$LeaderboardEntryToJson(LeaderboardEntry instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'displayName': instance.displayName,
      'avatarUrl': instance.avatarUrl,
      'role': instance.role,
      'rank': instance.rank,
      'previousRank': instance.previousRank,
      'score': instance.score,
      'previousScore': instance.previousScore,
      'stats': instance.stats,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

Leaderboard _$LeaderboardFromJson(Map<String, dynamic> json) => Leaderboard(
  id: json['id'] as String,
  category: $enumDecode(_$LeaderboardCategoryEnumMap, json['category']),
  period: $enumDecode(_$LeaderboardPeriodEnumMap, json['period']),
  title: json['title'] as String,
  description: json['description'] as String,
  maxEntries: (json['maxEntries'] as num).toInt(),
  entries: (json['entries'] as List<dynamic>)
      .map((e) => LeaderboardEntry.fromJson(e as Map<String, dynamic>))
      .toList(),
  totalParticipants: (json['totalParticipants'] as num).toInt(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  isRealTime: json['isRealTime'] as bool,
  updateFrequencyMinutes: (json['updateFrequencyMinutes'] as num?)?.toInt(),
  lastCalculated: DateTime.parse(json['lastCalculated'] as String),
  createdAt: DateTime.parse(json['createdAt'] as String),
  nextUpdate: json['nextUpdate'] == null
      ? null
      : DateTime.parse(json['nextUpdate'] as String),
);

Map<String, dynamic> _$LeaderboardToJson(Leaderboard instance) =>
    <String, dynamic>{
      'id': instance.id,
      'category': _$LeaderboardCategoryEnumMap[instance.category]!,
      'period': _$LeaderboardPeriodEnumMap[instance.period]!,
      'title': instance.title,
      'description': instance.description,
      'maxEntries': instance.maxEntries,
      'entries': instance.entries,
      'totalParticipants': instance.totalParticipants,
      'metadata': instance.metadata,
      'isRealTime': instance.isRealTime,
      'updateFrequencyMinutes': instance.updateFrequencyMinutes,
      'lastCalculated': instance.lastCalculated.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'nextUpdate': instance.nextUpdate?.toIso8601String(),
    };

const _$LeaderboardCategoryEnumMap = {
  LeaderboardCategory.totalPoints: 'total_points',
  LeaderboardCategory.monthlyPoints: 'monthly_points',
  LeaderboardCategory.weeklyPoints: 'weekly_points',
  LeaderboardCategory.dailyPoints: 'daily_points',
  LeaderboardCategory.questsCompleted: 'quests_completed',
  LeaderboardCategory.tasksCompleted: 'tasks_completed',
  LeaderboardCategory.currentStreak: 'current_streak',
  LeaderboardCategory.longestStreak: 'longest_streak',
  LeaderboardCategory.achievementsCount: 'achievements_count',
  LeaderboardCategory.collaborationScore: 'collaboration_score',
  LeaderboardCategory.efficiencyRating: 'efficiency_rating',
};

const _$LeaderboardPeriodEnumMap = {
  LeaderboardPeriod.allTime: 'all_time',
  LeaderboardPeriod.yearly: 'yearly',
  LeaderboardPeriod.monthly: 'monthly',
  LeaderboardPeriod.weekly: 'weekly',
  LeaderboardPeriod.daily: 'daily',
};
