import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user_points.g.dart';

/// Point transaction types for tracking point changes
enum PointTransactionType {
  @JsonValue('earned_quest')
  earnedQuest,
  @JsonValue('earned_task')
  earnedTask,
  @JsonValue('earned_achievement')
  earnedAchievement,
  @JsonValue('earned_streak')
  earnedStreak,
  @JsonValue('earned_bonus')
  earnedBonus,
  @JsonValue('spent_reward')
  spentReward,
  @JsonValue('spent_upgrade')
  spentUpgrade,
  @JsonValue('adjustment_admin')
  adjustmentAdmin,
  @JsonValue('penalty')
  penalty,
}

/// Point transaction record
@JsonSerializable()
class PointTransaction extends Equatable {
  /// Unique transaction identifier
  final String id;

  /// User ID this transaction belongs to
  final String userId;

  /// Transaction type
  final PointTransactionType type;

  /// Points changed (positive for earned, negative for spent)
  final int pointsChanged;

  /// Source ID (quest ID, task ID, achievement ID, etc.)
  final String? sourceId;

  /// Source type description
  final String? sourceType;

  /// Transaction description
  final String description;

  /// Multiplier applied (role bonus, streak bonus, etc.)
  final double multiplier;

  /// Base points before multiplier
  final int basePoints;

  /// Transaction metadata
  final Map<String, dynamic>? metadata;

  /// Transaction timestamp
  final DateTime timestamp;

  const PointTransaction({
    required this.id,
    required this.userId,
    required this.type,
    required this.pointsChanged,
    this.sourceId,
    this.sourceType,
    required this.description,
    required this.multiplier,
    required this.basePoints,
    this.metadata,
    required this.timestamp,
  });

  /// Create PointTransaction from JSON
  factory PointTransaction.fromJson(Map<String, dynamic> json) => _$PointTransactionFromJson(json);

  /// Convert PointTransaction to JSON
  Map<String, dynamic> toJson() => _$PointTransactionToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        pointsChanged,
        sourceId,
        sourceType,
        description,
        multiplier,
        basePoints,
        metadata,
        timestamp,
      ];

  @override
  bool get stringify => true;
}

/// Comprehensive user points model with role-based system from CLAUDE.md
@JsonSerializable()
class UserPoints extends Equatable {
  /// User ID this points record belongs to
  final String userId;

  /// Total lifetime points earned
  final int totalPoints;

  /// Available points for spending
  final int availablePoints;

  /// Points spent on rewards/upgrades
  final int spentPoints;

  /// Current level based on total points
  final int currentLevel;

  /// Points earned in current level
  final int currentLevelPoints;

  /// Points needed for next level
  final int pointsToNextLevel;

  /// Daily points earned today
  final int dailyPoints;

  /// Weekly points earned this week
  final int weeklyPoints;

  /// Monthly points earned this month
  final int monthlyPoints;

  /// Highest daily points earned
  final int bestDailyPoints;

  /// Current streak bonus multiplier
  final double streakMultiplier;

  /// Role-based points multiplier
  final double roleMultiplier;

  /// Total bonus points from achievements
  final int achievementBonusPoints;

  /// Daily points history (date -> points earned that day)
  final Map<String, int> dailyHistory;

  /// Recent point transactions (last 10)
  final List<PointTransaction> recentTransactions;

  /// Points statistics metadata
  final Map<String, dynamic>? statistics;

  /// Last points update timestamp
  final DateTime lastUpdated;

  /// Points record creation timestamp
  final DateTime createdAt;

  const UserPoints({
    required this.userId,
    required this.totalPoints,
    required this.availablePoints,
    required this.spentPoints,
    required this.currentLevel,
    required this.currentLevelPoints,
    required this.pointsToNextLevel,
    required this.dailyPoints,
    required this.weeklyPoints,
    required this.monthlyPoints,
    required this.bestDailyPoints,
    required this.streakMultiplier,
    required this.roleMultiplier,
    required this.achievementBonusPoints,
    required this.dailyHistory,
    required this.recentTransactions,
    this.statistics,
    required this.lastUpdated,
    required this.createdAt,
  });

  /// Create UserPoints from JSON
  factory UserPoints.fromJson(Map<String, dynamic> json) => _$UserPointsFromJson(json);

  /// Convert UserPoints to JSON
  Map<String, dynamic> toJson() => _$UserPointsToJson(this);

  /// Get total multiplier (role * streak)
  double get totalMultiplier => roleMultiplier * streakMultiplier;

  /// Get level progress percentage (0.0 - 1.0)
  double get levelProgress {
    final levelRange = _getLevelRange(currentLevel);
    return levelRange > 0 ? currentLevelPoints / levelRange : 0.0;
  }

  /// Get average daily points over the last 30 days
  double get averageDailyPoints {
    if (dailyHistory.isEmpty) return 0.0;
    
    final now = DateTime.now();
    int totalPoints = 0;
    int days = 0;
    
    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey = _formatDate(date);
      if (dailyHistory.containsKey(dateKey)) {
        totalPoints += dailyHistory[dateKey]!;
        days++;
      }
    }
    
    return days > 0 ? totalPoints / days : 0.0;
  }

  /// Get points earned in the last 7 days
  int get weeklyTotalPoints {
    final now = DateTime.now();
    int total = 0;
    
    for (int i = 0; i < 7; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey = _formatDate(date);
      total += dailyHistory[dateKey] ?? 0;
    }
    
    return total;
  }

  /// Calculate points with current multipliers
  int calculateEarnedPoints(int basePoints) {
    return (basePoints * totalMultiplier).round();
  }

  /// Check if user can afford a purchase
  bool canAfford(int cost) => availablePoints >= cost;

  /// Get spending power percentage (available / total earned)
  double get spendingPowerPercentage {
    if (totalPoints <= 0) return 0.0;
    return availablePoints / totalPoints;
  }

  /// Get efficiency rating based on points vs time
  double getEfficiencyRating() {
    final daysSinceCreation = DateTime.now().difference(createdAt).inDays;
    if (daysSinceCreation <= 0) return 0.0;
    return totalPoints / daysSinceCreation;
  }

  /// Get level range (points between current level start and next level start)
  static int _getLevelRange(int level) {
    final currentLevelThreshold = _getLevelThreshold(level);
    final nextLevelThreshold = _getLevelThreshold(level + 1);
    return nextLevelThreshold - currentLevelThreshold;
  }

  /// Get threshold points for a given level (matching User model)
  static int _getLevelThreshold(int level) {
    return level * level * 100;
  }

  /// Calculate level from total points
  static int calculateLevel(int totalPoints) {
    if (totalPoints <= 0) return 1;
    
    // Binary search for efficiency with large point totals
    int level = 1;
    while (_getLevelThreshold(level + 1) <= totalPoints) {
      level++;
    }
    return level;
  }

  /// Format date for daily history keys (YYYY-MM-DD)
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Create a copy with updated fields
  UserPoints copyWith({
    String? userId,
    int? totalPoints,
    int? availablePoints,
    int? spentPoints,
    int? currentLevel,
    int? currentLevelPoints,
    int? pointsToNextLevel,
    int? dailyPoints,
    int? weeklyPoints,
    int? monthlyPoints,
    int? bestDailyPoints,
    double? streakMultiplier,
    double? roleMultiplier,
    int? achievementBonusPoints,
    Map<String, int>? dailyHistory,
    List<PointTransaction>? recentTransactions,
    Map<String, dynamic>? statistics,
    DateTime? lastUpdated,
    DateTime? createdAt,
  }) {
    return UserPoints(
      userId: userId ?? this.userId,
      totalPoints: totalPoints ?? this.totalPoints,
      availablePoints: availablePoints ?? this.availablePoints,
      spentPoints: spentPoints ?? this.spentPoints,
      currentLevel: currentLevel ?? this.currentLevel,
      currentLevelPoints: currentLevelPoints ?? this.currentLevelPoints,
      pointsToNextLevel: pointsToNextLevel ?? this.pointsToNextLevel,
      dailyPoints: dailyPoints ?? this.dailyPoints,
      weeklyPoints: weeklyPoints ?? this.weeklyPoints,
      monthlyPoints: monthlyPoints ?? this.monthlyPoints,
      bestDailyPoints: bestDailyPoints ?? this.bestDailyPoints,
      streakMultiplier: streakMultiplier ?? this.streakMultiplier,
      roleMultiplier: roleMultiplier ?? this.roleMultiplier,
      achievementBonusPoints: achievementBonusPoints ?? this.achievementBonusPoints,
      dailyHistory: dailyHistory ?? this.dailyHistory,
      recentTransactions: recentTransactions ?? this.recentTransactions,
      statistics: statistics ?? this.statistics,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Create empty user points for initialization
  static UserPoints empty(String userId) {
    final now = DateTime.now();
    return UserPoints(
      userId: userId,
      totalPoints: 0,
      availablePoints: 0,
      spentPoints: 0,
      currentLevel: 1,
      currentLevelPoints: 0,
      pointsToNextLevel: 100,
      dailyPoints: 0,
      weeklyPoints: 0,
      monthlyPoints: 0,
      bestDailyPoints: 0,
      streakMultiplier: 1.0,
      roleMultiplier: 1.0,
      achievementBonusPoints: 0,
      dailyHistory: {},
      recentTransactions: [],
      lastUpdated: now,
      createdAt: now,
    );
  }

  @override
  List<Object?> get props => [
        userId,
        totalPoints,
        availablePoints,
        spentPoints,
        currentLevel,
        currentLevelPoints,
        pointsToNextLevel,
        dailyPoints,
        weeklyPoints,
        monthlyPoints,
        bestDailyPoints,
        streakMultiplier,
        roleMultiplier,
        achievementBonusPoints,
        dailyHistory,
        recentTransactions,
        statistics,
        lastUpdated,
        createdAt,
      ];

  @override
  bool get stringify => true;
}