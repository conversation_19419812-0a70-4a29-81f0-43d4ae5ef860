// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailedNotificationPreferences _$DetailedNotificationPreferencesFromJson(
  Map<String, dynamic> json,
) => DetailedNotificationPreferences(
  userId: json['userId'] as String,
  enableNotifications: json['enableNotifications'] as bool? ?? true,
  enablePushNotifications: json['enablePushNotifications'] as bool? ?? true,
  enableEmailNotifications: json['enableEmailNotifications'] as bool? ?? true,
  enableSmsNotifications: json['enableSmsNotifications'] as bool? ?? false,
  enableInAppNotifications: json['enableInAppNotifications'] as bool? ?? true,
  enableWebhookNotifications:
      json['enableWebhookNotifications'] as bool? ?? false,
  quietHours: json['quietHours'] == null
      ? null
      : QuietHours.fromJson(json['quietHours'] as Map<String, dynamic>),
  categoryPreferences:
      (json['categoryPreferences'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
          $enumDecode(_$NotificationCategoryEnumMap, k),
          CategoryPreferences.fromJson(e as Map<String, dynamic>),
        ),
      ) ??
      const {},
  deviceSettings: DeviceNotificationSettings.fromJson(
    json['deviceSettings'] as Map<String, dynamic>,
  ),
  frequency:
      $enumDecodeNullable(_$NotificationFrequencyEnumMap, json['frequency']) ??
      NotificationFrequency.immediate,
  language: json['language'] as String? ?? 'en',
  timezone: json['timezone'] as String? ?? 'UTC',
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$DetailedNotificationPreferencesToJson(
  DetailedNotificationPreferences instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'enableNotifications': instance.enableNotifications,
  'enablePushNotifications': instance.enablePushNotifications,
  'enableEmailNotifications': instance.enableEmailNotifications,
  'enableSmsNotifications': instance.enableSmsNotifications,
  'enableInAppNotifications': instance.enableInAppNotifications,
  'enableWebhookNotifications': instance.enableWebhookNotifications,
  'quietHours': instance.quietHours,
  'categoryPreferences': instance.categoryPreferences.map(
    (k, e) => MapEntry(_$NotificationCategoryEnumMap[k]!, e),
  ),
  'deviceSettings': instance.deviceSettings,
  'frequency': _$NotificationFrequencyEnumMap[instance.frequency]!,
  'language': instance.language,
  'timezone': instance.timezone,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$NotificationCategoryEnumMap = {
  NotificationCategory.system: 'system',
  NotificationCategory.quest: 'quest',
  NotificationCategory.achievement: 'achievement',
  NotificationCategory.social: 'social',
  NotificationCategory.reminder: 'reminder',
  NotificationCategory.marketing: 'marketing',
  NotificationCategory.security: 'security',
  NotificationCategory.team: 'team',
};

const _$NotificationFrequencyEnumMap = {
  NotificationFrequency.immediate: 'immediate',
  NotificationFrequency.every15Minutes: 'every15Minutes',
  NotificationFrequency.hourly: 'hourly',
  NotificationFrequency.daily: 'daily',
  NotificationFrequency.weekly: 'weekly',
  NotificationFrequency.never: 'never',
};

QuietHours _$QuietHoursFromJson(Map<String, dynamic> json) => QuietHours(
  enabled: json['enabled'] as bool? ?? false,
  startTime: json['startTime'] == null
      ? const TimeOfDay(hour: 22, minute: 0)
      : TimeOfDay.fromJson(json['startTime'] as Map<String, dynamic>),
  endTime: json['endTime'] == null
      ? const TimeOfDay(hour: 8, minute: 0)
      : TimeOfDay.fromJson(json['endTime'] as Map<String, dynamic>),
  daysOfWeek:
      (json['daysOfWeek'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList() ??
      const [1, 2, 3, 4, 5, 6, 7],
  allowCritical: json['allowCritical'] as bool? ?? true,
);

Map<String, dynamic> _$QuietHoursToJson(QuietHours instance) =>
    <String, dynamic>{
      'enabled': instance.enabled,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'daysOfWeek': instance.daysOfWeek,
      'allowCritical': instance.allowCritical,
    };

TimeOfDay _$TimeOfDayFromJson(Map<String, dynamic> json) => TimeOfDay(
  hour: (json['hour'] as num).toInt(),
  minute: (json['minute'] as num).toInt(),
);

Map<String, dynamic> _$TimeOfDayToJson(TimeOfDay instance) => <String, dynamic>{
  'hour': instance.hour,
  'minute': instance.minute,
};

CategoryPreferences _$CategoryPreferencesFromJson(Map<String, dynamic> json) =>
    CategoryPreferences(
      enabled: json['enabled'] as bool? ?? true,
      enablePush: json['enablePush'] as bool? ?? true,
      enableEmail: json['enableEmail'] as bool? ?? true,
      enableSms: json['enableSms'] as bool? ?? false,
      enableInApp: json['enableInApp'] as bool? ?? true,
      enableWebhook: json['enableWebhook'] as bool? ?? false,
      minimumPriority:
          $enumDecodeNullable(
            _$NotificationPriorityEnumMap,
            json['minimumPriority'],
          ) ??
          NotificationPriority.low,
      customSound: json['customSound'] as String?,
      showBadge: json['showBadge'] as bool? ?? true,
    );

Map<String, dynamic> _$CategoryPreferencesToJson(
  CategoryPreferences instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'enablePush': instance.enablePush,
  'enableEmail': instance.enableEmail,
  'enableSms': instance.enableSms,
  'enableInApp': instance.enableInApp,
  'enableWebhook': instance.enableWebhook,
  'minimumPriority': _$NotificationPriorityEnumMap[instance.minimumPriority]!,
  'customSound': instance.customSound,
  'showBadge': instance.showBadge,
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'low',
  NotificationPriority.normal: 'normal',
  NotificationPriority.high: 'high',
  NotificationPriority.urgent: 'urgent',
};

DeviceNotificationSettings _$DeviceNotificationSettingsFromJson(
  Map<String, dynamic> json,
) => DeviceNotificationSettings(
  pushToken: json['pushToken'] as String?,
  platform: json['platform'] as String,
  appVersion: json['appVersion'] as String,
  timezone: json['timezone'] as String? ?? 'UTC',
  language: json['language'] as String? ?? 'en',
  supportsRichNotifications:
      json['supportsRichNotifications'] as bool? ?? false,
  supportsActions: json['supportsActions'] as bool? ?? false,
  maxActions: (json['maxActions'] as num?)?.toInt() ?? 2,
  supportsCustomSounds: json['supportsCustomSounds'] as bool? ?? false,
  supportsVibration: json['supportsVibration'] as bool? ?? false,
);

Map<String, dynamic> _$DeviceNotificationSettingsToJson(
  DeviceNotificationSettings instance,
) => <String, dynamic>{
  'pushToken': instance.pushToken,
  'platform': instance.platform,
  'appVersion': instance.appVersion,
  'timezone': instance.timezone,
  'language': instance.language,
  'supportsRichNotifications': instance.supportsRichNotifications,
  'supportsActions': instance.supportsActions,
  'maxActions': instance.maxActions,
  'supportsCustomSounds': instance.supportsCustomSounds,
  'supportsVibration': instance.supportsVibration,
};
