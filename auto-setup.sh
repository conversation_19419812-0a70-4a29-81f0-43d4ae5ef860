#!/bin/bash
# Quester Project Auto-Setup Script
# Automatically sets up the development environment for the Quester project
# Usage: bash auto-setup.sh [ENVIRONMENT] [OPTIONS]
# Version: 3.4.0 - Fixed Docker shared package paths and working directories for proper dependency resolution

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Global variables for cleanup
CLEANUP_FILES=()
CLEANUP_DIRS=()

# Cleanup function
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        echo -e "${RED}Script failed with exit code $exit_code${NC}"
    fi
}

# Set up trap for cleanup
trap cleanup EXIT INT TERM

# Script version
SCRIPT_VERSION="3.4.0"

# Default values
ENVIRONMENT="dev"  # Default to dev environment
PROJECT_NAME="quester"
APP_DIR="app"
BACKUP_DIR="./app/backups"
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        dev|staging|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            # Help content moved here
            echo -e "${BLUE}🚀 Quester Project Auto-Setup Script${NC}"
            echo -e "${CYAN}Version: $SCRIPT_VERSION${NC}"
            echo ""
            echo -e "${YELLOW}Usage:${NC} bash auto-setup.sh [ENVIRONMENT] [OPTIONS]"
            echo ""
            echo -e "${YELLOW}ENVIRONMENTS:${NC}"
            echo "  dev       - Development environment (default)"
            echo "  staging   - Staging environment"
            echo "  prod      - Production environment"
            echo ""
            echo -e "${YELLOW}OPTIONS:${NC}"
            echo "  --dry-run - Show what would be done without making changes"
            echo "  --help    - Show this help message"
            echo "  --version - Show script version"
            echo ""
            echo -e "${YELLOW}Description:${NC}"
            echo "  Automatically sets up the development environment for the Quester project."
            echo ""
            echo -e "${YELLOW}Examples:${NC}"
            echo "  bash auto-setup.sh              # Setup development environment"
            echo "  bash auto-setup.sh dev          # Setup development environment"
            echo "  bash auto-setup.sh staging      # Setup staging environment"
            echo "  bash auto-setup.sh dev --dry-run # Preview what would be done"
            echo ""
            exit 0
            ;;
        --version)
            echo "Auto-setup script version: $SCRIPT_VERSION"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Project directories
SERVER_DIR="server"
CLIENT_DIR="client"
SHARED_DIR="shared"

# Detect OS
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    OS="windows"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
else
    OS="linux"
fi

echo -e "${BLUE}🚀 Quester Project Auto-Setup${NC}"
echo -e "${CYAN}Version: $SCRIPT_VERSION${NC}"
echo -e "${YELLOW}Target Environment: $ENVIRONMENT${NC}"
echo -e "${YELLOW}Detected OS: $OS${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to print status messages
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "success" ]; then
        echo -e "${GREEN}✓${NC} $message"
    elif [ "$status" = "warning" ]; then
        echo -e "${YELLOW}⚠${NC} $message"
    elif [ "$status" = "error" ]; then
        echo -e "${RED}✗${NC} $message"
    elif [ "$status" = "skip" ]; then
        echo -e "${CYAN}↷${NC} $message"
    else
        echo -e "${BLUE}ℹ${NC} $message"
    fi
}

# Function to validate environment before starting
validate_environment() {
    local errors=()
    
    # Check if we're in the correct directory
    if [ ! -f "auto-setup.sh" ] && [ ! -f "auto-setup-new.sh" ]; then
        errors+=("Script must be run from the project root directory")
    fi
    
    # Check for required commands
    local required_commands=("docker" "git")
    for cmd in "${required_commands[@]}"; do
        if ! command_exists "$cmd"; then
            errors+=("Required command '$cmd' not found")
        fi
    done
    
    # Check if valid environment
    case "$ENVIRONMENT" in
        dev|staging|prod) ;;
        *) errors+=("Invalid environment '$ENVIRONMENT'. Must be one of: dev, staging, prod") ;;
    esac
    
    # Print errors if any
    if [ ${#errors[@]} -gt 0 ]; then
        echo -e "${RED}Validation errors:${NC}"
        for error in "${errors[@]}"; do
            echo -e "${RED}  • $error${NC}"
        done
        exit 1
    fi
}

# Function to check and install dependencies
check_dependencies() {
    echo -e "${BLUE}📋 Checking system dependencies...${NC}"
    
    local missing_deps=()
    
    # Check Docker
    if command_exists docker; then
        local docker_version=$(docker --version 2>/dev/null | cut -d' ' -f3 | cut -d',' -f1)
        print_status "success" "Docker is installed (version: $docker_version)"
    else
        missing_deps+=("docker")
    fi
    
    # Check Docker Compose
    if docker compose version >/dev/null 2>&1; then
        print_status "success" "Docker Compose is available"
    elif command_exists docker-compose; then
        print_status "success" "Docker Compose (legacy) is available"
    else
        missing_deps+=("docker-compose")
    fi
    
    # Check Git
    if command_exists git; then
        print_status "success" "Git is installed"
    else
        missing_deps+=("git")
    fi
    
    # Check Dart SDK
    if command_exists dart; then
        local dart_version=$(dart --version 2>&1 | head -n1 | cut -d' ' -f4)
        print_status "success" "Dart SDK is installed (version: $dart_version)"
    else
        missing_deps+=("dart")
    fi
    
    # Check Flutter
    if command_exists flutter; then
        local flutter_version=$(flutter --version 2>/dev/null | head -n1 | cut -d' ' -f2)
        print_status "success" "Flutter is installed (version: $flutter_version)"
    else
        missing_deps+=("flutter")
    fi
    
    # Check Node.js (optional for some tools)
    if command_exists node; then
        local node_version=$(node --version)
        print_status "success" "Node.js is installed ($node_version)"
    else
        print_status "warning" "Node.js is not installed (optional)"
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}Missing dependencies: ${missing_deps[*]}${NC}"
        echo -e "${YELLOW}Please install missing dependencies and run the script again.${NC}"
        exit 1
    fi
}

# Function to setup project structure
setup_project_structure() {
    echo -e "${BLUE}📁 Setting up project structure...${NC}"
    
    # Create backup directory
    if [ ! -d "$BACKUP_DIR" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would create backup directory: $BACKUP_DIR"
        else
            mkdir -p "$BACKUP_DIR"
            print_status "success" "Created backup directory"
        fi
    else
        print_status "skip" "Backup directory already exists"
    fi
    
    # Create main project directories if they don't exist
    local project_dirs=("app/k8s" "app/load-tests" "app/monitoring" "app/logs/nginx" "app/logs/postgres" "app/logs/server")
    
    for dir in "${project_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            if [ "$DRY_RUN" = true ]; then
                print_status "info" "[DRY-RUN] Would create directory: $dir"
            else
                mkdir -p "$dir"
                print_status "success" "Created directory: $dir"
            fi
        else
            print_status "skip" "Directory already exists: $dir"
        fi
    done
    
    # Create environment-specific directories
    if [[ "$ENVIRONMENT" == "staging" || "$ENVIRONMENT" == "prod" ]]; then
        local monitoring_dirs=(
            "$APP_DIR/monitoring/grafana/dashboards"
            "$APP_DIR/monitoring/grafana/datasources"
            "$APP_DIR/monitoring/prometheus"
            "app/monitoring/grafana/dashboards"
            "app/monitoring/grafana/datasources"
            "app/monitoring/prometheus"
        )
        
        for dir in "${monitoring_dirs[@]}"; do
            if [ ! -d "$dir" ]; then
                if [ "$DRY_RUN" = true ]; then
                    print_status "info" "[DRY-RUN] Would create monitoring directory: $dir"
                else
                    mkdir -p "$dir"
                    print_status "success" "Created monitoring directory: $dir"
                fi
            else
                print_status "skip" "Monitoring directory already exists: $dir"
            fi
        done
    fi
    
    # Create SSL directory for production
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        if [ ! -d "$APP_DIR/nginx/ssl" ]; then
            if [ "$DRY_RUN" = true ]; then
                print_status "info" "[DRY-RUN] Would create SSL certificate directory"
            else
                mkdir -p "$APP_DIR/nginx/ssl"
                print_status "success" "Created SSL certificate directory"
            fi
        else
            print_status "skip" "SSL certificate directory already exists"
        fi
    fi
}

# Function to create project templates
create_project_templates() {
    echo -e "${BLUE}🏗️  Creating project templates...${NC}"
    
    # Create shared package first
    if [ ! -f "$SHARED_DIR/pubspec.yaml" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would create shared Dart package"
        else
            print_status "info" "Creating shared Dart package..."
            if command_exists dart; then
                dart create -t package "$SHARED_DIR" --force
                print_status "success" "Created shared Dart package"
            else
                print_status "error" "Dart not available - cannot create shared package"
            fi
        fi
    else
        print_status "skip" "Shared package already exists"
    fi
    
    # Create server application with Shelf
    if [ ! -f "$SERVER_DIR/pubspec.yaml" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would create Dart server application"
        else
            print_status "info" "Creating Dart server application with Shelf..."
            if command_exists dart; then
                dart create -t server-shelf "$SERVER_DIR" --force
                
                # Create the correct server.dart with health endpoint
                if [ -f "$SERVER_DIR/bin/server.dart" ]; then
                    cat > "$SERVER_DIR/bin/server.dart" <<'EOF'
import 'dart:io';

import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_router/shelf_router.dart';

// Configure routes.
final _router = Router()
  ..get('/', _rootHandler)
  ..get('/health', _healthHandler)
  ..get('/echo/<message>', _echoHandler);

Response _rootHandler(Request req) {
  return Response.ok('Hello, World!\n');
}

Response _healthHandler(Request req) {
  return Response.ok(
    '{"status":"healthy","timestamp":"${DateTime.now().toIso8601String()}"}',
    headers: {'content-type': 'application/json'},
  );
}

Response _echoHandler(Request request) {
  final message = request.params['message'];
  return Response.ok('$message\n');
}

void main(List<String> args) async {
  // Use any available host or container IP (usually `0.0.0.0`).
  final ip = InternetAddress.anyIPv4;

  // Configure a pipeline that logs requests.
  final handler = Pipeline()
      .addMiddleware(logRequests())
      .addHandler(_router.call);

  // For running in containers, we respect the PORT environment variable.
  final port = int.parse(Platform.environment['PORT'] ?? '8080');
  final server = await serve(handler, ip, port);
  print('Server listening on port ${server.port}');
}
EOF
                fi
                
                print_status "success" "Created Dart server application with Shelf and health endpoint"
            else
                print_status "error" "Dart not available - cannot create server application"
            fi
        fi
    else
        print_status "skip" "Server application already exists"
    fi
    
    # Create Flutter client application
    if [ ! -f "$CLIENT_DIR/pubspec.yaml" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would create Flutter client application"
        else
            print_status "info" "Creating Flutter client application..."
            if command_exists flutter; then
                flutter create "$CLIENT_DIR" --platforms=web,android,ios --org=com.quester --project-name=quester_client
                print_status "success" "Created Flutter client application"
            else
                print_status "error" "Flutter not available - cannot create client application"
            fi
        fi
    else
        print_status "skip" "Client application already exists"
    fi
}

# Function to check and create Docker configuration files
create_docker_config() {
    echo -e "${BLUE}🐳 Creating Docker configuration files...${NC}"
    
    cd "$APP_DIR"
    
    # List of Docker files to check
    local docker_files=(
        "docker-compose.base.yml"
        "docker-compose.dev.yml"
        "docker-compose.staging.yml"  
        "docker-compose.prod.yml"
        "server.dev.dockerfile"
        "server.staging.dockerfile"
        "server.prod.dockerfile"
        "client.dev.dockerfile"
        "client.staging.dockerfile"
        "client.prod.dockerfile"
    )
    
    # Check which files already exist
    local existing_files=()
    local missing_files=()
    
    for file in "${docker_files[@]}"; do
        if [ -f "$file" ]; then
            existing_files+=("$file")
        else
            missing_files+=("$file")
        fi
    done
    
    # Show status of existing vs missing files
    if [ ${#existing_files[@]} -gt 0 ]; then
        print_status "info" "Found existing Docker files: ${#existing_files[@]}/${#docker_files[@]}"
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_status "info" "Missing Docker files: ${#missing_files[@]}/${#docker_files[@]}"
    fi
    
    # If some files exist, ask user what to do
    if [ ${#existing_files[@]} -gt 0 ] && [ ${#missing_files[@]} -gt 0 ]; then
        print_status "warning" "Some Docker files exist, some are missing"
        if [ "$DRY_RUN" = false ]; then
            echo "Options:"
            echo "1) Create only missing files (recommended)"
            echo "2) Recreate all files"
            echo "3) Skip Docker configuration"
            read -p "Choose option (1-3): " -n 1 -r choice
            echo
            case $choice in
                1) create_missing_docker_files "${missing_files[@]}" ;;
                2) create_all_docker_files "${docker_files[@]}" ;;
                3) print_status "skip" "Skipping Docker configuration" ;;
                *) print_status "info" "Invalid choice, creating only missing files"
                   create_missing_docker_files "${missing_files[@]}" ;;
            esac
        else
            print_status "info" "[DRY-RUN] Would create missing Docker files: ${missing_files[*]}"
        fi
    elif [ ${#missing_files[@]} -eq ${#docker_files[@]} ]; then
        # No files exist, create all
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would create all Docker configuration files"
        else
            print_status "info" "Creating all Docker configuration files..."
            create_all_docker_files "${docker_files[@]}"
        fi
    elif [ ${#missing_files[@]} -eq 0 ]; then
        # All files exist
        print_status "skip" "All Docker configuration files already exist"
        if [ "$DRY_RUN" = false ]; then
            read -p "Do you want to recreate them? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                create_all_docker_files "${docker_files[@]}"
            fi
        fi
    fi
    
    cd ..
}

# Function to create missing Docker files only
create_missing_docker_files() {
    local files=("$@")
    print_status "info" "Creating ${#files[@]} missing Docker files..."
    
    for file in "${files[@]}"; do
        case "$file" in
            "docker-compose.base.yml") create_compose_base ;;
            "docker-compose.dev.yml") create_compose_dev ;;
            "docker-compose.staging.yml") create_compose_staging ;;
            "docker-compose.prod.yml") create_compose_prod ;;
            "server.dev.dockerfile") create_server_dev_dockerfile ;;
            "server.staging.dockerfile") create_server_staging_dockerfile ;;
            "server.prod.dockerfile") create_server_prod_dockerfile ;;
            "client.dev.dockerfile") create_client_dev_dockerfile ;;
            "client.staging.dockerfile") create_client_staging_dockerfile ;;
            "client.prod.dockerfile") create_client_prod_dockerfile ;;
        esac
    done
    
    # Create additional config files if directories don't exist
    create_additional_config_files
    print_status "success" "Created missing Docker configuration files"
}

# Function to create all Docker files
create_all_docker_files() {
    print_status "info" "Creating all Docker configuration files..."
    
    create_compose_base
    create_compose_dev
    create_compose_staging
    create_compose_prod
    create_server_dev_dockerfile
    create_server_staging_dockerfile
    create_server_prod_dockerfile
    create_client_dev_dockerfile
    create_client_staging_dockerfile
    create_client_prod_dockerfile
    create_additional_config_files
    
    print_status "success" "Created all Docker configuration files"
}

# Individual file creation functions
create_compose_base() {
    cat > docker-compose.base.yml <<'EOF'
# Base Docker Compose configuration for Quester
# This file contains common service definitions used across all environments

services:
  postgres:
    image: postgres:16
    platform: linux/amd64
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-quester}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-questerpass}
      POSTGRES_DB: ${POSTGRES_DB:-questerdb}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      PGUSER: ${POSTGRES_USER:-quester}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-quester} -d ${POSTGRES_DB:-questerdb}"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - quester-network

  redis:
    image: redis:7
    platform: linux/amd64
    restart: unless-stopped
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - quester-network

  server:
    build:
      context: ..
      dockerfile: app/server.${ENVIRONMENT:-dev}.dockerfile
      target: ${BUILD_TARGET:-development}
    restart: unless-stopped
    environment:
      - DART_ENV=${DART_ENV:-development}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-quester}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB:-questerdb}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - SERVER_HOST=${SERVER_HOST:-0.0.0.0}
      - PORT=${SERVER_PORT:-8080}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - quester-network

  client:
    build:
      context: ..
      dockerfile: app/client.${ENVIRONMENT:-dev}.dockerfile
      target: ${BUILD_TARGET:-development}
    restart: unless-stopped
    environment:
      - FLUTTER_WEB=${FLUTTER_WEB:-true}
      - API_BASE_URL=${API_BASE_URL:-http://localhost:8080}
      - WS_BASE_URL=${WS_BASE_URL:-ws://localhost:8080}
      - CLIENT_HOST=${CLIENT_HOST:-0.0.0.0}
      - CLIENT_PORT=${CLIENT_PORT:-3000}
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - quester-network

networks:
  quester-network:
    driver: bridge
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-network

volumes:
  postgres_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-postgres-data
  redis_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-redis-data
EOF
}

create_compose_dev() {
    cat > docker-compose.dev.yml <<'EOF'
# Development environment overrides for Quester
# Includes development tools and services

services:
  postgres:
    ports:
      - "${POSTGRES_EXTERNAL_PORT:-5432}:5432"

  redis:
    ports:
      - "${REDIS_EXTERNAL_PORT:-6379}:6379"

  server:
    volumes:
      - ../server:/app/server:cached
      - ../shared:/app/shared:cached
      - server_cache:/app/server/.dart_tool
    ports:
      - "${SERVER_EXTERNAL_PORT:-8080}:8080"
      - "${SERVER_DEBUG_PORT:-9229}:9229"
    working_dir: /app/server
    command: ["dart", "run", "--enable-vm-service=0.0.0.0:9229", "bin/server.dart"]

  client:
    volumes:
      - ../client:/app/client:cached
      - ../shared:/app/shared:cached
      - client_cache:/app/client/.dart_tool
      - client_build:/app/client/build
    ports:
      - "${CLIENT_EXTERNAL_PORT:-3000}:3000"
    working_dir: /app/client
    command: ["flutter", "run", "-d", "web-server", "--web-hostname", "0.0.0.0", "--web-port", "3000"]

  nginx:
    image: nginx:latest
    platform: linux/amd64
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    ports:
      - "${NGINX_EXTERNAL_PORT:-80}:80"
    depends_on:
      - server
      - client
    networks:
      - quester-network

  pgadmin:
    image: dpage/pgadmin4:latest
    platform: linux/amd64
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD:-adminpass}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "${PGADMIN_EXTERNAL_PORT:-5050}:80"
    depends_on:
      - postgres
    networks:
      - quester-network

  redis-commander:
    image: node:18-slim
    platform: linux/amd64
    restart: unless-stopped
    working_dir: /app
    command: >
      sh -c "npm install -g redis-commander &&
             redis-commander --redis-host redis --redis-port 6379 --redis-password $$REDIS_PASSWORD --port 8081 --address 0.0.0.0"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redispass}
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    depends_on:
      - redis
    networks:
      - quester-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mailhog:
    image: mailhog/mailhog:latest
    platform: linux/amd64
    restart: unless-stopped
    ports:
      - "${MAILHOG_WEB_PORT:-8025}:8025"
      - "${MAILHOG_SMTP_PORT:-1025}:1025"
    networks:
      - quester-network

  minio:
    image: bitnami/minio:latest
    platform: linux/amd64
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-quester}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-questerpass}
      MINIO_DEFAULT_BUCKETS: quester-dev
    volumes:
      - minio_data:/bitnami/minio/data
    ports:
      - "${MINIO_API_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    networks:
      - quester-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 120s

volumes:
  pgadmin_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-pgadmin-data
  minio_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-minio-data
  server_cache:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-server-cache
  client_cache:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-client-cache
  client_build:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-client-build
EOF
}

create_compose_staging() {
    cat > docker-compose.staging.yml <<'EOF'
# Staging environment configuration for Quester
# Includes monitoring and logging

services:
  server:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  client:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  nginx:
    image: nginx:latest
    platform: linux/amd64
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    ports:
      - "${NGINX_EXTERNAL_PORT:-80}:80"
    depends_on:
      - server
      - client
    networks:
      - quester-network

volumes:
  prometheus_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-staging}-prometheus-data
  grafana_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-staging}-grafana-data
EOF
}

create_compose_prod() {
    cat > docker-compose.prod.yml <<'EOF'
# Production environment configuration for Quester
# Optimized for performance and security

services:
  nginx:
    image: nginx:latest
    platform: linux/amd64
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - server
      - client
    networks:
      - quester-network

  server:
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '1.0'
          memory: 512M

  client:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
EOF
}

# Dockerfile creation functions
create_server_dev_dockerfile() {
    cat > server.dev.dockerfile <<'EOF'
# Development Dockerfile for Quester Server
FROM dart:stable AS development

RUN apt-get update && apt-get install -y wget curl git && rm -rf /var/lib/apt/lists/*

WORKDIR /app
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY server/ ./server/

# Set working directory to server and install dependencies
WORKDIR /app/server
RUN dart pub get

RUN groupadd -r quester && useradd -r -g quester quester -m
RUN chown -R quester:quester /app

EXPOSE 8080 9229

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

CMD ["dart", "run", "--enable-vm-service=0.0.0.0:9229", "bin/server.dart"]
EOF
}

create_server_staging_dockerfile() {
    cat > server.staging.dockerfile <<'EOF'
# Staging Dockerfile for Quester Server
FROM dart:stable AS build

WORKDIR /app
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY server/ ./server/

# Set working directory to server and build
WORKDIR /app/server
RUN dart pub get
RUN dart compile exe bin/server.dart -o bin/server

FROM debian:bookworm-slim AS staging
RUN apt-get update && apt-get install -y wget ca-certificates && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY --from=build /app/server/bin/server ./

RUN groupadd -r quester && useradd -r -g quester quester -m
RUN chown -R quester:quester /app
USER quester

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

CMD ["./server"]
EOF
}

create_server_prod_dockerfile() {
    cat > server.prod.dockerfile <<'EOF'
# Production Dockerfile for Quester Server
FROM dart:stable AS build

WORKDIR /app
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY server/ ./server/

# Set working directory to server and build
WORKDIR /app/server
RUN dart pub get
RUN dart compile exe bin/server.dart -o bin/server

FROM gcr.io/distroless/base-debian12 AS production
WORKDIR /app
COPY --from=build /app/server/bin/server ./

USER nonroot:nonroot
EXPOSE 8080

CMD ["./server"]
EOF
}

create_client_dev_dockerfile() {
    cat > client.dev.dockerfile <<'EOF'
# Development Dockerfile for Quester Client
FROM ghcr.io/cirruslabs/flutter:latest AS development

RUN apt-get update && apt-get install -y wget curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app
RUN flutter config --enable-web

# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY client/ ./client/

# Set working directory to client and install dependencies
WORKDIR /app/client
RUN flutter pub get

RUN groupadd -r quester && useradd -r -g quester quester -m
RUN chown -R quester:quester /app

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1

CMD ["flutter", "run", "-d", "web-server", "--web-hostname", "0.0.0.0", "--web-port", "3000"]
EOF
}

create_client_staging_dockerfile() {
    cat > client.staging.dockerfile <<'EOF'
# Staging Dockerfile for Quester Client
FROM ghcr.io/cirruslabs/flutter:latest AS build

WORKDIR /app
RUN flutter config --enable-web
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY client/ ./client/

# Set working directory to client and build
WORKDIR /app/client
RUN flutter pub get
RUN flutter build web --release --web-renderer html

FROM nginx:latest AS staging
COPY --from=build /app/client/build/web /usr/share/nginx/html

RUN groupadd -g 1001 quester && \
    useradd -u 1001 -g 1001 -d /usr/share/nginx/html -s /sbin/nologin quester && \
    chown -R quester:quester /usr/share/nginx/html

EXPOSE 3000

CMD ["nginx", "-g", "daemon off;"]
EOF
}

create_client_prod_dockerfile() {
    cat > client.prod.dockerfile <<'EOF'
# Production Dockerfile for Quester Client
FROM ghcr.io/cirruslabs/flutter:latest AS build

WORKDIR /app
RUN flutter config --enable-web
# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY client/ ./client/

# Set working directory to client and build
WORKDIR /app/client
RUN flutter pub get
RUN flutter build web --release --web-renderer canvaskit --dart-define=FLUTTER_WEB_USE_SKIA=true

FROM nginx:latest AS production
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*

COPY --from=build /app/client/build/web /usr/share/nginx/html

RUN groupadd -g 1001 quester && \
    useradd -u 1001 -g 1001 -d /usr/share/nginx/html -s /sbin/nologin quester && \
    chown -R quester:quester /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

USER quester
EXPOSE 3000

CMD ["nginx", "-g", "daemon off;"]
EOF
}

# Function to create additional configuration files
create_additional_config_files() {
    # Create nginx directory and config if they don't exist
    if [ ! -d "nginx" ]; then
        mkdir -p nginx/conf.d
    fi
    
    if [ ! -f "nginx/nginx.dev.conf" ]; then
        cat > nginx/nginx.dev.conf <<'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    upstream client {
        server client:3000;
    }
    
    upstream server {
        server server:8080;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/ {
            proxy_pass http://server/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location / {
            proxy_pass http://client/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF
    fi
    
    # Create Redis configuration
    if [ ! -d "redis" ]; then
        mkdir -p redis
    fi
    
    if [ ! -f "redis/redis.conf" ]; then
        cat > redis/redis.conf <<'EOF'
bind 0.0.0.0
port 6379
protected-mode yes
tcp-backlog 511
timeout 0
tcp-keepalive 300
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16
always-show-logo yes
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./
maxmemory 256mb
maxmemory-policy allkeys-lru
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
latency-monitor-threshold 0
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes
EOF
    fi
    
    # Create init scripts directory
    if [ ! -d "init-scripts" ]; then
        mkdir -p init-scripts
    fi
    
    if [ ! -f "init-scripts/01-init.sql" ]; then
        cat > init-scripts/01-init.sql <<'EOF'
-- Quester Database Initialization Script with Gamification Schema
-- Phase 5: Enterprise Features - Complete Database Schema
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

CREATE SCHEMA IF NOT EXISTS quester;

GRANT ALL PRIVILEGES ON SCHEMA quester TO quester;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA quester TO quester;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA quester TO quester;

-- Basic health check table
CREATE TABLE IF NOT EXISTS quester.health_check (
    id SERIAL PRIMARY KEY,
    status VARCHAR(50) NOT NULL DEFAULT 'healthy',
    last_check TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Gamification System Tables (Phase 5)
CREATE TABLE IF NOT EXISTS quester.user_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    total_points INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    role VARCHAR(50) DEFAULT 'Novice',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS quester.achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    rarity VARCHAR(20) DEFAULT 'Common',
    points_reward INTEGER DEFAULT 0,
    icon_url VARCHAR(255),
    requirements JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS quester.user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    achievement_id UUID REFERENCES quester.achievements(id),
    earned_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    points_awarded INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS quester.leaderboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    leaderboard_type VARCHAR(50) NOT NULL,
    score INTEGER DEFAULT 0,
    rank INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS quester.activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    points_earned INTEGER DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enterprise System Tables (Phase 5)
CREATE TABLE IF NOT EXISTS quester.organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS quester.audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    organization_id UUID,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO quester.health_check (status) VALUES ('healthy') ON CONFLICT DO NOTHING;

-- Insert sample achievements
INSERT INTO quester.achievements (name, description, category, rarity, points_reward) VALUES 
    ('First Quest', 'Complete your first quest', 'Quest Completion', 'Common', 50),
    ('Team Player', 'Collaborate on 5 quests', 'Collaboration', 'Uncommon', 150),
    ('Streak Master', 'Maintain a 7-day activity streak', 'Consistency', 'Rare', 400)
ON CONFLICT DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_points_user_id ON quester.user_points(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON quester.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_leaderboards_type_rank ON quester.leaderboards(leaderboard_type, rank);
CREATE INDEX IF NOT EXISTS idx_activity_log_user_date ON quester.activity_log(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_audit_log_org_date ON quester.audit_log(organization_id, created_at);

DO $$
BEGIN
    RAISE NOTICE 'Quester database with gamification and enterprise features initialized successfully';
END $$;
EOF
    fi
}

# Function to setup Flutter/Dart dependencies
setup_flutter_dart() {
    echo -e "${BLUE}📦 Setting up Flutter/Dart dependencies...${NC}"
    
    # Setup shared package first with build_runner for code generation
    if [ -d "$SHARED_DIR" ] && [ -f "$SHARED_DIR/pubspec.yaml" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would install shared package dependencies and generate code"
        else
            print_status "info" "Installing shared package dependencies and generating code..."
            cd "$SHARED_DIR"
            dart pub get
            if dart pub deps | grep -q "build_runner"; then
                dart pub run build_runner build --delete-conflicting-outputs
                print_status "success" "Generated shared package models and serialization code"
            fi
            cd ..
            print_status "success" "Shared package dependencies installed with code generation"
        fi
    fi
    
    # Setup server dependencies
    if [ -d "$SERVER_DIR" ] && [ -f "$SERVER_DIR/pubspec.yaml" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would install server dependencies"
        else
            print_status "info" "Installing server dependencies..."
            cd "$SERVER_DIR"
            dart pub get
            cd ..
            print_status "success" "Server dependencies installed"
        fi
    fi
    
    # Setup client dependencies
    if [ -d "$CLIENT_DIR" ] && [ -f "$CLIENT_DIR/pubspec.yaml" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would install client dependencies"
        else
            print_status "info" "Installing client dependencies..."
            cd "$CLIENT_DIR"
            flutter pub get
            cd ..
            print_status "success" "Client dependencies installed"
        fi
    fi
}

# Function to setup environment files
setup_environment() {
    echo -e "${BLUE}⚙️  Setting up environment configuration...${NC}"
    
    cd "$APP_DIR"
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ "$DRY_RUN" = true ]; then
            print_status "info" "[DRY-RUN] Would create .env file"
        else
            print_status "info" "Creating .env file with secure passwords..."
            
            # Generate secure passwords
            local postgres_password redis_password jwt_secret grafana_password minio_password
            postgres_password=$(openssl rand -base64 32 | tr -d "=+/\n" | cut -c1-25)
            redis_password=$(openssl rand -base64 32 | tr -d "=+/\n" | cut -c1-25)
            jwt_secret=$(openssl rand -base64 64 | tr -d "=+/\n" | cut -c1-50)
            grafana_password=$(openssl rand -base64 16 | tr -d "=+/\n" | cut -c1-12)
            minio_password=$(openssl rand -base64 20 | tr -d "=+/\n" | cut -c1-15)
            
            cat > .env <<EOF
# Quester Environment Configuration - $ENVIRONMENT
# Generated on $(date)
ENVIRONMENT=$ENVIRONMENT
COMPOSE_PROJECT_NAME=$PROJECT_NAME-$ENVIRONMENT

# Database Configuration
POSTGRES_USER=quester
POSTGRES_PASSWORD=$postgres_password
POSTGRES_DB=questerdb
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_PASSWORD=$redis_password
REDIS_HOST=redis
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=$jwt_secret

# Server Configuration
DART_ENV=$ENVIRONMENT
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
LOG_LEVEL=$([[ "$ENVIRONMENT" == "dev" ]] && echo "DEBUG" || echo "INFO")
DEBUG=$([[ "$ENVIRONMENT" == "dev" ]] && echo "true" || echo "false")

# Client Configuration
FLUTTER_WEB=true
CLIENT_HOST=0.0.0.0
CLIENT_PORT=3000
API_BASE_URL=http://localhost:8080
WS_BASE_URL=ws://localhost:8080

# Gamification Configuration (Phase 5)
GAMIFICATION_ENABLED=true
ACHIEVEMENTS_CACHE_TTL=3600
LEADERBOARD_UPDATE_INTERVAL=300
POINTS_MULTIPLIER_ENABLED=true
STREAK_BONUS_ENABLED=true

# Enterprise Configuration (Phase 5)
ENTERPRISE_FEATURES_ENABLED=true
RBAC_ENABLED=true
AUDIT_LOG_ENABLED=true
API_RATE_LIMIT=1000
MULTI_TENANT_ENABLED=true
COMPLIANCE_MODE=standard

# External Port Configuration
POSTGRES_EXTERNAL_PORT=5432
REDIS_EXTERNAL_PORT=6379
SERVER_EXTERNAL_PORT=8080
CLIENT_EXTERNAL_PORT=3000
NGINX_EXTERNAL_PORT=80

# Development Tools (dev environment only)
PGADMIN_EXTERNAL_PORT=5050
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=adminpass
REDIS_COMMANDER_PORT=8081
MAILHOG_WEB_PORT=8025
MAILHOG_SMTP_PORT=1025
MINIO_API_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_ROOT_USER=quester
MINIO_ROOT_PASSWORD=$minio_password
SERVER_DEBUG_PORT=9229

# Monitoring (staging/prod)
GRAFANA_PASSWORD=$grafana_password
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Build Configuration
BUILD_TARGET=$([[ "$ENVIRONMENT" == "dev" ]] && echo "development" || echo "$ENVIRONMENT")
EOF
            print_status "success" "Environment file created with secure passwords"
        fi
    else
        print_status "skip" "Environment file already exists"
    fi
    
    cd ..
}

# Function to validate Docker configuration
validate_docker_config() {
    echo -e "${BLUE}🔍 Validating Docker configuration...${NC}"
    
    cd "$APP_DIR"
    
    # Check required Docker files
    local required_files=(
        "docker-compose.base.yml"
        "docker-compose.$ENVIRONMENT.yml"
        "server.$ENVIRONMENT.dockerfile"
        "client.$ENVIRONMENT.dockerfile"
    )
    
    local missing_files=()
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_status "warning" "Missing required Docker files: ${missing_files[*]}"
        print_status "info" "You'll need to create missing files before running containers"
    else
        print_status "success" "All required Docker files are present"
        
        # Validate compose files syntax if docker is available
        if command_exists docker; then
            if docker compose -f docker-compose.base.yml -f "docker-compose.$ENVIRONMENT.yml" config >/dev/null 2>&1; then
                print_status "success" "Docker Compose configuration is valid"
            else
                print_status "warning" "Docker Compose configuration has issues"
            fi
        fi
    fi
    
    cd ..
}

# Function to show final instructions
show_final_instructions() {
    echo ""
    echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo ""
    echo -e "${YELLOW}1. Start the development environment:${NC}"
    echo "   bash docker.sh $ENVIRONMENT start"
    echo ""
    echo -e "${YELLOW}2. Check service health:${NC}"
    echo "   bash docker.sh health"
    echo ""
    echo -e "${YELLOW}3. View logs:${NC}"
    echo "   bash docker.sh logs --follow"
    echo ""
    echo -e "${YELLOW}4. Access services:${NC}"
    echo "   • Client: http://localhost:3000"
    echo "   • Server API: http://localhost:8080"
    echo "   • Health Check: http://localhost:8080/health"
    
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        echo "   • pgAdmin: http://localhost:5050"
        echo "   • Redis Commander: http://localhost:8081"
        echo "   • MailHog: http://localhost:8025"
        echo "   • MinIO Console: http://localhost:9001"
    fi
    
    echo ""
    echo -e "${YELLOW}5. Useful commands:${NC}"
    echo "   bash docker.sh help           # Show all available commands"
    echo "   bash docker.sh backup         # Create database backup"
    echo "   bash docker.sh status         # Show detailed status"
    echo ""
}

# Function to generate setup summary
generate_setup_summary() {
    local summary_file="setup-summary-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$summary_file" <<EOF
QUESTER PROJECT SETUP SUMMARY
============================
Date: $(date)
Script Version: $SCRIPT_VERSION
Environment: $ENVIRONMENT
Project Directory: $(pwd)
Operating System: $OS

COMPONENTS STATUS:
- Shared Package: $([ -f "$SHARED_DIR/pubspec.yaml" ] && echo "✓ Exists" || echo "✗ Missing")
- Server Application: $([ -f "$SERVER_DIR/pubspec.yaml" ] && echo "✓ Exists" || echo "✗ Missing")
- Client Application: $([ -f "$CLIENT_DIR/pubspec.yaml" ] && echo "✓ Exists" || echo "✗ Missing")
- Docker Configuration: $([ -f "$APP_DIR/docker-compose.base.yml" ] && echo "✓ Exists" || echo "✗ Missing")
- Environment Config: $([ -f "$APP_DIR/.env" ] && echo "✓ Exists" || echo "✗ Missing")

PLATFORM COMPATIBILITY:
- Docker images configured for linux/amd64 platform
- Using non-Alpine images for better Windows compatibility
- PostgreSQL, Redis, and Nginx optimized for cross-platform deployment

NEXT STEPS:
1. bash docker.sh $ENVIRONMENT start
2. Open http://localhost:3000 for the client
3. Check http://localhost:8080/health for API status

For help: bash docker.sh help

TROUBLESHOOTING:
- If Redis Commander or MinIO fail, this is expected on some Windows systems
- Core services (PostgreSQL, Redis, Server, Client) should work correctly
- Use 'bash docker.sh health' to check service status
EOF
    
    print_status "info" "Setup summary saved to: $summary_file"
}

# Main execution flow
main() {
    # Check for dry-run mode
    if [ "$DRY_RUN" = true ]; then
        echo -e "${YELLOW}🔍 DRY-RUN MODE: Showing what would be done without making changes${NC}"
        echo ""
    fi
    
    # Validate environment before starting
    validate_environment
    
    # Check dependencies
    check_dependencies
    
    # Run setup steps
    setup_project_structure
    create_project_templates
    create_docker_config
    setup_flutter_dart
    setup_environment
    validate_docker_config
    
    # Show final instructions and generate summary
    if [ "$DRY_RUN" = false ]; then
        show_final_instructions
        generate_setup_summary
    else
        echo -e "${YELLOW}🔍 DRY-RUN completed. No changes were made.${NC}"
    fi
}

# ======================================================
# PLATFORM COMPATIBILITY & TROUBLESHOOTING GUIDE
# ======================================================
# 
# This script generates Docker configurations with platform
# compatibility fixes applied automatically.
# 
# Platform Compatibility Features:
# - PostgreSQL: Uses postgres:16 (non-Alpine) for cross-platform compatibility
# - Redis: Uses redis:7 (non-Alpine) to avoid 'exec format error' on Windows
# - Nginx: Uses nginx:latest (non-Alpine) for better Windows Docker support
# - Platform: linux/amd64 specified for consistent behavior across architectures
# - Docker Images: Non-Alpine variants for better compatibility
# 
# Known Platform Issues:
# - All services now work on Windows Docker Desktop
# - Redis Commander: Fixed using Node.js base image with npm installation
# - MinIO: Fixed using Bitnami image for better Windows platform support
# - Core Services: PostgreSQL, Redis, Server, Client, Redis Commander, MinIO all work reliably
# 
# Windows-Specific Notes:
# - Use bash/WSL2 environment for best compatibility
# - Enable Hyper-V and Containers Windows features
# - Ensure WSL2 backend is enabled in Docker Desktop
# - Allocate sufficient resources in Docker Desktop settings
# - If 'exec format error' occurs, the script includes linux/amd64 platform fixes
# 
# macOS-Specific Notes:
# - Allocate sufficient memory/CPU in Docker Desktop settings
# - Check file sharing permissions for bind mounts
# - Apple Silicon: Platform specifications ensure x86_64 compatibility
# 
# Linux-Specific Notes:
# - Manage Docker service: systemctl start/stop/restart docker
# - Check user groups: sudo usermod -aG docker $USER
# - Ensure sufficient disk space for containers and images
# 
# Version Information:
# - Script Version: 3.3.1
# - Last Updated: Fixed YAML syntax issues in Docker Compose templates
# - Compatible With: Docker 20.x+, Docker Compose 2.x+
# 
# For support, see the generated docker.sh script's help section.
# ======================================================

# Execute main function
main
