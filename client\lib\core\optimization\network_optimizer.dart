import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import '../performance/performance_monitor.dart';
import 'cache_manager.dart';

/// Network optimization and request management
class NetworkOptimizer {
  static final NetworkOptimizer _instance = NetworkOptimizer._internal();
  factory NetworkOptimizer() => _instance;
  NetworkOptimizer._internal();

  final http.Client _client = http.Client();
  final CacheManager _cache = CacheManager();
  final PerformanceMonitor _performance = PerformanceMonitor();
  final Connectivity _connectivity = Connectivity();
  
  final Map<String, Completer<http.Response>> _pendingRequests = {};
  final Map<String, Timer> _retryTimers = {};
  final List<QueuedRequest> _requestQueue = [];
  
  ConnectivityResult _connectionStatus = ConnectivityResult.none;
  bool _isOnline = true;
  
  static const int _maxRetries = 3;
  static const int _timeoutSeconds = 30;
  static const int _maxConcurrentRequests = 10;
  static const int _requestQueueLimit = 100;

  /// Initialize network optimizer
  Future<void> initialize() async {
    // Monitor connectivity changes
    _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
    
    // Check initial connectivity
    final results = await _connectivity.checkConnectivity();
    _connectionStatus = results.isNotEmpty ? results.first : ConnectivityResult.none;
    _isOnline = _connectionStatus != ConnectivityResult.none;
    
    // Process queued requests when online
    if (_isOnline) {
      _processRequestQueue();
    }
  }

  /// Optimized GET request with caching and retry logic
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    bool useCache = true,
    int? cacheTTL,
    int? timeoutSeconds,
    int? maxRetries,
  }) async {
    final requestKey = _generateRequestKey('GET', url, headers);
    
    // Check cache first
    if (useCache) {
      final cached = await _cache.get<String>('response_$requestKey');
      if (cached != null) {
        _performance.recordMetric('network_cache_hit', 1);
        return http.Response(cached, 200);
      }
    }

    // Check for pending identical request
    if (_pendingRequests.containsKey(requestKey)) {
      _performance.recordMetric('network_request_deduped', 1);
      return await _pendingRequests[requestKey]!.future;
    }

    return _executeRequest(
      () => _client.get(
        Uri.parse(url),
        headers: headers,
      ),
      requestKey: requestKey,
      useCache: useCache,
      cacheTTL: cacheTTL,
      timeoutSeconds: timeoutSeconds ?? _timeoutSeconds,
      maxRetries: maxRetries ?? _maxRetries,
    );
  }

  /// Optimized POST request
  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    int? timeoutSeconds,
    int? maxRetries,
  }) async {
    final requestKey = _generateRequestKey('POST', url, headers, body);
    
    return _executeRequest(
      () => _client.post(
        Uri.parse(url),
        headers: headers,
        body: body,
        encoding: encoding,
      ),
      requestKey: requestKey,
      useCache: false, // Don't cache POST requests
      timeoutSeconds: timeoutSeconds ?? _timeoutSeconds,
      maxRetries: maxRetries ?? _maxRetries,
    );
  }

  /// Optimized PUT request
  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    int? timeoutSeconds,
    int? maxRetries,
  }) async {
    final requestKey = _generateRequestKey('PUT', url, headers, body);
    
    return _executeRequest(
      () => _client.put(
        Uri.parse(url),
        headers: headers,
        body: body,
        encoding: encoding,
      ),
      requestKey: requestKey,
      useCache: false,
      timeoutSeconds: timeoutSeconds ?? _timeoutSeconds,
      maxRetries: maxRetries ?? _maxRetries,
    );
  }

  /// Optimized DELETE request
  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    int? timeoutSeconds,
    int? maxRetries,
  }) async {
    final requestKey = _generateRequestKey('DELETE', url, headers, body);
    
    return _executeRequest(
      () => _client.delete(
        Uri.parse(url),
        headers: headers,
        body: body,
        encoding: encoding,
      ),
      requestKey: requestKey,
      useCache: false,
      timeoutSeconds: timeoutSeconds ?? _timeoutSeconds,
      maxRetries: maxRetries ?? _maxRetries,
    );
  }

  /// Batch multiple requests
  Future<List<http.Response>> batch(List<Future<http.Response> Function()> requests) async {
    if (!_isOnline) {
      throw NetworkException('No internet connection');
    }

    _performance.startTimer('network_batch_request');
    
    try {
      // Limit concurrent requests
      final results = <http.Response>[];
      
      for (int i = 0; i < requests.length; i += _maxConcurrentRequests) {
        final batch = requests.skip(i).take(_maxConcurrentRequests);
        final batchResults = await Future.wait(
          batch.map((request) => request()),
          eagerError: false,
        );
        results.addAll(batchResults);
      }
      
      return results;
    } finally {
      _performance.stopTimer('network_batch_request');
    }
  }

  /// Queue request for later execution when online
  void queueRequest(QueuedRequest request) {
    if (_requestQueue.length >= _requestQueueLimit) {
      _requestQueue.removeAt(0); // Remove oldest request
    }
    
    _requestQueue.add(request);
    _performance.recordMetric('network_request_queued', 1);
  }

  /// Get network statistics
  NetworkStatistics getStatistics() {
    return NetworkStatistics(
      isOnline: _isOnline,
      connectionType: _connectionStatus,
      pendingRequests: _pendingRequests.length,
      queuedRequests: _requestQueue.length,
      cacheStatistics: _cache.getStatistics(),
    );
  }

  /// Clear network cache
  Future<void> clearCache() async {
    await _cache.clear();
  }

  /// Execute request with optimization features
  Future<http.Response> _executeRequest(
    Future<http.Response> Function() request, {
    required String requestKey,
    bool useCache = false,
    int? cacheTTL,
    required int timeoutSeconds,
    required int maxRetries,
  }) async {
    if (!_isOnline) {
      throw NetworkException('No internet connection');
    }

    final completer = Completer<http.Response>();
    _pendingRequests[requestKey] = completer;

    try {
      _performance.startTimer('network_request_$requestKey');
      
      final response = await _executeWithRetry(
        request,
        maxRetries: maxRetries,
        timeoutSeconds: timeoutSeconds,
      );

      // Cache successful GET responses
      if (useCache && response.statusCode == 200) {
        await _cache.set(
          'response_$requestKey',
          response.body,
          ttlSeconds: cacheTTL,
        );
      }

      _performance.recordMetric('network_request_success', 1);
      completer.complete(response);
      return response;
      
    } catch (error) {
      _performance.recordMetric('network_request_error', 1);
      completer.completeError(error);
      rethrow;
    } finally {
      _performance.stopTimer('network_request_$requestKey');
      _pendingRequests.remove(requestKey);
    }
  }

  /// Execute request with retry logic
  Future<http.Response> _executeWithRetry(
    Future<http.Response> Function() request, {
    required int maxRetries,
    required int timeoutSeconds,
  }) async {
    int attempts = 0;
    
    while (attempts <= maxRetries) {
      try {
        final response = await request().timeout(
          Duration(seconds: timeoutSeconds),
          onTimeout: () => throw TimeoutException('Request timeout', Duration(seconds: timeoutSeconds)),
        );
        
        // Consider 5xx errors as retryable
        if (response.statusCode >= 500 && attempts < maxRetries) {
          attempts++;
          await _delay(Duration(seconds: _calculateBackoffDelay(attempts)));
          continue;
        }
        
        return response;
        
      } on SocketException {
        if (attempts >= maxRetries) rethrow;
        attempts++;
        await _delay(Duration(seconds: _calculateBackoffDelay(attempts)));
      } on TimeoutException {
        if (attempts >= maxRetries) rethrow;
        attempts++;
        await _delay(Duration(seconds: _calculateBackoffDelay(attempts)));
      } catch (e) {
        rethrow; // Don't retry for other errors
      }
    }
    
    throw NetworkException('Max retries exceeded');
  }

  /// Calculate exponential backoff delay
  int _calculateBackoffDelay(int attempt) {
    return (1 << (attempt - 1)).clamp(1, 30); // 1, 2, 4, 8, 16, 30 seconds
  }

  /// Generate unique request key for deduplication
  String _generateRequestKey(String method, String url, Map<String, String>? headers, [Object? body]) {
    final keyData = {
      'method': method,
      'url': url,
      'headers': headers ?? {},
      'body': body?.toString() ?? '',
    };
    
    return base64Encode(utf8.encode(jsonEncode(keyData)));
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    final wasOnline = _isOnline;
    _connectionStatus = results.isNotEmpty ? results.first : ConnectivityResult.none;
    _isOnline = _connectionStatus != ConnectivityResult.none;
    
    _performance.logEvent('network_connectivity_changed', data: {
      'from': wasOnline ? 'online' : 'offline',
      'to': _isOnline ? 'online' : 'offline',
      'type': _connectionStatus.name,
    });

    if (!wasOnline && _isOnline) {
      // Came back online - process queued requests
      _processRequestQueue();
    }
  }

  /// Process queued requests when back online
  Future<void> _processRequestQueue() async {
    if (_requestQueue.isEmpty) return;
    
    _performance.startTimer('network_queue_processing');
    
    final requests = List<QueuedRequest>.from(_requestQueue);
    _requestQueue.clear();
    
    try {
      for (final queuedRequest in requests) {
        try {
          await queuedRequest.execute();
          _performance.recordMetric('network_queued_request_success', 1);
        } catch (e) {
          _performance.recordMetric('network_queued_request_error', 1);
          if (kDebugMode) {
            print('Failed to execute queued request: $e');
          }
        }
      }
    } finally {
      _performance.stopTimer('network_queue_processing');
    }
  }

  /// Delay helper
  Future<void> _delay(Duration duration) {
    return Future.delayed(duration);
  }

  /// Dispose resources
  void dispose() {
    _client.close();
    
    for (final timer in _retryTimers.values) {
      timer.cancel();
    }
    _retryTimers.clear();
    
    _pendingRequests.clear();
    _requestQueue.clear();
  }
}

/// Queued request for offline execution
class QueuedRequest {
  final String id;
  final Future<http.Response> Function() request;
  final DateTime createdAt;
  final int priority;

  QueuedRequest({
    required this.id,
    required this.request,
    required this.createdAt,
    this.priority = 0,
  });

  Future<http.Response> execute() => request();
}

/// Network statistics
class NetworkStatistics {
  final bool isOnline;
  final ConnectivityResult connectionType;
  final int pendingRequests;
  final int queuedRequests;
  final CacheStatistics cacheStatistics;

  const NetworkStatistics({
    required this.isOnline,
    required this.connectionType,
    required this.pendingRequests,
    required this.queuedRequests,
    required this.cacheStatistics,
  });

  Map<String, dynamic> toJson() => {
    'isOnline': isOnline,
    'connectionType': connectionType.name,
    'pendingRequests': pendingRequests,
    'queuedRequests': queuedRequests,
    'cacheStatistics': cacheStatistics.toJson(),
  };
}

/// Network exception
class NetworkException implements Exception {
  final String message;
  const NetworkException(this.message);
  
  @override
  String toString() => 'NetworkException: $message';
}
