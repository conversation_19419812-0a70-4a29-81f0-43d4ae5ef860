import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../core/user.dart';
import '../enterprise/organization_role.dart';

part 'auth_session.g.dart';

/// Authentication session with user and permissions
@JsonSerializable()
class AuthSession extends Equatable {
  /// Authentication token
  final String token;

  /// Refresh token for token renewal
  final String refreshToken;

  /// Token expiration timestamp
  final DateTime expiresAt;

  /// Authenticated user
  final User user;

  /// User's organization roles (for multi-tenant)
  final List<UserOrganizationRole> organizationRoles;

  /// Session metadata
  final Map<String, dynamic>? metadata;

  /// Session creation timestamp
  final DateTime createdAt;

  /// Last activity timestamp
  final DateTime lastActivityAt;

  const AuthSession({
    required this.token,
    required this.refreshToken,
    required this.expiresAt,
    required this.user,
    required this.organizationRoles,
    this.metadata,
    required this.createdAt,
    required this.lastActivityAt,
  });

  factory AuthSession.fromJson(Map<String, dynamic> json) => _$AuthSessionFromJson(json);
  Map<String, dynamic> toJson() => _$AuthSessionToJson(this);

  /// Check if token is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if token expires soon (within 5 minutes)
  bool get isExpiringSoon => DateTime.now().add(Duration(minutes: 5)).isAfter(expiresAt);

  /// Get all permissions across all organizations
  Set<Permission> get allPermissions {
    return organizationRoles
        .expand((role) => role.role.permissions)
        .toSet();
  }

  /// Check if user has permission in any organization
  bool hasPermission(Permission permission) {
    return organizationRoles.any((role) => role.role.hasPermission(permission));
  }

  /// Check if user has permission in specific organization
  bool hasPermissionInOrganization(String organizationId, Permission permission) {
    return organizationRoles
        .where((role) => role.organizationId == organizationId)
        .any((role) => role.role.hasPermission(permission));
  }

  /// Check if user is admin in any organization
  bool get isAdmin {
    return organizationRoles.any((role) => role.role.isAdmin);
  }

  /// Check if user is system admin (has system permissions)
  bool get isSystemAdmin {
    return hasPermission(Permission.systemMaintenance) &&
           hasPermission(Permission.systemLogs);
  }

  /// Get organizations where user has admin access
  List<String> get adminOrganizations {
    return organizationRoles
        .where((role) => role.role.isAdmin)
        .map((role) => role.organizationId)
        .toList();
  }

  /// Create updated session with new activity timestamp
  AuthSession updateActivity() {
    return copyWith(lastActivityAt: DateTime.now());
  }

  AuthSession copyWith({
    String? token,
    String? refreshToken,
    DateTime? expiresAt,
    User? user,
    List<UserOrganizationRole>? organizationRoles,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? lastActivityAt,
  }) {
    return AuthSession(
      token: token ?? this.token,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      user: user ?? this.user,
      organizationRoles: organizationRoles ?? this.organizationRoles,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      lastActivityAt: lastActivityAt ?? this.lastActivityAt,
    );
  }

  @override
  List<Object?> get props => [
        token,
        refreshToken,
        expiresAt,
        user,
        organizationRoles,
        metadata,
        createdAt,
        lastActivityAt,
      ];

  @override
  bool get stringify => true;
}

/// User's role within a specific organization
@JsonSerializable()
class UserOrganizationRole extends Equatable {
  /// Organization ID
  final String organizationId;

  /// Organization name
  final String organizationName;

  /// User's role in this organization
  final OrganizationRole role;

  /// Whether this is the user's default organization
  final bool isDefault;

  /// Role assignment timestamp
  final DateTime assignedAt;

  const UserOrganizationRole({
    required this.organizationId,
    required this.organizationName,
    required this.role,
    required this.isDefault,
    required this.assignedAt,
  });

  factory UserOrganizationRole.fromJson(Map<String, dynamic> json) => 
      _$UserOrganizationRoleFromJson(json);
  Map<String, dynamic> toJson() => _$UserOrganizationRoleToJson(this);

  @override
  List<Object?> get props => [
        organizationId,
        organizationName,
        role,
        isDefault,
        assignedAt,
      ];

  @override
  bool get stringify => true;
}

/// Authentication state enumeration
enum AuthSessionState {
  /// User is not authenticated
  unauthenticated,
  /// User is authenticated and active
  authenticated,
  /// Token has expired
  expired,
  /// Session is being refreshed
  refreshing,
  /// Authentication is in progress
  authenticating,
  /// User account is suspended
  suspended,
  /// User account requires verification
  pendingVerification,
}

/// OAuth provider types
enum OAuthProvider {
  @JsonValue('google')
  google,
  @JsonValue('microsoft')
  microsoft,
  @JsonValue('github')
  github,
  @JsonValue('slack')
  slack,
}

/// Two-factor authentication methods
enum TwoFactorMethod {
  @JsonValue('totp')
  totp, // Time-based One-Time Password (Google Authenticator, etc.)
  @JsonValue('sms')
  sms, // SMS verification
  @JsonValue('email')
  email, // Email verification
  @JsonValue('backup_codes')
  backupCodes, // Backup recovery codes
}