import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// A widget that builds different layouts based on screen size
/// Provides responsive design capabilities for mobile, tablet, and desktop
class ResponsiveBuilder extends StatelessWidget {
  /// Builder for mobile layout (< 600px)
  final Widget Function(BuildContext context) mobile;
  
  /// Builder for tablet layout (600px - 1024px)
  final Widget Function(BuildContext context)? tablet;
  
  /// Builder for desktop layout (> 1024px)
  final Widget Function(BuildContext context)? desktop;

  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= AppConstants.desktopBreakpoint) {
      return desktop?.call(context) ?? tablet?.call(context) ?? mobile(context);
    } else if (screenWidth >= AppConstants.tabletBreakpoint) {
      return tablet?.call(context) ?? mobile(context);
    } else {
      return mobile(context);
    }
  }
}

/// Extension to get responsive values based on screen size
extension ResponsiveExtension on BuildContext {
  /// Check if current screen is mobile
  bool get isMobile => MediaQuery.of(this).size.width < AppConstants.tabletBreakpoint;
  
  /// Check if current screen is tablet
  bool get isTablet => MediaQuery.of(this).size.width >= AppConstants.tabletBreakpoint && 
                      MediaQuery.of(this).size.width < AppConstants.desktopBreakpoint;
  
  /// Check if current screen is desktop
  bool get isDesktop => MediaQuery.of(this).size.width >= AppConstants.desktopBreakpoint;
  
  /// Get responsive value based on screen size
  T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop) {
      return desktop ?? tablet ?? mobile;
    } else if (isTablet) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
  
  /// Get responsive padding
  EdgeInsets get responsivePadding => EdgeInsets.all(
    responsive(
      mobile: AppConstants.defaultPadding,
      tablet: AppConstants.largePadding,
      desktop: AppConstants.extraLargePadding,
    ),
  );
  
  /// Get responsive horizontal padding
  EdgeInsets get responsiveHorizontalPadding => EdgeInsets.symmetric(
    horizontal: responsive(
      mobile: AppConstants.defaultPadding,
      tablet: AppConstants.largePadding,
      desktop: AppConstants.extraLargePadding,
    ),
  );
  
  /// Get responsive vertical padding
  EdgeInsets get responsiveVerticalPadding => EdgeInsets.symmetric(
    vertical: responsive(
      mobile: AppConstants.smallPadding,
      tablet: AppConstants.defaultPadding,
      desktop: AppConstants.largePadding,
    ),
  );
}

/// Responsive grid widget that adjusts columns based on screen size
class ResponsiveGrid extends StatelessWidget {
  /// List of widgets to display in grid
  final List<Widget> children;
  
  /// Number of columns for mobile
  final int mobileColumns;
  
  /// Number of columns for tablet
  final int tabletColumns;
  
  /// Number of columns for desktop
  final int desktopColumns;
  
  /// Spacing between grid items
  final double spacing;
  
  /// Aspect ratio of grid items
  final double childAspectRatio;
  
  /// Physics for the grid view
  final ScrollPhysics? physics;
  
  /// Whether the grid should shrink wrap
  final bool shrinkWrap;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = AppConstants.defaultPadding,
    this.childAspectRatio = 1.0,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final columns = context.responsive(
      mobile: mobileColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
    );

    // Performance optimization: Use const delegate when possible
    final delegate = SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: columns,
      crossAxisSpacing: spacing,
      mainAxisSpacing: spacing,
      childAspectRatio: childAspectRatio,
    );

    return GridView.builder(
      physics: physics,
      shrinkWrap: shrinkWrap,
      gridDelegate: delegate,
      itemCount: children.length,
      // Performance optimization: Use indexed access instead of closure
      itemBuilder: (context, index) {
        if (index >= children.length) return const SizedBox.shrink();
        return children[index];
      },
      // Performance optimization: Add cache extent for better scrolling
      cacheExtent: 250.0,
    );
  }
}

/// Responsive wrap widget that adjusts spacing based on screen size
class ResponsiveWrap extends StatelessWidget {
  /// List of widgets to wrap
  final List<Widget> children;
  
  /// Direction of the wrap
  final Axis direction;
  
  /// Alignment of children
  final WrapAlignment alignment;
  
  /// Cross axis alignment
  final WrapCrossAlignment crossAxisAlignment;

  const ResponsiveWrap({
    super.key,
    required this.children,
    this.direction = Axis.horizontal,
    this.alignment = WrapAlignment.start,
    this.crossAxisAlignment = WrapCrossAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    final spacing = context.responsive(
      mobile: AppConstants.smallPadding,
      tablet: AppConstants.defaultPadding,
      desktop: AppConstants.largePadding,
    );

    return Wrap(
      direction: direction,
      alignment: alignment,
      crossAxisAlignment: crossAxisAlignment,
      spacing: spacing,
      runSpacing: spacing,
      children: children,
    );
  }
}

/// Responsive container that adjusts its constraints based on screen size
class ResponsiveContainer extends StatelessWidget {
  /// Child widget
  final Widget child;
  
  /// Maximum width for mobile
  final double? mobileMaxWidth;
  
  /// Maximum width for tablet
  final double? tabletMaxWidth;
  
  /// Maximum width for desktop
  final double? desktopMaxWidth;
  
  /// Padding for the container
  final EdgeInsets? padding;
  
  /// Margin for the container
  final EdgeInsets? margin;
  
  /// Decoration for the container
  final Decoration? decoration;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.mobileMaxWidth,
    this.tabletMaxWidth,
    this.desktopMaxWidth,
    this.padding,
    this.margin,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final maxWidth = context.responsive<double?>(
      mobile: mobileMaxWidth,
      tablet: tabletMaxWidth,
      desktop: desktopMaxWidth,
    );

    return Container(
      constraints: maxWidth != null ? BoxConstraints(maxWidth: maxWidth) : null,
      padding: padding ?? context.responsivePadding,
      margin: margin,
      decoration: decoration,
      child: child,
    );
  }
}

/// Responsive text widget that adjusts font size based on screen size
class ResponsiveText extends StatelessWidget {
  /// Text to display
  final String text;
  
  /// Text style for mobile
  final TextStyle? mobileStyle;
  
  /// Text style for tablet
  final TextStyle? tabletStyle;
  
  /// Text style for desktop
  final TextStyle? desktopStyle;
  
  /// Text alignment
  final TextAlign? textAlign;
  
  /// Maximum number of lines
  final int? maxLines;
  
  /// Text overflow behavior
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.mobileStyle,
    this.tabletStyle,
    this.desktopStyle,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final style = context.responsive<TextStyle?>(
      mobile: mobileStyle,
      tablet: tabletStyle,
      desktop: desktopStyle,
    );

    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Responsive sized box that adjusts size based on screen size
class ResponsiveSizedBox extends StatelessWidget {
  /// Width for mobile
  final double? mobileWidth;
  
  /// Width for tablet
  final double? tabletWidth;
  
  /// Width for desktop
  final double? desktopWidth;
  
  /// Height for mobile
  final double? mobileHeight;
  
  /// Height for tablet
  final double? tabletHeight;
  
  /// Height for desktop
  final double? desktopHeight;
  
  /// Child widget
  final Widget? child;

  const ResponsiveSizedBox({
    super.key,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    final width = context.responsive<double?>(
      mobile: mobileWidth,
      tablet: tabletWidth,
      desktop: desktopWidth,
    );
    
    final height = context.responsive<double?>(
      mobile: mobileHeight,
      tablet: tabletHeight,
      desktop: desktopHeight,
    );

    return SizedBox(
      width: width,
      height: height,
      child: child,
    );
  }
}

/// Utility class for responsive breakpoints and calculations
class ResponsiveUtils {
  /// Get the current device type
  static DeviceType getDeviceType(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= AppConstants.desktopBreakpoint) {
      return DeviceType.desktop;
    } else if (screenWidth >= AppConstants.tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.mobile;
    }
  }
  
  /// Calculate responsive font size
  static double getResponsiveFontSize(BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return context.responsive(
      mobile: mobile,
      tablet: tablet ?? mobile * 1.1,
      desktop: desktop ?? mobile * 1.2,
    );
  }
  
  /// Calculate responsive icon size
  static double getResponsiveIconSize(BuildContext context, {
    double mobile = 24.0,
    double? tablet,
    double? desktop,
  }) {
    return context.responsive(
      mobile: mobile,
      tablet: tablet ?? mobile * 1.2,
      desktop: desktop ?? mobile * 1.4,
    );
  }
}

/// Device type enumeration
enum DeviceType {
  mobile,
  tablet,
  desktop,
}
