// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Task _$TaskFromJson(Map<String, dynamic> json) => Task(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  questId: json['questId'] as String?,
  createdById: json['createdById'] as String,
  assignedToId: json['assignedToId'] as String?,
  status: $enumDecode(_$TaskStatusEnumMap, json['status']),
  priority: $enumDecode(_$TaskPriorityEnumMap, json['priority']),
  complexity: $enumDecode(_$TaskComplexityEnumMap, json['complexity']),
  points: (json['points'] as num).toInt(),
  earnedPoints: (json['earnedPoints'] as num).toInt(),
  deadline: json['deadline'] == null
      ? null
      : DateTime.parse(json['deadline'] as String),
  estimatedMinutes: (json['estimatedMinutes'] as num?)?.toInt(),
  actualMinutes: (json['actualMinutes'] as num?)?.toInt(),
  dependencies: (json['dependencies'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
  attachments: (json['attachments'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  comments: (json['comments'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  startedAt: json['startedAt'] == null
      ? null
      : DateTime.parse(json['startedAt'] as String),
);

Map<String, dynamic> _$TaskToJson(Task instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'questId': instance.questId,
  'createdById': instance.createdById,
  'assignedToId': instance.assignedToId,
  'status': _$TaskStatusEnumMap[instance.status]!,
  'priority': _$TaskPriorityEnumMap[instance.priority]!,
  'complexity': _$TaskComplexityEnumMap[instance.complexity]!,
  'points': instance.points,
  'earnedPoints': instance.earnedPoints,
  'deadline': instance.deadline?.toIso8601String(),
  'estimatedMinutes': instance.estimatedMinutes,
  'actualMinutes': instance.actualMinutes,
  'dependencies': instance.dependencies,
  'tags': instance.tags,
  'attachments': instance.attachments,
  'comments': instance.comments,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'completedAt': instance.completedAt?.toIso8601String(),
  'startedAt': instance.startedAt?.toIso8601String(),
};

const _$TaskStatusEnumMap = {
  TaskStatus.todo: 'todo',
  TaskStatus.pending: 'pending',
  TaskStatus.inProgress: 'in_progress',
  TaskStatus.completed: 'completed',
  TaskStatus.cancelled: 'cancelled',
  TaskStatus.blocked: 'blocked',
};

const _$TaskPriorityEnumMap = {
  TaskPriority.low: 'low',
  TaskPriority.medium: 'medium',
  TaskPriority.high: 'high',
  TaskPriority.critical: 'critical',
};

const _$TaskComplexityEnumMap = {
  TaskComplexity.simple: 'simple',
  TaskComplexity.moderate: 'moderate',
  TaskComplexity.complex: 'complex',
  TaskComplexity.veryComplex: 'very_complex',
};
