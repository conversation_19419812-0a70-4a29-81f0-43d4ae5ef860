-- Advanced Analytics Schema for Quester Platform
-- This script creates comprehensive analytics tables for data collection,
-- metrics calculation, custom reports, and business intelligence
-- Version: 1.0 - Analytics System Implementation

-- Ensure required extensions are available
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create enums for analytics system
CREATE TYPE analytics_event_type AS ENUM (
    'page_view',
    'button_click', 
    'task_action',
    'quest_action',
    'achievement_unlock',
    'reward_purchase',
    'login',
    'logout',
    'search',
    'export',
    'report_generate',
    'dashboard_view',
    'collaboration_action',
    'gamification_interaction',
    'enterprise_action',
    'api_call'
);

CREATE TYPE report_status AS ENUM (
    'draft',
    'active',
    'archived',
    'scheduled',
    'generating',
    'completed',
    'failed'
);

CREATE TYPE export_format AS ENUM (
    'pdf',
    'excel',
    'csv',
    'json',
    'powerpoint'
);

CREATE TYPE export_status AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed',
    'cancelled'
);

CREATE TYPE metric_aggregation_type AS ENUM (
    'sum',
    'average',
    'count',
    'distinct_count',
    'min',
    'max',
    'median',
    'percentile'
);

CREATE TYPE time_period AS ENUM (
    'hourly',
    'daily', 
    'weekly',
    'monthly',
    'quarterly',
    'yearly'
);

-- Core analytics events table for raw data collection
CREATE TABLE analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    user_id UUID,
    session_id UUID,
    event_type analytics_event_type NOT NULL,
    event_name VARCHAR(200) NOT NULL,
    event_data JSONB NOT NULL DEFAULT '{}',
    event_properties JSONB DEFAULT '{}',
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    page_url TEXT,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Add constraints
    CONSTRAINT fk_analytics_events_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_analytics_events_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Optimized indexes for analytics queries
CREATE INDEX idx_analytics_events_org_timestamp 
    ON analytics_events(organization_id, timestamp DESC);
CREATE INDEX idx_analytics_events_user_timestamp 
    ON analytics_events(user_id, timestamp DESC) WHERE user_id IS NOT NULL;
CREATE INDEX idx_analytics_events_type_timestamp 
    ON analytics_events(event_type, timestamp DESC);
CREATE INDEX idx_analytics_events_session 
    ON analytics_events(session_id, timestamp) WHERE session_id IS NOT NULL;
CREATE INDEX idx_analytics_events_properties 
    ON analytics_events USING GIN(event_properties);
CREATE INDEX idx_analytics_events_data 
    ON analytics_events USING GIN(event_data);

-- Pre-aggregated metrics for fast dashboard queries
CREATE TABLE analytics_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_category VARCHAR(50) NOT NULL,
    metric_value DECIMAL(20,6) NOT NULL,
    metric_count INTEGER DEFAULT 0,
    aggregation_type metric_aggregation_type NOT NULL,
    time_period time_period NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    dimensions JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Add constraints
    CONSTRAINT fk_analytics_metrics_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT unique_metric_period 
        UNIQUE (organization_id, metric_name, metric_category, time_period, period_start)
);

-- Indexes for metrics table
CREATE INDEX idx_analytics_metrics_org_name 
    ON analytics_metrics(organization_id, metric_name);
CREATE INDEX idx_analytics_metrics_category_period 
    ON analytics_metrics(metric_category, time_period, period_start DESC);
CREATE INDEX idx_analytics_metrics_period_range 
    ON analytics_metrics(period_start, period_end);
CREATE INDEX idx_analytics_metrics_dimensions 
    ON analytics_metrics USING GIN(dimensions);

-- User behavior analytics for cohort and retention analysis
CREATE TABLE user_behavior_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    user_id UUID NOT NULL,
    analysis_date DATE NOT NULL,
    session_count INTEGER DEFAULT 0,
    page_views INTEGER DEFAULT 0,
    actions_count INTEGER DEFAULT 0,
    time_spent_seconds INTEGER DEFAULT 0,
    features_used TEXT[] DEFAULT '{}',
    engagement_score DECIMAL(5,2) DEFAULT 0.0,
    behavior_patterns JSONB DEFAULT '{}',
    cohort_data JSONB DEFAULT '{}',
    retention_metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Add constraints
    CONSTRAINT fk_user_behavior_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_behavior_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT unique_user_behavior_date 
        UNIQUE (user_id, analysis_date)
);

-- Indexes for user behavior analytics
CREATE INDEX idx_user_behavior_org_date 
    ON user_behavior_analytics(organization_id, analysis_date DESC);
CREATE INDEX idx_user_behavior_user_date 
    ON user_behavior_analytics(user_id, analysis_date DESC);
CREATE INDEX idx_user_behavior_engagement 
    ON user_behavior_analytics(organization_id, engagement_score DESC);
CREATE INDEX idx_user_behavior_patterns 
    ON user_behavior_analytics USING GIN(behavior_patterns);

-- Custom reports configuration and templates
CREATE TABLE custom_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    report_type VARCHAR(50) NOT NULL DEFAULT 'dashboard',
    query_config JSONB NOT NULL,
    visualization_config JSONB NOT NULL DEFAULT '{}',
    layout_config JSONB DEFAULT '{}',
    filter_config JSONB DEFAULT '{}',
    schedule_config JSONB DEFAULT '{}',
    permissions JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    status report_status DEFAULT 'draft',
    created_by UUID NOT NULL,
    updated_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Add constraints
    CONSTRAINT fk_custom_reports_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_reports_creator 
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_reports_updater 
        FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Indexes for custom reports
CREATE INDEX idx_custom_reports_org_status 
    ON custom_reports(organization_id, status);
CREATE INDEX idx_custom_reports_creator 
    ON custom_reports(created_by, created_at DESC);
CREATE INDEX idx_custom_reports_type 
    ON custom_reports(report_type, is_template);
CREATE INDEX idx_custom_reports_tags 
    ON custom_reports USING GIN(tags);
CREATE INDEX idx_custom_reports_name_search 
    ON custom_reports USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Report generation history and management
CREATE TABLE report_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    generated_by UUID,
    generation_started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    generation_completed_at TIMESTAMP WITH TIME ZONE,
    file_path VARCHAR(500),
    file_name VARCHAR(255),
    file_format export_format NOT NULL,
    file_size_bytes BIGINT,
    parameters JSONB DEFAULT '{}',
    status export_status DEFAULT 'pending',
    error_message TEXT,
    download_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Add constraints
    CONSTRAINT fk_report_history_report 
        FOREIGN KEY (report_id) REFERENCES custom_reports(id) ON DELETE CASCADE,
    CONSTRAINT fk_report_history_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_report_history_user 
        FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Indexes for report history
CREATE INDEX idx_report_history_report 
    ON report_history(report_id, generation_started_at DESC);
CREATE INDEX idx_report_history_org_status 
    ON report_history(organization_id, status, generation_started_at DESC);
CREATE INDEX idx_report_history_user 
    ON report_history(generated_by, generation_started_at DESC) WHERE generated_by IS NOT NULL;
CREATE INDEX idx_report_history_expires 
    ON report_history(expires_at) WHERE expires_at IS NOT NULL;

-- Data export requests and tracking
CREATE TABLE data_exports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    requested_by UUID NOT NULL,
    export_name VARCHAR(200) NOT NULL,
    export_type VARCHAR(50) NOT NULL,
    data_query JSONB NOT NULL,
    export_format export_format NOT NULL,
    filters JSONB DEFAULT '{}',
    date_range JSONB DEFAULT '{}',
    file_path VARCHAR(500),
    file_name VARCHAR(255),
    file_size_bytes BIGINT,
    record_count INTEGER,
    status export_status DEFAULT 'pending',
    progress_percentage INTEGER DEFAULT 0,
    error_message TEXT,
    download_token VARCHAR(255),
    download_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Add constraints
    CONSTRAINT fk_data_exports_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_data_exports_user 
        FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes for data exports
CREATE INDEX idx_data_exports_org_status 
    ON data_exports(organization_id, status, created_at DESC);
CREATE INDEX idx_data_exports_user 
    ON data_exports(requested_by, created_at DESC);
CREATE INDEX idx_data_exports_expires 
    ON data_exports(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_data_exports_token 
    ON data_exports(download_token) WHERE download_token IS NOT NULL;

-- Predictive analytics and insights storage
CREATE TABLE analytics_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    insight_type VARCHAR(100) NOT NULL,
    insight_category VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    insight_data JSONB NOT NULL,
    confidence_score DECIMAL(4,3), -- 0.000 to 1.000
    impact_level VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    action_recommendations JSONB DEFAULT '{}',
    related_metrics TEXT[] DEFAULT '{}',
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Add constraints
    CONSTRAINT fk_analytics_insights_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT chk_confidence_range 
        CHECK (confidence_score IS NULL OR (confidence_score >= 0.000 AND confidence_score <= 1.000)),
    CONSTRAINT chk_impact_level 
        CHECK (impact_level IN ('low', 'medium', 'high', 'critical'))
);

-- Indexes for analytics insights
CREATE INDEX idx_analytics_insights_org_active 
    ON analytics_insights(organization_id, is_active, created_at DESC);
CREATE INDEX idx_analytics_insights_type_category 
    ON analytics_insights(insight_type, insight_category);
CREATE INDEX idx_analytics_insights_impact 
    ON analytics_insights(organization_id, impact_level, confidence_score DESC NULLS LAST);
CREATE INDEX idx_analytics_insights_validity 
    ON analytics_insights(valid_from, valid_until) WHERE is_active = TRUE;

-- External integrations and webhooks configuration
CREATE TABLE analytics_integrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    integration_name VARCHAR(100) NOT NULL,
    integration_type VARCHAR(50) NOT NULL, -- webhook, api, bi_tool
    endpoint_url TEXT,
    api_key_hash VARCHAR(255),
    configuration JSONB NOT NULL DEFAULT '{}',
    authentication JSONB DEFAULT '{}',
    data_mapping JSONB DEFAULT '{}',
    sync_schedule VARCHAR(50), -- cron expression
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(20) DEFAULT 'active', -- active, paused, failed
    error_count INTEGER DEFAULT 0,
    last_error_message TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Add constraints
    CONSTRAINT fk_analytics_integrations_org 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_analytics_integrations_user 
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT unique_integration_name 
        UNIQUE (organization_id, integration_name)
);

-- Indexes for analytics integrations
CREATE INDEX idx_analytics_integrations_org 
    ON analytics_integrations(organization_id, is_active);
CREATE INDEX idx_analytics_integrations_type 
    ON analytics_integrations(integration_type, sync_status);
CREATE INDEX idx_analytics_integrations_sync 
    ON analytics_integrations(last_sync_at) WHERE sync_schedule IS NOT NULL;

-- Create views for common analytics queries
CREATE VIEW analytics_daily_summary AS
SELECT 
    organization_id,
    DATE(timestamp) AS summary_date,
    COUNT(*) AS total_events,
    COUNT(DISTINCT user_id) AS active_users,
    COUNT(DISTINCT session_id) AS sessions,
    COUNT(*) FILTER (WHERE event_type = 'task_action') AS task_actions,
    COUNT(*) FILTER (WHERE event_type = 'quest_action') AS quest_actions,
    COUNT(*) FILTER (WHERE event_type = 'achievement_unlock') AS achievement_unlocks,
    COUNT(*) FILTER (WHERE event_type = 'gamification_interaction') AS gamification_interactions
FROM analytics_events
WHERE user_id IS NOT NULL
GROUP BY organization_id, DATE(timestamp);

CREATE VIEW analytics_user_engagement AS
SELECT 
    uba.organization_id,
    uba.user_id,
    u.email,
    u.full_name,
    uba.analysis_date,
    uba.engagement_score,
    uba.session_count,
    uba.actions_count,
    uba.time_spent_seconds,
    CASE 
        WHEN uba.engagement_score >= 8.0 THEN 'Highly Engaged'
        WHEN uba.engagement_score >= 6.0 THEN 'Moderately Engaged'
        WHEN uba.engagement_score >= 4.0 THEN 'Lightly Engaged'
        ELSE 'At Risk'
    END AS engagement_category
FROM user_behavior_analytics uba
JOIN users u ON uba.user_id = u.id
WHERE uba.analysis_date >= CURRENT_DATE - INTERVAL '30 days';

-- Add comments for documentation
COMMENT ON TABLE analytics_events IS 'Core event tracking table for all user interactions and system events';
COMMENT ON TABLE analytics_metrics IS 'Pre-calculated metrics for fast dashboard queries and reporting';
COMMENT ON TABLE user_behavior_analytics IS 'Daily user behavior analysis for engagement and retention tracking';
COMMENT ON TABLE custom_reports IS 'User-created custom reports and dashboard configurations';
COMMENT ON TABLE report_history IS 'History of report generation requests and file management';
COMMENT ON TABLE data_exports IS 'Data export requests and file delivery tracking';
COMMENT ON TABLE analytics_insights IS 'AI-generated insights and recommendations based on analytics data';
COMMENT ON TABLE analytics_integrations IS 'External system integrations for data sync and webhooks';

-- Grant appropriate permissions (adjust based on your user roles)
-- These would typically be granted to the application database user
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO quester_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO quester_app;

-- Create initial indexes for performance optimization
-- Additional indexes may be added based on query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_events_timestamp_only 
    ON analytics_events(timestamp DESC);

-- Analytics schema setup complete
-- Version: 1.0
-- Tables created: 8
-- Indexes created: 25+
-- Views created: 2
-- Enums created: 6