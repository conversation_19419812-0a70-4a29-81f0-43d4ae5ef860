import 'package:http/http.dart' as http;
import 'package:shared/shared.dart';

import '../../core/services/api_service.dart';
import '../../core/models/api_response.dart' as client_api;
import '../../core/config/app_config.dart';

/// Remote data source for API calls
class RemoteDataSource {
  final http.Client client;
  final ApiService apiService;

  RemoteDataSource({
    required this.client,
    required this.apiService,
  });

  // Authentication API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> login(String email, String password) async {
    return apiService.post(
      AppConfig.loginEndpoint,
      body: {
        'email': email,
        'password': password,
      },
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> register(String email, String password, String name) async {
    return apiService.post(
      AppConfig.registerEndpoint,
      body: {
        'email': email,
        'password': password,
        'name': name,
      },
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> refreshToken(String refreshToken) async {
    return apiService.post(
      AppConfig.refreshTokenEndpoint,
      body: {
        'refresh_token': refreshToken,
      },
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> checkAuthStatus(String token) async {
    return apiService.get(
      '/auth/status',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateProfile(String userId, Map<String, dynamic> profileData, String token) async {
    return apiService.put(
      '/users/$userId/profile',
      token: token,
      body: profileData,
    );
  }

  // Gamification API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> getUserPoints(String userId, String token) async {
    return apiService.get(
      '${AppConfig.userPointsEndpoint}/$userId/points',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getUserStats(String userId, String token) async {
    return apiService.get(
      '${AppConfig.userPointsEndpoint}/$userId/stats',
      token: token,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getUserAchievements(String userId, String token) async {
    return apiService.get(
      '${AppConfig.userPointsEndpoint}/$userId/achievements',
      token: token,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getLeaderboard(String token) async {
    return apiService.get(
      AppConfig.leaderboardEndpoint,
      token: token,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getRewards(String token) async {
    return apiService.get(
      AppConfig.rewardsEndpoint,
      token: token,
    );
  }

  // Freelancing API calls
  Future<client_api.ApiResponse<List<dynamic>>> getProjects(String token, {Map<String, String>? filters}) async {
    return apiService.get(
      AppConfig.projectsEndpoint,
      token: token,
      queryParams: filters,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getProject(String projectId, String token) async {
    return apiService.get(
      '${AppConfig.projectsEndpoint}/$projectId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createProject(Map<String, dynamic> projectData, String token) async {
    return apiService.post(
      AppConfig.projectsEndpoint,
      token: token,
      body: projectData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getFreelancerProfile(String userId, String token) async {
    return apiService.get(
      '${AppConfig.freelancersEndpoint}/$userId/profile',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createFreelancerProfile(Map<String, dynamic> profileData, String token) async {
    return apiService.post(
      '${AppConfig.freelancersEndpoint}/profile',
      token: token,
      body: profileData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> submitProposal(Map<String, dynamic> proposalData, String token) async {
    return apiService.post(
      AppConfig.proposalsEndpoint,
      token: token,
      body: proposalData,
    );
  }

  // Learning API calls
  Future<client_api.ApiResponse<List<dynamic>>> getCourses(String token, {Map<String, String>? filters}) async {
    return apiService.get(
      AppConfig.coursesEndpoint,
      token: token,
      queryParams: filters,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getCourse(String courseId, String token) async {
    return apiService.get(
      '${AppConfig.coursesEndpoint}/$courseId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> enrollInCourse(String courseId, String userId, String token) async {
    return apiService.post(
      AppConfig.enrollmentsEndpoint,
      token: token,
      body: {
        'course_id': courseId,
        'user_id': userId,
      },
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getUserEnrollments(String userId, String token) async {
    return apiService.get(
      '${AppConfig.learningEndpoint}/users/$userId/enrollments',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateProgress(String userId, String courseId, Map<String, dynamic> progressData, String token) async {
    return apiService.post(
      '${AppConfig.learningEndpoint}/users/$userId/courses/$courseId/progress',
      token: token,
      body: progressData,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getUserCertificates(String userId, String token) async {
    return apiService.get(
      '${AppConfig.learningEndpoint}/users/$userId/certificates',
      token: token,
    );
  }

  // Analytics API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> trackEvent(Map<String, dynamic> eventData, String token) async {
    return apiService.post(
      AppConfig.eventsEndpoint,
      token: token,
      body: eventData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getDashboard(String userId, String token) async {
    return apiService.get(
      '${AppConfig.dashboardEndpoint}/$userId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getMetrics(String token, {Map<String, String>? filters}) async {
    return apiService.get(
      AppConfig.metricsEndpoint,
      token: token,
      queryParams: filters,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getInsights(String token, {Map<String, String>? params}) async {
    return apiService.get(
      AppConfig.insightsEndpoint,
      token: token,
      queryParams: params,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> generateReport(Map<String, dynamic> reportData, String token) async {
    return apiService.post(
      AppConfig.reportsEndpoint,
      token: token,
      body: reportData,
    );
  }

  // Collaboration API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> createTeamQuest(Map<String, dynamic> questData, String token) async {
    return apiService.post(
      '${AppConfig.teamQuestEndpoint}/create',
      token: token,
      body: questData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> joinTeamQuest(String questId, String userId, String token) async {
    return apiService.post(
      '${AppConfig.teamQuestEndpoint}/$questId/join',
      token: token,
      body: {'user_id': userId},
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getTeamQuestStatus(String questId, String token) async {
    return apiService.get(
      '${AppConfig.teamQuestEndpoint}/$questId/status',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> sendTeamMessage(String questId, Map<String, dynamic> messageData, String token) async {
    return apiService.post(
      '${AppConfig.teamQuestEndpoint}/$questId/message',
      token: token,
      body: messageData,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getTeamMessages(String questId, String token, {int limit = 50, int offset = 0}) async {
    return apiService.get(
      '${AppConfig.teamQuestEndpoint}/$questId/messages',
      token: token,
      queryParams: {
        'limit': limit.toString(),
        'offset': offset.toString(),
      },
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getUserTeamQuests(String userId, String token, {String? status}) async {
    return apiService.get(
      '/api/v1/collaboration/user/$userId/team-quests',
      token: token,
      queryParams: status != null ? {'status': status} : null,
    );
  }

  // Enterprise API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> createOrganization(Map<String, dynamic> orgData, String token) async {
    return apiService.post(
      AppConfig.organizationsEndpoint,
      token: token,
      body: orgData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getOrganization(String orgId, String token) async {
    return apiService.get(
      '${AppConfig.organizationsEndpoint}/$orgId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateOrganization(String orgId, Map<String, dynamic> updateData, String token) async {
    return apiService.put(
      '${AppConfig.organizationsEndpoint}/$orgId',
      token: token,
      body: updateData,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> listUserOrganizations(String userId, String token) async {
    return apiService.get(
      '/api/v1/enterprise/user/$userId/organizations',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createMember(String orgId, Map<String, dynamic> memberData, String token) async {
    return apiService.post(
      '${AppConfig.organizationsEndpoint}/$orgId/members',
      token: token,
      body: memberData,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> listMembers(String orgId, String token) async {
    return apiService.get(
      '${AppConfig.organizationsEndpoint}/$orgId/members',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateMember(String orgId, String memberId, Map<String, dynamic> updateData, String token) async {
    return apiService.put(
      '${AppConfig.organizationsEndpoint}/$orgId/members/$memberId',
      token: token,
      body: updateData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> deleteMember(String orgId, String memberId, String token) async {
    return apiService.delete(
      '${AppConfig.organizationsEndpoint}/$orgId/members/$memberId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> listRoles(String orgId, String token) async {
    return apiService.get(
      '${AppConfig.organizationsEndpoint}/$orgId/roles',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createRole(String orgId, Map<String, dynamic> roleData, String token) async {
    return apiService.post(
      '${AppConfig.organizationsEndpoint}/$orgId/roles',
      token: token,
      body: roleData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateRole(String orgId, String roleId, Map<String, dynamic> updateData, String token) async {
    return apiService.put(
      '${AppConfig.organizationsEndpoint}/$orgId/roles/$roleId',
      token: token,
      body: updateData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> deleteRole(String orgId, String roleId, String token) async {
    return apiService.delete(
      '${AppConfig.organizationsEndpoint}/$orgId/roles/$roleId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getEnterpriseAnalyticsSummary(String orgId, String token) async {
    return apiService.get(
      '${AppConfig.organizationsEndpoint}/$orgId/analytics/summary',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getEnterpriseAnalytics(String orgId, String token, {Map<String, String>? params}) async {
    return apiService.get(
      '${AppConfig.organizationsEndpoint}/$orgId/analytics',
      token: token,
      queryParams: params,
    );
  }

  // Advanced Auth API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> verifyEmail(String token, String verificationCode) async {
    return apiService.post(
      AppConfig.verifyEmailEndpoint,
      body: {
        'token': token,
        'code': verificationCode,
      },
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> resendVerification(String email) async {
    return apiService.post(
      AppConfig.resendVerificationEndpoint,
      body: {'email': email},
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> forgotPassword(String email) async {
    return apiService.post(
      AppConfig.forgotPasswordEndpoint,
      body: {'email': email},
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> resetPassword(String token, String newPassword) async {
    return apiService.post(
      AppConfig.resetPasswordEndpoint,
      body: {
        'token': token,
        'new_password': newPassword,
      },
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> changePassword(String oldPassword, String newPassword, String token) async {
    return apiService.post(
      AppConfig.changePasswordEndpoint,
      token: token,
      body: {
        'old_password': oldPassword,
        'new_password': newPassword,
      },
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> enableTwoFactor(String token) async {
    return apiService.post(
      '${AppConfig.twoFactorEndpoint}/enable',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> verifyTwoFactor(String code, String token) async {
    return apiService.post(
      '${AppConfig.twoFactorEndpoint}/verify',
      token: token,
      body: {'code': code},
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> getUserSessions(String token) async {
    return apiService.get(
      AppConfig.sessionsEndpoint,
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> revokeSession(String sessionId, String token) async {
    return apiService.delete(
      '${AppConfig.sessionsEndpoint}/$sessionId',
      token: token,
    );
  }

  // Monitoring API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> getPerformanceMetrics(String token) async {
    return apiService.get(
      '${AppConfig.performanceEndpoint}/summary',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getSystemInfo(String token) async {
    return apiService.get(
      AppConfig.systemInfoEndpoint,
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getHealthCheck(String token) async {
    return apiService.get(
      '${AppConfig.healthCheckEndpoint}/detailed',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getEndpointPerformance(String token) async {
    return apiService.get(
      '${AppConfig.performanceEndpoint}/endpoints',
      token: token,
    );
  }

  // Notification API calls
  Future<client_api.ApiResponse<Map<String, dynamic>>> getNotifications(String userId, GetNotificationsRequest request, String token) async {
    return apiService.get(
      '/notifications',
      token: token,
      queryParams: <String, String>{
        'userId': userId,
        'page': request.page.toString(),
        'limit': request.limit.toString(),
        if (request.status != null) 'status': request.status!.name,
        if (request.type != null) 'type': request.type!.name,
        if (request.category != null) 'category': request.category!.name,
        if (request.priority != null) 'priority': request.priority!.name,
        if (request.isRead != null) 'isRead': request.isRead!.toString(),
        if (request.startDate != null) 'startDate': request.startDate!.toIso8601String(),
        if (request.endDate != null) 'endDate': request.endDate!.toIso8601String(),
        if (request.sortBy != null) 'sortBy': request.sortBy!,
        if (request.sortDirection != null) 'sortDirection': request.sortDirection!.name,
      },
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getNotificationPreferences(String userId, String token) async {
    return apiService.get(
      '/notifications/preferences/$userId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateNotificationPreferences(String userId, UpdateNotificationPreferencesRequest request, String token) async {
    return apiService.put(
      '/notifications/preferences/$userId',
      token: token,
      body: request.toJson(),
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> markNotificationsAsRead(List<String> notificationIds, String token) async {
    return apiService.post(
      '/notifications/mark-read',
      token: token,
      body: {'notificationIds': notificationIds},
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> deleteNotification(String notificationId, String token) async {
    return apiService.delete(
      '/notifications/$notificationId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createNotification(CreateNotificationRequest request, String token) async {
    return apiService.post(
      '/notifications',
      token: token,
      body: request.toJson(),
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createNotificationFromTemplate(CreateNotificationFromTemplateRequest request, String token) async {
    return apiService.post(
      '/notifications/from-template',
      token: token,
      body: request.toJson(),
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> registerDevice(RegisterDeviceRequest request, String token) async {
    return apiService.post(
      '/notifications/register-device',
      token: token,
      body: request.toJson(),
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getNotificationStats(String userId, String token) async {
    return apiService.get(
      '/notifications/stats/$userId',
      token: token,
    );
  }

  // Quest API calls
  Future<client_api.ApiResponse<List<dynamic>>> getQuests(String token, {Map<String, String>? filters}) async {
    return apiService.get(
      '/quests',
      token: token,
      queryParams: filters,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getQuest(String questId, String token) async {
    return apiService.get(
      '/quests/$questId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createQuest(Map<String, dynamic> questData, String token) async {
    return apiService.post(
      '/quests',
      token: token,
      body: questData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateQuest(String questId, Map<String, dynamic> questData, String token) async {
    return apiService.put(
      '/quests/$questId',
      token: token,
      body: questData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateQuestStatus(String questId, QuestStatus status, String token) async {
    return apiService.put(
      '/quests/$questId/status',
      token: token,
      body: {'status': status.name},
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateQuestPriority(String questId, QuestPriority priority, String token) async {
    return apiService.put(
      '/quests/$questId/priority',
      token: token,
      body: {'priority': priority.name},
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> deleteQuest(String questId, String token) async {
    return apiService.delete(
      '/quests/$questId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> addTaskToQuest(String questId, Map<String, dynamic> taskData, String token) async {
    return apiService.post(
      '/quests/$questId/tasks',
      token: token,
      body: taskData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateTask(String taskId, Map<String, dynamic> taskData, String token) async {
    return apiService.put(
      '/tasks/$taskId',
      token: token,
      body: taskData,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> deleteTask(String taskId, String token) async {
    return apiService.delete(
      '/tasks/$taskId',
      token: token,
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> assignQuestToUser(String questId, String userId, String token) async {
    return apiService.post(
      '/quests/$questId/assign',
      token: token,
      body: {'userId': userId},
    );
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> unassignQuestFromUser(String questId, String userId, String token) async {
    return apiService.post(
      '/quests/$questId/unassign',
      token: token,
      body: {'userId': userId},
    );
  }

  Future<client_api.ApiResponse<List<dynamic>>> searchQuests(String query, String token, {Map<String, String>? filters}) async {
    final queryParams = <String, String>{
      'q': query,
      if (filters != null) ...filters,
    };

    return apiService.get(
      '/quests/search',
      token: token,
      queryParams: queryParams,
    );
  }
}