import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'analytics_insights.g.dart';

/// Analytics insights for AI-generated recommendations
@JsonSerializable()
class AnalyticsInsights extends Equatable {
  /// Unique insight identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// Insight type (e.g., 'engagement_drop', 'feature_adoption')
  final String insightType;

  /// Insight category for grouping
  final String insightCategory;

  /// Insight title
  final String title;

  /// Detailed description
  final String? description;

  /// Insight data and analysis
  final Map<String, dynamic> insightData;

  /// Confidence score (0.000 to 1.000)
  final double? confidenceScore;

  /// Impact level
  final ImpactLevel impactLevel;

  /// Action recommendations
  final Map<String, dynamic> actionRecommendations;

  /// Related metrics that support this insight
  final List<String> relatedMetrics;

  /// When this insight becomes valid
  final DateTime validFrom;

  /// When this insight expires
  final DateTime? validUntil;

  /// Whether this insight is currently active
  final bool isActive;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const AnalyticsInsights({
    required this.id,
    required this.organizationId,
    required this.insightType,
    required this.insightCategory,
    required this.title,
    this.description,
    required this.insightData,
    this.confidenceScore,
    required this.impactLevel,
    required this.actionRecommendations,
    required this.relatedMetrics,
    required this.validFrom,
    this.validUntil,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create AnalyticsInsights from JSON
  factory AnalyticsInsights.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsInsightsFromJson(json);

  /// Convert AnalyticsInsights to JSON
  Map<String, dynamic> toJson() => _$AnalyticsInsightsToJson(this);

  /// Check if insight is currently valid
  bool get isCurrentlyValid {
    final now = DateTime.now().toUtc();
    if (!isActive) return false;
    if (now.isBefore(validFrom)) return false;
    if (validUntil != null && now.isAfter(validUntil!)) return false;
    return true;
  }

  /// Get confidence level as string
  String get confidenceLevelString {
    if (confidenceScore == null) return 'Unknown';
    if (confidenceScore! >= 0.9) return 'Very High';
    if (confidenceScore! >= 0.75) return 'High';
    if (confidenceScore! >= 0.5) return 'Medium';
    if (confidenceScore! >= 0.25) return 'Low';
    return 'Very Low';
  }

  /// Get formatted confidence percentage
  String get confidencePercentage {
    if (confidenceScore == null) return 'N/A';
    return '${(confidenceScore! * 100).toStringAsFixed(1)}%';
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        insightType,
        insightCategory,
        title,
        description,
        insightData,
        confidenceScore,
        impactLevel,
        actionRecommendations,
        relatedMetrics,
        validFrom,
        validUntil,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Impact level enumeration
enum ImpactLevel {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

/// Predictive analytics results
@JsonSerializable()
class PredictionResults extends Equatable {
  /// Organization identifier
  final String organizationId;

  /// Prediction type
  final String predictionType;

  /// Prediction model used
  final String modelName;

  /// Model version
  final String modelVersion;

  /// Prediction results
  final Map<String, dynamic> predictions;

  /// Feature importance scores
  final Map<String, double> featureImportance;

  /// Model performance metrics
  final Map<String, double> performanceMetrics;

  /// Prediction confidence intervals
  final Map<String, ConfidenceInterval>? confidenceIntervals;

  /// When predictions were generated
  final DateTime generatedAt;

  /// How long predictions are valid
  final Duration validityPeriod;

  const PredictionResults({
    required this.organizationId,
    required this.predictionType,
    required this.modelName,
    required this.modelVersion,
    required this.predictions,
    required this.featureImportance,
    required this.performanceMetrics,
    this.confidenceIntervals,
    required this.generatedAt,
    required this.validityPeriod,
  });

  /// Create PredictionResults from JSON
  factory PredictionResults.fromJson(Map<String, dynamic> json) =>
      _$PredictionResultsFromJson(json);

  /// Convert PredictionResults to JSON
  Map<String, dynamic> toJson() => _$PredictionResultsToJson(this);

  /// Check if predictions are still valid
  bool get isValid {
    return DateTime.now().toUtc().isBefore(generatedAt.add(validityPeriod));
  }

  /// Get expiration time
  DateTime get expiresAt => generatedAt.add(validityPeriod);

  @override
  List<Object?> get props => [
        organizationId,
        predictionType,
        modelName,
        modelVersion,
        predictions,
        featureImportance,
        performanceMetrics,
        confidenceIntervals,
        generatedAt,
        validityPeriod,
      ];

  @override
  bool get stringify => true;
}

/// Confidence interval for predictions
@JsonSerializable()
class ConfidenceInterval extends Equatable {
  /// Lower bound
  final double lowerBound;

  /// Upper bound
  final double upperBound;

  /// Confidence level (e.g., 0.95 for 95%)
  final double confidenceLevel;

  const ConfidenceInterval({
    required this.lowerBound,
    required this.upperBound,
    required this.confidenceLevel,
  });

  /// Create ConfidenceInterval from JSON
  factory ConfidenceInterval.fromJson(Map<String, dynamic> json) =>
      _$ConfidenceIntervalFromJson(json);

  /// Convert ConfidenceInterval to JSON
  Map<String, dynamic> toJson() => _$ConfidenceIntervalToJson(this);

  /// Get interval width
  double get width => upperBound - lowerBound;

  /// Get midpoint
  double get midpoint => (lowerBound + upperBound) / 2;

  @override
  List<Object?> get props => [lowerBound, upperBound, confidenceLevel];

  @override
  bool get stringify => true;
}

/// External analytics integration configuration
@JsonSerializable()
class AnalyticsIntegration extends Equatable {
  /// Unique integration identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// Integration name
  final String integrationName;

  /// Integration type (webhook, api, bi_tool)
  final IntegrationType integrationType;

  /// Endpoint URL
  final String? endpointUrl;

  /// API key hash for security
  final String? apiKeyHash;

  /// Integration configuration
  final Map<String, dynamic> configuration;

  /// Authentication settings
  final Map<String, dynamic> authentication;

  /// Data mapping configuration
  final Map<String, dynamic> dataMapping;

  /// Sync schedule (cron expression)
  final String? syncSchedule;

  /// Last sync timestamp
  final DateTime? lastSyncAt;

  /// Sync status
  final SyncStatus syncStatus;

  /// Error count
  final int errorCount;

  /// Last error message
  final String? lastErrorMessage;

  /// Whether integration is active
  final bool isActive;

  /// Creator user ID
  final String createdBy;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const AnalyticsIntegration({
    required this.id,
    required this.organizationId,
    required this.integrationName,
    required this.integrationType,
    this.endpointUrl,
    this.apiKeyHash,
    required this.configuration,
    required this.authentication,
    required this.dataMapping,
    this.syncSchedule,
    this.lastSyncAt,
    required this.syncStatus,
    required this.errorCount,
    this.lastErrorMessage,
    required this.isActive,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create AnalyticsIntegration from JSON
  factory AnalyticsIntegration.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsIntegrationFromJson(json);

  /// Convert AnalyticsIntegration to JSON
  Map<String, dynamic> toJson() => _$AnalyticsIntegrationToJson(this);

  /// Check if integration is healthy
  bool get isHealthy {
    return isActive && 
           syncStatus == SyncStatus.active && 
           errorCount < 5;
  }

  /// Check if integration needs attention
  bool get needsAttention {
    return syncStatus == SyncStatus.failed || errorCount >= 5;
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        integrationName,
        integrationType,
        endpointUrl,
        apiKeyHash,
        configuration,
        authentication,
        dataMapping,
        syncSchedule,
        lastSyncAt,
        syncStatus,
        errorCount,
        lastErrorMessage,
        isActive,
        createdBy,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Integration type enumeration
enum IntegrationType {
  @JsonValue('webhook')
  webhook,
  @JsonValue('api')
  api,
  @JsonValue('bi_tool')
  biTool,
}

/// Sync status enumeration
enum SyncStatus {
  @JsonValue('active')
  active,
  @JsonValue('paused')
  paused,
  @JsonValue('failed')
  failed,
}

/// Analytics query builder model
@JsonSerializable()
class AnalyticsQuery extends Equatable {
  /// Query identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// Query name
  final String name;

  /// Data source selection
  final List<String> dataSources;

  /// Metrics to calculate
  final List<QueryMetric> metrics;

  /// Dimensions for grouping
  final List<QueryDimension> dimensions;

  /// Filters to apply
  final List<QueryFilter> filters;

  /// Time range
  final QueryTimeRange timeRange;

  /// Sort configuration
  final List<QuerySort>? sorts;

  /// Limit number of results
  final int? limit;

  /// Query metadata
  final Map<String, dynamic> metadata;

  const AnalyticsQuery({
    required this.id,
    required this.organizationId,
    required this.name,
    required this.dataSources,
    required this.metrics,
    required this.dimensions,
    required this.filters,
    required this.timeRange,
    this.sorts,
    this.limit,
    required this.metadata,
  });

  /// Create AnalyticsQuery from JSON
  factory AnalyticsQuery.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsQueryFromJson(json);

  /// Convert AnalyticsQuery to JSON
  Map<String, dynamic> toJson() => _$AnalyticsQueryToJson(this);

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        dataSources,
        metrics,
        dimensions,
        filters,
        timeRange,
        sorts,
        limit,
        metadata,
      ];

  @override
  bool get stringify => true;
}

/// Query metric definition
@JsonSerializable()
class QueryMetric extends Equatable {
  /// Metric field name
  final String fieldName;

  /// Aggregation function
  final String aggregation;

  /// Alias for the metric
  final String? alias;

  /// Filters specific to this metric
  final List<QueryFilter>? filters;

  const QueryMetric({
    required this.fieldName,
    required this.aggregation,
    this.alias,
    this.filters,
  });

  /// Create QueryMetric from JSON
  factory QueryMetric.fromJson(Map<String, dynamic> json) =>
      _$QueryMetricFromJson(json);

  /// Convert QueryMetric to JSON
  Map<String, dynamic> toJson() => _$QueryMetricToJson(this);

  @override
  List<Object?> get props => [fieldName, aggregation, alias, filters];

  @override
  bool get stringify => true;
}

/// Query dimension for grouping
@JsonSerializable()
class QueryDimension extends Equatable {
  /// Dimension field name
  final String fieldName;

  /// Alias for the dimension
  final String? alias;

  /// Time bucket for time dimensions
  final String? timeBucket;

  const QueryDimension({
    required this.fieldName,
    this.alias,
    this.timeBucket,
  });

  /// Create QueryDimension from JSON
  factory QueryDimension.fromJson(Map<String, dynamic> json) =>
      _$QueryDimensionFromJson(json);

  /// Convert QueryDimension to JSON
  Map<String, dynamic> toJson() => _$QueryDimensionToJson(this);

  @override
  List<Object?> get props => [fieldName, alias, timeBucket];

  @override
  bool get stringify => true;
}

/// Query filter definition
@JsonSerializable()
class QueryFilter extends Equatable {
  /// Field to filter on
  final String fieldName;

  /// Filter operator
  final String operator;

  /// Filter value(s)
  final dynamic value;

  /// Logical connector (AND/OR)
  final String? connector;

  const QueryFilter({
    required this.fieldName,
    required this.operator,
    required this.value,
    this.connector,
  });

  /// Create QueryFilter from JSON
  factory QueryFilter.fromJson(Map<String, dynamic> json) =>
      _$QueryFilterFromJson(json);

  /// Convert QueryFilter to JSON
  Map<String, dynamic> toJson() => _$QueryFilterToJson(this);

  @override
  List<Object?> get props => [fieldName, operator, value, connector];

  @override
  bool get stringify => true;
}

/// Query time range
@JsonSerializable()
class QueryTimeRange extends Equatable {
  /// Start timestamp
  final DateTime start;

  /// End timestamp
  final DateTime end;

  /// Time zone
  final String? timeZone;

  const QueryTimeRange({
    required this.start,
    required this.end,
    this.timeZone,
  });

  /// Create QueryTimeRange from JSON
  factory QueryTimeRange.fromJson(Map<String, dynamic> json) =>
      _$QueryTimeRangeFromJson(json);

  /// Convert QueryTimeRange to JSON
  Map<String, dynamic> toJson() => _$QueryTimeRangeToJson(this);

  @override
  List<Object?> get props => [start, end, timeZone];

  @override
  bool get stringify => true;
}

/// Query sort definition
@JsonSerializable()
class QuerySort extends Equatable {
  /// Field to sort by
  final String fieldName;

  /// Sort direction
  final SortDirection direction;

  const QuerySort({
    required this.fieldName,
    required this.direction,
  });

  /// Create QuerySort from JSON
  factory QuerySort.fromJson(Map<String, dynamic> json) =>
      _$QuerySortFromJson(json);

  /// Convert QuerySort to JSON
  Map<String, dynamic> toJson() => _$QuerySortToJson(this);

  @override
  List<Object?> get props => [fieldName, direction];

  @override
  bool get stringify => true;
}

/// Sort direction enumeration
enum SortDirection {
  @JsonValue('asc')
  ascending,
  @JsonValue('desc')
  descending,
}