// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MFASetupStatus _$MFASetupStatusFromJson(Map<String, dynamic> json) =>
    MFASetupStatus(
      userId: json['user_id'] as String,
      totpEnabled: json['totp_enabled'] as bool,
      totpVerified: json['totp_verified'] as bool,
      backupCodesCount: (json['backup_codes_count'] as num).toInt(),
      trustedDevicesCount: (json['trusted_devices_count'] as num).toInt(),
      phoneConfigured: json['phone_configured'] as bool,
      emailConfigured: json['email_configured'] as bool,
      setupComplete: json['setup_complete'] as bool,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );

Map<String, dynamic> _$MFASetupStatusToJson(MFASetupStatus instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'totp_enabled': instance.totpEnabled,
      'totp_verified': instance.totpVerified,
      'backup_codes_count': instance.backupCodesCount,
      'trusted_devices_count': instance.trustedDevicesCount,
      'phone_configured': instance.phoneConfigured,
      'email_configured': instance.emailConfigured,
      'setup_complete': instance.setupComplete,
      'last_updated': instance.lastUpdated.toIso8601String(),
    };

MFAEnforcementResult _$MFAEnforcementResultFromJson(
  Map<String, dynamic> json,
) => MFAEnforcementResult(
  isMFARequired: json['is_mfa_required'] as bool,
  canBypassMFA: json['can_bypass_mfa'] as bool,
  availableMethods: (json['available_methods'] as List<dynamic>)
      .map((e) => $enumDecode(_$MFAMethodEnumMap, e))
      .toList(),
  recommendedMethods: (json['recommended_methods'] as List<dynamic>)
      .map((e) => $enumDecode(_$MFAMethodEnumMap, e))
      .toList(),
  bypassReason: $enumDecodeNullable(
    _$MFABypassReasonEnumMap,
    json['bypass_reason'],
  ),
  challengeId: json['challenge_id'] as String?,
  policyContext: json['policy_context'] as Map<String, dynamic>? ?? const {},
  warnings:
      (json['warnings'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  maxAttempts: (json['max_attempts'] as num?)?.toInt() ?? 3,
  challengeValidDurationMinutes:
      (json['challenge_valid_duration_minutes'] as num?)?.toInt() ?? 5,
  enforcedAt: DateTime.parse(json['enforced_at'] as String),
);

Map<String, dynamic> _$MFAEnforcementResultToJson(
  MFAEnforcementResult instance,
) => <String, dynamic>{
  'is_mfa_required': instance.isMFARequired,
  'can_bypass_mfa': instance.canBypassMFA,
  'available_methods': instance.availableMethods
      .map((e) => _$MFAMethodEnumMap[e]!)
      .toList(),
  'recommended_methods': instance.recommendedMethods
      .map((e) => _$MFAMethodEnumMap[e]!)
      .toList(),
  'bypass_reason': _$MFABypassReasonEnumMap[instance.bypassReason],
  'challenge_id': instance.challengeId,
  'policy_context': instance.policyContext,
  'warnings': instance.warnings,
  'max_attempts': instance.maxAttempts,
  'challenge_valid_duration_minutes': instance.challengeValidDurationMinutes,
  'enforced_at': instance.enforcedAt.toIso8601String(),
};

const _$MFAMethodEnumMap = {
  MFAMethod.totp: 'totp',
  MFAMethod.sms: 'sms',
  MFAMethod.email: 'email',
  MFAMethod.backupCode: 'backupCode',
  MFAMethod.trustedDevice: 'trustedDevice',
};

const _$MFABypassReasonEnumMap = {
  MFABypassReason.trustedDevice: 'trustedDevice',
  MFABypassReason.emergencyAccess: 'emergencyAccess',
  MFABypassReason.adminOverride: 'adminOverride',
  MFABypassReason.temporaryException: 'temporaryException',
};

MFAChallenge _$MFAChallengeFromJson(Map<String, dynamic> json) => MFAChallenge(
  id: json['id'] as String,
  userId: json['user_id'] as String,
  method: $enumDecode(_$MFAMethodEnumMap, json['method']),
  challengeData: json['challenge_data'] as String?,
  destinationHint: json['destination_hint'] as String?,
  createdAt: DateTime.parse(json['created_at'] as String),
  expiresAt: DateTime.parse(json['expires_at'] as String),
  isCompleted: json['is_completed'] as bool? ?? false,
  completedAt: json['completed_at'] == null
      ? null
      : DateTime.parse(json['completed_at'] as String),
  attemptCount: (json['attempt_count'] as num?)?.toInt() ?? 0,
  maxAttempts: (json['max_attempts'] as num?)?.toInt() ?? 3,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$MFAChallengeToJson(MFAChallenge instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'method': _$MFAMethodEnumMap[instance.method]!,
      'challenge_data': instance.challengeData,
      'destination_hint': instance.destinationHint,
      'created_at': instance.createdAt.toIso8601String(),
      'expires_at': instance.expiresAt.toIso8601String(),
      'is_completed': instance.isCompleted,
      'completed_at': instance.completedAt?.toIso8601String(),
      'attempt_count': instance.attemptCount,
      'max_attempts': instance.maxAttempts,
      'metadata': instance.metadata,
    };

MFAVerificationResult _$MFAVerificationResultFromJson(
  Map<String, dynamic> json,
) => MFAVerificationResult(
  isValid: json['is_valid'] as bool,
  isCompleted: json['is_completed'] as bool,
  sessionToken: json['session_token'] as String?,
  errorMessage: json['error_message'] as String?,
  attemptsRemaining: (json['attempts_remaining'] as num).toInt(),
  deviceTrusted: json['device_trusted'] as bool? ?? false,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  verifiedAt: DateTime.parse(json['verified_at'] as String),
);

Map<String, dynamic> _$MFAVerificationResultToJson(
  MFAVerificationResult instance,
) => <String, dynamic>{
  'is_valid': instance.isValid,
  'is_completed': instance.isCompleted,
  'session_token': instance.sessionToken,
  'error_message': instance.errorMessage,
  'attempts_remaining': instance.attemptsRemaining,
  'device_trusted': instance.deviceTrusted,
  'metadata': instance.metadata,
  'verified_at': instance.verifiedAt.toIso8601String(),
};

MFASettings _$MFASettingsFromJson(Map<String, dynamic> json) => MFASettings(
  userId: json['user_id'] as String,
  mfaEnabled: json['mfa_enabled'] as bool,
  totpConfigured: json['totp_configured'] as bool,
  smsConfigured: json['sms_configured'] as bool,
  emailConfigured: json['email_configured'] as bool,
  backupCodesAvailable: json['backup_codes_available'] as bool,
  backupCodesCount: (json['backup_codes_count'] as num).toInt(),
  trustedDevicesCount: (json['trusted_devices_count'] as num).toInt(),
  recoveryOptions: (json['recovery_options'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  lastUpdated: DateTime.parse(json['last_updated'] as String),
);

Map<String, dynamic> _$MFASettingsToJson(MFASettings instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'mfa_enabled': instance.mfaEnabled,
      'totp_configured': instance.totpConfigured,
      'sms_configured': instance.smsConfigured,
      'email_configured': instance.emailConfigured,
      'backup_codes_available': instance.backupCodesAvailable,
      'backup_codes_count': instance.backupCodesCount,
      'trusted_devices_count': instance.trustedDevicesCount,
      'recovery_options': instance.recoveryOptions,
      'last_updated': instance.lastUpdated.toIso8601String(),
    };

MFAUserStats _$MFAUserStatsFromJson(Map<String, dynamic> json) => MFAUserStats(
  userId: json['user_id'] as String,
  email: json['email'] as String,
  totpEnabled: json['totp_enabled'] as bool,
  backupCodesCount: (json['backup_codes_count'] as num).toInt(),
  backupCodesUsed: (json['backup_codes_used'] as num).toInt(),
  trustedDevicesCount: (json['trusted_devices_count'] as num).toInt(),
  activeMfaSessions: (json['active_mfa_sessions'] as num).toInt(),
  lastMfaActivity: json['last_mfa_activity'] == null
      ? null
      : DateTime.parse(json['last_mfa_activity'] as String),
  securityScore: (json['security_score'] as num).toDouble(),
);

Map<String, dynamic> _$MFAUserStatsToJson(MFAUserStats instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'email': instance.email,
      'totp_enabled': instance.totpEnabled,
      'backup_codes_count': instance.backupCodesCount,
      'backup_codes_used': instance.backupCodesUsed,
      'trusted_devices_count': instance.trustedDevicesCount,
      'active_mfa_sessions': instance.activeMfaSessions,
      'last_mfa_activity': instance.lastMfaActivity?.toIso8601String(),
      'security_score': instance.securityScore,
    };

CreateMFAChallengeRequest _$CreateMFAChallengeRequestFromJson(
  Map<String, dynamic> json,
) => CreateMFAChallengeRequest(
  userId: json['user_id'] as String,
  method: $enumDecode(_$MFAMethodEnumMap, json['method']),
  destination: json['destination'] as String?,
  validDurationMinutes: (json['valid_duration_minutes'] as num?)?.toInt(),
  maxAttempts: (json['max_attempts'] as num?)?.toInt(),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$CreateMFAChallengeRequestToJson(
  CreateMFAChallengeRequest instance,
) => <String, dynamic>{
  'user_id': instance.userId,
  'method': _$MFAMethodEnumMap[instance.method]!,
  'destination': instance.destination,
  'valid_duration_minutes': instance.validDurationMinutes,
  'max_attempts': instance.maxAttempts,
  'metadata': instance.metadata,
};

VerifyMFAChallengeRequest _$VerifyMFAChallengeRequestFromJson(
  Map<String, dynamic> json,
) => VerifyMFAChallengeRequest(
  challengeId: json['challenge_id'] as String,
  userId: json['user_id'] as String,
  method: $enumDecode(_$MFAMethodEnumMap, json['method']),
  verificationCode: json['verification_code'] as String,
  deviceFingerprint: json['device_fingerprint'] as String?,
  trustDevice: json['trust_device'] as bool? ?? false,
);

Map<String, dynamic> _$VerifyMFAChallengeRequestToJson(
  VerifyMFAChallengeRequest instance,
) => <String, dynamic>{
  'challenge_id': instance.challengeId,
  'user_id': instance.userId,
  'method': _$MFAMethodEnumMap[instance.method]!,
  'verification_code': instance.verificationCode,
  'device_fingerprint': instance.deviceFingerprint,
  'trust_device': instance.trustDevice,
};

EvaluateMFARequirementRequest _$EvaluateMFARequirementRequestFromJson(
  Map<String, dynamic> json,
) => EvaluateMFARequirementRequest(
  userId: json['user_id'] as String,
  organizationId: json['organization_id'] as String?,
  deviceFingerprint: json['device_fingerprint'] as String?,
  context: json['context'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$EvaluateMFARequirementRequestToJson(
  EvaluateMFARequirementRequest instance,
) => <String, dynamic>{
  'user_id': instance.userId,
  'organization_id': instance.organizationId,
  'device_fingerprint': instance.deviceFingerprint,
  'context': instance.context,
};

VerifyPhoneRequest _$VerifyPhoneRequestFromJson(Map<String, dynamic> json) =>
    VerifyPhoneRequest(
      userId: json['user_id'] as String,
      phoneNumber: json['phone_number'] as String,
    );

Map<String, dynamic> _$VerifyPhoneRequestToJson(VerifyPhoneRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'phone_number': instance.phoneNumber,
    };

VerifyEmailRequest _$VerifyEmailRequestFromJson(Map<String, dynamic> json) =>
    VerifyEmailRequest(
      userId: json['user_id'] as String,
      email: json['email'] as String,
    );

Map<String, dynamic> _$VerifyEmailRequestToJson(VerifyEmailRequest instance) =>
    <String, dynamic>{'user_id': instance.userId, 'email': instance.email};

DisableMFARequest _$DisableMFARequestFromJson(Map<String, dynamic> json) =>
    DisableMFARequest(
      userId: json['user_id'] as String,
      reason: json['reason'] as String?,
      verificationCode: json['verification_code'] as String?,
    );

Map<String, dynamic> _$DisableMFARequestToJson(DisableMFARequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'reason': instance.reason,
      'verification_code': instance.verificationCode,
    };

MFASetupStatusResponse _$MFASetupStatusResponseFromJson(
  Map<String, dynamic> json,
) => MFASetupStatusResponse(
  data: MFASetupStatus.fromJson(json['data'] as Map<String, dynamic>),
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$MFASetupStatusResponseToJson(
  MFASetupStatusResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

MFAEnforcementResponse _$MFAEnforcementResponseFromJson(
  Map<String, dynamic> json,
) => MFAEnforcementResponse(
  data: MFAEnforcementResult.fromJson(json['data'] as Map<String, dynamic>),
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$MFAEnforcementResponseToJson(
  MFAEnforcementResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

MFAChallengeResponse _$MFAChallengeResponseFromJson(
  Map<String, dynamic> json,
) => MFAChallengeResponse(
  data: json['data'] as Map<String, dynamic>,
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$MFAChallengeResponseToJson(
  MFAChallengeResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

MFAVerificationResponse _$MFAVerificationResponseFromJson(
  Map<String, dynamic> json,
) => MFAVerificationResponse(
  data: MFAVerificationResult.fromJson(json['data'] as Map<String, dynamic>),
  message: json['message'] as String,
  success: json['success'] as bool? ?? true,
);

Map<String, dynamic> _$MFAVerificationResponseToJson(
  MFAVerificationResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

MFASettingsResponse _$MFASettingsResponseFromJson(Map<String, dynamic> json) =>
    MFASettingsResponse(
      data: MFASettings.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String,
      success: json['success'] as bool? ?? true,
    );

Map<String, dynamic> _$MFASettingsResponseToJson(
  MFASettingsResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'message': instance.message,
  'success': instance.success,
};

DeviceRegistrationRequest _$DeviceRegistrationRequestFromJson(
  Map<String, dynamic> json,
) => DeviceRegistrationRequest(
  deviceFingerprint: json['device_fingerprint'] as String,
  deviceName: json['device_name'] as String,
  deviceType: json['device_type'] as String,
  ipAddress: json['ip_address'] as String,
  userAgent: json['user_agent'] as String?,
  trustImmediately: json['trust_immediately'] as bool? ?? false,
);

Map<String, dynamic> _$DeviceRegistrationRequestToJson(
  DeviceRegistrationRequest instance,
) => <String, dynamic>{
  'device_fingerprint': instance.deviceFingerprint,
  'device_name': instance.deviceName,
  'device_type': instance.deviceType,
  'ip_address': instance.ipAddress,
  'user_agent': instance.userAgent,
  'trust_immediately': instance.trustImmediately,
};

MFAVerificationRequest _$MFAVerificationRequestFromJson(
  Map<String, dynamic> json,
) => MFAVerificationRequest(
  userId: json['user_id'] as String,
  method: $enumDecode(_$MFAMethodEnumMap, json['method']),
  code: json['code'] as String,
  challengeId: json['challenge_id'] as String?,
);

Map<String, dynamic> _$MFAVerificationRequestToJson(
  MFAVerificationRequest instance,
) => <String, dynamic>{
  'user_id': instance.userId,
  'method': _$MFAMethodEnumMap[instance.method]!,
  'code': instance.code,
  'challenge_id': instance.challengeId,
};

DeviceVerificationResult _$DeviceVerificationResultFromJson(
  Map<String, dynamic> json,
) => DeviceVerificationResult(
  isTrusted: json['is_trusted'] as bool,
  requiresMFA: json['requires_mfa'] as bool,
  device: json['device'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$DeviceVerificationResultToJson(
  DeviceVerificationResult instance,
) => <String, dynamic>{
  'is_trusted': instance.isTrusted,
  'requires_mfa': instance.requiresMFA,
  'device': instance.device,
};

TOTPSetup _$TOTPSetupFromJson(Map<String, dynamic> json) => TOTPSetup(
  secret: json['secret'] as String,
  qrCode: json['qr_code'] as String,
  backupCodes: (json['backup_codes'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$TOTPSetupToJson(TOTPSetup instance) => <String, dynamic>{
  'secret': instance.secret,
  'qr_code': instance.qrCode,
  'backup_codes': instance.backupCodes,
};

TOTPVerificationResult _$TOTPVerificationResultFromJson(
  Map<String, dynamic> json,
) => TOTPVerificationResult(
  isValid: json['is_valid'] as bool,
  message: json['message'] as String?,
);

Map<String, dynamic> _$TOTPVerificationResultToJson(
  TOTPVerificationResult instance,
) => <String, dynamic>{
  'is_valid': instance.isValid,
  'message': instance.message,
};

TOTPValidationResult _$TOTPValidationResultFromJson(
  Map<String, dynamic> json,
) => TOTPValidationResult(
  isValid: json['is_valid'] as bool,
  remainingAttempts: (json['remaining_attempts'] as num?)?.toInt(),
);

Map<String, dynamic> _$TOTPValidationResultToJson(
  TOTPValidationResult instance,
) => <String, dynamic>{
  'is_valid': instance.isValid,
  'remaining_attempts': instance.remainingAttempts,
};
