import 'package:equatable/equatable.dart';

/// Security event categories
enum SecurityEventCategory {
  authentication,
  authorization,
  dataAccess,
  configuration,
  securityPolicy,
  mfa,
  sso,
  session,
  suspicious;

  String get displayName {
    switch (this) {
      case SecurityEventCategory.authentication:
        return 'Authentication';
      case SecurityEventCategory.authorization:
        return 'Authorization';
      case SecurityEventCategory.dataAccess:
        return 'Data Access';
      case SecurityEventCategory.configuration:
        return 'Configuration';
      case SecurityEventCategory.securityPolicy:
        return 'Security Policy';
      case SecurityEventCategory.mfa:
        return 'Multi-Factor Authentication';
      case SecurityEventCategory.sso:
        return 'Single Sign-On';
      case SecurityEventCategory.session:
        return 'Session Management';
      case SecurityEventCategory.suspicious:
        return 'Suspicious Activity';
    }
  }
}

/// Security event severity levels
enum SecurityEventSeverity {
  low,
  medium,
  high,
  critical;

  String get displayName {
    switch (this) {
      case SecurityEventSeverity.low:
        return 'Low';
      case SecurityEventSeverity.medium:
        return 'Medium';
      case SecurityEventSeverity.high:
        return 'High';
      case SecurityEventSeverity.critical:
        return 'Critical';
    }
  }

  String get color {
    switch (this) {
      case SecurityEventSeverity.low:
        return '#28a745'; // Green
      case SecurityEventSeverity.medium:
        return '#ffc107'; // Yellow
      case SecurityEventSeverity.high:
        return '#fd7e14'; // Orange
      case SecurityEventSeverity.critical:
        return '#dc3545'; // Red
    }
  }
}

/// Geographic location information
class GeoLocation extends Equatable {
  final String? country;
  final String? countryCode;
  final String? region;
  final String? city;
  final double? latitude;
  final double? longitude;
  final String? timezone;
  final String? isp;

  const GeoLocation({
    this.country,
    this.countryCode,
    this.region,
    this.city,
    this.latitude,
    this.longitude,
    this.timezone,
    this.isp,
  });

  factory GeoLocation.fromJson(Map<String, dynamic> json) {
    return GeoLocation(
      country: json['country'] as String?,
      countryCode: json['country_code'] as String?,
      region: json['region'] as String?,
      city: json['city'] as String?,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      timezone: json['timezone'] as String?,
      isp: json['isp'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'country_code': countryCode,
      'region': region,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'isp': isp,
    };
  }

  String get displayLocation {
    final parts = <String>[];
    if (city != null) parts.add(city!);
    if (region != null) parts.add(region!);
    if (country != null) parts.add(country!);
    return parts.join(', ');
  }

  @override
  List<Object?> get props => [
        country,
        countryCode,
        region,
        city,
        latitude,
        longitude,
        timezone,
        isp,
      ];
}

/// Security Audit Log model for database table: security_audit_logs
class SecurityAuditLog extends Equatable {
  /// Unique log identifier
  final String id;

  /// Organization ID
  final String? organizationId;

  /// User ID
  final String? userId;

  /// Event type
  final String eventType;

  /// Event category
  final SecurityEventCategory eventCategory;

  /// Event description
  final String eventDescription;

  /// Event severity
  final SecurityEventSeverity eventSeverity;

  /// IP address
  final String? ipAddress;

  /// User agent
  final String? userAgent;

  /// Device fingerprint
  final String? deviceFingerprint;

  /// Geographic location
  final GeoLocation? geoLocation;

  /// Session ID
  final String? sessionId;

  /// Request ID
  final String? requestId;

  /// Resource type
  final String? resourceType;

  /// Resource ID
  final String? resourceId;

  /// Event-specific data
  final Map<String, dynamic>? eventData;

  /// Event metadata
  final Map<String, dynamic>? eventMetadata;

  /// Risk score (0-100)
  final int riskScore;

  /// Whether this is an anomaly
  final bool isAnomaly;

  /// Creation timestamp
  final DateTime createdAt;

  const SecurityAuditLog({
    required this.id,
    this.organizationId,
    this.userId,
    required this.eventType,
    required this.eventCategory,
    required this.eventDescription,
    required this.eventSeverity,
    this.ipAddress,
    this.userAgent,
    this.deviceFingerprint,
    this.geoLocation,
    this.sessionId,
    this.requestId,
    this.resourceType,
    this.resourceId,
    this.eventData,
    this.eventMetadata,
    required this.riskScore,
    required this.isAnomaly,
    required this.createdAt,
  });

  /// Create empty SecurityAuditLog for testing
  factory SecurityAuditLog.empty() {
    return SecurityAuditLog(
      id: '',
      eventType: '',
      eventCategory: SecurityEventCategory.authentication,
      eventDescription: '',
      eventSeverity: SecurityEventSeverity.medium,
      riskScore: 0,
      isAnomaly: false,
      createdAt: DateTime.now(),
    );
  }

  /// Create SecurityAuditLog from JSON
  factory SecurityAuditLog.fromJson(Map<String, dynamic> json) {
    return SecurityAuditLog(
      id: json['id'] as String,
      organizationId: json['organization_id'] as String?,
      userId: json['user_id'] as String?,
      eventType: json['event_type'] as String,
      eventCategory: SecurityEventCategory.values.firstWhere(
        (e) => e.name == json['event_category'],
        orElse: () => SecurityEventCategory.authentication,
      ),
      eventDescription: json['event_description'] as String,
      eventSeverity: SecurityEventSeverity.values.firstWhere(
        (e) => e.name == json['event_severity'],
        orElse: () => SecurityEventSeverity.medium,
      ),
      ipAddress: json['ip_address'] as String?,
      userAgent: json['user_agent'] as String?,
      deviceFingerprint: json['device_fingerprint'] as String?,
      geoLocation: json['geo_location'] != null
          ? GeoLocation.fromJson(json['geo_location'] as Map<String, dynamic>)
          : null,
      sessionId: json['session_id'] as String?,
      requestId: json['request_id'] as String?,
      resourceType: json['resource_type'] as String?,
      resourceId: json['resource_id'] as String?,
      eventData: json['event_data'] != null
          ? Map<String, dynamic>.from(json['event_data'] as Map)
          : null,
      eventMetadata: json['event_metadata'] != null
          ? Map<String, dynamic>.from(json['event_metadata'] as Map)
          : null,
      riskScore: json['risk_score'] as int? ?? 0,
      isAnomaly: json['is_anomaly'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// Convert SecurityAuditLog to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organization_id': organizationId,
      'user_id': userId,
      'event_type': eventType,
      'event_category': eventCategory.name,
      'event_description': eventDescription,
      'event_severity': eventSeverity.name,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'device_fingerprint': deviceFingerprint,
      'geo_location': geoLocation?.toJson(),
      'session_id': sessionId,
      'request_id': requestId,
      'resource_type': resourceType,
      'resource_id': resourceId,
      'event_data': eventData,
      'event_metadata': eventMetadata,
      'risk_score': riskScore,
      'is_anomaly': isAnomaly,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  SecurityAuditLog copyWith({
    String? id,
    String? organizationId,
    String? userId,
    String? eventType,
    SecurityEventCategory? eventCategory,
    String? eventDescription,
    SecurityEventSeverity? eventSeverity,
    String? ipAddress,
    String? userAgent,
    String? deviceFingerprint,
    GeoLocation? geoLocation,
    String? sessionId,
    String? requestId,
    String? resourceType,
    String? resourceId,
    Map<String, dynamic>? eventData,
    Map<String, dynamic>? eventMetadata,
    int? riskScore,
    bool? isAnomaly,
    DateTime? createdAt,
  }) {
    return SecurityAuditLog(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      userId: userId ?? this.userId,
      eventType: eventType ?? this.eventType,
      eventCategory: eventCategory ?? this.eventCategory,
      eventDescription: eventDescription ?? this.eventDescription,
      eventSeverity: eventSeverity ?? this.eventSeverity,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      deviceFingerprint: deviceFingerprint ?? this.deviceFingerprint,
      geoLocation: geoLocation ?? this.geoLocation,
      sessionId: sessionId ?? this.sessionId,
      requestId: requestId ?? this.requestId,
      resourceType: resourceType ?? this.resourceType,
      resourceId: resourceId ?? this.resourceId,
      eventData: eventData ?? this.eventData,
      eventMetadata: eventMetadata ?? this.eventMetadata,
      riskScore: riskScore ?? this.riskScore,
      isAnomaly: isAnomaly ?? this.isAnomaly,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Check if this is a high-risk event
  bool get isHighRisk => riskScore >= 70;

  /// Check if this is a critical event
  bool get isCritical => eventSeverity == SecurityEventSeverity.critical;

  /// Get display time ago
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Get formatted timestamp
  String get formattedTimestamp {
    return createdAt.toIso8601String();
  }

  /// Check if location information is available
  bool get hasLocationInfo => geoLocation != null;

  /// Get short device info from user agent
  String? get shortDeviceInfo {
    if (userAgent == null) return null;
    
    final ua = userAgent!.toLowerCase();
    if (ua.contains('mobile') || ua.contains('android') || ua.contains('iphone')) {
      return 'Mobile';
    } else if (ua.contains('tablet') || ua.contains('ipad')) {
      return 'Tablet';
    } else {
      return 'Desktop';
    }
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        userId,
        eventType,
        eventCategory,
        eventDescription,
        eventSeverity,
        ipAddress,
        userAgent,
        deviceFingerprint,
        geoLocation,
        sessionId,
        requestId,
        resourceType,
        resourceId,
        eventData,
        eventMetadata,
        riskScore,
        isAnomaly,
        createdAt,
      ];

  @override
  bool get stringify => true;

  /// Create commonly used audit log entries
  static SecurityAuditLog createLoginAttempt({
    required String organizationId,
    required String userId,
    required String ipAddress,
    required String userAgent,
    required bool successful,
    GeoLocation? geoLocation,
    String? sessionId,
    Map<String, dynamic>? eventData,
  }) {
    return SecurityAuditLog(
      id: '',
      organizationId: organizationId,
      userId: userId,
      eventType: successful ? 'login_success' : 'login_failed',
      eventCategory: SecurityEventCategory.authentication,
      eventDescription: successful 
          ? 'User successfully logged in'
          : 'Failed login attempt',
      eventSeverity: successful 
          ? SecurityEventSeverity.low 
          : SecurityEventSeverity.medium,
      ipAddress: ipAddress,
      userAgent: userAgent,
      geoLocation: geoLocation,
      sessionId: sessionId,
      eventData: eventData,
      riskScore: successful ? 0 : 30,
      isAnomaly: false,
      createdAt: DateTime.now(),
    );
  }

  static SecurityAuditLog createMFAEvent({
    required String organizationId,
    required String userId,
    required String eventType,
    required String eventDescription,
    required String ipAddress,
    String? sessionId,
    Map<String, dynamic>? eventData,
  }) {
    return SecurityAuditLog(
      id: '',
      organizationId: organizationId,
      userId: userId,
      eventType: eventType,
      eventCategory: SecurityEventCategory.mfa,
      eventDescription: eventDescription,
      eventSeverity: SecurityEventSeverity.medium,
      ipAddress: ipAddress,
      sessionId: sessionId,
      eventData: eventData,
      riskScore: 20,
      isAnomaly: false,
      createdAt: DateTime.now(),
    );
  }

  static SecurityAuditLog createSuspiciousActivity({
    required String organizationId,
    String? userId,
    required String eventType,
    required String eventDescription,
    required String ipAddress,
    String? userAgent,
    GeoLocation? geoLocation,
    required int riskScore,
    Map<String, dynamic>? eventData,
  }) {
    return SecurityAuditLog(
      id: '',
      organizationId: organizationId,
      userId: userId,
      eventType: eventType,
      eventCategory: SecurityEventCategory.suspicious,
      eventDescription: eventDescription,
      eventSeverity: riskScore >= 70 
          ? SecurityEventSeverity.critical 
          : SecurityEventSeverity.high,
      ipAddress: ipAddress,
      userAgent: userAgent,
      geoLocation: geoLocation,
      eventData: eventData,
      riskScore: riskScore,
      isAnomaly: true,
      createdAt: DateTime.now(),
    );
  }
}