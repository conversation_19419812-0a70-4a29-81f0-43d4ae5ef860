// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Reward _$RewardFromJson(Map<String, dynamic> json) => Reward(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  type: $enumDecode(_$RewardTypeEnumMap, json['type']),
  rarity: $enumDecode(_$RewardRarityEnumMap, json['rarity']),
  icon: json['icon'] as String,
  pointsCost: (json['pointsCost'] as num).toInt(),
  unlockMethod: $enumDecode(_$RewardUnlockMethodEnumMap, json['unlockMethod']),
  unlockRequirement: json['unlockRequirement'] as String?,
  isAvailable: json['isAvailable'] as bool,
  isLimited: json['isLimited'] as bool,
  isReusable: json['isReusable'] as bool,
  maxQuantity: (json['maxQuantity'] as num?)?.toInt(),
  effectData: json['effectData'] as Map<String, dynamic>?,
  availableFrom: json['availableFrom'] == null
      ? null
      : DateTime.parse(json['availableFrom'] as String),
  availableUntil: json['availableUntil'] == null
      ? null
      : DateTime.parse(json['availableUntil'] as String),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$RewardToJson(Reward instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'type': _$RewardTypeEnumMap[instance.type]!,
  'rarity': _$RewardRarityEnumMap[instance.rarity]!,
  'icon': instance.icon,
  'pointsCost': instance.pointsCost,
  'unlockMethod': _$RewardUnlockMethodEnumMap[instance.unlockMethod]!,
  'unlockRequirement': instance.unlockRequirement,
  'isAvailable': instance.isAvailable,
  'isLimited': instance.isLimited,
  'isReusable': instance.isReusable,
  'maxQuantity': instance.maxQuantity,
  'effectData': instance.effectData,
  'availableFrom': instance.availableFrom?.toIso8601String(),
  'availableUntil': instance.availableUntil?.toIso8601String(),
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$RewardTypeEnumMap = {
  RewardType.virtual: 'virtual',
  RewardType.privilege: 'privilege',
  RewardType.cosmetic: 'cosmetic',
  RewardType.functionality: 'functionality',
  RewardType.recognition: 'recognition',
};

const _$RewardRarityEnumMap = {
  RewardRarity.common: 'common',
  RewardRarity.uncommon: 'uncommon',
  RewardRarity.rare: 'rare',
  RewardRarity.epic: 'epic',
  RewardRarity.legendary: 'legendary',
};

const _$RewardUnlockMethodEnumMap = {
  RewardUnlockMethod.achievement: 'achievement',
  RewardUnlockMethod.points: 'points',
  RewardUnlockMethod.level: 'level',
  RewardUnlockMethod.quest: 'quest',
  RewardUnlockMethod.streak: 'streak',
  RewardUnlockMethod.specialEvent: 'special_event',
};
