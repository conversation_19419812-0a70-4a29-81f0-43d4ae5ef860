/// Response utilities for consistent API responses
library;

import 'dart:convert';
import 'package:shelf/shelf.dart';

/// Utility class for creating consistent API responses
class ResponseUtils {
  /// Creates a successful JSON response
  static Response success({
    Object? data,
    String? message,
    int statusCode = 200,
    Map<String, String>? headers,
  }) {
    final responseData = <String, dynamic>{
      'success': true,
      'data': data,
      if (message != null) 'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response(
      statusCode,
      body: jsonEncode(responseData),
      headers: {
        'content-type': 'application/json',
        ...?headers,
      },
    );
  }

  /// Creates an error JSON response
  static Response error({
    required String message,
    Object? details,
    int statusCode = 400,
    String? errorCode,
    Map<String, String>? headers,
  }) {
    final responseData = <String, dynamic>{
      'success': false,
      'error': {
        'message': message,
        if (details != null) 'details': details,
        if (errorCode != null) 'code': errorCode,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response(
      statusCode,
      body: jsonEncode(responseData),
      headers: {
        'content-type': 'application/json',
        ...?headers,
      },
    );
  }

  /// Creates a validation error response
  static Response validationError({
    required String message,
    Map<String, List<String>>? fieldErrors,
    Map<String, String>? headers,
  }) {
    final responseData = <String, dynamic>{
      'success': false,
      'error': {
        'message': message,
        'type': 'validation_error',
        if (fieldErrors != null) 'field_errors': fieldErrors,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response(
      422,
      body: jsonEncode(responseData),
      headers: {
        'content-type': 'application/json',
        ...?headers,
      },
    );
  }

  /// Creates an unauthorized response
  static Response unauthorized({
    String message = 'Unauthorized',
    Map<String, String>? headers,
  }) {
    return error(
      message: message,
      statusCode: 401,
      errorCode: 'UNAUTHORIZED',
      headers: headers,
    );
  }

  /// Creates a forbidden response
  static Response forbidden({
    String message = 'Forbidden',
    Map<String, String>? headers,
  }) {
    return error(
      message: message,
      statusCode: 403,
      errorCode: 'FORBIDDEN',
      headers: headers,
    );
  }

  /// Creates a not found response
  static Response notFound({
    String message = 'Resource not found',
    Map<String, String>? headers,
  }) {
    return error(
      message: message,
      statusCode: 404,
      errorCode: 'NOT_FOUND',
      headers: headers,
    );
  }

  /// Creates a bad request response
  static Response badRequest({
    String message = 'Bad request',
    Object? details,
    Map<String, String>? headers,
  }) {
    return error(
      message: message,
      details: details,
      statusCode: 400,
      errorCode: 'BAD_REQUEST',
      headers: headers,
    );
  }

  /// Creates an internal server error response
  static Response internalServerError({
    String message = 'Internal server error',
    Object? details,
    Map<String, String>? headers,
  }) {
    return error(
      message: message,
      details: details,
      statusCode: 500,
      errorCode: 'INTERNAL_SERVER_ERROR',
      headers: headers,
    );
  }

  /// Creates a paginated response
  static Response paginated({
    required List<dynamic> items,
    required int page,
    required int limit,
    required int total,
    String? message,
    Map<String, String>? headers,
  }) {
    final totalPages = (total / limit).ceil();
    final hasNext = page < totalPages;
    final hasPrevious = page > 1;

    final responseData = <String, dynamic>{
      'success': true,
      'data': {
        'items': items,
        'pagination': {
          'page': page,
          'limit': limit,
          'total': total,
          'total_pages': totalPages,
          'has_next': hasNext,
          'has_previous': hasPrevious,
        },
      },
      if (message != null) 'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response(
      200,
      body: jsonEncode(responseData),
      headers: {
        'content-type': 'application/json',
        ...?headers,
      },
    );
  }

  /// Creates a no content response
  static Response noContent({
    Map<String, String>? headers,
  }) {
    return Response(
      204,
      headers: headers,
    );
  }

  /// Creates a created response
  static Response created({
    Object? data,
    String? message,
    String? location,
    Map<String, String>? headers,
  }) {
    final responseHeaders = <String, String>{
      'content-type': 'application/json',
      if (location != null) 'location': location,
      ...?headers,
    };

    return success(
      data: data,
      message: message ?? 'Resource created successfully',
      statusCode: 201,
      headers: responseHeaders,
    );
  }

  /// Creates an accepted response
  static Response accepted({
    Object? data,
    String? message,
    Map<String, String>? headers,
  }) {
    return success(
      data: data,
      message: message ?? 'Request accepted for processing',
      statusCode: 202,
      headers: headers,
    );
  }

  /// Extracts JSON body from request
  static Future<Map<String, dynamic>> extractJsonBody(Request request) async {
    try {
      final body = await request.readAsString();
      if (body.isEmpty) {
        throw const FormatException('Request body is empty');
      }
      return jsonDecode(body) as Map<String, dynamic>;
    } catch (e) {
      throw FormatException('Invalid JSON in request body: $e');
    }
  }

  /// Extracts query parameters with type conversion
  static Map<String, dynamic> extractQueryParams(Request request) {
    final params = <String, dynamic>{};
    
    for (final entry in request.url.queryParameters.entries) {
      final key = entry.key;
      final value = entry.value;
      
      // Try to convert to appropriate types
      if (value.toLowerCase() == 'true') {
        params[key] = true;
      } else if (value.toLowerCase() == 'false') {
        params[key] = false;
      } else if (int.tryParse(value) != null) {
        params[key] = int.parse(value);
      } else if (double.tryParse(value) != null) {
        params[key] = double.parse(value);
      } else {
        params[key] = value;
      }
    }
    
    return params;
  }

  /// Validates required fields in request body
  static void validateRequiredFields(
    Map<String, dynamic> body,
    List<String> requiredFields,
  ) {
    final missingFields = <String>[];
    
    for (final field in requiredFields) {
      if (!body.containsKey(field) || body[field] == null) {
        missingFields.add(field);
      }
    }
    
    if (missingFields.isNotEmpty) {
      throw ArgumentError(
        'Missing required fields: ${missingFields.join(', ')}',
      );
    }
  }

  /// Creates CORS headers
  static Map<String, String> corsHeaders({
    String origin = '*',
    String methods = 'GET, POST, PUT, DELETE, OPTIONS',
    String headers = 'Content-Type, Authorization',
  }) {
    return {
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': methods,
      'Access-Control-Allow-Headers': headers,
      'Access-Control-Max-Age': '86400',
    };
  }

  /// Creates an OPTIONS response for CORS preflight
  static Response options({
    String origin = '*',
    String methods = 'GET, POST, PUT, DELETE, OPTIONS',
    String headers = 'Content-Type, Authorization',
  }) {
    return Response(
      200,
      headers: corsHeaders(
        origin: origin,
        methods: methods,
        headers: headers,
      ),
    );
  }
}
