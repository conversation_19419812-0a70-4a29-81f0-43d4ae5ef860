// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalyticsMetrics _$AnalyticsMetricsFromJson(Map<String, dynamic> json) =>
    AnalyticsMetrics(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      metricName: json['metricName'] as String,
      metricCategory: json['metricCategory'] as String,
      metricValue: (json['metricValue'] as num).toDouble(),
      metricCount: (json['metricCount'] as num).toInt(),
      aggregationType: $enumDecode(
        _$MetricAggregationTypeEnumMap,
        json['aggregationType'],
      ),
      timePeriod: $enumDecode(_$TimePeriodEnumMap, json['timePeriod']),
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
      dimensions: json['dimensions'] as Map<String, dynamic>,
      metadata: json['metadata'] as Map<String, dynamic>,
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
    );

Map<String, dynamic> _$AnalyticsMetricsToJson(
  AnalyticsMetrics instance,
) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'metricName': instance.metricName,
  'metricCategory': instance.metricCategory,
  'metricValue': instance.metricValue,
  'metricCount': instance.metricCount,
  'aggregationType': _$MetricAggregationTypeEnumMap[instance.aggregationType]!,
  'timePeriod': _$TimePeriodEnumMap[instance.timePeriod]!,
  'periodStart': instance.periodStart.toIso8601String(),
  'periodEnd': instance.periodEnd.toIso8601String(),
  'dimensions': instance.dimensions,
  'metadata': instance.metadata,
  'calculatedAt': instance.calculatedAt.toIso8601String(),
};

const _$MetricAggregationTypeEnumMap = {
  MetricAggregationType.sum: 'sum',
  MetricAggregationType.average: 'average',
  MetricAggregationType.count: 'count',
  MetricAggregationType.distinctCount: 'distinct_count',
  MetricAggregationType.min: 'min',
  MetricAggregationType.max: 'max',
  MetricAggregationType.median: 'median',
  MetricAggregationType.percentile: 'percentile',
};

const _$TimePeriodEnumMap = {
  TimePeriod.hourly: 'hourly',
  TimePeriod.daily: 'daily',
  TimePeriod.weekly: 'weekly',
  TimePeriod.monthly: 'monthly',
  TimePeriod.quarterly: 'quarterly',
  TimePeriod.yearly: 'yearly',
};

DashboardMetrics _$DashboardMetricsFromJson(Map<String, dynamic> json) =>
    DashboardMetrics(
      organizationId: json['organizationId'] as String,
      timeRange: DateTimeRange.fromJson(
        json['timeRange'] as Map<String, dynamic>,
      ),
      activeUsers: (json['activeUsers'] as num).toInt(),
      totalEvents: (json['totalEvents'] as num).toInt(),
      taskCompletionRate: (json['taskCompletionRate'] as num).toDouble(),
      questCompletionRate: (json['questCompletionRate'] as num).toDouble(),
      avgEngagementScore: (json['avgEngagementScore'] as num).toDouble(),
      achievementUnlocks: (json['achievementUnlocks'] as num).toInt(),
      collaborationActions: (json['collaborationActions'] as num).toInt(),
      topUsers: (json['topUsers'] as List<dynamic>)
          .map((e) => UserMetricsSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      trends: (json['trends'] as List<dynamic>)
          .map((e) => MetricTrend.fromJson(e as Map<String, dynamic>))
          .toList(),
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
    );

Map<String, dynamic> _$DashboardMetricsToJson(DashboardMetrics instance) =>
    <String, dynamic>{
      'organizationId': instance.organizationId,
      'timeRange': instance.timeRange,
      'activeUsers': instance.activeUsers,
      'totalEvents': instance.totalEvents,
      'taskCompletionRate': instance.taskCompletionRate,
      'questCompletionRate': instance.questCompletionRate,
      'avgEngagementScore': instance.avgEngagementScore,
      'achievementUnlocks': instance.achievementUnlocks,
      'collaborationActions': instance.collaborationActions,
      'topUsers': instance.topUsers,
      'trends': instance.trends,
      'calculatedAt': instance.calculatedAt.toIso8601String(),
    };

DateTimeRange _$DateTimeRangeFromJson(Map<String, dynamic> json) =>
    DateTimeRange(
      start: DateTime.parse(json['start'] as String),
      end: DateTime.parse(json['end'] as String),
    );

Map<String, dynamic> _$DateTimeRangeToJson(DateTimeRange instance) =>
    <String, dynamic>{
      'start': instance.start.toIso8601String(),
      'end': instance.end.toIso8601String(),
    };

UserMetricsSummary _$UserMetricsSummaryFromJson(Map<String, dynamic> json) =>
    UserMetricsSummary(
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userEmail: json['userEmail'] as String?,
      totalActions: (json['totalActions'] as num).toInt(),
      tasksCompleted: (json['tasksCompleted'] as num).toInt(),
      questsCompleted: (json['questsCompleted'] as num).toInt(),
      achievementsUnlocked: (json['achievementsUnlocked'] as num).toInt(),
      engagementScore: (json['engagementScore'] as num).toDouble(),
      lastActivity: json['lastActivity'] == null
          ? null
          : DateTime.parse(json['lastActivity'] as String),
    );

Map<String, dynamic> _$UserMetricsSummaryToJson(UserMetricsSummary instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'userEmail': instance.userEmail,
      'totalActions': instance.totalActions,
      'tasksCompleted': instance.tasksCompleted,
      'questsCompleted': instance.questsCompleted,
      'achievementsUnlocked': instance.achievementsUnlocked,
      'engagementScore': instance.engagementScore,
      'lastActivity': instance.lastActivity?.toIso8601String(),
    };

MetricTrend _$MetricTrendFromJson(Map<String, dynamic> json) => MetricTrend(
  metricName: json['metricName'] as String,
  currentValue: (json['currentValue'] as num).toDouble(),
  previousValue: (json['previousValue'] as num).toDouble(),
  percentageChange: (json['percentageChange'] as num).toDouble(),
  direction: $enumDecode(_$TrendDirectionEnumMap, json['direction']),
  isSignificant: json['isSignificant'] as bool,
);

Map<String, dynamic> _$MetricTrendToJson(MetricTrend instance) =>
    <String, dynamic>{
      'metricName': instance.metricName,
      'currentValue': instance.currentValue,
      'previousValue': instance.previousValue,
      'percentageChange': instance.percentageChange,
      'direction': _$TrendDirectionEnumMap[instance.direction]!,
      'isSignificant': instance.isSignificant,
    };

const _$TrendDirectionEnumMap = {
  TrendDirection.up: 'up',
  TrendDirection.down: 'down',
  TrendDirection.stable: 'stable',
};
