import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'logging_service.dart';

/// Production configuration service for managing external service integrations
/// Handles validation, setup, and monitoring of SendGrid, Twilio, and other services
class ProductionConfigService {
  static final ProductionConfigService _instance = ProductionConfigService._internal();
  factory ProductionConfigService() => _instance;
  ProductionConfigService._internal();

  // Configuration cache
  final Map<String, dynamic> _configCache = {};
  bool _isInitialized = false;

  /// Initialize production configuration
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.info('🔧 Initializing production configuration...');
      
      await _loadEnvironmentConfig();
      await _validateRequiredServices();
      await _testServiceConnections();
      
      _isInitialized = true;
      LoggingService.info('✅ Production configuration initialized successfully');
      
    } catch (e) {
      LoggingService.error('❌ Failed to initialize production configuration: $e');
      rethrow;
    }
  }

  /// Load environment configuration
  Future<void> _loadEnvironmentConfig() async {
    final environment = Platform.environment['DART_ENV'] ?? 'development';
    
    _configCache['environment'] = environment;
    _configCache['sendgrid'] = _loadSendGridConfig();
    _configCache['twilio'] = _loadTwilioConfig();
    _configCache['database'] = _loadDatabaseConfig();
    _configCache['redis'] = _loadRedisConfig();
    _configCache['security'] = _loadSecurityConfig();
    _configCache['monitoring'] = _loadMonitoringConfig();
    
    LoggingService.info('📋 Configuration loaded for environment: $environment');
  }

  /// Load SendGrid configuration
  Map<String, dynamic> _loadSendGridConfig() {
    return {
      'enabled': _getBoolEnv('EMAIL_ENABLED', false),
      'provider': _getEnv('EMAIL_PROVIDER', 'mock'),
      'api_key': _getEnv('SENDGRID_API_KEY', ''),
      'from_email': _getEnv('SENDGRID_FROM_EMAIL', '<EMAIL>'),
      'from_name': _getEnv('SENDGRID_FROM_NAME', 'Quester'),
      'reply_to': _getEnv('SENDGRID_REPLY_TO', '<EMAIL>'),
      'templates': {
        'welcome': _getEnv('SENDGRID_TEMPLATE_WELCOME', ''),
        'verification': _getEnv('SENDGRID_TEMPLATE_VERIFICATION', ''),
        'password_reset': _getEnv('SENDGRID_TEMPLATE_PASSWORD_RESET', ''),
        'mfa_code': _getEnv('SENDGRID_TEMPLATE_MFA_CODE', ''),
        'security_alert': _getEnv('SENDGRID_TEMPLATE_SECURITY_ALERT', ''),
        'achievement_unlock': _getEnv('SENDGRID_TEMPLATE_ACHIEVEMENT_UNLOCK', ''),
        'quest_reminder': _getEnv('SENDGRID_TEMPLATE_QUEST_REMINDER', ''),
        'team_invitation': _getEnv('SENDGRID_TEMPLATE_TEAM_INVITATION', ''),
      },
      'rate_limits': {
        'per_hour': _getIntEnv('EMAIL_RATE_LIMIT_PER_HOUR', 100),
        'per_day': _getIntEnv('EMAIL_RATE_LIMIT_PER_DAY', 1000),
      },
    };
  }

  /// Load Twilio configuration
  Map<String, dynamic> _loadTwilioConfig() {
    return {
      'enabled': _getBoolEnv('SMS_ENABLED', false),
      'provider': _getEnv('SMS_PROVIDER', 'mock'),
      'account_sid': _getEnv('TWILIO_ACCOUNT_SID', ''),
      'auth_token': _getEnv('TWILIO_AUTH_TOKEN', ''),
      'from_phone': _getEnv('TWILIO_FROM_PHONE', ''),
      'messaging_service_sid': _getEnv('TWILIO_MESSAGING_SERVICE_SID', ''),
      'verify_service_sid': _getEnv('TWILIO_VERIFY_SERVICE_SID', ''),
      'webhook_url': _getEnv('TWILIO_WEBHOOK_URL', ''),
      'status_callback_url': _getEnv('TWILIO_STATUS_CALLBACK_URL', ''),
      'rate_limits': {
        'per_hour': _getIntEnv('SMS_RATE_LIMIT_PER_HOUR', 20),
        'per_day': _getIntEnv('SMS_RATE_LIMIT_PER_DAY', 100),
      },
    };
  }

  /// Load database configuration
  Map<String, dynamic> _loadDatabaseConfig() {
    return {
      'url': _getEnv('DATABASE_URL', ''),
      'host': _getEnv('POSTGRES_HOST', 'localhost'),
      'port': _getIntEnv('POSTGRES_PORT', 5432),
      'user': _getEnv('POSTGRES_USER', 'quester'),
      'password': _getEnv('POSTGRES_PASSWORD', ''),
      'database': _getEnv('POSTGRES_DB', 'quester'),
      'ssl_mode': _getEnv('POSTGRES_SSL_MODE', 'prefer'),
      'pool': {
        'min_connections': _getIntEnv('DB_POOL_MIN_CONNECTIONS', 5),
        'max_connections': _getIntEnv('DB_POOL_MAX_CONNECTIONS', 20),
        'connection_timeout': _getIntEnv('DB_CONNECTION_TIMEOUT', 30000),
        'idle_timeout': _getIntEnv('DB_IDLE_TIMEOUT', 600000),
      },
    };
  }

  /// Load Redis configuration
  Map<String, dynamic> _loadRedisConfig() {
    return {
      'url': _getEnv('REDIS_URL', ''),
      'host': _getEnv('REDIS_HOST', 'localhost'),
      'port': _getIntEnv('REDIS_PORT', 6379),
      'password': _getEnv('REDIS_PASSWORD', ''),
      'database': _getIntEnv('REDIS_DB', 0),
      'tls': _getBoolEnv('REDIS_TLS', false),
    };
  }

  /// Load security configuration
  Map<String, dynamic> _loadSecurityConfig() {
    return {
      'jwt_secret': _getEnv('JWT_SECRET', ''),
      'jwt_expires_in': _getEnv('JWT_EXPIRES_IN', '24h'),
      'encryption_key': _getEnv('ENCRYPTION_KEY', ''),
      'api_key': _getEnv('API_KEY', ''),
      'session_secret': _getEnv('SESSION_SECRET', ''),
      'cors_origin': _getEnv('CORS_ORIGIN', '*'),
      'ssl_enabled': _getBoolEnv('SSL_ENABLED', false),
    };
  }

  /// Load monitoring configuration
  Map<String, dynamic> _loadMonitoringConfig() {
    return {
      'sentry_dsn': _getEnv('SENTRY_DSN', ''),
      'datadog_api_key': _getEnv('DATADOG_API_KEY', ''),
      'newrelic_license_key': _getEnv('NEWRELIC_LICENSE_KEY', ''),
      'performance_monitoring': _getBoolEnv('PERFORMANCE_MONITORING_ENABLED', true),
      'analytics_enabled': _getBoolEnv('ANALYTICS_ENABLED', false),
    };
  }

  /// Validate required services configuration
  Future<void> _validateRequiredServices() async {
    final environment = _configCache['environment'] as String;
    
    if (environment == 'production') {
      await _validateProductionRequirements();
    }
    
    await _validateSendGridConfig();
    await _validateTwilioConfig();
    await _validateSecurityConfig();
  }

  /// Validate production-specific requirements
  Future<void> _validateProductionRequirements() async {
    final requiredSecrets = [
      'JWT_SECRET',
      'ENCRYPTION_KEY',
      'DATABASE_URL',
    ];
    
    for (final secret in requiredSecrets) {
      final value = _getEnv(secret, '');
      if (value.isEmpty) {
        throw Exception('Required production secret not configured: $secret');
      }
      if (value.length < 32) {
        throw Exception('Production secret too short: $secret (minimum 32 characters)');
      }
    }
    
    LoggingService.info('✅ Production security requirements validated');
  }

  /// Validate SendGrid configuration
  Future<void> _validateSendGridConfig() async {
    final sendgridConfig = _configCache['sendgrid'] as Map<String, dynamic>;
    
    if (!sendgridConfig['enabled']) {
      LoggingService.info('📧 SendGrid is disabled, skipping validation');
      return;
    }
    
    final apiKey = sendgridConfig['api_key'] as String;
    if (apiKey.isEmpty) {
      throw Exception('SendGrid API key is required when email is enabled');
    }
    
    if (!apiKey.startsWith('SG.')) {
      throw Exception('Invalid SendGrid API key format');
    }
    
    LoggingService.info('✅ SendGrid configuration validated');
  }

  /// Validate Twilio configuration
  Future<void> _validateTwilioConfig() async {
    final twilioConfig = _configCache['twilio'] as Map<String, dynamic>;
    
    if (!twilioConfig['enabled']) {
      LoggingService.info('📱 Twilio is disabled, skipping validation');
      return;
    }
    
    final accountSid = twilioConfig['account_sid'] as String;
    final authToken = twilioConfig['auth_token'] as String;
    
    if (accountSid.isEmpty || authToken.isEmpty) {
      throw Exception('Twilio Account SID and Auth Token are required when SMS is enabled');
    }
    
    if (!accountSid.startsWith('AC')) {
      throw Exception('Invalid Twilio Account SID format');
    }
    
    LoggingService.info('✅ Twilio configuration validated');
  }

  /// Validate security configuration
  Future<void> _validateSecurityConfig() async {
    final securityConfig = _configCache['security'] as Map<String, dynamic>;
    
    final jwtSecret = securityConfig['jwt_secret'] as String;
    final encryptionKey = securityConfig['encryption_key'] as String;
    
    if (jwtSecret.isEmpty || encryptionKey.isEmpty) {
      throw Exception('JWT secret and encryption key are required');
    }
    
    LoggingService.info('✅ Security configuration validated');
  }

  /// Test service connections
  Future<void> _testServiceConnections() async {
    final futures = <Future>[];
    
    final sendgridConfig = _configCache['sendgrid'] as Map<String, dynamic>;
    if (sendgridConfig['enabled']) {
      futures.add(_testSendGridConnection());
    }
    
    final twilioConfig = _configCache['twilio'] as Map<String, dynamic>;
    if (twilioConfig['enabled']) {
      futures.add(_testTwilioConnection());
    }
    
    await Future.wait(futures);
  }

  /// Test SendGrid connection
  Future<void> _testSendGridConnection() async {
    try {
      final sendgridConfig = _configCache['sendgrid'] as Map<String, dynamic>;
      final apiKey = sendgridConfig['api_key'] as String;
      
      final response = await http.get(
        Uri.parse('https://api.sendgrid.com/v3/user/profile'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        LoggingService.info('✅ SendGrid connection test successful');
      } else {
        LoggingService.warning('⚠️ SendGrid connection test failed: ${response.statusCode}');
      }
      
    } catch (e) {
      LoggingService.warning('⚠️ SendGrid connection test failed: $e');
    }
  }

  /// Test Twilio connection
  Future<void> _testTwilioConnection() async {
    try {
      final twilioConfig = _configCache['twilio'] as Map<String, dynamic>;
      final accountSid = twilioConfig['account_sid'] as String;
      final authToken = twilioConfig['auth_token'] as String;
      
      final credentials = base64Encode(utf8.encode('$accountSid:$authToken'));
      final response = await http.get(
        Uri.parse('https://api.twilio.com/2010-04-01/Accounts/$accountSid.json'),
        headers: {
          'Authorization': 'Basic $credentials',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        LoggingService.info('✅ Twilio connection test successful');
      } else {
        LoggingService.warning('⚠️ Twilio connection test failed: ${response.statusCode}');
      }
      
    } catch (e) {
      LoggingService.warning('⚠️ Twilio connection test failed: $e');
    }
  }

  /// Get configuration value
  T getConfig<T>(String path, T defaultValue) {
    final keys = path.split('.');
    dynamic current = _configCache;
    
    for (final key in keys) {
      if (current is Map && current.containsKey(key)) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current as T? ?? defaultValue;
  }

  /// Get environment variable as string
  String _getEnv(String key, String defaultValue) {
    return Platform.environment[key] ?? defaultValue;
  }

  /// Get environment variable as integer
  int _getIntEnv(String key, int defaultValue) {
    final value = Platform.environment[key];
    return value != null ? int.tryParse(value) ?? defaultValue : defaultValue;
  }

  /// Get environment variable as boolean
  bool _getBoolEnv(String key, bool defaultValue) {
    final value = Platform.environment[key]?.toLowerCase();
    if (value == null) return defaultValue;
    return value == 'true' || value == '1' || value == 'yes';
  }

  /// Check if service is properly configured
  bool isServiceConfigured(String serviceName) {
    switch (serviceName.toLowerCase()) {
      case 'sendgrid':
        return getConfig<bool>('sendgrid.enabled', false) &&
               getConfig<String>('sendgrid.api_key', '').isNotEmpty;
      case 'twilio':
        return getConfig<bool>('twilio.enabled', false) &&
               getConfig<String>('twilio.account_sid', '').isNotEmpty;
      default:
        return false;
    }
  }

  /// Get service health status
  Map<String, dynamic> getServiceHealthStatus() {
    return {
      'sendgrid': {
        'configured': isServiceConfigured('sendgrid'),
        'enabled': getConfig<bool>('sendgrid.enabled', false),
      },
      'twilio': {
        'configured': isServiceConfigured('twilio'),
        'enabled': getConfig<bool>('twilio.enabled', false),
      },
      'database': {
        'configured': getConfig<String>('database.url', '').isNotEmpty,
      },
      'redis': {
        'configured': getConfig<String>('redis.url', '').isNotEmpty,
      },
    };
  }
}
