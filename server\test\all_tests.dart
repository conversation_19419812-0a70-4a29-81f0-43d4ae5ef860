import 'package:test/test.dart';
import 'dart:io';

// Import all test suites
import 'unit/database_service_test.dart' as database_tests;
import 'unit/auth_service_test.dart' as auth_tests;
import 'unit/websocket_service_test.dart' as websocket_tests;
import 'unit/persistence_coordinator_test.dart' as persistence_tests;
import 'unit/migration_service_test.dart' as migration_tests;

import 'integration/api_integration_test.dart' as api_tests;
import 'integration/realtime_integration_test.dart' as realtime_tests;
import 'integration/gamification_integration_test.dart' as gamification_tests;

import 'e2e/user_journey_test.dart' as e2e_tests;
import 'e2e/freelancing_workflow_test.dart' as freelancing_e2e_tests;
import 'e2e/learning_workflow_test.dart' as learning_e2e_tests;

void main() {
  setUpAll(() async {
    print('🧪 Starting Quester Test Suite');
    print('=' * 60);
    
    // Set test environment variables
    Platform.environment['NODE_ENV'] = 'test';
    Platform.environment['DB_NAME'] = 'questerdb_test';
    Platform.environment['REDIS_DB'] = '1'; // Use different Redis DB for tests
    
    print('✅ Test environment configured');
  });

  tearDownAll(() {
    print('=' * 60);
    print('🎉 Test Suite Complete');
  });

  group('Unit Tests', () {
    print('\n📋 Running Unit Tests...\n');
    
    database_tests.main();
    auth_tests.main();
    websocket_tests.main();
    persistence_tests.main();
    migration_tests.main();
  });

  group('Integration Tests', () {
    print('\n🔗 Running Integration Tests...\n');
    
    api_tests.main();
    realtime_tests.main();
    gamification_tests.main();
  });

  group('End-to-End Tests', () {
    print('\n🎭 Running End-to-End Tests...\n');
    
    e2e_tests.main();
    freelancing_e2e_tests.main();
    learning_e2e_tests.main();
  });
}