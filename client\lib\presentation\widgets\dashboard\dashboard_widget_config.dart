/// Dashboard widget configuration and types
library;

import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// Types of dashboard widgets
enum DashboardWidgetType {
  chart,
  metric,
  list,
  progress,
  progressBar,
  calendar,
  activity,
  leaderboard,
  achievements,
  tasks,
  notifications,
  statsCard,
}

/// Configuration for dashboard widgets
class DashboardWidgetConfig extends Equatable {
  final String id;
  final String title;
  final DashboardWidgetType type;
  final Map<String, dynamic> settings;
  final Map<String, dynamic>? data;
  final int gridWidth;
  final int gridHeight;
  final bool isVisible;
  final int order;

  const DashboardWidgetConfig({
    required this.id,
    required this.title,
    required this.type,
    this.settings = const {},
    this.data,
    this.gridWidth = 1,
    this.gridHeight = 1,
    this.isVisible = true,
    this.order = 0,
  });

  /// Create a copy with updated values
  DashboardWidgetConfig copyWith({
    String? id,
    String? title,
    DashboardWidgetType? type,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? data,
    int? gridWidth,
    int? gridHeight,
    bool? isVisible,
    int? order,
  }) {
    return DashboardWidgetConfig(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      settings: settings ?? this.settings,
      data: data ?? this.data,
      gridWidth: gridWidth ?? this.gridWidth,
      gridHeight: gridHeight ?? this.gridHeight,
      isVisible: isVisible ?? this.isVisible,
      order: order ?? this.order,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type.name,
      'settings': settings,
      'data': data,
      'gridWidth': gridWidth,
      'gridHeight': gridHeight,
      'isVisible': isVisible,
      'order': order,
    };
  }

  /// Create from JSON
  factory DashboardWidgetConfig.fromJson(Map<String, dynamic> json) {
    return DashboardWidgetConfig(
      id: json['id'] as String,
      title: json['title'] as String,
      type: DashboardWidgetType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => DashboardWidgetType.metric,
      ),
      settings: json['settings'] as Map<String, dynamic>? ?? {},
      data: json['data'] as Map<String, dynamic>?,
      gridWidth: json['gridWidth'] as int? ?? 1,
      gridHeight: json['gridHeight'] as int? ?? 1,
      isVisible: json['isVisible'] as bool? ?? true,
      order: json['order'] as int? ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        type,
        settings,
        data,
        gridWidth,
        gridHeight,
        isVisible,
        order,
      ];
}

/// Predefined dashboard widget configurations
class DashboardWidgetConfigs {
  static const List<DashboardWidgetConfig> defaultConfigs = [
    DashboardWidgetConfig(
      id: 'total_points',
      title: 'Total Points',
      type: DashboardWidgetType.metric,
      gridWidth: 1,
      gridHeight: 1,
      order: 1,
    ),
    DashboardWidgetConfig(
      id: 'active_quests',
      title: 'Active Quests',
      type: DashboardWidgetType.metric,
      gridWidth: 1,
      gridHeight: 1,
      order: 2,
    ),
    DashboardWidgetConfig(
      id: 'completed_tasks',
      title: 'Completed Tasks',
      type: DashboardWidgetType.metric,
      gridWidth: 1,
      gridHeight: 1,
      order: 3,
    ),
    DashboardWidgetConfig(
      id: 'achievements',
      title: 'Recent Achievements',
      type: DashboardWidgetType.achievements,
      gridWidth: 2,
      gridHeight: 2,
      order: 4,
    ),
    DashboardWidgetConfig(
      id: 'progress_chart',
      title: 'Progress Chart',
      type: DashboardWidgetType.chart,
      gridWidth: 2,
      gridHeight: 2,
      order: 5,
    ),
    DashboardWidgetConfig(
      id: 'leaderboard',
      title: 'Leaderboard',
      type: DashboardWidgetType.leaderboard,
      gridWidth: 1,
      gridHeight: 2,
      order: 6,
    ),
  ];
}
