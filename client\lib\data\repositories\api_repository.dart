import 'package:shared/shared.dart';

import '../../core/services/auth_service.dart';
import '../../core/models/api_response.dart' as client_api;
import '../datasources/remote_data_source.dart';

/// Main repository for API operations
class ApiRepository {
  final RemoteDataSource remoteDataSource;
  final AuthService authService;

  ApiRepository({
    required this.remoteDataSource,
    required this.authService,
  });

  String? get _token => authService.currentToken;
  String? get _userId => authService.currentUser?.id;

  // Authentication methods
  Future<client_api.ApiResponse<User>> login(String email, String password) async {
    final response = await remoteDataSource.login(email, password);
    
    if (response.isSuccess && response.data != null) {
      try {
        final data = response.data!;
        final user = User.fromJson(data['user']);
        final token = data['token'] as String;
        final refreshToken = data['refresh_token'] as String;
        
        await authService.login(token, refreshToken, user);
        
        return client_api.ApiResponse.success(user);
      } catch (e) {
        return client_api.ApiResponse.error('Failed to parse login response: $e');
      }
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Login failed');
  }

  Future<client_api.ApiResponse<User>> register(String email, String password, String name) async {
    final response = await remoteDataSource.register(email, password, name);
    
    if (response.isSuccess && response.data != null) {
      try {
        final data = response.data!;
        final user = User.fromJson(data['user']);
        final token = data['token'] as String;
        final refreshToken = data['refresh_token'] as String;
        
        await authService.login(token, refreshToken, user);
        
        return client_api.ApiResponse.success(user);
      } catch (e) {
        return client_api.ApiResponse.error('Failed to parse register response: $e');
      }
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Registration failed');
  }

  Future<void> logout() async {
    await authService.logout();
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> checkAuthStatus() async {
    if (_token == null) {
      return client_api.ApiResponse.error('No token available');
    }

    final response = await remoteDataSource.checkAuthStatus(_token!);
    return response;
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> requestPasswordReset(String email) async {
    return await remoteDataSource.forgotPassword(email);
  }

  Future<client_api.ApiResponse<User>> updateProfile(Map<String, dynamic> profileData) async {
    if (_token == null || _userId == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.updateProfile(_userId!, profileData, _token!);

    if (response.isSuccess && response.data != null) {
      try {
        final user = User.fromJson(response.data!);
        await authService.updateUser(user);
        return client_api.ApiResponse.success(user);
      } catch (e) {
        return client_api.ApiResponse.error('Failed to parse updated profile: $e');
      }
    }

    return client_api.ApiResponse.error(response.error ?? 'Failed to update profile');
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> refreshToken() async {
    final currentRefreshToken = authService.refreshTokenValue;
    if (currentRefreshToken == null) {
      return client_api.ApiResponse.error('No refresh token available');
    }

    final response = await remoteDataSource.refreshToken(currentRefreshToken);

    if (response.isSuccess && response.data != null) {
      try {
        final data = response.data!;
        final token = data['token'] as String;
        final refreshToken = data['refresh_token'] as String;

        await authService.updateTokens(token, refreshToken);

        return client_api.ApiResponse.success(data);
      } catch (e) {
        return client_api.ApiResponse.error('Failed to parse token response: $e');
      }
    }

    return client_api.ApiResponse.error(response.error ?? 'Failed to refresh token');
  }

  // Gamification methods
  Future<client_api.ApiResponse<UserPoints>> getUserPoints() async {
    if (_token == null || _userId == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.getUserPoints(_userId!, _token!);
    
    if (response.isSuccess && response.data != null) {
      try {
        return client_api.ApiResponse.success(UserPoints.fromJson(response.data!));
      } catch (e) {
        return client_api.ApiResponse.error('Failed to parse user points: $e');
      }
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Failed to get user points');
  }

  // Notification methods
  Future<GetNotificationsResponse> getNotifications(GetNotificationsRequest request) async {
    if (_token == null || _userId == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.getNotifications(_userId!, request, _token!);

    if (response.isSuccess && response.data != null) {
      return GetNotificationsResponse.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to get notifications');
  }

  Future<NotificationPreferences> getNotificationPreferences() async {
    if (_token == null || _userId == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.getNotificationPreferences(_userId!, _token!);

    if (response.isSuccess && response.data != null) {
      return NotificationPreferences.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to get notification preferences');
  }

  Future<void> updateNotificationPreferences(UpdateNotificationPreferencesRequest request) async {
    if (_token == null || _userId == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.updateNotificationPreferences(_userId!, request, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to update notification preferences');
    }
  }

  Future<void> markNotificationsAsRead(List<String> notificationIds) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.markNotificationsAsRead(notificationIds, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to mark notifications as read');
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.deleteNotification(notificationId, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to delete notification');
    }
  }

  Future<Notification> createNotification(CreateNotificationRequest request) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.createNotification(request, _token!);

    if (response.isSuccess && response.data != null) {
      return Notification.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to create notification');
  }

  Future<Notification> createNotificationFromTemplate(CreateNotificationFromTemplateRequest request) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.createNotificationFromTemplate(request, _token!);

    if (response.isSuccess && response.data != null) {
      return Notification.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to create notification from template');
  }

  Future<void> registerDevice(RegisterDeviceRequest request) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.registerDevice(request, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to register device');
    }
  }

  Future<NotificationStatsResponse> getNotificationStats() async {
    if (_token == null || _userId == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.getNotificationStats(_userId!, _token!);

    if (response.isSuccess && response.data != null) {
      return NotificationStatsResponse.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to get notification stats');
  }

  // Quest methods
  Future<List<Quest>> getQuests({Map<String, String>? filters}) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.getQuests(_token!, filters: filters);

    if (response.isSuccess && response.data != null) {
      final questsData = response.data as List;
      return questsData.map((questJson) => Quest.fromJson(questJson as Map<String, dynamic>)).toList();
    }

    throw Exception(response.error ?? 'Failed to get quests');
  }

  Future<Quest> getQuestById(String questId) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.getQuest(questId, _token!);

    if (response.isSuccess && response.data != null) {
      return Quest.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to get quest');
  }

  Future<Quest> createQuest(Map<String, dynamic> questData) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.createQuest(questData, _token!);

    if (response.isSuccess && response.data != null) {
      return Quest.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to create quest');
  }

  Future<Quest> updateQuest(String questId, Map<String, dynamic> questData) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.updateQuest(questId, questData, _token!);

    if (response.isSuccess && response.data != null) {
      return Quest.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to update quest');
  }

  Future<void> updateQuestStatus(String questId, QuestStatus status) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.updateQuestStatus(questId, status, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to update quest status');
    }
  }

  Future<void> updateQuestPriority(String questId, QuestPriority priority) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.updateQuestPriority(questId, priority, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to update quest priority');
    }
  }

  Future<void> deleteQuest(String questId) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.deleteQuest(questId, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to delete quest');
    }
  }

  Future<Task> addTaskToQuest(String questId, Map<String, dynamic> taskData) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.addTaskToQuest(questId, taskData, _token!);

    if (response.isSuccess && response.data != null) {
      return Task.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to add task to quest');
  }

  Future<Task> updateTask(String taskId, Map<String, dynamic> taskData) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.updateTask(taskId, taskData, _token!);

    if (response.isSuccess && response.data != null) {
      return Task.fromJson(response.data!);
    }

    throw Exception(response.error ?? 'Failed to update task');
  }

  Future<void> deleteTask(String taskId) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.deleteTask(taskId, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to delete task');
    }
  }

  Future<void> assignQuestToUser(String questId, String userId) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.assignQuestToUser(questId, userId, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to assign quest to user');
    }
  }

  Future<void> unassignQuestFromUser(String questId, String userId) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.unassignQuestFromUser(questId, userId, _token!);

    if (!response.isSuccess) {
      throw Exception(response.error ?? 'Failed to unassign quest from user');
    }
  }

  Future<List<Quest>> searchQuests(String query, {Map<String, String>? filters}) async {
    if (_token == null) {
      throw Exception('User not authenticated');
    }

    final response = await remoteDataSource.searchQuests(query, _token!, filters: filters);

    if (response.isSuccess && response.data != null) {
      final questsData = response.data as List;
      return questsData.map((questJson) => Quest.fromJson(questJson as Map<String, dynamic>)).toList();
    }

    throw Exception(response.error ?? 'Failed to search quests');
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getUserStats() async {
    if (_token == null || _userId == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getUserStats(_userId!, _token!);
  }

  Future<client_api.ApiResponse<List<Achievement>>> getUserAchievements() async {
    if (_token == null || _userId == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.getUserAchievements(_userId!, _token!);
    
    if (response.isSuccess && response.data != null) {
      try {
        final achievements = (response.data as List)
            .map((json) => Achievement.fromJson(json))
            .toList();
        return client_api.ApiResponse.success(achievements);
      } catch (e) {
        return client_api.ApiResponse.error('Failed to parse achievements: $e');
      }
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Failed to get achievements');
  }

  Future<client_api.ApiResponse<List<Map<String, dynamic>>>> getLeaderboard() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.getLeaderboard(_token!);
    
    if (response.isSuccess && response.data != null) {
      return client_api.ApiResponse.success(List<Map<String, dynamic>>.from(response.data as List));
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Failed to get leaderboard');
  }

  Future<client_api.ApiResponse<List<Map<String, dynamic>>>> getRewards() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.getRewards(_token!);

    if (response.isSuccess && response.data != null) {
      return client_api.ApiResponse.success(List<Map<String, dynamic>>.from(response.data as List));
    }

    return client_api.ApiResponse.error(response.error ?? 'Failed to get rewards');
  }

  // Freelancing methods
  Future<client_api.ApiResponse<List<Map<String, dynamic>>>> getProjects({Map<String, String>? filters}) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.getProjects(_token!, filters: filters);
    
    if (response.isSuccess && response.data != null) {
      return client_api.ApiResponse.success(List<Map<String, dynamic>>.from(response.data as List));
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Failed to get projects');
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getProject(String projectId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getProject(projectId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createProject(Map<String, dynamic> projectData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.createProject(projectData, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getFreelancerProfile() async {
    if (_token == null || _userId == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getFreelancerProfile(_userId!, _token!);
  }

  // Learning methods
  Future<client_api.ApiResponse<List<Map<String, dynamic>>>> getCourses({Map<String, String>? filters}) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.getCourses(_token!, filters: filters);
    
    if (response.isSuccess && response.data != null) {
      return client_api.ApiResponse.success(List<Map<String, dynamic>>.from(response.data as List));
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Failed to get courses');
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getCourse(String courseId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getCourse(courseId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> enrollInCourse(String courseId) async {
    if (_token == null || _userId == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.enrollInCourse(courseId, _userId!, _token!);
  }

  Future<client_api.ApiResponse<List<Map<String, dynamic>>>> getUserEnrollments() async {
    if (_token == null || _userId == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    final response = await remoteDataSource.getUserEnrollments(_userId!, _token!);
    
    if (response.isSuccess && response.data != null) {
      return client_api.ApiResponse.success(List<Map<String, dynamic>>.from(response.data as List));
    }
    
    return client_api.ApiResponse.error(response.error ?? 'Failed to get enrollments');
  }

  // Analytics methods
  Future<client_api.ApiResponse<Map<String, dynamic>>> trackEvent(Map<String, dynamic> eventData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.trackEvent(eventData, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getDashboard(String userId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getDashboard(userId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getMetrics({Map<String, String>? filters}) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getMetrics(_token!, filters: filters);
  }

  Future<client_api.ApiResponse<List<dynamic>>> getInsights({Map<String, String>? params}) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getInsights(_token!, params: params);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> generateReport(Map<String, dynamic> reportData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.generateReport(reportData, _token!);
  }

  // Collaboration methods
  Future<client_api.ApiResponse<Map<String, dynamic>>> createTeamQuest(Map<String, dynamic> questData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.createTeamQuest(questData, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> joinTeamQuest(String questId, String userId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.joinTeamQuest(questId, userId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getTeamQuestStatus(String questId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getTeamQuestStatus(questId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> sendTeamMessage(String questId, Map<String, dynamic> messageData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.sendTeamMessage(questId, messageData, _token!);
  }

  Future<client_api.ApiResponse<List<dynamic>>> getTeamMessages(String questId, {int limit = 50, int offset = 0}) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getTeamMessages(questId, _token!, limit: limit, offset: offset);
  }

  Future<client_api.ApiResponse<List<dynamic>>> getUserTeamQuests(String userId, {String? status}) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getUserTeamQuests(userId, _token!, status: status);
  }

  // Enterprise methods
  Future<client_api.ApiResponse<Map<String, dynamic>>> createOrganization(Map<String, dynamic> orgData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.createOrganization(orgData, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getOrganization(String orgId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getOrganization(orgId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateOrganization(String orgId, Map<String, dynamic> updateData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.updateOrganization(orgId, updateData, _token!);
  }

  Future<client_api.ApiResponse<List<dynamic>>> listUserOrganizations(String userId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.listUserOrganizations(userId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createMember(String orgId, Map<String, dynamic> memberData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.createMember(orgId, memberData, _token!);
  }

  Future<client_api.ApiResponse<List<dynamic>>> listMembers(String orgId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.listMembers(orgId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateMember(String orgId, String memberId, Map<String, dynamic> updateData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.updateMember(orgId, memberId, updateData, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> deleteMember(String orgId, String memberId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.deleteMember(orgId, memberId, _token!);
  }

  Future<client_api.ApiResponse<List<dynamic>>> listRoles(String orgId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.listRoles(orgId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> createRole(String orgId, Map<String, dynamic> roleData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.createRole(orgId, roleData, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> updateRole(String orgId, String roleId, Map<String, dynamic> updateData) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.updateRole(orgId, roleId, updateData, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> deleteRole(String orgId, String roleId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.deleteRole(orgId, roleId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getEnterpriseAnalyticsSummary(String orgId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getEnterpriseAnalyticsSummary(orgId, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getEnterpriseAnalytics(String orgId, {Map<String, String>? params}) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getEnterpriseAnalytics(orgId, _token!, params: params);
  }

  // Advanced Auth methods
  Future<client_api.ApiResponse<Map<String, dynamic>>> verifyEmail(String token, String verificationCode) async {
    return await remoteDataSource.verifyEmail(token, verificationCode);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> resendVerification(String email) async {
    return await remoteDataSource.resendVerification(email);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> forgotPassword(String email) async {
    return await remoteDataSource.forgotPassword(email);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> resetPassword(String token, String newPassword) async {
    return await remoteDataSource.resetPassword(token, newPassword);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> changePassword(String oldPassword, String newPassword) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.changePassword(oldPassword, newPassword, _token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> enableTwoFactor() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.enableTwoFactor(_token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> verifyTwoFactor(String code) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.verifyTwoFactor(code, _token!);
  }

  Future<client_api.ApiResponse<List<dynamic>>> getUserSessions() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getUserSessions(_token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> revokeSession(String sessionId) async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.revokeSession(sessionId, _token!);
  }

  // Monitoring methods
  Future<client_api.ApiResponse<Map<String, dynamic>>> getPerformanceMetrics() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getPerformanceMetrics(_token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getSystemInfo() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getSystemInfo(_token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getHealthCheck() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getHealthCheck(_token!);
  }

  Future<client_api.ApiResponse<Map<String, dynamic>>> getEndpointPerformance() async {
    if (_token == null) {
      return client_api.ApiResponse.error('User not authenticated');
    }

    return await remoteDataSource.getEndpointPerformance(_token!);
  }
}