/// Threat Detection API Handlers
library;

/// HTTP request handlers for advanced threat detection and security monitoring.
/// Provides endpoints for configuring threat detection, viewing security alerts,
/// and managing security policies.
/// 
/// Key Features:
/// - Real-time threat monitoring dashboard
/// - Threat detection configuration management
/// - Security alert management and investigation
/// - Threat intelligence integration
/// - Automated response configuration
/// - Security metrics and reporting

import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/database_service.dart';
import '../services/threat_detection_service.dart';
import 'package:shared/shared.dart';

/// Threat Detection API Handlers
class ThreatDetectionHandlers {
  // ignore: unused_field
  final DatabaseService _databaseService;
  final ThreatDetectionService _threatDetectionService;

  ThreatDetectionHandlers(this._databaseService, this._threatDetectionService);

  /// Configure routes
  Router get router {
    final router = Router();

    // Threat monitoring and alerts
    router.get('/threats/<organizationId>', getThreats);
    router.get('/threats/<organizationId>/<threatId>', getThreat);
    router.put('/threats/<organizationId>/<threatId>/status', updateThreatStatus);
    router.post('/threats/<organizationId>/<threatId>/investigate', investigateThreat);
    router.delete('/threats/<organizationId>/<threatId>', dismissThreat);

    // Threat detection configuration
    router.get('/config/<organizationId>', getThreatConfig);
    router.put('/config/<organizationId>', updateThreatConfig);
    router.post('/config/<organizationId>/test', testThreatConfig);

    // Security analytics and reporting
    router.get('/analytics/<organizationId>/stats', getThreatStats);
    router.get('/analytics/<organizationId>/trends', getThreatTrends);
    router.get('/analytics/<organizationId>/dashboard', getSecurityDashboard);

    // Behavioral analysis
    router.get('/behavior/<organizationId>/users/<userId>', getUserBehaviorAnalysis);
    router.post('/behavior/<organizationId>/analyze', analyzeBehaviorPatterns);

    // Real-time monitoring
    router.get('/monitoring/<organizationId>/status', getMonitoringStatus);
    router.post('/monitoring/<organizationId>/alert', triggerSecurityAlert);

    return router;
  }

  /// Get all threats for an organization
  Future<Response> getThreats(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final status = request.url.queryParameters['status'];
      final severity = request.url.queryParameters['severity'];
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
      final offset = int.tryParse(request.url.queryParameters['offset'] ?? '0') ?? 0;

      // Mock threats - in production this would query the database
      final mockThreats = _generateMockThreats(organizationId, limit, offset);

      // Filter by status if provided
      var filteredThreats = mockThreats;
      if (status != null) {
        filteredThreats = mockThreats.where((t) => t['status'] == status).toList();
      }

      // Filter by severity if provided
      if (severity != null) {
        filteredThreats = filteredThreats.where((t) => t['severity'] == severity).toList();
      }

      return Response.ok(
        json.encode({
          'success': true,
          'threats': filteredThreats,
          'pagination': {
            'total': 157,
            'limit': limit,
            'offset': offset,
            'has_more': offset + limit < 157,
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to retrieve threats: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get a specific threat
  Future<Response> getThreat(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final threatId = request.params['threatId']!;

      // Mock threat details - in production this would query the database
      final threat = {
        'id': threatId,
        'organization_id': organizationId,
        'user_id': 'user_123',
        'threat_type': 'suspiciousLogin',
        'severity': 'high',
        'title': 'HIGH: Suspicious Login Activity',
        'description': 'Multiple failed login attempts detected from suspicious IP address',
        'source_ip': '************',
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'geolocation': {
          'country': 'Unknown',
          'city': 'Unknown',
          'latitude': 0.0,
          'longitude': 0.0,
        },
        'indicators': [
          {
            'type': 'brute_force',
            'value': '************',
            'severity': 'high',
            'description': 'Multiple failed login attempts detected from this IP',
            'confidence': 0.89,
          },
          {
            'type': 'malicious_ip',
            'value': '************',
            'severity': 'critical',
            'description': 'IP address appears in threat intelligence feeds',
            'confidence': 0.95,
          }
        ],
        'risk_score': 0.82,
        'confidence_score': 0.92,
        'status': 'detected',
        'mitigation_actions': ['block_ip', 'alert_admin', 'require_mfa'],
        'assigned_to': null,
        'resolved_at': null,
        'detected_at': DateTime.now().subtract(const Duration(minutes: 30)).toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'investigation_notes': [],
        'related_threats': ['threat_456', 'threat_789'],
      };

      return Response.ok(
        json.encode({
          'success': true,
          'threat': threat,
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to retrieve threat: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Update threat status
  Future<Response> updateThreatStatus(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final threatId = request.params['threatId']!;
      final requestBody = await request.readAsString();
      final data = json.decode(requestBody) as Map<String, dynamic>;

      final newStatus = data['status'] as String;
      final assignedTo = data['assigned_to'] as String?;
      final notes = data['notes'] as String?;

      // Mock status update - in production this would update the database
      print('Updating threat $threatId status to $newStatus');
      
      final response = {
        'success': true,
        'threat_id': threatId,
        'status': newStatus,
        'assigned_to': assignedTo,
        'updated_at': DateTime.now().toIso8601String(),
        'message': 'Threat status updated successfully',
      };

      if (notes != null) {
        response['investigation_notes'] = [
          {
            'note': notes,
            'author': assignedTo ?? 'system',
            'timestamp': DateTime.now().toIso8601String(),
          }
        ];
      }

      return Response.ok(
        json.encode(response),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to update threat status: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get threat detection configuration
  Future<Response> getThreatConfig(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final config = await _threatDetectionService.getThreatConfig(organizationId);

      return Response.ok(
        json.encode({
          'success': true,
          'config': config.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to retrieve threat config: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Update threat detection configuration
  Future<Response> updateThreatConfig(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final requestBody = await request.readAsString();
      final data = json.decode(requestBody) as Map<String, dynamic>;

      // Validate configuration
      final validationErrors = _validateThreatConfig(data);
      if (validationErrors.isNotEmpty) {
        return Response.badRequest(
          body: json.encode({
            'success': false,
            'error': 'Configuration validation failed',
            'details': validationErrors,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final config = ThreatDetectionConfig.fromJson(data);
      await _threatDetectionService.updateThreatConfig(config);

      return Response.ok(
        json.encode({
          'success': true,
          'message': 'Threat detection configuration updated successfully',
          'config': config.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to update threat config: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get threat statistics
  Future<Response> getThreatStats(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final stats = await _threatDetectionService.getThreatStatistics(organizationId);

      return Response.ok(
        json.encode({
          'success': true,
          'statistics': stats.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to retrieve threat statistics: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get security dashboard data
  Future<Response> getSecurityDashboard(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;

      // Mock dashboard data - in production this would aggregate from multiple sources
      final dashboardData = {
        'organization_id': organizationId,
        'security_score': 85,
        'threat_level': 'medium',
        'active_threats': 12,
        'critical_alerts': 3,
        'recent_incidents': [
          {
            'id': 'incident_001',
            'type': 'Brute Force Attack',
            'severity': 'high',
            'status': 'investigating',
            'timestamp': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
          },
          {
            'id': 'incident_002',
            'type': 'Suspicious Login',
            'severity': 'medium',
            'status': 'resolved',
            'timestamp': DateTime.now().subtract(const Duration(hours: 6)).toIso8601String(),
          }
        ],
        'security_metrics': {
          'blocked_attempts': 147,
          'successful_blocks': 134,
          'false_positives': 13,
          'average_response_time': '15 minutes',
          'detection_accuracy': '92%',
        },
        'top_threat_sources': [
          {'source': '203.0.113.0/24', 'count': 23, 'type': 'Brute Force'},
          {'source': '198.51.100.0/24', 'count': 18, 'type': 'Suspicious Login'},
          {'source': 'Unknown Location', 'count': 15, 'type': 'Geo Anomaly'},
        ],
        'compliance_status': {
          'policies_active': 15,
          'policies_total': 18,
          'compliance_score': 83,
          'last_audit': DateTime.now().subtract(const Duration(days: 7)).toIso8601String(),
        },
        'generated_at': DateTime.now().toIso8601String(),
      };

      return Response.ok(
        json.encode({
          'success': true,
          'dashboard': dashboardData,
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to retrieve security dashboard: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get user behavior analysis
  Future<Response> getUserBehaviorAnalysis(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final userId = request.params['userId']!;

      final analysis = await _threatDetectionService.analyzeBehaviorPattern(organizationId, userId);

      return Response.ok(
        json.encode({
          'success': true,
          'analysis': analysis,
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to analyze user behavior: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get monitoring status
  Future<Response> getMonitoringStatus(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;

      final monitoringStatus = {
        'organization_id': organizationId,
        'monitoring_enabled': true,
        'last_health_check': DateTime.now().toIso8601String(),
        'detection_engines': {
          'brute_force_detection': {'status': 'active', 'last_update': DateTime.now().toIso8601String()},
          'geo_anomaly_detection': {'status': 'active', 'last_update': DateTime.now().toIso8601String()},
          'device_fingerprinting': {'status': 'active', 'last_update': DateTime.now().toIso8601String()},
          'ip_reputation': {'status': 'active', 'last_update': DateTime.now().toIso8601String()},
          'behavioral_analysis': {'status': 'active', 'last_update': DateTime.now().toIso8601String()},
        },
        'alert_channels': {
          'email': {'status': 'active', 'last_test': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String()},
          'slack': {'status': 'active', 'last_test': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String()},
          'sms': {'status': 'inactive', 'last_test': null},
        },
        'performance_metrics': {
          'average_detection_time': '2.3 seconds',
          'false_positive_rate': '8%',
          'system_load': 'normal',
          'memory_usage': '45%',
        }
      };

      return Response.ok(
        json.encode({
          'success': true,
          'monitoring': monitoringStatus,
        }),
        headers: {'Content-Type': 'application/json'},
      );

    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to retrieve monitoring status: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Helper methods

  /// Generate mock threats for testing
  List<Map<String, dynamic>> _generateMockThreats(String organizationId, int limit, int offset) {
    final threats = <Map<String, dynamic>>[];
    final threatTypes = ['bruteForce', 'suspiciousLogin', 'geolocationAnomaly', 'deviceAnomaly', 'anomalousAccess'];
    final severities = ['low', 'medium', 'high', 'critical'];
    final statuses = ['detected', 'investigating', 'mitigated', 'resolved', 'falsePositive'];

    for (int i = 0; i < limit; i++) {
      final index = offset + i;
      final threatType = threatTypes[index % threatTypes.length];
      final severity = severities[index % severities.length];
      final status = statuses[index % statuses.length];

      threats.add({
        'id': 'threat_${1000 + index}',
        'organization_id': organizationId,
        'user_id': 'user_${100 + (index % 50)}',
        'threat_type': threatType,
        'severity': severity,
        'title': '${severity.toUpperCase()}: ${_getThreatTypeTitle(threatType)}',
        'source_ip': '203.0.113.${(index % 255) + 1}',
        'risk_score': ((index % 100) + 1) / 100.0,
        'confidence_score': ((index % 90) + 10) / 100.0,
        'status': status,
        'detected_at': DateTime.now().subtract(Duration(hours: index % 72)).toIso8601String(),
        'updated_at': DateTime.now().subtract(Duration(minutes: index % 120)).toIso8601String(),
      });
    }

    return threats;
  }

  /// Get threat type title
  String _getThreatTypeTitle(String type) {
    switch (type) {
      case 'bruteForce':
        return 'Brute Force Attack Detected';
      case 'suspiciousLogin':
        return 'Suspicious Login Activity';
      case 'geolocationAnomaly':
        return 'Unusual Geographic Access';
      case 'deviceAnomaly':
        return 'Unrecognized Device Access';
      default:
        return 'Security Anomaly Detected';
    }
  }

  /// Validate threat detection configuration
  List<String> _validateThreatConfig(Map<String, dynamic> config) {
    final errors = <String>[];

    // Validate sensitivity level
    final sensitivity = config['sensitivity_level'];
    if (sensitivity != null && (sensitivity < 0.0 || sensitivity > 1.0)) {
      errors.add('Sensitivity level must be between 0.0 and 1.0');
    }

    // Validate notification settings
    final notifications = config['notification_settings'];
    if (notifications != null) {
      final emailEnabled = notifications['email_enabled'] as bool?;
      final emailRecipients = notifications['email_recipients'] as List?;
      if (emailEnabled == true && (emailRecipients == null || emailRecipients.isEmpty)) {
        errors.add('Email recipients required when email notifications are enabled');
      }
    }

    // Validate IP addresses in whitelist/blacklist
    final whitelistIPs = config['whitelist_ips'] as List?;
    if (whitelistIPs != null) {
      for (final ip in whitelistIPs) {
        if (!_isValidIPRange(ip as String)) {
          errors.add('Invalid IP range in whitelist: $ip');
        }
      }
    }

    return errors;
  }

  /// Validate IP address or CIDR range
  bool _isValidIPRange(String ipRange) {
    // Simple validation - in production this would be more comprehensive
    return ipRange.contains('.') && (ipRange.split('.').length == 4 || ipRange.contains('/'));
  }

  /// Investigate a threat
  Future<Response> investigateThreat(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final threatId = request.params['threatId']!;
      final body = await request.readAsString();
      final data = json.decode(body) as Map<String, dynamic>;

      // Mock investigation start
      final investigation = {
        'id': 'inv_${DateTime.now().millisecondsSinceEpoch}',
        'threat_id': threatId,
        'organization_id': organizationId,
        'status': 'in_progress',
        'investigator': data['investigator'] ?? 'system',
        'notes': data['notes'] ?? '',
        'started_at': DateTime.now().toIso8601String(),
        'estimated_completion': DateTime.now().add(const Duration(hours: 2)).toIso8601String(),
      };

      return Response.ok(
        json.encode({
          'success': true,
          'investigation': investigation,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to start investigation: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Dismiss a threat
  Future<Response> dismissThreat(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final threatId = request.params['threatId']!;

      // Mock dismissal
      return Response.ok(
        json.encode({
          'success': true,
          'message': 'Threat dismissed successfully',
          'threat_id': threatId,
          'dismissed_at': DateTime.now().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to dismiss threat: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Test threat detection configuration
  Future<Response> testThreatConfig(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final body = await request.readAsString();
      // ignore: unused_local_variable
      final config = json.decode(body) as Map<String, dynamic>;

      // Mock test results
      final testResults = {
        'organization_id': organizationId,
        'test_started_at': DateTime.now().toIso8601String(),
        'test_duration': '45 seconds',
        'results': {
          'email_notifications': {'status': 'success', 'message': 'Test email sent successfully'},
          'slack_notifications': {'status': 'success', 'message': 'Test Slack message sent'},
          'detection_rules': {'status': 'success', 'message': 'All detection rules validated'},
          'thresholds': {'status': 'warning', 'message': 'Low threshold may cause false positives'},
        },
        'overall_status': 'passed_with_warnings',
      };

      return Response.ok(
        json.encode({
          'success': true,
          'test_results': testResults,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to test configuration: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get threat trends
  Future<Response> getThreatTrends(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final period = request.url.queryParameters['period'] ?? '30d';

      // Mock trend data
      final trends = {
        'organization_id': organizationId,
        'period': period,
        'trends': [
          {
            'date': DateTime.now().subtract(const Duration(days: 29)).toIso8601String().substring(0, 10),
            'threats_detected': 12,
            'threats_mitigated': 10,
            'false_positives': 2,
          },
          {
            'date': DateTime.now().subtract(const Duration(days: 22)).toIso8601String().substring(0, 10),
            'threats_detected': 18,
            'threats_mitigated': 16,
            'false_positives': 1,
          },
          {
            'date': DateTime.now().subtract(const Duration(days: 15)).toIso8601String().substring(0, 10),
            'threats_detected': 25,
            'threats_mitigated': 23,
            'false_positives': 3,
          },
          {
            'date': DateTime.now().subtract(const Duration(days: 8)).toIso8601String().substring(0, 10),
            'threats_detected': 31,
            'threats_mitigated': 28,
            'false_positives': 2,
          },
          {
            'date': DateTime.now().subtract(const Duration(days: 1)).toIso8601String().substring(0, 10),
            'threats_detected': 19,
            'threats_mitigated': 17,
            'false_positives': 1,
          }
        ],
        'summary': {
          'total_threats': 105,
          'total_mitigated': 94,
          'total_false_positives': 9,
          'detection_accuracy': '91.4%',
          'trend_direction': 'increasing',
        },
        'generated_at': DateTime.now().toIso8601String(),
      };

      return Response.ok(
        json.encode({
          'success': true,
          'trends': trends,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to retrieve threat trends: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Analyze behavior patterns
  Future<Response> analyzeBehaviorPatterns(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final body = await request.readAsString();
      final data = json.decode(body) as Map<String, dynamic>;

      // Mock behavior analysis
      final analysis = {
        'organization_id': organizationId,
        'analysis_type': data['type'] ?? 'comprehensive',
        'time_period': data['period'] ?? '7d',
        'patterns': [
          {
            'pattern_type': 'login_time_anomaly',
            'description': 'Unusual login times detected for 3 users',
            'severity': 'medium',
            'affected_users': 3,
            'risk_score': 0.45,
          },
          {
            'pattern_type': 'device_switching',
            'description': 'Rapid device switching pattern detected',
            'severity': 'high',
            'affected_users': 1,
            'risk_score': 0.72,
          },
          {
            'pattern_type': 'geolocation_anomaly',
            'description': 'Impossible travel patterns detected',
            'severity': 'critical',
            'affected_users': 2,
            'risk_score': 0.89,
          }
        ],
        'recommendations': [
          'Enable additional MFA for users with high-risk patterns',
          'Implement device registration requirements',
          'Set up geolocation-based access controls'
        ],
        'analyzed_at': DateTime.now().toIso8601String(),
      };

      return Response.ok(
        json.encode({
          'success': true,
          'analysis': analysis,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to analyze behavior patterns: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Trigger security alert
  Future<Response> triggerSecurityAlert(Request request) async {
    try {
      // ignore: unused_local_variable
      final organizationId = request.params['organizationId']!;
      final body = await request.readAsString();
      final data = json.decode(body) as Map<String, dynamic>;

      // Mock alert triggering
      final alert = {
        'id': 'alert_${DateTime.now().millisecondsSinceEpoch}',
        'organization_id': organizationId,
        'alert_type': data['type'] ?? 'manual',
        'severity': data['severity'] ?? 'medium',
        'title': data['title'] ?? 'Manual Security Alert',
        'description': data['description'] ?? 'Manual alert triggered by administrator',
        'triggered_by': data['triggered_by'] ?? 'system',
        'triggered_at': DateTime.now().toIso8601String(),
        'status': 'active',
        'notifications_sent': {
          'email': true,
          'slack': true,
          'sms': false,
        },
      };

      return Response.ok(
        json.encode({
          'success': true,
          'alert': alert,
          'message': 'Security alert triggered successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: json.encode({
          'success': false,
          'error': 'Failed to trigger security alert: $e',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}