import 'package:equatable/equatable.dart';

/// Analytics metric types
enum AnalyticsMetricType {
  userEngagement,
  questCompletion,
  teamPerformance,
  gamificationEngagement,
  systemUsage,
  businessImpact,
}

/// Analytics time period
enum AnalyticsTimePeriod {
  hour,
  day,
  week,
  month,
  quarter,
  year,
  custom,
}

/// Analytics metric aggregation type
enum AnalyticsAggregationType {
  sum,
  average,
  count,
  min,
  max,
  median,
  percentile,
}

/// Analytics metric data point
class AnalyticsDataPoint extends Equatable {
  /// Timestamp for this data point
  final DateTime timestamp;

  /// Metric value
  final double value;

  /// Additional metadata for this data point
  final Map<String, dynamic>? metadata;

  const AnalyticsDataPoint({
    required this.timestamp,
    required this.value,
    this.metadata,
  });

  /// Create AnalyticsDataPoint from JSON
  factory AnalyticsDataPoint.fromJson(Map<String, dynamic> json) {
    return AnalyticsDataPoint(
      timestamp: DateTime.parse(json['timestamp'] as String),
      value: (json['value'] as num).toDouble(),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
    );
  }

  /// Convert AnalyticsDataPoint to JSON
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'value': value,
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [timestamp, value, metadata];

  @override
  bool get stringify => true;
}

/// Analytics metric definition
class AnalyticsMetric extends Equatable {
  /// Unique metric identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Metric name
  final String name;

  /// Metric description
  final String? description;

  /// Metric type
  final AnalyticsMetricType type;

  /// Aggregation type
  final AnalyticsAggregationType aggregationType;

  /// Metric unit (e.g., 'count', 'percentage', 'seconds')
  final String unit;

  /// Metric tags for categorization
  final List<String> tags;

  /// Whether this metric is active
  final bool isActive;

  /// Metric configuration
  final Map<String, dynamic> config;

  /// Data retention period in days
  final int retentionDays;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const AnalyticsMetric({
    required this.id,
    required this.organizationId,
    required this.name,
    this.description,
    required this.type,
    required this.aggregationType,
    required this.unit,
    required this.tags,
    required this.isActive,
    required this.config,
    required this.retentionDays,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create AnalyticsMetric from JSON
  factory AnalyticsMetric.fromJson(Map<String, dynamic> json) {
    return AnalyticsMetric(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: AnalyticsMetricType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => AnalyticsMetricType.userEngagement,
      ),
      aggregationType: AnalyticsAggregationType.values.firstWhere(
        (e) => e.name == json['aggregationType'],
        orElse: () => AnalyticsAggregationType.count,
      ),
      unit: json['unit'] as String,
      tags: List<String>.from(json['tags'] as List),
      isActive: json['isActive'] as bool,
      config: Map<String, dynamic>.from(json['config'] as Map),
      retentionDays: json['retentionDays'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert AnalyticsMetric to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'name': name,
      'description': description,
      'type': type.name,
      'aggregationType': aggregationType.name,
      'unit': unit,
      'tags': tags,
      'isActive': isActive,
      'config': config,
      'retentionDays': retentionDays,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        description,
        type,
        aggregationType,
        unit,
        tags,
        isActive,
        config,
        retentionDays,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Analytics report definition
class AnalyticsReport extends Equatable {
  /// Unique report identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Report name
  final String name;

  /// Report description
  final String? description;

  /// Report type
  final String reportType;

  /// Metrics included in this report
  final List<String> metricIds;

  /// Report configuration
  final Map<String, dynamic> config;

  /// Report filters
  final Map<String, dynamic>? filters;

  /// Time period for the report
  final AnalyticsTimePeriod timePeriod;

  /// Custom time range (if timePeriod is custom)
  final Map<String, dynamic>? customTimeRange;

  /// Whether this report is active
  final bool isActive;

  /// Whether this report is scheduled
  final bool isScheduled;

  /// Schedule configuration
  final Map<String, dynamic>? scheduleConfig;

  /// Report recipients
  final List<String>? recipients;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Last generation timestamp
  final DateTime? lastGeneratedAt;

  const AnalyticsReport({
    required this.id,
    required this.organizationId,
    required this.name,
    this.description,
    required this.reportType,
    required this.metricIds,
    required this.config,
    this.filters,
    required this.timePeriod,
    this.customTimeRange,
    required this.isActive,
    required this.isScheduled,
    this.scheduleConfig,
    this.recipients,
    required this.createdAt,
    required this.updatedAt,
    this.lastGeneratedAt,
  });

  /// Create AnalyticsReport from JSON
  factory AnalyticsReport.fromJson(Map<String, dynamic> json) {
    return AnalyticsReport(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      reportType: json['reportType'] as String,
      metricIds: List<String>.from(json['metricIds'] as List),
      config: Map<String, dynamic>.from(json['config'] as Map),
      filters: json['filters'] != null
          ? Map<String, dynamic>.from(json['filters'] as Map)
          : null,
      timePeriod: AnalyticsTimePeriod.values.firstWhere(
        (e) => e.name == json['timePeriod'],
        orElse: () => AnalyticsTimePeriod.day,
      ),
      customTimeRange: json['customTimeRange'] != null
          ? Map<String, dynamic>.from(json['customTimeRange'] as Map)
          : null,
      isActive: json['isActive'] as bool,
      isScheduled: json['isScheduled'] as bool,
      scheduleConfig: json['scheduleConfig'] != null
          ? Map<String, dynamic>.from(json['scheduleConfig'] as Map)
          : null,
      recipients: json['recipients'] != null
          ? List<String>.from(json['recipients'] as List)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastGeneratedAt: json['lastGeneratedAt'] != null
          ? DateTime.parse(json['lastGeneratedAt'] as String)
          : null,
    );
  }

  /// Convert AnalyticsReport to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'name': name,
      'description': description,
      'reportType': reportType,
      'metricIds': metricIds,
      'config': config,
      'filters': filters,
      'timePeriod': timePeriod.name,
      'customTimeRange': customTimeRange,
      'isActive': isActive,
      'isScheduled': isScheduled,
      'scheduleConfig': scheduleConfig,
      'recipients': recipients,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastGeneratedAt': lastGeneratedAt?.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        description,
        reportType,
        metricIds,
        config,
        filters,
        timePeriod,
        customTimeRange,
        isActive,
        isScheduled,
        scheduleConfig,
        recipients,
        createdAt,
        updatedAt,
        lastGeneratedAt,
      ];

  @override
  bool get stringify => true;
}

/// Analytics dashboard model
class AnalyticsDashboard extends Equatable {
  /// Unique dashboard identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Dashboard name
  final String name;

  /// Dashboard description
  final String? description;

  /// Dashboard layout configuration
  final Map<String, dynamic> layout;

  /// Widgets included in this dashboard
  final List<Map<String, dynamic>> widgets;

  /// Dashboard filters
  final Map<String, dynamic>? filters;

  /// Whether this dashboard is public
  final bool isPublic;

  /// Dashboard access permissions
  final Map<String, dynamic>? accessPermissions;

  /// Dashboard configuration
  final Map<String, dynamic> config;

  /// Refresh interval in seconds
  final int? refreshInterval;

  /// Whether auto-refresh is enabled
  final bool autoRefresh;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Last access timestamp
  final DateTime? lastAccessedAt;

  const AnalyticsDashboard({
    required this.id,
    required this.organizationId,
    required this.name,
    this.description,
    required this.layout,
    required this.widgets,
    this.filters,
    required this.isPublic,
    this.accessPermissions,
    required this.config,
    this.refreshInterval,
    required this.autoRefresh,
    required this.createdAt,
    required this.updatedAt,
    this.lastAccessedAt,
  });

  /// Create AnalyticsDashboard from JSON
  factory AnalyticsDashboard.fromJson(Map<String, dynamic> json) {
    return AnalyticsDashboard(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      layout: Map<String, dynamic>.from(json['layout'] as Map),
      widgets: List<Map<String, dynamic>>.from(
        (json['widgets'] as List).map((w) => Map<String, dynamic>.from(w as Map)),
      ),
      filters: json['filters'] != null
          ? Map<String, dynamic>.from(json['filters'] as Map)
          : null,
      isPublic: json['isPublic'] as bool,
      accessPermissions: json['accessPermissions'] != null
          ? Map<String, dynamic>.from(json['accessPermissions'] as Map)
          : null,
      config: Map<String, dynamic>.from(json['config'] as Map),
      refreshInterval: json['refreshInterval'] as int?,
      autoRefresh: json['autoRefresh'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastAccessedAt: json['lastAccessedAt'] != null
          ? DateTime.parse(json['lastAccessedAt'] as String)
          : null,
    );
  }

  /// Convert AnalyticsDashboard to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'name': name,
      'description': description,
      'layout': layout,
      'widgets': widgets,
      'filters': filters,
      'isPublic': isPublic,
      'accessPermissions': accessPermissions,
      'config': config,
      'refreshInterval': refreshInterval,
      'autoRefresh': autoRefresh,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastAccessedAt': lastAccessedAt?.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        description,
        layout,
        widgets,
        filters,
        isPublic,
        accessPermissions,
        config,
        refreshInterval,
        autoRefresh,
        createdAt,
        updatedAt,
        lastAccessedAt,
      ];

  @override
  bool get stringify => true;
}
