import 'package:shared/shared.dart';
import 'package:test/test.dart';

// Import security model tests
import 'models/security/security_models_test.dart' as security_tests;

void main() {
  group('Shared Package Tests', () {
    setUp(() {
      // Initialize environment configuration for tests
      EnvironmentConfig.initialize(Environment.development);
    });

    test('User model creation and validation', () {
      final user = User.empty().copyWith(
        id: '123',
        email: '<EMAIL>',
        displayName: 'Test User',
      );
      
      expect(user.id, equals('123'));
      expect(user.email, equals('<EMAIL>'));
      expect(user.displayName, equals('Test User'));
      expect(user.role, equals(UserRole.newcomer));
    });

    test('ValidationUtils email validation', () {
      expect(ValidationUtils.validateEmail('<EMAIL>').isValid, isTrue);
      expect(ValidationUtils.validateEmail('invalid-email').isValid, isFalse);
      expect(ValidationUtils.validateEmail('').isValid, isFalse);
    });

    test('DateUtils formatting', () {
      final date = DateTime(2023, 12, 25, 15, 30);
      expect(DateUtils.formatIsoDate(date), equals('2023-12-25'));
      expect(DateUtils.formatTime(date), equals('15:30'));
    });

    test('GamificationConstants role multipliers', () {
      expect(GamificationConstants.getRoleMultiplier('newcomer'), equals(1.0));
      expect(GamificationConstants.getRoleMultiplier('expert'), equals(1.6));
      expect(GamificationConstants.getRoleMultiplier('mythic'), equals(3.0));
    });

    test('String extensions', () {
      expect('hello world'.titleCase, equals('Hello World'));
      expect('<EMAIL>'.isEmail, isTrue);
      expect('not-an-email'.isEmail, isFalse);
    });
  });

  // Run comprehensive security model tests
  group('Security Models', () {
    security_tests.main();
  });
}
