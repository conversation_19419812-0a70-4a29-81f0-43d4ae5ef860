import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'streak.g.dart';

/// Streak types for different activities
enum StreakType {
  @JsonValue('daily_login')
  dailyLogin,
  @JsonValue('daily_quest')
  dailyQuest,
  @JsonValue('daily_task')
  dailyTask,
  @JsonValue('weekly_goal')
  weeklyGoal,
  @JsonValue('monthly_challenge')
  monthlyChallenge,
  @JsonValue('custom')
  custom,
}

/// Streak status
enum StreakStatus {
  @JsonValue('active')
  active,
  @JsonValue('broken')
  broken,
  @JsonValue('paused')
  paused,
  @JsonValue('completed')
  completed,
}

/// Individual streak record for a specific activity
@JsonSerializable()
class Streak extends Equatable {
  /// Unique streak identifier
  final String id;

  /// User ID this streak belongs to
  final String userId;

  /// Type of streak
  final StreakType type;

  /// Current streak count
  final int currentStreak;

  /// Longest streak achieved for this type
  final int longestStreak;

  /// Total times this activity was completed
  final int totalCount;

  /// Current streak status
  final StreakStatus status;

  /// Last activity date
  final DateTime lastActivityDate;

  /// Streak start date (when current streak began)
  final DateTime streakStartDate;

  /// Target goal (e.g., maintain streak for X days)
  final int? targetGoal;

  /// Daily activity history (date -> completed)
  final Map<String, bool> activityHistory;

  /// Streak multiplier bonus
  final double multiplierBonus;

  /// Custom streak metadata
  final Map<String, dynamic>? metadata;

  /// Streak creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const Streak({
    required this.id,
    required this.userId,
    required this.type,
    required this.currentStreak,
    required this.longestStreak,
    required this.totalCount,
    required this.status,
    required this.lastActivityDate,
    required this.streakStartDate,
    this.targetGoal,
    required this.activityHistory,
    required this.multiplierBonus,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Streak from JSON
  factory Streak.fromJson(Map<String, dynamic> json) => _$StreakFromJson(json);

  /// Convert Streak to JSON
  Map<String, dynamic> toJson() => _$StreakToJson(this);

  /// Check if streak is currently active
  bool get isActive => status == StreakStatus.active;

  /// Check if streak is broken
  bool get isBroken => status == StreakStatus.broken;

  /// Check if activity was completed today
  bool get completedToday {
    final today = _formatDate(DateTime.now());
    return activityHistory[today] == true;
  }

  /// Check if streak is at risk (last activity was yesterday)
  bool get isAtRisk {
    if (!isActive) return false;
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final lastActivity = lastActivityDate;
    return !_isSameDay(lastActivity, DateTime.now()) && 
           _isSameDay(lastActivity, yesterday);
  }

  /// Check if streak is broken (missed activity for more than a day)
  bool get shouldBeBroken {
    if (!isActive) return false;
    final now = DateTime.now();
    final daysSinceLastActivity = now.difference(lastActivityDate).inDays;
    return daysSinceLastActivity > 1;
  }

  /// Get days since last activity
  int get daysSinceLastActivity {
    return DateTime.now().difference(lastActivityDate).inDays;
  }

  /// Get streak duration in days
  int get streakDurationDays {
    return DateTime.now().difference(streakStartDate).inDays + 1;
  }

  /// Get progress towards target goal (0.0 - 1.0)
  double get targetProgress {
    if (targetGoal == null || targetGoal! <= 0) return 0.0;
    return (currentStreak / targetGoal!).clamp(0.0, 1.0);
  }

  /// Check if target goal is reached
  bool get targetReached {
    return targetGoal != null && currentStreak >= targetGoal!;
  }

  /// Get activity completion rate over the last 30 days
  double get recentCompletionRate {
    final now = DateTime.now();
    int completedDays = 0;
    int totalDays = 0;

    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey = _formatDate(date);
      
      if (activityHistory.containsKey(dateKey)) {
        totalDays++;
        if (activityHistory[dateKey] == true) {
          completedDays++;
        }
      }
    }

    return totalDays > 0 ? completedDays / totalDays : 0.0;
  }

  /// Get current streak multiplier bonus
  double get currentMultiplier {
    if (!isActive) return 1.0;
    
    // Progressive multiplier: +0.1 for every 7 days, max 3.0x
    final weeklyBonuses = (currentStreak / 7).floor();
    final multiplier = 1.0 + (weeklyBonuses * 0.1);
    return multiplier.clamp(1.0, 3.0);
  }

  /// Get streak type display name
  String get typeDisplayName {
    switch (type) {
      case StreakType.dailyLogin:
        return 'Daily Login';
      case StreakType.dailyQuest:
        return 'Daily Quest';
      case StreakType.dailyTask:
        return 'Daily Task';
      case StreakType.weeklyGoal:
        return 'Weekly Goal';
      case StreakType.monthlyChallenge:
        return 'Monthly Challenge';
      case StreakType.custom:
        return 'Custom Streak';
    }
  }

  /// Get motivational message based on current streak
  String get motivationalMessage {
    if (!isActive) return 'Get back on track!';
    
    if (currentStreak == 1) return 'Great start! Keep it up!';
    if (currentStreak < 7) return 'Building momentum!';
    if (currentStreak < 30) return 'You\'re on fire! 🔥';
    if (currentStreak < 100) return 'Incredible dedication!';
    return 'Legendary streaker! 🏆';
  }

  /// Record activity completion for today
  Streak recordActivity() {
    final today = DateTime.now();
    final todayKey = _formatDate(today);
    
    // If already completed today, return unchanged
    if (activityHistory[todayKey] == true) return this;
    
    final newHistory = Map<String, bool>.from(activityHistory);
    newHistory[todayKey] = true;
    
    // Calculate new streak count
    int newCurrentStreak = currentStreak;
    if (isActive && !completedToday) {
      // Check if this extends the current streak
      final yesterday = today.subtract(const Duration(days: 1));
      final yesterdayKey = _formatDate(yesterday);
      
      if (currentStreak == 0 || activityHistory[yesterdayKey] == true) {
        newCurrentStreak = currentStreak + 1;
      }
    }
    
    final newLongestStreak = newCurrentStreak > longestStreak ? newCurrentStreak : longestStreak;
    
    return copyWith(
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      totalCount: totalCount + 1,
      status: StreakStatus.active,
      lastActivityDate: today,
      activityHistory: newHistory,
      multiplierBonus: currentMultiplier,
      updatedAt: today,
    );
  }

  /// Break the current streak
  Streak breakStreak() {
    return copyWith(
      currentStreak: 0,
      status: StreakStatus.broken,
      updatedAt: DateTime.now(),
    );
  }

  /// Format date for activity history keys (YYYY-MM-DD)
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Check if two dates are the same day
  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Create a copy with updated fields
  Streak copyWith({
    String? id,
    String? userId,
    StreakType? type,
    int? currentStreak,
    int? longestStreak,
    int? totalCount,
    StreakStatus? status,
    DateTime? lastActivityDate,
    DateTime? streakStartDate,
    int? targetGoal,
    Map<String, bool>? activityHistory,
    double? multiplierBonus,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Streak(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      totalCount: totalCount ?? this.totalCount,
      status: status ?? this.status,
      lastActivityDate: lastActivityDate ?? this.lastActivityDate,
      streakStartDate: streakStartDate ?? this.streakStartDate,
      targetGoal: targetGoal ?? this.targetGoal,
      activityHistory: activityHistory ?? this.activityHistory,
      multiplierBonus: multiplierBonus ?? this.multiplierBonus,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create empty streak for initialization
  static Streak empty(String userId, StreakType type) {
    final now = DateTime.now();
    return Streak(
      id: '',
      userId: userId,
      type: type,
      currentStreak: 0,
      longestStreak: 0,
      totalCount: 0,
      status: StreakStatus.active,
      lastActivityDate: now,
      streakStartDate: now,
      activityHistory: {},
      multiplierBonus: 1.0,
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        currentStreak,
        longestStreak,
        totalCount,
        status,
        lastActivityDate,
        streakStartDate,
        targetGoal,
        activityHistory,
        multiplierBonus,
        metadata,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}