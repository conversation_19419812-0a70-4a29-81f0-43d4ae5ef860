import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shared/shared.dart';
import '../services/messaging_service.dart';
import '../services/auth_service.dart';

/// Routes for messaging and chat functionality
class MessagingRoutes {
  static const String version = '1.0.0';
  
  final MessagingService _messagingService;
  final AuthService _authService;

  MessagingRoutes({
    required MessagingService messagingService,
    required AuthService authService,
  }) : _messagingService = messagingService,
       _authService = authService;

  /// Create router with all messaging routes
  static Router createRouter({
    required MessagingService messagingService,
    required AuthService authService,
  }) {
    final routes = MessagingRoutes(
      messagingService: messagingService,
      authService: authService,
    );

    final router = Router();

    // Chat management routes
    router.post('/chats', routes._createChat);
    router.get('/chats', routes._getUserChats);
    router.get('/chats/<chatId>', routes._getChat);
    router.put('/chats/<chatId>', routes._updateChat);
    router.delete('/chats/<chatId>', routes._deleteChat);
    router.post('/chats/<chatId>/participants', routes._addParticipant);
    router.delete('/chats/<chatId>/participants/<userId>', routes._removeParticipant);

    // Message routes
    router.post('/chats/<chatId>/messages', routes._sendMessage);
    router.get('/chats/<chatId>/messages', routes._getMessages);
    router.get('/messages/<messageId>', routes._getMessage);
    router.put('/messages/<messageId>', routes._updateMessage);
    router.delete('/messages/<messageId>', routes._deleteMessage);
    router.post('/messages/<messageId>/read', routes._markMessageAsRead);

    // Reaction routes
    router.post('/messages/<messageId>/reactions', routes._addReaction);
    router.delete('/messages/<messageId>/reactions', routes._removeReaction);

    // Typing indicator routes
    router.post('/chats/<chatId>/typing/start', routes._startTyping);
    router.post('/chats/<chatId>/typing/stop', routes._stopTyping);

    // File upload routes
    router.post('/chats/<chatId>/upload', routes._uploadFile);

    // Search routes
    router.get('/chats/<chatId>/search', routes._searchMessages);
    router.get('/chats/search', routes._searchChats);

    return router;
  }

  /// Create a new chat
  Future<Response> _createChat(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final chat = await _messagingService.createChat(
        createdBy: userId,
        type: ChatType.values.firstWhere(
          (t) => t.toString().split('.').last == data['type'],
          orElse: () => ChatType.direct,
        ),
        name: data['name'],
        description: data['description'],
        participantIds: List<String>.from(data['participantIds'] ?? []),
        settings: data['settings'] != null 
            ? ChatSettings.fromJson(data['settings'])
            : null,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': chat.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to create chat: $e');
    }
  }

  /// Get user's chats
  Future<Response> _getUserChats(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final page = int.tryParse(request.url.queryParameters['page'] ?? '1') ?? 1;
      final pageSize = int.tryParse(request.url.queryParameters['pageSize'] ?? '20') ?? 20;

      final chats = await _messagingService.getUserChats(
        userId,
        page: page,
        pageSize: pageSize,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': chats.map((c) => c.toJson()).toList(),
          'pagination': {
            'page': page,
            'pageSize': pageSize,
            'hasMore': chats.length == pageSize,
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to get chats: $e');
    }
  }

  /// Get specific chat
  Future<Response> _getChat(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final chatId = request.params['chatId']!;

      final chat = await _messagingService.getChat(chatId);
      if (chat == null) {
        return _notFoundResponse('Chat not found');
      }

      // Check if user has access to this chat
      if (!chat.participantIds.contains(userId)) {
        return _forbiddenResponse('Access denied');
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': chat.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to get chat: $e');
    }
  }

  /// Send a message
  Future<Response> _sendMessage(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final chatId = request.params['chatId']!;
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final message = await _messagingService.sendMessage(
        chatId: chatId,
        senderId: userId,
        content: data['content'],
        type: MessageType.values.firstWhere(
          (t) => t.toString().split('.').last == data['type'],
          orElse: () => MessageType.text,
        ),
        replyToId: data['replyToId'],
        metadata: data['metadata'],
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': message.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to send message: $e');
    }
  }

  /// Get messages for a chat
  Future<Response> _getMessages(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final chatId = request.params['chatId']!;
      final page = int.tryParse(request.url.queryParameters['page'] ?? '1') ?? 1;
      final pageSize = int.tryParse(request.url.queryParameters['pageSize'] ?? '50') ?? 50;
      final beforeMessageId = request.url.queryParameters['before'];

      // Verify user has access to chat
      final chat = await _messagingService.getChat(chatId);
      if (chat == null || !chat.participantIds.contains(userId)) {
        return _forbiddenResponse('Access denied');
      }

      final messages = await _messagingService.getMessages(
        chatId: chatId,
        page: page,
        pageSize: pageSize,
        beforeMessageId: beforeMessageId,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': messages.map((m) => m.toJson()).toList(),
          'pagination': {
            'page': page,
            'pageSize': pageSize,
            'hasMore': messages.length == pageSize,
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to get messages: $e');
    }
  }

  /// Add reaction to message
  Future<Response> _addReaction(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final messageId = request.params['messageId']!;
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      await _messagingService.addReaction(
        messageId: messageId,
        userId: userId,
        emoji: data['emoji'],
      );

      return Response.ok(
        jsonEncode({'success': true}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to add reaction: $e');
    }
  }

  /// Remove reaction from message
  Future<Response> _removeReaction(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final messageId = request.params['messageId']!;
      final emoji = request.url.queryParameters['emoji'];

      if (emoji == null) {
        return _badRequestResponse('Emoji parameter is required');
      }

      await _messagingService.removeReaction(
        messageId: messageId,
        userId: userId,
        emoji: emoji,
      );

      return Response.ok(
        jsonEncode({'success': true}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to remove reaction: $e');
    }
  }

  /// Start typing indicator
  Future<Response> _startTyping(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final chatId = request.params['chatId']!;

      await _messagingService.startTyping(
        chatId: chatId,
        userId: userId,
      );

      return Response.ok(
        jsonEncode({'success': true}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to start typing: $e');
    }
  }

  /// Stop typing indicator
  Future<Response> _stopTyping(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final chatId = request.params['chatId']!;

      await _messagingService.stopTyping(
        chatId: chatId,
        userId: userId,
      );

      return Response.ok(
        jsonEncode({'success': true}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to stop typing: $e');
    }
  }

  /// Mark message as read
  Future<Response> _markMessageAsRead(Request request) async {
    try {
      final userId = await _getUserIdFromRequest(request);
      final messageId = request.params['messageId']!;

      await _messagingService.markMessageAsRead(
        messageId: messageId,
        userId: userId,
      );

      return Response.ok(
        jsonEncode({'success': true}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return _errorResponse('Failed to mark message as read: $e');
    }
  }

  // Placeholder methods for additional functionality
  Future<Response> _updateChat(Request request) async {
    return _notImplementedResponse('Update chat not implemented yet');
  }

  Future<Response> _deleteChat(Request request) async {
    return _notImplementedResponse('Delete chat not implemented yet');
  }

  Future<Response> _addParticipant(Request request) async {
    return _notImplementedResponse('Add participant not implemented yet');
  }

  Future<Response> _removeParticipant(Request request) async {
    return _notImplementedResponse('Remove participant not implemented yet');
  }

  Future<Response> _getMessage(Request request) async {
    return _notImplementedResponse('Get message not implemented yet');
  }

  Future<Response> _updateMessage(Request request) async {
    return _notImplementedResponse('Update message not implemented yet');
  }

  Future<Response> _deleteMessage(Request request) async {
    return _notImplementedResponse('Delete message not implemented yet');
  }

  Future<Response> _uploadFile(Request request) async {
    return _notImplementedResponse('File upload not implemented yet');
  }

  Future<Response> _searchMessages(Request request) async {
    return _notImplementedResponse('Message search not implemented yet');
  }

  Future<Response> _searchChats(Request request) async {
    return _notImplementedResponse('Chat search not implemented yet');
  }

  /// Helper methods

  Future<String> _getUserIdFromRequest(Request request) async {
    final authHeader = request.headers['authorization'];
    if (authHeader == null || !authHeader.startsWith('Bearer ')) {
      throw Exception('Missing or invalid authorization header');
    }

    final token = authHeader.substring(7);
    final user = await _authService.validateToken(token);
    if (user == null) {
      throw Exception('Invalid token');
    }

    return user['id'] as String;
  }

  Response _errorResponse(String message, {int statusCode = 500}) {
    return Response(
      statusCode,
      body: jsonEncode({
        'success': false,
        'error': message,
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Response _badRequestResponse(String message) {
    return _errorResponse(message, statusCode: 400);
  }

  Response _forbiddenResponse(String message) {
    return _errorResponse(message, statusCode: 403);
  }

  Response _notFoundResponse(String message) {
    return _errorResponse(message, statusCode: 404);
  }

  Response _notImplementedResponse(String message) {
    return _errorResponse(message, statusCode: 501);
  }
}
