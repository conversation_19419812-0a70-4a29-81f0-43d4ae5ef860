# Docker Compose configuration for testing environment
# Provides isolated PostgreSQL instance for integration tests

version: '3.8'

services:
  postgres-test:
    image: postgres:16
    platform: linux/amd64
    restart: "no"  # Don't restart automatically in test environment
    environment:
      POSTGRES_USER: ${TEST_POSTGRES_USER:-quester}
      POSTGRES_PASSWORD: ${TEST_POSTGRES_PASSWORD:-questerpass}
      POSTGRES_DB: ${TEST_POSTGRES_DB:-questerdb}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      PGUSER: ${TEST_POSTGRES_USER:-quester}
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "${TEST_POSTGRES_PORT:-5433}:5432"  # Use different port to avoid conflicts
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${TEST_POSTGRES_USER:-quester} -d ${TEST_POSTGRES_DB:-questerdb}"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - quester-test-network
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_worker_processes=8
      -c max_parallel_workers_per_gather=4
      -c max_parallel_workers=8
      -c max_parallel_maintenance_workers=4

  redis-test:
    image: redis:7-alpine
    restart: "no"
    ports:
      - "${TEST_REDIS_PORT:-6380}:6379"  # Use different port to avoid conflicts
    networks:
      - quester-test-network
    command: >
      redis-server
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save ""
      --appendonly no
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # Test runner service (optional - for running tests in containerized environment)
  test-runner:
    build:
      context: ..
      dockerfile: server/Dockerfile.test
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    environment:
      TEST_POSTGRES_HOST: postgres-test
      TEST_POSTGRES_PORT: 5432
      TEST_POSTGRES_USER: ${TEST_POSTGRES_USER:-quester}
      TEST_POSTGRES_PASSWORD: ${TEST_POSTGRES_PASSWORD:-questerpass}
      TEST_POSTGRES_DB: ${TEST_POSTGRES_DB:-questerdb}
      TEST_POSTGRES_TEMPLATE: ${TEST_POSTGRES_DB:-questerdb}
      TEST_REDIS_HOST: redis-test
      TEST_REDIS_PORT: 6379
      DART_ENV: test
    volumes:
      - ../:/workspace:ro
      - test_results:/workspace/test-results
    working_dir: /workspace
    networks:
      - quester-test-network
    profiles:
      - test-runner  # Only start when explicitly requested

volumes:
  postgres_test_data:
    driver: local
    name: quester_postgres_test_data
  test_results:
    driver: local
    name: quester_test_results

networks:
  quester-test-network:
    driver: bridge
    name: quester-test-network

# Health check script for the entire test environment
x-healthcheck-script: &healthcheck-script |
  #!/bin/bash
  set -e
  
  echo "🔍 Checking test environment health..."
  
  # Check PostgreSQL
  if ! pg_isready -h postgres-test -p 5432 -U quester -d questerdb; then
    echo "❌ PostgreSQL test database is not ready"
    exit 1
  fi
  
  # Check Redis
  if ! redis-cli -h redis-test -p 6379 ping | grep -q PONG; then
    echo "❌ Redis test instance is not ready"
    exit 1
  fi
  
  echo "✅ Test environment is healthy"

# Test environment configuration
x-test-environment: &test-environment
  TEST_POSTGRES_HOST: postgres-test
  TEST_POSTGRES_PORT: 5432
  TEST_POSTGRES_USER: quester
  TEST_POSTGRES_PASSWORD: questerpass
  TEST_POSTGRES_DB: questerdb
  TEST_REDIS_HOST: redis-test
  TEST_REDIS_PORT: 6379
  DART_ENV: test
  LOG_LEVEL: info

# Common test service configuration
x-test-service: &test-service
  depends_on:
    postgres-test:
      condition: service_healthy
    redis-test:
      condition: service_healthy
  environment:
    <<: *test-environment
  networks:
    - quester-test-network
  volumes:
    - ../:/workspace:ro
  working_dir: /workspace

# Additional services for specific test scenarios
services:
  # Performance test database (separate instance for load testing)
  postgres-perf-test:
    <<: *test-service
    image: postgres:16
    platform: linux/amd64
    restart: "no"
    environment:
      POSTGRES_USER: quester_perf
      POSTGRES_PASSWORD: questerpass_perf
      POSTGRES_DB: questerdb_perf
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_perf_test_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "${PERF_TEST_POSTGRES_PORT:-5434}:5432"
    profiles:
      - performance-test
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=500
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c maintenance_work_mem=128MB
      -c work_mem=8MB
      -c max_worker_processes=16
      -c max_parallel_workers=16

  # Integration test coordinator
  test-coordinator:
    <<: *test-service
    image: alpine:latest
    profiles:
      - test-coordinator
    command: |
      sh -c "
        echo '🚀 Starting test environment coordination...'
        echo '📊 Test environment status:'
        echo '  - PostgreSQL: postgres-test:5432'
        echo '  - Redis: redis-test:6379'
        echo '  - Performance DB: postgres-perf-test:5432 (if enabled)'
        echo ''
        echo '🔧 Environment variables:'
        env | grep TEST_ | sort
        echo ''
        echo '✅ Test environment ready for integration tests'
        tail -f /dev/null
      "

volumes:
  postgres_perf_test_data:
    driver: local
    name: quester_postgres_perf_test_data

# Test execution profiles
# Usage examples:
# docker-compose -f docker-compose.test.yml up postgres-test redis-test
# docker-compose -f docker-compose.test.yml --profile test-runner up
# docker-compose -f docker-compose.test.yml --profile performance-test up postgres-perf-test
# docker-compose -f docker-compose.test.yml --profile test-coordinator up
