// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateQuestDTO _$CreateQuestDTOFromJson(Map<String, dynamic> json) =>
    CreateQuestDTO(
      title: json['title'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
      difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
      pointsReward: (json['pointsReward'] as num).toInt(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      taskTitles:
          (json['taskTitles'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$CreateQuestDTOToJson(CreateQuestDTO instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'category': _$QuestCategoryEnumMap[instance.category]!,
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
      'pointsReward': instance.pointsReward,
      'dueDate': instance.dueDate?.toIso8601String(),
      'taskTitles': instance.taskTitles,
    };

const _$QuestCategoryEnumMap = {
  QuestCategory.personal: 'personal',
  QuestCategory.work: 'work',
  QuestCategory.learning: 'learning',
  QuestCategory.health: 'health',
  QuestCategory.social: 'social',
  QuestCategory.creative: 'creative',
  QuestCategory.productivity: 'productivity',
  QuestCategory.other: 'other',
};

const _$QuestDifficultyEnumMap = {
  QuestDifficulty.beginner: 'beginner',
  QuestDifficulty.intermediate: 'intermediate',
  QuestDifficulty.advanced: 'advanced',
  QuestDifficulty.expert: 'expert',
  QuestDifficulty.master: 'master',
};

UpdateQuestDTO _$UpdateQuestDTOFromJson(Map<String, dynamic> json) =>
    UpdateQuestDTO(
      title: json['title'] as String?,
      description: json['description'] as String?,
      status: $enumDecodeNullable(_$QuestStatusEnumMap, json['status']),
      category: $enumDecodeNullable(_$QuestCategoryEnumMap, json['category']),
      difficulty: $enumDecodeNullable(
        _$QuestDifficultyEnumMap,
        json['difficulty'],
      ),
      pointsReward: (json['pointsReward'] as num?)?.toInt(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
    );

Map<String, dynamic> _$UpdateQuestDTOToJson(UpdateQuestDTO instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'status': _$QuestStatusEnumMap[instance.status],
      'category': _$QuestCategoryEnumMap[instance.category],
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty],
      'pointsReward': instance.pointsReward,
      'dueDate': instance.dueDate?.toIso8601String(),
    };

const _$QuestStatusEnumMap = {
  QuestStatus.draft: 'draft',
  QuestStatus.active: 'active',
  QuestStatus.inProgress: 'in_progress',
  QuestStatus.paused: 'paused',
  QuestStatus.completed: 'completed',
  QuestStatus.archived: 'archived',
  QuestStatus.cancelled: 'cancelled',
};

CreateQuestDto _$CreateQuestDtoFromJson(Map<String, dynamic> json) =>
    CreateQuestDto(
      title: json['title'] as String,
      description: json['description'] as String,
      assignedToId: json['assignedToId'] as String?,
      priority: $enumDecode(_$QuestPriorityEnumMap, json['priority']),
      difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
      category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
      estimatedHours: (json['estimatedHours'] as num?)?.toInt(),
      deadline: json['deadline'] == null
          ? null
          : DateTime.parse(json['deadline'] as String),
      participantIds: (json['participantIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CreateQuestDtoToJson(CreateQuestDto instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'assignedToId': instance.assignedToId,
      'priority': _$QuestPriorityEnumMap[instance.priority]!,
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
      'category': _$QuestCategoryEnumMap[instance.category]!,
      'estimatedHours': instance.estimatedHours,
      'deadline': instance.deadline?.toIso8601String(),
      'participantIds': instance.participantIds,
      'tags': instance.tags,
      'metadata': instance.metadata,
    };

const _$QuestPriorityEnumMap = {
  QuestPriority.low: 'low',
  QuestPriority.medium: 'medium',
  QuestPriority.high: 'high',
  QuestPriority.urgent: 'urgent',
};

UpdateQuestDto _$UpdateQuestDtoFromJson(Map<String, dynamic> json) =>
    UpdateQuestDto(
      title: json['title'] as String?,
      description: json['description'] as String?,
      assignedToId: json['assignedToId'] as String?,
      status: $enumDecodeNullable(_$QuestStatusEnumMap, json['status']),
      priority: $enumDecodeNullable(_$QuestPriorityEnumMap, json['priority']),
      difficulty: $enumDecodeNullable(
        _$QuestDifficultyEnumMap,
        json['difficulty'],
      ),
      category: $enumDecodeNullable(_$QuestCategoryEnumMap, json['category']),
      estimatedHours: (json['estimatedHours'] as num?)?.toInt(),
      actualHours: (json['actualHours'] as num?)?.toInt(),
      deadline: json['deadline'] == null
          ? null
          : DateTime.parse(json['deadline'] as String),
      progressPercentage: (json['progressPercentage'] as num?)?.toDouble(),
      participantIds: (json['participantIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UpdateQuestDtoToJson(UpdateQuestDto instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'assignedToId': instance.assignedToId,
      'status': _$QuestStatusEnumMap[instance.status],
      'priority': _$QuestPriorityEnumMap[instance.priority],
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty],
      'category': _$QuestCategoryEnumMap[instance.category],
      'estimatedHours': instance.estimatedHours,
      'actualHours': instance.actualHours,
      'deadline': instance.deadline?.toIso8601String(),
      'progressPercentage': instance.progressPercentage,
      'participantIds': instance.participantIds,
      'tags': instance.tags,
      'metadata': instance.metadata,
    };

QuestFilterDto _$QuestFilterDtoFromJson(Map<String, dynamic> json) =>
    QuestFilterDto(
      statuses: (json['statuses'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$QuestStatusEnumMap, e))
          .toList(),
      priorities: (json['priorities'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$QuestPriorityEnumMap, e))
          .toList(),
      difficulties: (json['difficulties'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$QuestDifficultyEnumMap, e))
          .toList(),
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$QuestCategoryEnumMap, e))
          .toList(),
      createdById: json['createdById'] as String?,
      assignedToId: json['assignedToId'] as String?,
      participantId: json['participantId'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      createdAfter: json['createdAfter'] == null
          ? null
          : DateTime.parse(json['createdAfter'] as String),
      createdBefore: json['createdBefore'] == null
          ? null
          : DateTime.parse(json['createdBefore'] as String),
      deadlineAfter: json['deadlineAfter'] == null
          ? null
          : DateTime.parse(json['deadlineAfter'] as String),
      deadlineBefore: json['deadlineBefore'] == null
          ? null
          : DateTime.parse(json['deadlineBefore'] as String),
      isOverdue: json['isOverdue'] as bool?,
      search: json['search'] as String?,
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
      sortBy: json['sortBy'] as String?,
      sortOrder: json['sortOrder'] as String?,
    );

Map<String, dynamic> _$QuestFilterDtoToJson(
  QuestFilterDto instance,
) => <String, dynamic>{
  'statuses': instance.statuses?.map((e) => _$QuestStatusEnumMap[e]!).toList(),
  'priorities': instance.priorities
      ?.map((e) => _$QuestPriorityEnumMap[e]!)
      .toList(),
  'difficulties': instance.difficulties
      ?.map((e) => _$QuestDifficultyEnumMap[e]!)
      .toList(),
  'categories': instance.categories
      ?.map((e) => _$QuestCategoryEnumMap[e]!)
      .toList(),
  'createdById': instance.createdById,
  'assignedToId': instance.assignedToId,
  'participantId': instance.participantId,
  'tags': instance.tags,
  'createdAfter': instance.createdAfter?.toIso8601String(),
  'createdBefore': instance.createdBefore?.toIso8601String(),
  'deadlineAfter': instance.deadlineAfter?.toIso8601String(),
  'deadlineBefore': instance.deadlineBefore?.toIso8601String(),
  'isOverdue': instance.isOverdue,
  'search': instance.search,
  'limit': instance.limit,
  'offset': instance.offset,
  'sortBy': instance.sortBy,
  'sortOrder': instance.sortOrder,
};

QuestListResponseDto _$QuestListResponseDtoFromJson(
  Map<String, dynamic> json,
) => QuestListResponseDto(
  quests: (json['quests'] as List<dynamic>)
      .map((e) => Quest.fromJson(e as Map<String, dynamic>))
      .toList(),
  totalCount: (json['totalCount'] as num).toInt(),
  pageCount: (json['pageCount'] as num).toInt(),
  currentPage: (json['currentPage'] as num).toInt(),
  hasNext: json['hasNext'] as bool,
  hasPrevious: json['hasPrevious'] as bool,
);

Map<String, dynamic> _$QuestListResponseDtoToJson(
  QuestListResponseDto instance,
) => <String, dynamic>{
  'quests': instance.quests,
  'totalCount': instance.totalCount,
  'pageCount': instance.pageCount,
  'currentPage': instance.currentPage,
  'hasNext': instance.hasNext,
  'hasPrevious': instance.hasPrevious,
};

QuestStatsDto _$QuestStatsDtoFromJson(
  Map<String, dynamic> json,
) => QuestStatsDto(
  totalQuests: (json['totalQuests'] as num).toInt(),
  activeQuests: (json['activeQuests'] as num).toInt(),
  completedQuests: (json['completedQuests'] as num).toInt(),
  overdueQuests: (json['overdueQuests'] as num).toInt(),
  todayDeadlines: (json['todayDeadlines'] as num).toInt(),
  thisWeekDeadlines: (json['thisWeekDeadlines'] as num).toInt(),
  completionRate: (json['completionRate'] as num).toDouble(),
  averageCompletionTime: (json['averageCompletionTime'] as num).toDouble(),
  totalPointsEarned: (json['totalPointsEarned'] as num).toInt(),
  totalPointsAvailable: (json['totalPointsAvailable'] as num).toInt(),
  statusBreakdown: Map<String, int>.from(json['statusBreakdown'] as Map),
  priorityBreakdown: Map<String, int>.from(json['priorityBreakdown'] as Map),
  categoryBreakdown: Map<String, int>.from(json['categoryBreakdown'] as Map),
);

Map<String, dynamic> _$QuestStatsDtoToJson(QuestStatsDto instance) =>
    <String, dynamic>{
      'totalQuests': instance.totalQuests,
      'activeQuests': instance.activeQuests,
      'completedQuests': instance.completedQuests,
      'overdueQuests': instance.overdueQuests,
      'todayDeadlines': instance.todayDeadlines,
      'thisWeekDeadlines': instance.thisWeekDeadlines,
      'completionRate': instance.completionRate,
      'averageCompletionTime': instance.averageCompletionTime,
      'totalPointsEarned': instance.totalPointsEarned,
      'totalPointsAvailable': instance.totalPointsAvailable,
      'statusBreakdown': instance.statusBreakdown,
      'priorityBreakdown': instance.priorityBreakdown,
      'categoryBreakdown': instance.categoryBreakdown,
    };

QuestCompletionDto _$QuestCompletionDtoFromJson(Map<String, dynamic> json) =>
    QuestCompletionDto(
      questId: json['questId'] as String,
      actualHours: (json['actualHours'] as num).toInt(),
      completionNotes: json['completionNotes'] as String?,
      completionData: json['completionData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$QuestCompletionDtoToJson(QuestCompletionDto instance) =>
    <String, dynamic>{
      'questId': instance.questId,
      'actualHours': instance.actualHours,
      'completionNotes': instance.completionNotes,
      'completionData': instance.completionData,
    };

AddParticipantDto _$AddParticipantDtoFromJson(Map<String, dynamic> json) =>
    AddParticipantDto(
      questId: json['questId'] as String,
      userId: json['userId'] as String,
      role: json['role'] as String?,
      inviteMessage: json['inviteMessage'] as String?,
    );

Map<String, dynamic> _$AddParticipantDtoToJson(AddParticipantDto instance) =>
    <String, dynamic>{
      'questId': instance.questId,
      'userId': instance.userId,
      'role': instance.role,
      'inviteMessage': instance.inviteMessage,
    };
