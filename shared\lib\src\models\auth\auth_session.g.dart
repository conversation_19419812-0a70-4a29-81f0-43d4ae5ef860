// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_session.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthSession _$AuthSessionFromJson(Map<String, dynamic> json) => AuthSession(
  token: json['token'] as String,
  refreshToken: json['refreshToken'] as String,
  expiresAt: DateTime.parse(json['expiresAt'] as String),
  user: User.fromJson(json['user'] as Map<String, dynamic>),
  organizationRoles: (json['organizationRoles'] as List<dynamic>)
      .map((e) => UserOrganizationRole.fromJson(e as Map<String, dynamic>))
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  lastActivityAt: DateTime.parse(json['lastActivityAt'] as String),
);

Map<String, dynamic> _$AuthSessionToJson(AuthSession instance) =>
    <String, dynamic>{
      'token': instance.token,
      'refreshToken': instance.refreshToken,
      'expiresAt': instance.expiresAt.toIso8601String(),
      'user': instance.user,
      'organizationRoles': instance.organizationRoles,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastActivityAt': instance.lastActivityAt.toIso8601String(),
    };

UserOrganizationRole _$UserOrganizationRoleFromJson(
  Map<String, dynamic> json,
) => UserOrganizationRole(
  organizationId: json['organizationId'] as String,
  organizationName: json['organizationName'] as String,
  role: OrganizationRole.fromJson(json['role'] as Map<String, dynamic>),
  isDefault: json['isDefault'] as bool,
  assignedAt: DateTime.parse(json['assignedAt'] as String),
);

Map<String, dynamic> _$UserOrganizationRoleToJson(
  UserOrganizationRole instance,
) => <String, dynamic>{
  'organizationId': instance.organizationId,
  'organizationName': instance.organizationName,
  'role': instance.role,
  'isDefault': instance.isDefault,
  'assignedAt': instance.assignedAt.toIso8601String(),
};
