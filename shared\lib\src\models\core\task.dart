import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'task.g.dart';

/// Task status enumeration
enum TaskStatus {
  @JsonValue('todo')
  todo,
  @JsonValue('pending')
  pending,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('blocked')
  blocked,
}

/// Task priority levels
enum TaskPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

/// Task complexity for point calculation
enum TaskComplexity {
  @JsonValue('simple')
  simple,
  @JsonValue('moderate')
  moderate,
  @JsonValue('complex')
  complex,
  @JsonValue('very_complex')
  veryComplex,
}

/// Core task model with gamification features
@JsonSerializable()
class Task extends Equatable {
  /// Unique task identifier
  final String id;

  /// Task title
  final String title;

  /// Task description
  final String description;

  /// Parent quest ID (optional)
  final String? questId;

  /// Task creator user ID
  final String createdById;

  /// Task assignee user ID (can be same as creator)
  final String? assignedToId;

  /// Current task status
  final TaskStatus status;

  /// Task priority level
  final TaskPriority priority;

  /// Task complexity for point calculation
  final TaskComplexity complexity;

  /// Points awarded on completion
  final int points;

  /// Points earned (usually 0 or full points)
  final int earnedPoints;

  /// Task deadline (optional)
  final DateTime? deadline;

  /// Estimated time to complete (in minutes)
  final int? estimatedMinutes;

  /// Actual time spent (in minutes)
  final int? actualMinutes;

  /// Task dependencies (other task IDs that must be completed first)
  final List<String> dependencies;

  /// Task tags for organization and filtering
  final List<String> tags;

  /// Task attachments/file references
  final List<String> attachments;

  /// Task comments/notes
  final List<String> comments;

  /// Task metadata as JSON
  final Map<String, dynamic>? metadata;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Completion timestamp (if completed)
  final DateTime? completedAt;

  /// Task start timestamp (when moved to in_progress)
  final DateTime? startedAt;

  const Task({
    required this.id,
    required this.title,
    required this.description,
    this.questId,
    required this.createdById,
    this.assignedToId,
    required this.status,
    required this.priority,
    required this.complexity,
    required this.points,
    required this.earnedPoints,
    this.deadline,
    this.estimatedMinutes,
    this.actualMinutes,
    required this.dependencies,
    required this.tags,
    required this.attachments,
    required this.comments,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.completedAt,
    this.startedAt,
  });

  /// Create Task from JSON
  factory Task.fromJson(Map<String, dynamic> json) => _$TaskFromJson(json);

  /// Convert Task to JSON
  Map<String, dynamic> toJson() => _$TaskToJson(this);

  /// Check if task is overdue
  bool get isOverdue {
    if (deadline == null) return false;
    return DateTime.now().isAfter(deadline!) && status != TaskStatus.completed;
  }

  /// Check if task is completed
  bool get isCompleted => status == TaskStatus.completed;

  /// Check if task is active (in progress)
  bool get isActive => status == TaskStatus.inProgress;

  /// Check if task is blocked
  bool get isBlocked => status == TaskStatus.blocked;

  /// Check if task can be started (no incomplete dependencies)
  bool get canStart => dependencies.isEmpty; // Simplified - would need dependency checking

  /// Get days until deadline (negative if overdue)
  int? get daysUntilDeadline {
    if (deadline == null) return null;
    final now = DateTime.now();
    return deadline!.difference(now).inDays;
  }

  /// Get hours until deadline
  int? get hoursUntilDeadline {
    if (deadline == null) return null;
    final now = DateTime.now();
    return deadline!.difference(now).inHours;
  }

  /// Get complexity multiplier for points
  double get complexityMultiplier {
    switch (complexity) {
      case TaskComplexity.simple:
        return 1.0;
      case TaskComplexity.moderate:
        return 1.5;
      case TaskComplexity.complex:
        return 2.0;
      case TaskComplexity.veryComplex:
        return 3.0;
    }
  }

  /// Get priority multiplier for urgency
  double get priorityMultiplier {
    switch (priority) {
      case TaskPriority.low:
        return 1.0;
      case TaskPriority.medium:
        return 1.2;
      case TaskPriority.high:
        return 1.5;
      case TaskPriority.critical:
        return 2.0;
    }
  }

  /// Calculate time efficiency bonus (1.0 = on time, >1.0 = early, <1.0 = late)
  double get timeEfficiencyBonus {
    if (actualMinutes == null || estimatedMinutes == null || estimatedMinutes! <= 0) {
      return 1.0;
    }
    return estimatedMinutes! / actualMinutes!;
  }

  /// Get duration worked in minutes
  int? get durationWorked {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!).inMinutes;
  }

  /// Check if task has attachments
  bool get hasAttachments => attachments.isNotEmpty;

  /// Check if task has comments
  bool get hasComments => comments.isNotEmpty;

  /// Check if task has dependencies
  bool get hasDependencies => dependencies.isNotEmpty;

  /// Check if task belongs to a quest
  bool get belongsToQuest => questId != null;

  /// Create a copy with updated fields
  Task copyWith({
    String? id,
    String? title,
    String? description,
    String? questId,
    String? createdById,
    String? assignedToId,
    TaskStatus? status,
    TaskPriority? priority,
    TaskComplexity? complexity,
    int? points,
    int? earnedPoints,
    DateTime? deadline,
    int? estimatedMinutes,
    int? actualMinutes,
    List<String>? dependencies,
    List<String>? tags,
    List<String>? attachments,
    List<String>? comments,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
    DateTime? startedAt,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      questId: questId ?? this.questId,
      createdById: createdById ?? this.createdById,
      assignedToId: assignedToId ?? this.assignedToId,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      complexity: complexity ?? this.complexity,
      points: points ?? this.points,
      earnedPoints: earnedPoints ?? this.earnedPoints,
      deadline: deadline ?? this.deadline,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      actualMinutes: actualMinutes ?? this.actualMinutes,
      dependencies: dependencies ?? this.dependencies,
      tags: tags ?? this.tags,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
      startedAt: startedAt ?? this.startedAt,
    );
  }

  /// Create empty task for initialization
  static Task empty() {
    final now = DateTime.now();
    return Task(
      id: '',
      title: '',
      description: '',
      createdById: '',
      status: TaskStatus.todo,
      priority: TaskPriority.medium,
      complexity: TaskComplexity.simple,
      points: 0,
      earnedPoints: 0,
      dependencies: [],
      tags: [],
      attachments: [],
      comments: [],
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        questId,
        createdById,
        assignedToId,
        status,
        priority,
        complexity,
        points,
        earnedPoints,
        deadline,
        estimatedMinutes,
        actualMinutes,
        dependencies,
        tags,
        attachments,
        comments,
        metadata,
        createdAt,
        updatedAt,
        completedAt,
        startedAt,
      ];

  @override
  bool get stringify => true;
}