import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'notification.dart';

part 'notification_preferences.g.dart';

/// User's detailed notification preferences
@JsonSerializable()
class DetailedNotificationPreferences extends Equatable {
  /// User ID these preferences belong to
  final String userId;

  /// Global notification settings
  final bool enableNotifications;

  /// Enable push notifications
  final bool enablePushNotifications;

  /// Enable email notifications
  final bool enableEmailNotifications;

  /// Enable SMS notifications
  final bool enableSmsNotifications;

  /// Enable in-app notifications
  final bool enableInAppNotifications;

  /// Enable webhook notifications
  final bool enableWebhookNotifications;

  /// Quiet hours settings
  final QuietHours? quietHours;

  /// Category-specific preferences
  final Map<NotificationCategory, CategoryPreferences> categoryPreferences;

  /// Device-specific settings
  final DeviceNotificationSettings deviceSettings;

  /// Frequency settings for batched notifications
  final NotificationFrequency frequency;

  /// Language preference for notifications
  final String language;

  /// Timezone for scheduling notifications
  final String timezone;

  /// When preferences were created
  final DateTime createdAt;

  /// When preferences were last updated
  final DateTime updatedAt;

  const DetailedNotificationPreferences({
    required this.userId,
    this.enableNotifications = true,
    this.enablePushNotifications = true,
    this.enableEmailNotifications = true,
    this.enableSmsNotifications = false,
    this.enableInAppNotifications = true,
    this.enableWebhookNotifications = false,
    this.quietHours,
    this.categoryPreferences = const {},
    required this.deviceSettings,
    this.frequency = NotificationFrequency.immediate,
    this.language = 'en',
    this.timezone = 'UTC',
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates DetailedNotificationPreferences from JSON
  factory DetailedNotificationPreferences.fromJson(Map<String, dynamic> json) =>
      _$DetailedNotificationPreferencesFromJson(json);

  /// Converts DetailedNotificationPreferences to JSON
  Map<String, dynamic> toJson() => _$DetailedNotificationPreferencesToJson(this);

  /// Creates a copy with updated fields
  DetailedNotificationPreferences copyWith({
    String? userId,
    bool? enableNotifications,
    bool? enablePushNotifications,
    bool? enableEmailNotifications,
    bool? enableSmsNotifications,
    bool? enableInAppNotifications,
    bool? enableWebhookNotifications,
    QuietHours? quietHours,
    Map<NotificationCategory, CategoryPreferences>? categoryPreferences,
    DeviceNotificationSettings? deviceSettings,
    NotificationFrequency? frequency,
    String? language,
    String? timezone,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DetailedNotificationPreferences(
      userId: userId ?? this.userId,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enablePushNotifications: enablePushNotifications ?? this.enablePushNotifications,
      enableEmailNotifications: enableEmailNotifications ?? this.enableEmailNotifications,
      enableSmsNotifications: enableSmsNotifications ?? this.enableSmsNotifications,
      enableInAppNotifications: enableInAppNotifications ?? this.enableInAppNotifications,
      enableWebhookNotifications: enableWebhookNotifications ?? this.enableWebhookNotifications,
      quietHours: quietHours ?? this.quietHours,
      categoryPreferences: categoryPreferences ?? this.categoryPreferences,
      deviceSettings: deviceSettings ?? this.deviceSettings,
      frequency: frequency ?? this.frequency,
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Checks if notifications are allowed for a specific category
  bool isCategoryEnabled(NotificationCategory category) {
    if (!enableNotifications) return false;
    
    final categoryPref = categoryPreferences[category];
    return categoryPref?.enabled ?? true;
  }

  /// Checks if a specific delivery method is enabled for a category
  bool isDeliveryMethodEnabled(NotificationCategory category, DeliveryMethod method) {
    if (!isCategoryEnabled(category)) return false;
    
    switch (method) {
      case DeliveryMethod.push:
        return enablePushNotifications &&
               (categoryPreferences[category]?.enablePush ?? true);
      case DeliveryMethod.email:
        return enableEmailNotifications &&
               (categoryPreferences[category]?.enableEmail ?? true);
      case DeliveryMethod.sms:
        return enableSmsNotifications &&
               (categoryPreferences[category]?.enableSms ?? false);
      case DeliveryMethod.inApp:
        return enableInAppNotifications &&
               (categoryPreferences[category]?.enableInApp ?? true);
      case DeliveryMethod.webhook:
        return enableWebhookNotifications &&
               (categoryPreferences[category]?.enableWebhook ?? false);
    }
  }

  /// Checks if current time is within quiet hours
  bool isInQuietHours() {
    if (quietHours == null || !quietHours!.enabled) return false;
    
    final now = DateTime.now();
    final currentTime = TimeOfDay(hour: now.hour, minute: now.minute);
    
    return quietHours!.isTimeInQuietHours(currentTime);
  }

  @override
  List<Object?> get props => [
        userId,
        enableNotifications,
        enablePushNotifications,
        enableEmailNotifications,
        enableSmsNotifications,
        enableInAppNotifications,
        enableWebhookNotifications,
        quietHours,
        categoryPreferences,
        deviceSettings,
        frequency,
        language,
        timezone,
        createdAt,
        updatedAt,
      ];
}

/// Quiet hours configuration
@JsonSerializable()
class QuietHours extends Equatable {
  /// Whether quiet hours are enabled
  final bool enabled;

  /// Start time for quiet hours
  final TimeOfDay startTime;

  /// End time for quiet hours
  final TimeOfDay endTime;

  /// Days of the week when quiet hours apply
  final List<int> daysOfWeek; // 1-7, Monday-Sunday

  /// Whether to allow critical notifications during quiet hours
  final bool allowCritical;

  const QuietHours({
    this.enabled = false,
    this.startTime = const TimeOfDay(hour: 22, minute: 0), // 10 PM
    this.endTime = const TimeOfDay(hour: 8, minute: 0), // 8 AM
    this.daysOfWeek = const [1, 2, 3, 4, 5, 6, 7], // All days
    this.allowCritical = true,
  });

  /// Creates QuietHours from JSON
  factory QuietHours.fromJson(Map<String, dynamic> json) =>
      _$QuietHoursFromJson(json);

  /// Converts QuietHours to JSON
  Map<String, dynamic> toJson() => _$QuietHoursToJson(this);

  /// Checks if a given time is within quiet hours
  bool isTimeInQuietHours(TimeOfDay time) {
    if (!enabled) return false;

    final now = DateTime.now();
    final currentDay = now.weekday;
    
    if (!daysOfWeek.contains(currentDay)) return false;

    final timeInMinutes = time.hour * 60 + time.minute;
    final startInMinutes = startTime.hour * 60 + startTime.minute;
    final endInMinutes = endTime.hour * 60 + endTime.minute;

    // Handle overnight quiet hours (e.g., 10 PM to 8 AM)
    if (startInMinutes > endInMinutes) {
      return timeInMinutes >= startInMinutes || timeInMinutes <= endInMinutes;
    } else {
      return timeInMinutes >= startInMinutes && timeInMinutes <= endInMinutes;
    }
  }

  @override
  List<Object?> get props => [enabled, startTime, endTime, daysOfWeek, allowCritical];
}

/// Time of day representation
@JsonSerializable()
class TimeOfDay extends Equatable {
  final int hour;
  final int minute;

  const TimeOfDay({required this.hour, required this.minute});

  /// Creates TimeOfDay from JSON
  factory TimeOfDay.fromJson(Map<String, dynamic> json) =>
      _$TimeOfDayFromJson(json);

  /// Converts TimeOfDay to JSON
  Map<String, dynamic> toJson() => _$TimeOfDayToJson(this);

  @override
  List<Object?> get props => [hour, minute];

  @override
  String toString() => '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
}

/// Category-specific notification preferences
@JsonSerializable()
class CategoryPreferences extends Equatable {
  /// Whether this category is enabled
  final bool enabled;

  /// Enable push notifications for this category
  final bool enablePush;

  /// Enable email notifications for this category
  final bool enableEmail;

  /// Enable SMS notifications for this category
  final bool enableSms;

  /// Enable in-app notifications for this category
  final bool enableInApp;

  /// Enable webhook notifications for this category
  final bool enableWebhook;

  /// Minimum priority level for notifications in this category
  final NotificationPriority minimumPriority;

  /// Custom sound for this category
  final String? customSound;

  /// Whether to show badge for this category
  final bool showBadge;

  const CategoryPreferences({
    this.enabled = true,
    this.enablePush = true,
    this.enableEmail = true,
    this.enableSms = false,
    this.enableInApp = true,
    this.enableWebhook = false,
    this.minimumPriority = NotificationPriority.low,
    this.customSound,
    this.showBadge = true,
  });

  /// Creates CategoryPreferences from JSON
  factory CategoryPreferences.fromJson(Map<String, dynamic> json) =>
      _$CategoryPreferencesFromJson(json);

  /// Converts CategoryPreferences to JSON
  Map<String, dynamic> toJson() => _$CategoryPreferencesToJson(this);

  @override
  List<Object?> get props => [
        enabled,
        enablePush,
        enableEmail,
        enableSms,
        enableInApp,
        enableWebhook,
        minimumPriority,
        customSound,
        showBadge,
      ];
}

/// Device-specific notification settings
@JsonSerializable()
class DeviceNotificationSettings extends Equatable {
  /// Device push token for notifications
  final String? pushToken;

  /// Device platform (iOS, Android, Web)
  final String platform;

  /// App version on the device
  final String appVersion;

  /// Device timezone
  final String timezone;

  /// Device language
  final String language;

  /// Whether device supports rich notifications
  final bool supportsRichNotifications;

  /// Whether device supports action buttons
  final bool supportsActions;

  /// Maximum number of action buttons supported
  final int maxActions;

  /// Whether device supports custom sounds
  final bool supportsCustomSounds;

  /// Whether device supports vibration
  final bool supportsVibration;

  const DeviceNotificationSettings({
    this.pushToken,
    required this.platform,
    required this.appVersion,
    this.timezone = 'UTC',
    this.language = 'en',
    this.supportsRichNotifications = false,
    this.supportsActions = false,
    this.maxActions = 2,
    this.supportsCustomSounds = false,
    this.supportsVibration = false,
  });

  /// Creates DeviceNotificationSettings from JSON
  factory DeviceNotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$DeviceNotificationSettingsFromJson(json);

  /// Converts DeviceNotificationSettings to JSON
  Map<String, dynamic> toJson() => _$DeviceNotificationSettingsToJson(this);

  @override
  List<Object?> get props => [
        pushToken,
        platform,
        appVersion,
        timezone,
        language,
        supportsRichNotifications,
        supportsActions,
        maxActions,
        supportsCustomSounds,
        supportsVibration,
      ];
}

/// Notification frequency settings
enum NotificationFrequency {
  /// Send notifications immediately
  immediate,
  
  /// Batch notifications every 15 minutes
  every15Minutes,
  
  /// Batch notifications hourly
  hourly,
  
  /// Send daily digest
  daily,
  
  /// Send weekly digest
  weekly,
  
  /// Never send (disable)
  never,
}


