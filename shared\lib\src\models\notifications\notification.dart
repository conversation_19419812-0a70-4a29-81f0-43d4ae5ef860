/// Notification model for the Quester platform
library;

import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'notification_preferences.dart';

part 'notification.g.dart';

/// Represents a notification in the system
@JsonSerializable()
class Notification extends Equatable {
  /// Unique identifier for the notification
  final String id;
  
  /// ID of the user who should receive this notification
  final String userId;
  
  /// Type of notification
  final NotificationType type;
  
  /// Title of the notification
  final String title;
  
  /// Body/content of the notification
  final String body;
  
  /// Optional action text (e.g., "View Quest", "Accept Invite")
  final String? actionText;
  
  /// URL or route to navigate to when notification is tapped
  final String? actionUrl;
  
  /// Priority level of the notification
  final NotificationPriority priority;
  
  /// Current status of the notification
  final NotificationStatus status;
  
  /// Delivery methods for this notification
  final List<DeliveryMethod> deliveryMethods;
  
  /// Metadata associated with the notification
  final Map<String, dynamic>? metadata;
  
  /// ID of the related entity (quest, task, user, etc.)
  final String? relatedEntityId;
  
  /// Type of the related entity
  final String? relatedEntityType;
  
  /// Icon to display with the notification
  final String? icon;
  
  /// Image URL to display with the notification
  final String? imageUrl;
  
  /// Sound to play for this notification
  final String? sound;
  
  /// Timestamp when the notification was created
  final DateTime createdAt;
  
  /// Timestamp when the notification was scheduled to be sent
  final DateTime? scheduledAt;
  
  /// Timestamp when the notification was sent
  final DateTime? sentAt;
  
  /// Timestamp when the notification was read
  final DateTime? readAt;
  
  /// Timestamp when the notification expires
  final DateTime? expiresAt;
  
  /// Number of retry attempts for delivery
  final int retryCount;
  
  /// Whether this notification can be grouped with others
  final bool canGroup;
  
  /// Group key for batching similar notifications
  final String? groupKey;

  const Notification({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.body,
    this.actionText,
    this.actionUrl,
    required this.priority,
    required this.status,
    required this.deliveryMethods,
    this.metadata,
    this.relatedEntityId,
    this.relatedEntityType,
    this.icon,
    this.imageUrl,
    this.sound,
    required this.createdAt,
    this.scheduledAt,
    this.sentAt,
    this.readAt,
    this.expiresAt,
    this.retryCount = 0,
    this.canGroup = false,
    this.groupKey,
  });

  /// Create Notification from JSON
  factory Notification.fromJson(Map<String, dynamic> json) => 
      _$NotificationFromJson(json);

  /// Convert Notification to JSON
  Map<String, dynamic> toJson() => _$NotificationToJson(this);

  /// Create a copy with updated fields
  Notification copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    String? title,
    String? body,
    String? actionText,
    String? actionUrl,
    NotificationPriority? priority,
    NotificationStatus? status,
    List<DeliveryMethod>? deliveryMethods,
    Map<String, dynamic>? metadata,
    String? relatedEntityId,
    String? relatedEntityType,
    String? icon,
    String? imageUrl,
    String? sound,
    DateTime? createdAt,
    DateTime? scheduledAt,
    DateTime? sentAt,
    DateTime? readAt,
    DateTime? expiresAt,
    int? retryCount,
    bool? canGroup,
    String? groupKey,
  }) {
    return Notification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      body: body ?? this.body,
      actionText: actionText ?? this.actionText,
      actionUrl: actionUrl ?? this.actionUrl,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      deliveryMethods: deliveryMethods ?? this.deliveryMethods,
      metadata: metadata ?? this.metadata,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      icon: icon ?? this.icon,
      imageUrl: imageUrl ?? this.imageUrl,
      sound: sound ?? this.sound,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      readAt: readAt ?? this.readAt,
      expiresAt: expiresAt ?? this.expiresAt,
      retryCount: retryCount ?? this.retryCount,
      canGroup: canGroup ?? this.canGroup,
      groupKey: groupKey ?? this.groupKey,
    );
  }

  /// Check if notification is read
  bool get isRead => readAt != null;

  /// Check if notification is expired
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  /// Check if notification is scheduled for future delivery
  bool get isScheduled => scheduledAt != null && DateTime.now().isBefore(scheduledAt!);

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        title,
        body,
        actionText,
        actionUrl,
        priority,
        status,
        deliveryMethods,
        metadata,
        relatedEntityId,
        relatedEntityType,
        icon,
        imageUrl,
        sound,
        createdAt,
        scheduledAt,
        sentAt,
        readAt,
        expiresAt,
        retryCount,
        canGroup,
        groupKey,
      ];
}

/// Types of notifications
enum NotificationType {
  @JsonValue('quest_reminder')
  questReminder,
  @JsonValue('task_reminder')
  taskReminder,
  @JsonValue('deadline_alert')
  deadlineAlert,
  @JsonValue('achievement_unlocked')
  achievementUnlocked,
  @JsonValue('points_earned')
  pointsEarned,
  @JsonValue('level_up')
  levelUp,
  @JsonValue('message_received')
  messageReceived,
  @JsonValue('collaboration_invite')
  collaborationInvite,
  @JsonValue('quest_invite')
  questInvite,
  @JsonValue('team_invite')
  teamInvite,
  @JsonValue('system_alert')
  systemAlert,
  @JsonValue('maintenance_notice')
  maintenanceNotice,
  @JsonValue('update_available')
  updateAvailable,
  @JsonValue('streak_reminder')
  streakReminder,
  @JsonValue('leaderboard_update')
  leaderboardUpdate,
  @JsonValue('reward_available')
  rewardAvailable,
}

/// Categories for organizing notifications
enum NotificationCategory {
  @JsonValue('system')
  system,
  @JsonValue('quest')
  quest,
  @JsonValue('achievement')
  achievement,
  @JsonValue('social')
  social,
  @JsonValue('reminder')
  reminder,
  @JsonValue('marketing')
  marketing,
  @JsonValue('security')
  security,
  @JsonValue('team')
  team,
}

/// Priority levels for notifications
enum NotificationPriority {
  @JsonValue('low')
  low,
  @JsonValue('normal')
  normal,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

/// Status of a notification
enum NotificationStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('sent')
  sent,
  @JsonValue('delivered')
  delivered,
  @JsonValue('read')
  read,
  @JsonValue('failed')
  failed,
  @JsonValue('expired')
  expired,
  @JsonValue('cancelled')
  cancelled,
}

/// Delivery methods for notifications
enum DeliveryMethod {
  @JsonValue('in_app')
  inApp,
  @JsonValue('push')
  push,
  @JsonValue('email')
  email,
  @JsonValue('sms')
  sms,
  @JsonValue('webhook')
  webhook,
}

/// User notification preferences
@JsonSerializable()
class NotificationPreferences extends Equatable {
  /// User ID these preferences belong to
  final String userId;
  
  /// Global notification settings
  final bool globalEnabled;
  
  /// Quiet hours settings
  final QuietHours? quietHours;
  
  /// Per-type notification settings
  final Map<NotificationType, NotificationTypeSettings> typeSettings;
  
  /// Per-delivery method settings
  final Map<DeliveryMethod, bool> deliveryMethodSettings;
  
  /// Timestamp when preferences were last updated
  final DateTime updatedAt;

  const NotificationPreferences({
    required this.userId,
    this.globalEnabled = true,
    this.quietHours,
    required this.typeSettings,
    required this.deliveryMethodSettings,
    required this.updatedAt,
  });

  /// Create NotificationPreferences from JSON
  factory NotificationPreferences.fromJson(Map<String, dynamic> json) => 
      _$NotificationPreferencesFromJson(json);

  /// Convert NotificationPreferences to JSON
  Map<String, dynamic> toJson() => _$NotificationPreferencesToJson(this);

  /// Create a copy with updated fields
  NotificationPreferences copyWith({
    String? userId,
    bool? globalEnabled,
    QuietHours? quietHours,
    Map<NotificationType, NotificationTypeSettings>? typeSettings,
    Map<DeliveryMethod, bool>? deliveryMethodSettings,
    DateTime? updatedAt,
  }) {
    return NotificationPreferences(
      userId: userId ?? this.userId,
      globalEnabled: globalEnabled ?? this.globalEnabled,
      quietHours: quietHours ?? this.quietHours,
      typeSettings: typeSettings ?? this.typeSettings,
      deliveryMethodSettings: deliveryMethodSettings ?? this.deliveryMethodSettings,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        userId,
        globalEnabled,
        quietHours,
        typeSettings,
        deliveryMethodSettings,
        updatedAt,
      ];
}

/// Settings for a specific notification type
@JsonSerializable()
class NotificationTypeSettings extends Equatable {
  /// Whether this notification type is enabled
  final bool enabled;
  
  /// Delivery methods enabled for this type
  final List<DeliveryMethod> enabledMethods;
  
  /// Custom sound for this notification type
  final String? customSound;
  
  /// Whether to show preview in notifications
  final bool showPreview;

  const NotificationTypeSettings({
    this.enabled = true,
    required this.enabledMethods,
    this.customSound,
    this.showPreview = true,
  });

  /// Create NotificationTypeSettings from JSON
  factory NotificationTypeSettings.fromJson(Map<String, dynamic> json) => 
      _$NotificationTypeSettingsFromJson(json);

  /// Convert NotificationTypeSettings to JSON
  Map<String, dynamic> toJson() => _$NotificationTypeSettingsToJson(this);

  @override
  List<Object?> get props => [enabled, enabledMethods, customSound, showPreview];
}


