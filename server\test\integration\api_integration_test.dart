import 'package:test/test.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  group('API Integration Tests', () {
    const baseUrl = 'http://localhost:8080';
    late http.Client client;

    setUp(() {
      client = http.Client();
    });

    tearDown(() {
      client.close();
    });

    group('Authentication API', () {
      test('should handle user registration', () async {
        try {
          final response = await client.post(
            Uri.parse('$baseUrl/api/auth/register'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'email': '<EMAIL>',
              'password': 'TestPassword123!',
              'displayName': 'Test User',
              'acceptTerms': true,
            }),
          );
          
          expect(response.statusCode, anyOf([200, 201, 400, 409]));
          final data = jsonDecode(response.body);
          expect(data, isA<Map<String, dynamic>>());
        } catch (e) {
          print('⚠️  Skipping registration API test: $e');
        }
      });

      test('should handle user login', () async {
        try {
          final response = await client.post(
            Uri.parse('$baseUrl/api/auth/login'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'email': '<EMAIL>',
              'password': 'TestPassword123!',
            }),
          );
          
          expect(response.statusCode, anyOf([200, 401, 404]));
          final data = jsonDecode(response.body);
          expect(data, isA<Map<String, dynamic>>());
        } catch (e) {
          print('⚠️  Skipping login API test: $e');
        }
      });
    });

    group('User API', () {
      test('should get user profile', () async {
        try {
          final response = await client.get(
            Uri.parse('$baseUrl/api/users/profile'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token',
            },
          );
          
          expect(response.statusCode, anyOf([200, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping user profile API test: $e');
        }
      });

      test('should update user profile', () async {
        try {
          final response = await client.put(
            Uri.parse('$baseUrl/api/users/profile'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token',
            },
            body: jsonEncode({
              'displayName': 'Updated Test User',
              'firstName': 'Test',
              'lastName': 'User',
            }),
          );
          
          expect(response.statusCode, anyOf([200, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping user update API test: $e');
        }
      });
    });

    group('Gamification API', () {
      test('should get user stats', () async {
        try {
          final response = await client.get(
            Uri.parse('$baseUrl/api/gamification/stats'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token',
            },
          );
          
          expect(response.statusCode, anyOf([200, 401]));
        } catch (e) {
          print('⚠️  Skipping gamification stats API test: $e');
        }
      });

      test('should get leaderboard', () async {
        try {
          final response = await client.get(
            Uri.parse('$baseUrl/api/gamification/leaderboard'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token',
            },
          );
          
          expect(response.statusCode, anyOf([200, 401]));
        } catch (e) {
          print('⚠️  Skipping leaderboard API test: $e');
        }
      });
    });

    group('Error Handling', () {
      test('should handle 404 errors gracefully', () async {
        try {
          final response = await client.get(
            Uri.parse('$baseUrl/api/nonexistent'),
          );
          
          expect(response.statusCode, equals(404));
        } catch (e) {
          print('⚠️  Skipping 404 error test: $e');
        }
      });

      test('should handle malformed requests', () async {
        try {
          final response = await client.post(
            Uri.parse('$baseUrl/api/auth/login'),
            headers: {'Content-Type': 'application/json'},
            body: 'invalid json',
          );
          
          expect(response.statusCode, equals(400));
        } catch (e) {
          print('⚠️  Skipping malformed request test: $e');
        }
      });
    });
  });
}
