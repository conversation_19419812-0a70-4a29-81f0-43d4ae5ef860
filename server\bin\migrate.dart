import 'dart:io';
import 'package:logging/logging.dart';
import 'package:server/services/migration_service.dart';
import 'package:server/migrations/migration_definitions.dart';

void main(List<String> arguments) async {
  // Configure logging
  Logger.root.level = Level.INFO;
  Logger.root.onRecord.listen((record) {
    print('${record.level.name}: ${record.time}: ${record.message}');
  });

  final logger = Logger('Migrate');

  if (arguments.isEmpty) {
    _printUsage();
    exit(1);
  }

  final command = arguments[0].toLowerCase();
  final migrationService = MigrationService();

  try {
    await migrationService.initialize();
    
    switch (command) {
      case 'status':
        await _showStatus(migrationService, logger);
        break;
      case 'up':
        await _migrateUp(migrationService, logger, arguments);
        break;
      case 'down':
        await _migrateDown(migrationService, logger, arguments);
        break;
      case 'reset':
        await _resetDatabase(migrationService, logger);
        break;
      case 'validate':
        await _validateSchema(migrationService, logger);
        break;
      case 'create':
        if (arguments.length < 2) {
          logger.severe('Migration name required for create command');
          exit(1);
        }
        await _createMigration(arguments[1], logger);
        break;
      default:
        logger.severe('Unknown command: $command');
        _printUsage();
        exit(1);
    }
  } catch (e) {
    logger.severe('Migration command failed: $e');
    exit(1);
  } finally {
    await migrationService.close();
  }
}

void _printUsage() {
  print('''
Quester Database Migration Tool

Usage:
  dart run bin/migrate.dart <command> [options]

Commands:
  status      - Show current migration status
  up [count]  - Apply migrations (optionally limit count)
  down [count]- Rollback migrations (optionally limit count)  
  reset       - Reset database (WARNING: destructive)
  validate    - Validate current schema
  create <name> - Create new migration file

Examples:
  dart run bin/migrate.dart status
  dart run bin/migrate.dart up
  dart run bin/migrate.dart up 3
  dart run bin/migrate.dart down 1
  dart run bin/migrate.dart validate
  dart run bin/migrate.dart create add_user_preferences
''');
}

Future<void> _showStatus(MigrationService service, Logger logger) async {
  logger.info('Getting database status...');
  
  final status = await service.getDatabaseStatus();
  final allMigrations = MigrationDefinitions.getAllMigrations();
  
  print('\n=== Database Status ===');
  print('Database initialized: ${status.isInitialized}');
  print('Applied migrations: ${status.appliedMigrations.length}');
  print('Available migrations: ${allMigrations.length}');
  print('Available tables: ${status.availableTables.length}');
  print('Installed extensions: ${status.installedExtensions.join(', ')}');
  
  print('\n=== Migration Details ===');
  for (final migration in allMigrations) {
    final isApplied = status.appliedMigrations.contains(migration.version);
    final statusIcon = isApplied ? '✓' : '✗';
    print('$statusIcon ${migration.version}: ${migration.description}');
  }
  
  final pendingCount = allMigrations.length - status.appliedMigrations.length;
  if (pendingCount > 0) {
    print('\n$pendingCount migration(s) pending. Run "dart run bin/migrate.dart up" to apply.');
  } else {
    print('\nAll migrations are up to date.');
  }
}

Future<void> _migrateUp(MigrationService service, Logger logger, List<String> args) async {
  final allMigrations = MigrationDefinitions.getAllMigrations();
  final appliedMigrations = await service.getAppliedMigrations();
  
  final pendingMigrations = allMigrations
      .where((m) => !appliedMigrations.contains(m.version))
      .toList();
  
  if (pendingMigrations.isEmpty) {
    logger.info('No pending migrations to apply');
    return;
  }
  
  int? limit;
  if (args.length > 1) {
    limit = int.tryParse(args[1]);
    if (limit == null || limit <= 0) {
      logger.severe('Invalid migration count: ${args[1]}');
      exit(1);
    }
  }
  
  final migrationsToApply = limit != null 
      ? pendingMigrations.take(limit).toList()
      : pendingMigrations;
  
  logger.info('Applying ${migrationsToApply.length} migration(s)...');
  
  for (final migration in migrationsToApply) {
    await service.applyMigration(migration);
  }
  
  logger.info('Migration complete!');
}

Future<void> _migrateDown(MigrationService service, Logger logger, List<String> args) async {
  final appliedMigrations = await service.getAppliedMigrations();
  
  if (appliedMigrations.isEmpty) {
    logger.info('No migrations to rollback');
    return;
  }
  
  int count = 1;
  if (args.length > 1) {
    final parsedCount = int.tryParse(args[1]);
    if (parsedCount == null || parsedCount <= 0) {
      logger.severe('Invalid rollback count: ${args[1]}');
      exit(1);
    }
    count = parsedCount;
  }
  
  final allMigrations = MigrationDefinitions.getAllMigrations();
  final migrationsToRollback = <Migration>[];
  
  // Get migrations to rollback in reverse order
  for (int i = appliedMigrations.length - 1; i >= 0 && migrationsToRollback.length < count; i--) {
    final version = appliedMigrations[i];
    final migration = allMigrations.firstWhere((m) => m.version == version);
    migrationsToRollback.add(migration);
  }
  
  logger.info('Rolling back ${migrationsToRollback.length} migration(s)...');
  
  for (final migration in migrationsToRollback) {
    await service.rollbackMigration(migration);
  }
  
  logger.info('Rollback complete!');
}

Future<void> _resetDatabase(MigrationService service, Logger logger) async {
  print('WARNING: This will destroy all data in the database!');
  print('Type "yes" to confirm: ');
  
  final confirmation = stdin.readLineSync();
  if (confirmation?.toLowerCase() != 'yes') {
    logger.info('Reset cancelled');
    return;
  }
  
  logger.info('Resetting database...');
  
  // Get all applied migrations in reverse order
  final appliedMigrations = await service.getAppliedMigrations();
  final allMigrations = MigrationDefinitions.getAllMigrations();
  
  for (int i = appliedMigrations.length - 1; i >= 0; i--) {
    final version = appliedMigrations[i];
    try {
      final migration = allMigrations.firstWhere((m) => m.version == version);
      await service.rollbackMigration(migration);
    } catch (e) {
      logger.warning('Could not rollback $version: $e');
    }
  }
  
  logger.info('Database reset complete!');
}

Future<void> _validateSchema(MigrationService service, Logger logger) async {
  logger.info('Validating database schema...');
  
  try {
    await service.validateSchema();
    logger.info('Schema validation passed ✓');
  } catch (e) {
    logger.severe('Schema validation failed: $e');
    exit(1);
  }
}

Future<void> _createMigration(String name, Logger logger) async {
  final timestamp = DateTime.now().toIso8601String().replaceAll(RegExp(r'[:\-.]'), '');
  final version = '${timestamp}_$name';
  final filename = 'migration_$version.dart';
  final filepath = 'server/lib/migrations/$filename';
  
  final template = '''
import '../services/migration_service.dart';

class Migration$version {
  static Migration create() {
    return Migration(
      version: '$version',
      description: 'TODO: Add description for $name',
      checksum: Migration.generateChecksum(_sql),
      sql: _sql,
      rollbackSql: _rollbackSql,
    );
  }

  static const String _sql = \'\'\'
-- TODO: Add your migration SQL here
-- Example:
-- CREATE TABLE IF NOT EXISTS quester.example (
--     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
--     name VARCHAR(255) NOT NULL,
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
-- );
\'\'\';

  static const String _rollbackSql = \'\'\'
-- TODO: Add rollback SQL here
-- Example:
-- DROP TABLE IF EXISTS quester.example;
\'\'\';
}
''';

  final file = File(filepath);
  await file.create(recursive: true);
  await file.writeAsString(template);

  logger.info('Created migration file: $filepath');
  logger.info('Remember to:');
  logger.info('1. Add the migration SQL');
  logger.info('2. Add the rollback SQL (if applicable)');
  logger.info('3. Add the migration to MigrationDefinitions.getAllMigrations()');
}