import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

/// Database encryption service for handling sensitive data encryption
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  /// Master encryption key from environment
  String get _masterKey => Platform.environment['DB_ENCRYPTION_KEY'] ?? _generateMasterKey();

  /// Generate a secure master key if none is provided
  String _generateMasterKey() {
    final random = Random.secure();
    final keyBytes = List<int>.generate(32, (i) => random.nextInt(256));
    final key = base64.encode(keyBytes);
    
    print('⚠️  WARNING: Using generated encryption key. Set DB_ENCRYPTION_KEY environment variable for production!');
    print('🔑 Generated key: $key');
    
    return key;
  }

  /// Encrypt sensitive data with AES-like encryption
  String encryptData(String plaintext) {
    if (plaintext.isEmpty) return '';

    try {
      final keyBytes = sha256.convert(utf8.encode(_masterKey)).bytes;
      final plaintextBytes = utf8.encode(plaintext);
      
      // Generate random IV
      final iv = List<int>.generate(16, (i) => Random.secure().nextInt(256));
      
      // Encrypt using XOR with key derivation (simplified AES-like approach)
      final encrypted = <int>[];
      encrypted.addAll(iv); // Prepend IV
      
      for (int i = 0; i < plaintextBytes.length; i++) {
        final keyIndex = (i + iv[i % iv.length]) % keyBytes.length;
        final encryptedByte = plaintextBytes[i] ^ keyBytes[keyIndex] ^ iv[i % iv.length];
        encrypted.add(encryptedByte);
      }
      
      return base64.encode(encrypted);
    } catch (e) {
      print('❌ Encryption error: $e');
      throw Exception('Failed to encrypt data');
    }
  }

  /// Decrypt sensitive data
  String decryptData(String encryptedData) {
    if (encryptedData.isEmpty) return '';

    try {
      final keyBytes = sha256.convert(utf8.encode(_masterKey)).bytes;
      final encryptedBytes = base64.decode(encryptedData);
      
      if (encryptedBytes.length < 16) {
        throw Exception('Invalid encrypted data format');
      }
      
      // Extract IV and encrypted data
      final iv = encryptedBytes.sublist(0, 16);
      final ciphertext = encryptedBytes.sublist(16);
      
      // Decrypt
      final decrypted = <int>[];
      for (int i = 0; i < ciphertext.length; i++) {
        final keyIndex = (i + iv[i % iv.length]) % keyBytes.length;
        final decryptedByte = ciphertext[i] ^ keyBytes[keyIndex] ^ iv[i % iv.length];
        decrypted.add(decryptedByte);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      print('❌ Decryption error: $e');
      throw Exception('Failed to decrypt data');
    }
  }

  /// Hash password with salt
  String hashPassword(String password, {String? salt}) {
    salt ??= _generateSalt();
    final saltedPassword = password + salt;
    final hash = sha256.convert(utf8.encode(saltedPassword));
    return '$salt:${hash.toString()}';
  }

  /// Verify password against hash
  bool verifyPassword(String password, String hashedPassword) {
    try {
      final parts = hashedPassword.split(':');
      if (parts.length != 2) return false;
      
      final salt = parts[0];
      final expectedHash = parts[1];
      
      final saltedPassword = password + salt;
      final actualHash = sha256.convert(utf8.encode(saltedPassword)).toString();
      
      return actualHash == expectedHash;
    } catch (e) {
      print('❌ Password verification error: $e');
      return false;
    }
  }

  /// Generate secure salt
  String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  /// Generate secure token
  String generateSecureToken({int length = 32}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Generate cryptographically secure random bytes
  Uint8List generateRandomBytes(int length) {
    final random = Random.secure();
    return Uint8List.fromList(List<int>.generate(length, (i) => random.nextInt(256)));
  }

  /// Encrypt PII data with additional metadata
  Map<String, dynamic> encryptPII(String data, String dataType) {
    final encrypted = encryptData(data);
    final timestamp = DateTime.now().toUtc().toIso8601String();
    final checksum = sha256.convert(utf8.encode(data)).toString();
    
    return {
      'encrypted_data': encrypted,
      'data_type': dataType,
      'encrypted_at': timestamp,
      'checksum': checksum,
      'version': '1.0',
    };
  }

  /// Decrypt PII data and verify integrity
  String decryptPII(Map<String, dynamic> encryptedPII) {
    final encryptedData = encryptedPII['encrypted_data'] as String;
    final expectedChecksum = encryptedPII['checksum'] as String;
    
    final decrypted = decryptData(encryptedData);
    final actualChecksum = sha256.convert(utf8.encode(decrypted)).toString();
    
    if (actualChecksum != expectedChecksum) {
      throw Exception('PII data integrity check failed');
    }
    
    return decrypted;
  }

  /// Validate encryption key strength
  bool validateKeyStrength(String key) {
    if (key.length < 32) return false;
    
    // Check for sufficient entropy
    final keyBytes = utf8.encode(key);
    final uniqueBytes = keyBytes.toSet();
    
    return uniqueBytes.length >= 16; // At least 16 unique characters
  }

  /// Generate database encryption functions SQL
  String generateEncryptionFunctionsSQL() {
    return '''
-- Database encryption functions using pgcrypto
CREATE OR REPLACE FUNCTION encrypt_pii(data TEXT, key_name TEXT DEFAULT 'default')
RETURNS BYTEA AS \$\$
BEGIN
    RETURN pgp_sym_encrypt(data, current_setting('app.encryption_key', true));
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Encryption failed: %', SQLERRM;
END;
\$\$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION decrypt_pii(encrypted_data BYTEA, key_name TEXT DEFAULT 'default')
RETURNS TEXT AS \$\$
BEGIN
    RETURN pgp_sym_decrypt(encrypted_data, current_setting('app.encryption_key', true));
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Decryption failed: %', SQLERRM;
END;
\$\$ LANGUAGE plpgsql SECURITY DEFINER;

-- Secure views for encrypted data access
CREATE OR REPLACE VIEW secure_user_pii AS
SELECT 
    id,
    email,
    CASE 
        WHEN phone_encrypted IS NOT NULL THEN decrypt_pii(phone_encrypted)
        ELSE phone
    END as phone,
    CASE 
        WHEN address_encrypted IS NOT NULL THEN decrypt_pii(address_encrypted)
        ELSE address
    END as address
FROM users
WHERE has_table_privilege(current_user, 'users', 'SELECT');

-- Audit function for encryption operations
CREATE OR REPLACE FUNCTION audit_encryption_access()
RETURNS TRIGGER AS \$\$
BEGIN
    INSERT INTO encryption_audit_log (
        table_name, operation, user_name, access_time, record_id
    ) VALUES (
        TG_TABLE_NAME, TG_OP, current_user, NOW(), 
        COALESCE(NEW.id, OLD.id)
    );
    RETURN COALESCE(NEW, OLD);
END;
\$\$ LANGUAGE plpgsql;
''';
  }

  /// Test encryption/decryption functionality
  bool testEncryption() {
    try {
      const testData = 'Test encryption data 123!@#';
      final encrypted = encryptData(testData);
      final decrypted = decryptData(encrypted);
      
      final success = decrypted == testData;
      if (success) {
        print('✅ Encryption test passed');
      } else {
        print('❌ Encryption test failed: data mismatch');
      }
      
      return success;
    } catch (e) {
      print('❌ Encryption test failed: $e');
      return false;
    }
  }
}
