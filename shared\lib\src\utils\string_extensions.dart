/// String extension utilities for Quester
extension StringExtensions on String {
  /// Check if string is null or empty
  bool get isNullOrEmpty => isEmpty;

  /// Check if string is not null and not empty
  bool get isNotNullOrEmpty => isNotEmpty;

  /// Capitalize first letter of the string
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }

  /// Capitalize first letter of each word
  String get titleCase {
    if (isEmpty) return this;
    return split(' ').map((word) => word.capitalize).join(' ');
  }

  /// Convert to camelCase
  String get toCamelCase {
    if (isEmpty) return this;
    final words = split(RegExp(r'[\s_-]+'));
    if (words.isEmpty) return this;
    
    return words.first.toLowerCase() + 
           words.skip(1).map((word) => word.capitalize).join('');
  }

  /// Convert to PascalCase
  String get toPascalCase {
    if (isEmpty) return this;
    return split(RegExp(r'[\s_-]+'))
        .map((word) => word.capitalize)
        .join('');
  }

  /// Convert to snake_case
  String get toSnakeCase {
    if (isEmpty) return this;
    return replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => '_${match.group(1)!.toLowerCase()}',
    ).replaceAll(RegExp(r'[\s-]+'), '_').toLowerCase();
  }

  /// Convert to kebab-case
  String get toKebabCase {
    if (isEmpty) return this;
    return replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => '-${match.group(1)!.toLowerCase()}',
    ).replaceAll(RegExp(r'[\s_]+'), '-').toLowerCase();
  }

  /// Remove all whitespace
  String get removeWhitespace => replaceAll(RegExp(r'\s+'), '');

  /// Remove extra whitespace and trim
  String get normalizeWhitespace => trim().replaceAll(RegExp(r'\s+'), ' ');

  /// Truncate string to specified length with ellipsis
  String truncate(int maxLength, [String ellipsis = '...']) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength - ellipsis.length)}$ellipsis';
  }

  /// Truncate string at word boundary
  String truncateAtWord(int maxLength, [String ellipsis = '...']) {
    if (length <= maxLength) return this;
    
    final truncated = substring(0, maxLength - ellipsis.length);
    final lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > 0) {
      return '${truncated.substring(0, lastSpace)}$ellipsis';
    }
    
    return truncated + ellipsis;
  }

  /// Check if string contains only alphabetic characters
  bool get isAlpha => RegExp(r'^[a-zA-Z]+$').hasMatch(this);

  /// Check if string contains only numeric characters
  bool get isNumeric => RegExp(r'^[0-9]+$').hasMatch(this);

  /// Check if string contains only alphanumeric characters
  bool get isAlphanumeric => RegExp(r'^[a-zA-Z0-9]+$').hasMatch(this);

  /// Check if string is a valid email format
  bool get isEmail => RegExp(
        r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$',
      ).hasMatch(this);

  /// Check if string is a valid URL format
  bool get isUrl => RegExp(
        r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
      ).hasMatch(this);

  /// Check if string is a valid phone number format
  bool get isPhoneNumber => RegExp(r'^\+?[\d\s\-\(\)]{10,15}$').hasMatch(this);

  /// Count words in string
  int get wordCount => split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;

  /// Get initials from name
  String get initials {
    final words = split(' ').where((word) => word.isNotEmpty);
    if (words.isEmpty) return '';
    if (words.length == 1) return words.first[0].toUpperCase();
    return words.take(2).map((word) => word[0].toUpperCase()).join();
  }

  /// Reverse the string
  String get reverse => split('').reversed.join('');

  /// Check if string is palindrome
  bool get isPalindrome {
    final cleaned = toLowerCase().removeWhitespace;
    return cleaned == cleaned.reverse;
  }

  /// Convert to slug format (URL-friendly)
  String get toSlug {
    return toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s-]'), '')
        .normalizeWhitespace
        .replaceAll(' ', '-')
        .replaceAll(RegExp(r'-+'), '-')
        .replaceAll(RegExp(r'^-|-$'), '');
  }

  /// Mask string (useful for sensitive data)
  String mask({int visibleStart = 2, int visibleEnd = 2, String maskChar = '*'}) {
    if (length <= visibleStart + visibleEnd) return this;
    
    final start = substring(0, visibleStart);
    final end = substring(length - visibleEnd);
    final maskLength = length - visibleStart - visibleEnd;
    
    return start + (maskChar * maskLength) + end;
  }

  /// Extract numbers from string
  List<int> get extractNumbers {
    return RegExp(r'\d+')
        .allMatches(this)
        .map((match) => int.parse(match.group(0)!))
        .toList();
  }

  /// Remove HTML tags
  String get stripHtml => replaceAll(RegExp(r'<[^>]*>'), '');

  /// Convert markdown-style formatting to plain text
  String get stripMarkdown {
    return replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1') // Bold
        .replaceAll(RegExp(r'\*(.*?)\*'), r'$1') // Italic
        .replaceAll(RegExp(r'`(.*?)`'), r'$1') // Code
        .replaceAll(RegExp(r'\[([^\]]+)\]\([^\)]+\)'), r'$1') // Links
        .replaceAll(RegExp(r'^#+\s*', multiLine: true), '') // Headers
        .replaceAll(RegExp(r'^>\s*', multiLine: true), '') // Blockquotes
        .replaceAll(RegExp(r'^\*\s+', multiLine: true), '• ') // Bullet points
        .replaceAll(RegExp(r'^\d+\.\s+', multiLine: true), '') // Numbered lists
        .trim();
  }

  /// Generate random string of specified length
  static String random(int length, {bool includeNumbers = true, bool includeSymbols = false}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    
    String charSet = chars;
    if (includeNumbers) charSet += numbers;
    if (includeSymbols) charSet += symbols;
    
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(length, (index) => charSet[(random + index) % charSet.length]).join();
  }

  /// Calculate Levenshtein distance (string similarity)
  int levenshteinDistance(String other) {
    if (this == other) return 0;
    if (isEmpty) return other.length;
    if (other.isEmpty) return length;

    final matrix = List.generate(
      length + 1,
      (i) => List.generate(other.length + 1, (j) => 0),
    );

    for (int i = 0; i <= length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= other.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= length; i++) {
      for (int j = 1; j <= other.length; j++) {
        final cost = this[i - 1] == other[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[length][other.length];
  }

  /// Calculate string similarity percentage
  double similarity(String other) {
    if (this == other) return 1.0;
    final maxLength = length > other.length ? length : other.length;
    if (maxLength == 0) return 1.0;
    return 1.0 - (levenshteinDistance(other) / maxLength);
  }

  /// Highlight search terms in string
  String highlight(String searchTerm, {String startTag = '<mark>', String endTag = '</mark>'}) {
    if (searchTerm.isEmpty) return this;
    return replaceAllMapped(
      RegExp(RegExp.escape(searchTerm), caseSensitive: false),
      (match) => '$startTag${match.group(0)}$endTag',
    );
  }

  /// Parse string as boolean
  bool? get toBool {
    final lower = toLowerCase();
    if (lower == 'true' || lower == '1' || lower == 'yes' || lower == 'on') {
      return true;
    }
    if (lower == 'false' || lower == '0' || lower == 'no' || lower == 'off') {
      return false;
    }
    return null;
  }

  /// Parse string as integer safely
  int? get toInt => int.tryParse(this);

  /// Parse string as double safely
  double? get toDouble => double.tryParse(this);

  /// Parse string as DateTime safely
  DateTime? get toDateTime => DateTime.tryParse(this);

  /// Check if string matches any in a list (case insensitive)
  bool matchesAny(List<String> strings, {bool caseSensitive = false}) {
    final comparison = caseSensitive ? this : toLowerCase();
    return strings.any((str) => 
      caseSensitive ? str == comparison : str.toLowerCase() == comparison
    );
  }

  /// Replace multiple patterns at once
  String replaceMultiple(Map<String, String> replacements) {
    String result = this;
    for (final entry in replacements.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }
    return result;
  }

  /// Insert string at specified position
  String insertAt(int position, String insertion) {
    if (position < 0 || position > length) return this;
    return substring(0, position) + insertion + substring(position);
  }

  /// Remove string at specified range
  String removeRange(int start, int end) {
    if (start < 0 || end > length || start >= end) return this;
    return substring(0, start) + substring(end);
  }
}

/// Nullable string extensions
extension NullableStringExtensions on String? {
  /// Check if string is null or empty
  bool get isNullOrEmpty => this == null || this!.isEmpty;

  /// Check if string is not null and not empty
  bool get isNotNullOrEmpty => this != null && this!.isNotEmpty;

  /// Get string or default value if null
  String orDefault(String defaultValue) => this ?? defaultValue;

  /// Get string or empty string if null
  String get orEmpty => this ?? '';

  /// Apply extension only if not null
  T? ifNotNull<T>(T Function(String) action) {
    return this != null ? action(this!) : null;
  }
}