import 'dart:convert';
import 'package:postgres/postgres.dart';

/// Service for database query optimization and caching
class QueryOptimizationService {
  static final QueryOptimizationService _instance = QueryOptimizationService._internal();
  factory QueryOptimizationService() => _instance;
  QueryOptimizationService._internal();

  // Query result cache with TTL
  final Map<String, _CachedResult> _queryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // Performance metrics
  final Map<String, List<Duration>> _queryPerformance = {};
  final Map<String, int> _queryExecutionCounts = {};
  
  // Configuration
  static const Duration _defaultCacheTTL = Duration(minutes: 5);
  static const int _maxCacheSize = 1000;
  static const Duration _performanceThreshold = Duration(milliseconds: 50);

  /// Execute query with caching and performance monitoring
  Future<Result> executeOptimizedQuery(
    Connection connection,
    String sql,
    Map<String, dynamic> parameters, {
    Duration? cacheTTL,
    bool enableCache = true,
    String? queryKey,
  }) async {
    final stopwatch = Stopwatch()..start();
    final cacheKey = queryKey ?? _generateCacheKey(sql, parameters);
    
    try {
      // Check cache first
      if (enableCache && _isCacheValid(cacheKey, cacheTTL ?? _defaultCacheTTL)) {
        final cached = _queryCache[cacheKey]!;
        _recordCacheHit(cacheKey);
        return cached.result;
      }

      // Execute query
      final result = await connection.execute(
        Sql.named(sql),
        parameters: parameters,
      );

      stopwatch.stop();
      
      // Cache result if enabled
      if (enableCache) {
        _cacheResult(cacheKey, result, cacheTTL ?? _defaultCacheTTL);
      }
      
      // Record performance metrics
      _recordQueryPerformance(cacheKey, stopwatch.elapsed);
      
      // Log slow queries
      if (stopwatch.elapsed > _performanceThreshold) {
        print('⚠️ Slow query detected: $cacheKey - ${stopwatch.elapsed.inMilliseconds}ms');
        print('🔍 SQL: ${sql.substring(0, 100)}...');
      }
      
      return result;
      
    } catch (e) {
      stopwatch.stop();
      _recordQueryError(cacheKey, e, stopwatch.elapsed);
      rethrow;
    }
  }

  /// Get query performance statistics
  Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{};
    
    for (final entry in _queryPerformance.entries) {
      final queryKey = entry.key;
      final durations = entry.value;
      
      if (durations.isNotEmpty) {
        final avgMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a + b) / durations.length;
        final maxMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b);
        final minMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b);
        
        stats[queryKey] = {
          'execution_count': _queryExecutionCounts[queryKey] ?? 0,
          'avg_response_time_ms': avgMs.round(),
          'max_response_time_ms': maxMs,
          'min_response_time_ms': minMs,
          'cache_hit_ratio': _getCacheHitRatio(queryKey),
        };
      }
    }
    
    return {
      'query_stats': stats,
      'cache_size': _queryCache.length,
      'cache_hit_ratio_overall': _getOverallCacheHitRatio(),
      'slow_queries_count': _getSlowQueriesCount(),
    };
  }

  /// Clear query cache
  void clearCache() {
    _queryCache.clear();
    _cacheTimestamps.clear();
    print('🧹 Query cache cleared');
  }

  /// Clear performance metrics
  void clearPerformanceMetrics() {
    _queryPerformance.clear();
    _queryExecutionCounts.clear();
    print('🧹 Performance metrics cleared');
  }

  /// Generate cache key from SQL and parameters
  String _generateCacheKey(String sql, Map<String, dynamic> parameters) {
    final normalizedSql = sql.replaceAll(RegExp(r'\s+'), ' ').trim();
    final paramString = jsonEncode(parameters);
    return '${normalizedSql.hashCode}_${paramString.hashCode}';
  }

  /// Check if cached result is still valid
  bool _isCacheValid(String cacheKey, Duration ttl) {
    if (!_queryCache.containsKey(cacheKey)) return false;
    
    final timestamp = _cacheTimestamps[cacheKey];
    if (timestamp == null) return false;
    
    return DateTime.now().difference(timestamp) < ttl;
  }

  /// Cache query result
  void _cacheResult(String cacheKey, Result result, Duration ttl) {
    // Implement cache size limit
    if (_queryCache.length >= _maxCacheSize) {
      _evictOldestCacheEntry();
    }
    
    _queryCache[cacheKey] = _CachedResult(result, 0);
    _cacheTimestamps[cacheKey] = DateTime.now();
  }

  /// Record cache hit
  void _recordCacheHit(String cacheKey) {
    if (_queryCache.containsKey(cacheKey)) {
      _queryCache[cacheKey]!.hitCount++;
    }
  }

  /// Record query performance
  void _recordQueryPerformance(String cacheKey, Duration duration) {
    _queryPerformance.putIfAbsent(cacheKey, () => []);
    _queryPerformance[cacheKey]!.add(duration);
    
    // Keep only last 100 measurements per query
    if (_queryPerformance[cacheKey]!.length > 100) {
      _queryPerformance[cacheKey]!.removeAt(0);
    }
    
    _queryExecutionCounts[cacheKey] = (_queryExecutionCounts[cacheKey] ?? 0) + 1;
  }

  /// Record query error
  void _recordQueryError(String cacheKey, dynamic error, Duration duration) {
    print('❌ Query error for $cacheKey: $error (${duration.inMilliseconds}ms)');
  }

  /// Evict oldest cache entry
  void _evictOldestCacheEntry() {
    if (_cacheTimestamps.isEmpty) return;
    
    final oldestKey = _cacheTimestamps.entries
        .reduce((a, b) => a.value.isBefore(b.value) ? a : b)
        .key;
    
    _queryCache.remove(oldestKey);
    _cacheTimestamps.remove(oldestKey);
  }

  /// Get cache hit ratio for specific query
  double _getCacheHitRatio(String cacheKey) {
    final cached = _queryCache[cacheKey];
    final executions = _queryExecutionCounts[cacheKey] ?? 0;
    
    if (cached == null || executions == 0) return 0.0;
    
    return cached.hitCount / (cached.hitCount + executions);
  }

  /// Get overall cache hit ratio
  double _getOverallCacheHitRatio() {
    final totalHits = _queryCache.values.fold(0, (sum, cached) => sum + cached.hitCount);
    final totalExecutions = _queryExecutionCounts.values.fold(0, (sum, count) => sum + count);
    
    if (totalExecutions == 0) return 0.0;
    return totalHits / (totalHits + totalExecutions);
  }

  /// Get count of slow queries
  int _getSlowQueriesCount() {
    return _queryPerformance.values
        .expand((durations) => durations)
        .where((duration) => duration > _performanceThreshold)
        .length;
  }
}

/// Cached query result with hit count
class _CachedResult {
  final Result result;
  int hitCount;
  
  _CachedResult(this.result, this.hitCount);
}

/// Query optimization hints and utilities
class QueryOptimizationHints {
  /// Common query patterns that benefit from caching
  static const Set<String> cacheablePatterns = {
    'SELECT COUNT(*)',
    'SELECT * FROM achievements',
    'SELECT * FROM leaderboards',
    'SELECT * FROM user_points WHERE',
  };
  
  /// Queries that should not be cached
  static const Set<String> nonCacheablePatterns = {
    'INSERT',
    'UPDATE',
    'DELETE',
    'CREATE',
    'DROP',
    'ALTER',
  };
  
  /// Check if query should be cached
  static bool shouldCache(String sql) {
    final upperSql = sql.toUpperCase().trim();
    
    // Don't cache write operations
    for (final pattern in nonCacheablePatterns) {
      if (upperSql.startsWith(pattern)) return false;
    }
    
    // Cache read operations by default
    return upperSql.startsWith('SELECT');
  }
  
  /// Get recommended cache TTL based on query type
  static Duration getRecommendedTTL(String sql) {
    final upperSql = sql.toUpperCase();
    
    if (upperSql.contains('LEADERBOARD')) return Duration(minutes: 2);
    if (upperSql.contains('ACHIEVEMENT')) return Duration(hours: 1);
    if (upperSql.contains('USER_POINTS')) return Duration(minutes: 5);
    if (upperSql.contains('ACTIVITY_LOG')) return Duration(minutes: 1);
    
    return Duration(minutes: 5); // Default
  }
}
