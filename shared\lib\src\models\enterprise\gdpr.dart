import 'package:equatable/equatable.dart';

/// GDPR data processing purposes
enum DataProcessingPurpose {
  // Legal basis under GDPR
  consent,
  contract,
  legalObligation,
  vitalInterests,
  publicTask,
  legitimateInterests,
  
  // Specific purposes
  authentication,
  userManagement,
  analytics,
  marketing,
  customerSupport,
  billing,
  security,
  compliance,
}

/// GDPR data categories
enum DataCategory {
  // Personal data categories
  identityData,
  contactData,
  profileData,
  usageData,
  technicalData,
  marketingData,
  financialData,
  
  // Special categories (Article 9)
  healthData,
  biometricData,
  geneticData,
  raceEthnicOrigin,
  politicalOpinions,
  religiousBeliefs,
  tradeUnionMembership,
  sexualOrientation,
  criminalData,
}

/// GDPR data subject rights
enum DataSubjectRight {
  access,
  rectification,
  erasure,
  restrictProcessing,
  dataPortability,
  object,
  withdrawConsent,
  notAutomatedDecision,
}

/// GDPR consent status
enum ConsentStatus {
  given,
  withdrawn,
  expired,
  pending,
  declined,
}

/// GDPR data processing record
class DataProcessingRecord extends Equatable {
  /// Unique record identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Data controller name
  final String dataController;

  /// Data processor name (if applicable)
  final String? dataProcessor;

  /// Processing activity name
  final String activityName;

  /// Processing purpose
  final DataProcessingPurpose purpose;

  /// Legal basis for processing
  final String legalBasis;

  /// Data categories being processed
  final List<DataCategory> dataCategories;

  /// Data sources
  final List<String> dataSources;

  /// Data recipients
  final List<String> dataRecipients;

  /// Third country transfers
  final List<String> thirdCountryTransfers;

  /// Retention period
  final String retentionPeriod;

  /// Technical and organizational measures
  final List<String> securityMeasures;

  /// Data Protection Impact Assessment (DPIA) required
  final bool dpiaRequired;

  /// DPIA reference
  final String? dpiaReference;

  /// Record creation date
  final DateTime createdAt;

  /// Last update date
  final DateTime updatedAt;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  const DataProcessingRecord({
    required this.id,
    required this.organizationId,
    required this.dataController,
    this.dataProcessor,
    required this.activityName,
    required this.purpose,
    required this.legalBasis,
    required this.dataCategories,
    required this.dataSources,
    required this.dataRecipients,
    required this.thirdCountryTransfers,
    required this.retentionPeriod,
    required this.securityMeasures,
    required this.dpiaRequired,
    this.dpiaReference,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  /// Create DataProcessingRecord from JSON
  factory DataProcessingRecord.fromJson(Map<String, dynamic> json) {
    return DataProcessingRecord(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      dataController: json['dataController'] as String,
      dataProcessor: json['dataProcessor'] as String?,
      activityName: json['activityName'] as String,
      purpose: DataProcessingPurpose.values.firstWhere(
        (e) => e.name == json['purpose'],
        orElse: () => DataProcessingPurpose.legitimateInterests,
      ),
      legalBasis: json['legalBasis'] as String,
      dataCategories: (json['dataCategories'] as List<dynamic>)
          .map((c) => DataCategory.values.firstWhere(
                (e) => e.name == c,
                orElse: () => DataCategory.identityData,
              ))
          .toList(),
      dataSources: List<String>.from(json['dataSources'] as List),
      dataRecipients: List<String>.from(json['dataRecipients'] as List),
      thirdCountryTransfers: List<String>.from(json['thirdCountryTransfers'] as List),
      retentionPeriod: json['retentionPeriod'] as String,
      securityMeasures: List<String>.from(json['securityMeasures'] as List),
      dpiaRequired: json['dpiaRequired'] as bool,
      dpiaReference: json['dpiaReference'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
    );
  }

  /// Convert DataProcessingRecord to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'dataController': dataController,
      'dataProcessor': dataProcessor,
      'activityName': activityName,
      'purpose': purpose.name,
      'legalBasis': legalBasis,
      'dataCategories': dataCategories.map((c) => c.name).toList(),
      'dataSources': dataSources,
      'dataRecipients': dataRecipients,
      'thirdCountryTransfers': thirdCountryTransfers,
      'retentionPeriod': retentionPeriod,
      'securityMeasures': securityMeasures,
      'dpiaRequired': dpiaRequired,
      'dpiaReference': dpiaReference,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        dataController,
        dataProcessor,
        activityName,
        purpose,
        legalBasis,
        dataCategories,
        dataSources,
        dataRecipients,
        thirdCountryTransfers,
        retentionPeriod,
        securityMeasures,
        dpiaRequired,
        dpiaReference,
        createdAt,
        updatedAt,
        metadata,
      ];

  @override
  bool get stringify => true;
}

/// GDPR consent record
class ConsentRecord extends Equatable {
  /// Unique consent identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Data subject (user) ID
  final String dataSubjectId;

  /// Processing purpose
  final DataProcessingPurpose purpose;

  /// Consent status
  final ConsentStatus status;

  /// Consent given date
  final DateTime? consentGivenAt;

  /// Consent withdrawn date
  final DateTime? consentWithdrawnAt;

  /// Consent expiry date
  final DateTime? expiresAt;

  /// Consent method (how it was obtained)
  final String consentMethod;

  /// Consent version
  final String consentVersion;

  /// Privacy policy version when consent was given
  final String privacyPolicyVersion;

  /// Granular consent details
  final Map<String, bool> granularConsents;

  /// IP address when consent was given
  final String? ipAddress;

  /// User agent when consent was given
  final String? userAgent;

  /// Consent evidence (e.g., form data, checkbox state)
  final Map<String, dynamic>? consentEvidence;

  /// Withdrawal reason
  final String? withdrawalReason;

  /// Record creation date
  final DateTime createdAt;

  /// Last update date
  final DateTime updatedAt;

  const ConsentRecord({
    required this.id,
    required this.organizationId,
    required this.dataSubjectId,
    required this.purpose,
    required this.status,
    this.consentGivenAt,
    this.consentWithdrawnAt,
    this.expiresAt,
    required this.consentMethod,
    required this.consentVersion,
    required this.privacyPolicyVersion,
    required this.granularConsents,
    this.ipAddress,
    this.userAgent,
    this.consentEvidence,
    this.withdrawalReason,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create ConsentRecord from JSON
  factory ConsentRecord.fromJson(Map<String, dynamic> json) {
    return ConsentRecord(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      dataSubjectId: json['dataSubjectId'] as String,
      purpose: DataProcessingPurpose.values.firstWhere(
        (e) => e.name == json['purpose'],
        orElse: () => DataProcessingPurpose.legitimateInterests,
      ),
      status: ConsentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ConsentStatus.pending,
      ),
      consentGivenAt: json['consentGivenAt'] != null
          ? DateTime.parse(json['consentGivenAt'] as String)
          : null,
      consentWithdrawnAt: json['consentWithdrawnAt'] != null
          ? DateTime.parse(json['consentWithdrawnAt'] as String)
          : null,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      consentMethod: json['consentMethod'] as String,
      consentVersion: json['consentVersion'] as String,
      privacyPolicyVersion: json['privacyPolicyVersion'] as String,
      granularConsents: Map<String, bool>.from(json['granularConsents'] as Map),
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      consentEvidence: json['consentEvidence'] != null
          ? Map<String, dynamic>.from(json['consentEvidence'] as Map)
          : null,
      withdrawalReason: json['withdrawalReason'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert ConsentRecord to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'dataSubjectId': dataSubjectId,
      'purpose': purpose.name,
      'status': status.name,
      'consentGivenAt': consentGivenAt?.toIso8601String(),
      'consentWithdrawnAt': consentWithdrawnAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'consentMethod': consentMethod,
      'consentVersion': consentVersion,
      'privacyPolicyVersion': privacyPolicyVersion,
      'granularConsents': granularConsents,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'consentEvidence': consentEvidence,
      'withdrawalReason': withdrawalReason,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Check if consent is currently valid
  bool get isValid {
    if (status != ConsentStatus.given) return false;
    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) return false;
    return true;
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        dataSubjectId,
        purpose,
        status,
        consentGivenAt,
        consentWithdrawnAt,
        expiresAt,
        consentMethod,
        consentVersion,
        privacyPolicyVersion,
        granularConsents,
        ipAddress,
        userAgent,
        consentEvidence,
        withdrawalReason,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// GDPR data subject request
class DataSubjectRequest extends Equatable {
  /// Unique request identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Data subject (user) ID
  final String dataSubjectId;

  /// Type of right being exercised
  final DataSubjectRight rightType;

  /// Request status
  final String status;

  /// Request description
  final String description;

  /// Requested data categories (for access requests)
  final List<DataCategory>? requestedDataCategories;

  /// Request verification method
  final String verificationMethod;

  /// Verification status
  final bool verified;

  /// Verification date
  final DateTime? verifiedAt;

  /// Request submission date
  final DateTime submittedAt;

  /// Request deadline (30 days from submission)
  final DateTime deadline;

  /// Request completion date
  final DateTime? completedAt;

  /// Response data (for access requests)
  final Map<String, dynamic>? responseData;

  /// Response file URLs
  final List<String>? responseFiles;

  /// Rejection reason (if applicable)
  final String? rejectionReason;

  /// Request metadata
  final Map<String, dynamic>? metadata;

  /// Processing notes
  final String? processingNotes;

  /// Assigned to user ID
  final String? assignedTo;

  /// Record creation date
  final DateTime createdAt;

  /// Last update date
  final DateTime updatedAt;

  const DataSubjectRequest({
    required this.id,
    required this.organizationId,
    required this.dataSubjectId,
    required this.rightType,
    required this.status,
    required this.description,
    this.requestedDataCategories,
    required this.verificationMethod,
    required this.verified,
    this.verifiedAt,
    required this.submittedAt,
    required this.deadline,
    this.completedAt,
    this.responseData,
    this.responseFiles,
    this.rejectionReason,
    this.metadata,
    this.processingNotes,
    this.assignedTo,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create DataSubjectRequest from JSON
  factory DataSubjectRequest.fromJson(Map<String, dynamic> json) {
    return DataSubjectRequest(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      dataSubjectId: json['dataSubjectId'] as String,
      rightType: DataSubjectRight.values.firstWhere(
        (e) => e.name == json['rightType'],
        orElse: () => DataSubjectRight.access,
      ),
      status: json['status'] as String,
      description: json['description'] as String,
      requestedDataCategories: json['requestedDataCategories'] != null
          ? (json['requestedDataCategories'] as List<dynamic>)
              .map((c) => DataCategory.values.firstWhere(
                    (e) => e.name == c,
                    orElse: () => DataCategory.identityData,
                  ))
              .toList()
          : null,
      verificationMethod: json['verificationMethod'] as String,
      verified: json['verified'] as bool,
      verifiedAt: json['verifiedAt'] != null
          ? DateTime.parse(json['verifiedAt'] as String)
          : null,
      submittedAt: DateTime.parse(json['submittedAt'] as String),
      deadline: DateTime.parse(json['deadline'] as String),
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      responseData: json['responseData'] != null
          ? Map<String, dynamic>.from(json['responseData'] as Map)
          : null,
      responseFiles: json['responseFiles'] != null
          ? List<String>.from(json['responseFiles'] as List)
          : null,
      rejectionReason: json['rejectionReason'] as String?,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      processingNotes: json['processingNotes'] as String?,
      assignedTo: json['assignedTo'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert DataSubjectRequest to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'dataSubjectId': dataSubjectId,
      'rightType': rightType.name,
      'status': status,
      'description': description,
      'requestedDataCategories': requestedDataCategories?.map((c) => c.name).toList(),
      'verificationMethod': verificationMethod,
      'verified': verified,
      'verifiedAt': verifiedAt?.toIso8601String(),
      'submittedAt': submittedAt.toIso8601String(),
      'deadline': deadline.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'responseData': responseData,
      'responseFiles': responseFiles,
      'rejectionReason': rejectionReason,
      'metadata': metadata,
      'processingNotes': processingNotes,
      'assignedTo': assignedTo,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Check if request is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(deadline) && completedAt == null;
  }

  /// Get days remaining until deadline
  int get daysRemaining {
    final now = DateTime.now();
    if (now.isAfter(deadline)) return 0;
    return deadline.difference(now).inDays;
  }

  /// Get right type display name
  String get rightDisplayName {
    switch (rightType) {
      case DataSubjectRight.access:
        return 'Right of Access';
      case DataSubjectRight.rectification:
        return 'Right to Rectification';
      case DataSubjectRight.erasure:
        return 'Right to Erasure';
      case DataSubjectRight.restrictProcessing:
        return 'Right to Restrict Processing';
      case DataSubjectRight.dataPortability:
        return 'Right to Data Portability';
      case DataSubjectRight.object:
        return 'Right to Object';
      case DataSubjectRight.withdrawConsent:
        return 'Right to Withdraw Consent';
      case DataSubjectRight.notAutomatedDecision:
        return 'Right Not to be Subject to Automated Decision-making';
    }
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        dataSubjectId,
        rightType,
        status,
        description,
        requestedDataCategories,
        verificationMethod,
        verified,
        verifiedAt,
        submittedAt,
        deadline,
        completedAt,
        responseData,
        responseFiles,
        rejectionReason,
        metadata,
        processingNotes,
        assignedTo,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}
