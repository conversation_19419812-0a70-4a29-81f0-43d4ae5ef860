import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/enterprise_service.dart';

/// Enterprise routes handler
class EnterpriseRoutes {
  static void initialize() {
    // Initialize any required services for enterprise routes
  }

  static Router createRouter() {
    final router = Router()
      // Organization management
      ..post('/organizations', _createOrganizationHandler)
      ..get('/organizations/<orgId>', _getOrganizationHandler)
      ..put('/organizations/<orgId>', _updateOrganizationHandler)
      ..get('/user/<userId>/organizations', _listUserOrganizationsHandler)
      
      // Organization members
      ..post('/organizations/<orgId>/members', _createMemberHandler)
      ..get('/organizations/<orgId>/members', _listMembersHandler)
      ..put('/organizations/<orgId>/members/<memberId>', _updateMemberHandler)
      ..delete('/organizations/<orgId>/members/<memberId>', _deleteMemberHandler)
      
      // Organization roles
      ..get('/organizations/<orgId>/roles', _listRolesHandler)
      ..post('/organizations/<orgId>/roles', _createRoleHandler)
      ..put('/organizations/<orgId>/roles/<roleId>', _updateRoleHandler)
      ..delete('/organizations/<orgId>/roles/<roleId>', _deleteRoleHandler)
      
      // Analytics
      ..get('/organizations/<orgId>/analytics/summary', _getAnalyticsSummaryHandler)
      ..get('/organizations/<orgId>/analytics', _getAnalyticsHandler)
      ..post('/organizations/<orgId>/analytics/reports', _createReportHandler)
      ..get('/organizations/<orgId>/analytics/reports', _listReportsHandler);

    return router;
  }

  static Future<Response> _createOrganizationHandler(Request request) async {
    try {
      final requestData = await request.readAsString();
      final data = jsonDecode(requestData) as Map<String, dynamic>;
      
      final result = await EnterpriseService.createOrganization(data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create organization: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getOrganizationHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      
      final result = await EnterpriseService.getOrganization(orgId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get organization: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _updateOrganizationHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      final requestData = await request.readAsString();
      final data = jsonDecode(requestData) as Map<String, dynamic>;
      
      final result = await EnterpriseService.updateOrganization(orgId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to update organization: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _listUserOrganizationsHandler(Request request) async {
    try {
      final userId = request.params['userId']!;
      
      final result = await EnterpriseService.listOrganizations(userId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to list user organizations: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _createMemberHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      final requestData = await request.readAsString();
      final data = jsonDecode(requestData) as Map<String, dynamic>;
      
      final result = await EnterpriseService.createMember(orgId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create member: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _listMembersHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      
      final result = await EnterpriseService.listMembers(orgId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to list members: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _updateMemberHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      final memberId = request.params['memberId']!;
      final requestData = await request.readAsString();
      final data = jsonDecode(requestData) as Map<String, dynamic>;
      
      final result = await EnterpriseService.updateMember(orgId, memberId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to update member: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Response _deleteMemberHandler(Request request) {
    try {
      final orgId = request.params['orgId']!;
      final memberId = request.params['memberId']!;
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Member deleted successfully',
          'organizationId': orgId,
          'memberId': memberId,
        }),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to delete member: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _listRolesHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      
      final result = await EnterpriseService.listRoles(orgId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to list roles: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _createRoleHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      final requestData = await request.readAsString();
      final data = jsonDecode(requestData) as Map<String, dynamic>;
      
      final result = await EnterpriseService.createRole(orgId, data);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create role: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Response _updateRoleHandler(Request request) {
    try {
      final orgId = request.params['orgId']!;
      final roleId = request.params['roleId']!;
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Role updated successfully',
          'organizationId': orgId,
          'roleId': roleId,
        }),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to update role: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Response _deleteRoleHandler(Request request) {
    try {
      final orgId = request.params['orgId']!;
      final roleId = request.params['roleId']!;
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Role deleted successfully',
          'organizationId': orgId,
          'roleId': roleId,
        }),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to delete role: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getAnalyticsSummaryHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      
      final result = await EnterpriseService.getAnalyticsSummary(orgId);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get analytics summary: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getAnalyticsHandler(Request request) async {
    try {
      final orgId = request.params['orgId']!;
      final queryParams = request.url.queryParameters;
      
      final result = await EnterpriseService.getAnalytics(orgId, queryParams);
      
      return Response.ok(
        jsonEncode(result),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get analytics: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Response _createReportHandler(Request request) {
    try {
      final orgId = request.params['orgId']!;
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Report created successfully',
          'organizationId': orgId,
          'reportId': 'report_${DateTime.now().millisecondsSinceEpoch}',
        }),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create report: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Response _listReportsHandler(Request request) {
    try {
      final orgId = request.params['orgId']!;
      
      final reports = [
        {
          'id': 'report_001',
          'name': 'Weekly Engagement Report',
          'type': 'engagement',
          'createdAt': DateTime.now().subtract(const Duration(days: 7)).toIso8601String(),
        },
        {
          'id': 'report_002',
          'name': 'Monthly Performance Report',
          'type': 'performance',
          'createdAt': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        },
      ];
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': reports,
          'total': reports.length,
          'organizationId': orgId,
        }),
        headers: {'content-type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to list reports: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }
}