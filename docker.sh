#!/bin/bash
# Enhanced Docker management script for Quester project
# Usage: bash docker.sh [ENVIRONMENT] [ACTION] [OPTIONS]
# ENVIRONMENT: dev, staging, prod
# ACTION: build, start, stop, restart, logs, clean, health, backup, restore, update, init
# Version: 2.10.0 - Fixed Docker shared package dependency paths and working directories

set -e

# Default values
ENVIRONMENT="dev"
ACTION="start"
APP_DIR="app"
VERBOSE=false
FORCE=false
BACKUP_DIR="./backups"
PROJECT_NAME="quester"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Help function
show_help() {
    echo -e "${BLUE}Enhanced Quester Docker Management Script v2.10.0${NC}"
    echo -e "${YELLOW}Usage:${NC} bash docker.sh [ENVIRONMENT] [ACTION] [OPTIONS]"
    echo ""
    echo -e "${YELLOW}ENVIRONMENTS:${NC}"
    echo "  dev       - Development environment with hot reload (default)"
    echo "  staging   - Staging environment with monitoring"
    echo "  prod      - Production environment with full stack"
    echo ""
    echo -e "${YELLOW}ACTIONS:${NC}"
    echo "  init      - Initialize environment and create .env files"
    echo "  build     - Build containers with optimizations"
    echo "  start     - Start containers (default)"
    echo "  stop      - Stop containers gracefully"
    echo "  restart   - Restart containers"
    echo "  logs      - Show container logs (use -f for follow)"
    echo "  health    - Check health status of all services"
    echo "  status    - Show detailed container status"
    echo "  backup    - Create database backup"
    echo "  restore   - Restore database from backup"
    echo "  update    - Pull latest images and rebuild"
    echo "  clean     - Stop and remove containers, networks, volumes"
    echo "  reset     - Complete reset - clean + remove images"
    echo "  troubleshoot - Run diagnostics and show troubleshooting info"
    echo "  analyze   - Analyze container logs for common issues"
    echo ""
    echo -e "${YELLOW}OPTIONS:${NC}"
    echo "  -v, --verbose    - Enable verbose output"
    echo "  -f, --force      - Force action without confirmation"
    echo "  --no-cache       - Build without using cache"
    echo "  --service NAME   - Target specific service"
    echo "  --follow         - Follow logs (for logs action)"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  bash docker.sh init                    # Initialize project"
    echo "  bash docker.sh dev start -v           # Start dev with verbose"
    echo "  bash docker.sh prod build --no-cache  # Production build"
    echo "  bash docker.sh logs --follow          # Follow all logs"
    echo "  bash docker.sh backup                 # Backup database"
    echo "  bash docker.sh health                 # Check service health"
    echo "  bash docker.sh troubleshoot           # Run diagnostics"
    echo "  bash docker.sh analyze                # Analyze container logs"
    echo ""
    echo -e "${YELLOW}Platform Compatibility:${NC}"
    echo "  • Optimized for Windows Docker Desktop with WSL2"
    echo "  • Uses linux/amd64 platform for cross-compatibility"  
    echo "  • All services now fully compatible with Windows Docker Desktop"
    echo "  • Redis Commander: Fixed using Node.js base image with npm installation"
    echo "  • MinIO: Fixed using Bitnami image for better platform support"
    echo ""
    echo -e "${YELLOW}Recent Fixes (v2.10.0):${NC}"
    echo "  • Fixed Docker shared package dependency resolution"
    echo "  • Corrected Dockerfile COPY paths for proper build context"
    echo "  • Updated working directories for client and server containers"
    echo "  • Fixed volume mounts in development environment"
}

# Additional variables
NO_CACHE=false
TARGET_SERVICE=""
FOLLOW_LOGS=false

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        dev|staging|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        init|build|start|stop|restart|logs|clean|status|health|backup|restore|update|reset|troubleshoot|analyze)
            ACTION="$1"
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --service)
            TARGET_SERVICE="$2"
            shift 2
            ;;
        --follow)
            FOLLOW_LOGS=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Check if Docker daemon is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Error: Docker daemon is not running. Please start Docker Desktop.${NC}"
        exit 1
    fi
}

# Check if we're in the correct directory
check_directory() {
    if [ ! -d "$APP_DIR" ]; then
        echo -e "${RED}Error: $APP_DIR directory not found. Please run this script from the project root.${NC}"
        echo -e "${YELLOW}Current directory: $(pwd)${NC}"
        echo -e "${YELLOW}Expected structure:${NC}"
        echo "  • $(pwd)/$APP_DIR/ (Docker configuration)"
        echo "  • $(pwd)/server/ (Dart server)"
        echo "  • $(pwd)/client/ (Flutter client)"
        echo "  • $(pwd)/shared/ (Shared package)"
        exit 1
    fi
    
    # Check if essential project directories exist
    local missing_dirs=()
    if [ ! -d "server" ]; then
        missing_dirs+=("server")
    fi
    if [ ! -d "client" ]; then
        missing_dirs+=("client")
    fi
    if [ ! -d "shared" ]; then
        missing_dirs+=("shared")
    fi
    
    if [ ${#missing_dirs[@]} -gt 0 ]; then
        echo -e "${YELLOW}Warning: Missing project directories: ${missing_dirs[*]}${NC}"
        echo -e "${YELLOW}Run 'bash auto-setup.sh' to create missing project structure${NC}"
    fi
}

# Check and fix .env file issues
check_env_file() {
    if [ -f ".env" ]; then
        # Check for JWT_SECRET line breaks (common issue from openssl rand output)
        if grep -q "^JWT_SECRET=" .env; then
            local jwt_line_count=$(grep -c "^JWT_SECRET=" .env)
            if [ "$jwt_line_count" -gt 1 ]; then
                echo -e "${YELLOW}Warning: Multiple JWT_SECRET lines detected, fixing...${NC}"
                # Create backup
                cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
                # Fix the JWT_SECRET by keeping only the first line
                local fixed_jwt=$(grep "^JWT_SECRET=" .env | head -1)
                # Remove all JWT_SECRET lines and add the fixed one
                grep -v "^JWT_SECRET=" .env > .env.tmp
                echo "$fixed_jwt" >> .env.tmp
                mv .env.tmp .env
                echo -e "${GREEN}Fixed JWT_SECRET formatting in .env file${NC}"
                log_verbose "JWT_SECRET line breaks have been fixed"
            fi
        fi
        
        # Check for other potential multi-line environment variables
        local broken_vars=()
        while IFS= read -r line; do
            # Check if line starts with a known env var but doesn't contain '='
            if [[ "$line" =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ ! "$line" =~ = ]]; then
                broken_vars+=("$line")
            fi
        done < .env
        
        if [ ${#broken_vars[@]} -gt 0 ]; then
            echo -e "${YELLOW}Warning: Detected potentially broken environment variables: ${broken_vars[*]}${NC}"
            echo -e "${YELLOW}Consider regenerating .env file with: bash docker.sh init${NC}"
        fi
    fi
}

# Check if docker-compose files exist
check_docker_compose() {
    local missing_files=()
    
    if [ ! -f "docker-compose.base.yml" ]; then
        missing_files+=("docker-compose.base.yml")
    fi
    if [ ! -f "docker-compose.$ENVIRONMENT.yml" ]; then
        missing_files+=("docker-compose.$ENVIRONMENT.yml")
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo -e "${RED}Error: Missing Docker Compose files: ${missing_files[*]}${NC}"
        echo -e "${YELLOW}Available files in $APP_DIR:${NC}"
        ls -la docker-compose*.yml 2>/dev/null || echo "  No docker-compose files found"
        echo ""
        echo -e "${YELLOW}To fix this issue:${NC}"
        echo "1. Run 'bash auto-setup.sh' to generate missing files"
        echo "2. Or manually create the missing Docker Compose files"
        echo ""
        echo -e "${BLUE}Required files for $ENVIRONMENT environment:${NC}"
        echo "  • docker-compose.base.yml (base configuration)"
        echo "  • docker-compose.$ENVIRONMENT.yml (environment-specific overrides)"
        exit 1
    fi
    
    # Check if Dockerfiles exist
    local missing_dockerfiles=()
    if [ ! -f "server.$ENVIRONMENT.dockerfile" ]; then
        missing_dockerfiles+=("server.$ENVIRONMENT.dockerfile")
    fi
    if [ ! -f "client.$ENVIRONMENT.dockerfile" ]; then
        missing_dockerfiles+=("client.$ENVIRONMENT.dockerfile")
    fi
    
    if [ ${#missing_dockerfiles[@]} -gt 0 ]; then
        echo -e "${YELLOW}Warning: Missing Dockerfiles: ${missing_dockerfiles[*]}${NC}"
        echo -e "${YELLOW}Run 'bash auto-setup.sh' to create missing Dockerfiles${NC}"
    fi
}

# Verbose logging function
log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# Confirmation prompt
confirm_action() {
    if [ "$FORCE" = true ]; then
        return 0
    fi
    
    local message="$1"
    echo -e "${YELLOW}$message${NC}"
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0
    else
        echo -e "${RED}Operation cancelled.${NC}"
        exit 1
    fi
}

# Initialize environment
init_environment() {
    echo -e "${BLUE}Initializing Quester environment...${NC}"
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        log_verbose "Creating .env file for $ENVIRONMENT environment"
        
        # Generate secure passwords with proper newline handling
        local postgres_password redis_password jwt_secret grafana_password minio_password
        
        postgres_password=$(openssl rand -base64 32 | tr -d "=+/\n" | cut -c1-25)
        redis_password=$(openssl rand -base64 32 | tr -d "=+/\n" | cut -c1-25)
        jwt_secret=$(openssl rand -base64 64 | tr -d "=+/\n" | cut -c1-50)
        grafana_password=$(openssl rand -base64 16 | tr -d "=+/\n" | cut -c1-12)
        minio_password=$(openssl rand -base64 20 | tr -d "=+/\n" | cut -c1-15)
        
        cat > .env <<EOF
# Quester Environment Configuration - $ENVIRONMENT
# Generated on $(date)
ENVIRONMENT=$ENVIRONMENT
COMPOSE_PROJECT_NAME=$PROJECT_NAME-$ENVIRONMENT

# Database Configuration
POSTGRES_USER=quester
POSTGRES_PASSWORD=$postgres_password
POSTGRES_DB=questerdb
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_PASSWORD=$redis_password
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_HOSTS=local:redis:6379

# JWT Configuration
JWT_SECRET=$jwt_secret

# Server Configuration
DART_ENV=$ENVIRONMENT
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
LOG_LEVEL=$([[ "$ENVIRONMENT" == "dev" ]] && echo "DEBUG" || echo "INFO")
DEBUG=$([[ "$ENVIRONMENT" == "dev" ]] && echo "true" || echo "false")

# Client Configuration
FLUTTER_WEB=true
CLIENT_HOST=0.0.0.0
CLIENT_PORT=3000
API_BASE_URL=http://localhost:8080
WS_BASE_URL=ws://localhost:8080

# Gamification Configuration (Phase 5)
GAMIFICATION_ENABLED=true
ACHIEVEMENTS_CACHE_TTL=3600
LEADERBOARD_UPDATE_INTERVAL=300
POINTS_MULTIPLIER_ENABLED=true
STREAK_BONUS_ENABLED=true

# Enterprise Configuration (Phase 5)
ENTERPRISE_FEATURES_ENABLED=true
RBAC_ENABLED=true
AUDIT_LOG_ENABLED=true
API_RATE_LIMIT=1000
MULTI_TENANT_ENABLED=true
COMPLIANCE_MODE=standard

# Nginx Configuration
NGINX_PORT=80

# External Port Configuration
POSTGRES_EXTERNAL_PORT=5432
REDIS_EXTERNAL_PORT=6379
SERVER_EXTERNAL_PORT=8080
CLIENT_EXTERNAL_PORT=3000
NGINX_EXTERNAL_PORT=80

# Development Tools (dev environment only)
PGADMIN_EXTERNAL_PORT=5050
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=adminpass
REDIS_COMMANDER_PORT=8081
REDIS_COMMANDER_USER=admin
REDIS_COMMANDER_PASSWORD=adminpass
MAILHOG_WEB_PORT=8025
MAILHOG_SMTP_PORT=1025
MINIO_API_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_ROOT_USER=quester
MINIO_ROOT_PASSWORD=$minio_password
SERVER_DEBUG_PORT=9229

# Monitoring (staging/prod)
GRAFANA_PASSWORD=$grafana_password
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Build Configuration
BUILD_TARGET=$([[ "$ENVIRONMENT" == "dev" ]] && echo "development" || echo "$ENVIRONMENT")
EOF
        echo -e "${GREEN}.env file created with secure passwords${NC}"
    else
        echo -e "${YELLOW}.env file already exists, skipping creation${NC}"
    fi
    
    # Create backup directory
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log_verbose "Created backup directory: $BACKUP_DIR"
    fi
    
    # Create monitoring directories for staging/prod
    if [[ "$ENVIRONMENT" == "staging" || "$ENVIRONMENT" == "prod" ]]; then
        mkdir -p app/monitoring/{grafana/{dashboards,datasources},prometheus}
        mkdir -p app/logs/{nginx,server,postgres}
        log_verbose "Created monitoring and logging directories"
    fi
    
    # Create SSL directory for production
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        mkdir -p nginx/ssl
        log_verbose "Created SSL certificate directory"
    fi
    
    echo -e "${GREEN}Environment initialization completed!${NC}"
}

# Get Docker Compose command with proper files
get_compose_command() {
    echo "docker compose -f docker-compose.base.yml -f docker-compose.$ENVIRONMENT.yml"
}

# Check service health
check_health() {
    echo -e "${BLUE}Checking service health for $ENVIRONMENT environment...${NC}"
    
    local compose_cmd
    compose_cmd=$(get_compose_command)
    
    # Base services available in all environments
    local services=("postgres" "redis" "server")
    
    # Add environment-specific services
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        services+=("client" "nginx" "pgadmin" "redis-commander" "mailhog" "minio")
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        services+=("client" "nginx" "prometheus" "grafana" "node-exporter")
    elif [[ "$ENVIRONMENT" == "prod" ]]; then
        services+=("client" "nginx")
    fi
    
    for service in "${services[@]}"; do
        echo -n "  Checking $service... "
        if $compose_cmd ps -q "$service" > /dev/null 2>&1; then
            local container_status
            container_status=$($compose_cmd ps --format "table {{.Service}}\t{{.Status}}" | grep "$service" | awk '{print $2}')
            if [[ "$container_status" == *"healthy"* || "$container_status" == *"running"* || "$container_status" == *"Up"* ]]; then
                echo -e "${GREEN}✓ Running${NC}"
            else
                echo -e "${RED}✗ Unhealthy ($container_status)${NC}"
                # Show last few log lines for unhealthy services
                if [[ "$container_status" == *"unhealthy"* || "$container_status" == *"Restarting"* ]]; then
                    echo -e "${YELLOW}    Last few log lines:${NC}"
                    $compose_cmd logs --tail=3 "$service" 2>/dev/null | sed 's/^/      /'
                    
                    # Special handling for client health issues
                    if [[ "$service" == "client" && "$container_status" == *"unhealthy"* ]]; then
                        echo -e "${CYAN}    Note: Flutter web may need more time to start (health check timeout: 60s)${NC}"
                    fi
                fi
            fi
        else
            echo -e "${RED}✗ Not running${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}Service URLs:${NC}"
    if [ -f ".env" ]; then
        source .env
        echo -e "  🌐 Client (Flutter):      http://localhost:${CLIENT_EXTERNAL_PORT:-3000}"
        echo -e "  🚀 Server API:            http://localhost:${SERVER_EXTERNAL_PORT:-8080}"
        echo -e "  🔍 Health Check:          http://localhost:${SERVER_EXTERNAL_PORT:-8080}/health"
        echo -e "  🎮 Gamification API:      http://localhost:${SERVER_EXTERNAL_PORT:-8080}/gamification/health"
        echo -e "  🏢 Enterprise API:        http://localhost:${SERVER_EXTERNAL_PORT:-8080}/enterprise/health"
        
        if [[ "$ENVIRONMENT" != "dev" ]]; then
            echo -e "  🌍 Nginx (Load Balancer): http://localhost:${NGINX_EXTERNAL_PORT:-80}"
        fi
        
        if [[ "$ENVIRONMENT" == "dev" ]]; then
            echo -e "  🗄️  pgAdmin:               http://localhost:${PGADMIN_EXTERNAL_PORT:-5050}"
            echo -e "  📊 Redis Commander:       http://localhost:${REDIS_COMMANDER_PORT:-8081}"
            echo -e "  📧 MailHog:               http://localhost:${MAILHOG_WEB_PORT:-8025}"
            echo -e "  📦 MinIO Console:         http://localhost:${MINIO_CONSOLE_PORT:-9001}"
        fi
        
        if [[ "$ENVIRONMENT" == "staging" ]]; then
            echo -e "  📊 Grafana:               http://localhost:${GRAFANA_PORT:-3001}"
            echo -e "  🔍 Prometheus:            http://localhost:${PROMETHEUS_PORT:-9090}"
        fi
    fi
}

# Backup database
backup_database() {
    echo -e "${BLUE}Creating database backup for $ENVIRONMENT environment...${NC}"
    
    local compose_cmd=$(get_compose_command)
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$BACKUP_DIR/postgres_backup_${ENVIRONMENT}_${timestamp}.sql"
    
    # Ensure backup directory exists
    mkdir -p "$BACKUP_DIR"
    
    # Source environment variables
    if [ -f ".env" ]; then
        source .env
    fi
    
    log_verbose "Creating backup file: $backup_file"
    
    if $compose_cmd exec -T postgres pg_dump -U "${POSTGRES_USER:-quester}" "${POSTGRES_DB:-questerdb}" > "$backup_file"; then
        echo -e "${GREEN}Database backup created: $backup_file${NC}"
        
        # Compress the backup
        gzip "$backup_file"
        echo -e "${GREEN}Backup compressed: $backup_file.gz${NC}"
        
        # Clean up old backups (keep last 5)
        ls -t "$BACKUP_DIR"/postgres_backup_${ENVIRONMENT}_*.sql.gz 2>/dev/null | tail -n +6 | xargs -r rm
        log_verbose "Cleaned up old backups"
    else
        echo -e "${RED}Failed to create database backup${NC}"
        exit 1
    fi
}

# Restore database
restore_database() {
    echo -e "${BLUE}Available backups for $ENVIRONMENT:${NC}"
    
    local backup_files=($(ls -t "$BACKUP_DIR"/postgres_backup_${ENVIRONMENT}_*.sql.gz 2>/dev/null))
    
    if [ ${#backup_files[@]} -eq 0 ]; then
        echo -e "${RED}No backup files found for $ENVIRONMENT environment${NC}"
        exit 1
    fi
    
    for i in "${!backup_files[@]}"; do
        echo "  $((i+1)). $(basename "${backup_files[$i]}")"
    done
    
    read -p "Select backup number to restore (1-${#backup_files[@]}): " backup_num
    
    if [[ "$backup_num" =~ ^[0-9]+$ ]] && [ "$backup_num" -ge 1 ] && [ "$backup_num" -le ${#backup_files[@]} ]; then
        local selected_backup="${backup_files[$((backup_num-1))]}"
        
        confirm_action "This will restore database from: $(basename "$selected_backup")"
        
        local compose_cmd=$(get_compose_command)
        
        # Source environment variables
        if [ -f ".env" ]; then
            source .env
        fi
        
        echo -e "${BLUE}Restoring database...${NC}"
        
        # Decompress and restore
        if gunzip -c "$selected_backup" | $compose_cmd exec -T postgres psql -U "${POSTGRES_USER:-quester}" -d "${POSTGRES_DB:-questerdb}"; then
            echo -e "${GREEN}Database restored successfully!${NC}"
        else
            echo -e "${RED}Failed to restore database${NC}"
            exit 1
        fi
    else
        echo -e "${RED}Invalid selection${NC}"
        exit 1
    fi
}

# Update services
update_services() {
    echo -e "${BLUE}Updating services for $ENVIRONMENT environment...${NC}"
    
    local compose_cmd=$(get_compose_command)
    
    log_verbose "Pulling latest images"
    $compose_cmd pull
    
    log_verbose "Rebuilding services"
    if [ "$NO_CACHE" = true ]; then
        $compose_cmd build --no-cache
    else
        $compose_cmd build
    fi
    
    echo -e "${GREEN}Services updated successfully!${NC}"
}

# Analyze logs function
analyze_logs() {
    echo -e "${BLUE}Analyzing container logs for common issues...${NC}"
    echo ""
    
    local compose_cmd
    compose_cmd=$(get_compose_command)
    
    # Base services available in all environments
    local services=("postgres" "redis" "server")
    
    # Add environment-specific services
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        services+=("client" "nginx" "pgadmin" "redis-commander" "mailhog" "minio")
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        services+=("client" "nginx" "prometheus" "grafana" "node-exporter")
    elif [[ "$ENVIRONMENT" == "prod" ]]; then
        services+=("client" "nginx")
    fi
    
    echo -e "${YELLOW}Log Analysis Results:${NC}"
    echo ""
    
    for service in "${services[@]}"; do
        echo -e "${BLUE}📋 Analyzing $service logs...${NC}"
        
        if ! $compose_cmd ps -q "$service" > /dev/null 2>&1; then
            echo "  ❌ Service not running"
            continue
        fi
        
        # Get recent logs
        local logs
        logs=$($compose_cmd logs --tail=100 "$service" 2>/dev/null)
        
        # Check for common error patterns
        local issues_found=0
        local suggestions=()
        
        # Check for database connection issues
        if echo "$logs" | grep -qi "connection.*refused\|connection.*failed\|connection.*timeout"; then
            echo "  🔍 Database connection issues detected"
            suggestions+=("Check if postgres container is running")
            issues_found=$((issues_found + 1))
        fi
        
        # Check for authentication errors
        if echo "$logs" | grep -qi "auth.*failed\|authentication.*required\|auth.*error\|noauth"; then
            echo "  🔐 Authentication issues detected"
            suggestions+=("Verify credentials in .env file")
            issues_found=$((issues_found + 1))
        fi
        
        # Check for permission errors
        if echo "$logs" | grep -qi "permission.*denied\|access.*denied\|not.*permitted"; then
            echo "  🚫 Permission issues detected"
            suggestions+=("Check file permissions or user configuration")
            issues_found=$((issues_found + 1))
        fi
        
        # Check for compilation/syntax errors
        if echo "$logs" | grep -qi "syntax.*error\|compilation.*failed\|parse.*error\|unexpected.*token"; then
            echo "  💥 Syntax/Compilation errors detected"
            suggestions+=("Check source code for syntax errors")
            issues_found=$((issues_found + 1))
        fi
        
        # Check for memory issues
        if echo "$logs" | grep -qi "out.*of.*memory\|memory.*allocation\|killed"; then
            echo "  🧠 Memory issues detected"
            suggestions+=("Increase Docker memory allocation")
            issues_found=$((issues_found + 1))
        fi
        
        # Check for network issues
        if echo "$logs" | grep -qi "network.*unreachable\|connection.*reset\|timeout.*expired"; then
            echo "  🌐 Network connectivity issues detected"
            suggestions+=("Check Docker network configuration")
            issues_found=$((issues_found + 1))
        fi
        
        # Check for port binding issues
        if echo "$logs" | grep -qi "port.*already.*in.*use\|address.*already.*in.*use\|bind.*failed"; then
            echo "  🔌 Port binding issues detected"
            suggestions+=("Change conflicting ports in .env file")
            issues_found=$((issues_found + 1))
        fi
        
        # Check for health check failures
        if echo "$logs" | grep -qi "health.*check.*failed\|healthcheck.*timeout"; then
            echo "  ⚕️ Health check failures detected"
            suggestions+=("Service may be slow to start - wait or check service logs")
            issues_found=$((issues_found + 1))
        fi
        
        # Service-specific checks
        case "$service" in
            "server")
                if echo "$logs" | grep -qi "missing.*quotes\|unmatched.*quotes"; then
                    echo "  📝 Dart syntax issues detected (missing quotes)"
                    suggestions+=("Check server.dart for missing quotes in route definitions")
                    issues_found=$((issues_found + 1))
                fi
                if echo "$logs" | grep -qi "handler.*not.*found\|route.*not.*found"; then
                    echo "  🛣️ Route handler issues detected"
                    suggestions+=("Verify route handlers are properly defined")
                    issues_found=$((issues_found + 1))
                fi
                ;;
            "client")
                if echo "$logs" | grep -qi "failed.*to.*start\|build.*failed\|web.*server.*failed"; then
                    echo "  🌐 Flutter web server issues detected"
                    suggestions+=("Check Flutter web build configuration")
                    issues_found=$((issues_found + 1))
                fi
                if echo "$logs" | grep -qi "wget.*failed\|spider.*failed"; then
                    echo "  🕷️ Health check failing (Flutter may still be starting)"
                    suggestions+=("Flutter web needs 60-90s to start - increase timeout if needed")
                    issues_found=$((issues_found + 1))
                fi
                if echo "$logs" | grep -qi "connection.*refused.*3000"; then
                    echo "  🌐 Flutter web server not accessible"
                    suggestions+=("Check if Flutter dev server is properly started")
                    issues_found=$((issues_found + 1))
                fi
                ;;
            "redis-commander")
                if echo "$logs" | grep -qi "noauth\|authentication.*required"; then
                    echo "  ⚠️ Redis Commander auth issue detected"
                    suggestions+=("This is expected with password-protected Redis - use workaround")
                    issues_found=$((issues_found + 1))
                fi
                ;;
            "pgadmin")
                if echo "$logs" | grep -qi "email.*invalid\|email.*format"; then
                    echo "  📧 pgAdmin email validation issues detected"
                    suggestions+=("Use proper email <NAME_EMAIL>")
                    issues_found=$((issues_found + 1))
                fi
                ;;
            "postgres")
                if echo "$logs" | grep -qi "database.*does.*not.*exist\|role.*does.*not.*exist"; then
                    echo "  🗄️ Database initialization issues detected"
                    suggestions+=("Check init scripts or recreate database")
                    issues_found=$((issues_found + 1))
                fi
                ;;
            "redis")
                if echo "$logs" | grep -qi "config.*error\|invalid.*configuration"; then
                    echo "  ⚙️ Redis configuration issues detected"
                    suggestions+=("Check redis.conf file")
                    issues_found=$((issues_found + 1))
                fi
                ;;
        esac
        
        if [ $issues_found -eq 0 ]; then
            echo "  ✅ No critical issues detected"
        else
            echo "  📊 Found $issues_found potential issue(s)"
            if [ ${#suggestions[@]} -gt 0 ]; then
                echo "  💡 Suggestions:"
                for suggestion in "${suggestions[@]}"; do
                    echo "    - $suggestion"
                done
            fi
        fi
        echo ""
    done
    
    echo -e "${YELLOW}💡 General Quick Fixes:${NC}"
    echo "  • For auth issues: Verify .env file credentials are correct"
    echo "  • For connection issues: Check service dependencies and startup order"
    echo "  • For port conflicts: Change conflicting ports in .env file"
    echo "  • For Redis Commander: Use 'docker exec -it quester-$ENVIRONMENT-redis-1 redis-cli -a \$REDIS_PASSWORD'"
    echo "  • For Flutter slow start: Health checks may fail initially - wait 60-90 seconds"
    echo "  • For detailed logs: bash docker.sh logs --service <service-name>"
    echo "  • For service restart: bash docker.sh restart --service <service-name>"
    echo "  • For complete reset: bash docker.sh clean && bash docker.sh start"
    echo ""
}

# Troubleshoot function
troubleshoot_services() {
    echo -e "${BLUE}Running diagnostics for $ENVIRONMENT environment...${NC}"
    echo ""
    
    # Check Docker daemon
    echo -e "${YELLOW}Docker Status:${NC}"
    if docker info > /dev/null 2>&1; then
        echo "  ✓ Docker daemon is running"
        echo "  Docker version: $(docker --version)"
    else
        echo "  ✗ Docker daemon is not running"
        return 1
    fi
    echo ""
    
    # Check compose files (we're already in app dir from main execution)
    echo -e "${YELLOW}Configuration Files:${NC}"
    local files=("docker-compose.base.yml" "docker-compose.$ENVIRONMENT.yml" ".env")
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            echo "  ✓ $file exists"
        else
            echo "  ✗ $file missing"
        fi
    done
    echo ""
    
    # Check environment variables
    echo -e "${YELLOW}Environment Variables:${NC}"
    if [ -f ".env" ]; then
        echo "  Environment: $(grep ENVIRONMENT .env | cut -d'=' -f2)"
        echo "  Project: $(grep COMPOSE_PROJECT_NAME .env | cut -d'=' -f2)"
        echo "  Postgres User: $(grep POSTGRES_USER .env | cut -d'=' -f2)"
        echo "  Redis configured: $(grep -q REDIS_PASSWORD .env && echo "Yes" || echo "No")"
    else
        echo "  ✗ .env file not found"
    fi
    echo ""
    
    # Check network connectivity
    echo -e "${YELLOW}Network Status:${NC}"
    local compose_cmd=$(get_compose_command)
    if $compose_cmd ps > /dev/null 2>&1; then
        local network_name="${COMPOSE_PROJECT_NAME:-quester-dev}-network"
        if docker network ls | grep -q "$network_name"; then
            echo "  ✓ Docker network '$network_name' exists"
        else
            echo "  ✗ Docker network '$network_name' not found"
        fi
    fi
    echo ""
    
    # Common issues and solutions
    echo -e "${YELLOW}Common Issues & Solutions:${NC}"
    echo "  • If containers won't start: bash docker.sh clean && bash docker.sh start"
    echo "  • If health checks fail: bash docker.sh logs --service <service-name>"
    echo "  • If ports are busy: Change ports in .env file"
    echo "  • If build fails: bash docker.sh build --no-cache"
    echo "  • Redis Commander auth issues: Known limitation - use redis-cli directly"
    echo "    - Access Redis: docker exec -it quester-dev-redis-1 redis-cli -a \$REDIS_PASSWORD"
    echo "    - Test connection: docker exec -it quester-dev-redis-1 redis-cli -a \$REDIS_PASSWORD ping"
    echo "  • For urgent Redis debugging: Use pgAdmin for DB or direct redis-cli access"
    echo ""
    
    # Platform-specific troubleshooting
    echo -e "${YELLOW}Platform-Specific Issues (Windows):${NC}"
    echo "  • 'exec format error': Fixed by using linux/amd64 platform and compatible images"
    echo "  • 'YAML syntax error': Fixed by correcting variable escaping in Docker Compose files"
    echo "  • Redis Commander: Now working with Node.js base image + npm installation"
    echo "  • MinIO: Now working with Bitnami image for better Windows support"
    echo "  • 'unknown user postgres': Fixed by using non-Alpine PostgreSQL image (already configured)"
    echo "  • All services status: All services should work correctly on Windows Docker Desktop"
    echo "  • If Docker Desktop issues: Ensure WSL2 backend is enabled"
    echo ""
}

# Change to app directory
cd_app_dir() {
    if ! cd "$APP_DIR"; then
        echo -e "${RED}Error: Failed to change to $APP_DIR directory${NC}"
        exit 1
    fi
}

# Enhanced Docker Compose execution with proper file handling
execute_docker_command() {
    local compose_cmd=$(get_compose_command)
    
    log_verbose "Using Docker Compose command: $compose_cmd"
    
    case $ACTION in
        init)
            init_environment
            ;;
        build)
            echo -e "${BLUE}Building containers for $ENVIRONMENT environment...${NC}"
            
            # Build shared package dependencies first if it exists
            if [ -d "../shared" ] && [ -f "../shared/pubspec.yaml" ]; then
                echo -e "${BLUE}Building shared package dependencies...${NC}"
                cd ../shared
                dart pub get
                if dart pub deps | grep -q "build_runner"; then
                    dart pub run build_runner build --delete-conflicting-outputs
                    log_verbose "Generated shared package code"
                fi
                cd ../app
                log_verbose "Shared package dependencies built"
            fi
            
            if [ "$NO_CACHE" = true ]; then
                log_verbose "Building with --no-cache flag"
                $compose_cmd build --no-cache
            else
                $compose_cmd build
            fi
            if [ -n "$TARGET_SERVICE" ]; then
                log_verbose "Building specific service: $TARGET_SERVICE"
                $compose_cmd build "$TARGET_SERVICE"
            fi
            ;;
        start)
            echo -e "${BLUE}Starting containers for $ENVIRONMENT environment...${NC}"
            if [ -n "$TARGET_SERVICE" ]; then
                log_verbose "Starting specific service: $TARGET_SERVICE"
                $compose_cmd up -d "$TARGET_SERVICE"
            else
                $compose_cmd up -d
            fi
            
            # Enhanced startup monitoring with proper wait times
            echo -e "${YELLOW}Waiting for services to start up...${NC}"
            sleep 5
            
            echo -e "${BLUE}Checking initial service status...${NC}"
            local services_to_check=("postgres" "redis" "server")
            if [[ "$ENVIRONMENT" == "dev" ]]; then
                services_to_check+=("client")
            fi
            
            for service in "${services_to_check[@]}"; do
                echo -n "  Checking $service startup... "
                local max_attempts=30
                local attempt=1
                
                while [ $attempt -le $max_attempts ]; do
                    if $compose_cmd ps -q "$service" > /dev/null 2>&1; then
                        local status=$($compose_cmd ps --format "table {{.Service}}\t{{.Status}}" | grep "$service" | awk '{$1=""; print $0}' | xargs)
                        if [[ "$status" == *"Up"* ]]; then
                            echo -e "${GREEN}Started${NC}"
                            break
                        elif [[ "$status" == *"starting"* || "$status" == *"health: starting"* ]]; then
                            if [ $attempt -eq $max_attempts ]; then
                                echo -e "${YELLOW}Still starting (may need more time)${NC}"
                                break
                            fi
                        elif [[ "$status" == *"unhealthy"* ]]; then
                            if [ "$service" == "client" ]; then
                                echo -e "${YELLOW}Unhealthy (Flutter web may need 60-90s to fully start)${NC}"
                                break
                            else
                                echo -e "${RED}Unhealthy${NC}"
                                break
                            fi
                        elif [[ "$status" == *"Exited"* ]]; then
                            echo -e "${RED}Failed to start${NC}"
                            echo -e "${YELLOW}  Check logs: bash docker.sh logs --service $service${NC}"
                            break
                        fi
                    fi
                    
                    sleep 2
                    attempt=$((attempt + 1))
                done
            done
            
            echo ""
            echo -e "${BLUE}Initial startup check completed.${NC}"
            echo -e "${YELLOW}Note: Some services (especially Flutter client) may take additional time to become fully healthy.${NC}"
            echo -e "${CYAN}Use 'bash docker.sh health' to check detailed service health status.${NC}"
            ;;
        stop)
            echo -e "${BLUE}Stopping containers...${NC}"
            if [ -n "$TARGET_SERVICE" ]; then
                log_verbose "Stopping specific service: $TARGET_SERVICE"
                $compose_cmd stop "$TARGET_SERVICE"
            else
                $compose_cmd down
            fi
            ;;
        restart)
            echo -e "${BLUE}Restarting containers for $ENVIRONMENT environment...${NC}"
            if [ -n "$TARGET_SERVICE" ]; then
                log_verbose "Restarting specific service: $TARGET_SERVICE"
                $compose_cmd restart "$TARGET_SERVICE"
            else
                $compose_cmd restart
            fi
            ;;
        logs)
            echo -e "${BLUE}Showing logs for $ENVIRONMENT environment...${NC}"
            if [ "$FOLLOW_LOGS" = true ]; then
                if [ -n "$TARGET_SERVICE" ]; then
                    $compose_cmd logs -f "$TARGET_SERVICE"
                else
                    $compose_cmd logs -f
                fi
            else
                if [ -n "$TARGET_SERVICE" ]; then
                    $compose_cmd logs "$TARGET_SERVICE"
                else
                    $compose_cmd logs
                fi
            fi
            ;;
        health)
            check_health
            ;;
        status)
            echo -e "${BLUE}Container status for $ENVIRONMENT environment:${NC}"
            $compose_cmd ps -a
            echo ""
            echo -e "${BLUE}Resource usage:${NC}"
            docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
            ;;
        backup)
            backup_database
            ;;
        restore)
            restore_database
            ;;
        update)
            update_services
            ;;
        troubleshoot)
            troubleshoot_services
            ;;
        analyze)
            analyze_logs
            ;;
        clean)
            confirm_action "This will stop and remove all containers, networks, and volumes for $ENVIRONMENT."
            echo -e "${BLUE}Cleaning up containers, networks, and volumes...${NC}"
            $compose_cmd down -v --remove-orphans
            log_verbose "Removing unused Docker resources"
            docker system prune -f
            echo -e "${GREEN}Cleanup completed!${NC}"
            ;;
        reset)
            confirm_action "This will completely reset the environment, including removing images."
            echo -e "${BLUE}Performing complete reset...${NC}"
            $compose_cmd down -v --remove-orphans --rmi all
            docker system prune -af
            echo -e "${GREEN}Complete reset finished!${NC}"
            ;;
        *)
            echo -e "${RED}Unknown action: $ACTION${NC}"
            show_help
            exit 1
            ;;
    esac
}


# Display enhanced service information after successful start
show_service_urls() {
    local exit_code=$?
    if [ "$ACTION" = "start" ] && [ $exit_code -eq 0 ]; then
        echo ""
        echo -e "${GREEN}🎉 Quester services are up and running!${NC}"
        echo ""
        echo -e "${BLUE}📋 Service URLs ($ENVIRONMENT environment):${NC}"
        
        # Source .env file to get actual port values
        if [ -f ".env" ]; then
            source .env
        fi
        
        echo -e "  🌐 Client (Flutter):         http://localhost:${CLIENT_EXTERNAL_PORT:-3000}"
        echo -e "  🚀 Server API:               http://localhost:${SERVER_EXTERNAL_PORT:-8080}"
        echo -e "  � Health Check:             http://localhost:${SERVER_EXTERNAL_PORT:-8080}/health"
        
        echo -e "  🎮 Gamification API:         http://localhost:${SERVER_EXTERNAL_PORT:-8080}/gamification/health"
        echo -e "  🏢 Enterprise API:           http://localhost:${SERVER_EXTERNAL_PORT:-8080}/enterprise/health"
        
        if [[ "$ENVIRONMENT" != "dev" ]]; then
            echo -e "  🌍 Nginx (Load Balancer):    http://localhost:${NGINX_EXTERNAL_PORT:-80}"
        fi
        
        if [[ "$ENVIRONMENT" == "dev" ]]; then
            echo -e "  🗄️  pgAdmin:                  http://localhost:${PGADMIN_EXTERNAL_PORT:-5050}"
            echo -e "  📊 Redis Commander:          http://localhost:${REDIS_COMMANDER_PORT:-8081}"
            echo -e "  📧 MailHog:                   http://localhost:${MAILHOG_WEB_PORT:-8025}"
            echo -e "  � MinIO Console:             http://localhost:${MINIO_CONSOLE_PORT:-9001}"
        fi
        
        echo ""
        echo -e "${YELLOW}💡 Useful Commands:${NC}"
        echo "  • bash docker.sh health                    - Check service health"
        echo "  • bash docker.sh logs --follow             - Follow all logs"
        echo "  • bash docker.sh logs --service server     - View server logs only"
        echo "  • bash docker.sh backup                    - Create database backup"
        echo "  • bash docker.sh status                    - Show detailed status"
        echo "  • bash docker.sh stop                      - Stop all services"
        echo ""
        
        if [[ "$ENVIRONMENT" == "dev" ]]; then
            echo -e "${BLUE}🔧 Development Mode Tips:${NC}"
            echo "  • Hot reload is enabled for both client and server"
            echo "  • Debug port ${SERVER_DEBUG_PORT:-9229} is exposed for server debugging"
            echo "  • Use 'bash docker.sh logs --service server' for server-only logs"
            echo "  • Database changes are persisted in volumes"
            echo "  • Access development tools:"
            echo "    - pgAdmin for database management"
            echo "    - Redis Commander for Redis monitoring"
            echo "    - MailHog for email testing"
            echo "    - MinIO for file storage testing"
            echo ""
        fi
        
        if [[ "$ENVIRONMENT" == "staging" ]]; then
            echo -e "${BLUE}🔧 Staging Environment:${NC}"
            echo "  • Performance monitoring enabled"
            echo "  • Scaled services for load testing"
            echo "  • Use for pre-production validation"
            echo ""
        fi
        
        if [[ "$ENVIRONMENT" == "prod" ]]; then
            echo -e "${BLUE}🔧 Production Environment:${NC}"
            echo "  • Multiple replicas for high availability"
            echo "  • Resource limits enforced"
            echo "  • SSL/TLS termination at nginx"
            echo "  • Monitor logs and metrics regularly"
            echo ""
        fi
    fi
}

# Main execution
check_docker
check_directory
cd_app_dir
check_env_file
check_docker_compose
execute_docker_command
show_service_urls

echo -e "${GREEN}Operation completed successfully!${NC}"
