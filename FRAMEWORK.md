# Quester Framework Architecture
**Modern Full-Stack Framework for Gamified Applications**  
**Version 2.0** | **Agent OS Compatible** | **Production Ready**

## 🎯 Framework Overview

The Quester Framework is a comprehensive, type-safe, full-stack architecture designed specifically for building gamified applications with real-time features. It provides a synchronized development experience across Flutter client, Dart server, and shared package ecosystems.

## 🏗️ Architecture Principles

### 1. **Contract-First Development**
- **Shared Models**: Single source of truth for data structures
- **Type Safety**: End-to-end type checking across all packages
- **API Contracts**: Guaranteed client-server synchronization

### 2. **Framework Synchronization**
- **Version Locking**: Automatic package version validation
- **Configuration Management**: Unified settings across environments
- **State Consistency**: Real-time synchronization mechanisms

### 3. **Developer Experience**
- **Hot Reload**: Instant development feedback
- **Automatic Code Generation**: JSON serialization and routing
- **Comprehensive Logging**: Debug-friendly development tools

---

## 📦 Package Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                      QUESTER FRAMEWORK                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌──────────────────┐   ┌──────────────────┐   ┌──────────────┐ │
│  │     CLIENT       │◄──┤     SHARED       │──►│    SERVER    │ │
│  │   (Flutter)      │   │   (Core Types)   │   │   (Shelf)    │ │
│  │                  │   │                  │   │              │ │
│  │ • BLoC Pattern   │   │ • Models & DTOs  │   │ • REST APIs  │ │
│  │ • Responsive UI  │   │ • Utilities      │   │ • WebSockets │ │
│  │ • WebSocket      │   │ • Constants      │   │ • Middleware │ │
│  │ • State Mgmt     │   │ • Contracts      │   │ • Services   │ │
│  └──────────────────┘   └──────────────────┘   └──────────────┘ │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🚀 Quick Start Guide

### Prerequisites
- **Dart SDK**: ^3.8.1
- **Flutter SDK**: Latest stable
- **Docker**: For development environment
- **Git**: For version control

### 1. Environment Setup
```bash
# Clone repository
git clone <repository-url>
cd Quester

# Initialize framework
bash auto-setup.sh

# Start development environment
bash docker.sh dev start
```

### 2. Framework Initialization

#### Client (Flutter)
```dart
// lib/main.dart
import 'package:shared/shared.dart';
import 'src/core/framework/app_framework.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize framework
  await AppFramework.initialize(
    environment: Environment.development
  );
  
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  Widget build(BuildContext context) {
    return AppFramework.createApp(
      app: MaterialApp(
        title: 'Quester',
        home: HomePage(),
      ),
    );
  }
}
```

#### Server (Dart)
```dart
// bin/server.dart
import 'package:shared/shared.dart';
import '../lib/src/framework/server_framework.dart';

void main() async {
  // Initialize server framework
  await ServerFramework.initialize(
    environment: Environment.development
  );
  
  // Start server
  await ServerFramework.start(port: 8080);
}
```

---

## 🔧 Core Components

### 1. Shared Package Foundation

#### Models with Gamification
```dart
@JsonSerializable()
class User extends Equatable {
  final String id;
  final String username;
  final int totalPoints;
  final int level;
  final int experience;
  final int streak;
  final List<String> achievements;

  // Gamification computed properties
  int get experienceForNextLevel => level * 1000;
  double get experienceProgress => experience / experienceForNextLevel;
  
  // JSON serialization
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
```

#### Framework Utilities
```dart
// Standardized API responses
final response = FrameworkUtils.createApiResponse(
  success: true,
  message: 'Operation successful',
  data: {'result': 'data'},
  requestId: 'REQ_123',
);

// Performance monitoring
final result = await FrameworkUtils.measurePerformance(
  'database-query',
  () => database.query('SELECT * FROM users'),
);
```

#### Configuration Management
```dart
// Access framework configuration
final config = FrameworkConfig.instance;

// Check environment
if (config.enableDetailedLogging) {
  print('Debug mode enabled');
}

// Get package-specific settings
final flutterConfig = config.flutter;
final serverConfig = config.server;
```

### 2. Flutter Client Architecture

#### BLoC State Management
```dart
class QuestBloc extends Bloc<QuestEvent, QuestState> {
  QuestBloc() : super(QuestInitial()) {
    on<CompleteQuest>(_onCompleteQuest);
    on<LoadQuests>(_onLoadQuests);
  }

  void _onCompleteQuest(CompleteQuest event, Emitter emit) async {
    try {
      // Update quest status
      final quest = await questRepository.completeQuest(event.questId);
      
      // Award gamification points
      await gamificationService.awardPoints(
        userId: event.userId,
        points: quest.totalPoints,
      );
      
      emit(QuestCompleted(quest));
    } catch (error) {
      emit(QuestError(error.toString()));
    }
  }
}
```

#### Responsive UI Components
```dart
class StatCard extends StatelessWidget {
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: _buildMobileCard(context),
      tablet: _buildTabletCard(context),
      desktop: _buildDesktopCard(context),
    );
  }
}
```

### 3. Dart Server Framework

#### Modern Handler Pattern
```dart
class QuestHandler {
  late final Router _router;
  
  QuestHandler() {
    _router = Router()
      ..get('/', _listQuests)
      ..post('/', _createQuest)
      ..patch('/<questId>', _updateQuest)
      ..delete('/<questId>', _deleteQuest);
  }

  Future<Response> _createQuest(Request request) async {
    final body = await request.readAsString();
    final data = FrameworkUtils.safeJsonDecode(body);
    
    // Validate input using shared models
    final questDto = QuestCreateDto.fromJson(data);
    
    // Business logic
    final quest = await questService.createQuest(questDto);
    
    // Standardized response
    final response = FrameworkUtils.createApiResponse(
      success: true,
      message: 'Quest created successfully',
      data: quest.toJson(),
      requestId: request.headers['x-request-id'],
    );
    
    return Response.ok(jsonEncode(response));
  }
}
```

#### Middleware Pipeline
```dart
// Automatic middleware setup
final handler = Pipeline()
    .addMiddleware(createCorsMiddleware())
    .addMiddleware(createLoggingMiddleware())
    .addMiddleware(createAuthMiddleware())
    .addMiddleware(createErrorHandlingMiddleware())
    .addHandler(router.call);
```

---

## 🎮 Gamification Features

### Point System
```dart
class GamificationService {
  Future<void> awardPoints(String userId, int points) async {
    // Calculate with multipliers
    final finalPoints = points * _getPriorityMultiplier();
    
    // Update user progress
    await userRepository.addPoints(userId, finalPoints);
    
    // Check for level up
    if (await _checkLevelUp(userId)) {
      await _triggerLevelUpReward(userId);
    }
    
    // Evaluate achievements
    await _evaluateAchievements(userId);
    
    // Send real-time update
    await webSocketService.broadcastUserUpdate(userId);
  }
}
```

### Achievement System
```dart
@JsonSerializable()
class Achievement {
  final String id;
  final String name;
  final AchievementType type;
  final int pointsRequired;
  final AchievementRarity rarity;
  
  // Achievement evaluation logic
  bool isUnlockedBy(UserProgress progress) {
    switch (type) {
      case AchievementType.taskCompletion:
        return progress.completedTasks >= pointsRequired;
      case AchievementType.streakMaintenance:
        return progress.currentStreak >= pointsRequired;
      // ... more conditions
    }
  }
}
```

### Real-time Notifications
```dart
// WebSocket event creation
final achievementEvent = WebSocketEventFactory.createAchievementUnlockedEvent(
  userId: user.id,
  achievementId: achievement.id,
  achievementName: achievement.name,
  pointsEarned: achievement.pointsReward,
);

await webSocketService.sendEvent(achievementEvent);
```

---

## 🔒 Security & Performance

### Security Features
- **JWT Authentication**: Secure token-based auth with refresh tokens
- **Input Validation**: Comprehensive data validation using shared models
- **CORS Configuration**: Proper cross-origin resource sharing
- **Rate Limiting**: API endpoint protection
- **Request Tracing**: Distributed request tracking

### Performance Optimizations
- **Response Caching**: Redis-based caching layer
- **Database Optimization**: Connection pooling and query optimization
- **WebSocket Efficiency**: Event batching and connection management
- **Flutter Performance**: Image caching and widget optimization

---

## 📊 Development Workflow

### 1. Feature Development
```bash
# 1. Shared package (Models & DTOs)
cd shared/
# Add models, run code generation
dart run build_runner build

# 2. Server implementation
cd ../server/
# Implement handlers and services
dart run bin/server.dart

# 3. Client implementation  
cd ../client/
# Build UI with BLoC
flutter run -d web-server
```

### 2. Testing Strategy
```bash
# Unit tests
cd shared/ && dart test
cd server/ && dart test  
cd client/ && flutter test

# Integration tests
bash docker.sh test integration

# E2E tests
bash docker.sh test e2e
```

### 3. Deployment Pipeline
```bash
# Build for production
bash docker.sh build prod

# Deploy to staging
bash docker.sh deploy staging

# Deploy to production
bash docker.sh deploy prod
```

---

## 🛠️ Configuration Management

### Environment Configuration
```dart
// shared/lib/src/config/framework_config.dart
class FrameworkConfig {
  // Environment-specific settings
  bool get enableDetailedLogging => 
      EnvConfig.environment != Environment.production;
  
  // Package-specific configuration
  FlutterClientConfig get flutter => FlutterClientConfig();
  DartServerConfig get server => DartServerConfig();
  SharedPackageConfig get shared => SharedPackageConfig();
}
```

### Development Environment
```yaml
# docker-compose.dev.yml
services:
  client:
    command: ["flutter", "run", "-d", "web-server"]
    ports: ["3000:3000"]
    
  server:
    command: ["dart", "run", "bin/server.dart"]
    ports: ["8080:8080"]
    
  postgres:
    ports: ["5432:5432"]
    
  redis:
    ports: ["6379:6379"]
```

---

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/v1/auth/login
POST /api/v1/auth/register
POST /api/v1/auth/logout
POST /api/v1/auth/refresh
GET  /api/v1/auth/verify
```

### Core Endpoints
```
GET    /api/v1/users/profile
PATCH  /api/v1/users/profile

GET    /api/v1/quests
POST   /api/v1/quests
PATCH  /api/v1/quests/{id}
DELETE /api/v1/quests/{id}

GET    /api/v1/tasks
POST   /api/v1/tasks
PATCH  /api/v1/tasks/{id}
DELETE /api/v1/tasks/{id}

GET    /api/v1/achievements
GET    /api/v1/leaderboard/global
```

### WebSocket Events
```dart
enum WebSocketEventType {
  taskUpdated,           // Task status change
  questCompleted,        // Quest completion
  achievementUnlocked,   // New achievement
  userStatusChanged,     // Online/offline status
  notificationReceived,  // Real-time notifications
  presenceUpdate,        // User activity status
}
```

---

## 🔄 Real-time Features

### WebSocket Integration
```dart
// Client-side WebSocket handling
class WebSocketBloc extends Bloc<WebSocketEvent, WebSocketState> {
  late final WebSocketService _webSocketService;
  
  WebSocketBloc() : super(WebSocketDisconnected()) {
    _webSocketService = WebSocketService();
    
    // Listen to WebSocket events
    _webSocketService.eventStream.listen((event) {
      switch (event.type) {
        case WebSocketEventType.achievementUnlocked:
          add(AchievementReceived(event));
          break;
        case WebSocketEventType.taskUpdated:
          add(TaskUpdateReceived(event));
          break;
      }
    });
  }
}
```

### Event-Driven Architecture
```dart
// Server-side event broadcasting
class TaskService {
  Future<void> completeTask(String taskId) async {
    final task = await taskRepository.completeTask(taskId);
    
    // Business logic
    await gamificationService.awardPoints(task.userId, task.points);
    
    // Broadcast real-time update
    final event = WebSocketEventFactory.createTaskUpdateEvent(
      taskId: taskId,
      userId: task.userId,
      updates: {'status': 'completed'},
    );
    
    await webSocketService.broadcastToUser(task.userId, event);
  }
}
```

---

## 📋 Troubleshooting

### Common Issues

#### 1. Package Synchronization Errors
```bash
# Fix version mismatches
bash auto-setup.sh --fix-versions

# Regenerate shared code
cd shared/ && dart run build_runner build --delete-conflicting-outputs
```

#### 2. Development Server Issues
```bash
# Restart development environment
bash docker.sh dev restart

# Check service health
bash docker.sh health

# View detailed logs
bash docker.sh logs --follow server
```

#### 3. WebSocket Connection Problems
```dart
// Enable WebSocket debugging
FrameworkConfig.initialize(environment: Environment.development);

// Check connection status
final status = webSocketService.connectionState;
print('WebSocket status: $status');
```

### Performance Monitoring
```dart
// Enable performance tracking
final result = await FrameworkUtils.measurePerformance(
  'api-call',
  () => apiService.getData(),
);

print('Operation took: ${result.duration.inMilliseconds}ms');
```

---

## 🚀 Production Deployment

### Docker Production Build
```dockerfile
# Multi-stage build for server
FROM dart:stable AS build
WORKDIR /app
COPY . .
RUN dart pub get
RUN dart compile exe bin/server.dart -o bin/server

FROM alpine:latest
RUN apk add --no-cache ca-certificates
COPY --from=build /app/bin/server /app/server
EXPOSE 8080
CMD ["/app/server"]
```

### Environment Configuration
```bash
# Production environment variables
ENVIRONMENT=production
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
JWT_SECRET=your-secret-key
CORS_ORIGIN=https://your-domain.com
```

### Health Monitoring
```bash
# Health check endpoints
curl http://localhost:8080/health
curl http://localhost:8080/status

# Framework status
curl http://localhost:8080/api/v1/framework/status
```

---

## 📈 Roadmap & Future Enhancements

### Phase 1: Core Stability (Current)
- [x] Framework synchronization
- [x] Basic gamification features  
- [x] Real-time WebSocket integration
- [x] Docker development environment

### Phase 2: Advanced Features (Next)
- [ ] Database integration (PostgreSQL)
- [ ] Advanced authentication (OAuth, 2FA)
- [ ] Comprehensive test coverage
- [ ] Performance monitoring dashboard

### Phase 3: Enterprise Features (Future)
- [ ] Multi-tenant architecture
- [ ] Analytics and insights
- [ ] Mobile app development
- [ ] Microservice migration

---

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Follow framework conventions
4. Add tests for new features
5. Submit pull request

### Code Style
- Use shared models for all data structures
- Follow BLoC pattern for Flutter state management
- Implement standardized error handling
- Add comprehensive documentation

### Testing Requirements
- Unit tests for shared models
- Integration tests for API endpoints
- Widget tests for Flutter components
- E2E tests for critical user flows

---

**Framework Status**: ✅ Production Ready | **Version**: 2.0  
**Last Updated**: August 25, 2025 | **Agent OS Compatible**: Yes

**Get Started**: `bash auto-setup.sh` | **Documentation**: `/docs/` | **Support**: [Issues](https://github.com/your-org/quester/issues)