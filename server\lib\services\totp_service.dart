/// TOTP (Time-based One-Time Password) Service
/// 
/// Comprehensive service for implementing TOTP-based Multi-Factor Authentication.
/// Supports RFC 6238 TOTP algorithm, secret generation, QR code creation,
/// and code validation with time window tolerance.
/// 
/// Key Features:
/// - RFC 6238 compliant TOTP implementation
/// - Secure secret generation and storage
/// - QR code generation for authenticator apps
/// - Code validation with time drift tolerance
/// - Backup window validation
/// - Rate limiting and replay attack prevention
/// - Multiple hash algorithms (SHA1, SHA256, SHA512)
/// - Configurable time steps and code lengths
library;

import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import '../services/database_service.dart';

/// TOTP algorithm configuration
class TOTPConfig {
  final int period; // Time step in seconds (default: 30)
  final int digits; // Number of digits in code (default: 6)
  final String algorithm; // Hash algorithm (SHA1, SHA256, SHA512)
  final int windowSize; // Number of time steps to check for validation
  final String issuer; // Issuer name for QR codes
  final int secretLength; // Length of generated secret in bytes

  const TOTPConfig({
    this.period = 30,
    this.digits = 6,
    this.algorithm = 'SHA1',
    this.windowSize = 1,
    this.issuer = 'Quester',
    this.secretLength = 32,
  });

  factory TOTPConfig.fromJson(Map<String, dynamic> json) {
    return TOTPConfig(
      period: json['period'] as int? ?? 30,
      digits: json['digits'] as int? ?? 6,
      algorithm: json['algorithm'] as String? ?? 'SHA1',
      windowSize: json['window_size'] as int? ?? 1,
      issuer: json['issuer'] as String? ?? 'Quester',
      secretLength: json['secret_length'] as int? ?? 32,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'period': period,
      'digits': digits,
      'algorithm': algorithm,
      'window_size': windowSize,
      'issuer': issuer,
      'secret_length': secretLength,
    };
  }
}

/// TOTP secret information
class TOTPSecret {
  final String secret; // Base32 encoded secret
  final String backupCodes; // Comma-separated backup codes
  final String qrCodeUrl; // QR code data URL
  final String manualEntryKey; // Secret for manual entry
  final DateTime createdAt;
  final TOTPConfig config;

  const TOTPSecret({
    required this.secret,
    required this.backupCodes,
    required this.qrCodeUrl,
    required this.manualEntryKey,
    required this.createdAt,
    required this.config,
  });

  Map<String, dynamic> toJson() {
    return {
      'secret': secret,
      'backup_codes': backupCodes,
      'qr_code_url': qrCodeUrl,
      'manual_entry_key': manualEntryKey,
      'created_at': createdAt.toIso8601String(),
      'config': config.toJson(),
    };
  }
}

/// TOTP validation result
class TOTPValidationResult {
  final bool isValid;
  final int? timeStep;
  final int? windowOffset;
  final bool isBackupCode;
  final String? error;
  final Map<String, dynamic> metadata;

  const TOTPValidationResult({
    required this.isValid,
    this.timeStep,
    this.windowOffset,
    this.isBackupCode = false,
    this.error,
    this.metadata = const {},
  });

  factory TOTPValidationResult.success({
    required int timeStep,
    int windowOffset = 0,
    bool isBackupCode = false,
    Map<String, dynamic> metadata = const {},
  }) {
    return TOTPValidationResult(
      isValid: true,
      timeStep: timeStep,
      windowOffset: windowOffset,
      isBackupCode: isBackupCode,
      metadata: metadata,
    );
  }

  factory TOTPValidationResult.failure({
    required String error,
    Map<String, dynamic> metadata = const {},
  }) {
    return TOTPValidationResult(
      isValid: false,
      error: error,
      metadata: metadata,
    );
  }
}

/// Main TOTP Service
class TOTPService {
  final DatabaseService _databaseService;
  final TOTPConfig _defaultConfig;
  
  // Rate limiting for validation attempts
  final Map<String, List<DateTime>> _validationAttempts = {};
  final int _maxAttemptsPerMinute = 10;
  
  // Used codes tracking to prevent replay attacks
  final Map<String, Set<String>> _usedCodes = {};
  final Duration _codeReuseWindow = const Duration(minutes: 2);

  TOTPService(this._databaseService, {TOTPConfig? config})
      : _defaultConfig = config ?? const TOTPConfig();

  /// Generate new TOTP secret for user
  Future<TOTPSecret> generateSecret({
    required String userId,
    required String userEmail,
    String? issuer,
    TOTPConfig? config,
  }) async {
    final effectiveConfig = config ?? _defaultConfig;
    final effectiveIssuer = issuer ?? effectiveConfig.issuer;

    try {
      // Generate cryptographically secure random secret
      final secretBytes = _generateSecureRandomBytes(effectiveConfig.secretLength);
      final secret = _base32Encode(secretBytes);

      // Generate backup codes
      final backupCodes = _generateBackupCodes();

      // Create QR code URL
      final qrCodeUrl = _createQRCodeUrl(
        secret: secret,
        userEmail: userEmail,
        issuer: effectiveIssuer,
        config: effectiveConfig,
      );

      // Format manual entry key (grouped for readability)
      final manualEntryKey = _formatManualEntryKey(secret);

      // Store TOTP configuration in database
      await _storeTOTPSecret(userId, secret, backupCodes, effectiveConfig);

      return TOTPSecret(
        secret: secret,
        backupCodes: backupCodes,
        qrCodeUrl: qrCodeUrl,
        manualEntryKey: manualEntryKey,
        createdAt: DateTime.now(),
        config: effectiveConfig,
      );

    } catch (e) {
      throw TOTPException('Failed to generate TOTP secret: $e', 'SECRET_GENERATION_FAILED');
    }
  }

  /// Validate TOTP code
  Future<TOTPValidationResult> validateCode({
    required String userId,
    required String code,
    TOTPConfig? config,
    bool allowBackupCodes = true,
  }) async {
    final effectiveConfig = config ?? _defaultConfig;

    try {
      // Rate limiting check
      if (!_checkRateLimit(userId)) {
        return TOTPValidationResult.failure(
          error: 'Too many validation attempts. Please try again later.',
          metadata: {'rate_limited': true},
        );
      }

      // Get user's TOTP configuration
      final userMFA = await _databaseService.getUserMFASettings(userId);
      if (userMFA == null) {
        return TOTPValidationResult.failure(
          error: 'TOTP not configured for user',
          metadata: {'totp_not_configured': true},
        );
      }

      final secret = userMFA['totp_secret'] as String?;
      if (secret == null || secret.isEmpty) {
        return TOTPValidationResult.failure(
          error: 'TOTP secret not found',
          metadata: {'secret_missing': true},
        );
      }

      // Check for backup code first if enabled
      if (allowBackupCodes && code.length >= 8) {
        final backupResult = await _validateBackupCode(userId, code, userMFA);
        if (backupResult.isValid) {
          return backupResult;
        }
      }

      // Validate TOTP code
      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final currentTimeStep = currentTime ~/ effectiveConfig.period;

      // Check code reuse
      if (_isCodeUsed(userId, code, currentTimeStep)) {
        return TOTPValidationResult.failure(
          error: 'Code has already been used',
          metadata: {'code_reused': true},
        );
      }

      // Validate code within time window
      for (int offset = -effectiveConfig.windowSize; offset <= effectiveConfig.windowSize; offset++) {
        final timeStep = currentTimeStep + offset;
        final expectedCode = _generateTOTPCode(
          secret: secret,
          timeStep: timeStep,
          config: effectiveConfig,
        );

        if (expectedCode == code) {
          // Mark code as used
          _markCodeAsUsed(userId, code, timeStep);

          // Update user's last MFA usage
          await _databaseService.updateUserMFASettings(userId, {
            'last_used_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          });

          return TOTPValidationResult.success(
            timeStep: timeStep,
            windowOffset: offset,
            metadata: {
              'algorithm': effectiveConfig.algorithm,
              'time_step': timeStep,
              'validated_at': DateTime.now().toIso8601String(),
            },
          );
        }
      }

      // Log failed validation attempt
      await _logMFAEvent(
        userId: userId,
        eventType: 'totp_validation_failed',
        details: {
          'code_length': code.length,
          'time_step': currentTimeStep,
          'window_size': effectiveConfig.windowSize,
        },
      );

      return TOTPValidationResult.failure(
        error: 'Invalid TOTP code',
        metadata: {
          'invalid_code': true,
          'time_step': currentTimeStep,
        },
      );

    } catch (e) {
      return TOTPValidationResult.failure(
        error: 'TOTP validation error: $e',
        metadata: {'exception': e.toString()},
      );
    }
  }

  /// Generate TOTP code for given time step
  String _generateTOTPCode({
    required String secret,
    required int timeStep,
    required TOTPConfig config,
  }) {
    // Decode base32 secret
    final secretBytes = _base32Decode(secret);

    // Convert time step to 8-byte big-endian
    final timeBytes = Uint8List(8);
    final byteData = ByteData.sublistView(timeBytes);
    byteData.setUint64(0, timeStep, Endian.big);

    // Generate HMAC
    final hmac = _getHMAC(config.algorithm);
    final concatenated = [...secretBytes, ...timeBytes];
    final digest = hmac.convert(concatenated);
    final hmacBytes = digest.bytes;

    // Dynamic truncation (RFC 4226)
    final offset = hmacBytes.last & 0x0f;
    final binaryCode = (hmacBytes[offset] & 0x7f) << 24 |
                      (hmacBytes[offset + 1] & 0xff) << 16 |
                      (hmacBytes[offset + 2] & 0xff) << 8 |
                      (hmacBytes[offset + 3] & 0xff);

    // Generate final code
    final code = binaryCode % pow(10, config.digits).toInt();
    return code.toString().padLeft(config.digits, '0');
  }

  /// Validate backup code
  Future<TOTPValidationResult> _validateBackupCode(
    String userId,
    String code,
    Map<String, dynamic> userMFA,
  ) async {
    final backupCodes = userMFA['backup_codes'] as List<dynamic>?;
    if (backupCodes == null || backupCodes.isEmpty) {
      return TOTPValidationResult.failure(
        error: 'No backup codes available',
      );
    }

    final codeString = code.replaceAll(' ', '').replaceAll('-', '');
    
    if (backupCodes.contains(codeString)) {
      // Remove used backup code
      final updatedCodes = List<String>.from(backupCodes.cast<String>())
        ..remove(codeString);

      await _databaseService.updateUserMFASettings(userId, {
        'backup_codes': updatedCodes,
        'backup_codes_used_count': (userMFA['backup_codes_used_count'] as int? ?? 0) + 1,
        'last_used_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      await _logMFAEvent(
        userId: userId,
        eventType: 'backup_code_used',
        details: {
          'remaining_codes': updatedCodes.length,
          'codes_used_total': (userMFA['backup_codes_used_count'] as int? ?? 0) + 1,
        },
      );

      return TOTPValidationResult.success(
        timeStep: 0,
        isBackupCode: true,
        metadata: {
          'backup_code_used': true,
          'remaining_codes': updatedCodes.length,
        },
      );
    }

    return TOTPValidationResult.failure(
      error: 'Invalid backup code',
      metadata: {'invalid_backup_code': true},
    );
  }

  /// Generate secure random bytes
  Uint8List _generateSecureRandomBytes(int length) {
    final random = Random.secure();
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = random.nextInt(256);
    }
    return bytes;
  }

  /// Generate backup codes
  String _generateBackupCodes({int count = 10}) {
    final codes = <String>[];
    final random = Random.secure();
    
    for (int i = 0; i < count; i++) {
      // Generate 8-digit backup codes
      final code = random.nextInt(100000000).toString().padLeft(8, '0');
      // Format as XXXX-XXXX for readability
      final formatted = '${code.substring(0, 4)}-${code.substring(4)}';
      codes.add(formatted);
    }
    
    return codes.join(',');
  }

  /// Create QR code URL for authenticator apps
  String _createQRCodeUrl({
    required String secret,
    required String userEmail,
    required String issuer,
    required TOTPConfig config,
  }) {
    // Create otpauth URL
    final label = Uri.encodeComponent('$issuer:$userEmail');
    final otpauthUrl = 'otpauth://totp/$label?'
        'secret=$secret&'
        'issuer=${Uri.encodeComponent(issuer)}&'
        'algorithm=${config.algorithm}&'
        'digits=${config.digits}&'
        'period=${config.period}';

    // For QR code generation, you would typically use a QR code library
    // For now, we'll return the otpauth URL which can be converted to QR code
    return otpauthUrl;
  }

  /// Format secret for manual entry (grouped for readability)
  String _formatManualEntryKey(String secret) {
    final chunks = <String>[];
    for (int i = 0; i < secret.length; i += 4) {
      final end = (i + 4 < secret.length) ? i + 4 : secret.length;
      chunks.add(secret.substring(i, end));
    }
    return chunks.join(' ');
  }

  /// Store TOTP secret securely in database
  Future<void> _storeTOTPSecret(
    String userId,
    String secret,
    String backupCodes,
    TOTPConfig config,
  ) async {
    // Check if user already has TOTP configured
    final existingMFA = await _databaseService.getUserMFASettings(userId);
    
    if (existingMFA != null) {
      // Update existing configuration
      await _databaseService.updateUserMFASettings(userId, {
        'totp_secret': secret,
        'backup_codes': backupCodes.split(','),
        'backup_codes_generated_at': DateTime.now().toIso8601String(),
        'backup_codes_used_count': 0,
        'totp_config': config.toJson(),
        'is_enabled': true,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } else {
      // Create new MFA settings
      await _databaseService.createUserMFASettings({
        'user_id': userId,
        'is_enabled': true,
        'preferred_method': 'totp',
        'totp_secret': secret,
        'backup_codes': backupCodes.split(','),
        'backup_codes_generated_at': DateTime.now().toIso8601String(),
        'backup_codes_used_count': 0,
        'totp_config': config.toJson(),
        'created_at': DateTime.now().toIso8601String(),
      });
    }

    await _logMFAEvent(
      userId: userId,
      eventType: 'totp_secret_generated',
      details: {
        'algorithm': config.algorithm,
        'digits': config.digits,
        'period': config.period,
        'backup_codes_count': backupCodes.split(',').length,
      },
    );
  }

  /// Check rate limiting for validation attempts
  bool _checkRateLimit(String userId) {
    final now = DateTime.now();
    final attempts = _validationAttempts[userId] ?? [];
    
    // Remove attempts older than 1 minute
    attempts.removeWhere((time) => now.difference(time).inMinutes >= 1);
    
    if (attempts.length >= _maxAttemptsPerMinute) {
      return false;
    }
    
    // Add current attempt
    attempts.add(now);
    _validationAttempts[userId] = attempts;
    
    return true;
  }

  /// Check if code was already used (replay attack prevention)
  bool _isCodeUsed(String userId, String code, int timeStep) {
    final userUsedCodes = _usedCodes[userId] ?? <String>{};
    final codeKey = '${timeStep}_$code';
    return userUsedCodes.contains(codeKey);
  }

  /// Mark code as used
  void _markCodeAsUsed(String userId, String code, int timeStep) {
    final userUsedCodes = _usedCodes[userId] ?? <String>{};
    final codeKey = '${timeStep}_$code';
    userUsedCodes.add(codeKey);
    _usedCodes[userId] = userUsedCodes;
    
    // Clean up old codes (older than reuse window)
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final currentTimeStep = now ~/ _defaultConfig.period;
    final cutoffTimeStep = currentTimeStep - (_codeReuseWindow.inSeconds ~/ _defaultConfig.period);
    
    userUsedCodes.removeWhere((key) {
      final keyTimeStep = int.tryParse(key.split('_')[0]) ?? 0;
      return keyTimeStep < cutoffTimeStep;
    });
  }

  /// Get HMAC function for algorithm
  Hmac _getHMAC(String algorithm) {
    switch (algorithm.toUpperCase()) {
      case 'SHA1':
        return Hmac(sha1, []);
      case 'SHA256':
        return Hmac(sha256, []);
      case 'SHA512':
        return Hmac(sha512, []);
      default:
        throw TOTPException('Unsupported HMAC algorithm: $algorithm', 'UNSUPPORTED_ALGORITHM');
    }
  }

  /// Base32 encoding (RFC 4648)
  String _base32Encode(Uint8List bytes) {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    final result = StringBuffer();
    
    for (int i = 0; i < bytes.length; i += 5) {
      final chunk = _getChunk(bytes, i, 5);
      final value = _bytesToInt(chunk);
      
      // Convert 40-bit value to 8 base32 characters
      for (int j = 7; j >= 0; j--) {
        final index = (value >> (j * 5)) & 0x1f;
        result.write(alphabet[index]);
      }
    }
    
    // Remove padding for our use case
    return result.toString().replaceAll(RegExp(r'=+$'), '');
  }

  /// Base32 decoding
  Uint8List _base32Decode(String encoded) {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    final bytes = <int>[];
    
    // Pad if necessary
    final padded = encoded.toUpperCase().padRight((encoded.length + 7) ~/ 8 * 8, '=');
    
    for (int i = 0; i < padded.length; i += 8) {
      int value = 0;
      for (int j = 0; j < 8; j++) {
        if (i + j < padded.length && padded[i + j] != '=') {
          final charIndex = alphabet.indexOf(padded[i + j]);
          if (charIndex == -1) {
            throw TOTPException('Invalid base32 character: ${padded[i + j]}', 'INVALID_BASE32');
          }
          value = (value << 5) | charIndex;
        }
      }
      
      // Extract 5 bytes from 40-bit value
      for (int k = 4; k >= 0; k--) {
        if (bytes.length < (i ~/ 8) * 5 + k + 1) {
          bytes.add((value >> (k * 8)) & 0xff);
        }
      }
    }
    
    return Uint8List.fromList(bytes);
  }

  /// Get chunk of bytes
  Uint8List _getChunk(Uint8List bytes, int start, int length) {
    final end = (start + length > bytes.length) ? bytes.length : start + length;
    final chunk = Uint8List(5); // Always 5 bytes for base32
    
    for (int i = 0; i < 5; i++) {
      if (start + i < end) {
        chunk[i] = bytes[start + i];
      } else {
        chunk[i] = 0;
      }
    }
    
    return chunk;
  }

  /// Convert bytes to integer
  int _bytesToInt(Uint8List bytes) {
    int value = 0;
    for (int i = 0; i < bytes.length; i++) {
      value = (value << 8) | bytes[i];
    }
    return value;
  }

  /// Disable TOTP for user
  Future<bool> disableTOTP(String userId) async {
    try {
      await _databaseService.updateUserMFASettings(userId, {
        'is_enabled': false,
        'totp_secret': null,
        'backup_codes': null,
        'disabled_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Clear used codes cache
      _usedCodes.remove(userId);
      _validationAttempts.remove(userId);

      await _logMFAEvent(
        userId: userId,
        eventType: 'totp_disabled',
        details: {'disabled_by': 'user'}, // In production, track who disabled it
      );

      return true;

    } catch (e) {
      await _logMFAEvent(
        userId: userId,
        eventType: 'totp_disable_failed',
        details: {'error': e.toString()},
      );
      return false;
    }
  }

  /// Regenerate backup codes
  Future<List<String>> regenerateBackupCodes(String userId) async {
    try {
      final newBackupCodes = _generateBackupCodes();
      final codesList = newBackupCodes.split(',');

      await _databaseService.updateUserMFASettings(userId, {
        'backup_codes': codesList,
        'backup_codes_generated_at': DateTime.now().toIso8601String(),
        'backup_codes_used_count': 0,
        'updated_at': DateTime.now().toIso8601String(),
      });

      await _logMFAEvent(
        userId: userId,
        eventType: 'backup_codes_regenerated',
        details: {
          'codes_count': codesList.length,
        },
      );

      return codesList;

    } catch (e) {
      throw TOTPException('Failed to regenerate backup codes: $e', 'BACKUP_CODES_FAILED');
    }
  }

  /// Get current TOTP code for testing (development only)
  String getCurrentCode({
    required String secret,
    TOTPConfig? config,
  }) {
    final effectiveConfig = config ?? _defaultConfig;
    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final timeStep = currentTime ~/ effectiveConfig.period;
    
    return _generateTOTPCode(
      secret: secret,
      timeStep: timeStep,
      config: effectiveConfig,
    );
  }

  /// Get time remaining for current TOTP code
  int getTimeRemaining({TOTPConfig? config}) {
    final effectiveConfig = config ?? _defaultConfig;
    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return effectiveConfig.period - (currentTime % effectiveConfig.period);
  }

  /// Verify TOTP setup (user scans QR and provides code)
  Future<bool> verifyTOTPSetup({
    required String userId,
    required String verificationCode,
    TOTPConfig? config,
  }) async {
    final validationResult = await validateCode(
      userId: userId,
      code: verificationCode,
      config: config,
      allowBackupCodes: false, // Only TOTP codes for setup verification
    );

    if (validationResult.isValid && !validationResult.isBackupCode) {
      // Mark TOTP as verified and enabled
      await _databaseService.updateUserMFASettings(userId, {
        'is_enabled': true,
        'totp_verified_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      await _logMFAEvent(
        userId: userId,
        eventType: 'totp_setup_completed',
        details: {
          'verification_successful': true,
        },
      );

      return true;
    }

    await _logMFAEvent(
      userId: userId,
      eventType: 'totp_setup_verification_failed',
      details: {
        'error': validationResult.error ?? 'Invalid verification code',
      },
    );

    return false;
  }

  /// Log MFA events for audit
  Future<void> _logMFAEvent({
    required String userId,
    required String eventType,
    required Map<String, dynamic> details,
  }) async {
    try {
      // Get user's organization
      final user = await _databaseService.getUser(userId);
      if (user == null) return;

      await _databaseService.createSecurityAuditLog({
        'organization_id': user['organization_id'],
        'user_id': userId,
        'event_type': 'mfa',
        'event_category': eventType,
        'event_description': 'MFA $eventType for user',
        'event_severity': _getMFAEventSeverity(eventType),
        'details': details,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Failed to log MFA event: $e');
    }
  }

  /// Get event severity for MFA events
  String _getMFAEventSeverity(String eventType) {
    switch (eventType) {
      case 'totp_validation_failed':
      case 'totp_setup_verification_failed':
      case 'totp_disable_failed':
        return 'warning';
      case 'backup_code_used':
      case 'totp_secret_generated':
      case 'totp_setup_completed':
        return 'info';
      default:
        return 'info';
    }
  }
}

/// TOTP-specific exception
class TOTPException implements Exception {
  final String message;
  final String code;

  TOTPException(this.message, this.code);

  @override
  String toString() => 'TOTPException($code): $message';
}