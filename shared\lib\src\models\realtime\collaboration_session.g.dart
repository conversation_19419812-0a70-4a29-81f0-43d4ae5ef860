// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collaboration_session.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SessionParticipant _$SessionParticipantFromJson(Map<String, dynamic> json) =>
    SessionParticipant(
      userId: json['userId'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      role: $enumDecode(_$ParticipantRoleEnumMap, json['role']),
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      lastActivity: json['lastActivity'] == null
          ? null
          : DateTime.parse(json['lastActivity'] as String),
      isActive: json['isActive'] as bool? ?? true,
      cursorPosition: json['cursorPosition'] as Map<String, dynamic>?,
      selections: json['selections'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SessionParticipantToJson(SessionParticipant instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'displayName': instance.displayName,
      'avatarUrl': instance.avatarUrl,
      'role': _$ParticipantRoleEnumMap[instance.role]!,
      'joinedAt': instance.joinedAt.toIso8601String(),
      'lastActivity': instance.lastActivity?.toIso8601String(),
      'isActive': instance.isActive,
      'cursorPosition': instance.cursorPosition,
      'selections': instance.selections,
    };

const _$ParticipantRoleEnumMap = {
  ParticipantRole.owner: 'owner',
  ParticipantRole.editor: 'editor',
  ParticipantRole.viewer: 'viewer',
  ParticipantRole.commentator: 'commentator',
};

CollaborationSession _$CollaborationSessionFromJson(
  Map<String, dynamic> json,
) => CollaborationSession(
  id: json['id'] as String,
  type: $enumDecode(_$CollaborationSessionTypeEnumMap, json['type']),
  title: json['title'] as String,
  description: json['description'] as String?,
  resourceId: json['resourceId'] as String,
  resourceType: json['resourceType'] as String,
  ownerId: json['ownerId'] as String,
  organizationId: json['organizationId'] as String?,
  participants:
      (json['participants'] as List<dynamic>?)
          ?.map((e) => SessionParticipant.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  status:
      $enumDecodeNullable(_$SessionStatusEnumMap, json['status']) ??
      SessionStatus.active,
  createdAt: DateTime.parse(json['createdAt'] as String),
  startedAt: json['startedAt'] == null
      ? null
      : DateTime.parse(json['startedAt'] as String),
  endedAt: json['endedAt'] == null
      ? null
      : DateTime.parse(json['endedAt'] as String),
  settings: json['settings'] as Map<String, dynamic>? ?? const {},
  sessionData: json['sessionData'] as Map<String, dynamic>?,
  maxParticipants: (json['maxParticipants'] as num?)?.toInt(),
  requiresInvitation: json['requiresInvitation'] as bool? ?? true,
  allowAnonymous: json['allowAnonymous'] as bool? ?? false,
  recordingInfo: json['recordingInfo'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$CollaborationSessionToJson(
  CollaborationSession instance,
) => <String, dynamic>{
  'id': instance.id,
  'type': _$CollaborationSessionTypeEnumMap[instance.type]!,
  'title': instance.title,
  'description': instance.description,
  'resourceId': instance.resourceId,
  'resourceType': instance.resourceType,
  'ownerId': instance.ownerId,
  'organizationId': instance.organizationId,
  'participants': instance.participants,
  'status': _$SessionStatusEnumMap[instance.status]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'startedAt': instance.startedAt?.toIso8601String(),
  'endedAt': instance.endedAt?.toIso8601String(),
  'settings': instance.settings,
  'sessionData': instance.sessionData,
  'maxParticipants': instance.maxParticipants,
  'requiresInvitation': instance.requiresInvitation,
  'allowAnonymous': instance.allowAnonymous,
  'recordingInfo': instance.recordingInfo,
};

const _$CollaborationSessionTypeEnumMap = {
  CollaborationSessionType.questEditing: 'quest_editing',
  CollaborationSessionType.taskEditing: 'task_editing',
  CollaborationSessionType.documentEditing: 'document_editing',
  CollaborationSessionType.whiteboard: 'whiteboard',
  CollaborationSessionType.brainstorming: 'brainstorming',
  CollaborationSessionType.codeReview: 'code_review',
  CollaborationSessionType.meeting: 'meeting',
};

const _$SessionStatusEnumMap = {
  SessionStatus.active: 'active',
  SessionStatus.paused: 'paused',
  SessionStatus.ended: 'ended',
};
