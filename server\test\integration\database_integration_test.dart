import 'package:test/test.dart';
import 'package:server/services/test_database_service.dart';

void main() {
  group('Database Integration Tests', () {
    late TestDatabaseService testDb;

    setUpAll(() async {
      print('🔧 Setting up database integration tests...');

      // Create isolated test database
      testDb = await TestDatabaseService.create(testId: 'integration_test');

      print('✅ Database integration test setup completed');
    });

    tearDownAll(() async {
      print('🧹 Cleaning up database integration tests...');
      await testDb.cleanup();
      print('✅ Database integration test cleanup completed');
    });

    group('User Management', () {
      test('should create and retrieve user successfully', () async {
        // Arrange
        final testUserData = {
          'username': 'integration_test_user',
          'email': '<EMAIL>',
          'display_name': 'Integration Test User',
          'is_active': true,
        };

        // Act - Create user directly in test database
        final result = await testDb.execute(
          '''
          INSERT INTO quester.users (username, email, display_name, is_active)
          VALUES (@username, @email, @displayName, @isActive)
          RETURNING id
          ''',
          parameters: {
            'username': testUserData['username'],
            'email': testUserData['email'],
            'displayName': testUserData['display_name'],
            'isActive': testUserData['is_active'],
          },
        );

        final userId = result.first[0].toString();

        // Assert - Verify user was created
        expect(userId, isNotEmpty);
        expect(userId.length, equals(36)); // UUID length

        // Verify user can be retrieved
        final retrievedUser = await testDb.execute(
          'SELECT username, email, display_name FROM quester.users WHERE id = @userId::uuid',
          parameters: {'userId': userId},
        );

        expect(retrievedUser.length, equals(1));
        expect(retrievedUser.first[0], equals(testUserData['username']));
        expect(retrievedUser.first[1], equals(testUserData['email']));
        expect(retrievedUser.first[2], equals(testUserData['display_name']));
      });

      test('should handle user stats operations', () async {
        // Arrange - Get a test user
        final users = await testDb.execute('SELECT id FROM quester.users LIMIT 1');
        expect(users.isNotEmpty, isTrue, reason: 'Test database should have seeded users');
        
        final userId = users.first[0].toString();

        // Act - Get user stats (this tests the optimized getUserStats method)
        final userStats = await testDb.execute(
          '''
          SELECT up.total_points, up.current_level, up.role::text,
                 s.current_streak, s.longest_streak
          FROM quester.user_points up
          LEFT JOIN quester.streaks s ON s.user_id = up.user_id
          WHERE up.user_id = @userId::uuid
          ''',
          parameters: {'userId': userId},
        );

        // Assert
        expect(userStats.isNotEmpty, isTrue);
        final stats = userStats.first;
        expect(stats[0], isA<int>()); // total_points
        expect(stats[1], isA<int>()); // current_level
        expect(stats[2], isA<String>()); // role
      });
    });

    group('Activity Tracking', () {
      test('should record and retrieve activity logs', () async {
        // Arrange
        final users = await testDb.execute('SELECT id FROM quester.users LIMIT 1');
        final userId = users.first[0].toString();
        
        final activityData = {
          'user_id': userId,
          'activity_type': 'quest_completed',
          'points_earned': 100,
          'description': 'Test activity description',
          'created_at': DateTime.now().toIso8601String(),
        };

        // Act - Insert activity log
        await testDb.execute(
          '''
          INSERT INTO quester.activity_log (user_id, activity_type, points_earned, description, created_at)
          VALUES (@userId::uuid, @activityType::activity_type, @points, @description, @createdAt::timestamp)
          ''',
          parameters: {
            'userId': activityData['user_id'],
            'activityType': activityData['activity_type'],
            'points': activityData['points_earned'],
            'description': activityData['description'],
            'createdAt': activityData['created_at'],
          },
        );

        // Assert - Verify activity was recorded
        final activities = await testDb.execute(
          '''
          SELECT activity_type::text, points_earned, description
          FROM quester.activity_log
          WHERE user_id = @userId::uuid
          ORDER BY created_at DESC
          LIMIT 1
          ''',
          parameters: {'userId': userId},
        );

        expect(activities.isNotEmpty, isTrue);
        final activity = activities.first;
        expect(activity[0], equals(activityData['activity_type']));
        expect(activity[1], equals(activityData['points_earned']));
        expect(activity[2], equals(activityData['description']));
      });

      test('should retrieve global activity feed efficiently', () async {
        // Act - Test the optimized global activity query
        final stopwatch = Stopwatch()..start();
        
        final activities = await testDb.execute(
          '''
          SELECT 
            al.user_id::text,
            u.username,
            u.display_name,
            al.activity_type::text,
            al.points_earned,
            al.description,
            al.created_at
          FROM (
            SELECT user_id, activity_type, points_earned, description, created_at
            FROM quester.activity_log
            ORDER BY created_at DESC
            LIMIT 10
          ) al
          JOIN quester.users u ON u.id = al.user_id AND u.is_active = true
          ORDER BY al.created_at DESC
          ''',
        );
        
        stopwatch.stop();

        // Assert - Performance and data integrity
        expect(stopwatch.elapsedMilliseconds, lessThan(100), 
          reason: 'Global activity feed should be fast');
        expect(activities.length, lessThanOrEqualTo(10));
        
        if (activities.isNotEmpty) {
          final activity = activities.first;
          expect(activity[0], isNotEmpty); // user_id
          expect(activity[1], isNotEmpty); // username
          expect(activity[3], isNotEmpty); // activity_type
          expect(activity[4], isA<int>()); // points_earned
        }
      });
    });

    group('Leaderboard Operations', () {
      test('should manage leaderboard rankings', () async {
        // Act - Get leaderboard data
        final leaderboard = await testDb.execute(
          '''
          SELECT 
            l.user_id::text,
            u.username,
            l.leaderboard_type::text,
            l.rank,
            l.score
          FROM quester.leaderboards l
          JOIN quester.users u ON u.id = l.user_id
          WHERE l.leaderboard_type = 'weekly_points'
          ORDER BY l.rank ASC
          LIMIT 5
          ''',
        );

        // Assert
        expect(leaderboard.isNotEmpty, isTrue);
        
        // Verify ranking order
        for (int i = 0; i < leaderboard.length - 1; i++) {
          final currentRank = leaderboard[i][3] as int;
          final nextRank = leaderboard[i + 1][3] as int;
          expect(currentRank, lessThan(nextRank), 
            reason: 'Leaderboard should be ordered by rank');
        }
      });
    });

    group('Achievement System', () {
      test('should track user achievements', () async {
        // Arrange
        final users = await testDb.execute('SELECT id FROM quester.users LIMIT 1');
        final userId = users.first[0].toString();
        
        final achievements = await testDb.execute('SELECT id FROM quester.achievements LIMIT 1');
        expect(achievements.isNotEmpty, isTrue, reason: 'Test database should have achievements');
        final achievementId = achievements.first[0].toString();

        // Act - Award achievement to user
        await testDb.execute(
          '''
          INSERT INTO quester.user_achievements (user_id, achievement_id, progress_percentage, earned_at)
          VALUES (@userId::uuid, @achievementId::uuid, 100, NOW())
          ''',
          parameters: {
            'userId': userId,
            'achievementId': achievementId,
          },
        );

        // Assert - Verify achievement was awarded
        final userAchievements = await testDb.execute(
          '''
          SELECT ua.progress_percentage, a.name
          FROM quester.user_achievements ua
          JOIN quester.achievements a ON a.id = ua.achievement_id
          WHERE ua.user_id = @userId::uuid
          ''',
          parameters: {'userId': userId},
        );

        expect(userAchievements.isNotEmpty, isTrue);
        expect(userAchievements.first[0], equals(100)); // progress_percentage
        expect(userAchievements.first[1], isNotEmpty); // achievement name
      });
    });

    group('Performance Validation', () {
      test('should maintain performance under concurrent operations', () async {
        // Arrange
        final users = await testDb.execute('SELECT id FROM quester.users');
        expect(users.length, greaterThanOrEqualTo(3), 
          reason: 'Need multiple users for concurrency test');

        // Act - Simulate concurrent user stats queries
        final futures = <Future>[];
        final stopwatch = Stopwatch()..start();
        
        for (final user in users.take(3)) {
          final userId = user[0].toString();
          futures.add(
            testDb.execute(
              '''
              SELECT up.total_points, up.current_level, s.current_streak
              FROM quester.user_points up
              LEFT JOIN quester.streaks s ON s.user_id = up.user_id
              WHERE up.user_id = @userId::uuid
              ''',
              parameters: {'userId': userId},
            )
          );
        }
        
        final results = await Future.wait(futures);
        stopwatch.stop();

        // Assert - Performance and correctness
        expect(stopwatch.elapsedMilliseconds, lessThan(200), 
          reason: 'Concurrent queries should complete quickly');
        expect(results.length, equals(3));
        
        for (final result in results) {
          expect(result.isNotEmpty, isTrue);
        }
      });

      test('should handle large dataset queries efficiently', () async {
        // Act - Query with potential for large result set
        final stopwatch = Stopwatch()..start();
        
        final activities = await testDb.execute(
          '''
          SELECT COUNT(*) as total_activities,
                 COUNT(DISTINCT user_id) as active_users,
                 AVG(points_earned) as avg_points
          FROM quester.activity_log
          WHERE created_at >= NOW() - INTERVAL '7 days'
          ''',
        );
        
        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(100), 
          reason: 'Aggregate queries should be fast');
        expect(activities.isNotEmpty, isTrue);
        
        final stats = activities.first;
        expect(stats[0], isA<int>()); // total_activities
        expect(stats[1], isA<int>()); // active_users
      });
    });

    group('Data Integrity', () {
      test('should maintain referential integrity', () async {
        // Test foreign key constraints
        expect(
          () async => await testDb.execute(
            '''
            INSERT INTO quester.user_points (user_id, total_points, current_level, role)
            VALUES ('00000000-0000-0000-0000-000000000000'::uuid, 100, 1, 'adventurer'::user_role)
            '''
          ),
          throwsA(isA<Exception>()),
          reason: 'Should not allow invalid user_id in user_points',
        );
      });

      test('should enforce data constraints', () async {
        // Test check constraints
        expect(
          () async => await testDb.execute(
            '''
            INSERT INTO quester.achievements (name, description, category, rarity, points_reward)
            VALUES ('Test', 'Test', 'Test', 'Invalid'::achievement_rarity, -100)
            '''
          ),
          throwsA(isA<Exception>()),
          reason: 'Should not allow invalid enum values or negative points',
        );
      });
    });
  });
}
