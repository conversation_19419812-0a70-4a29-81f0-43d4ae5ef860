import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/repositories/api_repository.dart';

// Events
abstract class EnterpriseEvent extends Equatable {
  const EnterpriseEvent();
  
  @override
  List<Object?> get props => [];
}

class CreateOrganization extends EnterpriseEvent {
  final Map<String, dynamic> orgData;
  
  const CreateOrganization({required this.orgData});
  
  @override
  List<Object?> get props => [orgData];
}

class LoadOrganization extends EnterpriseEvent {
  final String orgId;
  
  const LoadOrganization({required this.orgId});
  
  @override
  List<Object?> get props => [orgId];
}

class UpdateOrganization extends EnterpriseEvent {
  final String orgId;
  final Map<String, dynamic> updateData;
  
  const UpdateOrganization({
    required this.orgId,
    required this.updateData,
  });
  
  @override
  List<Object?> get props => [orgId, updateData];
}

class LoadUserOrganizations extends EnterpriseEvent {
  final String userId;
  
  const LoadUserOrganizations({required this.userId});
  
  @override
  List<Object?> get props => [userId];
}

class LoadMembers extends EnterpriseEvent {
  final String orgId;
  
  const LoadMembers({required this.orgId});
  
  @override
  List<Object?> get props => [orgId];
}

class CreateMember extends EnterpriseEvent {
  final String orgId;
  final Map<String, dynamic> memberData;
  
  const CreateMember({
    required this.orgId,
    required this.memberData,
  });
  
  @override
  List<Object?> get props => [orgId, memberData];
}

class UpdateMember extends EnterpriseEvent {
  final String orgId;
  final String memberId;
  final Map<String, dynamic> updateData;
  
  const UpdateMember({
    required this.orgId,
    required this.memberId,
    required this.updateData,
  });
  
  @override
  List<Object?> get props => [orgId, memberId, updateData];
}

class DeleteMember extends EnterpriseEvent {
  final String orgId;
  final String memberId;
  
  const DeleteMember({
    required this.orgId,
    required this.memberId,
  });
  
  @override
  List<Object?> get props => [orgId, memberId];
}

class LoadRoles extends EnterpriseEvent {
  final String orgId;
  
  const LoadRoles({required this.orgId});
  
  @override
  List<Object?> get props => [orgId];
}

class CreateRole extends EnterpriseEvent {
  final String orgId;
  final Map<String, dynamic> roleData;
  
  const CreateRole({
    required this.orgId,
    required this.roleData,
  });
  
  @override
  List<Object?> get props => [orgId, roleData];
}

class UpdateRole extends EnterpriseEvent {
  final String orgId;
  final String roleId;
  final Map<String, dynamic> updateData;
  
  const UpdateRole({
    required this.orgId,
    required this.roleId,
    required this.updateData,
  });
  
  @override
  List<Object?> get props => [orgId, roleId, updateData];
}

class DeleteRole extends EnterpriseEvent {
  final String orgId;
  final String roleId;
  
  const DeleteRole({
    required this.orgId,
    required this.roleId,
  });
  
  @override
  List<Object?> get props => [orgId, roleId];
}

class LoadEnterpriseAnalytics extends EnterpriseEvent {
  final String orgId;
  final Map<String, String>? params;
  
  const LoadEnterpriseAnalytics({
    required this.orgId,
    this.params,
  });
  
  @override
  List<Object?> get props => [orgId, params];
}

class RefreshEnterprise extends EnterpriseEvent {
  const RefreshEnterprise();
}

// States
abstract class EnterpriseState extends Equatable {
  const EnterpriseState();
  
  @override
  List<Object?> get props => [];
}

class EnterpriseInitial extends EnterpriseState {
  const EnterpriseInitial();
}

class EnterpriseLoading extends EnterpriseState {
  const EnterpriseLoading();
}

class EnterpriseLoaded extends EnterpriseState {
  final Map<String, dynamic>? currentOrganization;
  final List<dynamic>? userOrganizations;
  final List<dynamic>? members;
  final List<dynamic>? roles;
  final Map<String, dynamic>? analytics;
  
  const EnterpriseLoaded({
    this.currentOrganization,
    this.userOrganizations,
    this.members,
    this.roles,
    this.analytics,
  });
  
  @override
  List<Object?> get props => [currentOrganization, userOrganizations, members, roles, analytics];
  
  EnterpriseLoaded copyWith({
    Map<String, dynamic>? currentOrganization,
    List<dynamic>? userOrganizations,
    List<dynamic>? members,
    List<dynamic>? roles,
    Map<String, dynamic>? analytics,
  }) {
    return EnterpriseLoaded(
      currentOrganization: currentOrganization ?? this.currentOrganization,
      userOrganizations: userOrganizations ?? this.userOrganizations,
      members: members ?? this.members,
      roles: roles ?? this.roles,
      analytics: analytics ?? this.analytics,
    );
  }
}

class EnterpriseError extends EnterpriseState {
  final String message;
  
  const EnterpriseError({required this.message});
  
  @override
  List<Object?> get props => [message];
}

class OrganizationCreated extends EnterpriseState {
  final Map<String, dynamic> organization;
  
  const OrganizationCreated({required this.organization});
  
  @override
  List<Object?> get props => [organization];
}

class OrganizationUpdated extends EnterpriseState {
  final String orgId;
  final String message;
  
  const OrganizationUpdated({
    required this.orgId,
    required this.message,
  });
  
  @override
  List<Object?> get props => [orgId, message];
}

class MemberCreated extends EnterpriseState {
  final Map<String, dynamic> member;
  
  const MemberCreated({required this.member});
  
  @override
  List<Object?> get props => [member];
}

class MemberUpdated extends EnterpriseState {
  final String orgId;
  final String memberId;
  final String message;
  
  const MemberUpdated({
    required this.orgId,
    required this.memberId,
    required this.message,
  });
  
  @override
  List<Object?> get props => [orgId, memberId, message];
}

class MemberDeleted extends EnterpriseState {
  final String orgId;
  final String memberId;
  final String message;
  
  const MemberDeleted({
    required this.orgId,
    required this.memberId,
    required this.message,
  });
  
  @override
  List<Object?> get props => [orgId, memberId, message];
}

class RoleCreated extends EnterpriseState {
  final Map<String, dynamic> role;
  
  const RoleCreated({required this.role});
  
  @override
  List<Object?> get props => [role];
}

class RoleUpdated extends EnterpriseState {
  final String orgId;
  final String roleId;
  final String message;
  
  const RoleUpdated({
    required this.orgId,
    required this.roleId,
    required this.message,
  });
  
  @override
  List<Object?> get props => [orgId, roleId, message];
}

class RoleDeleted extends EnterpriseState {
  final String orgId;
  final String roleId;
  final String message;
  
  const RoleDeleted({
    required this.orgId,
    required this.roleId,
    required this.message,
  });
  
  @override
  List<Object?> get props => [orgId, roleId, message];
}

// BLoC
class EnterpriseBloc extends Bloc<EnterpriseEvent, EnterpriseState> {
  final ApiRepository repository;
  
  EnterpriseBloc({required this.repository}) : super(const EnterpriseInitial()) {
    on<CreateOrganization>(_onCreateOrganization);
    on<LoadOrganization>(_onLoadOrganization);
    on<UpdateOrganization>(_onUpdateOrganization);
    on<LoadUserOrganizations>(_onLoadUserOrganizations);
    on<LoadMembers>(_onLoadMembers);
    on<CreateMember>(_onCreateMember);
    on<UpdateMember>(_onUpdateMember);
    on<DeleteMember>(_onDeleteMember);
    on<LoadRoles>(_onLoadRoles);
    on<CreateRole>(_onCreateRole);
    on<UpdateRole>(_onUpdateRole);
    on<DeleteRole>(_onDeleteRole);
    on<LoadEnterpriseAnalytics>(_onLoadEnterpriseAnalytics);
    on<RefreshEnterprise>(_onRefreshEnterprise);
  }

  Future<void> _onCreateOrganization(CreateOrganization event, Emitter<EnterpriseState> emit) async {
    emit(const EnterpriseLoading());
    
    try {
      final response = await repository.createOrganization(event.orgData);
      
      if (response.isSuccess && response.data != null) {
        emit(OrganizationCreated(organization: response.data!));
        // Update the loaded state with the new organization
        final currentState = state is EnterpriseLoaded ? state as EnterpriseLoaded : const EnterpriseLoaded();
        emit(currentState.copyWith(currentOrganization: response.data));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to create organization'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error creating organization: $e'));
    }
  }

  Future<void> _onLoadOrganization(LoadOrganization event, Emitter<EnterpriseState> emit) async {
    emit(const EnterpriseLoading());
    
    try {
      final response = await repository.getOrganization(event.orgId);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is EnterpriseLoaded ? state as EnterpriseLoaded : const EnterpriseLoaded();
        emit(currentState.copyWith(currentOrganization: response.data));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to load organization'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error loading organization: $e'));
    }
  }

  Future<void> _onUpdateOrganization(UpdateOrganization event, Emitter<EnterpriseState> emit) async {
    emit(const EnterpriseLoading());
    
    try {
      final response = await repository.updateOrganization(event.orgId, event.updateData);
      
      if (response.isSuccess) {
        emit(OrganizationUpdated(
          orgId: event.orgId,
          message: 'Organization updated successfully',
        ));
        // Return to loaded state
        if (state is EnterpriseLoaded) {
          emit(state as EnterpriseLoaded);
        } else {
          emit(const EnterpriseLoaded());
        }
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to update organization'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error updating organization: $e'));
    }
  }

  Future<void> _onLoadUserOrganizations(LoadUserOrganizations event, Emitter<EnterpriseState> emit) async {
    emit(const EnterpriseLoading());
    
    try {
      final response = await repository.listUserOrganizations(event.userId);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is EnterpriseLoaded ? state as EnterpriseLoaded : const EnterpriseLoaded();
        emit(currentState.copyWith(userOrganizations: response.data));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to load user organizations'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error loading user organizations: $e'));
    }
  }

  Future<void> _onLoadMembers(LoadMembers event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.listMembers(event.orgId);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is EnterpriseLoaded ? state as EnterpriseLoaded : const EnterpriseLoaded();
        emit(currentState.copyWith(members: response.data));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to load members'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error loading members: $e'));
    }
  }

  Future<void> _onCreateMember(CreateMember event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.createMember(event.orgId, event.memberData);
      
      if (response.isSuccess && response.data != null) {
        emit(MemberCreated(member: response.data!));
        // Reload members
        add(LoadMembers(orgId: event.orgId));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to create member'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error creating member: $e'));
    }
  }

  Future<void> _onUpdateMember(UpdateMember event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.updateMember(event.orgId, event.memberId, event.updateData);
      
      if (response.isSuccess) {
        emit(MemberUpdated(
          orgId: event.orgId,
          memberId: event.memberId,
          message: 'Member updated successfully',
        ));
        // Reload members
        add(LoadMembers(orgId: event.orgId));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to update member'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error updating member: $e'));
    }
  }

  Future<void> _onDeleteMember(DeleteMember event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.deleteMember(event.orgId, event.memberId);
      
      if (response.isSuccess) {
        emit(MemberDeleted(
          orgId: event.orgId,
          memberId: event.memberId,
          message: 'Member deleted successfully',
        ));
        // Reload members
        add(LoadMembers(orgId: event.orgId));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to delete member'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error deleting member: $e'));
    }
  }

  Future<void> _onLoadRoles(LoadRoles event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.listRoles(event.orgId);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is EnterpriseLoaded ? state as EnterpriseLoaded : const EnterpriseLoaded();
        emit(currentState.copyWith(roles: response.data));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to load roles'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error loading roles: $e'));
    }
  }

  Future<void> _onCreateRole(CreateRole event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.createRole(event.orgId, event.roleData);
      
      if (response.isSuccess && response.data != null) {
        emit(RoleCreated(role: response.data!));
        // Reload roles
        add(LoadRoles(orgId: event.orgId));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to create role'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error creating role: $e'));
    }
  }

  Future<void> _onUpdateRole(UpdateRole event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.updateRole(event.orgId, event.roleId, event.updateData);
      
      if (response.isSuccess) {
        emit(RoleUpdated(
          orgId: event.orgId,
          roleId: event.roleId,
          message: 'Role updated successfully',
        ));
        // Reload roles
        add(LoadRoles(orgId: event.orgId));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to update role'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error updating role: $e'));
    }
  }

  Future<void> _onDeleteRole(DeleteRole event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.deleteRole(event.orgId, event.roleId);
      
      if (response.isSuccess) {
        emit(RoleDeleted(
          orgId: event.orgId,
          roleId: event.roleId,
          message: 'Role deleted successfully',
        ));
        // Reload roles
        add(LoadRoles(orgId: event.orgId));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to delete role'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error deleting role: $e'));
    }
  }

  Future<void> _onLoadEnterpriseAnalytics(LoadEnterpriseAnalytics event, Emitter<EnterpriseState> emit) async {
    try {
      final response = await repository.getEnterpriseAnalytics(event.orgId, params: event.params);
      
      if (response.isSuccess && response.data != null) {
        final currentState = state is EnterpriseLoaded ? state as EnterpriseLoaded : const EnterpriseLoaded();
        emit(currentState.copyWith(analytics: response.data));
      } else {
        emit(EnterpriseError(message: response.error ?? 'Failed to load enterprise analytics'));
      }
    } catch (e) {
      emit(EnterpriseError(message: 'Error loading enterprise analytics: $e'));
    }
  }

  Future<void> _onRefreshEnterprise(RefreshEnterprise event, Emitter<EnterpriseState> emit) async {
    emit(const EnterpriseLoading());
    
    try {
      // Refresh enterprise data
      emit(const EnterpriseLoaded());
    } catch (e) {
      emit(EnterpriseError(message: 'Error refreshing enterprise: $e'));
    }
  }
}