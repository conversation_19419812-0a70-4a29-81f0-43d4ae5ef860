import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import '../performance/performance_monitor.dart';
import 'cache_manager.dart';

/// Performance optimization utilities and strategies
class PerformanceOptimizer {
  static final PerformanceOptimizer _instance = PerformanceOptimizer._internal();
  factory PerformanceOptimizer() => _instance;
  PerformanceOptimizer._internal();

  final PerformanceMonitor _monitor = PerformanceMonitor();
  final CacheManager _cache = CacheManager();
  
  final Map<String, Isolate> _backgroundIsolates = {};
  final Map<String, Timer> _debounceTimers = {};
  final Map<String, Completer> _throttleCompleters = {};
  
  bool _isOptimizationEnabled = true;
  Timer? _optimizationTimer;

  /// Initialize performance optimizer
  Future<void> initialize() async {
    if (!_isOptimizationEnabled) return;

    _monitor.initialize();
    await _cache.initialize();
    
    // Start periodic optimization
    _startPeriodicOptimization();
    
    _monitor.logEvent('performance_optimizer_initialized');
  }

  /// Optimize widget build performance
  T optimizeBuild<T>(String widgetName, T Function() buildFunction) {
    if (!_isOptimizationEnabled) return buildFunction();
    
    return _monitor.timeFunctionSync('${widgetName}_build', buildFunction);
  }

  /// Optimize async operations with caching
  Future<T> optimizeAsync<T>(
    String operationName,
    Future<T> Function() operation, {
    Duration? cacheDuration,
    bool useCache = true,
  }) async {
    if (!_isOptimizationEnabled) return await operation();

    final cacheKey = 'async_$operationName';
    
    // Try cache first
    if (useCache) {
      final cached = await _cache.get<T>(cacheKey);
      if (cached != null) {
        _monitor.recordMetric('${operationName}_cache_hit', 1);
        return cached;
      }
    }

    // Execute operation with timing
    final result = await _monitor.timeFunction(operationName, operation);
    
    // Cache result
    if (useCache && cacheDuration != null) {
      await _cache.set(cacheKey, result, ttlSeconds: cacheDuration.inSeconds);
    }
    
    return result;
  }

  /// Debounce function calls to reduce excessive operations
  void debounce(String key, VoidCallback callback, Duration delay) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, () {
      callback();
      _debounceTimers.remove(key);
    });
  }

  /// Throttle function calls to limit execution frequency
  Future<T> throttle<T>(
    String key,
    Future<T> Function() operation,
    Duration interval,
  ) async {
    // Check if already throttled
    if (_throttleCompleters.containsKey(key)) {
      return await _throttleCompleters[key]!.future as T;
    }

    final completer = Completer<T>();
    _throttleCompleters[key] = completer;

    try {
      final result = await operation();
      completer.complete(result);
      
      // Set throttle timer
      Timer(interval, () {
        _throttleCompleters.remove(key);
      });
      
      return result;
    } catch (error) {
      completer.completeError(error);
      _throttleCompleters.remove(key);
      rethrow;
    }
  }

  /// Execute heavy computation in background isolate
  Future<T> executeInBackground<T>(
    String taskName,
    T Function() computation,
  ) async {
    if (!_isOptimizationEnabled) return computation();

    _monitor.startTimer('background_task_$taskName');
    
    try {
      final receivePort = ReceivePort();
      final isolate = await Isolate.spawn(
        _backgroundTaskRunner,
        [receivePort.sendPort, computation],
      );
      
      _backgroundIsolates[taskName] = isolate;
      
      final result = await receivePort.first as T;
      
      isolate.kill();
      _backgroundIsolates.remove(taskName);
      
      return result;
    } finally {
      _monitor.stopTimer('background_task_$taskName');
    }
  }

  /// Optimize list rendering with lazy loading
  List<T> optimizeListRendering<T>(
    List<T> items, {
    int? visibleItemCount,
    int bufferSize = 10,
  }) {
    if (!_isOptimizationEnabled || visibleItemCount == null) {
      return items;
    }

    final totalItems = visibleItemCount + bufferSize;
    return items.take(totalItems).toList();
  }

  /// Optimize image loading and caching
  Future<void> optimizeImageLoading(List<String> imageUrls) async {
    if (!_isOptimizationEnabled) return;

    _monitor.startTimer('image_preloading');
    
    try {
      // Preload images in batches
      const batchSize = 5;
      for (int i = 0; i < imageUrls.length; i += batchSize) {
        final batch = imageUrls.skip(i).take(batchSize);
        await Future.wait(
          batch.map((url) => _preloadImage(url)),
          eagerError: false,
        );
      }
    } finally {
      _monitor.stopTimer('image_preloading');
    }
  }

  /// Optimize memory usage by clearing unused resources
  Future<void> optimizeMemoryUsage() async {
    if (!_isOptimizationEnabled) return;

    _monitor.startTimer('memory_optimization');
    
    try {
      // Clear expired cache entries
      await _cache.cleanup();
      
      // Force garbage collection (if available)
      if (kDebugMode) {
        // In debug mode, we can suggest GC
        await _suggestGarbageCollection();
      }
      
      // Clear unused isolates
      _cleanupBackgroundIsolates();
      
      _monitor.recordMetric('memory_optimization_completed', 1);
    } finally {
      _monitor.stopTimer('memory_optimization');
    }
  }

  /// Optimize network requests with batching
  Future<List<T>> optimizeNetworkBatch<T>(
    List<Future<T> Function()> requests, {
    int batchSize = 5,
    Duration batchDelay = const Duration(milliseconds: 100),
  }) async {
    if (!_isOptimizationEnabled) {
      return await Future.wait(requests.map((r) => r()));
    }

    _monitor.startTimer('network_batch_optimization');
    
    try {
      final results = <T>[];
      
      for (int i = 0; i < requests.length; i += batchSize) {
        final batch = requests.skip(i).take(batchSize);
        final batchResults = await Future.wait(
          batch.map((request) => request()),
          eagerError: false,
        );
        
        results.addAll(batchResults);
        
        // Small delay between batches to prevent overwhelming
        if (i + batchSize < requests.length) {
          await Future.delayed(batchDelay);
        }
      }
      
      return results;
    } finally {
      _monitor.stopTimer('network_batch_optimization');
    }
  }

  /// Get optimization recommendations
  List<OptimizationRecommendation> getRecommendations() {
    final recommendations = <OptimizationRecommendation>[];
    final summary = _monitor.getSummary();
    
    // Analyze performance metrics
    for (final metric in summary.metrics.entries) {
      final metricSummary = metric.value;
      
      // Check for slow operations
      if (metricSummary.average > 1000) { // > 1 second
        recommendations.add(OptimizationRecommendation(
          type: OptimizationType.performance,
          severity: RecommendationSeverity.high,
          title: 'Slow Operation Detected',
          description: '${metric.key} is taking ${metricSummary.average.toInt()}ms on average',
          suggestion: 'Consider optimizing this operation or moving it to a background thread',
        ));
      }
      
      // Check for memory issues
      if (metric.key.contains('memory') && metricSummary.average > 150) {
        recommendations.add(OptimizationRecommendation(
          type: OptimizationType.memory,
          severity: RecommendationSeverity.medium,
          title: 'High Memory Usage',
          description: 'Memory usage is ${metricSummary.average.toInt()}MB on average',
          suggestion: 'Consider implementing more aggressive caching cleanup or reducing data retention',
        ));
      }
    }
    
    // Check cache hit rate
    final cacheStats = _cache.getStatistics();
    if (cacheStats.hitRate < 0.7) {
      recommendations.add(OptimizationRecommendation(
        type: OptimizationType.caching,
        severity: RecommendationSeverity.medium,
        title: 'Low Cache Hit Rate',
        description: 'Cache hit rate is ${(cacheStats.hitRate * 100).toInt()}%',
        suggestion: 'Review caching strategy and increase cache TTL for frequently accessed data',
      ));
    }
    
    return recommendations;
  }

  /// Apply automatic optimizations
  Future<void> applyAutomaticOptimizations() async {
    if (!_isOptimizationEnabled) return;

    _monitor.startTimer('automatic_optimization');
    
    try {
      // Clean up expired cache entries
      await _cache.cleanup();
      
      // Optimize memory usage
      await optimizeMemoryUsage();
      
      // Clear old performance data
      _monitor.clear();
      
      _monitor.recordMetric('automatic_optimization_completed', 1);
    } finally {
      _monitor.stopTimer('automatic_optimization');
    }
  }

  /// Enable or disable optimization
  void setOptimizationEnabled(bool enabled) {
    _isOptimizationEnabled = enabled;
    
    if (enabled) {
      _startPeriodicOptimization();
    } else {
      _optimizationTimer?.cancel();
    }
    
    _monitor.logEvent('optimization_${enabled ? 'enabled' : 'disabled'}');
  }

  /// Start periodic optimization
  void _startPeriodicOptimization() {
    _optimizationTimer?.cancel();
    
    _optimizationTimer = Timer.periodic(
      const Duration(minutes: 5),
      (timer) => applyAutomaticOptimizations(),
    );
  }

  /// Background task runner for isolates
  static void _backgroundTaskRunner(List<dynamic> args) {
    final sendPort = args[0] as SendPort;
    final computation = args[1] as Function();
    
    try {
      final result = computation();
      sendPort.send(result);
    } catch (error) {
      sendPort.send(error);
    }
  }

  /// Preload image helper
  Future<void> _preloadImage(String url) async {
    try {
      // This would use actual image loading in a real implementation
      await Future.delayed(const Duration(milliseconds: 100));
      _monitor.recordMetric('image_preloaded', 1);
    } catch (e) {
      _monitor.recordMetric('image_preload_failed', 1);
    }
  }

  /// Suggest garbage collection
  Future<void> _suggestGarbageCollection() async {
    // This is a placeholder - actual GC triggering would be platform-specific
    await Future.delayed(const Duration(milliseconds: 10));
  }

  /// Cleanup background isolates
  void _cleanupBackgroundIsolates() {
    for (final isolate in _backgroundIsolates.values) {
      isolate.kill();
    }
    _backgroundIsolates.clear();
  }

  /// Dispose resources
  void dispose() {
    _optimizationTimer?.cancel();
    
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    
    _throttleCompleters.clear();
    _cleanupBackgroundIsolates();
    
    _monitor.dispose();
  }
}

/// Optimization recommendation
class OptimizationRecommendation {
  final OptimizationType type;
  final RecommendationSeverity severity;
  final String title;
  final String description;
  final String suggestion;

  const OptimizationRecommendation({
    required this.type,
    required this.severity,
    required this.title,
    required this.description,
    required this.suggestion,
  });
}

/// Types of optimizations
enum OptimizationType {
  performance,
  memory,
  network,
  caching,
  ui,
}

/// Recommendation severity levels
enum RecommendationSeverity {
  low,
  medium,
  high,
  critical,
}
