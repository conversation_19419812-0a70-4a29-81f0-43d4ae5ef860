/// Authentication middleware for the Quester server
library;

import 'package:shelf/shelf.dart';
import '../services/auth_service.dart';
import '../utils/response_utils.dart';

/// Middleware for handling authentication
class AuthMiddleware {
  final AuthService _authService;

  const AuthMiddleware(this._authService);

  /// Creates middleware that requires authentication
  Middleware requireAuth() {
    return (Handler innerHandler) {
      return (Request request) async {
        try {
          // Extract token from Authorization header
          final authHeader = request.headers['authorization'];
          if (authHeader == null || !authHeader.startsWith('Bearer ')) {
            return ResponseUtils.unauthorized(
              message: 'Missing or invalid authorization header',
            );
          }

          final token = authHeader.substring(7); // Remove 'Bearer ' prefix
          
          // Validate token
          final user = await _authService.validateToken(token);
          if (user == null) {
            return ResponseUtils.unauthorized(
              message: 'Invalid or expired token',
            );
          }

          // Add user to request context
          final updatedRequest = request.change(context: {
            ...request.context,
            'user': user,
            'userId': user['id'],
          });

          return await innerHandler(updatedRequest);
        } catch (e) {
          return ResponseUtils.unauthorized(
            message: 'Authentication failed: ${e.toString()}',
          );
        }
      };
    };
  }

  /// Creates middleware that optionally authenticates (doesn't fail if no auth)
  Middleware optionalAuth() {
    return (Handler innerHandler) {
      return (Request request) async {
        try {
          final authHeader = request.headers['authorization'];
          if (authHeader != null && authHeader.startsWith('Bearer ')) {
            final token = authHeader.substring(7);
            final user = await _authService.validateToken(token);
            
            if (user != null) {
              final updatedRequest = request.change(context: {
                ...request.context,
                'user': user,
                'userId': user['id'],
              });
              return await innerHandler(updatedRequest);
            }
          }

          // Continue without authentication
          return await innerHandler(request);
        } catch (e) {
          // Continue without authentication on error
          return await innerHandler(request);
        }
      };
    };
  }

  /// Creates middleware that requires specific roles
  Middleware requireRole(List<String> allowedRoles) {
    return (Handler innerHandler) {
      return (Request request) async {
        final user = request.context['user'] as Map<String, dynamic>?;
        if (user == null) {
          return ResponseUtils.unauthorized(
            message: 'Authentication required',
          );
        }

        final userRole = user['role'] as String?;
        if (userRole == null || !allowedRoles.contains(userRole)) {
          return ResponseUtils.forbidden(
            message: 'Insufficient permissions. Required roles: ${allowedRoles.join(', ')}',
          );
        }

        return await innerHandler(request);
      };
    };
  }

  /// Creates middleware that requires admin role
  Middleware requireAdmin() {
    return requireRole(['admin', 'super_admin']);
  }

  /// Creates middleware that requires the user to own the resource or be admin
  Middleware requireOwnershipOrAdmin(String userIdParam) {
    return (Handler innerHandler) {
      return (Request request) async {
        final user = request.context['user'] as Map<String, dynamic>?;
        if (user == null) {
          return ResponseUtils.unauthorized(
            message: 'Authentication required',
          );
        }

        final userId = user['id'] as String;
        final userRole = user['role'] as String?;
        final resourceUserId = request.url.pathSegments.isNotEmpty
            ? request.url.pathSegments.last
            : null;

        // Allow if user is admin or owns the resource
        if (userRole == 'admin' || 
            userRole == 'super_admin' || 
            userId == resourceUserId) {
          return await innerHandler(request);
        }

        return ResponseUtils.forbidden(
          message: 'You can only access your own resources',
        );
      };
    };
  }

  /// Creates middleware for API key authentication
  Middleware requireApiKey() {
    return (Handler innerHandler) {
      return (Request request) async {
        try {
          final apiKey = request.headers['x-api-key'];
          if (apiKey == null) {
            return ResponseUtils.unauthorized(
              message: 'API key required',
            );
          }

          // Validate API key (implement your API key validation logic)
          final isValid = await _authService.validateApiKey(apiKey);
          if (!isValid) {
            return ResponseUtils.unauthorized(
              message: 'Invalid API key',
            );
          }

          return await innerHandler(request);
        } catch (e) {
          return ResponseUtils.unauthorized(
            message: 'API key validation failed: ${e.toString()}',
          );
        }
      };
    };
  }

  /// Creates middleware for rate limiting based on user
  Middleware rateLimit({
    int maxRequests = 100,
    Duration window = const Duration(minutes: 15),
  }) {
    final Map<String, List<DateTime>> requestCounts = {};

    return (Handler innerHandler) {
      return (Request request) async {
        final userId = request.context['userId'] as String?;
        final identifier = userId ?? request.headers['x-forwarded-for'] ?? 'anonymous';
        
        final now = DateTime.now();
        final windowStart = now.subtract(window);
        
        // Clean old requests
        requestCounts[identifier]?.removeWhere((time) => time.isBefore(windowStart));
        
        // Check rate limit
        final currentRequests = requestCounts[identifier] ?? [];
        if (currentRequests.length >= maxRequests) {
          return ResponseUtils.error(
            message: 'Rate limit exceeded. Try again later.',
            statusCode: 429,
            errorCode: 'RATE_LIMIT_EXCEEDED',
          );
        }
        
        // Add current request
        requestCounts[identifier] = [...currentRequests, now];
        
        return await innerHandler(request);
      };
    };
  }

  /// Creates middleware for CORS handling
  static Middleware cors({
    String origin = '*',
    String methods = 'GET, POST, PUT, DELETE, OPTIONS',
    String headers = 'Content-Type, Authorization, X-API-Key',
  }) {
    return (Handler innerHandler) {
      return (Request request) async {
        if (request.method == 'OPTIONS') {
          return ResponseUtils.options(
            origin: origin,
            methods: methods,
            headers: headers,
          );
        }

        final response = await innerHandler(request);
        
        return response.change(headers: {
          ...response.headers,
          ...ResponseUtils.corsHeaders(
            origin: origin,
            methods: methods,
            headers: headers,
          ),
        });
      };
    };
  }

  /// Creates middleware for request logging
  static Middleware logging() {
    return (Handler innerHandler) {
      return (Request request) async {
        final start = DateTime.now();
        final response = await innerHandler(request);
        final duration = DateTime.now().difference(start);
        
        print('${request.method} ${request.requestedUri} - '
              '${response.statusCode} - ${duration.inMilliseconds}ms');
        
        return response;
      };
    };
  }

  /// Creates middleware for error handling
  static Middleware errorHandler() {
    return (Handler innerHandler) {
      return (Request request) async {
        try {
          return await innerHandler(request);
        } catch (error, stackTrace) {
          print('Error handling request: $error');
          print('Stack trace: $stackTrace');
          
          return ResponseUtils.internalServerError(
            message: 'An unexpected error occurred',
            details: error.toString(),
          );
        }
      };
    };
  }
}

/// Extension to add helper methods to Request
extension RequestExtensions on Request {
  /// Gets the authenticated user from the request context
  Map<String, dynamic>? get user => context['user'] as Map<String, dynamic>?;
  
  /// Gets the authenticated user ID from the request context
  String? get userId => context['userId'] as String?;
  
  /// Checks if the request is authenticated
  bool get isAuthenticated => user != null;
  
  /// Checks if the authenticated user has a specific role
  bool hasRole(String role) {
    final userRole = user?['role'] as String?;
    return userRole == role;
  }
  
  /// Checks if the authenticated user is an admin
  bool get isAdmin => hasRole('admin') || hasRole('super_admin');
}
