import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// A customizable user avatar widget with various display options
class UserAvatar extends StatelessWidget {
  /// User data to display
  final User user;
  
  /// Size of the avatar
  final double size;
  
  /// Border radius (null for circular)
  final double? borderRadius;
  
  /// Border width
  final double borderWidth;
  
  /// Border color
  final Color? borderColor;
  
  /// Background color for initials
  final Color? backgroundColor;
  
  /// Text color for initials
  final Color? textColor;
  
  /// Font size for initials (auto-calculated if null)
  final double? fontSize;
  
  /// Whether to show online status indicator
  final bool showOnlineStatus;
  
  /// Online status
  final bool isOnline;
  
  /// Callback when avatar is tapped
  final VoidCallback? onTap;
  
  /// Whether to show a placeholder when loading
  final bool showPlaceholder;
  
  /// Custom placeholder widget
  final Widget? placeholder;
  
  /// Whether to show level badge
  final bool showLevelBadge;

  const UserAvatar({
    super.key,
    required this.user,
    this.size = 40,
    this.borderRadius,
    this.borderWidth = 0,
    this.borderColor,
    this.backgroundColor,
    this.textColor,
    this.fontSize,
    this.showOnlineStatus = false,
    this.isOnline = false,
    this.onTap,
    this.showPlaceholder = true,
    this.placeholder,
    this.showLevelBadge = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget avatar = _buildAvatar(context);
    
    // Add online status indicator
    if (showOnlineStatus) {
      avatar = _buildWithOnlineStatus(avatar, context);
    }
    
    // Add level badge
    if (showLevelBadge) {
      avatar = _buildWithLevelBadge(avatar, context);
    }
    
    // Add tap functionality
    if (onTap != null) {
      avatar = GestureDetector(
        onTap: onTap,
        child: avatar,
      );
    }
    
    return avatar;
  }

  /// Build the main avatar widget
  Widget _buildAvatar(BuildContext context) {
    final effectiveBorderRadius = borderRadius ?? size / 2;
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        border: borderWidth > 0 ? Border.all(
          color: borderColor ?? Theme.of(context).colorScheme.outline,
          width: borderWidth,
        ) : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        child: _buildAvatarContent(context),
      ),
    );
  }

  /// Build avatar content (image or initials)
  Widget _buildAvatarContent(BuildContext context) {
    if (user.avatarUrl != null && user.avatarUrl!.isNotEmpty) {
      return _buildImageAvatar(context);
    } else {
      return _buildInitialsAvatar(context);
    }
  }

  /// Build image avatar
  Widget _buildImageAvatar(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: user.avatarUrl!,
      width: size,
      height: size,
      fit: BoxFit.cover,
      placeholder: showPlaceholder ? (context, url) => 
          placeholder ?? _buildLoadingPlaceholder(context) : null,
      errorWidget: (context, url, error) => _buildInitialsAvatar(context),
    );
  }

  /// Build initials avatar
  Widget _buildInitialsAvatar(BuildContext context) {
    final initials = _getInitials();
    final effectiveFontSize = fontSize ?? size * 0.4;
    final effectiveBackgroundColor = backgroundColor ?? 
        _generateColorFromName(user.displayName);
    final effectiveTextColor = textColor ?? 
        _getContrastingTextColor(effectiveBackgroundColor);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(borderRadius ?? size / 2),
      ),
      child: Center(
        child: Text(
          initials,
          style: TextStyle(
            fontSize: effectiveFontSize,
            fontWeight: FontWeight.w600,
            color: effectiveTextColor,
          ),
        ),
      ),
    );
  }

  /// Build loading placeholder
  Widget _buildLoadingPlaceholder(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(borderRadius ?? size / 2),
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.3,
          height: size * 0.3,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ),
    );
  }

  /// Build avatar with online status indicator
  Widget _buildWithOnlineStatus(Widget avatar, BuildContext context) {
    return Stack(
      children: [
        avatar,
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: size * 0.25,
            height: size * 0.25,
            decoration: BoxDecoration(
              color: isOnline ? Colors.green : Colors.grey,
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context).colorScheme.surface,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build avatar with level badge
  Widget _buildWithLevelBadge(Widget avatar, BuildContext context) {
    return Stack(
      children: [
        avatar,
        Positioned(
          top: -2,
          right: -2,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.surface,
                width: 1,
              ),
            ),
            child: Text(
              '${user.level ?? 1}',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimary,
                fontSize: size * 0.2,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Get user initials
  String _getInitials() {
    final name = user.displayName.trim();
    if (name.isEmpty) return '?';
    
    final parts = name.split(' ');
    if (parts.length == 1) {
      return parts[0].substring(0, 1).toUpperCase();
    } else {
      return '${parts[0].substring(0, 1)}${parts[1].substring(0, 1)}'.toUpperCase();
    }
  }

  /// Generate color from name
  Color _generateColorFromName(String name) {
    final colors = [
      const Color(0xFF1976D2), // Blue
      const Color(0xFF388E3C), // Green
      const Color(0xFFF57C00), // Orange
      const Color(0xFF7B1FA2), // Purple
      const Color(0xFFD32F2F), // Red
      const Color(0xFF0097A7), // Cyan
      const Color(0xFF5D4037), // Brown
      const Color(0xFF455A64), // Blue Grey
      const Color(0xFF512DA8), // Deep Purple
      const Color(0xFF00796B), // Teal
    ];
    
    final hash = name.hashCode;
    return colors[hash.abs() % colors.length];
  }

  /// Get contrasting text color
  Color _getContrastingTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }
}

/// A group of user avatars displayed in a stack
class UserAvatarGroup extends StatelessWidget {
  /// List of users to display
  final List<User> users;
  
  /// Size of each avatar
  final double avatarSize;
  
  /// Maximum number of avatars to show
  final int maxAvatars;
  
  /// Overlap amount between avatars
  final double overlap;
  
  /// Whether to show count of remaining users
  final bool showRemainingCount;
  
  /// Callback when avatar group is tapped
  final VoidCallback? onTap;

  const UserAvatarGroup({
    super.key,
    required this.users,
    this.avatarSize = 32,
    this.maxAvatars = 4,
    this.overlap = 8,
    this.showRemainingCount = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final displayUsers = users.take(maxAvatars).toList();
    final remainingCount = users.length - displayUsers.length;
    
    Widget avatarGroup = SizedBox(
      width: _calculateGroupWidth(),
      height: avatarSize,
      child: Stack(
        children: [
          ...displayUsers.asMap().entries.map((entry) {
            final index = entry.key;
            final user = entry.value;
            
            return Positioned(
              left: index * (avatarSize - overlap),
              child: UserAvatar(
                user: user,
                size: avatarSize,
                borderWidth: 2,
                borderColor: Theme.of(context).colorScheme.surface,
              ),
            );
          }),
          if (showRemainingCount && remainingCount > 0)
            Positioned(
              left: displayUsers.length * (avatarSize - overlap),
              child: _buildRemainingCountAvatar(context, remainingCount),
            ),
        ],
      ),
    );
    
    if (onTap != null) {
      avatarGroup = GestureDetector(
        onTap: onTap,
        child: avatarGroup,
      );
    }
    
    return avatarGroup;
  }

  /// Calculate total width of the avatar group
  double _calculateGroupWidth() {
    final visibleAvatars = users.length > maxAvatars ? maxAvatars : users.length;
    final remainingCountWidth = showRemainingCount && users.length > maxAvatars ? avatarSize : 0;
    
    if (visibleAvatars <= 1) {
      return avatarSize + remainingCountWidth;
    }
    
    return (visibleAvatars - 1) * (avatarSize - overlap) + avatarSize + remainingCountWidth;
  }

  /// Build remaining count avatar
  Widget _buildRemainingCountAvatar(BuildContext context, int count) {
    return Container(
      width: avatarSize,
      height: avatarSize,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        shape: BoxShape.circle,
        border: Border.all(
          color: Theme.of(context).colorScheme.surface,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          '+$count',
          style: TextStyle(
            fontSize: avatarSize * 0.3,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }
}

/// A user avatar with additional information
class UserAvatarWithInfo extends StatelessWidget {
  /// User data to display
  final User user;
  
  /// Size of the avatar
  final double avatarSize;
  
  /// Whether to show user name
  final bool showName;
  
  /// Whether to show user email
  final bool showEmail;
  
  /// Whether to show user level
  final bool showLevel;
  
  /// Whether to show online status
  final bool showOnlineStatus;
  
  /// Online status
  final bool isOnline;
  
  /// Layout direction
  final Axis direction;
  
  /// Spacing between avatar and info
  final double spacing;
  
  /// Callback when tapped
  final VoidCallback? onTap;

  const UserAvatarWithInfo({
    super.key,
    required this.user,
    this.avatarSize = 48,
    this.showName = true,
    this.showEmail = false,
    this.showLevel = false,
    this.showOnlineStatus = false,
    this.isOnline = false,
    this.direction = Axis.horizontal,
    this.spacing = 12,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final avatar = UserAvatar(
      user: user,
      size: avatarSize,
      showOnlineStatus: showOnlineStatus,
      isOnline: isOnline,
      showLevelBadge: showLevel,
    );

    final info = _buildUserInfo(context);

    Widget content;
    if (direction == Axis.horizontal) {
      content = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          avatar,
          SizedBox(width: spacing),
          Expanded(child: info),
        ],
      );
    } else {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          avatar,
          SizedBox(height: spacing),
          info,
        ],
      );
    }

    if (onTap != null) {
      content = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          child: content,
        ),
      );
    }

    return content;
  }

  /// Build user information
  Widget _buildUserInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: direction == Axis.horizontal 
          ? CrossAxisAlignment.start 
          : CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showName)
          Text(
            user.displayName,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        if (showEmail && user.email.isNotEmpty) ...[
          const SizedBox(height: 2),
          Text(
            user.email,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        if (showLevel) ...[
          const SizedBox(height: 2),
          Text(
            'Level ${user.level}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }
}
