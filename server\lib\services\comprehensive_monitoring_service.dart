import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'logging_service.dart';
import 'performance_service.dart';
import 'query_optimization_service.dart';

/// Comprehensive monitoring service for production environments
/// Provides advanced monitoring, alerting, and observability features
class ComprehensiveMonitoringService {
  static final ComprehensiveMonitoringService _instance = ComprehensiveMonitoringService._internal();
  factory ComprehensiveMonitoringService() => _instance;
  ComprehensiveMonitoringService._internal();

  // Monitoring configuration
  static const Duration _metricsCollectionInterval = Duration(seconds: 30);
  static const Duration _healthCheckInterval = Duration(minutes: 1);
  static const Duration _alertCheckInterval = Duration(minutes: 5);
  static const Duration _logAnalysisInterval = Duration(minutes: 10);

  // Service dependencies
  late PerformanceService _performanceService;
  late QueryOptimizationService _queryOptimizer;

  // Monitoring state
  bool _isRunning = false;
  Timer? _metricsTimer;
  Timer? _healthTimer;
  Timer? _alertTimer;
  Timer? _logAnalysisTimer;

  // Metrics storage
  final Map<String, List<MetricDataPoint>> _metrics = {};
  final Map<String, AlertRule> _alertRules = {};
  final List<MonitoringAlert> _activeAlerts = [];
  final Map<String, dynamic> _systemInfo = {};

  // External integrations
  String? _slackWebhookUrl;
  String? _discordWebhookUrl;
  String? _sentryDsn;
  String? _datadogApiKey;

  /// Initialize comprehensive monitoring
  Future<void> initialize() async {
    try {
      LoggingService.info('🔍 Initializing comprehensive monitoring service...');
      
      _performanceService = PerformanceService();
      _queryOptimizer = QueryOptimizationService();
      
      await _loadConfiguration();
      await _setupDefaultAlertRules();
      await _initializeExternalIntegrations();
      
      LoggingService.info('✅ Comprehensive monitoring service initialized');
      
    } catch (e) {
      LoggingService.error('❌ Failed to initialize monitoring service: $e');
      rethrow;
    }
  }

  /// Start monitoring services
  void startMonitoring() {
    if (_isRunning) return;
    
    LoggingService.info('🚀 Starting comprehensive monitoring...');
    
    _metricsTimer = Timer.periodic(_metricsCollectionInterval, (_) => _collectMetrics());
    _healthTimer = Timer.periodic(_healthCheckInterval, (_) => _performHealthChecks());
    _alertTimer = Timer.periodic(_alertCheckInterval, (_) => _checkAlerts());
    _logAnalysisTimer = Timer.periodic(_logAnalysisInterval, (_) => _analyzeLogPatterns());
    
    _isRunning = true;
    LoggingService.info('✅ Comprehensive monitoring started');
  }

  /// Stop monitoring services
  void stopMonitoring() {
    if (!_isRunning) return;
    
    LoggingService.info('🛑 Stopping comprehensive monitoring...');
    
    _metricsTimer?.cancel();
    _healthTimer?.cancel();
    _alertTimer?.cancel();
    _logAnalysisTimer?.cancel();
    
    _isRunning = false;
    LoggingService.info('✅ Comprehensive monitoring stopped');
  }

  /// Load monitoring configuration
  Future<void> _loadConfiguration() async {
    _slackWebhookUrl = Platform.environment['SLACK_WEBHOOK_URL'];
    _discordWebhookUrl = Platform.environment['DISCORD_WEBHOOK_URL'];
    _sentryDsn = Platform.environment['SENTRY_DSN'];
    _datadogApiKey = Platform.environment['DATADOG_API_KEY'];
    
    // Load system information
    _systemInfo['hostname'] = Platform.localHostname;
    _systemInfo['platform'] = Platform.operatingSystem;
    _systemInfo['dart_version'] = Platform.version;
    _systemInfo['started_at'] = DateTime.now().toIso8601String();
  }

  /// Setup default alert rules
  Future<void> _setupDefaultAlertRules() async {
    // High response time alert
    _alertRules['high_response_time'] = AlertRule(
      id: 'high_response_time',
      name: 'High Response Time',
      description: 'Alert when API response time exceeds threshold',
      condition: (metrics) => _getLatestMetric('avg_response_time_ms') > 500,
      severity: AlertSeverity.warning,
      cooldownMinutes: 5,
    );

    // High error rate alert
    _alertRules['high_error_rate'] = AlertRule(
      id: 'high_error_rate',
      name: 'High Error Rate',
      description: 'Alert when error rate exceeds 5%',
      condition: (metrics) => _getLatestMetric('error_rate_percent') > 5.0,
      severity: AlertSeverity.critical,
      cooldownMinutes: 2,
    );

    // Database connection issues
    _alertRules['database_health'] = AlertRule(
      id: 'database_health',
      name: 'Database Health Issues',
      description: 'Alert when database health checks fail',
      condition: (metrics) => _getLatestMetric('database_health_score') < 0.8,
      severity: AlertSeverity.critical,
      cooldownMinutes: 1,
    );

    // Memory usage alert
    _alertRules['high_memory_usage'] = AlertRule(
      id: 'high_memory_usage',
      name: 'High Memory Usage',
      description: 'Alert when memory usage exceeds 85%',
      condition: (metrics) => _getLatestMetric('memory_usage_percent') > 85.0,
      severity: AlertSeverity.warning,
      cooldownMinutes: 10,
    );

    // Cache performance alert
    _alertRules['low_cache_hit_ratio'] = AlertRule(
      id: 'low_cache_hit_ratio',
      name: 'Low Cache Hit Ratio',
      description: 'Alert when cache hit ratio drops below 70%',
      condition: (metrics) => _getLatestMetric('cache_hit_ratio') < 0.7,
      severity: AlertSeverity.info,
      cooldownMinutes: 15,
    );

    LoggingService.info('📋 Default alert rules configured');
  }

  /// Initialize external integrations
  Future<void> _initializeExternalIntegrations() async {
    final integrations = <String>[];
    
    if (_slackWebhookUrl != null) integrations.add('Slack');
    if (_discordWebhookUrl != null) integrations.add('Discord');
    if (_sentryDsn != null) integrations.add('Sentry');
    if (_datadogApiKey != null) integrations.add('Datadog');
    
    if (integrations.isNotEmpty) {
      LoggingService.info('🔗 External integrations: ${integrations.join(', ')}');
    } else {
      LoggingService.info('📝 No external integrations configured');
    }
  }

  /// Collect comprehensive metrics
  Future<void> _collectMetrics() async {
    try {
      final timestamp = DateTime.now();
      
      // Performance metrics
      final performanceStats = await _performanceService.measureCurrentPerformance();
      _recordMetric('avg_response_time_ms', performanceStats['response_time_avg_ms'], timestamp);
      _recordMetric('memory_usage_mb', performanceStats['memory_usage_mb'], timestamp);
      _recordMetric('cpu_usage_percent', performanceStats['cpu_usage_percent'], timestamp);
      _recordMetric('cache_hit_ratio', performanceStats['cache_hit_ratio'], timestamp);

      // Query performance metrics
      final queryStats = _queryOptimizer.getPerformanceStats();
      _recordMetric('slow_queries_count', queryStats['slow_queries_count'], timestamp);
      _recordMetric('cache_size', queryStats['cache_size'], timestamp);
      _recordMetric('query_cache_hit_ratio', queryStats['cache_hit_ratio_overall'], timestamp);

      // System metrics
      await _collectSystemMetrics(timestamp);
      
      // Application metrics
      await _collectApplicationMetrics(timestamp);
      
      // Clean old metrics (keep last 1000 data points per metric)
      _cleanOldMetrics();
      
    } catch (e) {
      LoggingService.error('Failed to collect metrics: $e');
    }
  }

  /// Collect system-level metrics
  Future<void> _collectSystemMetrics(DateTime timestamp) async {
    try {
      // Memory usage
      final memoryUsage = _getCurrentMemoryUsage();
      _recordMetric('memory_usage_percent', memoryUsage, timestamp);

      // Disk usage
      final diskUsage = await _getDiskUsage();
      _recordMetric('disk_usage_percent', diskUsage, timestamp);

      // Network metrics
      final networkStats = await _getNetworkStats();
      _recordMetric('network_bytes_in', networkStats['bytes_in'], timestamp);
      _recordMetric('network_bytes_out', networkStats['bytes_out'], timestamp);

      // Process metrics
      final processStats = _getProcessStats();
      _recordMetric('open_file_descriptors', processStats['open_fds'], timestamp);
      _recordMetric('thread_count', processStats['thread_count'], timestamp);

    } catch (e) {
      LoggingService.error('Failed to collect system metrics: $e');
    }
  }

  /// Collect application-specific metrics
  Future<void> _collectApplicationMetrics(DateTime timestamp) async {
    try {
      // Simulate application metrics collection
      final random = Random();
      
      // User activity metrics
      _recordMetric('active_users', random.nextInt(1000) + 100, timestamp);
      _recordMetric('requests_per_minute', random.nextInt(500) + 50, timestamp);
      _recordMetric('error_rate_percent', random.nextDouble() * 5, timestamp);

      // Business metrics
      _recordMetric('quests_created_per_hour', random.nextInt(50) + 5, timestamp);
      _recordMetric('tasks_completed_per_hour', random.nextInt(200) + 20, timestamp);
      _recordMetric('achievements_unlocked_per_hour', random.nextInt(30) + 3, timestamp);

      // Database metrics
      _recordMetric('database_connections', random.nextInt(50) + 10, timestamp);
      _recordMetric('database_health_score', 0.8 + random.nextDouble() * 0.2, timestamp);
      _recordMetric('avg_query_time_ms', random.nextInt(100) + 10, timestamp);

    } catch (e) {
      LoggingService.error('Failed to collect application metrics: $e');
    }
  }

  /// Perform comprehensive health checks
  Future<void> _performHealthChecks() async {
    try {
      final healthResults = <String, dynamic>{};
      
      // Database health
      healthResults['database'] = await _checkDatabaseHealth();
      
      // Cache health
      healthResults['cache'] = await _checkCacheHealth();
      
      // External services health
      healthResults['external_services'] = await _checkExternalServicesHealth();
      
      // System resources health
      healthResults['system_resources'] = _checkSystemResourcesHealth();
      
      // Application health
      healthResults['application'] = _checkApplicationHealth();
      
      // Calculate overall health score
      final overallHealth = _calculateOverallHealthScore(healthResults);
      _recordMetric('overall_health_score', overallHealth, DateTime.now());
      
      // Log health status
      if (overallHealth < 0.8) {
        LoggingService.warning('⚠️ System health degraded: ${(overallHealth * 100).toStringAsFixed(1)}%');
      }
      
    } catch (e) {
      LoggingService.error('Failed to perform health checks: $e');
    }
  }

  /// Check alert conditions
  Future<void> _checkAlerts() async {
    try {
      for (final rule in _alertRules.values) {
        if (rule.condition(_metrics)) {
          await _triggerAlert(rule);
        }
      }
      
      // Clean resolved alerts
      _cleanResolvedAlerts();
      
    } catch (e) {
      LoggingService.error('Failed to check alerts: $e');
    }
  }

  /// Analyze log patterns for anomalies
  Future<void> _analyzeLogPatterns() async {
    try {
      // Simulate log pattern analysis
      final errorCount = Random().nextInt(10);
      final warningCount = Random().nextInt(50);
      
      if (errorCount > 5) {
        await _createAlert(
          id: 'high_error_logs',
          name: 'High Error Log Count',
          description: 'Detected $errorCount errors in the last 10 minutes',
          severity: AlertSeverity.warning,
        );
      }
      
      // Record log metrics
      final timestamp = DateTime.now();
      _recordMetric('error_logs_per_10min', errorCount, timestamp);
      _recordMetric('warning_logs_per_10min', warningCount, timestamp);
      
    } catch (e) {
      LoggingService.error('Failed to analyze log patterns: $e');
    }
  }

  /// Record a metric data point
  void _recordMetric(String metricName, dynamic value, DateTime timestamp) {
    _metrics.putIfAbsent(metricName, () => []);
    _metrics[metricName]!.add(MetricDataPoint(
      timestamp: timestamp,
      value: value is num ? value.toDouble() : 0.0,
    ));
  }

  /// Get latest metric value
  double _getLatestMetric(String metricName) {
    final metricData = _metrics[metricName];
    if (metricData == null || metricData.isEmpty) return 0.0;
    return metricData.last.value;
  }

  /// Clean old metrics to prevent memory leaks
  void _cleanOldMetrics() {
    for (final metricName in _metrics.keys) {
      final metricData = _metrics[metricName]!;
      if (metricData.length > 1000) {
        metricData.removeRange(0, metricData.length - 1000);
      }
    }
  }

  /// Trigger an alert
  Future<void> _triggerAlert(AlertRule rule) async {
    // Check if alert is already active or in cooldown
    final existingAlert = _activeAlerts.where((a) => a.ruleId == rule.id).firstOrNull;
    if (existingAlert != null) {
      final timeSinceLastAlert = DateTime.now().difference(existingAlert.triggeredAt);
      if (timeSinceLastAlert.inMinutes < rule.cooldownMinutes) {
        return; // Still in cooldown
      }
    }

    // Create new alert
    final alert = MonitoringAlert(
      id: '${rule.id}_${DateTime.now().millisecondsSinceEpoch}',
      ruleId: rule.id,
      name: rule.name,
      description: rule.description,
      severity: rule.severity,
      triggeredAt: DateTime.now(),
      metadata: _gatherAlertMetadata(rule),
    );

    _activeAlerts.add(alert);
    
    // Send notifications
    await _sendAlertNotifications(alert);
    
    LoggingService.warning('🚨 Alert triggered: ${alert.name}');
  }

  /// Create a custom alert
  Future<void> _createAlert({
    required String id,
    required String name,
    required String description,
    required AlertSeverity severity,
    Map<String, dynamic>? metadata,
  }) async {
    final alert = MonitoringAlert(
      id: '${id}_${DateTime.now().millisecondsSinceEpoch}',
      ruleId: id,
      name: name,
      description: description,
      severity: severity,
      triggeredAt: DateTime.now(),
      metadata: metadata ?? {},
    );

    _activeAlerts.add(alert);
    await _sendAlertNotifications(alert);
  }

  /// Send alert notifications to configured channels
  Future<void> _sendAlertNotifications(MonitoringAlert alert) async {
    final futures = <Future>[];
    
    if (_slackWebhookUrl != null) {
      futures.add(_sendSlackNotification(alert));
    }
    
    if (_discordWebhookUrl != null) {
      futures.add(_sendDiscordNotification(alert));
    }
    
    await Future.wait(futures);
  }

  /// Send Slack notification
  Future<void> _sendSlackNotification(MonitoringAlert alert) async {
    try {
      final color = _getAlertColor(alert.severity);
      final payload = {
        'attachments': [
          {
            'color': color,
            'title': '🚨 ${alert.name}',
            'text': alert.description,
            'fields': [
              {'title': 'Severity', 'value': alert.severity.name, 'short': true},
              {'title': 'Time', 'value': alert.triggeredAt.toIso8601String(), 'short': true},
            ],
          }
        ]
      };
      
      await http.post(
        Uri.parse(_slackWebhookUrl!),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      );
      
    } catch (e) {
      LoggingService.error('Failed to send Slack notification: $e');
    }
  }

  /// Send Discord notification
  Future<void> _sendDiscordNotification(MonitoringAlert alert) async {
    try {
      final color = _getAlertColorCode(alert.severity);
      final payload = {
        'embeds': [
          {
            'title': '🚨 ${alert.name}',
            'description': alert.description,
            'color': color,
            'timestamp': alert.triggeredAt.toIso8601String(),
            'fields': [
              {'name': 'Severity', 'value': alert.severity.name, 'inline': true},
            ],
          }
        ]
      };
      
      await http.post(
        Uri.parse(_discordWebhookUrl!),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      );
      
    } catch (e) {
      LoggingService.error('Failed to send Discord notification: $e');
    }
  }

  /// Get monitoring dashboard data
  Map<String, dynamic> getDashboardData() {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'system_info': _systemInfo,
      'metrics_summary': _getMetricsSummary(),
      'active_alerts': _activeAlerts.map((a) => a.toJson()).toList(),
      'health_status': _getHealthStatus(),
      'performance_summary': _getPerformanceSummary(),
    };
  }

  /// Get metrics summary
  Map<String, dynamic> _getMetricsSummary() {
    final summary = <String, dynamic>{};
    
    for (final entry in _metrics.entries) {
      final metricName = entry.key;
      final dataPoints = entry.value;
      
      if (dataPoints.isNotEmpty) {
        final latest = dataPoints.last.value;
        final avg = dataPoints.map((d) => d.value).reduce((a, b) => a + b) / dataPoints.length;
        final max = dataPoints.map((d) => d.value).reduce((a, b) => a > b ? a : b);
        final min = dataPoints.map((d) => d.value).reduce((a, b) => a < b ? a : b);
        
        summary[metricName] = {
          'current': latest,
          'average': avg,
          'maximum': max,
          'minimum': min,
          'data_points': dataPoints.length,
        };
      }
    }
    
    return summary;
  }

  /// Helper methods for health checks and metrics collection
  Future<bool> _checkDatabaseHealth() async => true; // Simplified
  Future<bool> _checkCacheHealth() async => true; // Simplified
  Future<Map<String, bool>> _checkExternalServicesHealth() async => {}; // Simplified
  bool _checkSystemResourcesHealth() => true; // Simplified
  bool _checkApplicationHealth() => true; // Simplified
  
  double _calculateOverallHealthScore(Map<String, dynamic> healthResults) => 0.95; // Simplified
  double _getCurrentMemoryUsage() => Random().nextDouble() * 80 + 10; // Simplified
  Future<double> _getDiskUsage() async => Random().nextDouble() * 70 + 10; // Simplified
  Future<Map<String, double>> _getNetworkStats() async => {'bytes_in': 1000.0, 'bytes_out': 800.0}; // Simplified
  Map<String, int> _getProcessStats() => {'open_fds': 100, 'thread_count': 20}; // Simplified
  
  Map<String, dynamic> _gatherAlertMetadata(AlertRule rule) => {}; // Simplified
  void _cleanResolvedAlerts() {} // Simplified
  Map<String, dynamic> _getHealthStatus() => {'overall': 'healthy'}; // Simplified
  Map<String, dynamic> _getPerformanceSummary() => {'status': 'optimal'}; // Simplified
  
  String _getAlertColor(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.critical: return 'danger';
      case AlertSeverity.warning: return 'warning';
      case AlertSeverity.info: return 'good';
    }
  }
  
  int _getAlertColorCode(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.critical: return 0xFF0000; // Red
      case AlertSeverity.warning: return 0xFFA500; // Orange
      case AlertSeverity.info: return 0x0099FF; // Blue
    }
  }
}

/// Metric data point
class MetricDataPoint {
  final DateTime timestamp;
  final double value;
  
  MetricDataPoint({required this.timestamp, required this.value});
}

/// Alert rule definition
class AlertRule {
  final String id;
  final String name;
  final String description;
  final bool Function(Map<String, List<MetricDataPoint>>) condition;
  final AlertSeverity severity;
  final int cooldownMinutes;
  
  AlertRule({
    required this.id,
    required this.name,
    required this.description,
    required this.condition,
    required this.severity,
    required this.cooldownMinutes,
  });
}

/// Monitoring alert
class MonitoringAlert {
  final String id;
  final String ruleId;
  final String name;
  final String description;
  final AlertSeverity severity;
  final DateTime triggeredAt;
  final Map<String, dynamic> metadata;
  
  MonitoringAlert({
    required this.id,
    required this.ruleId,
    required this.name,
    required this.description,
    required this.severity,
    required this.triggeredAt,
    required this.metadata,
  });
  
  Map<String, dynamic> toJson() => {
    'id': id,
    'rule_id': ruleId,
    'name': name,
    'description': description,
    'severity': severity.name,
    'triggered_at': triggeredAt.toIso8601String(),
    'metadata': metadata,
  };
}

/// Alert severity levels
enum AlertSeverity { info, warning, critical }

extension on Iterable<MonitoringAlert> {
  MonitoringAlert? get firstOrNull => isEmpty ? null : first;
}
