import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared/shared.dart';
import 'package:client/presentation/blocs/quest/quest_bloc.dart';
import 'package:client/data/repositories/api_repository.dart';

class MockApiRepository extends Mock implements ApiRepository {}

void main() {
  group('QuestBloc', () {
    late QuestBloc questBloc;
    late MockApiRepository mockRepository;

    setUp(() {
      mockRepository = MockApiRepository();
      questBloc = QuestBloc(repository: mockRepository);
    });

    tearDown(() {
      questBloc.close();
    });

    test('initial state is QuestInitial', () {
      expect(questBloc.state, equals(const QuestInitial()));
    });

    group('LoadQuests', () {
      final quests = [
        Quest(
          id: '1',
          title: 'Test Quest 1',
          description: 'Description 1',
          createdById: 'user1',
          assignedToId: 'user1',
          status: QuestStatus.active,
          priority: QuestPriority.high,
          difficulty: QuestDifficulty.medium,
          category: QuestCategory.personal,
          basePoints: 100,
          bonusPoints: 20,
          totalPoints: 120,
          earnedPoints: 0,
          progressPercentage: 0.0,
          taskIds: [],
          participantIds: ['user1'],
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Quest(
          id: '2',
          title: 'Test Quest 2',
          description: 'Description 2',
          createdById: 'user1',
          assignedToId: 'user2',
          status: QuestStatus.completed,
          priority: QuestPriority.medium,
          difficulty: QuestDifficulty.easy,
          category: QuestCategory.work,
          basePoints: 200,
          bonusPoints: 40,
          totalPoints: 240,
          earnedPoints: 240,
          progressPercentage: 100.0,
          taskIds: [],
          participantIds: ['user1', 'user2'],
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestsLoaded] when loading quests succeeds',
        build: () {
          when(() => mockRepository.getQuests(
            status: any(named: 'status'),
            priority: any(named: 'priority'),
            type: any(named: 'type'),
            page: any(named: 'page'),
            limit: any(named: 'limit'),
          )).thenAnswer((_) async => quests);
          return questBloc;
        },
        act: (bloc) => bloc.add(const LoadQuests()),
        expect: () => [
          const QuestLoading(),
          QuestsLoaded(quests: quests),
        ],
        verify: (_) {
          verify(() => mockRepository.getQuests(
            status: null,
            priority: null,
            type: null,
            page: 1,
            limit: 20,
          )).called(1);
        },
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestError] when loading quests fails',
        build: () {
          when(() => mockRepository.getQuests(
            status: any(named: 'status'),
            priority: any(named: 'priority'),
            type: any(named: 'type'),
            page: any(named: 'page'),
            limit: any(named: 'limit'),
          )).thenThrow(Exception('Failed to load quests'));
          return questBloc;
        },
        act: (bloc) => bloc.add(const LoadQuests()),
        expect: () => [
          const QuestLoading(),
          const QuestError(message: 'Exception: Failed to load quests'),
        ],
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestsLoaded] with filtered quests when status filter is applied',
        build: () {
          final activeQuests = quests.where((q) => q.status == QuestStatus.active).toList();
          when(() => mockRepository.getQuests(
            status: QuestStatus.active,
            priority: any(named: 'priority'),
            type: any(named: 'type'),
            page: any(named: 'page'),
            limit: any(named: 'limit'),
          )).thenAnswer((_) async => activeQuests);
          return questBloc;
        },
        act: (bloc) => bloc.add(const LoadQuests(status: QuestStatus.active)),
        expect: () => [
          const QuestLoading(),
          QuestsLoaded(quests: [quests[0]]), // Only active quest
        ],
      );
    });

    group('LoadQuestDetails', () {
      final quest = Quest(
        id: '1',
        title: 'Test Quest',
        description: 'Test Description',
        status: QuestStatus.active,
        priority: QuestPriority.high,
        type: QuestType.personal,
        createdBy: 'user1',
        assignedTo: ['user1'],
        tasks: [
          Task(
            id: 'task1',
            title: 'Test Task',
            description: 'Task Description',
            status: TaskStatus.pending,
            priority: TaskPriority.medium,
            assignedTo: 'user1',
            createdBy: 'user1',
            questId: '1',
            pointsReward: 50,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ],
        pointsReward: 100,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestDetailsLoaded] when loading quest details succeeds',
        build: () {
          when(() => mockRepository.getQuestById('1'))
              .thenAnswer((_) async => quest);
          return questBloc;
        },
        act: (bloc) => bloc.add(const LoadQuestDetails(questId: '1')),
        expect: () => [
          const QuestLoading(),
          QuestDetailsLoaded(quest: quest),
        ],
        verify: (_) {
          verify(() => mockRepository.getQuestById('1')).called(1);
        },
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestError] when loading quest details fails',
        build: () {
          when(() => mockRepository.getQuestById('1'))
              .thenThrow(Exception('Quest not found'));
          return questBloc;
        },
        act: (bloc) => bloc.add(const LoadQuestDetails(questId: '1')),
        expect: () => [
          const QuestLoading(),
          const QuestError(message: 'Exception: Quest not found'),
        ],
      );
    });

    group('CreateQuest', () {
      const questData = CreateQuestRequest(
        title: 'New Quest',
        description: 'New Description',
        priority: QuestPriority.high,
        type: QuestType.personal,
        assignedTo: ['user1'],
        pointsReward: 100,
      );

      final createdQuest = Quest(
        id: '1',
        title: 'New Quest',
        description: 'New Description',
        status: QuestStatus.active,
        priority: QuestPriority.high,
        type: QuestType.personal,
        createdBy: 'user1',
        assignedTo: ['user1'],
        tasks: [],
        pointsReward: 100,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestCreated, QuestLoading, QuestsLoaded] when creating quest succeeds',
        build: () {
          when(() => mockRepository.createQuest(questData))
              .thenAnswer((_) async => createdQuest);
          when(() => mockRepository.getQuests(
            status: any(named: 'status'),
            priority: any(named: 'priority'),
            type: any(named: 'type'),
            page: any(named: 'page'),
            limit: any(named: 'limit'),
          )).thenAnswer((_) async => [createdQuest]);
          return questBloc;
        },
        act: (bloc) => bloc.add(const CreateQuest(questData: questData)),
        expect: () => [
          const QuestLoading(),
          QuestCreated(quest: createdQuest),
          const QuestLoading(),
          QuestsLoaded(quests: [createdQuest]),
        ],
        verify: (_) {
          verify(() => mockRepository.createQuest(questData)).called(1);
          verify(() => mockRepository.getQuests(
            status: null,
            priority: null,
            type: null,
            page: 1,
            limit: 20,
          )).called(1);
        },
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestError] when creating quest fails',
        build: () {
          when(() => mockRepository.createQuest(questData))
              .thenThrow(Exception('Failed to create quest'));
          return questBloc;
        },
        act: (bloc) => bloc.add(const CreateQuest(questData: questData)),
        expect: () => [
          const QuestLoading(),
          const QuestError(message: 'Exception: Failed to create quest'),
        ],
      );
    });

    group('UpdateQuestStatus', () {
      blocTest<QuestBloc, QuestState>(
        'emits [QuestStatusUpdated, QuestLoading, QuestsLoaded] when updating quest status succeeds',
        build: () {
          when(() => mockRepository.updateQuestStatus('1', QuestStatus.completed))
              .thenAnswer((_) async => {});
          when(() => mockRepository.getQuests(
            status: any(named: 'status'),
            priority: any(named: 'priority'),
            type: any(named: 'type'),
            page: any(named: 'page'),
            limit: any(named: 'limit'),
          )).thenAnswer((_) async => []);
          return questBloc;
        },
        act: (bloc) => bloc.add(const UpdateQuestStatus(
          questId: '1',
          status: QuestStatus.completed,
        )),
        expect: () => [
          const QuestStatusUpdated(questId: '1', status: QuestStatus.completed),
          const QuestLoading(),
          const QuestsLoaded(quests: []),
        ],
        verify: (_) {
          verify(() => mockRepository.updateQuestStatus('1', QuestStatus.completed)).called(1);
          verify(() => mockRepository.getQuests(
            status: null,
            priority: null,
            type: null,
            page: 1,
            limit: 20,
          )).called(1);
        },
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestError] when updating quest status fails',
        build: () {
          when(() => mockRepository.updateQuestStatus('1', QuestStatus.completed))
              .thenThrow(Exception('Failed to update status'));
          return questBloc;
        },
        act: (bloc) => bloc.add(const UpdateQuestStatus(
          questId: '1',
          status: QuestStatus.completed,
        )),
        expect: () => [
          const QuestError(message: 'Exception: Failed to update status'),
        ],
      );
    });

    group('UpdateQuestPriority', () {
      blocTest<QuestBloc, QuestState>(
        'emits [QuestPriorityUpdated, QuestLoading, QuestsLoaded] when updating quest priority succeeds',
        build: () {
          when(() => mockRepository.updateQuestPriority('1', QuestPriority.urgent))
              .thenAnswer((_) async => {});
          when(() => mockRepository.getQuests(
            status: any(named: 'status'),
            priority: any(named: 'priority'),
            type: any(named: 'type'),
            page: any(named: 'page'),
            limit: any(named: 'limit'),
          )).thenAnswer((_) async => []);
          return questBloc;
        },
        act: (bloc) => bloc.add(const UpdateQuestPriority(
          questId: '1',
          priority: QuestPriority.urgent,
        )),
        expect: () => [
          const QuestPriorityUpdated(questId: '1', priority: QuestPriority.urgent),
          const QuestLoading(),
          const QuestsLoaded(quests: []),
        ],
        verify: (_) {
          verify(() => mockRepository.updateQuestPriority('1', QuestPriority.urgent)).called(1);
        },
      );
    });

    group('DeleteQuest', () {
      blocTest<QuestBloc, QuestState>(
        'emits [QuestDeleted, QuestLoading, QuestsLoaded] when deleting quest succeeds',
        build: () {
          when(() => mockRepository.deleteQuest('1'))
              .thenAnswer((_) async => {});
          when(() => mockRepository.getQuests(
            status: any(named: 'status'),
            priority: any(named: 'priority'),
            type: any(named: 'type'),
            page: any(named: 'page'),
            limit: any(named: 'limit'),
          )).thenAnswer((_) async => []);
          return questBloc;
        },
        act: (bloc) => bloc.add(const DeleteQuest(questId: '1')),
        expect: () => [
          const QuestDeleted(questId: '1'),
          const QuestLoading(),
          const QuestsLoaded(quests: []),
        ],
        verify: (_) {
          verify(() => mockRepository.deleteQuest('1')).called(1);
        },
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestError] when deleting quest fails',
        build: () {
          when(() => mockRepository.deleteQuest('1'))
              .thenThrow(Exception('Failed to delete quest'));
          return questBloc;
        },
        act: (bloc) => bloc.add(const DeleteQuest(questId: '1')),
        expect: () => [
          const QuestError(message: 'Exception: Failed to delete quest'),
        ],
      );
    });

    group('SearchQuests', () {
      final searchResults = [
        Quest(
          id: '1',
          title: 'Searchable Quest',
          description: 'This quest matches the search',
          createdById: 'user1',
          assignedToId: 'user1',
          status: QuestStatus.active,
          priority: QuestPriority.medium,
          difficulty: QuestDifficulty.beginner,
          category: QuestCategory.personal,
          basePoints: 100,
          bonusPoints: 20,
          totalPoints: 120,
          earnedPoints: 0,
          progressPercentage: 0.0,
          taskIds: [],
          participantIds: ['user1'],
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestsLoaded] when searching quests succeeds',
        build: () {
          when(() => mockRepository.searchQuests(
            query: 'search term',
            filters: any(named: 'filters'),
          )).thenAnswer((_) async => searchResults);
          return questBloc;
        },
        act: (bloc) => bloc.add(const SearchQuests(
          query: 'search term',
          filters: {'type': 'personal'},
        )),
        expect: () => [
          const QuestLoading(),
          QuestsLoaded(quests: searchResults),
        ],
        verify: (_) {
          verify(() => mockRepository.searchQuests(
            query: 'search term',
            filters: {'type': 'personal'},
          )).called(1);
        },
      );

      blocTest<QuestBloc, QuestState>(
        'emits [QuestLoading, QuestError] when searching quests fails',
        build: () {
          when(() => mockRepository.searchQuests(
            query: 'search term',
            filters: any(named: 'filters'),
          )).thenThrow(Exception('Search failed'));
          return questBloc;
        },
        act: (bloc) => bloc.add(const SearchQuests(
          query: 'search term',
        )),
        expect: () => [
          const QuestLoading(),
          const QuestError(message: 'Exception: Search failed'),
        ],
      );
    });
  });
}
