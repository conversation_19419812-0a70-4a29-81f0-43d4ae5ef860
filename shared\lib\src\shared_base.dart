/// Shared base types and utilities for the Quester platform
library;

/// API response wrapper for consistent response handling
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final String? message;
  final int? statusCode;

  const ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.message,
    this.statusCode,
  });

  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
    );
  }

  factory ApiResponse.error(String error, {int? statusCode}) {
    return ApiResponse(
      success: false,
      error: error,
      statusCode: statusCode,
    );
  }

  bool get isSuccess => success;
  bool get isError => !success;
}

/// Base validation result for form validation
class ValidationResult {
  final bool isValid;
  final Map<String, String> fieldErrors;
  final List<String> generalErrors;

  const ValidationResult({
    required this.isValid,
    this.fieldErrors = const {},
    this.generalErrors = const [],
  });

  factory ValidationResult.valid() {
    return const ValidationResult(isValid: true);
  }

  factory ValidationResult.invalid({
    Map<String, String>? fieldErrors,
    List<String>? generalErrors,
  }) {
    return ValidationResult(
      isValid: false,
      fieldErrors: fieldErrors ?? {},
      generalErrors: generalErrors ?? [],
    );
  }
}

/// Quest validation result specifically for quest creation
class QuestValidationResult extends ValidationResult {
  const QuestValidationResult({
    required super.isValid,
    super.fieldErrors,
    super.generalErrors,
  });

  factory QuestValidationResult.valid() {
    return const QuestValidationResult(isValid: true);
  }

  factory QuestValidationResult.invalid({
    Map<String, String>? fieldErrors,
    List<String>? generalErrors,
  }) {
    return QuestValidationResult(
      isValid: false,
      fieldErrors: fieldErrors ?? {},
      generalErrors: generalErrors ?? [],
    );
  }
}

/// Common pagination parameters
class PaginationParams {
  final int page;
  final int limit;
  final String? sortBy;
  final String? sortOrder;

  const PaginationParams({
    this.page = 1,
    this.limit = 20,
    this.sortBy,
    this.sortOrder = 'asc',
  });

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      if (sortBy != null) 'sortBy': sortBy,
      if (sortOrder != null) 'sortOrder': sortOrder,
    };
  }
}

/// Paginated response wrapper
class PaginatedResponse<T> {
  final List<T> items;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginatedResponse({
    required this.items,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final itemsList = (json['items'] as List<dynamic>)
        .map((item) => fromJsonT(item as Map<String, dynamic>))
        .toList();

    return PaginatedResponse(
      items: itemsList,
      totalCount: json['totalCount'] as int,
      currentPage: json['currentPage'] as int,
      totalPages: json['totalPages'] as int,
      hasNextPage: json['hasNextPage'] as bool,
      hasPreviousPage: json['hasPreviousPage'] as bool,
    );
  }
}

/// Common filter parameters
class FilterParams {
  final Map<String, dynamic> filters;

  const FilterParams({this.filters = const {}});

  Map<String, dynamic> toJson() => filters;

  FilterParams copyWith({Map<String, dynamic>? filters}) {
    return FilterParams(
      filters: filters ?? this.filters,
    );
  }
}

/// Base entity interface for all domain models
abstract class BaseEntity {
  String get id;
  DateTime get createdAt;
  DateTime get updatedAt;
}

/// Common date range filter
class DateRange {
  final DateTime? startDate;
  final DateTime? endDate;

  const DateRange({this.startDate, this.endDate});

  bool get isValid => startDate != null && endDate != null && startDate!.isBefore(endDate!);

  Map<String, dynamic> toJson() {
    return {
      if (startDate != null) 'startDate': startDate!.toIso8601String(),
      if (endDate != null) 'endDate': endDate!.toIso8601String(),
    };
  }
}
