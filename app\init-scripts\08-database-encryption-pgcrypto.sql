-- Migration: Database-Level Encryption with pgcrypto
-- Version: 1.0.0
-- Date: 2025-08-21
-- Description: Implement field-level encryption for sensitive data using pgcrypto extension

BEGIN;

-- =============================================================================
-- ENCRYPTION SETUP AND KEY MANAGEMENT
-- =============================================================================

-- Ensure pgcrypto extension is available
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create encryption key management functions
-- Note: In production, keys should be managed through external key management systems
-- This is a basic implementation for development/testing purposes

CREATE OR REPLACE FUNCTION get_encryption_key(key_type TEXT DEFAULT 'default')
R<PERSON>URNS TEXT AS $$
BEGIN
    -- In production, this should fetch keys from a secure key management service
    -- For now, we use environment-based keys or generate them
    CASE key_type
        WHEN 'pii' THEN 
            RETURN COALESCE(
                current_setting('app.encryption.pii_key', true),
                'pii_encryption_key_2025_v1'
            );
        WHEN 'financial' THEN 
            RETURN COALESCE(
                current_setting('app.encryption.financial_key', true),
                'financial_encryption_key_2025_v1'
            );
        WHEN 'mfa' THEN 
            RETURN COALESCE(
                current_setting('app.encryption.mfa_key', true),
                'mfa_encryption_key_2025_v1'
            );
        ELSE 
            RETURN COALESCE(
                current_setting('app.encryption.default_key', true),
                'default_encryption_key_2025_v1'
            );
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create encryption functions for different data types
CREATE OR REPLACE FUNCTION encrypt_sensitive_text(
    plaintext TEXT,
    key_type TEXT DEFAULT 'default'
) RETURNS BYTEA AS $$
BEGIN
    IF plaintext IS NULL OR plaintext = '' THEN
        RETURN NULL;
    END IF;
    
    -- Use AES-256 encryption with HMAC for integrity
    RETURN pgp_sym_encrypt(
        plaintext,
        get_encryption_key(key_type),
        'compress-algo=1, cipher-algo=aes256'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create decryption function
CREATE OR REPLACE FUNCTION decrypt_sensitive_text(
    ciphertext BYTEA,
    key_type TEXT DEFAULT 'default'
) RETURNS TEXT AS $$
BEGIN
    IF ciphertext IS NULL THEN
        RETURN NULL;
    END IF;
    
    RETURN pgp_sym_decrypt(ciphertext, get_encryption_key(key_type));
EXCEPTION
    WHEN OTHERS THEN
        -- Log decryption failure (in production, this should go to secure audit log)
        RAISE WARNING 'Decryption failed for key_type: %', key_type;
        RETURN '[DECRYPTION_FAILED]';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create hashing function for data that only needs integrity checking
CREATE OR REPLACE FUNCTION hash_sensitive_data(
    data TEXT,
    salt TEXT DEFAULT NULL
) RETURNS TEXT AS $$
BEGIN
    IF data IS NULL OR data = '' THEN
        RETURN NULL;
    END IF;
    
    -- Use SHA-256 with optional salt
    IF salt IS NOT NULL THEN
        RETURN encode(digest(data || salt, 'sha256'), 'hex');
    ELSE
        RETURN encode(digest(data, 'sha256'), 'hex');
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create password hashing function (using bcrypt)
CREATE OR REPLACE FUNCTION hash_password(password TEXT) 
RETURNS TEXT AS $$
BEGIN
    IF password IS NULL OR password = '' THEN
        RETURN NULL;
    END IF;
    
    -- Use bcrypt with cost factor 12
    RETURN crypt(password, gen_salt('bf', 12));
END;
$$ LANGUAGE plpgsql;

-- Create password verification function
CREATE OR REPLACE FUNCTION verify_password(password TEXT, hash TEXT) 
RETURNS BOOLEAN AS $$
BEGIN
    IF password IS NULL OR hash IS NULL THEN
        RETURN false;
    END IF;
    
    RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- ENCRYPTED COLUMNS FOR SENSITIVE DATA
-- =============================================================================

-- Add encrypted columns to users table for PII data
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_encrypted BYTEA;
ALTER TABLE users ADD COLUMN IF NOT EXISTS ssn_encrypted BYTEA;
ALTER TABLE users ADD COLUMN IF NOT EXISTS address_encrypted BYTEA;
ALTER TABLE users ADD COLUMN IF NOT EXISTS emergency_contact_encrypted BYTEA;

-- Add encrypted password storage (replace plaintext if exists)
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash_new TEXT;

-- Add encrypted columns for MFA secrets
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_secret_encrypted BYTEA;
ALTER TABLE users ADD COLUMN IF NOT EXISTS backup_codes_encrypted BYTEA;

-- Update user_mfa_settings table with encrypted fields
ALTER TABLE user_mfa_settings ADD COLUMN IF NOT EXISTS totp_secret_encrypted BYTEA;
ALTER TABLE user_mfa_settings ADD COLUMN IF NOT EXISTS recovery_codes_encrypted BYTEA;
ALTER TABLE user_mfa_settings ADD COLUMN IF NOT EXISTS sms_phone_encrypted BYTEA;

-- Add encrypted fields to user_sso_identities
ALTER TABLE user_sso_identities ADD COLUMN IF NOT EXISTS external_attributes_encrypted BYTEA;

-- Add encrypted session tokens
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS session_token_hash TEXT;
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS refresh_token_encrypted BYTEA;

-- Add encrypted fields to security audit logs for sensitive action details
ALTER TABLE security_audit_logs ADD COLUMN IF NOT EXISTS sensitive_details_encrypted BYTEA;

-- Add encrypted configuration for SSO providers
ALTER TABLE sso_providers ADD COLUMN IF NOT EXISTS private_key_encrypted BYTEA;
ALTER TABLE sso_providers ADD COLUMN IF NOT EXISTS client_secret_encrypted BYTEA;

-- =============================================================================
-- DATA MIGRATION FUNCTIONS
-- =============================================================================

-- Function to migrate existing plaintext data to encrypted format
CREATE OR REPLACE FUNCTION migrate_user_sensitive_data()
RETURNS void AS $$
DECLARE
    user_record RECORD;
    encrypted_phone BYTEA;
    encrypted_mfa_secret BYTEA;
BEGIN
    -- Migrate existing user data to encrypted format
    FOR user_record IN SELECT id, phone, mfa_secret FROM users 
                       WHERE phone IS NOT NULL OR mfa_secret IS NOT NULL LOOP
        
        -- Encrypt phone number if present
        IF user_record.phone IS NOT NULL THEN
            encrypted_phone := encrypt_sensitive_text(user_record.phone, 'pii');
            UPDATE users 
            SET phone_encrypted = encrypted_phone,
                phone = NULL  -- Clear plaintext after encryption
            WHERE id = user_record.id;
        END IF;
        
        -- Encrypt MFA secret if present
        IF user_record.mfa_secret IS NOT NULL THEN
            encrypted_mfa_secret := encrypt_sensitive_text(user_record.mfa_secret, 'mfa');
            UPDATE users 
            SET mfa_secret_encrypted = encrypted_mfa_secret,
                mfa_secret = NULL  -- Clear plaintext after encryption
            WHERE id = user_record.id;
        END IF;
        
    END LOOP;
    
    RAISE NOTICE 'Completed migration of user sensitive data to encrypted format';
END;
$$ LANGUAGE plpgsql;

-- Function to migrate session tokens to hashed format
CREATE OR REPLACE FUNCTION migrate_session_tokens()
RETURNS void AS $$
DECLARE
    session_record RECORD;
BEGIN
    -- Hash existing session tokens
    FOR session_record IN SELECT id, session_token, refresh_token FROM sessions 
                          WHERE session_token IS NOT NULL LOOP
        
        -- Hash session token for lookup
        UPDATE sessions 
        SET session_token_hash = hash_sensitive_data(session_record.session_token, 'session_salt'),
            session_token = substring(session_record.session_token, 1, 10) || '...[REDACTED]'
        WHERE id = session_record.id;
        
        -- Encrypt refresh token if present
        IF session_record.refresh_token IS NOT NULL THEN
            UPDATE sessions 
            SET refresh_token_encrypted = encrypt_sensitive_text(session_record.refresh_token, 'default')
            WHERE id = session_record.id;
        END IF;
        
    END LOOP;
    
    RAISE NOTICE 'Completed migration of session tokens to secure format';
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- SECURE VIEWS FOR DATA ACCESS
-- =============================================================================

-- Create view for accessing user data with decrypted fields (restricted access)
CREATE OR REPLACE VIEW users_decrypted AS
SELECT 
    id,
    email,
    name,
    organization_id,
    created_at,
    updated_at,
    is_active,
    last_login_at,
    login_count,
    failed_login_attempts,
    account_locked_until,
    mfa_enabled,
    -- Decrypt sensitive fields only when explicitly needed
    decrypt_sensitive_text(phone_encrypted, 'pii') as phone_decrypted,
    decrypt_sensitive_text(mfa_secret_encrypted, 'mfa') as mfa_secret_decrypted,
    -- Show masked versions for general access
    CASE 
        WHEN phone_encrypted IS NOT NULL THEN 'XXX-XXX-' || right(decrypt_sensitive_text(phone_encrypted, 'pii'), 4)
        ELSE NULL 
    END as phone_masked,
    -- Security metadata
    CASE 
        WHEN mfa_secret_encrypted IS NOT NULL THEN true 
        ELSE false 
    END as has_encrypted_mfa,
    CASE 
        WHEN phone_encrypted IS NOT NULL THEN true 
        ELSE false 
    END as has_encrypted_phone
FROM users
WHERE deleted_at IS NULL;

-- Create view for MFA settings with controlled decryption
CREATE OR REPLACE VIEW user_mfa_settings_secure AS
SELECT 
    id,
    user_id,
    is_enabled,
    preferred_method,
    enforcement_date,
    last_used_at,
    created_at,
    updated_at,
    -- Show availability of encrypted data without exposing it
    CASE 
        WHEN totp_secret_encrypted IS NOT NULL THEN true 
        ELSE false 
    END as has_totp_secret,
    CASE 
        WHEN recovery_codes_encrypted IS NOT NULL THEN true 
        ELSE false 
    END as has_recovery_codes,
    CASE 
        WHEN sms_phone_encrypted IS NOT NULL THEN 
            'XXX-XXX-' || right(decrypt_sensitive_text(sms_phone_encrypted, 'pii'), 4)
        ELSE NULL 
    END as sms_phone_masked
FROM user_mfa_settings;

-- =============================================================================
-- ENCRYPTION TRIGGER FUNCTIONS
-- =============================================================================

-- Trigger function to automatically encrypt sensitive data on insert/update
CREATE OR REPLACE FUNCTION encrypt_user_sensitive_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Encrypt phone number if provided in plaintext
    IF NEW.phone IS NOT NULL AND NEW.phone != OLD.phone THEN
        NEW.phone_encrypted := encrypt_sensitive_text(NEW.phone, 'pii');
        NEW.phone := NULL;  -- Clear plaintext
    END IF;
    
    -- Encrypt MFA secret if provided
    IF NEW.mfa_secret IS NOT NULL AND (OLD.mfa_secret IS NULL OR NEW.mfa_secret != OLD.mfa_secret) THEN
        NEW.mfa_secret_encrypted := encrypt_sensitive_text(NEW.mfa_secret, 'mfa');
        NEW.mfa_secret := NULL;  -- Clear plaintext
    END IF;
    
    -- Hash password if provided
    IF NEW.password IS NOT NULL AND (OLD.password IS NULL OR NEW.password != OLD.password) THEN
        NEW.password_hash_new := hash_password(NEW.password);
        NEW.password := NULL;  -- Clear plaintext
        NEW.password_changed_at := NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for users table
DROP TRIGGER IF EXISTS trigger_encrypt_user_data ON users;
CREATE TRIGGER trigger_encrypt_user_data
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION encrypt_user_sensitive_data();

-- Trigger function for MFA settings encryption
CREATE OR REPLACE FUNCTION encrypt_mfa_sensitive_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Encrypt TOTP secret
    IF NEW.totp_secret IS NOT NULL AND (OLD.totp_secret IS NULL OR NEW.totp_secret != OLD.totp_secret) THEN
        NEW.totp_secret_encrypted := encrypt_sensitive_text(NEW.totp_secret, 'mfa');
        NEW.totp_secret := NULL;
    END IF;
    
    -- Encrypt recovery codes (JSON array to text)
    IF NEW.recovery_codes IS NOT NULL AND (OLD.recovery_codes IS NULL OR NEW.recovery_codes != OLD.recovery_codes) THEN
        NEW.recovery_codes_encrypted := encrypt_sensitive_text(NEW.recovery_codes::text, 'mfa');
        NEW.recovery_codes := NULL;
    END IF;
    
    -- Encrypt SMS phone
    IF NEW.sms_phone IS NOT NULL AND (OLD.sms_phone IS NULL OR NEW.sms_phone != OLD.sms_phone) THEN
        NEW.sms_phone_encrypted := encrypt_sensitive_text(NEW.sms_phone, 'pii');
        NEW.sms_phone := NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for MFA settings table
DROP TRIGGER IF EXISTS trigger_encrypt_mfa_data ON user_mfa_settings;
CREATE TRIGGER trigger_encrypt_mfa_data
    BEFORE INSERT OR UPDATE ON user_mfa_settings
    FOR EACH ROW EXECUTE FUNCTION encrypt_mfa_sensitive_data();

-- =============================================================================
-- SECURE SESSION TOKEN HANDLING
-- =============================================================================

-- Function for secure session token storage
CREATE OR REPLACE FUNCTION store_session_token(
    p_session_id UUID,
    p_session_token TEXT,
    p_refresh_token TEXT DEFAULT NULL
) RETURNS void AS $$
BEGIN
    UPDATE sessions 
    SET 
        session_token_hash = hash_sensitive_data(p_session_token, 'session_salt'),
        session_token = substring(p_session_token, 1, 8) || '...[SECURE]',
        refresh_token_encrypted = CASE 
            WHEN p_refresh_token IS NOT NULL THEN encrypt_sensitive_text(p_refresh_token, 'default')
            ELSE NULL 
        END
    WHERE id = p_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for session token verification
CREATE OR REPLACE FUNCTION verify_session_token(
    p_session_token TEXT
) RETURNS UUID AS $$
DECLARE
    session_id UUID;
BEGIN
    SELECT id INTO session_id 
    FROM sessions 
    WHERE session_token_hash = hash_sensitive_data(p_session_token, 'session_salt')
      AND is_active = true 
      AND expires_at > NOW();
      
    RETURN session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- AUDIT LOG ENCRYPTION
-- =============================================================================

-- Function to securely log sensitive audit events
CREATE OR REPLACE FUNCTION log_sensitive_security_event(
    p_user_id UUID,
    p_organization_id UUID,
    p_action TEXT,
    p_event_type TEXT,
    p_sensitive_details JSONB,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO security_audit_logs (
        id,
        user_id,
        organization_id,
        action,
        event_type,
        details,
        sensitive_details_encrypted,
        ip_address,
        user_agent,
        created_at
    ) VALUES (
        gen_random_uuid(),
        p_user_id,
        p_organization_id,
        p_action,
        p_event_type,
        '{"encrypted": true}'::jsonb,
        encrypt_sensitive_text(p_sensitive_details::text, 'default'),
        p_ip_address,
        p_user_agent,
        NOW()
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- ENCRYPTION KEY ROTATION SUPPORT
-- =============================================================================

-- Function to rotate encryption keys (for future use)
CREATE OR REPLACE FUNCTION rotate_encryption_key(
    old_key_type TEXT,
    new_key_type TEXT,
    table_name TEXT,
    column_name TEXT
) RETURNS void AS $$
DECLARE
    sql_query TEXT;
BEGIN
    -- This is a template for key rotation - would need specific implementation
    -- for each encrypted column based on business requirements
    
    RAISE NOTICE 'Key rotation requested for %.% (% -> %)', 
                 table_name, column_name, old_key_type, new_key_type;
    
    -- In production, this would:
    -- 1. Decrypt data with old key
    -- 2. Re-encrypt with new key  
    -- 3. Update key references
    -- 4. Audit the rotation
    
    RAISE NOTICE 'Key rotation not implemented - manual process required';
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- SECURITY ACCESS CONTROLS
-- =============================================================================

-- Revoke direct access to encrypted columns from normal users
-- Grant access only to specific roles that need it

-- Create role for encryption management
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'encryption_admin') THEN
        CREATE ROLE encryption_admin;
    END IF;
END $$;

-- Grant necessary permissions to encryption admin
GRANT EXECUTE ON FUNCTION get_encryption_key(TEXT) TO encryption_admin;
GRANT EXECUTE ON FUNCTION encrypt_sensitive_text(TEXT, TEXT) TO encryption_admin;
GRANT EXECUTE ON FUNCTION decrypt_sensitive_text(BYTEA, TEXT) TO encryption_admin;
GRANT EXECUTE ON FUNCTION rotate_encryption_key(TEXT, TEXT, TEXT, TEXT) TO encryption_admin;

-- Create role for application access (limited decryption)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'app_secure_access') THEN
        CREATE ROLE app_secure_access;
    END IF;
END $$;

-- Grant limited permissions for application use
GRANT SELECT ON users_decrypted TO app_secure_access;
GRANT SELECT ON user_mfa_settings_secure TO app_secure_access;
GRANT EXECUTE ON FUNCTION verify_session_token(TEXT) TO app_secure_access;
GRANT EXECUTE ON FUNCTION store_session_token(UUID, TEXT, TEXT) TO app_secure_access;
GRANT EXECUTE ON FUNCTION log_sensitive_security_event(UUID, UUID, TEXT, TEXT, JSONB, INET, TEXT) TO app_secure_access;

-- =============================================================================
-- ENCRYPTION MONITORING AND MAINTENANCE
-- =============================================================================

-- Function to check encryption status across tables
CREATE OR REPLACE FUNCTION check_encryption_status()
RETURNS TABLE(
    table_name TEXT,
    column_name TEXT,
    encrypted_count BIGINT,
    total_count BIGINT,
    encryption_percentage NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'users'::TEXT,
        'phone'::TEXT,
        COUNT(CASE WHEN phone_encrypted IS NOT NULL THEN 1 END),
        COUNT(*),
        ROUND(COUNT(CASE WHEN phone_encrypted IS NOT NULL THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2)
    FROM users
    UNION ALL
    SELECT 
        'users'::TEXT,
        'mfa_secret'::TEXT,
        COUNT(CASE WHEN mfa_secret_encrypted IS NOT NULL THEN 1 END),
        COUNT(*),
        ROUND(COUNT(CASE WHEN mfa_secret_encrypted IS NOT NULL THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2)
    FROM users
    UNION ALL
    SELECT 
        'user_mfa_settings'::TEXT,
        'totp_secret'::TEXT,
        COUNT(CASE WHEN totp_secret_encrypted IS NOT NULL THEN 1 END),
        COUNT(*),
        ROUND(COUNT(CASE WHEN totp_secret_encrypted IS NOT NULL THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2)
    FROM user_mfa_settings;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate encryption integrity
CREATE OR REPLACE FUNCTION validate_encryption_integrity()
RETURNS TABLE(
    table_name TEXT,
    record_id UUID,
    column_name TEXT,
    can_decrypt BOOLEAN,
    error_message TEXT
) AS $$
DECLARE
    rec RECORD;
    decryption_result TEXT;
BEGIN
    -- Check user phone encryption
    FOR rec IN SELECT id, phone_encrypted FROM users WHERE phone_encrypted IS NOT NULL LOOP
        BEGIN
            decryption_result := decrypt_sensitive_text(rec.phone_encrypted, 'pii');
            RETURN QUERY SELECT 'users'::TEXT, rec.id, 'phone'::TEXT, true, NULL::TEXT;
        EXCEPTION WHEN OTHERS THEN
            RETURN QUERY SELECT 'users'::TEXT, rec.id, 'phone'::TEXT, false, SQLERRM;
        END;
    END LOOP;
    
    -- Check user MFA secret encryption
    FOR rec IN SELECT id, mfa_secret_encrypted FROM users WHERE mfa_secret_encrypted IS NOT NULL LOOP
        BEGIN
            decryption_result := decrypt_sensitive_text(rec.mfa_secret_encrypted, 'mfa');
            RETURN QUERY SELECT 'users'::TEXT, rec.id, 'mfa_secret'::TEXT, true, NULL::TEXT;
        EXCEPTION WHEN OTHERS THEN
            RETURN QUERY SELECT 'users'::TEXT, rec.id, 'mfa_secret'::TEXT, false, SQLERRM;
        END;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;

-- =============================================================================
-- POST-MIGRATION VERIFICATION
-- =============================================================================

-- Run encryption status check
SELECT * FROM check_encryption_status();

-- Note: To complete the encryption setup:
-- 1. Run migrate_user_sensitive_data() to encrypt existing data
-- 2. Run migrate_session_tokens() to secure session tokens  
-- 3. Set proper encryption keys in environment variables
-- 4. Test decryption access through secure views
-- 5. Validate encryption integrity with validate_encryption_integrity()
-- 6. Update application code to use encrypted views and functions