# Production Environment Configuration for Quest<PERSON>
# This file contains production-specific environment variables
# IMPORTANT: This file should be kept secure and not committed to version control

# =============================================================================
# ENVIRONMENT IDENTIFICATION
# =============================================================================
NODE_ENV=production
DART_ENV=production
ENVIRONMENT=production

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
API_BASE_URL=https://api.quester.app
WEB_BASE_URL=https://quester.app
WEBSOCKET_URL=wss://api.quester.app/ws
APP_NAME=Quester
APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Production Database
DATABASE_URL=************************************************/quester_prod
POSTGRES_HOST=prod-db-host
POSTGRES_PORT=5432
POSTGRES_USER=quester_prod
POSTGRES_PASSWORD=REPLACE_WITH_SECURE_PASSWORD
POSTGRES_DB=quester_prod
POSTGRES_SSL_MODE=require

# Connection Pool Settings
DB_POOL_MIN_CONNECTIONS=10
DB_POOL_MAX_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://prod-redis-host:6379/0
REDIS_HOST=prod-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=REPLACE_WITH_REDIS_PASSWORD
REDIS_DB=0
REDIS_TLS=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration
JWT_SECRET=REPLACE_WITH_SECURE_JWT_SECRET_256_BITS
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Encryption Keys
ENCRYPTION_KEY=REPLACE_WITH_SECURE_ENCRYPTION_KEY_256_BITS
API_KEY=REPLACE_WITH_SECURE_API_KEY

# Session Configuration
SESSION_SECRET=REPLACE_WITH_SECURE_SESSION_SECRET
SESSION_TIMEOUT=3600000

# CORS Configuration
CORS_ORIGIN=https://quester.app,https://www.quester.app
CORS_CREDENTIALS=true

# =============================================================================
# SENDGRID EMAIL CONFIGURATION
# =============================================================================
# Email Provider Settings
EMAIL_PROVIDER=sendgrid
EMAIL_ENABLED=true

# SendGrid API Configuration
SENDGRID_API_KEY=REPLACE_WITH_SENDGRID_API_KEY
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Quester
SENDGRID_REPLY_TO=<EMAIL>

# SendGrid Template IDs (create these in SendGrid dashboard)
SENDGRID_TEMPLATE_WELCOME=d-REPLACE_WITH_WELCOME_TEMPLATE_ID
SENDGRID_TEMPLATE_VERIFICATION=d-REPLACE_WITH_VERIFICATION_TEMPLATE_ID
SENDGRID_TEMPLATE_PASSWORD_RESET=d-REPLACE_WITH_PASSWORD_RESET_TEMPLATE_ID
SENDGRID_TEMPLATE_MFA_CODE=d-REPLACE_WITH_MFA_CODE_TEMPLATE_ID
SENDGRID_TEMPLATE_SECURITY_ALERT=d-REPLACE_WITH_SECURITY_ALERT_TEMPLATE_ID
SENDGRID_TEMPLATE_ACHIEVEMENT_UNLOCK=d-REPLACE_WITH_ACHIEVEMENT_TEMPLATE_ID
SENDGRID_TEMPLATE_QUEST_REMINDER=d-REPLACE_WITH_QUEST_REMINDER_TEMPLATE_ID
SENDGRID_TEMPLATE_TEAM_INVITATION=d-REPLACE_WITH_TEAM_INVITATION_TEMPLATE_ID

# Email Rate Limiting
EMAIL_RATE_LIMIT_PER_HOUR=100
EMAIL_RATE_LIMIT_PER_DAY=1000

# =============================================================================
# TWILIO SMS CONFIGURATION
# =============================================================================
# SMS Provider Settings
SMS_PROVIDER=twilio
SMS_ENABLED=true

# Twilio API Configuration
TWILIO_ACCOUNT_SID=REPLACE_WITH_TWILIO_ACCOUNT_SID
TWILIO_AUTH_TOKEN=REPLACE_WITH_TWILIO_AUTH_TOKEN
TWILIO_FROM_PHONE=+**********
TWILIO_MESSAGING_SERVICE_SID=REPLACE_WITH_MESSAGING_SERVICE_SID

# Twilio Features
TWILIO_VERIFY_SERVICE_SID=REPLACE_WITH_VERIFY_SERVICE_SID
TWILIO_WEBHOOK_URL=https://api.quester.app/webhooks/twilio
TWILIO_STATUS_CALLBACK_URL=https://api.quester.app/callbacks/sms-status

# SMS Rate Limiting
SMS_RATE_LIMIT_PER_HOUR=20
SMS_RATE_LIMIT_PER_DAY=100

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_TIMESTAMP=true
LOG_COLORIZE=false

# Log Destinations
LOG_TO_CONSOLE=true
LOG_TO_FILE=true
LOG_FILE_PATH=/var/log/quester/app.log
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_MAX_FILES=10

# External Logging Services
SENTRY_DSN=REPLACE_WITH_SENTRY_DSN
DATADOG_API_KEY=REPLACE_WITH_DATADOG_API_KEY
NEWRELIC_LICENSE_KEY=REPLACE_WITH_NEWRELIC_LICENSE_KEY

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================
# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_THRESHOLD_MS=100
SLOW_QUERY_THRESHOLD_MS=1000

# Analytics
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID=REPLACE_WITH_GA_ID
MIXPANEL_TOKEN=REPLACE_WITH_MIXPANEL_TOKEN

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_GAMIFICATION_ENABLED=true
FEATURE_REAL_TIME_UPDATES=true
FEATURE_OFFLINE_MODE=true
FEATURE_PUSH_NOTIFICATIONS=true
FEATURE_FILE_ATTACHMENTS=true
FEATURE_COLLABORATION=true
FEATURE_ADVANCED_SEARCH=true
FEATURE_BULK_OPERATIONS=true
FEATURE_API_ACCESS=false
FEATURE_ADMIN_PANEL=false
FEATURE_DEBUG_TOOLS=false

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# API Rate Limits
API_RATE_LIMIT_PER_MINUTE=60
API_RATE_LIMIT_PER_HOUR=1000
API_RATE_LIMIT_PER_DAY=10000

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# File Upload Settings
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt,md
UPLOAD_PATH=/var/uploads/quester

# Cloud Storage (AWS S3)
AWS_ACCESS_KEY_ID=REPLACE_WITH_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=REPLACE_WITH_AWS_SECRET_KEY
AWS_REGION=us-east-1
AWS_S3_BUCKET=quester-prod-uploads
AWS_S3_PUBLIC_URL=https://cdn.quester.app

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_ENABLED=true
CACHE_TTL_SECONDS=1800
CACHE_MAX_SIZE_MB=512

# Cache Strategies
CACHE_USER_DATA_TTL=300
CACHE_QUEST_DATA_TTL=600
CACHE_ACHIEVEMENT_DATA_TTL=3600
CACHE_LEADERBOARD_TTL=300

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=quester-prod-backups
BACKUP_ENCRYPTION_KEY=REPLACE_WITH_BACKUP_ENCRYPTION_KEY

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/quester.app.crt
SSL_KEY_PATH=/etc/ssl/private/quester.app.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
WEBHOOK_SECRET=REPLACE_WITH_WEBHOOK_SECRET
WEBHOOK_TIMEOUT=30000
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=5000

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Slack Integration
SLACK_BOT_TOKEN=REPLACE_WITH_SLACK_BOT_TOKEN
SLACK_WEBHOOK_URL=REPLACE_WITH_SLACK_WEBHOOK_URL

# Microsoft Teams Integration
TEAMS_WEBHOOK_URL=REPLACE_WITH_TEAMS_WEBHOOK_URL

# Discord Integration
DISCORD_BOT_TOKEN=REPLACE_WITH_DISCORD_BOT_TOKEN
DISCORD_WEBHOOK_URL=REPLACE_WITH_DISCORD_WEBHOOK_URL

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_TIMESTAMP=REPLACE_WITH_DEPLOYMENT_TIMESTAMP
DEPLOYMENT_COMMIT_SHA=REPLACE_WITH_COMMIT_SHA

# Container Configuration
CONTAINER_MEMORY_LIMIT=1G
CONTAINER_CPU_LIMIT=2.0
CONTAINER_RESTART_POLICY=unless-stopped

# =============================================================================
# MAINTENANCE MODE
# =============================================================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=Quester is currently undergoing maintenance. Please check back soon.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1

# =============================================================================
# SECURITY HEADERS
# =============================================================================
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff

# =============================================================================
# NOTES FOR DEPLOYMENT
# =============================================================================
# 1. Replace all REPLACE_WITH_* placeholders with actual values
# 2. Ensure all secrets are generated securely (use tools like openssl rand -hex 32)
# 3. Set up proper file permissions (600) for this file
# 4. Use environment-specific secret management (AWS Secrets Manager, Azure Key Vault, etc.)
# 5. Regularly rotate secrets and API keys
# 6. Monitor for any exposed credentials in logs or error messages
# 7. Set up proper backup and disaster recovery procedures
# 8. Configure monitoring and alerting for all critical services
# 9. Test all integrations in staging environment before production deployment
# 10. Document all configuration changes and maintain change logs
