import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shared/shared.dart';
import '../services/auth_service.dart';

/// Authentication and user management routes
class AuthRoutes {
  static final AuthService _authService = AuthService();
  static bool _initialized = false;
  
  /// Initialize the auth service
  static Future<void> initialize() async {
    if (!_initialized) {
      await _authService.initialize();
      _initialized = true;
      print('🔐 AuthRoutes initialized');
    }
  }
  
  /// Get the router with all authentication routes
  static Router get router {
    final router = Router();

    // Health check
    router.get('/health', _health);

    // Authentication routes
    router.post('/login', _login);
    router.post('/register', _register);
    router.post('/logout', _logout);
    router.post('/refresh-token', _refreshToken);

    // Email verification
    router.post('/verify-email', _verifyEmail);
    router.post('/resend-verification', _resendVerification);

    // Password management
    router.post('/forgot-password', _forgotPassword);
    router.post('/reset-password', _resetPassword);
    router.post('/change-password', _changePassword);

    // Two-factor authentication
    router.post('/setup-2fa', _setupTwoFactor);
    router.post('/verify-2fa', _verifyTwoFactor);
    router.post('/disable-2fa', _disableTwoFactor);
    router.get('/2fa-backup-codes', _getBackupCodes);

    // OAuth routes
    router.post('/oauth/google', _oauthGoogle);
    router.post('/oauth/microsoft', _oauthMicrosoft);
    router.post('/oauth/github', _oauthGithub);

    // Session management
    router.get('/session', _getSession);
    router.get('/sessions', _getUserSessions);
    router.delete('/sessions/<sessionId>', _terminateSession);
    router.delete('/sessions/all', _terminateAllSessions);

    // User profile
    router.get('/profile', _getProfile);
    router.put('/profile', _updateProfile);
    router.post('/profile/avatar', _updateAvatar);

    // Invitation handling
    router.post('/accept-invitation', _acceptInvitation);
    router.get('/invitation/<token>', _getInvitationDetails);

    // Admin routes (require admin permissions)
    router.post('/admin/impersonate', _impersonateUser);
    router.post('/admin/end-impersonation', _endImpersonation);
    router.get('/admin/users', _getUsers);
    router.get('/admin/users/<userId>', _getUserDetails);
    router.put('/admin/users/<userId>/status', _updateUserStatus);
    router.get('/admin/audit-logs', _getAuditLogs);

    // Utility routes
    router.get('/password-policy', _getPasswordPolicy);
    router.post('/check-email', _checkEmailAvailability);

    return router;
  }

  // Authentication endpoints

  /// Health check endpoint
  static Response _health(Request request) {
    return _jsonResponse({
      'status': 'healthy',
      'service': 'authentication',
      'timestamp': DateTime.now().toIso8601String(),
      'version': '1.0.0',
    });
  }

  static Future<Response> _login(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.authenticate(
        email: data['email'] as String,
        password: data['password'] as String,
        twoFactorCode: data['twoFactorCode'] as String?,
        organizationId: data['organizationId'] as String?,
        deviceInfo: _extractDeviceInfo(request),
        rememberMe: data['rememberMe'] as bool? ?? false,
      );

      return _jsonResponse(result, result['success'] ? 200 : 400);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _register(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.register(
        email: data['email'] as String,
        password: data['password'] as String,
        displayName: data['displayName'] as String,
        firstName: data['firstName'] as String?,
        lastName: data['lastName'] as String?,
        organizationName: data['organizationName'] as String?,
        invitationToken: data['invitationToken'] as String?,
        userPreferences: data['userPreferences'] as Map<String, dynamic>?,
        acceptTerms: data['acceptTerms'] as bool? ?? false,
        subscribeToNewsletter: data['subscribeToNewsletter'] as bool? ?? false,
        deviceInfo: _extractDeviceInfo(request),
      );

      return _jsonResponse(result, result['success'] ? 201 : 400);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _logout(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      final sessionId = _extractSessionId(request);
      if (sessionId == null) {
        return _errorResponse('Invalid session', 400);
      }

      final result = await _authService.logout(sessionId);
      return _jsonResponse(result);
    } catch (e) {
      return _errorResponse('Logout failed', 500);
    }
  }

  static Future<Response> _refreshToken(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.refreshToken(
        data['refreshToken'] as String,
      );

      return _jsonResponse(result, result['success'] ? 200 : 401);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  // Email verification endpoints

  static Future<Response> _verifyEmail(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.verifyEmail(
        verificationToken: data['verificationToken'] as String,
        email: data['email'] as String,
      );

      return _jsonResponse(result);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _resendVerification(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      // Resend verification email
      final authService = AuthService();
      try {
        await authService.resendVerificationEmail(session.user.id);
        return _jsonResponse({
          'success': true,
          'message': 'Verification email sent successfully',
        });
      } catch (e) {
        return _errorResponse('Failed to send verification email: $e', 500);
      }
    } catch (e) {
      return _errorResponse('Failed to send verification', 500);
    }
  }

  // Password management endpoints

  static Future<Response> _forgotPassword(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.initiatePasswordReset(
        data['email'] as String,
      );

      return _jsonResponse(result);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _resetPassword(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.completePasswordReset(
        resetToken: data['resetToken'] as String,
        newPassword: data['newPassword'] as String,
      );

      return _jsonResponse(result);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _changePassword(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.changePassword(
        userId: session.user.id,
        currentPassword: data['currentPassword'] as String,
        newPassword: data['newPassword'] as String,
      );

      return _jsonResponse(result);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  // Two-factor authentication endpoints

  static Future<Response> _setupTwoFactor(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final method = TwoFactorMethod.values.firstWhere(
        (m) => m.name == data['method'],
        orElse: () => TwoFactorMethod.totp,
      );

      final result = await _authService.setupTwoFactor(
        userId: session.user.id,
        method: method,
        phoneNumber: data['phoneNumber'] as String?,
        email: data['email'] as String?,
      );

      return _jsonResponse(result);
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _verifyTwoFactor(Request request) async {
    try {
      final body = await request.readAsString();
      final requestData = jsonDecode(body) as Map<String, dynamic>;
      
      // Implement 2FA verification
      final code = requestData['code'] as String?;
      final sessionToken = requestData['sessionToken'] as String?;

      if (code == null || sessionToken == null) {
        return _errorResponse('Code and session token required', 400);
      }

      // Mock 2FA verification - in real implementation, verify against stored codes
      if (code.length == 6 && code.contains(RegExp(r'^\d+$'))) {
        return _jsonResponse({
          'success': true,
          'message': '2FA verification successful',
          'accessToken': 'mock_access_token_after_2fa',
          'refreshToken': 'mock_refresh_token_after_2fa',
        });
      } else {
        return _errorResponse('Invalid verification code', 400);
      }
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _disableTwoFactor(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      // Get user ID from context
      final userId = request.context['userId'] as String?;
      if (userId == null) {
        return _errorResponse('User ID not found', 400);
      }

      // Disable 2FA for the user (mock implementation)
      // In a real implementation, this would update the database
      await Future.delayed(const Duration(milliseconds: 100));

      return _jsonResponse({
        'success': true,
        'message': '2FA disabled successfully',
        'userId': userId,
      });
    } catch (e) {
      return _errorResponse('Failed to disable 2FA', 500);
    }
  }

  static Future<Response> _getBackupCodes(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      // Get user ID from context
      final userId = request.context['userId'] as String?;
      if (userId == null) {
        return _errorResponse('User ID not found', 400);
      }

      // Generate backup codes (mock implementation)
      final backupCodes = List.generate(10, (i) {
        final random = DateTime.now().millisecondsSinceEpoch + i;
        return 'BACKUP-${random.toRadixString(36).toUpperCase()}';
      });

      return _jsonResponse({
        'success': true,
        'backupCodes': backupCodes,
        'message': 'Backup codes generated successfully',
      });
    } catch (e) {
      return _errorResponse('Failed to get backup codes', 500);
    }
  }

  // OAuth endpoints

  static Future<Response> _oauthGoogle(Request request) async {
    try {
      final body = await request.readAsString();
      final requestData = jsonDecode(body) as Map<String, dynamic>;

      final authCode = requestData['code'] as String?;

      if (authCode == null) {
        return _errorResponse('Authorization code required', 400);
      }

      // In a real implementation, you would:
      // 1. Exchange auth code for access token with Google
      // 2. Get user info from Google API
      // 3. Create or update user in database
      // 4. Generate JWT tokens

      // Mock implementation for now
      final mockUserData = {
        'id': 'google_user_123',
        'email': '<EMAIL>',
        'name': 'Google User',
        'picture': 'https://example.com/avatar.jpg',
      };

      // Create or find user in database
      final authService = AuthService();
      final loginResult = await authService.loginWithOAuth(
        provider: 'google',
        providerId: mockUserData['id'] as String,
        email: mockUserData['email'] as String,
        displayName: mockUserData['name'] as String,
        avatarUrl: mockUserData['picture'] as String,
      );

      return _jsonResponse(loginResult);
    } catch (e) {
      return _errorResponse('OAuth failed: $e', 500);
    }
  }

  static Future<Response> _oauthMicrosoft(Request request) async {
    try {
      final body = await request.readAsString();
      final requestData = jsonDecode(body) as Map<String, dynamic>;

      final authCode = requestData['code'] as String?;

      if (authCode == null) {
        return _errorResponse('Authorization code required', 400);
      }

      // Mock Microsoft OAuth implementation
      final mockUserData = {
        'id': 'microsoft_user_123',
        'email': '<EMAIL>',
        'name': 'Microsoft User',
        'picture': 'https://example.com/microsoft-avatar.jpg',
      };

      final authService = AuthService();
      final loginResult = await authService.loginWithOAuth(
        provider: 'microsoft',
        providerId: mockUserData['id'] as String,
        email: mockUserData['email'] as String,
        displayName: mockUserData['name'] as String,
        avatarUrl: mockUserData['picture'] as String,
      );

      return _jsonResponse(loginResult);
    } catch (e) {
      return _errorResponse('OAuth failed: $e', 500);
    }
  }

  static Future<Response> _oauthGithub(Request request) async {
    try {
      final body = await request.readAsString();
      final requestData = jsonDecode(body) as Map<String, dynamic>;

      final authCode = requestData['code'] as String?;

      if (authCode == null) {
        return _errorResponse('Authorization code required', 400);
      }

      // Mock GitHub OAuth implementation
      final mockUserData = {
        'id': 'github_user_123',
        'email': '<EMAIL>',
        'name': 'GitHub User',
        'picture': 'https://avatars.githubusercontent.com/u/123456',
      };

      final authService = AuthService();
      final loginResult = await authService.loginWithOAuth(
        provider: 'github',
        providerId: mockUserData['id'] as String,
        email: mockUserData['email'] as String,
        displayName: mockUserData['name'] as String,
        avatarUrl: mockUserData['picture'] as String,
      );

      return _jsonResponse(loginResult);
    } catch (e) {
      return _errorResponse('OAuth failed: $e', 500);
    }
  }

  // Session management endpoints

  static Future<Response> _getSession(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      return _jsonResponse({
        'success': true,
        'session': session.toJson(),
      });
    } catch (e) {
      return _errorResponse('Failed to get session', 500);
    }
  }

  static Future<Response> _getUserSessions(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      // Get all user sessions
      final authService = AuthService();
      try {
        final sessions = await authService.getUserSessions(session.user.id);
        return _jsonResponse({
          'success': true,
          'sessions': sessions,
        });
      } catch (e) {
        return _errorResponse('Failed to get sessions: $e', 500);
      }
    } catch (e) {
      return _errorResponse('Failed to get sessions', 500);
    }
  }

  static Future<Response> _terminateSession(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      final sessionId = request.params['sessionId']!;
      final result = await _authService.logout(sessionId);
      
      return _jsonResponse(result);
    } catch (e) {
      return _errorResponse('Failed to terminate session', 500);
    }
  }

  static Future<Response> _terminateAllSessions(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      // Terminate all user sessions
      final authService = AuthService();
      try {
        await authService.terminateAllUserSessions(session.user.id);
        return _jsonResponse({
          'success': true,
          'message': 'All sessions terminated successfully',
        });
      } catch (e) {
        return _errorResponse('Failed to terminate sessions: $e', 500);
      }
    } catch (e) {
      return _errorResponse('Failed to terminate sessions', 500);
    }
  }

  // User profile endpoints

  static Future<Response> _getProfile(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      return _jsonResponse({
        'success': true,
        'user': session.user.toJson(),
        'organizationRoles': session.organizationRoles
            .map((role) => role.toJson())
            .toList(),
      });
    } catch (e) {
      return _errorResponse('Failed to get profile', 500);
    }
  }

  static Future<Response> _updateProfile(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      final body = await request.readAsString();
      final profileData = jsonDecode(body) as Map<String, dynamic>;
      
      // Update user profile
      final authService = AuthService();
      try {
        await authService.updateUserProfile(
          userId: session.user.id,
          profileData: profileData,
        );

        return _jsonResponse({
          'success': true,
          'message': 'Profile updated successfully',
        });
      } catch (e) {
        return _errorResponse('Failed to update profile: $e', 500);
      }
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _updateAvatar(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      // Get user ID from context
      final userId = request.context['userId'] as String?;
      if (userId == null) {
        return _errorResponse('User ID not found', 400);
      }

      // Mock avatar upload implementation
      // In a real implementation, this would:
      // 1. Validate the uploaded file
      // 2. Store it in cloud storage (AWS S3, etc.)
      // 3. Update the user's avatar URL in the database

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final avatarUrl = 'https://cdn.quester.com/avatars/$userId-$timestamp.jpg';

      return _jsonResponse({
        'success': true,
        'avatarUrl': avatarUrl,
        'message': 'Avatar uploaded successfully',
      });
    } catch (e) {
      return _errorResponse('Failed to update avatar', 500);
    }
  }

  // Invitation endpoints

  static Future<Response> _acceptInvitation(Request request) async {
    try {
      final body = await request.readAsString();
      final invitationData = jsonDecode(body) as Map<String, dynamic>;
      
      // Extract invitation data
      final token = invitationData['token'] as String?;
      final email = invitationData['email'] as String?;
      final password = invitationData['password'] as String?;
      final firstName = invitationData['firstName'] as String?;
      final lastName = invitationData['lastName'] as String?;

      if (token == null || email == null || password == null) {
        return _errorResponse('Missing required fields', 400);
      }

      // Mock invitation acceptance implementation
      // In a real implementation, this would:
      // 1. Validate the invitation token
      // 2. Create the user account
      // 3. Assign appropriate roles/permissions
      // 4. Send welcome email

      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';

      return _jsonResponse({
        'success': true,
        'message': 'Invitation accepted successfully',
        'userId': userId,
        'email': email,
        'firstName': firstName,
        'lastName': lastName,
      });
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  static Future<Response> _getInvitationDetails(Request request) async {
    try {
      final invitationToken = request.params['token']!;
      
      // Mock invitation details implementation
      // In a real implementation, this would:
      // 1. Validate the invitation token
      // 2. Fetch invitation details from database
      // 3. Check if token is expired

      final isValidToken = invitationToken.startsWith('inv_');

      if (!isValidToken) {
        return _errorResponse('Invalid invitation token', 400);
      }

      return _jsonResponse({
        'success': true,
        'token': invitationToken,
        'invitation': {
          'organizationName': 'Acme Corporation',
          'inviterName': 'John Doe',
          'inviterEmail': '<EMAIL>',
          'roleName': 'Developer',
          'expiresAt': DateTime.now().add(const Duration(days: 7)).toIso8601String(),
          'isExpired': false,
        },
      });
    } catch (e) {
      return _errorResponse('Invalid invitation', 400);
    }
  }

  // Admin endpoints

  static Future<Response> _impersonateUser(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      if (!session.isSystemAdmin) {
        return _errorResponse('Insufficient permissions', 403);
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _authService.impersonateUser(
        adminUserId: session.user.id,
        targetUserId: data['targetUserId'] as String,
        reason: data['reason'] as String,
        durationMinutes: data['durationMinutes'] as int?,
      );

      return _jsonResponse(result, result['success'] ? 200 : 400);
    } catch (e) {
      return _errorResponse('Impersonation failed', 500);
    }
  }

  static Future<Response> _endImpersonation(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      // End impersonation session
      final userId = request.context['userId'] as String?;
      if (userId == null) {
        return _errorResponse('User ID not found', 400);
      }

      // In a real implementation, this would:
      // 1. Validate that the user is currently impersonating
      // 2. Restore the original user session
      // 3. Log the impersonation end event
      // 4. Clear impersonation flags

      return _jsonResponse({
        'success': true,
        'message': 'Impersonation ended successfully',
        'originalUserId': userId,
      });
    } catch (e) {
      return _errorResponse('Failed to end impersonation', 500);
    }
  }

  static Future<Response> _getUsers(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      if (!session.hasPermission(Permission.userRead)) {
        return _errorResponse('Insufficient permissions', 403);
      }

      // Get users list with pagination and filtering
      final page = int.tryParse(request.url.queryParameters['page'] ?? '1') ?? 1;
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '25') ?? 25;
      final search = request.url.queryParameters['search'];
      final status = request.url.queryParameters['status'];

      // Mock users data - in real implementation, this would query the database
      final mockUsers = [
        {
          'id': 'user_1',
          'email': '<EMAIL>',
          'displayName': 'John Doe',
          'firstName': 'John',
          'lastName': 'Doe',
          'role': 'journeyman',
          'status': 'active',
          'totalPoints': 1250,
          'level': 3,
          'createdAt': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
          'lastLoginAt': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        },
        {
          'id': 'user_2',
          'email': '<EMAIL>',
          'displayName': 'Jane Smith',
          'firstName': 'Jane',
          'lastName': 'Smith',
          'role': 'expert',
          'status': 'active',
          'totalPoints': 3500,
          'level': 5,
          'createdAt': DateTime.now().subtract(const Duration(days: 60)).toIso8601String(),
          'lastLoginAt': DateTime.now().subtract(const Duration(minutes: 15)).toIso8601String(),
        },
      ];

      // Apply filters
      var filteredUsers = mockUsers;
      if (search != null && search.isNotEmpty) {
        filteredUsers = filteredUsers.where((user) =>
          user['displayName'].toString().toLowerCase().contains(search.toLowerCase()) ||
          user['email'].toString().toLowerCase().contains(search.toLowerCase())
        ).toList();
      }

      if (status != null && status.isNotEmpty) {
        filteredUsers = filteredUsers.where((user) => user['status'] == status).toList();
      }

      // Apply pagination
      final startIndex = (page - 1) * limit;
      final paginatedUsers = filteredUsers.skip(startIndex).take(limit).toList();

      return _jsonResponse({
        'success': true,
        'users': paginatedUsers,
        'pagination': {
          'page': page,
          'limit': limit,
          'total': filteredUsers.length,
          'totalPages': (filteredUsers.length / limit).ceil(),
        },
      });
    } catch (e) {
      return _errorResponse('Failed to get users', 500);
    }
  }

  static Future<Response> _getUserDetails(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      if (!session.hasPermission(Permission.userRead)) {
        return _errorResponse('Insufficient permissions', 403);
      }

      final targetUserId = request.params['userId']!;
      
      // Get detailed user information
      // In a real implementation, this would query the database for user details
      final mockUserDetails = {
        'id': targetUserId,
        'email': '<EMAIL>',
        'displayName': 'Sample User',
        'firstName': 'Sample',
        'lastName': 'User',
        'avatarUrl': 'https://example.com/avatar.jpg',
        'role': 'journeyman',
        'status': 'active',
        'totalPoints': 2500,
        'currentLevelPoints': 500,
        'level': 4,
        'currentStreak': 7,
        'longestStreak': 15,
        'achievementCount': 12,
        'questsCompleted': 25,
        'tasksCompleted': 150,
        'createdAt': DateTime.now().subtract(const Duration(days: 90)).toIso8601String(),
        'updatedAt': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
        'lastLoginAt': DateTime.now().subtract(const Duration(minutes: 30)).toIso8601String(),
        'preferences': {
          'theme': 'dark',
          'notifications': true,
          'language': 'en',
        },
      };

      final mockOrganizationRoles = [
        {
          'organizationId': 'org_1',
          'organizationName': 'Example Corp',
          'role': 'member',
          'isDefault': true,
          'assignedAt': DateTime.now().subtract(const Duration(days: 60)).toIso8601String(),
        }
      ];

      final mockSessions = [
        {
          'id': 'session_1',
          'deviceInfo': 'Chrome on Windows',
          'ipAddress': '*************',
          'location': 'New York, US',
          'lastActivity': DateTime.now().subtract(const Duration(minutes: 5)).toIso8601String(),
          'isCurrentSession': true,
        }
      ];

      return _jsonResponse({
        'success': true,
        'userId': targetUserId,
        'user': mockUserDetails,
        'organizationRoles': mockOrganizationRoles,
        'sessions': mockSessions,
        'auditLog': [], // Recent audit events for this user
      });
    } catch (e) {
      return _errorResponse('Failed to get user details', 500);
    }
  }

  static Future<Response> _updateUserStatus(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      if (!session.hasPermission(Permission.userWrite)) {
        return _errorResponse('Insufficient permissions', 403);
      }

      final targetUserId = request.params['userId']!;
      final body = await request.readAsString();
      final statusData = jsonDecode(body) as Map<String, dynamic>;
      
      // Update user status
      final newStatus = statusData['status'] as String?;
      final reason = statusData['reason'] as String?;

      if (newStatus == null) {
        return _errorResponse('Status is required', 400);
      }

      // Validate status value
      final validStatuses = ['active', 'inactive', 'suspended', 'pending'];
      if (!validStatuses.contains(newStatus)) {
        return _errorResponse('Invalid status value', 400);
      }

      // In a real implementation, this would:
      // 1. Update the user status in the database
      // 2. Log the status change in audit logs
      // 3. Send notifications if needed
      // 4. Handle session termination for suspended users

      return _jsonResponse({
        'success': true,
        'message': 'User status updated successfully',
        'userId': targetUserId,
        'previousStatus': 'active', // Would come from database
        'newStatus': newStatus,
        'reason': reason,
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      return _errorResponse('Failed to update user status', 500);
    }
  }

  static Future<Response> _getAuditLogs(Request request) async {
    try {
      final session = await _requireAuthentication(request);
      if (session == null) {
        return _errorResponse('Authentication required', 401);
      }

      if (!session.hasPermission(Permission.systemLogs)) {
        return _errorResponse('Insufficient permissions', 403);
      }

      // Get audit logs with filtering and pagination
      final page = int.tryParse(request.url.queryParameters['page'] ?? '1') ?? 1;
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
      final userId = request.url.queryParameters['userId'];
      final action = request.url.queryParameters['action'];
      final startDate = request.url.queryParameters['startDate'];
      final endDate = request.url.queryParameters['endDate'];

      // Mock audit logs - in real implementation, this would query the audit_logs table
      final mockLogs = [
        {
          'id': 'log_1',
          'userId': 'user_123',
          'userEmail': '<EMAIL>',
          'action': 'user_login',
          'resource': 'auth',
          'resourceId': null,
          'details': {
            'ipAddress': '*************',
            'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'location': 'New York, US',
          },
          'timestamp': DateTime.now().subtract(const Duration(minutes: 30)).toIso8601String(),
          'success': true,
        },
        {
          'id': 'log_2',
          'userId': 'admin_456',
          'userEmail': '<EMAIL>',
          'action': 'user_status_update',
          'resource': 'user',
          'resourceId': 'user_789',
          'details': {
            'previousStatus': 'active',
            'newStatus': 'suspended',
            'reason': 'Policy violation',
          },
          'timestamp': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
          'success': true,
        },
        {
          'id': 'log_3',
          'userId': 'user_789',
          'userEmail': '<EMAIL>',
          'action': 'quest_create',
          'resource': 'quest',
          'resourceId': 'quest_101',
          'details': {
            'questTitle': 'Complete Project Documentation',
            'category': 'work',
            'priority': 'high',
          },
          'timestamp': DateTime.now().subtract(const Duration(hours: 4)).toIso8601String(),
          'success': true,
        },
      ];

      // Apply filters
      var filteredLogs = mockLogs;

      if (userId != null && userId.isNotEmpty) {
        filteredLogs = filteredLogs.where((log) => log['userId'] == userId).toList();
      }

      if (action != null && action.isNotEmpty) {
        filteredLogs = filteredLogs.where((log) => log['action'] == action).toList();
      }

      // Apply pagination
      final startIndex = (page - 1) * limit;
      final paginatedLogs = filteredLogs.skip(startIndex).take(limit).toList();

      return _jsonResponse({
        'success': true,
        'logs': paginatedLogs,
        'pagination': {
          'page': page,
          'limit': limit,
          'total': filteredLogs.length,
          'totalPages': (filteredLogs.length / limit).ceil(),
        },
        'filters': {
          'userId': userId,
          'action': action,
          'startDate': startDate,
          'endDate': endDate,
        },
      });
    } catch (e) {
      return _errorResponse('Failed to get audit logs', 500);
    }
  }

  // Utility endpoints

  static Future<Response> _getPasswordPolicy(Request request) async {
    try {
      final policy = _authService.getPasswordPolicy();
      return _jsonResponse({
        'success': true,
        'policy': policy,
      });
    } catch (e) {
      return _errorResponse('Failed to get password policy', 500);
    }
  }

  static Future<Response> _checkEmailAvailability(Request request) async {
    try {
      final body = await request.readAsString();
      final emailData = jsonDecode(body) as Map<String, dynamic>;
      
      // Check email availability
      final email = emailData['email'] as String?;

      if (email == null || email.isEmpty) {
        return _errorResponse('Email is required', 400);
      }

      // Validate email format
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (!emailRegex.hasMatch(email)) {
        return _errorResponse('Invalid email format', 400);
      }

      // In a real implementation, this would:
      // 1. Query the database to check if email exists
      // 2. Check against reserved email patterns
      // 3. Validate against domain blacklists

      // Mock availability check - simulate some emails as taken
      final takenEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      final isAvailable = !takenEmails.contains(email.toLowerCase());

      return _jsonResponse({
        'success': true,
        'available': isAvailable,
        'email': email,
        'message': isAvailable
          ? 'Email is available'
          : 'Email is already registered',
      });
    } catch (e) {
      return _errorResponse('Invalid request format', 400);
    }
  }

  // Helper methods

  static Future<AuthSession?> _requireAuthentication(Request request) async {
    final authorization = request.headers['authorization'];
    if (authorization == null || !authorization.startsWith('Bearer ')) {
      return null;
    }

    final token = authorization.substring(7);
    return await _authService.validateSession(token);
  }

  static Map<String, String> _extractDeviceInfo(Request request) {
    return {
      'ip': request.headers['x-forwarded-for'] ?? 
            request.headers['x-real-ip'] ?? 
            'unknown',
      'userAgent': request.headers['user-agent'] ?? 'unknown',
    };
  }

  static String? _extractSessionId(Request request) {
    final authorization = request.headers['authorization'];
    if (authorization == null || !authorization.startsWith('Bearer ')) {
      return null;
    }

    try {
      final token = authorization.substring(7);
      
      // Simple JWT parsing to extract sessionId
      final parts = token.split('.');
      if (parts.length != 3) return null;
      
      final payloadEncoded = parts[1];
      // Add padding if needed
      final padding = 4 - (payloadEncoded.length % 4);
      final paddedPayload = payloadEncoded + '=' * (padding % 4);
      
      final payloadJson = utf8.decode(base64.decode(paddedPayload));
      final payload = jsonDecode(payloadJson) as Map<String, dynamic>;
      
      return payload['sessionId'] as String?;
    } catch (e) {
      print('🔍 Error extracting session ID: $e');
      return null;
    }
  }

  static Response _jsonResponse(
    Map<String, dynamic> data, [
    int statusCode = 200,
  ]) {
    return Response.ok(
      jsonEncode(data),
      headers: {
        'content-type': 'application/json',
        'access-control-allow-origin': '*',
        'access-control-allow-methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'access-control-allow-headers': 'Content-Type, Authorization',
      },
    );
  }

  static Response _errorResponse(String message, int statusCode) {
    return Response(
      statusCode,
      body: jsonEncode({
        'success': false,
        'error': message,
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {
        'content-type': 'application/json',
        'access-control-allow-origin': '*',
        'access-control-allow-methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'access-control-allow-headers': 'Content-Type, Authorization',
      },
    );
  }
}