import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('UserSSOIdentity Tests', () {
    late UserSSOIdentity testIdentity;
    
    setUp(() {
      testIdentity = UserSSOIdentity(
        id: 'identity_123',
        userId: 'user_456',
        providerId: 'provider_789',
        externalId: 'google_user_123456789',
        externalUsername: '<EMAIL>',
        externalEmail: '<EMAIL>',
        providerAttributes: {
          'name': '<PERSON>',
          'given_name': '<PERSON>',
          'family_name': 'Do<PERSON>',
          'email': '<EMAIL>',
          'picture': 'https://example.com/avatar.jpg',
          'locale': 'en',
          'email_verified': true,
          'department': 'Engineering',
          'title': 'Senior Developer',
        },
        lastLogin: DateTime.parse('2025-01-15T14:30:00.000Z'),
        isActive: true,
        createdAt: DateTime.parse('2025-01-10T09:00:00.000Z'),
        updatedAt: DateTime.parse('2025-01-15T14:30:00.000Z'),
      );
    });

    test('should create valid UserSSOIdentity instance', () {
      expect(testIdentity.id, equals('identity_123'));
      expect(testIdentity.userId, equals('user_456'));
      expect(testIdentity.providerId, equals('provider_789'));
      expect(testIdentity.externalId, equals('google_user_123456789'));
      expect(testIdentity.externalUsername, equals('<EMAIL>'));
      expect(testIdentity.externalEmail, equals('<EMAIL>'));
      expect(testIdentity.isActive, isTrue);
      expect(testIdentity.providerAttributes, isA<Map<String, dynamic>>());
      expect(testIdentity.lastLogin, isNotNull);
    });

    test('should create empty UserSSOIdentity for testing', () {
      final empty = UserSSOIdentity.empty();
      expect(empty.id, isEmpty);
      expect(empty.userId, isEmpty);
      expect(empty.providerId, isEmpty);
      expect(empty.externalId, isEmpty);
      expect(empty.externalUsername, isNull);
      expect(empty.externalEmail, isNull);
      expect(empty.providerAttributes, isNull);
      expect(empty.lastLogin, isNull);
      expect(empty.isActive, isTrue);
    });

    group('Display Name Generation', () {
      test('should get display name from provider attributes', () {
        expect(testIdentity.displayName, equals('John Doe'));
        
        // Test with displayName attribute
        final withDisplayName = testIdentity.copyWith(
          providerAttributes: {
            'displayName': 'Dr. John Doe',
            'name': 'John Doe', // Should prefer displayName
          },
        );
        expect(withDisplayName.displayName, equals('Dr. John Doe'));
        
        // Test with given_name and family_name
        final withSeparateNames = testIdentity.copyWith(
          providerAttributes: {
            'given_name': 'Jane',
            'family_name': 'Smith',
          },
        );
        expect(withSeparateNames.displayName, equals('Jane Smith'));
        
        // Test fallback to external username
        final noNameAttributes = testIdentity.copyWith(
          providerAttributes: {'email': '<EMAIL>'},
          externalUsername: 'testuser',
        );
        expect(noNameAttributes.displayName, equals('testuser'));
        
        // Test fallback to external email
        final emailFallback = testIdentity.copyWith(
          providerAttributes: {},
          externalUsername: null,
          externalEmail: '<EMAIL>',
        );
        expect(emailFallback.displayName, equals('<EMAIL>'));
        
        // Test ultimate fallback
        final unknownUser = testIdentity.copyWith(
          providerAttributes: {},
          externalUsername: null,
          externalEmail: null,
        );
        expect(unknownUser.displayName, equals('Unknown User'));
      });

      test('should handle partial name attributes gracefully', () {
        final onlyGivenName = testIdentity.copyWith(
          providerAttributes: {
            'given_name': 'John',
            'family_name': null,
          },
        );
        expect(onlyGivenName.displayName, equals('testuser')); // Falls back to username
        
        final onlyFamilyName = testIdentity.copyWith(
          providerAttributes: {
            'given_name': null,
            'family_name': 'Doe',
          },
        );
        expect(onlyFamilyName.displayName, equals('testuser')); // Falls back to username
      });
    });

    group('Email Resolution', () {
      test('should get effective email from provider attributes', () {
        expect(testIdentity.effectiveEmail, equals('<EMAIL>'));
        
        // Test with different email in provider attributes
        final differentProviderEmail = testIdentity.copyWith(
          providerAttributes: {
            'email': '<EMAIL>',
          },
          externalEmail: '<EMAIL>',
        );
        expect(differentProviderEmail.effectiveEmail, equals('<EMAIL>'));
        
        // Test fallback to external email
        final noProviderEmail = testIdentity.copyWith(
          providerAttributes: {},
          externalEmail: '<EMAIL>',
        );
        expect(noProviderEmail.effectiveEmail, equals('<EMAIL>'));
        
        // Test no email available
        final noEmail = testIdentity.copyWith(
          providerAttributes: {},
          externalEmail: null,
        );
        expect(noEmail.effectiveEmail, isNull);
      });

      test('should handle null email in provider attributes', () {
        final nullProviderEmail = testIdentity.copyWith(
          providerAttributes: {
            'email': null,
          },
          externalEmail: '<EMAIL>',
        );
        expect(nullProviderEmail.effectiveEmail, equals('<EMAIL>'));
      });
    });

    group('Activity Status', () {
      test('should identify recent activity correctly', () {
        expect(testIdentity.hasRecentActivity, isTrue);
        
        final oldActivity = testIdentity.copyWith(
          lastLogin: DateTime.now().subtract(const Duration(days: 45)),
        );
        expect(oldActivity.hasRecentActivity, isFalse);
        
        final recentActivity = testIdentity.copyWith(
          lastLogin: DateTime.now().subtract(const Duration(days: 15)),
        );
        expect(recentActivity.hasRecentActivity, isTrue);
        
        final noActivity = testIdentity.copyWith(lastLogin: null);
        expect(noActivity.hasRecentActivity, isFalse);
      });
    });

    test('should serialize to JSON correctly', () {
      final json = testIdentity.toJson();
      expect(json['id'], equals('identity_123'));
      expect(json['user_id'], equals('user_456'));
      expect(json['provider_id'], equals('provider_789'));
      expect(json['external_id'], equals('google_user_123456789'));
      expect(json['external_username'], equals('<EMAIL>'));
      expect(json['external_email'], equals('<EMAIL>'));
      expect(json['provider_attributes'], isA<Map<String, dynamic>>());
      expect(json['last_login'], isA<String>());
      expect(json['is_active'], isTrue);
      expect(json['created_at'], isA<String>());
      expect(json['updated_at'], isA<String>());
    });

    test('should deserialize from JSON correctly', () {
      final json = testIdentity.toJson();
      final deserialized = UserSSOIdentity.fromJson(json);
      
      expect(deserialized.id, equals(testIdentity.id));
      expect(deserialized.userId, equals(testIdentity.userId));
      expect(deserialized.providerId, equals(testIdentity.providerId));
      expect(deserialized.externalId, equals(testIdentity.externalId));
      expect(deserialized.externalUsername, equals(testIdentity.externalUsername));
      expect(deserialized.externalEmail, equals(testIdentity.externalEmail));
      expect(deserialized.providerAttributes, equals(testIdentity.providerAttributes));
      expect(deserialized.lastLogin, equals(testIdentity.lastLogin));
      expect(deserialized.isActive, equals(testIdentity.isActive));
      expect(deserialized.createdAt, equals(testIdentity.createdAt));
      expect(deserialized.updatedAt, equals(testIdentity.updatedAt));
    });

    test('should handle null optional fields in JSON deserialization', () {
      final json = {
        'id': 'test_id',
        'user_id': 'user_123',
        'provider_id': 'provider_456',
        'external_id': 'external_789',
        'external_username': null,
        'external_email': null,
        'provider_attributes': null,
        'last_login': null,
        'is_active': true,
        'created_at': '2025-01-15T10:00:00.000Z',
        'updated_at': '2025-01-15T10:00:00.000Z',
      };
      
      final identity = UserSSOIdentity.fromJson(json);
      expect(identity.externalUsername, isNull);
      expect(identity.externalEmail, isNull);
      expect(identity.providerAttributes, isNull);
      expect(identity.lastLogin, isNull);
    });

    test('should create copy with updated fields', () {
      final updated = testIdentity.copyWith(
        isActive: false,
        externalUsername: '<EMAIL>',
        lastLogin: DateTime.parse('2025-01-16T10:00:00.000Z'),
        providerAttributes: {
          'name': 'Updated Name',
          'department': 'Marketing',
        },
      );
      
      expect(updated.isActive, isFalse);
      expect(updated.externalUsername, equals('<EMAIL>'));
      expect(updated.lastLogin!.day, equals(16));
      expect(updated.providerAttributes!['name'], equals('Updated Name'));
      expect(updated.providerAttributes!['department'], equals('Marketing'));
      expect(updated.id, equals(testIdentity.id)); // Unchanged
      expect(updated.userId, equals(testIdentity.userId)); // Unchanged
      expect(updated.providerId, equals(testIdentity.providerId)); // Unchanged
    });

    test('should maintain equality for identical instances', () {
      final json = testIdentity.toJson();
      final identical = UserSSOIdentity.fromJson(json);
      expect(testIdentity, equals(identical));
    });

    test('should not be equal for different instances', () {
      final different = testIdentity.copyWith(id: 'different_id');
      expect(testIdentity, isNot(equals(different)));
    });

    group('Complex Provider Attributes', () {
      test('should handle complex nested provider attributes', () {
        final complexAttributes = {
          'name': 'John Doe',
          'email': '<EMAIL>',
          'profile': {
            'avatar_url': 'https://example.com/avatar.jpg',
            'bio': 'Software Engineer',
            'location': 'San Francisco',
          },
          'roles': ['developer', 'team_lead'],
          'permissions': {
            'read': true,
            'write': true,
            'admin': false,
          },
          'metadata': {
            'last_password_change': '2025-01-01',
            'account_created': '2023-05-15',
            'login_count': 156,
          },
        };
        
        final complexIdentity = testIdentity.copyWith(
          providerAttributes: complexAttributes,
        );
        
        final json = complexIdentity.toJson();
        final deserialized = UserSSOIdentity.fromJson(json);
        
        expect(deserialized.providerAttributes, equals(complexAttributes));
        expect(deserialized.providerAttributes!['profile'], isA<Map>());
        expect(deserialized.providerAttributes!['roles'], isA<List>());
        expect(deserialized.providerAttributes!['permissions'], isA<Map>());
        expect(deserialized.providerAttributes!['metadata']['login_count'], equals(156));
      });

      test('should handle empty and null provider attributes', () {
        final emptyAttributes = testIdentity.copyWith(providerAttributes: {});
        expect(emptyAttributes.displayName, equals('<EMAIL>')); // Falls back to username
        expect(emptyAttributes.effectiveEmail, equals('<EMAIL>')); // Falls back to external email
        
        final nullAttributes = testIdentity.copyWith(providerAttributes: null);
        expect(nullAttributes.displayName, equals('<EMAIL>')); // Falls back to username
        expect(nullAttributes.effectiveEmail, equals('<EMAIL>')); // Falls back to external email
      });
    });

    group('Enterprise Integration Scenarios', () {
      test('should handle SAML provider attributes', () {
        final samlIdentity = testIdentity.copyWith(
          providerId: 'saml_provider_123',
          externalId: 'CN=John Doe,OU=Engineering,DC=company,DC=com',
          providerAttributes: {
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': 'John Doe',
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': '<EMAIL>',
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname': 'John',
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname': 'Doe',
            'http://schemas.microsoft.com/ws/2008/06/identity/claims/groups': 'Engineering;Developers;TeamLeads',
            'department': 'Engineering',
            'employeeId': 'EMP001234',
          },
        );
        
        expect(samlIdentity.externalId, contains('CN=John Doe'));
        expect(samlIdentity.providerAttributes!['department'], equals('Engineering'));
        expect(samlIdentity.providerAttributes!['employeeId'], equals('EMP001234'));
      });

      test('should handle OAuth provider attributes', () {
        final oauthIdentity = testIdentity.copyWith(
          providerId: 'oauth_google_123',
          providerAttributes: {
            'sub': 'google_user_123456789',
            'name': 'John Doe',
            'given_name': 'John',
            'family_name': 'Doe',
            'picture': 'https://lh3.googleusercontent.com/avatar.jpg',
            'email': '<EMAIL>',
            'email_verified': true,
            'locale': 'en',
            'hd': 'company.com', // Google Workspace domain
          },
        );
        
        expect(oauthIdentity.providerAttributes!['sub'], equals('google_user_123456789'));
        expect(oauthIdentity.providerAttributes!['email_verified'], isTrue);
        expect(oauthIdentity.providerAttributes!['hd'], equals('company.com'));
      });

      test('should handle Active Directory attributes', () {
        final adIdentity = testIdentity.copyWith(
          providerId: 'ad_provider_123',
          externalId: '<EMAIL>',
          providerAttributes: {
            'sAMAccountName': 'john.doe',
            'userPrincipalName': '<EMAIL>',
            'displayName': 'Doe, John',
            'givenName': 'John',
            'sn': 'Doe',
            'mail': '<EMAIL>',
            'department': 'Information Technology',
            'title': 'Senior Software Engineer',
            'manager': 'CN=Jane Manager,OU=Engineering,DC=company,DC=local',
            'memberOf': [
              'CN=Developers,OU=Groups,DC=company,DC=local',
              'CN=Engineering,OU=Groups,DC=company,DC=local',
            ],
          },
        );
        
        expect(adIdentity.providerAttributes!['sAMAccountName'], equals('john.doe'));
        expect(adIdentity.providerAttributes!['displayName'], equals('Doe, John'));
        expect(adIdentity.providerAttributes!['memberOf'], isA<List>());
      });
    });

    group('Edge Cases', () {
      test('should handle extremely long external IDs', () {
        final longExternalId = 'a' * 500; // 500 character ID
        final longIdIdentity = testIdentity.copyWith(externalId: longExternalId);
        expect(longIdIdentity.externalId.length, equals(500));
        
        final json = longIdIdentity.toJson();
        final deserialized = UserSSOIdentity.fromJson(json);
        expect(deserialized.externalId, equals(longExternalId));
      });

      test('should handle special characters in external data', () {
        final specialCharsIdentity = testIdentity.copyWith(
          externalUsername: '<EMAIL>',
          externalEmail: '<EMAIL>',
          providerAttributes: {
            'name': 'José María García-López',
            'department': 'R&D',
            'location': 'São Paulo, Brazil',
            'notes': 'Special chars: áéíóú ñ ç œ æ ø',
          },
        );
        
        expect(specialCharsIdentity.externalUsername, contains('+'));
        expect(specialCharsIdentity.providerAttributes!['name'], contains('José'));
        expect(specialCharsIdentity.providerAttributes!['location'], contains('São Paulo'));
        
        final json = specialCharsIdentity.toJson();
        final deserialized = UserSSOIdentity.fromJson(json);
        expect(deserialized.providerAttributes!['name'], equals('José María García-López'));
      });

      test('should handle timestamp edge cases', () {
        final futureLogin = testIdentity.copyWith(
          lastLogin: DateTime.now().add(const Duration(days: 1)),
        );
        expect(futureLogin.hasRecentActivity, isTrue); // Future date is still "recent"
        
        final veryOldLogin = testIdentity.copyWith(
          lastLogin: DateTime.parse('1990-01-01T00:00:00.000Z'),
        );
        expect(veryOldLogin.hasRecentActivity, isFalse);
      });

      test('should handle provider attributes with null values', () {
        final attributesWithNulls = {
          'name': 'John Doe',
          'email': null,
          'department': '',
          'manager': null,
          'active': true,
          'metadata': null,
        };
        
        final nullAttributesIdentity = testIdentity.copyWith(
          providerAttributes: attributesWithNulls,
        );
        
        final json = nullAttributesIdentity.toJson();
        final deserialized = UserSSOIdentity.fromJson(json);
        
        expect(deserialized.providerAttributes!['name'], equals('John Doe'));
        expect(deserialized.providerAttributes!['email'], isNull);
        expect(deserialized.providerAttributes!['department'], isEmpty);
        expect(deserialized.providerAttributes!['active'], isTrue);
      });
    });
  });
}