import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Custom app bar for chat screens
class ChatAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Chat ID
  final String chatId;
  
  /// Chat name to display
  final String? chatName;
  
  /// Chat avatar URL
  final String? chatAvatarUrl;
  
  /// Online status of participants
  final bool isOnline;
  
  /// Last seen timestamp
  final DateTime? lastSeen;
  
  /// Number of participants
  final int participantCount;
  
  /// Callback when back button is pressed
  final VoidCallback? onBackPressed;
  
  /// Callback when chat info is pressed
  final VoidCallback? onInfoPressed;
  
  /// Callback when call button is pressed
  final VoidCallback? onCallPressed;
  
  /// Callback when video call button is pressed
  final VoidCallback? onVideoCallPressed;
  
  /// Callback when search button is pressed
  final VoidCallback? onSearchPressed;

  const ChatAppBar({
    super.key,
    required this.chatId,
    this.chatName,
    this.chatAvatarUrl,
    this.isOnline = false,
    this.lastSeen,
    this.participantCount = 0,
    this.onBackPressed,
    this.onInfoPressed,
    this.onCallPressed,
    this.onVideoCallPressed,
    this.onSearchPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
        icon: const Icon(Icons.arrow_back),
      ),
      title: GestureDetector(
        onTap: onInfoPressed,
        child: Row(
          children: [
            _buildChatAvatar(context),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    chatName ?? 'Chat',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  _buildSubtitle(context),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        if (onSearchPressed != null)
          IconButton(
            onPressed: onSearchPressed,
            icon: const Icon(Icons.search),
            tooltip: 'Search messages',
          ),
        if (onCallPressed != null)
          IconButton(
            onPressed: onCallPressed,
            icon: const Icon(Icons.call),
            tooltip: 'Voice call',
          ),
        if (onVideoCallPressed != null)
          IconButton(
            onPressed: onVideoCallPressed,
            icon: const Icon(Icons.videocam),
            tooltip: 'Video call',
          ),
        PopupMenuButton<String>(
          onSelected: _handleMenuSelection,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'info',
              child: ListTile(
                leading: Icon(Icons.info_outline),
                title: Text('Chat Info'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'search',
              child: ListTile(
                leading: Icon(Icons.search),
                title: Text('Search'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'mute',
              child: ListTile(
                leading: Icon(Icons.notifications_off),
                title: Text('Mute'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'clear',
              child: ListTile(
                leading: Icon(Icons.clear_all),
                title: Text('Clear Chat'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
      elevation: 1,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
    );
  }

  Widget _buildChatAvatar(BuildContext context) {
    if (chatAvatarUrl != null && chatAvatarUrl!.isNotEmpty) {
      return Stack(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundImage: NetworkImage(chatAvatarUrl!),
          ),
          if (isOnline)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.surface,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      );
    } else {
      return Stack(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: Text(
              _getChatInitials(),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (isOnline)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.surface,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      );
    }
  }

  Widget _buildSubtitle(BuildContext context) {
    String subtitleText;
    Color subtitleColor = Theme.of(context).colorScheme.onSurface.withOpacity(0.6);

    if (isOnline) {
      subtitleText = 'Online';
      subtitleColor = Colors.green;
    } else if (lastSeen != null) {
      subtitleText = 'Last seen ${_formatLastSeen(lastSeen!)}';
    } else if (participantCount > 0) {
      subtitleText = '$participantCount participants';
    } else {
      subtitleText = 'Offline';
    }

    return Text(
      subtitleText,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: subtitleColor,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  String _getChatInitials() {
    if (chatName == null || chatName!.isEmpty) {
      return '?';
    }
    
    final words = chatName!.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'.toUpperCase();
    }
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${lastSeen.day}/${lastSeen.month}/${lastSeen.year}';
    }
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'info':
        onInfoPressed?.call();
        break;
      case 'search':
        onSearchPressed?.call();
        break;
      case 'mute':
        // TODO: Implement mute functionality
        break;
      case 'clear':
        // TODO: Implement clear chat functionality
        break;
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Animated chat app bar with typing indicator
class AnimatedChatAppBar extends StatefulWidget implements PreferredSizeWidget {
  /// Chat ID
  final String chatId;
  
  /// Chat name to display
  final String? chatName;
  
  /// Whether someone is typing
  final bool isTyping;
  
  /// Names of users who are typing
  final List<String> typingUsers;
  
  /// Other properties from ChatAppBar
  final String? chatAvatarUrl;
  final bool isOnline;
  final DateTime? lastSeen;
  final int participantCount;
  final VoidCallback? onBackPressed;
  final VoidCallback? onInfoPressed;
  final VoidCallback? onCallPressed;
  final VoidCallback? onVideoCallPressed;
  final VoidCallback? onSearchPressed;

  const AnimatedChatAppBar({
    super.key,
    required this.chatId,
    this.chatName,
    this.isTyping = false,
    this.typingUsers = const [],
    this.chatAvatarUrl,
    this.isOnline = false,
    this.lastSeen,
    this.participantCount = 0,
    this.onBackPressed,
    this.onInfoPressed,
    this.onCallPressed,
    this.onVideoCallPressed,
    this.onSearchPressed,
  });

  @override
  State<AnimatedChatAppBar> createState() => _AnimatedChatAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _AnimatedChatAppBarState extends State<AnimatedChatAppBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(AnimatedChatAppBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isTyping != oldWidget.isTyping) {
      if (widget.isTyping) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChatAppBar(
      chatId: widget.chatId,
      chatName: widget.chatName,
      chatAvatarUrl: widget.chatAvatarUrl,
      isOnline: widget.isOnline,
      lastSeen: widget.lastSeen,
      participantCount: widget.participantCount,
      onBackPressed: widget.onBackPressed,
      onInfoPressed: widget.onInfoPressed,
      onCallPressed: widget.onCallPressed,
      onVideoCallPressed: widget.onVideoCallPressed,
      onSearchPressed: widget.onSearchPressed,
    );
  }
}
