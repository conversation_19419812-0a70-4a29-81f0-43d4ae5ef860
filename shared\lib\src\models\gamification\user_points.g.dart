// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_points.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PointTransaction _$PointTransactionFromJson(Map<String, dynamic> json) =>
    PointTransaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$PointTransactionTypeEnumMap, json['type']),
      pointsChanged: (json['pointsChanged'] as num).toInt(),
      sourceId: json['sourceId'] as String?,
      sourceType: json['sourceType'] as String?,
      description: json['description'] as String,
      multiplier: (json['multiplier'] as num).toDouble(),
      basePoints: (json['basePoints'] as num).toInt(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$PointTransactionToJson(PointTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$PointTransactionTypeEnumMap[instance.type]!,
      'pointsChanged': instance.pointsChanged,
      'sourceId': instance.sourceId,
      'sourceType': instance.sourceType,
      'description': instance.description,
      'multiplier': instance.multiplier,
      'basePoints': instance.basePoints,
      'metadata': instance.metadata,
      'timestamp': instance.timestamp.toIso8601String(),
    };

const _$PointTransactionTypeEnumMap = {
  PointTransactionType.earnedQuest: 'earned_quest',
  PointTransactionType.earnedTask: 'earned_task',
  PointTransactionType.earnedAchievement: 'earned_achievement',
  PointTransactionType.earnedStreak: 'earned_streak',
  PointTransactionType.earnedBonus: 'earned_bonus',
  PointTransactionType.spentReward: 'spent_reward',
  PointTransactionType.spentUpgrade: 'spent_upgrade',
  PointTransactionType.adjustmentAdmin: 'adjustment_admin',
  PointTransactionType.penalty: 'penalty',
};

UserPoints _$UserPointsFromJson(Map<String, dynamic> json) => UserPoints(
  userId: json['userId'] as String,
  totalPoints: (json['totalPoints'] as num).toInt(),
  availablePoints: (json['availablePoints'] as num).toInt(),
  spentPoints: (json['spentPoints'] as num).toInt(),
  currentLevel: (json['currentLevel'] as num).toInt(),
  currentLevelPoints: (json['currentLevelPoints'] as num).toInt(),
  pointsToNextLevel: (json['pointsToNextLevel'] as num).toInt(),
  dailyPoints: (json['dailyPoints'] as num).toInt(),
  weeklyPoints: (json['weeklyPoints'] as num).toInt(),
  monthlyPoints: (json['monthlyPoints'] as num).toInt(),
  bestDailyPoints: (json['bestDailyPoints'] as num).toInt(),
  streakMultiplier: (json['streakMultiplier'] as num).toDouble(),
  roleMultiplier: (json['roleMultiplier'] as num).toDouble(),
  achievementBonusPoints: (json['achievementBonusPoints'] as num).toInt(),
  dailyHistory: Map<String, int>.from(json['dailyHistory'] as Map),
  recentTransactions: (json['recentTransactions'] as List<dynamic>)
      .map((e) => PointTransaction.fromJson(e as Map<String, dynamic>))
      .toList(),
  statistics: json['statistics'] as Map<String, dynamic>?,
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
  createdAt: DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$UserPointsToJson(UserPoints instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'totalPoints': instance.totalPoints,
      'availablePoints': instance.availablePoints,
      'spentPoints': instance.spentPoints,
      'currentLevel': instance.currentLevel,
      'currentLevelPoints': instance.currentLevelPoints,
      'pointsToNextLevel': instance.pointsToNextLevel,
      'dailyPoints': instance.dailyPoints,
      'weeklyPoints': instance.weeklyPoints,
      'monthlyPoints': instance.monthlyPoints,
      'bestDailyPoints': instance.bestDailyPoints,
      'streakMultiplier': instance.streakMultiplier,
      'roleMultiplier': instance.roleMultiplier,
      'achievementBonusPoints': instance.achievementBonusPoints,
      'dailyHistory': instance.dailyHistory,
      'recentTransactions': instance.recentTransactions,
      'statistics': instance.statistics,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
    };
