// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ml_insights.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MLInsight _$MLInsightFromJson(Map<String, dynamic> json) => MLInsight(
  id: json['id'] as String,
  type: $enumDecode(_$InsightTypeEnumMap, json['type']),
  title: json['title'] as String,
  description: json['description'] as String,
  priority: $enumDecode(_$InsightPriorityEnumMap, json['priority']),
  actionabilityLevel: $enumDecode(
    _$ActionabilityLevelEnumMap,
    json['actionabilityLevel'],
  ),
  confidence: (json['confidence'] as num).toDouble(),
  impactScore: (json['impactScore'] as num).toDouble(),
  organizationId: json['organizationId'] as String?,
  entityId: json['entityId'] as String?,
  entityType: json['entityType'] as String?,
  data: json['data'] as Map<String, dynamic>,
  recommendedActions:
      (json['recommendedActions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  evidence:
      (json['evidence'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList() ??
      const [],
  relatedInsightIds:
      (json['relatedInsightIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  createdAt: DateTime.parse(json['createdAt'] as String),
  validUntil: json['validUntil'] == null
      ? null
      : DateTime.parse(json['validUntil'] as String),
  isActedUpon: json['isActedUpon'] as bool? ?? false,
  actionTakenAt: json['actionTakenAt'] == null
      ? null
      : DateTime.parse(json['actionTakenAt'] as String),
  feedback: json['feedback'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$MLInsightToJson(MLInsight instance) => <String, dynamic>{
  'id': instance.id,
  'type': _$InsightTypeEnumMap[instance.type]!,
  'title': instance.title,
  'description': instance.description,
  'priority': _$InsightPriorityEnumMap[instance.priority]!,
  'actionabilityLevel':
      _$ActionabilityLevelEnumMap[instance.actionabilityLevel]!,
  'confidence': instance.confidence,
  'impactScore': instance.impactScore,
  'organizationId': instance.organizationId,
  'entityId': instance.entityId,
  'entityType': instance.entityType,
  'data': instance.data,
  'recommendedActions': instance.recommendedActions,
  'evidence': instance.evidence,
  'relatedInsightIds': instance.relatedInsightIds,
  'tags': instance.tags,
  'createdAt': instance.createdAt.toIso8601String(),
  'validUntil': instance.validUntil?.toIso8601String(),
  'isActedUpon': instance.isActedUpon,
  'actionTakenAt': instance.actionTakenAt?.toIso8601String(),
  'feedback': instance.feedback,
};

const _$InsightTypeEnumMap = {
  InsightType.patternDetection: 'pattern_detection',
  InsightType.anomalyDetection: 'anomaly_detection',
  InsightType.trendAnalysis: 'trend_analysis',
  InsightType.correlationAnalysis: 'correlation_analysis',
  InsightType.optimizationOpportunity: 'optimization_opportunity',
  InsightType.riskAssessment: 'risk_assessment',
  InsightType.recommendation: 'recommendation',
};

const _$InsightPriorityEnumMap = {
  InsightPriority.low: 'low',
  InsightPriority.medium: 'medium',
  InsightPriority.high: 'high',
  InsightPriority.critical: 'critical',
};

const _$ActionabilityLevelEnumMap = {
  ActionabilityLevel.notActionable: 'not_actionable',
  ActionabilityLevel.needsInvestigation: 'needs_investigation',
  ActionabilityLevel.actionable: 'actionable',
  ActionabilityLevel.immediateAction: 'immediate_action',
};

SmartRecommendation _$SmartRecommendationFromJson(Map<String, dynamic> json) =>
    SmartRecommendation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      userId: json['userId'] as String?,
      organizationId: json['organizationId'] as String?,
      confidence: (json['confidence'] as num).toDouble(),
      expectedImpact: (json['expectedImpact'] as num).toDouble(),
      reasoning: json['reasoning'] as String,
      supportingData: json['supportingData'] as Map<String, dynamic>,
      actionSteps:
          (json['actionSteps'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      expectedOutcomes:
          (json['expectedOutcomes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      successMetrics:
          json['successMetrics'] as Map<String, dynamic>? ?? const {},
      prerequisites:
          (json['prerequisites'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      estimatedEffort: (json['estimatedEffort'] as num?)?.toDouble(),
      estimatedTimeframeDays: (json['estimatedTimeframeDays'] as num?)?.toInt(),
      category: json['category'] as String,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      isAccepted: json['isAccepted'] as bool? ?? false,
      isDismissed: json['isDismissed'] as bool? ?? false,
      implementationStatus: json['implementationStatus'] as String?,
    );

Map<String, dynamic> _$SmartRecommendationToJson(
  SmartRecommendation instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'type': instance.type,
  'userId': instance.userId,
  'organizationId': instance.organizationId,
  'confidence': instance.confidence,
  'expectedImpact': instance.expectedImpact,
  'reasoning': instance.reasoning,
  'supportingData': instance.supportingData,
  'actionSteps': instance.actionSteps,
  'expectedOutcomes': instance.expectedOutcomes,
  'successMetrics': instance.successMetrics,
  'prerequisites': instance.prerequisites,
  'estimatedEffort': instance.estimatedEffort,
  'estimatedTimeframeDays': instance.estimatedTimeframeDays,
  'category': instance.category,
  'tags': instance.tags,
  'createdAt': instance.createdAt.toIso8601String(),
  'expiresAt': instance.expiresAt?.toIso8601String(),
  'isAccepted': instance.isAccepted,
  'isDismissed': instance.isDismissed,
  'implementationStatus': instance.implementationStatus,
};
