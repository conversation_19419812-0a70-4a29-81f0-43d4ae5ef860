# Development Dockerfile for Quester Client
FROM ghcr.io/cirruslabs/flutter:latest AS development

RUN apt-get update && apt-get install -y wget curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app
RUN flutter config --enable-web

# Copy shared package first to the correct location
COPY shared/ ./shared/
COPY client/ ./client/

# Set working directory to client and install dependencies
WORKDIR /app/client
RUN flutter pub get

RUN groupadd -r quester && useradd -r -g quester quester -m
RUN chown -R quester:quester /app

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1

CMD ["flutter", "run", "-d", "web-server", "--web-hostname", "0.0.0.0", "--web-port", "3000"]
