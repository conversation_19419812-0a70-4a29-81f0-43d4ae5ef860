import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'analytics_metrics.g.dart';

/// Metric aggregation types for data calculation
enum MetricAggregationType {
  @JsonValue('sum')
  sum,
  @JsonValue('average')
  average,
  @JsonValue('count')
  count,
  @JsonValue('distinct_count')
  distinctCount,
  @JsonValue('min')
  min,
  @JsonValue('max')
  max,
  @JsonValue('median')
  median,
  @JsonValue('percentile')
  percentile,
}

/// Time period definitions for metric calculations
enum TimePeriod {
  @JsonValue('hourly')
  hourly,
  @JsonValue('daily')
  daily,
  @JsonValue('weekly')
  weekly,
  @JsonValue('monthly')
  monthly,
  @JsonValue('quarterly')
  quarterly,
  @JsonValue('yearly')
  yearly,
}

/// Pre-calculated analytics metric for fast dashboard queries
@JsonSerializable()
class AnalyticsMetrics extends Equatable {
  /// Unique metric identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// Metric name (e.g., 'daily_active_users', 'task_completion_rate')
  final String metricName;

  /// Metric category for grouping
  final String metricCategory;

  /// Calculated metric value
  final double metricValue;

  /// Number of data points used in calculation
  final int metricCount;

  /// Type of aggregation used
  final MetricAggregationType aggregationType;

  /// Time period this metric covers
  final TimePeriod timePeriod;

  /// Start of the time period
  final DateTime periodStart;

  /// End of the time period
  final DateTime periodEnd;

  /// Additional dimensions for filtering/grouping
  final Map<String, dynamic> dimensions;

  /// Metadata about metric calculation
  final Map<String, dynamic> metadata;

  /// When this metric was calculated
  final DateTime calculatedAt;

  const AnalyticsMetrics({
    required this.id,
    required this.organizationId,
    required this.metricName,
    required this.metricCategory,
    required this.metricValue,
    required this.metricCount,
    required this.aggregationType,
    required this.timePeriod,
    required this.periodStart,
    required this.periodEnd,
    required this.dimensions,
    required this.metadata,
    required this.calculatedAt,
  });

  /// Create AnalyticsMetrics from JSON
  factory AnalyticsMetrics.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsMetricsFromJson(json);

  /// Convert AnalyticsMetrics to JSON
  Map<String, dynamic> toJson() => _$AnalyticsMetricsToJson(this);

  /// Create a copy with updated fields
  AnalyticsMetrics copyWith({
    String? id,
    String? organizationId,
    String? metricName,
    String? metricCategory,
    double? metricValue,
    int? metricCount,
    MetricAggregationType? aggregationType,
    TimePeriod? timePeriod,
    DateTime? periodStart,
    DateTime? periodEnd,
    Map<String, dynamic>? dimensions,
    Map<String, dynamic>? metadata,
    DateTime? calculatedAt,
  }) {
    return AnalyticsMetrics(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      metricName: metricName ?? this.metricName,
      metricCategory: metricCategory ?? this.metricCategory,
      metricValue: metricValue ?? this.metricValue,
      metricCount: metricCount ?? this.metricCount,
      aggregationType: aggregationType ?? this.aggregationType,
      timePeriod: timePeriod ?? this.timePeriod,
      periodStart: periodStart ?? this.periodStart,
      periodEnd: periodEnd ?? this.periodEnd,
      dimensions: dimensions ?? this.dimensions,
      metadata: metadata ?? this.metadata,
      calculatedAt: calculatedAt ?? this.calculatedAt,
    );
  }

  /// Get formatted metric value based on metric type
  String get formattedValue {
    if (metricName.contains('rate') || metricName.contains('percentage')) {
      return '${(metricValue * 100).toStringAsFixed(1)}%';
    }
    
    if (metricName.contains('duration') || metricName.contains('time')) {
      return _formatDuration(metricValue.toInt());
    }
    
    if (metricValue >= 1000000) {
      return '${(metricValue / 1000000).toStringAsFixed(1)}M';
    }
    
    if (metricValue >= 1000) {
      return '${(metricValue / 1000).toStringAsFixed(1)}K';
    }
    
    return metricValue.toStringAsFixed(0);
  }

  /// Format duration in seconds to human readable format
  String _formatDuration(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      return '${(seconds / 60).toStringAsFixed(0)}m';
    } else {
      return '${(seconds / 3600).toStringAsFixed(1)}h';
    }
  }

  /// Get period label for display
  String get periodLabel {
    switch (timePeriod) {
      case TimePeriod.hourly:
        return 'Hourly';
      case TimePeriod.daily:
        return 'Daily';
      case TimePeriod.weekly:
        return 'Weekly';
      case TimePeriod.monthly:
        return 'Monthly';
      case TimePeriod.quarterly:
        return 'Quarterly';
      case TimePeriod.yearly:
        return 'Yearly';
    }
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        metricName,
        metricCategory,
        metricValue,
        metricCount,
        aggregationType,
        timePeriod,
        periodStart,
        periodEnd,
        dimensions,
        metadata,
        calculatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Dashboard metrics summary for quick overview
@JsonSerializable()
class DashboardMetrics extends Equatable {
  /// Organization identifier
  final String organizationId;

  /// Time range for metrics
  final DateTimeRange timeRange;

  /// Active users in period
  final int activeUsers;

  /// Total events in period
  final int totalEvents;

  /// Task completion rate
  final double taskCompletionRate;

  /// Quest completion rate
  final double questCompletionRate;

  /// Average engagement score
  final double avgEngagementScore;

  /// Achievement unlock count
  final int achievementUnlocks;

  /// Collaboration actions count
  final int collaborationActions;

  /// Top performing users
  final List<UserMetricsSummary> topUsers;

  /// Trending metrics
  final List<MetricTrend> trends;

  /// When these metrics were calculated
  final DateTime calculatedAt;

  const DashboardMetrics({
    required this.organizationId,
    required this.timeRange,
    required this.activeUsers,
    required this.totalEvents,
    required this.taskCompletionRate,
    required this.questCompletionRate,
    required this.avgEngagementScore,
    required this.achievementUnlocks,
    required this.collaborationActions,
    required this.topUsers,
    required this.trends,
    required this.calculatedAt,
  });

  /// Create DashboardMetrics from JSON
  factory DashboardMetrics.fromJson(Map<String, dynamic> json) =>
      _$DashboardMetricsFromJson(json);

  /// Convert DashboardMetrics to JSON
  Map<String, dynamic> toJson() => _$DashboardMetricsToJson(this);

  @override
  List<Object?> get props => [
        organizationId,
        timeRange,
        activeUsers,
        totalEvents,
        taskCompletionRate,
        questCompletionRate,
        avgEngagementScore,
        achievementUnlocks,
        collaborationActions,
        topUsers,
        trends,
        calculatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Date time range for metric queries
@JsonSerializable()
class DateTimeRange extends Equatable {
  /// Start of the time range
  final DateTime start;

  /// End of the time range
  final DateTime end;

  const DateTimeRange({
    required this.start,
    required this.end,
  });

  /// Create DateTimeRange from JSON
  factory DateTimeRange.fromJson(Map<String, dynamic> json) =>
      _$DateTimeRangeFromJson(json);

  /// Convert DateTimeRange to JSON
  Map<String, dynamic> toJson() => _$DateTimeRangeToJson(this);

  /// Duration of this time range
  Duration get duration => end.difference(start);

  /// Get period type based on duration
  TimePeriod get suggestedPeriod {
    final days = duration.inDays;
    if (days <= 2) return TimePeriod.hourly;
    if (days <= 31) return TimePeriod.daily;
    if (days <= 93) return TimePeriod.weekly;
    if (days <= 366) return TimePeriod.monthly;
    return TimePeriod.yearly;
  }

  /// Common time ranges
  static DateTimeRange last7Days() {
    final end = DateTime.now().toUtc();
    final start = end.subtract(const Duration(days: 7));
    return DateTimeRange(start: start, end: end);
  }

  static DateTimeRange last30Days() {
    final end = DateTime.now().toUtc();
    final start = end.subtract(const Duration(days: 30));
    return DateTimeRange(start: start, end: end);
  }

  static DateTimeRange last90Days() {
    final end = DateTime.now().toUtc();
    final start = end.subtract(const Duration(days: 90));
    return DateTimeRange(start: start, end: end);
  }

  static DateTimeRange thisMonth() {
    final now = DateTime.now().toUtc();
    final start = DateTime(now.year, now.month, 1).toUtc();
    final end = DateTime(now.year, now.month + 1, 0, 23, 59, 59).toUtc();
    return DateTimeRange(start: start, end: end);
  }

  @override
  List<Object?> get props => [start, end];

  @override
  bool get stringify => true;
}

/// User metrics summary for leaderboards and top performers
@JsonSerializable()
class UserMetricsSummary extends Equatable {
  /// User identifier
  final String userId;

  /// User display name
  final String userName;

  /// User email
  final String? userEmail;

  /// Total actions by this user
  final int totalActions;

  /// Tasks completed
  final int tasksCompleted;

  /// Quests completed
  final int questsCompleted;

  /// Achievements unlocked
  final int achievementsUnlocked;

  /// Engagement score
  final double engagementScore;

  /// Last activity timestamp
  final DateTime? lastActivity;

  const UserMetricsSummary({
    required this.userId,
    required this.userName,
    this.userEmail,
    required this.totalActions,
    required this.tasksCompleted,
    required this.questsCompleted,
    required this.achievementsUnlocked,
    required this.engagementScore,
    this.lastActivity,
  });

  /// Create UserMetricsSummary from JSON
  factory UserMetricsSummary.fromJson(Map<String, dynamic> json) =>
      _$UserMetricsSummaryFromJson(json);

  /// Convert UserMetricsSummary to JSON
  Map<String, dynamic> toJson() => _$UserMetricsSummaryToJson(this);

  @override
  List<Object?> get props => [
        userId,
        userName,
        userEmail,
        totalActions,
        tasksCompleted,
        questsCompleted,
        achievementsUnlocked,
        engagementScore,
        lastActivity,
      ];

  @override
  bool get stringify => true;
}

/// Metric trend information for showing growth/decline
@JsonSerializable()
class MetricTrend extends Equatable {
  /// Metric name
  final String metricName;

  /// Current value
  final double currentValue;

  /// Previous value for comparison
  final double previousValue;

  /// Percentage change
  final double percentageChange;

  /// Trend direction
  final TrendDirection direction;

  /// Is this trend significant (based on confidence threshold)
  final bool isSignificant;

  const MetricTrend({
    required this.metricName,
    required this.currentValue,
    required this.previousValue,
    required this.percentageChange,
    required this.direction,
    required this.isSignificant,
  });

  /// Create MetricTrend from JSON
  factory MetricTrend.fromJson(Map<String, dynamic> json) =>
      _$MetricTrendFromJson(json);

  /// Convert MetricTrend to JSON
  Map<String, dynamic> toJson() => _$MetricTrendToJson(this);

  /// Create trend from current and previous values
  factory MetricTrend.calculate({
    required String metricName,
    required double currentValue,
    required double previousValue,
    double significanceThreshold = 5.0,
  }) {
    final percentageChange = previousValue != 0
        ? ((currentValue - previousValue) / previousValue) * 100
        : 0.0;

    final direction = percentageChange > 0
        ? TrendDirection.up
        : percentageChange < 0
            ? TrendDirection.down
            : TrendDirection.stable;

    final isSignificant = percentageChange.abs() >= significanceThreshold;

    return MetricTrend(
      metricName: metricName,
      currentValue: currentValue,
      previousValue: previousValue,
      percentageChange: percentageChange,
      direction: direction,
      isSignificant: isSignificant,
    );
  }

  @override
  List<Object?> get props => [
        metricName,
        currentValue,
        previousValue,
        percentageChange,
        direction,
        isSignificant,
      ];

  @override
  bool get stringify => true;
}

/// Trend direction enumeration
enum TrendDirection {
  @JsonValue('up')
  up,
  @JsonValue('down')
  down,
  @JsonValue('stable')
  stable,
}