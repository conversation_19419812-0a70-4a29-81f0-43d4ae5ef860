import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'auth_dto.g.dart';

/// Login request DTO
@JsonSerializable()
class LoginRequestDto extends Equatable {
  final String email;
  final String password;
  final bool rememberMe;

  const LoginRequestDto({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  factory LoginRequestDto.fromJson(Map<String, dynamic> json) => _$LoginRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestDtoToJson(this);

  @override
  List<Object?> get props => [email, password, rememberMe];
}

/// Registration request DTO
@JsonSerializable()
class RegisterRequestDto extends Equatable {
  final String email;
  final String password;
  final String displayName;
  final String? firstName;
  final String? lastName;

  const RegisterRequestDto({
    required this.email,
    required this.password,
    required this.displayName,
    this.firstName,
    this.lastName,
  });

  factory RegisterRequestDto.fromJson(Map<String, dynamic> json) => _$RegisterRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$RegisterRequestDtoToJson(this);

  @override
  List<Object?> get props => [email, password, displayName, firstName, lastName];
}

/// Authentication response DTO
@JsonSerializable()
class AuthResponseDto extends Equatable {
  final String token;
  final String refreshToken;
  final String userId;
  final String email;
  final String displayName;
  final String? avatarUrl;
  final String role;
  final DateTime expiresAt;
  final Map<String, dynamic>? userProfile;

  const AuthResponseDto({
    required this.token,
    required this.refreshToken,
    required this.userId,
    required this.email,
    required this.displayName,
    this.avatarUrl,
    required this.role,
    required this.expiresAt,
    this.userProfile,
  });

  factory AuthResponseDto.fromJson(Map<String, dynamic> json) => _$AuthResponseDtoFromJson(json);
  Map<String, dynamic> toJson() => _$AuthResponseDtoToJson(this);

  @override
  List<Object?> get props => [
        token,
        refreshToken,
        userId,
        email,
        displayName,
        avatarUrl,
        role,
        expiresAt,
        userProfile,
      ];
}

/// Password reset request DTO
@JsonSerializable()
class PasswordResetRequestDto extends Equatable {
  final String email;

  const PasswordResetRequestDto({required this.email});

  factory PasswordResetRequestDto.fromJson(Map<String, dynamic> json) => _$PasswordResetRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$PasswordResetRequestDtoToJson(this);

  @override
  List<Object?> get props => [email];
}

/// Password reset confirmation DTO
@JsonSerializable()
class PasswordResetConfirmDto extends Equatable {
  final String token;
  final String newPassword;

  const PasswordResetConfirmDto({
    required this.token,
    required this.newPassword,
  });

  factory PasswordResetConfirmDto.fromJson(Map<String, dynamic> json) => _$PasswordResetConfirmDtoFromJson(json);
  Map<String, dynamic> toJson() => _$PasswordResetConfirmDtoToJson(this);

  @override
  List<Object?> get props => [token, newPassword];
}

/// Token refresh request DTO
@JsonSerializable()
class RefreshTokenRequestDto extends Equatable {
  final String refreshToken;

  const RefreshTokenRequestDto({required this.refreshToken});

  factory RefreshTokenRequestDto.fromJson(Map<String, dynamic> json) => _$RefreshTokenRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$RefreshTokenRequestDtoToJson(this);

  @override
  List<Object?> get props => [refreshToken];
}

/// Profile update DTO
@JsonSerializable()
class ProfileUpdateDto extends Equatable {
  final String? displayName;
  final String? firstName;
  final String? lastName;
  final String? avatarUrl;
  final Map<String, dynamic>? preferences;

  const ProfileUpdateDto({
    this.displayName,
    this.firstName,
    this.lastName,
    this.avatarUrl,
    this.preferences,
  });

  factory ProfileUpdateDto.fromJson(Map<String, dynamic> json) => _$ProfileUpdateDtoFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileUpdateDtoToJson(this);

  @override
  List<Object?> get props => [displayName, firstName, lastName, avatarUrl, preferences];
}

/// Change password DTO
@JsonSerializable()
class ChangePasswordDto extends Equatable {
  final String currentPassword;
  final String newPassword;

  const ChangePasswordDto({
    required this.currentPassword,
    required this.newPassword,
  });

  factory ChangePasswordDto.fromJson(Map<String, dynamic> json) => _$ChangePasswordDtoFromJson(json);
  Map<String, dynamic> toJson() => _$ChangePasswordDtoToJson(this);

  @override
  List<Object?> get props => [currentPassword, newPassword];
}