-- Authentication tokens schema
-- Email verification tokens
CREATE TABLE IF NOT EXISTS quester.email_verification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES quester.users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    used_at TIMESTAMPTZ NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Password reset tokens
CREATE TABLE IF NOT EXISTS quester.password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES quester.users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    used_at TIMESTAMPTZ NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Invitation tokens
CREATE TABLE IF NOT EXISTS quester.invitation_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES quester.organizations(id) ON DELETE CASCADE,
    role_id UUID NULL REFERENCES quester.organization_roles(id) ON DELETE SET NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    invited_email VARCHAR(255) NOT NULL,
    invited_by_user_id UUID NOT NULL REFERENCES quester.users(id) ON DELETE CASCADE,
    expires_at TIMESTAMPTZ NOT NULL,
    used_at TIMESTAMPTZ NULL,
    used_by_user_id UUID NULL REFERENCES quester.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token ON quester.email_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_email ON quester.email_verification_tokens(email);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON quester.email_verification_tokens(expires_at);

CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON quester.password_reset_tokens(token);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_email ON quester.password_reset_tokens(email);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON quester.password_reset_tokens(expires_at);

CREATE INDEX IF NOT EXISTS idx_invitation_tokens_token ON quester.invitation_tokens(token);
CREATE INDEX IF NOT EXISTS idx_invitation_tokens_email ON quester.invitation_tokens(invited_email);
CREATE INDEX IF NOT EXISTS idx_invitation_tokens_expires_at ON quester.invitation_tokens(expires_at);

-- Auto-update timestamps
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_email_verification_tokens_modtime 
    BEFORE UPDATE ON quester.email_verification_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_password_reset_tokens_modtime 
    BEFORE UPDATE ON quester.password_reset_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_invitation_tokens_modtime 
    BEFORE UPDATE ON quester.invitation_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Cleanup expired tokens (run periodically)
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS void AS $$
BEGIN
    -- Clean up expired email verification tokens (older than 7 days)
    DELETE FROM quester.email_verification_tokens 
    WHERE expires_at < NOW() - INTERVAL '7 days';
    
    -- Clean up expired password reset tokens (older than 1 day)  
    DELETE FROM quester.password_reset_tokens 
    WHERE expires_at < NOW() - INTERVAL '1 day';
    
    -- Clean up expired invitation tokens (older than 30 days)
    DELETE FROM quester.invitation_tokens 
    WHERE expires_at < NOW() - INTERVAL '30 days';
    
    RAISE NOTICE 'Expired tokens cleaned up';
END;
$$ language 'plpgsql';