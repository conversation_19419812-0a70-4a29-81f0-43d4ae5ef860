// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceCapabilities _$DeviceCapabilitiesFromJson(Map<String, dynamic> json) =>
    DeviceCapabilities(
      platform: $enumDecode(_$PlatformEnumMap, json['platform']),
      formFactor: $enumDecode(_$DeviceFormFactorEnumMap, json['formFactor']),
      screenWidth: (json['screenWidth'] as num).toDouble(),
      screenHeight: (json['screenHeight'] as num).toDouble(),
      devicePixelRatio: (json['devicePixelRatio'] as num).toDouble(),
      supportsTouch: json['supportsTouch'] as bool? ?? true,
      supportsHaptics: json['supportsHaptics'] as bool? ?? false,
      hasGPS: json['hasGPS'] as bool? ?? false,
      hasCamera: json['hasCamera'] as bool? ?? false,
      hasMicrophone: json['hasMicrophone'] as bool? ?? false,
      supportsBiometrics: json['supportsBiometrics'] as bool? ?? false,
      supportsNotifications: json['supportsNotifications'] as bool? ?? true,
      supportsBackgroundSync: json['supportsBackgroundSync'] as bool? ?? false,
      availableStorageMB: (json['availableStorageMB'] as num?)?.toInt(),
      ramMB: (json['ramMB'] as num?)?.toInt(),
      cpuCores: (json['cpuCores'] as num?)?.toInt(),
      performanceTier:
          $enumDecodeNullable(
            _$PerformanceTierEnumMap,
            json['performanceTier'],
          ) ??
          PerformanceTier.midRange,
      supportedWebApis:
          (json['supportedWebApis'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$DeviceCapabilitiesToJson(DeviceCapabilities instance) =>
    <String, dynamic>{
      'platform': _$PlatformEnumMap[instance.platform]!,
      'formFactor': _$DeviceFormFactorEnumMap[instance.formFactor]!,
      'screenWidth': instance.screenWidth,
      'screenHeight': instance.screenHeight,
      'devicePixelRatio': instance.devicePixelRatio,
      'supportsTouch': instance.supportsTouch,
      'supportsHaptics': instance.supportsHaptics,
      'hasGPS': instance.hasGPS,
      'hasCamera': instance.hasCamera,
      'hasMicrophone': instance.hasMicrophone,
      'supportsBiometrics': instance.supportsBiometrics,
      'supportsNotifications': instance.supportsNotifications,
      'supportsBackgroundSync': instance.supportsBackgroundSync,
      'availableStorageMB': instance.availableStorageMB,
      'ramMB': instance.ramMB,
      'cpuCores': instance.cpuCores,
      'performanceTier': _$PerformanceTierEnumMap[instance.performanceTier]!,
      'supportedWebApis': instance.supportedWebApis,
    };

const _$PlatformEnumMap = {
  Platform.android: 'android',
  Platform.ios: 'ios',
  Platform.web: 'web',
  Platform.windows: 'windows',
  Platform.macos: 'macos',
  Platform.linux: 'linux',
};

const _$DeviceFormFactorEnumMap = {
  DeviceFormFactor.phone: 'phone',
  DeviceFormFactor.tablet: 'tablet',
  DeviceFormFactor.desktop: 'desktop',
  DeviceFormFactor.tv: 'tv',
  DeviceFormFactor.watch: 'watch',
};

const _$PerformanceTierEnumMap = {
  PerformanceTier.lowEnd: 'low_end',
  PerformanceTier.midRange: 'mid_range',
  PerformanceTier.highEnd: 'high_end',
  PerformanceTier.flagship: 'flagship',
};

OfflineConfig _$OfflineConfigFromJson(Map<String, dynamic> json) =>
    OfflineConfig(
      isEnabled: json['isEnabled'] as bool? ?? true,
      maxCacheSizeMB: (json['maxCacheSizeMB'] as num?)?.toInt() ?? 100,
      cacheRetentionDays: (json['cacheRetentionDays'] as num?)?.toInt() ?? 7,
      syncDataTypes:
          (json['syncDataTypes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['quests', 'tasks', 'achievements'],
      storageStrategy: json['storageStrategy'] as String? ?? 'hybrid',
      maxOfflineActions: (json['maxOfflineActions'] as num?)?.toInt() ?? 50,
      showOfflineIndicator: json['showOfflineIndicator'] as bool? ?? true,
      fallbackAssets:
          (json['fallbackAssets'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
    );

Map<String, dynamic> _$OfflineConfigToJson(OfflineConfig instance) =>
    <String, dynamic>{
      'isEnabled': instance.isEnabled,
      'maxCacheSizeMB': instance.maxCacheSizeMB,
      'cacheRetentionDays': instance.cacheRetentionDays,
      'syncDataTypes': instance.syncDataTypes,
      'storageStrategy': instance.storageStrategy,
      'maxOfflineActions': instance.maxOfflineActions,
      'showOfflineIndicator': instance.showOfflineIndicator,
      'fallbackAssets': instance.fallbackAssets,
    };

NotificationConfig _$NotificationConfigFromJson(Map<String, dynamic> json) =>
    NotificationConfig(
      isEnabled: json['isEnabled'] as bool? ?? true,
      pushToken: json['pushToken'] as String?,
      enabledCategories:
          (json['enabledCategories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['achievements', 'tasks', 'quests'],
      quietHoursStart: json['quietHoursStart'] as String?,
      quietHoursEnd: json['quietHoursEnd'] as String?,
      showBadges: json['showBadges'] as bool? ?? true,
      playSound: json['playSound'] as bool? ?? true,
      vibrate: json['vibrate'] as bool? ?? true,
      customPreferences:
          (json['customPreferences'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as Map<String, dynamic>),
          ) ??
          const {},
    );

Map<String, dynamic> _$NotificationConfigToJson(NotificationConfig instance) =>
    <String, dynamic>{
      'isEnabled': instance.isEnabled,
      'pushToken': instance.pushToken,
      'enabledCategories': instance.enabledCategories,
      'quietHoursStart': instance.quietHoursStart,
      'quietHoursEnd': instance.quietHoursEnd,
      'showBadges': instance.showBadges,
      'playSound': instance.playSound,
      'vibrate': instance.vibrate,
      'customPreferences': instance.customPreferences,
    };

PerformanceConfig _$PerformanceConfigFromJson(Map<String, dynamic> json) =>
    PerformanceConfig(
      enableImageOptimization: json['enableImageOptimization'] as bool? ?? true,
      imageQuality: (json['imageQuality'] as num?)?.toDouble() ?? 0.8,
      enableLazyLoading: json['enableLazyLoading'] as bool? ?? true,
      reduceAnimations: json['reduceAnimations'] as bool? ?? false,
      targetFrameRate: (json['targetFrameRate'] as num?)?.toInt() ?? 60,
      enableHardwareAcceleration:
          json['enableHardwareAcceleration'] as bool? ?? true,
      memoryLimitMB: (json['memoryLimitMB'] as num?)?.toInt() ?? 256,
      preloadCriticalData: json['preloadCriticalData'] as bool? ?? true,
      batteryOptimizationLevel:
          (json['batteryOptimizationLevel'] as num?)?.toInt() ?? 1,
      optimizeDataUsage: json['optimizeDataUsage'] as bool? ?? false,
    );

Map<String, dynamic> _$PerformanceConfigToJson(PerformanceConfig instance) =>
    <String, dynamic>{
      'enableImageOptimization': instance.enableImageOptimization,
      'imageQuality': instance.imageQuality,
      'enableLazyLoading': instance.enableLazyLoading,
      'reduceAnimations': instance.reduceAnimations,
      'targetFrameRate': instance.targetFrameRate,
      'enableHardwareAcceleration': instance.enableHardwareAcceleration,
      'memoryLimitMB': instance.memoryLimitMB,
      'preloadCriticalData': instance.preloadCriticalData,
      'batteryOptimizationLevel': instance.batteryOptimizationLevel,
      'optimizeDataUsage': instance.optimizeDataUsage,
    };

AccessibilityConfig _$AccessibilityConfigFromJson(Map<String, dynamic> json) =>
    AccessibilityConfig(
      fontScale: (json['fontScale'] as num?)?.toDouble() ?? 1.0,
      enableHighContrast: json['enableHighContrast'] as bool? ?? false,
      reduceMotion: json['reduceMotion'] as bool? ?? false,
      screenReaderEnabled: json['screenReaderEnabled'] as bool? ?? false,
      readingOrder: json['readingOrder'] as String? ?? 'ltr',
      voiceControlEnabled: json['voiceControlEnabled'] as bool? ?? false,
      switchControlEnabled: json['switchControlEnabled'] as bool? ?? false,
      colorBlindAssistance: json['colorBlindAssistance'] as String?,
      customSettings:
          json['customSettings'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$AccessibilityConfigToJson(
  AccessibilityConfig instance,
) => <String, dynamic>{
  'fontScale': instance.fontScale,
  'enableHighContrast': instance.enableHighContrast,
  'reduceMotion': instance.reduceMotion,
  'screenReaderEnabled': instance.screenReaderEnabled,
  'readingOrder': instance.readingOrder,
  'voiceControlEnabled': instance.voiceControlEnabled,
  'switchControlEnabled': instance.switchControlEnabled,
  'colorBlindAssistance': instance.colorBlindAssistance,
  'customSettings': instance.customSettings,
};

MobileConfig _$MobileConfigFromJson(Map<String, dynamic> json) => MobileConfig(
  deviceCapabilities: DeviceCapabilities.fromJson(
    json['deviceCapabilities'] as Map<String, dynamic>,
  ),
  offlineConfig: json['offlineConfig'] == null
      ? const OfflineConfig()
      : OfflineConfig.fromJson(json['offlineConfig'] as Map<String, dynamic>),
  notificationConfig: json['notificationConfig'] == null
      ? const NotificationConfig()
      : NotificationConfig.fromJson(
          json['notificationConfig'] as Map<String, dynamic>,
        ),
  performanceConfig: json['performanceConfig'] == null
      ? const PerformanceConfig()
      : PerformanceConfig.fromJson(
          json['performanceConfig'] as Map<String, dynamic>,
        ),
  accessibilityConfig: json['accessibilityConfig'] == null
      ? const AccessibilityConfig()
      : AccessibilityConfig.fromJson(
          json['accessibilityConfig'] as Map<String, dynamic>,
        ),
  themeMode:
      $enumDecodeNullable(_$ThemeModeEnumMap, json['themeMode']) ??
      ThemeMode.system,
  locale: json['locale'] as String? ?? 'en-US',
  appVersion: json['appVersion'] as String,
  configVersion: json['configVersion'] as String,
  userId: json['userId'] as String?,
  organizationId: json['organizationId'] as String?,
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
  mobileFeatureFlags:
      (json['mobileFeatureFlags'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as bool),
      ) ??
      const {},
  customSettings: json['customSettings'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$MobileConfigToJson(MobileConfig instance) =>
    <String, dynamic>{
      'deviceCapabilities': instance.deviceCapabilities,
      'offlineConfig': instance.offlineConfig,
      'notificationConfig': instance.notificationConfig,
      'performanceConfig': instance.performanceConfig,
      'accessibilityConfig': instance.accessibilityConfig,
      'themeMode': _$ThemeModeEnumMap[instance.themeMode]!,
      'locale': instance.locale,
      'appVersion': instance.appVersion,
      'configVersion': instance.configVersion,
      'userId': instance.userId,
      'organizationId': instance.organizationId,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'mobileFeatureFlags': instance.mobileFeatureFlags,
      'customSettings': instance.customSettings,
    };

const _$ThemeModeEnumMap = {
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
  ThemeMode.system: 'system',
  ThemeMode.auto: 'auto',
};
