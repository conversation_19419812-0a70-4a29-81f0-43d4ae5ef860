-- Enterprise features schema for Quester platform
-- Adds compliance, SSO, API management, and GDPR support

-- Audit events table for compliance tracking
CREATE TABLE IF NOT EXISTS quester.audit_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'low',
    description TEXT NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    metadata JSONB,
    request_details JSONB,
    response_details JSONB,
    geolocation JSONB,
    requires_attention BOOLEAN DEFAULT FALSE,
    compliance_frameworks TEXT[] DEFAULT '{}',
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Compliance reports table
CREATE TABLE IF NOT EXISTS quester.compliance_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    framework VARCHAR(20) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    compliance_score DECIMAL(5,2) DEFAULT 0.00,
    total_requirements INTEGER DEFAULT 0,
    compliant_requirements INTEGER DEFAULT 0,
    non_compliant_requirements INTEGER DEFAULT 0,
    pending_requirements INTEGER DEFAULT 0,
    findings JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    evidence_files JSONB DEFAULT '[]',
    risk_assessment JSONB DEFAULT '{}',
    action_items JSONB DEFAULT '[]',
    metadata JSONB,
    generated_by UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    generated_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data processing records for GDPR compliance
CREATE TABLE IF NOT EXISTS quester.data_processing_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    data_controller VARCHAR(255) NOT NULL,
    data_processor VARCHAR(255),
    activity_name VARCHAR(255) NOT NULL,
    purpose VARCHAR(50) NOT NULL,
    legal_basis TEXT NOT NULL,
    data_categories TEXT[] DEFAULT '{}',
    data_sources TEXT[] DEFAULT '{}',
    data_recipients TEXT[] DEFAULT '{}',
    third_country_transfers TEXT[] DEFAULT '{}',
    retention_period VARCHAR(255),
    security_measures TEXT[] DEFAULT '{}',
    dpia_required BOOLEAN DEFAULT FALSE,
    dpia_reference VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Consent records for GDPR compliance
CREATE TABLE IF NOT EXISTS quester.consent_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    data_subject_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    purpose VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    consent_given_at TIMESTAMPTZ,
    consent_withdrawn_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    consent_method VARCHAR(100) NOT NULL,
    consent_version VARCHAR(20) NOT NULL,
    privacy_policy_version VARCHAR(20) NOT NULL,
    granular_consents JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    consent_evidence JSONB,
    withdrawal_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data subject requests for GDPR compliance
CREATE TABLE IF NOT EXISTS quester.data_subject_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    data_subject_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    right_type VARCHAR(30) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    description TEXT NOT NULL,
    requested_data_categories TEXT[],
    verification_method VARCHAR(100) NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMPTZ,
    submitted_at TIMESTAMPTZ DEFAULT NOW(),
    deadline TIMESTAMPTZ NOT NULL,
    completed_at TIMESTAMPTZ,
    response_data JSONB,
    response_files TEXT[],
    rejection_reason TEXT,
    metadata JSONB,
    processing_notes TEXT,
    assigned_to UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- SSO configurations for enterprise authentication
CREATE TABLE IF NOT EXISTS quester.sso_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    provider VARCHAR(20) NOT NULL,
    provider_name VARCHAR(100) NOT NULL,
    enabled BOOLEAN DEFAULT FALSE,
    saml_configuration JSONB,
    oauth_configuration JSONB,
    attribute_mapping JSONB DEFAULT '{}',
    default_role VARCHAR(50) DEFAULT 'member',
    auto_provision BOOLEAN DEFAULT TRUE,
    enforce_sso BOOLEAN DEFAULT FALSE,
    metadata JSONB,
    created_by UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- API keys for API management
CREATE TABLE IF NOT EXISTS quester.api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_value VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(20) DEFAULT 'read',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    last_used_at TIMESTAMPTZ,
    usage_count INTEGER DEFAULT 0,
    rate_limits JSONB DEFAULT '{}',
    allowed_ips TEXT[],
    allowed_domains TEXT[],
    scopes TEXT[] DEFAULT '{"read"}',
    created_by UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    metadata JSONB,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Rate limits for API management
CREATE TABLE IF NOT EXISTS quester.rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    api_key_id UUID REFERENCES quester.api_keys(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL,
    max_requests INTEGER NOT NULL,
    time_window_seconds INTEGER NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    endpoints TEXT[] DEFAULT '{}',
    ip_addresses TEXT[],
    user_roles TEXT[],
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Webhooks for API management
CREATE TABLE IF NOT EXISTS quester.webhooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    events TEXT[] DEFAULT '{}',
    secret VARCHAR(255) NOT NULL,
    headers JSONB DEFAULT '{}',
    max_retries INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 60,
    timeout INTEGER DEFAULT 30,
    verify_ssl BOOLEAN DEFAULT TRUE,
    last_success_at TIMESTAMPTZ,
    last_failure_at TIMESTAMPTZ,
    failure_count INTEGER DEFAULT 0,
    delivery_count INTEGER DEFAULT 0,
    metadata JSONB,
    created_by UUID REFERENCES quester.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Webhook delivery log
CREATE TABLE IF NOT EXISTS quester.webhook_deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    webhook_id UUID REFERENCES quester.webhooks(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    payload JSONB NOT NULL,
    response_status_code INTEGER,
    response_body TEXT,
    response_headers JSONB,
    attempt_number INTEGER DEFAULT 1,
    delivered_at TIMESTAMPTZ DEFAULT NOW(),
    response_time_ms INTEGER,
    successful BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    metadata JSONB
);

-- API usage log for rate limiting
CREATE TABLE IF NOT EXISTS quester.api_usage_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE CASCADE,
    api_key_id UUID REFERENCES quester.api_keys(id) ON DELETE SET NULL,
    rate_limit_id UUID REFERENCES quester.rate_limits(id) ON DELETE SET NULL,
    endpoint VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    response_status INTEGER,
    response_time_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_audit_events_org_timestamp ON quester.audit_events(organization_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_events_user_timestamp ON quester.audit_events(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_events_event_type ON quester.audit_events(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_events_severity ON quester.audit_events(severity);
CREATE INDEX IF NOT EXISTS idx_audit_events_compliance ON quester.audit_events USING GIN(compliance_frameworks);

CREATE INDEX IF NOT EXISTS idx_compliance_reports_org_framework ON quester.compliance_reports(organization_id, framework);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_generated_at ON quester.compliance_reports(generated_at DESC);

CREATE INDEX IF NOT EXISTS idx_consent_records_data_subject ON quester.consent_records(data_subject_id);
CREATE INDEX IF NOT EXISTS idx_consent_records_org_purpose ON quester.consent_records(organization_id, purpose);
CREATE INDEX IF NOT EXISTS idx_consent_records_status ON quester.consent_records(status);

CREATE INDEX IF NOT EXISTS idx_data_subject_requests_org ON quester.data_subject_requests(organization_id);
CREATE INDEX IF NOT EXISTS idx_data_subject_requests_data_subject ON quester.data_subject_requests(data_subject_id);
CREATE INDEX IF NOT EXISTS idx_data_subject_requests_status ON quester.data_subject_requests(status);
CREATE INDEX IF NOT EXISTS idx_data_subject_requests_deadline ON quester.data_subject_requests(deadline);

CREATE INDEX IF NOT EXISTS idx_sso_configurations_org ON quester.sso_configurations(organization_id);
CREATE INDEX IF NOT EXISTS idx_sso_configurations_provider ON quester.sso_configurations(provider);

CREATE INDEX IF NOT EXISTS idx_api_keys_org ON quester.api_keys(organization_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_value ON quester.api_keys(key_value);
CREATE INDEX IF NOT EXISTS idx_api_keys_status ON quester.api_keys(status);

CREATE INDEX IF NOT EXISTS idx_rate_limits_org ON quester.rate_limits(organization_id);
CREATE INDEX IF NOT EXISTS idx_rate_limits_api_key ON quester.rate_limits(api_key_id);

CREATE INDEX IF NOT EXISTS idx_webhooks_org ON quester.webhooks(organization_id);
CREATE INDEX IF NOT EXISTS idx_webhooks_status ON quester.webhooks(status);

CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_webhook ON quester.webhook_deliveries(webhook_id);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_delivered_at ON quester.webhook_deliveries(delivered_at DESC);

CREATE INDEX IF NOT EXISTS idx_api_usage_log_org_created ON quester.api_usage_log(organization_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_log_api_key ON quester.api_usage_log(api_key_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_log_rate_limit ON quester.api_usage_log(rate_limit_id);

-- Comments for documentation
COMMENT ON TABLE quester.audit_events IS 'Comprehensive audit trail for compliance tracking and security monitoring';
COMMENT ON TABLE quester.compliance_reports IS 'Generated compliance reports for various frameworks (GDPR, SOC 2, etc.)';
COMMENT ON TABLE quester.data_processing_records IS 'GDPR Article 30 data processing activity records';
COMMENT ON TABLE quester.consent_records IS 'GDPR consent management and tracking';
COMMENT ON TABLE quester.data_subject_requests IS 'GDPR data subject rights requests (access, deletion, etc.)';
COMMENT ON TABLE quester.sso_configurations IS 'Enterprise SSO provider configurations';
COMMENT ON TABLE quester.api_keys IS 'API key management for enterprise integrations';
COMMENT ON TABLE quester.rate_limits IS 'API rate limiting configuration and enforcement';
COMMENT ON TABLE quester.webhooks IS 'Webhook configurations for real-time event notifications';
COMMENT ON TABLE quester.webhook_deliveries IS 'Webhook delivery attempt logs';
COMMENT ON TABLE quester.api_usage_log IS 'API usage tracking for rate limiting and analytics';

-- Default GDPR compliance settings
INSERT INTO quester.data_processing_records (
    organization_id,
    data_controller,
    activity_name,
    purpose,
    legal_basis,
    data_categories,
    data_sources,
    data_recipients,
    retention_period,
    security_measures,
    dpia_required
) VALUES (
    (SELECT id FROM quester.organizations WHERE slug = 'default' LIMIT 1), -- Use default org ID
    'Quester Platform',
    'User Account Management',
    'authentication',
    'Performance of a contract (GDPR Art. 6(1)(b))',
    ARRAY['identityData', 'contactData', 'profileData'],
    ARRAY['User Registration', 'Profile Updates'],
    ARRAY['Internal Systems', 'Authentication Service'],
    '2 years after account deletion',
    ARRAY['Encryption at rest', 'TLS in transit', 'Access controls'],
    false
) ON CONFLICT DO NOTHING;
