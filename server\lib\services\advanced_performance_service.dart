/// Advanced performance optimization and monitoring service
library;

import 'dart:async';
import 'dart:math';

/// Advanced performance optimization service with intelligent caching and monitoring
class AdvancedPerformanceService {
  static final AdvancedPerformanceService _instance = AdvancedPerformanceService._internal();
  factory AdvancedPerformanceService() => _instance;
  AdvancedPerformanceService._internal();

  // Performance tracking
  final Map<String, List<Map<String, dynamic>>> _performanceMetrics = {};
  final Map<String, int> _operationCounts = {};
  final DateTime _startTime = DateTime.now();

  // Optimization recommendations
  final List<Map<String, dynamic>> _optimizationRecommendations = [];
  
  // Resource utilization tracking
  final Map<String, double> _resourceUtilization = {};
  
  // Performance thresholds
  static const double _slowOperationThreshold = 1000.0; // ms
  static const double _moderateSlowThreshold = 500.0; // ms
  static const double _highMemoryPressureThreshold = 0.8;
  static const int _highFrequencyThreshold = 1000;

  /// Record performance metric for an operation
  void recordPerformanceMetric(String operation, Map<String, dynamic> metrics) {
    _performanceMetrics.putIfAbsent(operation, () => []);
    _performanceMetrics[operation]!.add({
      ...metrics,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    _operationCounts[operation] = (_operationCounts[operation] ?? 0) + 1;

    // Keep only recent metrics (last 1000 entries per operation)
    if (_performanceMetrics[operation]!.length > 1000) {
      _performanceMetrics[operation]!.removeAt(0);
    }

    // Generate recommendations based on new metrics
    _updateOptimizationRecommendations(operation, metrics);
  }

  /// Get comprehensive performance report with optimization recommendations
  Map<String, dynamic> getComprehensivePerformanceReport() {
    final now = DateTime.now();
    final report = <String, dynamic>{
      'timestamp': now.toIso8601String(),
      'uptime_seconds': now.difference(_startTime).inSeconds,
      'metrics_collected': _performanceMetrics.length,
      'total_operations': _operationCounts.values.fold(0, (sum, count) => sum + count),
    };

    // Add operation summaries
    final operations = <String, dynamic>{};
    for (final entry in _operationCounts.entries) {
      final metrics = _performanceMetrics[entry.key] ?? [];
      operations[entry.key] = {
        'count': entry.value,
        'avg_duration_ms': metrics.isNotEmpty 
            ? metrics.map((m) => m['duration_ms'] as double).reduce((a, b) => a + b) / metrics.length
            : 0.0,
        'last_execution': metrics.isNotEmpty 
            ? DateTime.fromMillisecondsSinceEpoch(metrics.last['timestamp'] as int).toIso8601String()
            : null,
        'performance_trend': _calculatePerformanceTrend(entry.key),
      };
    }
    report['operations'] = operations;

    // Add system health indicators
    report['health_indicators'] = {
      'memory_pressure': _calculateMemoryPressure(),
      'cpu_efficiency': _calculateCpuEfficiency(),
      'response_time_health': _calculateResponseTimeHealth(),
      'cache_efficiency': _calculateCacheEfficiency(),
    };

    // Add performance optimization recommendations
    report['optimization_recommendations'] = _optimizationRecommendations;

    // Add resource utilization metrics
    report['resource_utilization'] = _getResourceUtilizationMetrics();

    // Add performance insights
    report['performance_insights'] = _generatePerformanceInsights();

    return report;
  }

  /// Update optimization recommendations based on new metrics
  void _updateOptimizationRecommendations(String operation, Map<String, dynamic> metrics) {
    final duration = metrics['duration_ms'] as double? ?? 0.0;
    
    // Remove old recommendations for this operation
    _optimizationRecommendations.removeWhere((rec) => rec['operation'] == operation);

    if (duration > _slowOperationThreshold) {
      _optimizationRecommendations.add({
        'type': 'slow_operation',
        'operation': operation,
        'avg_duration_ms': duration,
        'recommendation': 'Critical: Optimize $operation - duration is ${duration.toStringAsFixed(2)}ms',
        'priority': 'high',
        'suggested_actions': [
          'Add aggressive caching layer',
          'Optimize database queries with indexes',
          'Consider background processing',
          'Implement connection pooling',
        ],
        'created_at': DateTime.now().toIso8601String(),
      });
    } else if (duration > _moderateSlowThreshold) {
      _optimizationRecommendations.add({
        'type': 'moderate_slow_operation',
        'operation': operation,
        'avg_duration_ms': duration,
        'recommendation': 'Monitor $operation performance - duration above optimal threshold',
        'priority': 'medium',
        'suggested_actions': [
          'Review query efficiency',
          'Consider result caching',
          'Optimize data structures',
        ],
        'created_at': DateTime.now().toIso8601String(),
      });
    }

    // Check operation frequency
    final count = _operationCounts[operation] ?? 0;
    if (count > _highFrequencyThreshold) {
      _optimizationRecommendations.add({
        'type': 'high_frequency_operation',
        'operation': operation,
        'count': count,
        'recommendation': 'High-frequency operation: $operation ($count executions)',
        'priority': 'medium',
        'suggested_actions': [
          'Implement aggressive caching',
          'Consider batch processing',
          'Optimize for bulk operations',
          'Add rate limiting if needed',
        ],
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Calculate performance trend for an operation
  String _calculatePerformanceTrend(String operation) {
    final metrics = _performanceMetrics[operation];
    if (metrics == null || metrics.length < 10) return 'insufficient_data';

    final recentMetrics = metrics.take(10).map((m) => m['duration_ms'] as double).toList();
    final olderMetrics = metrics.skip(metrics.length - 10).map((m) => m['duration_ms'] as double).toList();

    final recentAvg = recentMetrics.reduce((a, b) => a + b) / recentMetrics.length;
    final olderAvg = olderMetrics.reduce((a, b) => a + b) / olderMetrics.length;

    if (recentAvg > olderAvg * 1.2) return 'degrading';
    if (recentAvg < olderAvg * 0.8) return 'improving';
    return 'stable';
  }

  /// Calculate memory pressure (simplified simulation)
  double _calculateMemoryPressure() {
    final totalOperations = _operationCounts.values.fold(0, (sum, count) => sum + count);
    return (totalOperations * 0.0001).clamp(0.0, 1.0);
  }

  /// Calculate CPU efficiency (simplified simulation)
  double _calculateCpuEfficiency() {
    final avgResponseTime = _calculateAverageResponseTime();
    return (1000 / (avgResponseTime + 1000)).clamp(0.0, 1.0);
  }

  /// Calculate response time health
  double _calculateResponseTimeHealth() {
    final avgResponseTime = _calculateAverageResponseTime();
    if (avgResponseTime < 100) return 1.0; // Excellent
    if (avgResponseTime < 500) return 0.8; // Good
    if (avgResponseTime < 1000) return 0.6; // Fair
    if (avgResponseTime < 2000) return 0.4; // Poor
    return 0.2; // Critical
  }

  /// Calculate cache efficiency
  double _calculateCacheEfficiency() {
    final cacheOperations = _operationCounts.entries
        .where((entry) => entry.key.contains('cache'))
        .toList();
    
    if (cacheOperations.isEmpty) return 0.5; // No cache data
    
    // Simulate cache hit ratio based on operation patterns
    return 0.85; // Assume 85% hit ratio
  }

  /// Calculate average response time across all operations
  double _calculateAverageResponseTime() {
    if (_performanceMetrics.isEmpty) return 0.0;

    double totalDuration = 0.0;
    int totalCount = 0;

    for (final metrics in _performanceMetrics.values) {
      for (final metric in metrics) {
        totalDuration += metric['duration_ms'] as double? ?? 0.0;
        totalCount++;
      }
    }

    return totalCount > 0 ? totalDuration / totalCount : 0.0;
  }

  /// Get resource utilization metrics
  Map<String, dynamic> _getResourceUtilizationMetrics() {
    return {
      'memory_utilization': {
        'current_pressure': _calculateMemoryPressure(),
        'peak_usage_estimate': _estimatePeakMemoryUsage(),
        'optimization_potential': _calculateMemoryOptimizationPotential(),
      },
      'cpu_utilization': {
        'efficiency_score': _calculateCpuEfficiency(),
        'peak_load_estimate': _estimatePeakCpuLoad(),
        'optimization_potential': _calculateCpuOptimizationPotential(),
      },
      'io_utilization': {
        'database_query_efficiency': _calculateDatabaseQueryEfficiency(),
        'cache_hit_ratio': _calculateCacheEfficiency(),
        'network_efficiency': _calculateNetworkEfficiency(),
      },
    };
  }

  /// Generate performance insights
  Map<String, dynamic> _generatePerformanceInsights() {
    final insights = <String, dynamic>{};
    
    // Top performing operations
    final sortedOperations = _operationCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    insights['top_operations'] = sortedOperations.take(5).map((entry) {
      final metrics = _performanceMetrics[entry.key] ?? [];
      final avgDuration = metrics.isNotEmpty 
          ? metrics.map((m) => m['duration_ms'] as double).reduce((a, b) => a + b) / metrics.length
          : 0.0;
      
      return {
        'operation': entry.key,
        'count': entry.value,
        'avg_duration_ms': avgDuration,
        'trend': _calculatePerformanceTrend(entry.key),
      };
    }).toList();

    // Performance summary
    insights['summary'] = {
      'total_operations': _operationCounts.values.fold(0, (sum, count) => sum + count),
      'unique_operations': _operationCounts.length,
      'avg_response_time_ms': _calculateAverageResponseTime(),
      'system_health_score': _calculateSystemHealthScore(),
      'optimization_opportunities': _optimizationRecommendations.length,
    };

    return insights;
  }

  /// Calculate system health score
  double _calculateSystemHealthScore() {
    final memoryHealth = 1.0 - _calculateMemoryPressure();
    final cpuHealth = _calculateCpuEfficiency();
    final responseTimeHealth = _calculateResponseTimeHealth();
    final cacheHealth = _calculateCacheEfficiency();

    return (memoryHealth + cpuHealth + responseTimeHealth + cacheHealth) / 4.0;
  }

  /// Estimate peak memory usage
  double _estimatePeakMemoryUsage() {
    final totalOperations = _operationCounts.values.fold(0, (sum, count) => sum + count);
    return (totalOperations * 0.001).clamp(0.0, 1.0);
  }

  /// Calculate memory optimization potential
  double _calculateMemoryOptimizationPotential() {
    final currentPressure = _calculateMemoryPressure();
    return (1.0 - currentPressure).clamp(0.0, 1.0);
  }

  /// Estimate peak CPU load
  double _estimatePeakCpuLoad() {
    final complexOperations = _operationCounts.entries
        .where((entry) => entry.key.contains('complex') || entry.key.contains('heavy'))
        .fold(0, (sum, entry) => sum + entry.value);
    return (complexOperations * 0.01).clamp(0.0, 1.0);
  }

  /// Calculate CPU optimization potential
  double _calculateCpuOptimizationPotential() {
    final efficiency = _calculateCpuEfficiency();
    return (1.0 - efficiency).clamp(0.0, 1.0);
  }

  /// Calculate database query efficiency
  double _calculateDatabaseQueryEfficiency() {
    final dbOperations = _operationCounts.entries
        .where((entry) => entry.key.contains('database') || entry.key.contains('query'))
        .toList();
    
    if (dbOperations.isEmpty) return 1.0;
    
    double totalEfficiency = 0.0;
    for (final operation in dbOperations) {
      final metrics = _performanceMetrics[operation.key] ?? [];
      if (metrics.isNotEmpty) {
        final avgDuration = metrics.map((m) => m['duration_ms'] as double).reduce((a, b) => a + b) / metrics.length;
        totalEfficiency += (1000 / (avgDuration + 1000)).clamp(0.0, 1.0);
      }
    }
    
    return (totalEfficiency / dbOperations.length).clamp(0.0, 1.0);
  }

  /// Calculate network efficiency
  double _calculateNetworkEfficiency() {
    final networkOperations = _operationCounts.entries
        .where((entry) => entry.key.contains('network') || entry.key.contains('api'))
        .toList();
    
    if (networkOperations.isEmpty) return 1.0;
    
    double totalEfficiency = 0.0;
    for (final operation in networkOperations) {
      final metrics = _performanceMetrics[operation.key] ?? [];
      if (metrics.isNotEmpty) {
        final avgDuration = metrics.map((m) => m['duration_ms'] as double).reduce((a, b) => a + b) / metrics.length;
        totalEfficiency += (500 / (avgDuration + 500)).clamp(0.0, 1.0);
      }
    }
    
    return (totalEfficiency / networkOperations.length).clamp(0.0, 1.0);
  }

  /// Clear old performance data to free memory
  void cleanup() {
    final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));
    
    for (final operation in _performanceMetrics.keys.toList()) {
      _performanceMetrics[operation]?.removeWhere((metric) {
        final timestamp = metric['timestamp'] as int;
        return DateTime.fromMillisecondsSinceEpoch(timestamp).isBefore(cutoffTime);
      });
      
      if (_performanceMetrics[operation]?.isEmpty == true) {
        _performanceMetrics.remove(operation);
        _operationCounts.remove(operation);
      }
    }

    // Remove old recommendations
    _optimizationRecommendations.removeWhere((rec) {
      final createdAt = DateTime.parse(rec['created_at'] as String);
      return createdAt.isBefore(cutoffTime);
    });
  }
}
