import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'notification.dart';

part 'notification_template.g.dart';

/// Template for creating notifications with dynamic content
@JsonSerializable()
class NotificationTemplate extends Equatable {
  /// Unique identifier for the template
  final String id;

  /// Template name for identification
  final String name;

  /// Template description
  final String description;

  /// Category this template belongs to
  final NotificationCategory category;

  /// Type of notifications this template creates
  final NotificationType type;

  /// Priority level for notifications created from this template
  final NotificationPriority priority;

  /// Template for the notification title
  final String titleTemplate;

  /// Template for the notification body
  final String bodyTemplate;

  /// Optional template for action text
  final String? actionTextTemplate;

  /// Optional template for action URL
  final String? actionUrlTemplate;

  /// Default delivery methods for this template
  final List<DeliveryMethod> defaultDeliveryMethods;

  /// Template variables and their descriptions
  final Map<String, TemplateVariable> variables;

  /// Optional icon for notifications created from this template
  final String? icon;

  /// Optional sound for notifications created from this template
  final String? sound;

  /// Optional vibration pattern
  final List<int>? vibrationPattern;

  /// Whether notifications from this template should show badges
  final bool showBadge;

  /// Optional expiration duration for notifications (in minutes)
  final int? expirationMinutes;

  /// Channel ID for grouping notifications
  final String? channelId;

  /// Whether this template is active
  final bool isActive;

  /// Supported languages for this template
  final List<String> supportedLanguages;

  /// Localized versions of the template
  final Map<String, LocalizedTemplate>? localizations;

  /// When the template was created
  final DateTime createdAt;

  /// When the template was last updated
  final DateTime updatedAt;

  /// Who created this template
  final String createdBy;

  const NotificationTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.type,
    required this.priority,
    required this.titleTemplate,
    required this.bodyTemplate,
    this.actionTextTemplate,
    this.actionUrlTemplate,
    required this.defaultDeliveryMethods,
    this.variables = const {},
    this.icon,
    this.sound,
    this.vibrationPattern,
    this.showBadge = true,
    this.expirationMinutes,
    this.channelId,
    this.isActive = true,
    this.supportedLanguages = const ['en'],
    this.localizations,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  });

  /// Creates NotificationTemplate from JSON
  factory NotificationTemplate.fromJson(Map<String, dynamic> json) =>
      _$NotificationTemplateFromJson(json);

  /// Converts NotificationTemplate to JSON
  Map<String, dynamic> toJson() => _$NotificationTemplateToJson(this);

  /// Creates a notification from this template with provided variables
  Notification createNotification({
    required String userId,
    required Map<String, dynamic> variables,
    String? language,
    DateTime? scheduledFor,
    String? groupId,
  }) {
    final lang = language ?? 'en';
    final template = _getLocalizedTemplate(lang);
    
    final title = _replaceVariables(template.titleTemplate, variables);
    final body = _replaceVariables(template.bodyTemplate, variables);
    final actionText = template.actionTextTemplate != null
        ? _replaceVariables(template.actionTextTemplate!, variables)
        : null;
    final actionUrl = template.actionUrlTemplate != null
        ? _replaceVariables(template.actionUrlTemplate!, variables)
        : null;

    final now = DateTime.now();
    final expiresAt = expirationMinutes != null
        ? now.add(Duration(minutes: expirationMinutes!))
        : null;

    return Notification(
      id: _generateNotificationId(),
      userId: userId,
      type: type,
      title: title,
      body: body,
      actionText: actionText,
      actionUrl: actionUrl,
      priority: priority,
      status: NotificationStatus.pending,
      deliveryMethods: defaultDeliveryMethods,
      metadata: {
        'templateId': id,
        'templateName': name,
        'variables': variables,
        'language': lang,
        'vibrationPattern': vibrationPattern,
        'showBadge': showBadge,
        'channelId': channelId,
        'groupId': groupId,
      },
      relatedEntityId: variables['entityId'] as String?,
      relatedEntityType: variables['entityType'] as String?,
      icon: icon,
      sound: sound,
      createdAt: now,
      scheduledAt: scheduledFor,
      expiresAt: expiresAt,
    );
  }

  /// Gets the localized template for a language
  LocalizedTemplate _getLocalizedTemplate(String language) {
    if (localizations != null && localizations!.containsKey(language)) {
      return localizations![language]!;
    }
    
    // Fallback to default template
    return LocalizedTemplate(
      titleTemplate: titleTemplate,
      bodyTemplate: bodyTemplate,
      actionTextTemplate: actionTextTemplate,
      actionUrlTemplate: actionUrlTemplate,
    );
  }

  /// Replaces variables in a template string
  String _replaceVariables(String template, Map<String, dynamic> variables) {
    String result = template;
    
    for (final entry in variables.entries) {
      final placeholder = '{{${entry.key}}}';
      final value = entry.value?.toString() ?? '';
      result = result.replaceAll(placeholder, value);
    }
    
    return result;
  }

  /// Generates a unique notification ID
  String _generateNotificationId() {
    return 'notif_${DateTime.now().millisecondsSinceEpoch}_${id.substring(0, 8)}';
  }

  /// Validates that all required variables are provided
  bool validateVariables(Map<String, dynamic> variables) {
    for (final variable in this.variables.values) {
      if (variable.required && !variables.containsKey(variable.name)) {
        return false;
      }
    }
    return true;
  }

  /// Gets missing required variables
  List<String> getMissingVariables(Map<String, dynamic> variables) {
    final missing = <String>[];
    
    for (final variable in this.variables.values) {
      if (variable.required && !variables.containsKey(variable.name)) {
        missing.add(variable.name);
      }
    }
    
    return missing;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        type,
        priority,
        titleTemplate,
        bodyTemplate,
        actionTextTemplate,
        actionUrlTemplate,
        defaultDeliveryMethods,
        variables,
        icon,
        sound,
        vibrationPattern,
        showBadge,
        expirationMinutes,
        channelId,
        isActive,
        supportedLanguages,
        localizations,
        createdAt,
        updatedAt,
        createdBy,
      ];
}

/// Template variable definition
@JsonSerializable()
class TemplateVariable extends Equatable {
  /// Variable name (used in templates as {{name}})
  final String name;

  /// Human-readable description of the variable
  final String description;

  /// Data type of the variable
  final VariableType type;

  /// Whether this variable is required
  final bool required;

  /// Default value if not provided
  final dynamic defaultValue;

  /// Validation pattern for string variables
  final String? validationPattern;

  /// Example value for documentation
  final dynamic exampleValue;

  const TemplateVariable({
    required this.name,
    required this.description,
    required this.type,
    this.required = false,
    this.defaultValue,
    this.validationPattern,
    this.exampleValue,
  });

  /// Creates TemplateVariable from JSON
  factory TemplateVariable.fromJson(Map<String, dynamic> json) =>
      _$TemplateVariableFromJson(json);

  /// Converts TemplateVariable to JSON
  Map<String, dynamic> toJson() => _$TemplateVariableToJson(this);

  @override
  List<Object?> get props => [
        name,
        description,
        type,
        required,
        defaultValue,
        validationPattern,
        exampleValue,
      ];
}

/// Types of template variables
enum VariableType {
  string,
  number,
  boolean,
  date,
  url,
  email,
  userId,
  questId,
  taskId,
}

/// Localized version of a notification template
@JsonSerializable()
class LocalizedTemplate extends Equatable {
  /// Localized title template
  final String titleTemplate;

  /// Localized body template
  final String bodyTemplate;

  /// Localized action text template
  final String? actionTextTemplate;

  /// Localized action URL template
  final String? actionUrlTemplate;

  const LocalizedTemplate({
    required this.titleTemplate,
    required this.bodyTemplate,
    this.actionTextTemplate,
    this.actionUrlTemplate,
  });

  /// Creates LocalizedTemplate from JSON
  factory LocalizedTemplate.fromJson(Map<String, dynamic> json) =>
      _$LocalizedTemplateFromJson(json);

  /// Converts LocalizedTemplate to JSON
  Map<String, dynamic> toJson() => _$LocalizedTemplateToJson(this);

  @override
  List<Object?> get props => [
        titleTemplate,
        bodyTemplate,
        actionTextTemplate,
        actionUrlTemplate,
      ];
}

/// Pre-defined notification templates
class NotificationTemplates {
  /// Quest completed template
  static final questCompleted = NotificationTemplate(
    id: 'quest_completed',
    name: 'Quest Completed',
    description: 'Notification sent when a user completes a quest',
    category: NotificationCategory.quest,
    type: NotificationType.pointsEarned,
    priority: NotificationPriority.normal,
    titleTemplate: '🎉 Quest Completed!',
    bodyTemplate: 'Congratulations! You completed "{{questTitle}}" and earned {{points}} points!',
    actionTextTemplate: 'View Quest',
    actionUrlTemplate: '/quests/{{questId}}',
    defaultDeliveryMethods: [DeliveryMethod.push, DeliveryMethod.inApp],
    variables: {
      'questTitle': TemplateVariable(
        name: 'questTitle',
        description: 'Title of the completed quest',
        type: VariableType.string,
        required: true,
        exampleValue: 'Complete Daily Tasks',
      ),
      'questId': TemplateVariable(
        name: 'questId',
        description: 'ID of the completed quest',
        type: VariableType.questId,
        required: true,
        exampleValue: 'quest_123',
      ),
      'points': TemplateVariable(
        name: 'points',
        description: 'Points earned from completing the quest',
        type: VariableType.number,
        required: true,
        exampleValue: 100,
      ),
    },
    icon: 'quest_completed',
    sound: 'success',
    showBadge: true,
    expirationMinutes: 1440, // 24 hours
    channelId: 'quests',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    createdBy: 'system',
  );

  /// Achievement unlocked template
  static final achievementUnlocked = NotificationTemplate(
    id: 'achievement_unlocked',
    name: 'Achievement Unlocked',
    description: 'Notification sent when a user unlocks an achievement',
    category: NotificationCategory.achievement,
    type: NotificationType.achievementUnlocked,
    priority: NotificationPriority.high,
    titleTemplate: '🏆 Achievement Unlocked!',
    bodyTemplate: 'You unlocked "{{achievementName}}"! {{description}}',
    actionTextTemplate: 'View Achievement',
    actionUrlTemplate: '/achievements/{{achievementId}}',
    defaultDeliveryMethods: [DeliveryMethod.push, DeliveryMethod.inApp],
    variables: {
      'achievementName': TemplateVariable(
        name: 'achievementName',
        description: 'Name of the unlocked achievement',
        type: VariableType.string,
        required: true,
        exampleValue: 'First Quest Master',
      ),
      'achievementId': TemplateVariable(
        name: 'achievementId',
        description: 'ID of the unlocked achievement',
        type: VariableType.string,
        required: true,
        exampleValue: 'achievement_123',
      ),
      'description': TemplateVariable(
        name: 'description',
        description: 'Description of the achievement',
        type: VariableType.string,
        required: false,
        exampleValue: 'Complete your first quest to earn this badge.',
      ),
    },
    icon: 'achievement',
    sound: 'achievement',
    showBadge: true,
    expirationMinutes: 2880, // 48 hours
    channelId: 'achievements',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    createdBy: 'system',
  );
}
