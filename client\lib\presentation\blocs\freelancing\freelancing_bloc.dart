import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/repositories/api_repository.dart';

// Events
abstract class FreelancingEvent extends Equatable {
  const FreelancingEvent();
  
  @override
  List<Object?> get props => [];
}

class LoadProjects extends FreelancingEvent {
  final Map<String, String>? filters;
  
  const LoadProjects({this.filters});
  
  @override
  List<Object?> get props => [filters];
}

class LoadProject extends FreelancingEvent {
  final String projectId;
  
  const LoadProject({required this.projectId});
  
  @override
  List<Object?> get props => [projectId];
}

class CreateProject extends FreelancingEvent {
  final Map<String, dynamic> projectData;
  
  const CreateProject({required this.projectData});
  
  @override
  List<Object?> get props => [projectData];
}

class LoadFreelancerProfile extends FreelancingEvent {
  const LoadFreelancerProfile();
}

// States
abstract class FreelancingState extends Equatable {
  const FreelancingState();
  
  @override
  List<Object?> get props => [];
}

class FreelancingInitial extends FreelancingState {
  const FreelancingInitial();
}

class FreelancingLoading extends FreelancingState {
  const FreelancingLoading();
}

class FreelancingLoaded extends FreelancingState {
  final List<Map<String, dynamic>>? projects;
  final Map<String, dynamic>? selectedProject;
  final Map<String, dynamic>? freelancerProfile;
  
  const FreelancingLoaded({
    this.projects,
    this.selectedProject,
    this.freelancerProfile,
  });
  
  @override
  List<Object?> get props => [projects, selectedProject, freelancerProfile];
  
  FreelancingLoaded copyWith({
    List<Map<String, dynamic>>? projects,
    Map<String, dynamic>? selectedProject,
    Map<String, dynamic>? freelancerProfile,
  }) {
    return FreelancingLoaded(
      projects: projects ?? this.projects,
      selectedProject: selectedProject ?? this.selectedProject,
      freelancerProfile: freelancerProfile ?? this.freelancerProfile,
    );
  }
}

class FreelancingError extends FreelancingState {
  final String message;
  
  const FreelancingError({required this.message});
  
  @override
  List<Object?> get props => [message];
}

class ProjectCreated extends FreelancingState {
  final Map<String, dynamic> project;
  
  const ProjectCreated({required this.project});
  
  @override
  List<Object?> get props => [project];
}

// BLoC
class FreelancingBloc extends Bloc<FreelancingEvent, FreelancingState> {
  final ApiRepository repository;
  
  FreelancingBloc({required this.repository}) : super(const FreelancingInitial()) {
    on<LoadProjects>(_onLoadProjects);
    on<LoadProject>(_onLoadProject);
    on<CreateProject>(_onCreateProject);
    on<LoadFreelancerProfile>(_onLoadFreelancerProfile);
  }

  Future<void> _onLoadProjects(LoadProjects event, Emitter<FreelancingState> emit) async {
    emit(const FreelancingLoading());
    
    final response = await repository.getProjects(filters: event.filters);
    
    if (response.isSuccess && response.data != null) {
      if (state is FreelancingLoaded) {
        emit((state as FreelancingLoaded).copyWith(projects: response.data));
      } else {
        emit(FreelancingLoaded(projects: response.data));
      }
    } else {
      emit(FreelancingError(message: response.error ?? 'Failed to load projects'));
    }
  }

  Future<void> _onLoadProject(LoadProject event, Emitter<FreelancingState> emit) async {
    if (state is! FreelancingLoading) {
      emit(const FreelancingLoading());
    }
    
    final response = await repository.getProject(event.projectId);
    
    if (response.isSuccess && response.data != null) {
      if (state is FreelancingLoaded) {
        emit((state as FreelancingLoaded).copyWith(selectedProject: response.data));
      } else {
        emit(FreelancingLoaded(selectedProject: response.data));
      }
    } else {
      emit(FreelancingError(message: response.error ?? 'Failed to load project'));
    }
  }

  Future<void> _onCreateProject(CreateProject event, Emitter<FreelancingState> emit) async {
    emit(const FreelancingLoading());
    
    final response = await repository.createProject(event.projectData);
    
    if (response.isSuccess && response.data != null) {
      emit(ProjectCreated(project: response.data!));
    } else {
      emit(FreelancingError(message: response.error ?? 'Failed to create project'));
    }
  }

  Future<void> _onLoadFreelancerProfile(LoadFreelancerProfile event, Emitter<FreelancingState> emit) async {
    if (state is! FreelancingLoading) {
      emit(const FreelancingLoading());
    }
    
    final response = await repository.getFreelancerProfile();
    
    if (response.isSuccess && response.data != null) {
      if (state is FreelancingLoaded) {
        emit((state as FreelancingLoaded).copyWith(freelancerProfile: response.data));
      } else {
        emit(FreelancingLoaded(freelancerProfile: response.data));
      }
    } else {
      emit(FreelancingError(message: response.error ?? 'Failed to load freelancer profile'));
    }
  }
}