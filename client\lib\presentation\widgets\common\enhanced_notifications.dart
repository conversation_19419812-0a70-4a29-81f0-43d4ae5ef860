import 'package:flutter/material.dart';
import 'dart:async';
import '../../../core/theme/app_theme.dart';
import 'enhanced_animations.dart';

/// Enhanced notification types
enum NotificationType {
  achievement,
  levelUp,
  questComplete,
  pointsEarned,
  streakMilestone,
  info,
  warning,
  error,
  success,
}

/// Enhanced notification system with gamification elements
class EnhancedNotificationService {
  static final EnhancedNotificationService _instance = EnhancedNotificationService._internal();
  factory EnhancedNotificationService() => _instance;
  EnhancedNotificationService._internal();

  final List<EnhancedNotification> _notifications = [];
  final StreamController<List<EnhancedNotification>> _controller = 
      StreamController<List<EnhancedNotification>>.broadcast();

  Stream<List<EnhancedNotification>> get notifications => _controller.stream;
  List<EnhancedNotification> get currentNotifications => List.unmodifiable(_notifications);

  void showNotification(EnhancedNotification notification) {
    _notifications.add(notification);
    _controller.add(_notifications);

    // Auto-dismiss after duration
    Timer(notification.duration, () {
      dismissNotification(notification.id);
    });
  }

  void dismissNotification(String id) {
    _notifications.removeWhere((notification) => notification.id == id);
    _controller.add(_notifications);
  }

  void clearAll() {
    _notifications.clear();
    _controller.add(_notifications);
  }

  void dispose() {
    _controller.close();
  }

  // Convenience methods for different notification types
  void showAchievement({
    required String title,
    required String description,
    required String rarity,
    IconData icon = Icons.emoji_events,
    Duration duration = const Duration(seconds: 5),
  }) {
    showNotification(EnhancedNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.achievement,
      title: title,
      description: description,
      icon: icon,
      duration: duration,
      metadata: {'rarity': rarity},
    ));
  }

  void showLevelUp({
    required int newLevel,
    required int pointsEarned,
    Duration duration = const Duration(seconds: 4),
  }) {
    showNotification(EnhancedNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.levelUp,
      title: 'Level Up!',
      description: 'You reached level $newLevel and earned $pointsEarned points!',
      icon: Icons.trending_up,
      duration: duration,
      metadata: {'level': newLevel, 'points': pointsEarned},
    ));
  }

  void showQuestComplete({
    required String questTitle,
    required int pointsEarned,
    Duration duration = const Duration(seconds: 3),
  }) {
    showNotification(EnhancedNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.questComplete,
      title: 'Quest Completed!',
      description: '$questTitle completed! +$pointsEarned points',
      icon: Icons.task_alt,
      duration: duration,
      metadata: {'quest': questTitle, 'points': pointsEarned},
    ));
  }

  void showPointsEarned({
    required int points,
    String? source,
    Duration duration = const Duration(seconds: 2),
  }) {
    showNotification(EnhancedNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.pointsEarned,
      title: 'Points Earned!',
      description: '+$points points${source != null ? ' from $source' : ''}',
      icon: Icons.stars,
      duration: duration,
      metadata: {'points': points, 'source': source},
    ));
  }
}

/// Enhanced notification data model
class EnhancedNotification {
  final String id;
  final NotificationType type;
  final String title;
  final String description;
  final IconData icon;
  final Duration duration;
  final Map<String, dynamic>? metadata;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const EnhancedNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    this.duration = const Duration(seconds: 3),
    this.metadata,
    this.onTap,
    this.onDismiss,
  });
}

/// Enhanced notification widget with animations and gamification
class EnhancedNotificationWidget extends StatefulWidget {
  final EnhancedNotification notification;
  final VoidCallback? onDismiss;

  const EnhancedNotificationWidget({
    super.key,
    required this.notification,
    this.onDismiss,
  });

  @override
  State<EnhancedNotificationWidget> createState() => _EnhancedNotificationWidgetState();
}

class _EnhancedNotificationWidgetState extends State<EnhancedNotificationWidget>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: widget.notification.duration,
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: GameCurves.slideIn,
    ));
    
    _progressAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
    _progressController.forward();
    
    if (_isGamificationNotification()) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  bool _isGamificationNotification() {
    return [
      NotificationType.achievement,
      NotificationType.levelUp,
      NotificationType.questComplete,
      NotificationType.pointsEarned,
      NotificationType.streakMilestone,
    ].contains(widget.notification.type);
  }

  Color _getNotificationColor() {
    switch (widget.notification.type) {
      case NotificationType.achievement:
        final rarity = widget.notification.metadata?['rarity'] ?? 'common';
        return AppTheme.getRarityColor(rarity);
      case NotificationType.levelUp:
        return AppTheme.infoColor;
      case NotificationType.questComplete:
        return AppTheme.successColor;
      case NotificationType.pointsEarned:
        return AppTheme.successColor;
      case NotificationType.streakMilestone:
        return AppTheme.warningColor;
      case NotificationType.success:
        return AppTheme.successColor;
      case NotificationType.warning:
        return AppTheme.warningColor;
      case NotificationType.error:
        return AppTheme.errorColor;
      case NotificationType.info:
      default:
        return AppTheme.infoColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = _getNotificationColor();

    return SlideTransition(
      position: _slideAnimation,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isGamificationNotification() ? _pulseAnimation.value : 1.0,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: color, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Column(
                  children: [
                    _buildNotificationContent(theme, color),
                    _buildProgressBar(color),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNotificationContent(ThemeData theme, Color color) {
    return InkWell(
      onTap: widget.notification.onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                widget.notification.icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.notification.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    widget.notification.description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () {
                widget.notification.onDismiss?.call();
                widget.onDismiss?.call();
              },
              icon: Icon(
                Icons.close,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar(Color color) {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Container(
          height: 3,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
          ),
          child: FractionallySizedBox(
            widthFactor: _progressAnimation.value,
            alignment: Alignment.centerLeft,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.5),
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Notification overlay widget
class NotificationOverlay extends StatelessWidget {
  const NotificationOverlay({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<EnhancedNotification>>(
      stream: EnhancedNotificationService().notifications,
      builder: (context, snapshot) {
        final notifications = snapshot.data ?? [];
        
        return Positioned(
          top: MediaQuery.of(context).padding.top + 16,
          right: 0,
          left: 0,
          child: Column(
            children: notifications.map((notification) {
              return EnhancedNotificationWidget(
                notification: notification,
                onDismiss: () {
                  EnhancedNotificationService().dismissNotification(notification.id);
                },
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
