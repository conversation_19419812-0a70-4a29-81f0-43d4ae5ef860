import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../models/auth/auth_session.dart';

part 'enhanced_auth_dto.g.dart';

/// Enhanced login request with additional options
@JsonSerializable()
class EnhancedLoginRequestDto extends Equatable {
  final String email;
  final String password;
  final bool rememberMe;
  final String? twoFactorCode;
  final String? organizationId; // For organization-specific login
  final Map<String, String>? deviceInfo;

  const EnhancedLoginRequestDto({
    required this.email,
    required this.password,
    this.rememberMe = false,
    this.twoFactorCode,
    this.organizationId,
    this.deviceInfo,
  });

  factory EnhancedLoginRequestDto.fromJson(Map<String, dynamic> json) => 
      _$EnhancedLoginRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$EnhancedLoginRequestDtoToJson(this);

  @override
  List<Object?> get props => [
        email,
        password,
        rememberMe,
        twoFactorCode,
        organizationId,
        deviceInfo,
      ];
}

/// OAuth login request
@JsonSerializable()
class OAuthLoginRequestDto extends Equatable {
  final OAuthProvider provider;
  final String authorizationCode;
  final String? state;
  final String? redirectUri;
  final String? organizationId;

  const OAuthLoginRequestDto({
    required this.provider,
    required this.authorizationCode,
    this.state,
    this.redirectUri,
    this.organizationId,
  });

  factory OAuthLoginRequestDto.fromJson(Map<String, dynamic> json) => 
      _$OAuthLoginRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$OAuthLoginRequestDtoToJson(this);

  @override
  List<Object?> get props => [
        provider,
        authorizationCode,
        state,
        redirectUri,
        organizationId,
      ];
}

/// Enhanced registration request
@JsonSerializable()
class EnhancedRegisterRequestDto extends Equatable {
  final String email;
  final String password;
  final String displayName;
  final String? firstName;
  final String? lastName;
  final String? organizationName; // For creating new organization
  final String? invitationToken; // For joining existing organization
  final Map<String, dynamic>? userPreferences;
  final bool acceptTerms;
  final bool subscribeToNewsletter;

  const EnhancedRegisterRequestDto({
    required this.email,
    required this.password,
    required this.displayName,
    this.firstName,
    this.lastName,
    this.organizationName,
    this.invitationToken,
    this.userPreferences,
    required this.acceptTerms,
    this.subscribeToNewsletter = false,
  });

  factory EnhancedRegisterRequestDto.fromJson(Map<String, dynamic> json) => 
      _$EnhancedRegisterRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$EnhancedRegisterRequestDtoToJson(this);

  @override
  List<Object?> get props => [
        email,
        password,
        displayName,
        firstName,
        lastName,
        organizationName,
        invitationToken,
        userPreferences,
        acceptTerms,
        subscribeToNewsletter,
      ];
}

/// Enhanced authentication response with full session data
@JsonSerializable()
class EnhancedAuthResponseDto extends Equatable {
  final AuthSession session;
  final bool requiresTwoFactor;
  final List<TwoFactorMethod>? availableTwoFactorMethods;
  final bool requiresEmailVerification;
  final String? onboardingNextStep;
  final Map<String, dynamic>? additionalData;

  const EnhancedAuthResponseDto({
    required this.session,
    this.requiresTwoFactor = false,
    this.availableTwoFactorMethods,
    this.requiresEmailVerification = false,
    this.onboardingNextStep,
    this.additionalData,
  });

  factory EnhancedAuthResponseDto.fromJson(Map<String, dynamic> json) => 
      _$EnhancedAuthResponseDtoFromJson(json);
  Map<String, dynamic> toJson() => _$EnhancedAuthResponseDtoToJson(this);

  @override
  List<Object?> get props => [
        session,
        requiresTwoFactor,
        availableTwoFactorMethods,
        requiresEmailVerification,
        onboardingNextStep,
        additionalData,
      ];
}

/// Two-factor authentication setup request
@JsonSerializable()
class TwoFactorSetupRequestDto extends Equatable {
  final TwoFactorMethod method;
  final String? phoneNumber; // For SMS
  final String? email; // For email verification
  final String? totpSecret; // For TOTP setup

  const TwoFactorSetupRequestDto({
    required this.method,
    this.phoneNumber,
    this.email,
    this.totpSecret,
  });

  factory TwoFactorSetupRequestDto.fromJson(Map<String, dynamic> json) => 
      _$TwoFactorSetupRequestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$TwoFactorSetupRequestDtoToJson(this);

  @override
  List<Object?> get props => [method, phoneNumber, email, totpSecret];
}

/// Two-factor authentication verification request
@JsonSerializable()
class TwoFactorVerificationDto extends Equatable {
  final String sessionToken; // Temporary token from initial login
  final TwoFactorMethod method;
  final String code;

  const TwoFactorVerificationDto({
    required this.sessionToken,
    required this.method,
    required this.code,
  });

  factory TwoFactorVerificationDto.fromJson(Map<String, dynamic> json) => 
      _$TwoFactorVerificationDtoFromJson(json);
  Map<String, dynamic> toJson() => _$TwoFactorVerificationDtoToJson(this);

  @override
  List<Object?> get props => [sessionToken, method, code];
}

/// Email verification request
@JsonSerializable()
class EmailVerificationDto extends Equatable {
  final String verificationToken;
  final String email;

  const EmailVerificationDto({
    required this.verificationToken,
    required this.email,
  });

  factory EmailVerificationDto.fromJson(Map<String, dynamic> json) => 
      _$EmailVerificationDtoFromJson(json);
  Map<String, dynamic> toJson() => _$EmailVerificationDtoToJson(this);

  @override
  List<Object?> get props => [verificationToken, email];
}

/// Organization invitation acceptance
@JsonSerializable()
class InvitationAcceptanceDto extends Equatable {
  final String invitationToken;
  final String? password; // If user doesn't exist yet
  final String? displayName; // If user doesn't exist yet

  const InvitationAcceptanceDto({
    required this.invitationToken,
    this.password,
    this.displayName,
  });

  factory InvitationAcceptanceDto.fromJson(Map<String, dynamic> json) => 
      _$InvitationAcceptanceDtoFromJson(json);
  Map<String, dynamic> toJson() => _$InvitationAcceptanceDtoToJson(this);

  @override
  List<Object?> get props => [invitationToken, password, displayName];
}

/// User impersonation request (for admin)
@JsonSerializable()
class UserImpersonationDto extends Equatable {
  final String targetUserId;
  final String reason;
  final int? durationMinutes; // Null = no time limit

  const UserImpersonationDto({
    required this.targetUserId,
    required this.reason,
    this.durationMinutes,
  });

  factory UserImpersonationDto.fromJson(Map<String, dynamic> json) => 
      _$UserImpersonationDtoFromJson(json);
  Map<String, dynamic> toJson() => _$UserImpersonationDtoToJson(this);

  @override
  List<Object?> get props => [targetUserId, reason, durationMinutes];
}

/// Session management request
@JsonSerializable()
class SessionManagementDto extends Equatable {
  final String action; // 'terminate', 'refresh', 'extend'
  final String? targetSessionId; // For admin actions
  final Map<String, dynamic>? parameters;

  const SessionManagementDto({
    required this.action,
    this.targetSessionId,
    this.parameters,
  });

  factory SessionManagementDto.fromJson(Map<String, dynamic> json) => 
      _$SessionManagementDtoFromJson(json);
  Map<String, dynamic> toJson() => _$SessionManagementDtoToJson(this);

  @override
  List<Object?> get props => [action, targetSessionId, parameters];
}

/// Password policy check response
@JsonSerializable()
class PasswordPolicyDto extends Equatable {
  final bool isValid;
  final int? minimumLength;
  final bool requiresUppercase;
  final bool requiresLowercase;
  final bool requiresNumbers;
  final bool requiresSpecialChars;
  final List<String> failedRequirements;
  final int strengthScore; // 0-100

  const PasswordPolicyDto({
    required this.isValid,
    this.minimumLength,
    required this.requiresUppercase,
    required this.requiresLowercase,
    required this.requiresNumbers,
    required this.requiresSpecialChars,
    required this.failedRequirements,
    required this.strengthScore,
  });

  factory PasswordPolicyDto.fromJson(Map<String, dynamic> json) => 
      _$PasswordPolicyDtoFromJson(json);
  Map<String, dynamic> toJson() => _$PasswordPolicyDtoToJson(this);

  @override
  List<Object?> get props => [
        isValid,
        minimumLength,
        requiresUppercase,
        requiresLowercase,
        requiresNumbers,
        requiresSpecialChars,
        failedRequirements,
        strengthScore,
      ];
}

/// Audit log entry for authentication events
@JsonSerializable()
class AuthAuditLogDto extends Equatable {
  final String id;
  final String userId;
  final String action; // 'login', 'logout', 'register', 'password_change', etc.
  final String ipAddress;
  final String userAgent;
  final String? organizationId;
  final Map<String, dynamic>? metadata;
  final DateTime timestamp;
  final bool success;
  final String? errorMessage;

  const AuthAuditLogDto({
    required this.id,
    required this.userId,
    required this.action,
    required this.ipAddress,
    required this.userAgent,
    this.organizationId,
    this.metadata,
    required this.timestamp,
    required this.success,
    this.errorMessage,
  });

  factory AuthAuditLogDto.fromJson(Map<String, dynamic> json) => 
      _$AuthAuditLogDtoFromJson(json);
  Map<String, dynamic> toJson() => _$AuthAuditLogDtoToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        action,
        ipAddress,
        userAgent,
        organizationId,
        metadata,
        timestamp,
        success,
        errorMessage,
      ];
}