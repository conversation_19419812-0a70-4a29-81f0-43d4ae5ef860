/// API Response wrapper for consistent response handling
library;

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

/// Generic API response wrapper
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> extends Equatable {
  /// Whether the request was successful
  final bool success;

  /// Response data
  final T? data;

  /// Error message if request failed
  final String? message;

  /// Error details
  final Map<String, dynamic>? error;

  /// Response timestamp
  final String? timestamp;

  /// HTTP status code
  final int? statusCode;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.timestamp,
    this.statusCode,
  });

  /// Creates a successful response
  factory ApiResponse.success({
    T? data,
    String? message,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode ?? 200,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Creates an error response
  factory ApiResponse.error({
    required String message,
    Map<String, dynamic>? error,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      error: error,
      statusCode: statusCode ?? 400,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Creates response from JSON
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  /// Converts response to JSON
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// Whether the response has data
  bool get hasData => data != null;

  /// Whether the response is an error
  bool get isError => !success;

  /// Gets the data or throws an exception if error
  T get dataOrThrow {
    if (isError) {
      throw ApiException(
        message: message ?? 'Unknown error',
        statusCode: statusCode,
        details: error,
      );
    }
    return data as T;
  }

  /// Gets the data or returns null if error
  T? get dataOrNull => success ? data : null;

  @override
  List<Object?> get props => [
        success,
        data,
        message,
        error,
        timestamp,
        statusCode,
      ];
}

/// Paginated API response
@JsonSerializable(genericArgumentFactories: true)
class PaginatedApiResponse<T> extends Equatable {
  /// Whether the request was successful
  final bool success;

  /// List of items
  final List<T> items;

  /// Current page number
  final int page;

  /// Items per page
  final int limit;

  /// Total number of items
  final int total;

  /// Total number of pages
  final int totalPages;

  /// Whether there are more pages
  final bool hasMore;

  /// Error message if request failed
  final String? message;

  /// Response timestamp
  final String? timestamp;

  const PaginatedApiResponse({
    required this.success,
    required this.items,
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
    required this.hasMore,
    this.message,
    this.timestamp,
  });

  /// Creates a successful paginated response
  factory PaginatedApiResponse.success({
    required List<T> items,
    required int page,
    required int limit,
    required int total,
    String? message,
  }) {
    final totalPages = (total / limit).ceil();
    final hasMore = page < totalPages;

    return PaginatedApiResponse<T>(
      success: true,
      items: items,
      page: page,
      limit: limit,
      total: total,
      totalPages: totalPages,
      hasMore: hasMore,
      message: message,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Creates an error paginated response
  factory PaginatedApiResponse.error({
    required String message,
  }) {
    return PaginatedApiResponse<T>(
      success: false,
      items: [],
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasMore: false,
      message: message,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Creates response from JSON
  factory PaginatedApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedApiResponseFromJson(json, fromJsonT);

  /// Converts response to JSON
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedApiResponseToJson(this, toJsonT);

  /// Whether the response is empty
  bool get isEmpty => items.isEmpty;

  /// Whether the response is not empty
  bool get isNotEmpty => items.isNotEmpty;

  @override
  List<Object?> get props => [
        success,
        items,
        page,
        limit,
        total,
        totalPages,
        hasMore,
        message,
        timestamp,
      ];
}

/// API Exception for error handling
class ApiException implements Exception {
  /// Error message
  final String message;

  /// HTTP status code
  final int? statusCode;

  /// Error details
  final Map<String, dynamic>? details;

  const ApiException({
    required this.message,
    this.statusCode,
    this.details,
  });

  @override
  String toString() {
    final buffer = StringBuffer('ApiException: $message');
    if (statusCode != null) {
      buffer.write(' (Status: $statusCode)');
    }
    if (details != null) {
      buffer.write(' Details: $details');
    }
    return buffer.toString();
  }
}

/// Network exception for connectivity issues
class NetworkException extends ApiException {
  const NetworkException({
    String message = 'Network error occurred',
    int? statusCode,
    Map<String, dynamic>? details,
  }) : super(
          message: message,
          statusCode: statusCode,
          details: details,
        );
}

/// Timeout exception for request timeouts
class TimeoutException extends ApiException {
  const TimeoutException({
    String message = 'Request timeout',
    int? statusCode,
    Map<String, dynamic>? details,
  }) : super(
          message: message,
          statusCode: statusCode,
          details: details,
        );
}

/// Unauthorized exception for authentication errors
class UnauthorizedException extends ApiException {
  const UnauthorizedException({
    String message = 'Unauthorized access',
    int? statusCode = 401,
    Map<String, dynamic>? details,
  }) : super(
          message: message,
          statusCode: statusCode,
          details: details,
        );
}

/// Server exception for server errors
class ServerException extends ApiException {
  const ServerException({
    String message = 'Server error occurred',
    int? statusCode = 500,
    Map<String, dynamic>? details,
  }) : super(
          message: message,
          statusCode: statusCode,
          details: details,
        );
}
