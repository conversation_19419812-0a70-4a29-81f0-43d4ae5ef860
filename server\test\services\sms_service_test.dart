import 'package:test/test.dart';
import 'package:server/services/sms_service.dart';

void main() {
  group('SMSService', () {
    late SMSService smsService;

    setUp(() {
      smsService = SMSService(
        provider: 'mock',
        config: {},
      );
    });

    group('sendMFACode', () {
      test('should send MFA code successfully', () async {
        // Act
        final result = await smsService.sendMFACode(
          '+**********',
          '123456',
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('sendVerificationCode', () {
      test('should send verification code successfully', () async {
        // Act
        final result = await smsService.sendVerificationCode(
          '+**********',
          '654321',
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('sendPasswordResetNotification', () {
      test('should send password reset notification successfully', () async {
        // Act
        final result = await smsService.sendPasswordResetNotification(
          '+**********',
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('sendSecurityAlert', () {
      test('should send security alert successfully', () async {
        // Act
        final result = await smsService.sendSecurityAlert(
          '+**********',
          'Suspicious login attempt',
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('phone number validation', () {
      test('should validate correct E.164 phone numbers', () {
        expect(smsService.isValidPhoneNumber('+**********'), isTrue);
        expect(smsService.isValidPhoneNumber('+447700900123'), isTrue);
        expect(smsService.isValidPhoneNumber('+33123456789'), isTrue);
      });

      test('should reject invalid phone numbers', () {
        expect(smsService.isValidPhoneNumber('**********'), isFalse);
        expect(smsService.isValidPhoneNumber('+'), isFalse);
        expect(smsService.isValidPhoneNumber('invalid'), isFalse);
        expect(smsService.isValidPhoneNumber(''), isFalse);
      });
    });

    group('phone number formatting', () {
      test('should format 10-digit US numbers', () {
        final formatted = smsService.formatPhoneNumber('**********');
        expect(formatted, equals('+1**********'));
      });

      test('should add + to numbers with country code', () {
        final formatted = smsService.formatPhoneNumber('447700900123');
        expect(formatted, equals('+447700900123'));
      });

      test('should handle already formatted numbers', () {
        final formatted = smsService.formatPhoneNumber('+**********');
        expect(formatted, equals('+**********'));
      });

      test('should use custom country code', () {
        final formatted = smsService.formatPhoneNumber('**********', defaultCountryCode: '+44');
        expect(formatted, equals('+44**********'));
      });
    });

    group('different providers', () {
      test('should handle Twilio provider', () async {
        // Arrange
        final twilioService = SMSService(
          provider: 'twilio',
          config: {
            'twilioAccountSid': 'test-sid',
            'twilioAuthToken': 'test-token',
            'twilioFromNumber': '+**********',
          },
        );

        // Act
        final result = await twilioService.sendMFACode(
          '+**********',
          '123456',
        );

        // Assert - Should fail due to invalid credentials but not crash
        expect(result, isFalse);
      });

      test('should handle AWS SNS provider', () async {
        // Arrange
        final snsService = SMSService(
          provider: 'sns',
          config: {},
        );

        // Act
        final result = await snsService.sendMFACode(
          '+**********',
          '123456',
        );

        // Assert - Should use mock implementation
        expect(result, isTrue);
      });
    });
  });
}
