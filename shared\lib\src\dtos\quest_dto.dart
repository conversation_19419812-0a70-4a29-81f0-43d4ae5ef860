import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../models/core/quest.dart';

part 'quest_dto.g.dart';

/// Simple create quest DTO for client use
@JsonSerializable()
class CreateQuestDTO extends Equatable {
  final String title;
  final String description;
  final QuestCategory category;
  final QuestDifficulty difficulty;
  final int pointsReward;
  final DateTime? dueDate;
  final List<String> taskTitles;

  const CreateQuestDTO({
    required this.title,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.pointsReward,
    this.dueDate,
    this.taskTitles = const [],
  });

  factory CreateQuestDTO.fromJson(Map<String, dynamic> json) => _$CreateQuestDTOFromJson(json);
  Map<String, dynamic> toJson() => _$CreateQuestDTOToJson(this);

  @override
  List<Object?> get props => [title, description, category, difficulty, pointsReward, dueDate, taskTitles];
}

/// Simple update quest DTO for client use
@JsonSerializable()
class UpdateQuestDTO extends Equatable {
  final String? title;
  final String? description;
  final QuestStatus? status;
  final QuestCategory? category;
  final QuestDifficulty? difficulty;
  final int? pointsReward;
  final DateTime? dueDate;

  const UpdateQuestDTO({
    this.title,
    this.description,
    this.status,
    this.category,
    this.difficulty,
    this.pointsReward,
    this.dueDate,
  });

  factory UpdateQuestDTO.fromJson(Map<String, dynamic> json) => _$UpdateQuestDTOFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateQuestDTOToJson(this);

  @override
  List<Object?> get props => [title, description, status, category, difficulty, pointsReward, dueDate];
}

/// Create quest request DTO
@JsonSerializable()
class CreateQuestDto extends Equatable {
  final String title;
  final String description;
  final String? assignedToId;
  final QuestPriority priority;
  final QuestDifficulty difficulty;
  final QuestCategory category;
  final int? estimatedHours;
  final DateTime? deadline;
  final List<String> participantIds;
  final List<String> tags;
  final Map<String, dynamic>? metadata;

  const CreateQuestDto({
    required this.title,
    required this.description,
    this.assignedToId,
    required this.priority,
    required this.difficulty,
    required this.category,
    this.estimatedHours,
    this.deadline,
    required this.participantIds,
    required this.tags,
    this.metadata,
  });

  factory CreateQuestDto.fromJson(Map<String, dynamic> json) => _$CreateQuestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$CreateQuestDtoToJson(this);

  @override
  List<Object?> get props => [
        title,
        description,
        assignedToId,
        priority,
        difficulty,
        category,
        estimatedHours,
        deadline,
        participantIds,
        tags,
        metadata,
      ];
}

/// Update quest request DTO
@JsonSerializable()
class UpdateQuestDto extends Equatable {
  final String? title;
  final String? description;
  final String? assignedToId;
  final QuestStatus? status;
  final QuestPriority? priority;
  final QuestDifficulty? difficulty;
  final QuestCategory? category;
  final int? estimatedHours;
  final int? actualHours;
  final DateTime? deadline;
  final double? progressPercentage;
  final List<String>? participantIds;
  final List<String>? tags;
  final Map<String, dynamic>? metadata;

  const UpdateQuestDto({
    this.title,
    this.description,
    this.assignedToId,
    this.status,
    this.priority,
    this.difficulty,
    this.category,
    this.estimatedHours,
    this.actualHours,
    this.deadline,
    this.progressPercentage,
    this.participantIds,
    this.tags,
    this.metadata,
  });

  factory UpdateQuestDto.fromJson(Map<String, dynamic> json) => _$UpdateQuestDtoFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateQuestDtoToJson(this);

  @override
  List<Object?> get props => [
        title,
        description,
        assignedToId,
        status,
        priority,
        difficulty,
        category,
        estimatedHours,
        actualHours,
        deadline,
        progressPercentage,
        participantIds,
        tags,
        metadata,
      ];
}

/// Quest filter parameters DTO
@JsonSerializable()
class QuestFilterDto extends Equatable {
  final List<QuestStatus>? statuses;
  final List<QuestPriority>? priorities;
  final List<QuestDifficulty>? difficulties;
  final List<QuestCategory>? categories;
  final String? createdById;
  final String? assignedToId;
  final String? participantId;
  final List<String>? tags;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final DateTime? deadlineAfter;
  final DateTime? deadlineBefore;
  final bool? isOverdue;
  final String? search;
  final int? limit;
  final int? offset;
  final String? sortBy;
  final String? sortOrder;

  const QuestFilterDto({
    this.statuses,
    this.priorities,
    this.difficulties,
    this.categories,
    this.createdById,
    this.assignedToId,
    this.participantId,
    this.tags,
    this.createdAfter,
    this.createdBefore,
    this.deadlineAfter,
    this.deadlineBefore,
    this.isOverdue,
    this.search,
    this.limit,
    this.offset,
    this.sortBy,
    this.sortOrder,
  });

  factory QuestFilterDto.fromJson(Map<String, dynamic> json) => _$QuestFilterDtoFromJson(json);
  Map<String, dynamic> toJson() => _$QuestFilterDtoToJson(this);

  @override
  List<Object?> get props => [
        statuses,
        priorities,
        difficulties,
        categories,
        createdById,
        assignedToId,
        participantId,
        tags,
        createdAfter,
        createdBefore,
        deadlineAfter,
        deadlineBefore,
        isOverdue,
        search,
        limit,
        offset,
        sortBy,
        sortOrder,
      ];
}

/// Quest list response DTO
@JsonSerializable()
class QuestListResponseDto extends Equatable {
  final List<Quest> quests;
  final int totalCount;
  final int pageCount;
  final int currentPage;
  final bool hasNext;
  final bool hasPrevious;

  const QuestListResponseDto({
    required this.quests,
    required this.totalCount,
    required this.pageCount,
    required this.currentPage,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory QuestListResponseDto.fromJson(Map<String, dynamic> json) => _$QuestListResponseDtoFromJson(json);
  Map<String, dynamic> toJson() => _$QuestListResponseDtoToJson(this);

  @override
  List<Object?> get props => [
        quests,
        totalCount,
        pageCount,
        currentPage,
        hasNext,
        hasPrevious,
      ];
}

/// Quest statistics DTO
@JsonSerializable()
class QuestStatsDto extends Equatable {
  final int totalQuests;
  final int activeQuests;
  final int completedQuests;
  final int overdueQuests;
  final int todayDeadlines;
  final int thisWeekDeadlines;
  final double completionRate;
  final double averageCompletionTime;
  final int totalPointsEarned;
  final int totalPointsAvailable;
  final Map<String, int> statusBreakdown;
  final Map<String, int> priorityBreakdown;
  final Map<String, int> categoryBreakdown;

  const QuestStatsDto({
    required this.totalQuests,
    required this.activeQuests,
    required this.completedQuests,
    required this.overdueQuests,
    required this.todayDeadlines,
    required this.thisWeekDeadlines,
    required this.completionRate,
    required this.averageCompletionTime,
    required this.totalPointsEarned,
    required this.totalPointsAvailable,
    required this.statusBreakdown,
    required this.priorityBreakdown,
    required this.categoryBreakdown,
  });

  factory QuestStatsDto.fromJson(Map<String, dynamic> json) => _$QuestStatsDtoFromJson(json);
  Map<String, dynamic> toJson() => _$QuestStatsDtoToJson(this);

  @override
  List<Object?> get props => [
        totalQuests,
        activeQuests,
        completedQuests,
        overdueQuests,
        todayDeadlines,
        thisWeekDeadlines,
        completionRate,
        averageCompletionTime,
        totalPointsEarned,
        totalPointsAvailable,
        statusBreakdown,
        priorityBreakdown,
        categoryBreakdown,
      ];
}

/// Quest completion DTO
@JsonSerializable()
class QuestCompletionDto extends Equatable {
  final String questId;
  final int actualHours;
  final String? completionNotes;
  final Map<String, dynamic>? completionData;

  const QuestCompletionDto({
    required this.questId,
    required this.actualHours,
    this.completionNotes,
    this.completionData,
  });

  factory QuestCompletionDto.fromJson(Map<String, dynamic> json) => _$QuestCompletionDtoFromJson(json);
  Map<String, dynamic> toJson() => _$QuestCompletionDtoToJson(this);

  @override
  List<Object?> get props => [questId, actualHours, completionNotes, completionData];
}

/// Add participant DTO
@JsonSerializable()
class AddParticipantDto extends Equatable {
  final String questId;
  final String userId;
  final String? role;
  final String? inviteMessage;

  const AddParticipantDto({
    required this.questId,
    required this.userId,
    this.role,
    this.inviteMessage,
  });

  factory AddParticipantDto.fromJson(Map<String, dynamic> json) => _$AddParticipantDtoFromJson(json);
  Map<String, dynamic> toJson() => _$AddParticipantDtoToJson(this);

  @override
  List<Object?> get props => [questId, userId, role, inviteMessage];
}