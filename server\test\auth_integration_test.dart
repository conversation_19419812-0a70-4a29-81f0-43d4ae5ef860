import 'dart:io';
import 'package:test/test.dart';
import 'package:server/services/auth_service.dart';
import 'package:server/services/database_service.dart';
// import 'package:server/services/audit_log_service.dart'; // Reserved for future audit testing

void main() {
  group('Authentication System Integration Tests', () {
    late AuthService authService;
    late DatabaseService dbService;
    // late AuditLogService auditService; // Reserved for future audit testing
    
    setUpAll(() async {
      // Load environment variables
      await _loadEnvironment();
      
      // Initialize services
      authService = AuthService();
      dbService = DatabaseService();
      // auditService = AuditLogService(); // Reserved for future audit testing
      
      await dbService.initialize();
      await authService.initialize();
    });
    
    tearDownAll(() async {
      await dbService.close();
    });

    group('User Registration', () {
      test('should register a new user successfully', () async {
        final testEmail = 'test_${DateTime.now().millisecondsSinceEpoch}@example.com';
        
        final result = await authService.register(
          email: testEmail,
          password: 'SecurePassword123!',
          displayName: 'Test User',
          firstName: 'Test',
          lastName: 'User',
          acceptTerms: true,
        );

        expect(result['success'], isTrue);
        expect(result['userId'], isNotNull);
        expect(result['requiresEmailVerification'], isTrue);
      });

      test('should reject weak passwords', () async {
        final testEmail = 'weak_${DateTime.now().millisecondsSinceEpoch}@example.com';
        
        final result = await authService.register(
          email: testEmail,
          password: '123',
          displayName: 'Test User',
          acceptTerms: true,
        );

        expect(result['success'], isFalse);
        expect(result['errorCode'], equals('AUTH_WEAK_PASSWORD'));
        expect(result['passwordPolicy'], isNotNull);
      });

      test('should reject duplicate email addresses', () async {
        final testEmail = 'duplicate_${DateTime.now().millisecondsSinceEpoch}@example.com';
        
        // Register first user
        await authService.register(
          email: testEmail,
          password: 'SecurePassword123!',
          displayName: 'First User',
          acceptTerms: true,
        );

        // Try to register second user with same email
        final result = await authService.register(
          email: testEmail,
          password: 'AnotherPassword123!',
          displayName: 'Second User',
          acceptTerms: true,
        );

        expect(result['success'], isFalse);
        expect(result['errorCode'], equals('AUTH_USER_EXISTS'));
      });
    });

    group('User Authentication', () {
      late String testEmail;
      late String testPassword;

      setUpAll(() async {
        testEmail = 'auth_test_${DateTime.now().millisecondsSinceEpoch}@example.com';
        testPassword = 'SecurePassword123!';
        
        // Create test user
        await authService.register(
          email: testEmail,
          password: testPassword,
          displayName: 'Auth Test User',
          acceptTerms: true,
        );
      });

      test('should authenticate with valid credentials', () async {
        final result = await authService.authenticate(
          email: testEmail,
          password: testPassword,
        );

        expect(result['success'], isTrue);
        expect(result['session'], isNotNull);
        
        final session = result['session'];
        expect(session['token'], isNotNull);
        expect(session['refreshToken'], isNotNull);
        expect(session['user']['email'], equals(testEmail));
      });

      test('should reject invalid credentials', () async {
        final result = await authService.authenticate(
          email: testEmail,
          password: 'WrongPassword',
        );

        expect(result['success'], isFalse);
        expect(result['errorCode'], equals('AUTH_INVALID_CREDENTIALS'));
      });

      test('should reject non-existent user', () async {
        final result = await authService.authenticate(
          email: '<EMAIL>',
          password: 'AnyPassword',
        );

        expect(result['success'], isFalse);
        expect(result['errorCode'], equals('AUTH_INVALID_CREDENTIALS'));
      });
    });

    group('Session Management', () {
      late Map<String, dynamic> authSession;

      setUpAll(() async {
        final testEmail = 'session_test_${DateTime.now().millisecondsSinceEpoch}@example.com';
        final testPassword = 'SecurePassword123!';
        
        await authService.register(
          email: testEmail,
          password: testPassword,
          displayName: 'Session Test User',
          acceptTerms: true,
        );

        final authResult = await authService.authenticate(
          email: testEmail,
          password: testPassword,
        );

        authSession = authResult['session'];
      });

      test('should validate session token', () async {
        final sessionId = authSession['sessionId'] ?? 'test-session-id';
        final result = await authService.validateSession(sessionId);

        expect(result, isNotNull);
        expect(result!.user.email, isNotNull);
      });

      test('should refresh token', () async {
        final refreshToken = authSession['refreshToken'];
        final result = await authService.refreshToken(refreshToken);

        expect(result['success'], isTrue);
        expect(result['session'], isNotNull);
        expect(result['session']['token'], isNot(equals(authSession['token'])));
      });

      test('should logout and invalidate session', () async {
        final sessionId = authSession['sessionId'] ?? 'test-session-id';
        
        final logoutResult = await authService.logout(sessionId);
        expect(logoutResult['success'], isTrue);

        // Try to validate the session after logout
        final validateResult = await authService.validateSession(sessionId);
        expect(validateResult, isNull);
      });
    });

    group('Password Security', () {
      test('should enforce password policy', () async {
        final policy = authService.getPasswordPolicy();
        
        expect(policy['minimumLength'], equals(8));
        expect(policy['requiresUppercase'], isTrue);
        expect(policy['requiresLowercase'], isTrue);
        expect(policy['requiresNumbers'], isTrue);
      });

      test('should validate password strength', () async {
        // Test weak password
        final weakResult = await authService.register(
          email: 'weak_test_${DateTime.now().millisecondsSinceEpoch}@example.com',
          password: '123',
          displayName: 'Test User',
          acceptTerms: true,
        );
        
        expect(weakResult['success'], isFalse);
        expect(weakResult['errorCode'], equals('AUTH_WEAK_PASSWORD'));
        
        // Test strong password
        final strongResult = await authService.register(
          email: 'strong_test_${DateTime.now().millisecondsSinceEpoch}@example.com',
          password: 'StrongPassword123!',
          displayName: 'Test User',
          acceptTerms: true,
        );
        
        expect(strongResult['success'], isTrue);
      });
    });
  });
}

Future<void> _loadEnvironment() async {
  try {
    final envFile = File('server/.env');
    if (await envFile.exists()) {
      final lines = await envFile.readAsLines();
      for (final line in lines) {
        if (line.trim().isEmpty || line.startsWith('#')) continue;
        
        final parts = line.split('=');
        if (parts.length == 2) {
          final key = parts[0].trim();
          final value = parts[1].trim();
          Platform.environment[key] = value;
        }
      }
    }
  } catch (e) {
    print('Warning: Could not load .env file: $e');
  }
}
