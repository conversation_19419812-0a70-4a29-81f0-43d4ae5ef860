import 'dart:async';
import 'package:shared/shared.dart';
import 'database_service.dart';
import 'notification_service.dart';
import 'cache_service.dart';

/// Service for handling real-time messaging and chat functionality
class MessagingService {
  static const String version = '1.0.0';
  
  final DatabaseService _dbService;
  final NotificationService _notificationService;
  final CacheService _cacheService;

  // Active typing indicators
  final Map<String, Map<String, Timer>> _typingIndicators = {};
  
  MessagingService({
    required DatabaseService dbService,
    required NotificationService notificationService,
    required CacheService cacheService,
  }) : _dbService = dbService,
       _notificationService = notificationService,
       _cacheService = cacheService;

  /// Initialize the messaging service
  Future<void> initialize() async {
    print('💬 Initializing Messaging Service v$version');
    await _setupMessageCleanup();
  }

  /// Create a new chat
  Future<Chat> createChat({
    required String createdBy,
    required ChatType type,
    String? name,
    String? description,
    List<String> participantIds = const [],
    ChatSettings? settings,
  }) async {
    final chatId = _generateChatId();
    final now = DateTime.now();
    
    final chat = Chat(
      id: chatId,
      name: name,
      description: description,
      type: type,
      createdBy: createdBy,
      participantIds: [createdBy, ...participantIds],
      settings: settings ?? const ChatSettings(),
      createdAt: now,
      updatedAt: now,
    );

    // Store in database
    await _dbService.insertChat(chat.toJson());

    // Cache the chat
    await _cacheService.set('chat:$chatId', chat.toJson());
    
    // Notify participants via WebSocket
    await _notifyParticipants(chat.participantIds, 'chat_created', {
      'chat': chat.toJson(),
    });

    return chat;
  }

  /// Send a message
  Future<Message> sendMessage({
    required String chatId,
    required String senderId,
    required String content,
    required MessageType type,
    String? replyToId,
    Map<String, dynamic>? metadata,
  }) async {
    // Validate chat exists and user has permission
    final chat = await getChat(chatId);
    if (chat == null) {
      throw Exception('Chat not found');
    }
    
    if (!chat.participantIds.contains(senderId)) {
      throw Exception('User not a participant in this chat');
    }

    // Get sender info
    final sender = await _dbService.getUserById(senderId);
    if (sender == null) {
      throw Exception('Sender not found');
    }

    final messageId = _generateMessageId();
    final now = DateTime.now();
    
    final message = Message(
      id: messageId,
      chatId: chatId,
      senderId: senderId,
      senderName: sender['display_name'] ?? sender['username'] ?? 'Unknown',
      senderAvatar: sender['avatar_url'],
      content: content,
      type: type,
      status: MessageStatus.sent,
      createdAt: now,
      replyToId: replyToId,
      metadata: metadata,
    );

    // Store message in database
    await _dbService.insertMessage(message.toJson());
    
    // Update chat's last message info
    await _updateChatLastMessage(chatId, messageId, now);
    
    // Cache recent messages
    await _cacheRecentMessage(chatId, message);
    
    // Send real-time updates to participants
    await _broadcastMessage(message, chat.participantIds);
    
    // Send notifications to offline participants
    await _sendMessageNotifications(message, chat);
    
    // Clear typing indicator for sender
    await _clearTypingIndicator(chatId, senderId);

    return message;
  }

  /// Get chat by ID
  Future<Chat?> getChat(String chatId) async {
    // Try cache first
    final cached = await _cacheService.get('chat:$chatId');
    if (cached != null) {
      return Chat.fromJson(cached);
    }
    
    // Fetch from database
    final chat = await _dbService.getChatById(chatId);
    if (chat != null) {
      // Cache for future requests
      await _cacheService.set('chat:$chatId', chat);
      return Chat.fromJson(chat);
    }

    return null;
  }

  /// Get messages for a chat with pagination
  Future<List<Message>> getMessages({
    required String chatId,
    int page = 1,
    int pageSize = 50,
    String? beforeMessageId,
  }) async {
    // Validate user has access to chat
    final chat = await getChat(chatId);
    if (chat == null) {
      throw Exception('Chat not found');
    }

    final messagesData = await _dbService.getMessages(
      chatId,
      limit: pageSize,
      offset: (page - 1) * pageSize,
    );

    return messagesData.map((data) => Message.fromJson(data)).toList();
  }

  /// Get user's chats
  Future<List<Chat>> getUserChats(String userId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    final chatsData = await _dbService.getUserChats(userId);
    return chatsData.map((data) => Chat.fromJson(data)).toList();
  }

  /// Add reaction to message
  Future<void> addReaction({
    required String messageId,
    required String userId,
    required String emoji,
  }) async {
    final message = await _dbService.getMessageById(messageId);
    if (message == null) {
      throw Exception('Message not found');
    }

    // Check if user already reacted with this emoji
    final reactions = (message['reactions'] as List<dynamic>?) ?? [];
    final existingReactionIndex = reactions.indexWhere(
      (r) => r['userId'] == userId && r['emoji'] == emoji,
    );

    if (existingReactionIndex != -1) {
      return; // Already reacted
    }

    final user = await _dbService.getUserById(userId);
    if (user == null) {
      throw Exception('User not found');
    }

    final reaction = MessageReaction(
      emoji: emoji,
      userId: userId,
      userName: user['displayName'] ?? user['username'] ?? 'Unknown',
      createdAt: DateTime.now(),
    );

    // Add reaction to message
    final updatedReactions = [...reactions, reaction.toJson()];
    final updatedMessage = Map<String, dynamic>.from(message);
    updatedMessage['reactions'] = updatedReactions;

    await _dbService.updateMessage(message['chatId'], updatedMessage);

    // Broadcast reaction update
    final chat = await getChat(message['chatId']);
    if (chat != null) {
      await _notifyParticipants(chat.participantIds, 'message_reaction_added', {
        'messageId': messageId,
        'reaction': reaction.toJson(),
      });
    }
  }

  /// Remove reaction from message
  Future<void> removeReaction({
    required String messageId,
    required String userId,
    required String emoji,
  }) async {
    final message = await _dbService.getMessageById(messageId);
    if (message == null) {
      throw Exception('Message not found');
    }

    // Remove the reaction
    final currentReactions = (message['reactions'] as List<dynamic>?) ?? [];
    final updatedReactions = currentReactions
        .where((r) => !(r['userId'] == userId && r['emoji'] == emoji))
        .toList();

    final updatedMessage = Map<String, dynamic>.from(message);
    updatedMessage['reactions'] = updatedReactions;
    await _dbService.updateMessage(message['chatId'], updatedMessage);

    // Broadcast reaction update
    final chat = await getChat(message['chatId']);
    if (chat != null) {
      await _notifyParticipants(chat.participantIds, 'message_reaction_removed', {
        'messageId': messageId,
        'userId': userId,
        'emoji': emoji,
      });
    }
  }

  /// Start typing indicator
  Future<void> startTyping({
    required String chatId,
    required String userId,
  }) async {
    final chat = await getChat(chatId);
    if (chat == null || !chat.participantIds.contains(userId)) {
      return;
    }

    // Clear existing timer
    _typingIndicators[chatId]?[userId]?.cancel();
    
    // Set new timer
    _typingIndicators[chatId] ??= {};
    _typingIndicators[chatId]![userId] = Timer(
      const Duration(seconds: 3),
      () => _clearTypingIndicator(chatId, userId),
    );

    // Notify other participants
    final otherParticipants = chat.participantIds.where((id) => id != userId).toList();
    await _notifyParticipants(otherParticipants, 'typing_start', {
      'chatId': chatId,
      'userId': userId,
    });
  }

  /// Stop typing indicator
  Future<void> stopTyping({
    required String chatId,
    required String userId,
  }) async {
    await _clearTypingIndicator(chatId, userId);
  }

  /// Mark message as read
  Future<void> markMessageAsRead({
    required String messageId,
    required String userId,
  }) async {
    final message = await _dbService.getMessageById(messageId);
    if (message == null) {
      return;
    }

    final readBy = (message['readBy'] as List<dynamic>?) ?? [];
    if (readBy.contains(userId)) {
      return; // Already read
    }

    final updatedMessage = Map<String, dynamic>.from(message);
    updatedMessage['readBy'] = [...readBy, userId];
    if (message['senderId'] != userId) {
      updatedMessage['readAt'] = DateTime.now().toIso8601String();
    }

    await _dbService.updateMessage(message['chatId'], updatedMessage);

    // Notify sender about read receipt
    if (message['senderId'] != userId) {
      await _notifyParticipants([message['senderId']], 'message_read', {
        'messageId': messageId,
        'readBy': userId,
        'readAt': updatedMessage['readAt'],
      });
    }
  }

  /// Private helper methods

  String _generateChatId() {
    return 'chat_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(8)}';
  }

  String _generateMessageId() {
    return 'msg_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(8)}';
  }

  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    return List.generate(length, (index) => chars[DateTime.now().millisecond % chars.length]).join();
  }

  Future<void> _updateChatLastMessage(String chatId, String messageId, DateTime timestamp) async {
    await _dbService.updateChatLastMessage(chatId, messageId, timestamp.toIso8601String());
    
    // Update cache
    final cached = await _cacheService.get('chat:$chatId');
    if (cached != null) {
      final chat = Map<String, dynamic>.from(cached);
      chat['lastMessageId'] = messageId;
      chat['lastMessageAt'] = timestamp.toIso8601String();
      chat['updatedAt'] = timestamp.toIso8601String();
      await _cacheService.set('chat:$chatId', chat);
    }
  }

  Future<void> _cacheRecentMessage(String chatId, Message message) async {
    final cacheKey = 'recent_messages:$chatId';
    final cached = await _cacheService.get(cacheKey) ?? [];
    final messages = (cached as List).map((m) => Message.fromJson(m)).toList();
    
    messages.insert(0, message);
    if (messages.length > 50) {
      messages.removeLast();
    }
    
    await _cacheService.set(cacheKey, messages.map((m) => m.toJson()).toList());
  }

  Future<void> _broadcastMessage(Message message, List<String> participantIds) async {
    await _notifyParticipants(participantIds, 'message_received', {
      'message': message.toJson(),
    });
  }

  Future<void> _sendMessageNotifications(Message message, Chat chat) async {
    // Send notifications to participants who are not currently online
    for (final participantId in chat.participantIds) {
      if (participantId == message.senderId) continue;
      
      // Send notification to offline users (simplified - always send)
      {
        await _notificationService.sendNotification(
          userId: participantId,
          type: NotificationType.messageReceived,
          title: 'New message from ${message.senderName}',
          body: _truncateMessage(message.content),
          actionUrl: '/chat/${chat.id}',
          metadata: {
            'chatId': chat.id,
            'messageId': message.id,
            'senderId': message.senderId,
          },
        );
      }
    }
  }

  String _truncateMessage(String content, {int maxLength = 100}) {
    if (content.length <= maxLength) return content;
    return '${content.substring(0, maxLength)}...';
  }

  Future<void> _clearTypingIndicator(String chatId, String userId) async {
    _typingIndicators[chatId]?[userId]?.cancel();
    _typingIndicators[chatId]?.remove(userId);
    
    if (_typingIndicators[chatId]?.isEmpty == true) {
      _typingIndicators.remove(chatId);
    }

    // Notify other participants
    final chat = await getChat(chatId);
    if (chat != null) {
      final otherParticipants = chat.participantIds.where((id) => id != userId).toList();
      await _notifyParticipants(otherParticipants, 'typing_stop', {
        'chatId': chatId,
        'userId': userId,
      });
    }
  }

  Future<void> _notifyParticipants(List<String> userIds, String eventType, Map<String, dynamic> data) async {
    for (final userId in userIds) {
      // Simplified WebSocket notification
      print('📡 Notifying user $userId: $eventType');
    }
  }

  Future<void> _setupMessageCleanup() async {
    // Set up periodic cleanup of old messages, typing indicators, etc.
    Timer.periodic(const Duration(hours: 1), (timer) {
      _cleanupTypingIndicators();
    });
  }

  void _cleanupTypingIndicators() {
    _typingIndicators.removeWhere((chatId, indicators) {
      indicators.removeWhere((userId, timer) {
        if (!timer.isActive) {
          timer.cancel();
          return true;
        }
        return false;
      });
      return indicators.isEmpty;
    });
  }
}
