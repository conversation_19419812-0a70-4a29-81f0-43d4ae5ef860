import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'achievement.g.dart';

/// Achievement categories from CLAUDE.md Phase 2 gamification system
enum AchievementCategory {
  @JsonValue('completion')
  completion, // Quest/task completion achievements
  @JsonValue('streak')
  streak, // Consecutive day achievements
  @JsonValue('speed')
  speed, // Time-based achievements
  @JsonValue('collaboration')
  collaboration, // Team/social achievements
  @JsonValue('quality')
  quality, // High-quality work achievements
  @JsonValue('milestone')
  milestone, // Level/point milestones
  @JsonValue('special')
  special, // Unique/event achievements
  @JsonValue('exploration')
  exploration, // Feature usage achievements
}

/// Achievement rarity levels (5 levels as per CLAUDE.md)
enum AchievementRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

/// Achievement unlock conditions
enum UnlockCondition {
  @JsonValue('quest_completion')
  questCompletion,
  @JsonValue('task_completion')
  taskCompletion,
  @JsonValue('streak_days')
  streakDays,
  @JsonValue('points_earned')
  pointsEarned,
  @JsonValue('level_reached')
  levelReached,
  @JsonValue('time_efficiency')
  timeEfficiency,
  @JsonValue('collaboration_count')
  collaborationCount,
  @JsonValue('feature_usage')
  featureUsage,
}

/// Achievement model with comprehensive gamification features
@JsonSerializable()
class Achievement extends Equatable {
  /// Unique achievement identifier
  final String id;

  /// Achievement title
  final String title;

  /// Achievement description
  final String description;

  /// Achievement category
  final AchievementCategory category;

  /// Achievement rarity level
  final AchievementRarity rarity;

  /// Icon identifier or URL
  final String icon;

  /// Points awarded when unlocked
  final int points;

  /// Unlock condition type
  final UnlockCondition unlockCondition;

  /// Required value to unlock (e.g., 10 quests, 100 points, 7 days streak)
  final int requiredValue;

  /// Optional secondary condition value
  final int? secondaryValue;

  /// Whether this is a hidden achievement (not shown until unlocked)
  final bool isHidden;

  /// Whether this achievement is currently active
  final bool isActive;

  /// Achievement metadata for complex conditions
  final Map<String, dynamic>? metadata;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.rarity,
    required this.icon,
    required this.points,
    required this.unlockCondition,
    required this.requiredValue,
    this.secondaryValue,
    required this.isHidden,
    required this.isActive,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Achievement from JSON
  factory Achievement.fromJson(Map<String, dynamic> json) => _$AchievementFromJson(json);

  /// Convert Achievement to JSON
  Map<String, dynamic> toJson() => _$AchievementToJson(this);

  /// Get rarity multiplier for points
  double get rarityMultiplier {
    switch (rarity) {
      case AchievementRarity.common:
        return 1.0;
      case AchievementRarity.uncommon:
        return 1.5;
      case AchievementRarity.rare:
        return 2.0;
      case AchievementRarity.epic:
        return 3.0;
      case AchievementRarity.legendary:
        return 5.0;
    }
  }

  /// Get total points with rarity multiplier
  int get totalPoints => (points * rarityMultiplier).round();

  /// Get rarity display name
  String get rarityDisplayName {
    switch (rarity) {
      case AchievementRarity.common:
        return 'Common';
      case AchievementRarity.uncommon:
        return 'Uncommon';
      case AchievementRarity.rare:
        return 'Rare';
      case AchievementRarity.epic:
        return 'Epic';
      case AchievementRarity.legendary:
        return 'Legendary';
    }
  }

  /// Get category display name
  String get categoryDisplayName {
    switch (category) {
      case AchievementCategory.completion:
        return 'Completion';
      case AchievementCategory.streak:
        return 'Streak';
      case AchievementCategory.speed:
        return 'Speed';
      case AchievementCategory.collaboration:
        return 'Collaboration';
      case AchievementCategory.quality:
        return 'Quality';
      case AchievementCategory.milestone:
        return 'Milestone';
      case AchievementCategory.special:
        return 'Special';
      case AchievementCategory.exploration:
        return 'Exploration';
    }
  }

  /// Get unlock condition display name
  String get unlockConditionDisplayName {
    switch (unlockCondition) {
      case UnlockCondition.questCompletion:
        return 'Complete quests';
      case UnlockCondition.taskCompletion:
        return 'Complete tasks';
      case UnlockCondition.streakDays:
        return 'Maintain streak';
      case UnlockCondition.pointsEarned:
        return 'Earn points';
      case UnlockCondition.levelReached:
        return 'Reach level';
      case UnlockCondition.timeEfficiency:
        return 'Time efficiency';
      case UnlockCondition.collaborationCount:
        return 'Collaborate';
      case UnlockCondition.featureUsage:
        return 'Use features';
    }
  }

  /// Check if achievement can be unlocked with current progress
  bool canUnlock({
    required int currentValue,
    int? currentSecondaryValue,
  }) {
    if (!isActive) return false;
    
    final primaryMet = currentValue >= requiredValue;
    if (secondaryValue == null) return primaryMet;
    
    final secondaryMet = (currentSecondaryValue ?? 0) >= secondaryValue!;
    return primaryMet && secondaryMet;
  }

  /// Get progress percentage towards unlocking (0.0 - 1.0)
  double getProgress({
    required int currentValue,
    int? currentSecondaryValue,
  }) {
    final primaryProgress = (currentValue / requiredValue).clamp(0.0, 1.0);
    
    if (secondaryValue == null) return primaryProgress;
    
    final secondaryProgress = ((currentSecondaryValue ?? 0) / secondaryValue!).clamp(0.0, 1.0);
    return (primaryProgress + secondaryProgress) / 2.0; // Average of both conditions
  }

  /// Create a copy with updated fields
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    AchievementCategory? category,
    AchievementRarity? rarity,
    String? icon,
    int? points,
    UnlockCondition? unlockCondition,
    int? requiredValue,
    int? secondaryValue,
    bool? isHidden,
    bool? isActive,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      rarity: rarity ?? this.rarity,
      icon: icon ?? this.icon,
      points: points ?? this.points,
      unlockCondition: unlockCondition ?? this.unlockCondition,
      requiredValue: requiredValue ?? this.requiredValue,
      secondaryValue: secondaryValue ?? this.secondaryValue,
      isHidden: isHidden ?? this.isHidden,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create empty achievement for initialization
  static Achievement empty() {
    final now = DateTime.now();
    return Achievement(
      id: '',
      title: '',
      description: '',
      category: AchievementCategory.completion,
      rarity: AchievementRarity.common,
      icon: '',
      points: 0,
      unlockCondition: UnlockCondition.questCompletion,
      requiredValue: 1,
      isHidden: false,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        category,
        rarity,
        icon,
        points,
        unlockCondition,
        requiredValue,
        secondaryValue,
        isHidden,
        isActive,
        metadata,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}