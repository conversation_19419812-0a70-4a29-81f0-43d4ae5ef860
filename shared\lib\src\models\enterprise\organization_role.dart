import 'package:equatable/equatable.dart';

/// Organization role permissions
enum Permission {
  // Organization management
  orgRead,
  orgWrite,
  orgDelete,
  orgBilling,
  orgSettings,
  
  // User management
  userRead,
  userWrite,
  userDelete,
  userInvite,
  userRoles,
  
  // Team management
  teamRead,
  teamWrite,
  team<PERSON><PERSON>te,
  team<PERSON>reate,
  teamMem<PERSON>,
  
  // Quest management
  questRead,
  questWrite,
  questDelete,
  questCreate,
  questAssign,
  
  // Analytics and reporting
  analyticsRead,
  analyticsExport,
  reportsRead,
  reportsCreate,
  
  // Gamification
  gamificationRead,
  gamificationWrite,
  rewardsManage,
  achievementsManage,
  
  // API access
  apiRead,
  apiWrite,
  apiAdmin,
  
  // System administration
  systemLogs,
  systemHealth,
  systemBackup,
  systemMaintenance,
}

/// Organization role model for RBAC
class OrganizationRole extends Equatable {
  /// Unique role identifier
  final String id;

  /// Organization ID this role belongs to
  final String organizationId;

  /// Role name
  final String name;

  /// Role description
  final String? description;

  /// Role color for UI display
  final String? color;

  /// Whether this is a system-defined role
  final bool isSystemRole;

  /// Whether this role is active
  final bool isActive;

  /// Role permissions
  final Set<Permission> permissions;

  /// Role priority (higher number = higher priority)
  final int priority;

  /// Role metadata
  final Map<String, dynamic>? metadata;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const OrganizationRole({
    required this.id,
    required this.organizationId,
    required this.name,
    this.description,
    this.color,
    required this.isSystemRole,
    required this.isActive,
    required this.permissions,
    required this.priority,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create OrganizationRole from JSON
  factory OrganizationRole.fromJson(Map<String, dynamic> json) {
    return OrganizationRole(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String?,
      isSystemRole: json['isSystemRole'] as bool,
      isActive: json['isActive'] as bool,
      permissions: (json['permissions'] as List<dynamic>)
          .map((p) => Permission.values.firstWhere(
                (e) => e.name == p,
                orElse: () => Permission.orgRead,
              ))
          .toSet(),
      priority: json['priority'] as int,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert OrganizationRole to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'name': name,
      'description': description,
      'color': color,
      'isSystemRole': isSystemRole,
      'isActive': isActive,
      'permissions': permissions.map((p) => p.name).toList(),
      'priority': priority,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Check if role has specific permission
  bool hasPermission(Permission permission) {
    return permissions.contains(permission);
  }

  /// Check if role has any of the given permissions
  bool hasAnyPermission(Set<Permission> requiredPermissions) {
    return permissions.any(requiredPermissions.contains);
  }

  /// Check if role has all of the given permissions
  bool hasAllPermissions(Set<Permission> requiredPermissions) {
    return requiredPermissions.every(permissions.contains);
  }

  /// Get permission level for organization operations
  String get organizationPermissionLevel {
    if (hasPermission(Permission.orgDelete)) return 'admin';
    if (hasPermission(Permission.orgWrite)) return 'manager';
    if (hasPermission(Permission.orgRead)) return 'viewer';
    return 'none';
  }

  /// Get permission level for user operations
  String get userPermissionLevel {
    if (hasPermission(Permission.userDelete)) return 'admin';
    if (hasPermission(Permission.userWrite)) return 'manager';
    if (hasPermission(Permission.userRead)) return 'viewer';
    return 'none';
  }

  /// Get permission level for team operations
  String get teamPermissionLevel {
    if (hasPermission(Permission.teamDelete)) return 'admin';
    if (hasPermission(Permission.teamWrite)) return 'manager';
    if (hasPermission(Permission.teamRead)) return 'viewer';
    return 'none';
  }

  /// Check if this is an admin role
  bool get isAdmin {
    return hasPermission(Permission.orgDelete) && 
           hasPermission(Permission.userDelete);
  }

  /// Check if this is a manager role
  bool get isManager {
    return hasPermission(Permission.orgWrite) && 
           hasPermission(Permission.userWrite);
  }

  /// Check if this is a viewer role
  bool get isViewer {
    return hasPermission(Permission.orgRead) && 
           hasPermission(Permission.userRead) &&
           !hasPermission(Permission.orgWrite);
  }

  /// Create a copy with updated fields
  OrganizationRole copyWith({
    String? id,
    String? organizationId,
    String? name,
    String? description,
    String? color,
    bool? isSystemRole,
    bool? isActive,
    Set<Permission>? permissions,
    int? priority,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrganizationRole(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      isSystemRole: isSystemRole ?? this.isSystemRole,
      isActive: isActive ?? this.isActive,
      permissions: permissions ?? this.permissions,
      priority: priority ?? this.priority,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        description,
        color,
        isSystemRole,
        isActive,
        permissions,
        priority,
        metadata,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;

  /// System role presets
  static OrganizationRole createOwnerRole(String organizationId) {
    return OrganizationRole(
      id: 'owner-$organizationId',
      organizationId: organizationId,
      name: 'Owner',
      description: 'Full access to all organization features',
      color: '#FF0000',
      isSystemRole: true,
      isActive: true,
      permissions: Permission.values.toSet(),
      priority: 1000,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static OrganizationRole createAdminRole(String organizationId) {
    return OrganizationRole(
      id: 'admin-$organizationId',
      organizationId: organizationId,
      name: 'Admin',
      description: 'Administrative access to organization features',
      color: '#FF6600',
      isSystemRole: true,
      isActive: true,
      permissions: {
        Permission.orgRead,
        Permission.orgWrite,
        Permission.orgSettings,
        Permission.userRead,
        Permission.userWrite,
        Permission.userInvite,
        Permission.userRoles,
        Permission.teamRead,
        Permission.teamWrite,
        Permission.teamCreate,
        Permission.teamMembers,
        Permission.questRead,
        Permission.questWrite,
        Permission.questCreate,
        Permission.questAssign,
        Permission.analyticsRead,
        Permission.reportsRead,
        Permission.reportsCreate,
        Permission.gamificationRead,
        Permission.gamificationWrite,
        Permission.rewardsManage,
        Permission.achievementsManage,
        Permission.apiRead,
        Permission.apiWrite,
      },
      priority: 900,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static OrganizationRole createManagerRole(String organizationId) {
    return OrganizationRole(
      id: 'manager-$organizationId',
      organizationId: organizationId,
      name: 'Manager',
      description: 'Team and quest management access',
      color: '#0066FF',
      isSystemRole: true,
      isActive: true,
      permissions: {
        Permission.orgRead,
        Permission.userRead,
        Permission.userInvite,
        Permission.teamRead,
        Permission.teamWrite,
        Permission.teamCreate,
        Permission.teamMembers,
        Permission.questRead,
        Permission.questWrite,
        Permission.questCreate,
        Permission.questAssign,
        Permission.analyticsRead,
        Permission.reportsRead,
        Permission.gamificationRead,
        Permission.rewardsManage,
        Permission.apiRead,
      },
      priority: 800,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static OrganizationRole createMemberRole(String organizationId) {
    return OrganizationRole(
      id: 'member-$organizationId',
      organizationId: organizationId,
      name: 'Member',
      description: 'Standard user access',
      color: '#00AA00',
      isSystemRole: true,
      isActive: true,
      permissions: {
        Permission.orgRead,
        Permission.userRead,
        Permission.teamRead,
        Permission.questRead,
        Permission.questWrite,
        Permission.gamificationRead,
        Permission.apiRead,
      },
      priority: 700,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static OrganizationRole createViewerRole(String organizationId) {
    return OrganizationRole(
      id: 'viewer-$organizationId',
      organizationId: organizationId,
      name: 'Viewer',
      description: 'Read-only access',
      color: '#888888',
      isSystemRole: true,
      isActive: true,
      permissions: {
        Permission.orgRead,
        Permission.userRead,
        Permission.teamRead,
        Permission.questRead,
        Permission.gamificationRead,
      },
      priority: 600,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
