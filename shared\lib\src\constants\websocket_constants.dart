/// WebSocket constants for real-time communication
library;

/// Constants for WebSocket connections and real-time events
class WebSocketConstants {
  // Connection settings
  static const String defaultWebSocketPath = '/ws';
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const int maxReconnectAttempts = 5;
  static const Duration heartbeatInterval = Duration(seconds: 30);
  static const Duration responseTimeout = Duration(seconds: 10);
  
  // Connection states
  static const String connecting = 'connecting';
  static const String connected = 'connected';
  static const String disconnected = 'disconnected';
  static const String reconnecting = 'reconnecting';
  static const String failed = 'failed';
  
  // Core WebSocket events
  static const String connect = 'connect';
  static const String disconnect = 'disconnect';
  static const String error = 'error';
  static const String heartbeat = 'heartbeat';
  static const String authenticate = 'authenticate';
  static const String authenticated = 'authenticated';
  static const String authenticationFailed = 'authentication_failed';
  
  // User presence events
  static const String userOnline = 'user_online';
  static const String userOffline = 'user_offline';
  static const String userPresenceUpdate = 'user_presence_update';
  static const String userStatusChange = 'user_status_change';
  
  // Quest and task events
  static const String questCreated = 'quest_created';
  static const String questUpdated = 'quest_updated';
  static const String questDeleted = 'quest_deleted';
  static const String questCompleted = 'quest_completed';
  static const String questStarted = 'quest_started';
  static const String questPaused = 'quest_paused';
  static const String questResumed = 'quest_resumed';
  static const String questArchived = 'quest_archived';
  
  static const String taskCreated = 'task_created';
  static const String taskUpdated = 'task_updated';
  static const String taskDeleted = 'task_deleted';
  static const String taskCompleted = 'task_completed';
  static const String taskAssigned = 'task_assigned';
  static const String taskUnassigned = 'task_unassigned';
  static const String taskStatusChanged = 'task_status_changed';
  
  // Gamification events
  static const String pointsEarned = 'points_earned';
  static const String achievementUnlocked = 'achievement_unlocked';
  static const String levelUp = 'level_up';
  static const String streakUpdated = 'streak_updated';
  static const String leaderboardUpdated = 'leaderboard_updated';
  static const String badgeEarned = 'badge_earned';
  static const String rewardClaimed = 'reward_claimed';
  
  // Messaging events
  static const String messageSent = 'message_sent';
  static const String messageReceived = 'message_received';
  static const String messageUpdated = 'message_updated';
  static const String messageDeleted = 'message_deleted';
  static const String messageRead = 'message_read';
  static const String typingStart = 'typing_start';
  static const String typingStop = 'typing_stop';
  static const String chatCreated = 'chat_created';
  static const String chatUpdated = 'chat_updated';
  static const String userJoinedChat = 'user_joined_chat';
  static const String userLeftChat = 'user_left_chat';
  
  // Notification events
  static const String notificationReceived = 'notification_received';
  static const String notificationRead = 'notification_read';
  static const String notificationDeleted = 'notification_deleted';
  static const String notificationSettingsUpdated = 'notification_settings_updated';
  
  // Collaboration events
  static const String collaborationInvited = 'collaboration_invited';
  static const String collaborationJoined = 'collaboration_joined';
  static const String collaborationLeft = 'collaboration_left';
  static const String collaborationUpdated = 'collaboration_updated';
  static const String collaborationSessionStarted = 'collaboration_session_started';
  static const String collaborationSessionEnded = 'collaboration_session_ended';
  
  // System events
  static const String systemMaintenance = 'system_maintenance';
  static const String systemUpdate = 'system_update';
  static const String systemAlert = 'system_alert';
  static const String serverRestart = 'server_restart';
  
  // Analytics events
  static const String analyticsEvent = 'analytics_event';
  static const String userActivityUpdate = 'user_activity_update';
  static const String performanceMetric = 'performance_metric';
  
  // Event priorities
  static const Map<String, int> eventPriorities = {
    // High priority - immediate processing
    'error': 5,
    'authentication_failed': 5,
    'system_alert': 5,
    'server_restart': 5,
    
    // Medium-high priority - user interactions
    'message_received': 4,
    'notification_received': 4,
    'achievement_unlocked': 4,
    'points_earned': 4,
    'collaboration_invited': 4,
    
    // Medium priority - updates
    'quest_updated': 3,
    'task_updated': 3,
    'user_presence_update': 3,
    'leaderboard_updated': 3,
    
    // Low priority - background events
    'analytics_event': 2,
    'user_activity_update': 2,
    'heartbeat': 1,
  };
  
  // Message types for WebSocket communication
  static const String requestType = 'request';
  static const String responseType = 'response';
  static const String eventType = 'event';
  static const String errorType = 'error';
  
  // Error codes for WebSocket
  static const Map<String, int> errorCodes = {
    'invalid_message_format': 4000,
    'authentication_required': 4001,
    'authentication_failed': 4002,
    'permission_denied': 4003,
    'rate_limit_exceeded': 4004,
    'invalid_event_type': 4005,
    'server_error': 4006,
    'maintenance_mode': 4007,
    'connection_limit_exceeded': 4008,
    'invalid_token': 4009,
    'token_expired': 4010,
  };
  
  // Rate limiting
  static const int maxMessagesPerMinute = 60;
  static const int maxEventsPerSecond = 10;
  static const Duration rateLimitWindow = Duration(minutes: 1);
  
  // Message size limits
  static const int maxMessageSize = 64 * 1024; // 64KB
  static const int maxBatchSize = 100;
  
  // Subscription management
  static const String subscribe = 'subscribe';
  static const String unsubscribe = 'unsubscribe';
  static const String subscribed = 'subscribed';
  static const String unsubscribed = 'unsubscribed';
  static const String subscriptionError = 'subscription_error';
  
  // Channel types for subscriptions
  static const String userChannel = 'user';
  static const String questChannel = 'quest';
  static const String teamChannel = 'team';
  static const String globalChannel = 'global';
  static const String chatChannel = 'chat';
  static const String notificationChannel = 'notification';
  
  // Utility methods
  
  /// Get event priority
  static int getEventPriority(String eventType) {
    return eventPriorities[eventType] ?? 1;
  }
  
  /// Check if event is high priority
  static bool isHighPriorityEvent(String eventType) {
    return getEventPriority(eventType) >= 4;
  }
  
  /// Generate channel name
  static String generateChannelName(String type, String id) {
    return '$type:$id';
  }
  
  /// Parse channel name
  static Map<String, String> parseChannelName(String channelName) {
    final parts = channelName.split(':');
    if (parts.length != 2) {
      return {'type': 'unknown', 'id': ''};
    }
    return {'type': parts[0], 'id': parts[1]};
  }
  
  /// Check if reconnection should be attempted
  static bool shouldReconnect(int attemptCount, String disconnectReason) {
    if (attemptCount >= maxReconnectAttempts) return false;
    
    // Don't reconnect for authentication failures
    if (disconnectReason == 'authentication_failed') return false;
    if (disconnectReason == 'invalid_token') return false;
    if (disconnectReason == 'permission_denied') return false;
    
    return true;
  }
  
  /// Calculate reconnect delay with exponential backoff
  static Duration calculateReconnectDelay(int attemptCount) {
    final baseDelay = reconnectDelay.inMilliseconds;
    final delay = baseDelay * (1 << (attemptCount - 1)); // Exponential backoff
    final maxDelay = 30000; // Max 30 seconds
    
    return Duration(milliseconds: delay > maxDelay ? maxDelay : delay);
  }
  
  /// Validate message size
  static bool isValidMessageSize(String message) {
    return message.length <= maxMessageSize;
  }
  
  /// Get error message for code
  static String getErrorMessage(int code) {
    switch (code) {
      case 4000: return 'Invalid message format';
      case 4001: return 'Authentication required';
      case 4002: return 'Authentication failed';
      case 4003: return 'Permission denied';
      case 4004: return 'Rate limit exceeded';
      case 4005: return 'Invalid event type';
      case 4006: return 'Server error';
      case 4007: return 'Server is in maintenance mode';
      case 4008: return 'Connection limit exceeded';
      case 4009: return 'Invalid token';
      case 4010: return 'Token expired';
      default: return 'Unknown error';
    }
  }
}
