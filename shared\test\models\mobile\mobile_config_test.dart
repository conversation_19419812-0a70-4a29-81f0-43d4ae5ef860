import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('MobileConfig Tests', () {
    test('should create a mobile config with device capabilities', () {
      final deviceCapabilities = DeviceCapabilities(
        platform: Platform.android,
        formFactor: DeviceFormFactor.phone,
        screenWidth: 390.0,
        screenHeight: 844.0,
        devicePixelRatio: 3.0,
        hasCamera: true,
        hasGPS: true,
        supportsBiometrics: true,
        performanceTier: PerformanceTier.highEnd,
        ramMB: 8192, // 8GB
        availableStorageMB: 50000, // ~50GB available
      );

      final config = MobileConfig(
        deviceCapabilities: deviceCapabilities,
        appVersion: '1.2.0',
        configVersion: '2.0',
        lastUpdated: DateTime.now(),
        userId: 'user123',
        organizationId: 'org456',
        themeMode: ThemeMode.dark,
        locale: 'en-US',
      );

      expect(config.deviceCapabilities.platform, equals(Platform.android));
      expect(config.deviceCapabilities.hasCamera, isTrue);
      expect(config.appVersion, equals('1.2.0'));
      expect(config.userId, equals('user123'));
      expect(config.themeMode, equals(ThemeMode.dark));
    });

    test('should create mobile config with correct properties', () {
      final deviceCapabilities = DeviceCapabilities(
        platform: Platform.ios,
        formFactor: DeviceFormFactor.tablet,
        screenWidth: 820.0,
        screenHeight: 1180.0,
        devicePixelRatio: 2.0,
        performanceTier: PerformanceTier.flagship,
      );

      final timestamp = DateTime.now();
      final config = MobileConfig(
        deviceCapabilities: deviceCapabilities,
        appVersion: '2.0.0',
        configVersion: '3.0',
        lastUpdated: timestamp,
        locale: 'es-ES',
        mobileFeatureFlags: {
          'enable_offline_mode': true,
          'enable_push_notifications': false,
        },
      );

      expect(config.appVersion, equals('2.0.0'));
      expect(config.locale, equals('es-ES'));
      expect(config.deviceCapabilities.platform, equals(Platform.ios));
      expect(config.mobileFeatureFlags['enable_offline_mode'], isTrue);
      expect(config.mobileFeatureFlags['enable_push_notifications'], isFalse);
    });

    test('should check device capabilities correctly', () {
      final mobileCapabilities = DeviceCapabilities(
        platform: Platform.android,
        formFactor: DeviceFormFactor.phone,
        screenWidth: 360.0,
        screenHeight: 640.0,
        devicePixelRatio: 2.0,
        performanceTier: PerformanceTier.lowEnd,
      );

      expect(mobileCapabilities.isMobile, isTrue);
      expect(mobileCapabilities.isDesktop, isFalse);
      expect(mobileCapabilities.isLowEndDevice, isTrue);
      expect(mobileCapabilities.screenSizeCategory, equals('small'));

      final desktopCapabilities = DeviceCapabilities(
        platform: Platform.windows,
        formFactor: DeviceFormFactor.desktop,
        screenWidth: 1920.0,
        screenHeight: 1080.0,
        devicePixelRatio: 1.0,
        performanceTier: PerformanceTier.flagship,
      );

      expect(desktopCapabilities.isMobile, isFalse);
      expect(desktopCapabilities.isDesktop, isTrue);
      expect(desktopCapabilities.isLowEndDevice, isFalse);
      expect(desktopCapabilities.screenSizeCategory, equals('extra_large'));
    });
  });

  group('DeviceCapabilities Tests', () {
    test('should create device capabilities with all features enabled', () {
      final capabilities = DeviceCapabilities(
        platform: Platform.ios,
        formFactor: DeviceFormFactor.phone,
        screenWidth: 375.0,
        screenHeight: 812.0,
        devicePixelRatio: 3.0,
        supportsTouch: true,
        supportsHaptics: true,
        hasGPS: true,
        hasCamera: true,
        hasMicrophone: true,
        supportsBiometrics: true,
        supportsNotifications: true,
        supportsBackgroundSync: true,
        performanceTier: PerformanceTier.highEnd,
        ramMB: 6144, // 6GB
        cpuCores: 8,
      );

      expect(capabilities.platform, equals(Platform.ios));
      expect(capabilities.formFactor, equals(DeviceFormFactor.phone));
      expect(capabilities.supportsHaptics, isTrue);
      expect(capabilities.supportsBiometrics, isTrue);
      expect(capabilities.performanceTier, equals(PerformanceTier.highEnd));
      expect(capabilities.isMobile, isTrue);
    });

    test('should create device capabilities with correct properties', () {
      final capabilities = DeviceCapabilities(
        platform: Platform.android,
        formFactor: DeviceFormFactor.tablet,
        screenWidth: 800.0,
        screenHeight: 1280.0,
        devicePixelRatio: 2.0,
        hasCamera: false,
        hasGPS: false,
        supportsBiometrics: false,
        performanceTier: PerformanceTier.midRange,
        supportedWebApis: ['camera', 'geolocation'],
      );

      expect(capabilities.platform, equals(Platform.android));
      expect(capabilities.formFactor, equals(DeviceFormFactor.tablet));
      expect(capabilities.hasCamera, isFalse);
      expect(capabilities.performanceTier, equals(PerformanceTier.midRange));
      expect(capabilities.supportedWebApis, equals(['camera', 'geolocation']));
      expect(capabilities.isMobile, isTrue);
    });

    test('should categorize screen sizes correctly', () {
      final smallScreen = DeviceCapabilities(
        platform: Platform.android,
        formFactor: DeviceFormFactor.phone,
        screenWidth: 320.0,
        screenHeight: 568.0,
        devicePixelRatio: 2.0,
      );
      expect(smallScreen.screenSizeCategory, equals('small'));

      final mediumScreen = DeviceCapabilities(
        platform: Platform.ios,
        formFactor: DeviceFormFactor.tablet,
        screenWidth: 768.0,
        screenHeight: 1024.0,
        devicePixelRatio: 2.0,
      );
      expect(mediumScreen.screenSizeCategory, equals('medium'));

      final largeScreen = DeviceCapabilities(
        platform: Platform.windows,
        formFactor: DeviceFormFactor.desktop,
        screenWidth: 1366.0,
        screenHeight: 768.0,
        devicePixelRatio: 1.0,
      );
      expect(largeScreen.screenSizeCategory, equals('large'));

      final extraLargeScreen = DeviceCapabilities(
        platform: Platform.macos,
        formFactor: DeviceFormFactor.desktop,
        screenWidth: 2560.0,
        screenHeight: 1440.0,
        devicePixelRatio: 2.0,
      );
      expect(extraLargeScreen.screenSizeCategory, equals('extra_large'));
    });
  });
}
