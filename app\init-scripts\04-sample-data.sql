-- Sample data for Quester gamification system
-- This populates the database with achievements, rewards, and test users
-- Based on the mock data from server v2.1.0

-- Insert sample achievements (44 achievements across 8 categories)
INSERT INTO quester.achievements (id, name, description, category, rarity, points_reward, icon_url, requirements) VALUES
-- Quest Completion Category
('first_quest', 'First Steps', 'Complete your very first quest', 'quest_completion', 'common', 50, '/icons/achievements/first_quest.svg', '{"quest_count": 1}'),
('quest_novice', 'Quest Novice', 'Complete 5 quests successfully', 'quest_completion', 'common', 100, '/icons/achievements/quest_novice.svg', '{"quest_count": 5}'),
('quest_explorer', 'Quest Explorer', 'Complete 25 quests', 'quest_completion', 'uncommon', 200, '/icons/achievements/quest_explorer.svg', '{"quest_count": 25}'),
('quest_master', 'Quest Master', 'Complete 100 quests', 'quest_completion', 'rare', 500, '/icons/achievements/quest_master.svg', '{"quest_count": 100}'),
('quest_legend', 'Quest Legend', 'Complete 500 quests', 'quest_completion', 'epic', 1000, '/icons/achievements/quest_legend.svg', '{"quest_count": 500}'),

-- Collaboration Category
('team_player', 'Team Player', 'Participate in your first collaborative quest', 'collaboration', 'common', 75, '/icons/achievements/team_player.svg', '{"collaborative_quests": 1}'),
('mentor', 'Mentor', 'Help 10 other users with their quests', 'collaboration', 'uncommon', 250, '/icons/achievements/mentor.svg', '{"help_count": 10}'),
('collaboration_expert', 'Collaboration Expert', 'Successfully complete 50 collaborative quests', 'collaboration', 'rare', 600, '/icons/achievements/collaboration_expert.svg', '{"collaborative_quests": 50}'),
('community_leader', 'Community Leader', 'Lead 25 successful team quests', 'collaboration', 'epic', 800, '/icons/achievements/community_leader.svg', '{"led_quests": 25}'),

-- Streak Building Category
('streak_starter', 'Streak Starter', 'Maintain a 3-day activity streak', 'streak_building', 'common', 60, '/icons/achievements/streak_starter.svg', '{"streak_days": 3}'),
('consistent_achiever', 'Consistent Achiever', 'Maintain a 7-day activity streak', 'streak_building', 'uncommon', 150, '/icons/achievements/consistent_achiever.svg', '{"streak_days": 7}'),
('dedication_master', 'Dedication Master', 'Maintain a 30-day activity streak', 'streak_building', 'rare', 400, '/icons/achievements/dedication_master.svg', '{"streak_days": 30}'),
('unstoppable_force', 'Unstoppable Force', 'Maintain a 100-day activity streak', 'streak_building', 'epic', 1000, '/icons/achievements/unstoppable_force.svg', '{"streak_days": 100}'),
('eternal_dedication', 'Eternal Dedication', 'Maintain a 365-day activity streak', 'streak_building', 'legendary', 2000, '/icons/achievements/eternal_dedication.svg', '{"streak_days": 365}'),

-- Quality Work Category
('perfectionist', 'Perfectionist', 'Complete 10 quests with perfect scores', 'quality_work', 'uncommon', 200, '/icons/achievements/perfectionist.svg', '{"perfect_quests": 10}'),
('quality_assurance', 'Quality Assurance', 'Maintain high quality standards in 50 quests', 'quality_work', 'rare', 500, '/icons/achievements/quality_assurance.svg', '{"quality_quests": 50}'),
('excellence_embodied', 'Excellence Embodied', 'Achieve excellence rating in 100 quests', 'quality_work', 'epic', 800, '/icons/achievements/excellence_embodied.svg', '{"excellent_quests": 100}'),

-- Leadership Category
('natural_leader', 'Natural Leader', 'Successfully lead your first team quest', 'leadership', 'uncommon', 200, '/icons/achievements/natural_leader.svg', '{"led_quests": 1}'),
('inspiring_leader', 'Inspiring Leader', 'Lead 10 successful team quests', 'leadership', 'rare', 500, '/icons/achievements/inspiring_leader.svg', '{"led_quests": 10}'),
('visionary', 'Visionary', 'Create and lead 25 innovative quests', 'leadership', 'epic', 900, '/icons/achievements/visionary.svg', '{"created_led_quests": 25}'),

-- Innovation Category
('creative_mind', 'Creative Mind', 'Create your first original quest', 'innovation', 'common', 100, '/icons/achievements/creative_mind.svg', '{"created_quests": 1}'),
('innovator', 'Innovator', 'Create 10 unique and popular quests', 'innovation', 'uncommon', 300, '/icons/achievements/innovator.svg', '{"popular_quests": 10}'),
('game_changer', 'Game Changer', 'Create a quest that gets 100+ participants', 'innovation', 'rare', 600, '/icons/achievements/game_changer.svg', '{"quest_participants": 100}'),
('innovation_master', 'Innovation Master', 'Have 5 quests featured as community favorites', 'innovation', 'epic', 1000, '/icons/achievements/innovation_master.svg', '{"featured_quests": 5}'),

-- Consistency Category
('daily_achiever', 'Daily Achiever', 'Complete at least one task every day for a week', 'consistency', 'common', 80, '/icons/achievements/daily_achiever.svg', '{"daily_tasks": 7}'),
('monthly_champion', 'Monthly Champion', 'Complete at least 100 tasks in a single month', 'consistency', 'uncommon', 250, '/icons/achievements/monthly_champion.svg', '{"monthly_tasks": 100}'),
('year_round_performer', 'Year-Round Performer', 'Maintain consistent activity for 12 months', 'consistency', 'epic', 1200, '/icons/achievements/year_round_performer.svg', '{"active_months": 12}'),

-- Special Events Category
('early_adopter', 'Early Adopter', 'Join the platform in its first month', 'special_events', 'rare', 500, '/icons/achievements/early_adopter.svg', '{"join_date": "2025-08"}'),
('beta_tester', 'Beta Tester', 'Participate in beta testing features', 'special_events', 'uncommon', 300, '/icons/achievements/beta_tester.svg', '{"beta_participation": true}'),
('community_builder', 'Community Builder', 'Help grow the community by inviting 10 active users', 'special_events', 'rare', 600, '/icons/achievements/community_builder.svg', '{"invited_users": 10}'),
('anniversary_celebration', 'Anniversary Celebration', 'Participate in the platform anniversary event', 'special_events', 'epic', 800, '/icons/achievements/anniversary_celebration.svg', '{"anniversary_event": true}'),

-- Additional achievements for comprehensive coverage
('task_completion_100', 'Century Maker', 'Complete 100 individual tasks', 'quest_completion', 'uncommon', 200, '/icons/achievements/century_maker.svg', '{"task_count": 100}'),
('task_completion_1000', 'Task Master', 'Complete 1000 individual tasks', 'quest_completion', 'rare', 500, '/icons/achievements/task_master.svg', '{"task_count": 1000}'),
('collaboration_champion', 'Collaboration Champion', 'Earn recognition in 20 collaborative efforts', 'collaboration', 'rare', 550, '/icons/achievements/collaboration_champion.svg', '{"collaboration_points": 20}'),
('streak_recovery', 'Comeback Kid', 'Rebuild a streak after losing one of 30+ days', 'streak_building', 'uncommon', 200, '/icons/achievements/comeback_kid.svg', '{"recovered_streak": 30}'),
('efficiency_expert', 'Efficiency Expert', 'Complete tasks 50% faster than average', 'quality_work', 'rare', 450, '/icons/achievements/efficiency_expert.svg', '{"efficiency_rating": 1.5}'),
('knowledge_sharer', 'Knowledge Sharer', 'Create 10 helpful guides or tutorials', 'innovation', 'uncommon', 300, '/icons/achievements/knowledge_sharer.svg', '{"guides_created": 10}'),
('milestone_achiever', 'Milestone Achiever', 'Reach 10,000 total points', 'consistency', 'rare', 400, '/icons/achievements/milestone_achiever.svg', '{"total_points": 10000}'),
('social_connector', 'Social Connector', 'Build meaningful connections with 50 users', 'collaboration', 'uncommon', 250, '/icons/achievements/social_connector.svg', '{"connections": 50}'),
('problem_solver', 'Problem Solver', 'Successfully resolve 25 challenging quests', 'quality_work', 'uncommon', 300, '/icons/achievements/problem_solver.svg', '{"difficult_quests": 25}'),
('platform_advocate', 'Platform Advocate', 'Provide valuable feedback that leads to improvements', 'special_events', 'rare', 500, '/icons/achievements/platform_advocate.svg', '{"feedback_implemented": true}'),
('speed_demon', 'Speed Demon', 'Complete 10 quests in a single day', 'consistency', 'uncommon', 200, '/icons/achievements/speed_demon.svg', '{"daily_quests": 10}'),
('legendary_contributor', 'Legendary Contributor', 'Achieve legendary status across multiple categories', 'special_events', 'legendary', 1500, '/icons/achievements/legendary_contributor.svg', '{"legendary_achievements": 3}'),
('point_accumulator', 'Point Accumulator', 'Earn 50,000 total points', 'consistency', 'epic', 800, '/icons/achievements/point_accumulator.svg', '{"total_points": 50000}'),
('ultimate_achiever', 'Ultimate Achiever', 'Unlock 75% of all available achievements', 'special_events', 'legendary', 2000, '/icons/achievements/ultimate_achiever.svg', '{"achievement_percentage": 0.75}'),
('quest_variety', 'Quest Variety Master', 'Complete quests in 10 different categories', 'quest_completion', 'uncommon', 250, '/icons/achievements/quest_variety.svg', '{"quest_categories": 10}'),
('weekend_warrior', 'Weekend Warrior', 'Complete significant work during 20 weekends', 'consistency', 'uncommon', 200, '/icons/achievements/weekend_warrior.svg', '{"weekend_activity": 20}'),
('night_owl', 'Night Owl', 'Complete 50 tasks during late night hours', 'consistency', 'common', 150, '/icons/achievements/night_owl.svg', '{"night_tasks": 50}'),
('early_bird', 'Early Bird', 'Complete 50 tasks during early morning hours', 'consistency', 'common', 150, '/icons/achievements/early_bird.svg', '{"morning_tasks": 50}')

ON CONFLICT (id) DO NOTHING;

-- Insert sample rewards (11 rewards across different types)
INSERT INTO quester.rewards (id, name, description, type, point_cost, requirements, icon_url) VALUES
('custom_avatar', 'Custom Avatar', 'Upload and use a custom avatar image', 'cosmetic', 500, '{"role": "explorer"}', '/icons/rewards/custom_avatar.svg'),
('golden_frame', 'Golden Avatar Frame', 'Elegant golden border for your avatar', 'avatar_frame', 1000, '{"achievements": 5}', '/icons/rewards/golden_frame.svg'),
('priority_support', 'Priority Support', 'Get priority access to customer support', 'privilege', 2000, '{"role": "adventurer"}', '/icons/rewards/priority_support.svg'),
('dark_theme', 'Dark Theme', 'Access to the premium dark theme', 'theme', 800, '{"role": "explorer"}', '/icons/rewards/dark_theme.svg'),
('quest_master_title', 'Quest Master Title', 'Display "Quest Master" title on your profile', 'title', 1500, '{"quest_count": 100}', '/icons/rewards/quest_master_title.svg'),
('collaboration_badge', 'Collaboration Badge', 'Special badge showing collaboration expertise', 'badge', 1200, '{"collaborative_quests": 25}', '/icons/rewards/collaboration_badge.svg'),
('advanced_analytics', 'Advanced Analytics', 'Access to detailed performance analytics', 'privilege', 3000, '{"role": "hero"}', '/icons/rewards/advanced_analytics.svg'),
('template_creation', 'Template Creation', 'Create and share quest templates', 'privilege', 2500, '{"role": "hero", "created_quests": 10}', '/icons/rewards/template_creation.svg'),
('legendary_frame', 'Legendary Avatar Frame', 'Exclusive frame for legendary achievers', 'avatar_frame', 5000, '{"role": "legend"}', '/icons/rewards/legendary_frame.svg'),
('custom_themes', 'Custom Theme Creator', 'Create and customize your own themes', 'privilege', 4000, '{"role": "legend", "achievements": 20}', '/icons/rewards/custom_themes.svg'),
('platform_influence', 'Platform Influence', 'Vote on platform features and improvements', 'privilege', 10000, '{"role": "mythic"}', '/icons/rewards/platform_influence.svg')

ON CONFLICT (id) DO NOTHING;

-- Insert sample users for testing (using proper UUIDs)
INSERT INTO quester.users (id, username, email, display_name, avatar_url) VALUES
(uuid_generate_v4(), 'alice_cooper', '<EMAIL>', 'Alice Cooper', '/avatars/alice.jpg'),
(uuid_generate_v4(), 'bob_builder', '<EMAIL>', 'Bob Builder', '/avatars/bob.jpg'),
(uuid_generate_v4(), 'charlie_coder', '<EMAIL>', 'Charlie Coder', '/avatars/charlie.jpg'),
(uuid_generate_v4(), 'diana_designer', '<EMAIL>', 'Diana Designer', '/avatars/diana.jpg'),
(uuid_generate_v4(), 'eve_explorer', '<EMAIL>', 'Eve Explorer', '/avatars/eve.jpg')

ON CONFLICT (username) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    avatar_url = EXCLUDED.avatar_url;

-- Initialize user points for sample users
INSERT INTO quester.user_points (user_id, total_points, current_level, role, points_to_next_level) VALUES
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 2500, 3, 'adventurer', 1500),
((SELECT id FROM quester.users WHERE username = 'bob_builder'), 750, 2, 'explorer', 750),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 8500, 5, 'hero', 1500),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 15000, 7, 'legend', 10000),
((SELECT id FROM quester.users WHERE username = 'eve_explorer'), 500, 1, 'explorer', 0)

ON CONFLICT (user_id) DO NOTHING;

-- Initialize streaks for sample users
INSERT INTO quester.streaks (user_id, current_streak, longest_streak, last_activity_date) VALUES
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 7, 15, CURRENT_TIMESTAMP - INTERVAL '1 hour'),
((SELECT id FROM quester.users WHERE username = 'bob_builder'), 3, 10, CURRENT_TIMESTAMP - INTERVAL '2 hours'),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 25, 45, CURRENT_TIMESTAMP - INTERVAL '30 minutes'),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 60, 60, CURRENT_TIMESTAMP - INTERVAL '15 minutes'),
((SELECT id FROM quester.users WHERE username = 'eve_explorer'), 1, 5, CURRENT_TIMESTAMP - INTERVAL '3 hours')

ON CONFLICT (user_id) DO NOTHING;

-- Sample user achievements
INSERT INTO quester.user_achievements (user_id, achievement_id, points_awarded) VALUES
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'first_quest', 50),
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'quest_novice', 100),
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'team_player', 75),
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'streak_starter', 60),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'first_quest', 50),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'quest_novice', 100),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'quest_explorer', 200),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'quest_master', 500),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'mentor', 250),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'perfectionist', 200),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'first_quest', 50),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'quest_novice', 100),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'quest_explorer', 200),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'quest_master', 500),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'quest_legend', 1000),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'collaboration_expert', 600),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'dedication_master', 400),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'natural_leader', 200)

ON CONFLICT (user_id, achievement_id) DO NOTHING;

-- Sample leaderboard data
INSERT INTO quester.leaderboards (user_id, leaderboard_type, score, rank) VALUES
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'global_points', 15000, 1),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'global_points', 8500, 2),
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'global_points', 2500, 3),
((SELECT id FROM quester.users WHERE username = 'bob_builder'), 'global_points', 750, 4),
((SELECT id FROM quester.users WHERE username = 'eve_explorer'), 'global_points', 500, 5),

((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'quest_completion', 85, 1),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'quest_completion', 62, 2),
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'quest_completion', 28, 3),

((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'streak_days', 60, 1),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'streak_days', 25, 2),
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'streak_days', 7, 3)

ON CONFLICT (user_id, leaderboard_type) DO NOTHING;

-- Sample activity log entries
INSERT INTO quester.activity_log (user_id, activity_type, points_earned, description) VALUES
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'quest_completion', 50, 'Completed "Learn Flutter Basics" quest'),
((SELECT id FROM quester.users WHERE username = 'alice_cooper'), 'achievement_unlock', 50, 'Unlocked "First Quest" achievement'),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'task_completion', 25, 'Completed advanced coding challenge'),
((SELECT id FROM quester.users WHERE username = 'charlie_coder'), 'collaboration', 30, 'Helped team member with debugging'),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'quest_creation', 100, 'Created "UI/UX Design Fundamentals" quest'),
((SELECT id FROM quester.users WHERE username = 'diana_designer'), 'collaboration', 75, 'Successfully led team design sprint');

-- Log completion
DO $$
DECLARE
    achievement_count INTEGER;
    reward_count INTEGER;
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO achievement_count FROM quester.achievements;
    SELECT COUNT(*) INTO reward_count FROM quester.rewards;
    SELECT COUNT(*) INTO user_count FROM quester.users;
    
    RAISE NOTICE 'Sample data loaded successfully!';
    RAISE NOTICE 'Achievements: %', achievement_count;
    RAISE NOTICE 'Rewards: %', reward_count;
    RAISE NOTICE 'Sample Users: %', user_count;
    RAISE NOTICE 'Database ready for gamification API integration!';
END $$;
