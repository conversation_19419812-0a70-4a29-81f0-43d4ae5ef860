import 'package:equatable/equatable.dart';
import 'mfa_models.dart'; // Import MFAMethod from mfa_models.dart
import 'trusted_device_models.dart'; // Import TrustedDevice from trusted_device_models.dart

/// User MFA configuration and settings
class UserMFASettings extends Equatable {
  final String userId;
  final bool isEnabled;
  final MFAMethod? primaryMethod;
  final List<MFAMethod> enabledMethods;
  final List<TrustedDevice> trustedDevices;
  final bool hasTOTP;
  final bool hasSMS;
  final bool hasRecoveryEmail;
  final bool hasBackupCodes;
  final int backupCodesRemaining;
  final String? phoneNumber;
  final String? recoveryEmail;
  final DateTime? lastUpdated;
  final DateTime? lastUsed;

  const UserMFASettings({
    required this.userId,
    required this.isEnabled,
    this.primaryMethod,
    required this.enabledMethods,
    required this.trustedDevices,
    required this.hasTOTP,
    required this.hasSMS,
    required this.hasRecoveryEmail,
    required this.hasBackupCodes,
    required this.backupCodesRemaining,
    this.phoneNumber,
    this.recoveryEmail,
    this.lastUpdated,
    this.lastUsed,
  });

  /// Get active trusted devices
  List<TrustedDevice> get activeTrustedDevices =>
      trustedDevices.where((device) => device.isActive).toList();

  /// Get available MFA methods
  List<MFAMethod> get availableMethods {
    final methods = <MFAMethod>[];
    if (hasTOTP) methods.add(MFAMethod.totp);
    if (hasSMS) methods.add(MFAMethod.sms);
    if (hasRecoveryEmail) methods.add(MFAMethod.email);
    if (hasBackupCodes) methods.add(MFAMethod.backupCode);
    return methods;
  }

  /// Check if MFA setup is complete
  bool get isSetupComplete => isEnabled && primaryMethod != null;

  @override
  List<Object?> get props => [
        userId,
        isEnabled,
        primaryMethod,
        enabledMethods,
        trustedDevices,
        hasTOTP,
        hasSMS,
        hasRecoveryEmail,
        hasBackupCodes,
        backupCodesRemaining,
        phoneNumber,
        recoveryEmail,
        lastUpdated,
        lastUsed,
      ];
}
