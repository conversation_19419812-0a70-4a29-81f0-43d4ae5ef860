import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Advanced cache manager for optimizing data access and storage
class CacheManager {
  static final CacheManager _instance = CacheManager._internal();
  factory CacheManager() => _instance;
  CacheManager._internal();

  SharedPreferences? _prefs;
  final Map<String, CacheEntry> _memoryCache = {};
  final Map<String, Timer> _expirationTimers = {};
  
  static const int _defaultTTL = 300; // 5 minutes
  static const int _maxMemoryCacheSize = 100;
  static const String _cachePrefix = 'cache_';

  /// Initialize the cache manager
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _cleanupExpiredEntries();
  }

  /// Store data in cache with optional TTL
  Future<void> set<T>(
    String key,
    T data, {
    int? ttlSeconds,
    CacheLevel level = CacheLevel.both,
  }) async {
    final ttl = ttlSeconds ?? _defaultTTL;
    final expiresAt = DateTime.now().add(Duration(seconds: ttl));
    
    final entry = CacheEntry<T>(
      key: key,
      data: data,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      accessCount: 0,
      lastAccessed: DateTime.now(),
    );

    // Store in memory cache
    if (level == CacheLevel.memory || level == CacheLevel.both) {
      _setMemoryCache(key, entry);
    }

    // Store in persistent cache
    if (level == CacheLevel.persistent || level == CacheLevel.both) {
      await _setPersistentCache(key, entry);
    }

    // Set expiration timer
    _setExpirationTimer(key, ttl);
  }

  /// Get data from cache
  Future<T?> get<T>(String key) async {
    // Try memory cache first
    final memoryEntry = _memoryCache[key];
    if (memoryEntry != null && !memoryEntry.isExpired) {
      memoryEntry.accessCount++;
      memoryEntry.lastAccessed = DateTime.now();
      return memoryEntry.data as T?;
    }

    // Try persistent cache
    final persistentEntry = await _getPersistentCache<T>(key);
    if (persistentEntry != null && !persistentEntry.isExpired) {
      // Promote to memory cache
      _setMemoryCache(key, persistentEntry);
      persistentEntry.accessCount++;
      persistentEntry.lastAccessed = DateTime.now();
      return persistentEntry.data;
    }

    return null;
  }

  /// Get data with fallback function
  Future<T> getOrSet<T>(
    String key,
    Future<T> Function() fallback, {
    int? ttlSeconds,
    CacheLevel level = CacheLevel.both,
  }) async {
    final cached = await get<T>(key);
    if (cached != null) {
      return cached;
    }

    final data = await fallback();
    await set(key, data, ttlSeconds: ttlSeconds, level: level);
    return data;
  }

  /// Check if key exists in cache
  Future<bool> has(String key) async {
    // Check memory cache
    final memoryEntry = _memoryCache[key];
    if (memoryEntry != null && !memoryEntry.isExpired) {
      return true;
    }

    // Check persistent cache
    final persistentEntry = await _getPersistentCache(key);
    return persistentEntry != null && !persistentEntry.isExpired;
  }

  /// Remove item from cache
  Future<void> remove(String key) async {
    _memoryCache.remove(key);
    _expirationTimers[key]?.cancel();
    _expirationTimers.remove(key);
    
    await _prefs?.remove('$_cachePrefix$key');
  }

  /// Clear all cache
  Future<void> clear() async {
    _memoryCache.clear();
    
    for (final timer in _expirationTimers.values) {
      timer.cancel();
    }
    _expirationTimers.clear();

    final keys = _prefs?.getKeys().where((key) => key.startsWith(_cachePrefix));
    if (keys != null) {
      for (final key in keys) {
        await _prefs?.remove(key);
      }
    }
  }

  /// Get cache statistics
  CacheStatistics getStatistics() {
    int totalEntries = _memoryCache.length;
    int expiredEntries = 0;
    int totalAccessCount = 0;
    DateTime? oldestEntry;
    DateTime? newestEntry;

    for (final entry in _memoryCache.values) {
      if (entry.isExpired) expiredEntries++;
      totalAccessCount += entry.accessCount;
      
      if (oldestEntry == null || entry.createdAt.isBefore(oldestEntry)) {
        oldestEntry = entry.createdAt;
      }
      
      if (newestEntry == null || entry.createdAt.isAfter(newestEntry)) {
        newestEntry = entry.createdAt;
      }
    }

    return CacheStatistics(
      totalEntries: totalEntries,
      expiredEntries: expiredEntries,
      hitRate: totalAccessCount > 0 ? totalAccessCount / totalEntries : 0.0,
      memoryUsage: _estimateMemoryUsage(),
      oldestEntry: oldestEntry,
      newestEntry: newestEntry,
    );
  }

  /// Cleanup expired entries
  Future<void> cleanup() async {
    await _cleanupExpiredEntries();
  }

  /// Preload data into cache
  Future<void> preload<T>(Map<String, Future<T> Function()> loaders) async {
    final futures = loaders.entries.map((entry) async {
      try {
        final data = await entry.value();
        await set(entry.key, data);
      } catch (e) {
        if (kDebugMode) {
          print('Failed to preload cache key ${entry.key}: $e');
        }
      }
    });

    await Future.wait(futures);
  }

  /// Set memory cache entry
  void _setMemoryCache(String key, CacheEntry entry) {
    // Remove oldest entries if cache is full
    if (_memoryCache.length >= _maxMemoryCacheSize) {
      _evictLeastRecentlyUsed();
    }

    _memoryCache[key] = entry;
  }

  /// Set persistent cache entry
  Future<void> _setPersistentCache(String key, CacheEntry entry) async {
    try {
      final json = jsonEncode({
        'data': entry.data,
        'createdAt': entry.createdAt.toIso8601String(),
        'expiresAt': entry.expiresAt.toIso8601String(),
        'accessCount': entry.accessCount,
        'lastAccessed': entry.lastAccessed.toIso8601String(),
      });
      
      await _prefs?.setString('$_cachePrefix$key', json);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to set persistent cache for key $key: $e');
      }
    }
  }

  /// Get persistent cache entry
  Future<CacheEntry<T>?> _getPersistentCache<T>(String key) async {
    try {
      final json = _prefs?.getString('$_cachePrefix$key');
      if (json == null) return null;

      final data = jsonDecode(json);
      return CacheEntry<T>(
        key: key,
        data: data['data'] as T,
        createdAt: DateTime.parse(data['createdAt']),
        expiresAt: DateTime.parse(data['expiresAt']),
        accessCount: data['accessCount'] ?? 0,
        lastAccessed: DateTime.parse(data['lastAccessed']),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get persistent cache for key $key: $e');
      }
      return null;
    }
  }

  /// Set expiration timer
  void _setExpirationTimer(String key, int ttlSeconds) {
    _expirationTimers[key]?.cancel();
    
    _expirationTimers[key] = Timer(Duration(seconds: ttlSeconds), () {
      remove(key);
    });
  }

  /// Evict least recently used entries
  void _evictLeastRecentlyUsed() {
    if (_memoryCache.isEmpty) return;

    String? lruKey;
    DateTime? oldestAccess;

    for (final entry in _memoryCache.entries) {
      if (oldestAccess == null || entry.value.lastAccessed.isBefore(oldestAccess)) {
        oldestAccess = entry.value.lastAccessed;
        lruKey = entry.key;
      }
    }

    if (lruKey != null) {
      _memoryCache.remove(lruKey);
      _expirationTimers[lruKey]?.cancel();
      _expirationTimers.remove(lruKey);
    }
  }

  /// Cleanup expired entries
  Future<void> _cleanupExpiredEntries() async {
    // Cleanup memory cache
    final expiredKeys = <String>[];
    for (final entry in _memoryCache.entries) {
      if (entry.value.isExpired) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _expirationTimers[key]?.cancel();
      _expirationTimers.remove(key);
    }

    // Cleanup persistent cache
    final persistentKeys = _prefs?.getKeys().where((key) => key.startsWith(_cachePrefix));
    if (persistentKeys != null) {
      for (final key in persistentKeys) {
        final cacheKey = key.substring(_cachePrefix.length);
        final entry = await _getPersistentCache(cacheKey);
        if (entry?.isExpired == true) {
          await _prefs?.remove(key);
        }
      }
    }
  }

  /// Estimate memory usage
  int _estimateMemoryUsage() {
    // Rough estimation - would need more sophisticated measurement
    return _memoryCache.length * 1024; // Assume 1KB per entry
  }
}

/// Cache entry wrapper
class CacheEntry<T> {
  final String key;
  final T data;
  final DateTime createdAt;
  final DateTime expiresAt;
  int accessCount;
  DateTime lastAccessed;

  CacheEntry({
    required this.key,
    required this.data,
    required this.createdAt,
    required this.expiresAt,
    required this.accessCount,
    required this.lastAccessed,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  
  Duration get age => DateTime.now().difference(createdAt);
  
  Duration get timeToLive => expiresAt.difference(DateTime.now());
}

/// Cache level options
enum CacheLevel {
  memory,      // Only in-memory cache
  persistent,  // Only persistent cache
  both,        // Both memory and persistent
}

/// Cache statistics
class CacheStatistics {
  final int totalEntries;
  final int expiredEntries;
  final double hitRate;
  final int memoryUsage;
  final DateTime? oldestEntry;
  final DateTime? newestEntry;

  const CacheStatistics({
    required this.totalEntries,
    required this.expiredEntries,
    required this.hitRate,
    required this.memoryUsage,
    this.oldestEntry,
    this.newestEntry,
  });

  Map<String, dynamic> toJson() => {
    'totalEntries': totalEntries,
    'expiredEntries': expiredEntries,
    'hitRate': hitRate,
    'memoryUsage': memoryUsage,
    'oldestEntry': oldestEntry?.toIso8601String(),
    'newestEntry': newestEntry?.toIso8601String(),
  };
}
