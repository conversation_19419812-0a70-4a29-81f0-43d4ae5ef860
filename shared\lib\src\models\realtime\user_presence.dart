import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_presence.g.dart';

/// User presence status enumeration
enum PresenceStatus {
  @JsonValue('online')
  online,
  @JsonValue('away')
  away,
  @JsonValue('busy')
  busy,
  @JsonValue('offline')
  offline,
  @JsonValue('invisible')
  invisible,
}

/// User activity types for presence tracking
enum UserActivity {
  @JsonValue('idle')
  idle,
  @JsonValue('typing')
  typing,
  @JsonValue('editing_quest')
  editingQuest,
  @JsonValue('editing_task')
  editingTask,
  @JsonValue('viewing_dashboard')
  viewingDashboard,
  @JsonValue('in_meeting')
  inMeeting,
  @JsonValue('working_on_task')
  workingOnTask,
}

/// User presence model for real-time status tracking
@JsonSerializable()
class UserPresence extends Equatable {
  /// User ID
  final String userId;

  /// Current presence status
  final PresenceStatus status;

  /// Current activity
  final UserActivity? activity;

  /// Activity context (e.g., quest ID, task ID)
  final String? activityContext;

  /// Custom status message
  final String? statusMessage;

  /// Last seen timestamp
  final DateTime lastSeen;

  /// Last activity timestamp
  final DateTime? lastActivity;

  /// Device information
  final Map<String, dynamic>? deviceInfo;

  /// Location information (if available)
  final Map<String, dynamic>? location;

  /// Organization ID (for multi-tenant presence)
  final String? organizationId;

  /// Room/channel IDs user is currently in
  final List<String> activeRooms;

  /// Whether user allows presence sharing
  final bool isPresencePublic;

  const UserPresence({
    required this.userId,
    required this.status,
    this.activity,
    this.activityContext,
    this.statusMessage,
    required this.lastSeen,
    this.lastActivity,
    this.deviceInfo,
    this.location,
    this.organizationId,
    this.activeRooms = const [],
    this.isPresencePublic = true,
  });

  /// Create UserPresence from JSON
  factory UserPresence.fromJson(Map<String, dynamic> json) => _$UserPresenceFromJson(json);

  /// Convert UserPresence to JSON
  Map<String, dynamic> toJson() => _$UserPresenceToJson(this);

  /// Create an online presence
  factory UserPresence.online({
    required String userId,
    UserActivity? activity,
    String? activityContext,
    String? statusMessage,
    String? organizationId,
  }) {
    return UserPresence(
      userId: userId,
      status: PresenceStatus.online,
      activity: activity,
      activityContext: activityContext,
      statusMessage: statusMessage,
      lastSeen: DateTime.now(),
      lastActivity: DateTime.now(),
      organizationId: organizationId,
    );
  }

  /// Create an offline presence
  factory UserPresence.offline({
    required String userId,
    String? organizationId,
  }) {
    return UserPresence(
      userId: userId,
      status: PresenceStatus.offline,
      lastSeen: DateTime.now(),
      organizationId: organizationId,
    );
  }

  /// Check if user is currently online
  bool get isOnline => status == PresenceStatus.online;

  /// Check if user is available for collaboration
  bool get isAvailable => status == PresenceStatus.online && activity != UserActivity.inMeeting;

  /// Get human-readable status
  String get displayStatus {
    switch (status) {
      case PresenceStatus.online:
        if (activity != null) {
          switch (activity!) {
            case UserActivity.typing:
              return 'Typing...';
            case UserActivity.editingQuest:
              return 'Editing quest';
            case UserActivity.editingTask:
              return 'Editing task';
            case UserActivity.inMeeting:
              return 'In meeting';
            case UserActivity.workingOnTask:
              return 'Working on task';
            default:
              return 'Online';
          }
        }
        return statusMessage ?? 'Online';
      case PresenceStatus.away:
        return statusMessage ?? 'Away';
      case PresenceStatus.busy:
        return statusMessage ?? 'Busy';
      case PresenceStatus.offline:
        return 'Offline';
      case PresenceStatus.invisible:
        return 'Offline'; // Show as offline to others
    }
  }

  /// Get time since last activity
  Duration? get timeSinceLastActivity {
    if (lastActivity == null) return null;
    return DateTime.now().difference(lastActivity!);
  }

  /// Update presence with new activity
  UserPresence updateActivity({
    UserActivity? activity,
    String? activityContext,
    String? statusMessage,
  }) {
    return UserPresence(
      userId: userId,
      status: status,
      activity: activity ?? this.activity,
      activityContext: activityContext ?? this.activityContext,
      statusMessage: statusMessage ?? this.statusMessage,
      lastSeen: DateTime.now(),
      lastActivity: DateTime.now(),
      deviceInfo: deviceInfo,
      location: location,
      organizationId: organizationId,
      activeRooms: activeRooms,
      isPresencePublic: isPresencePublic,
    );
  }

  /// Update presence status
  UserPresence updateStatus({
    required PresenceStatus status,
    String? statusMessage,
  }) {
    return UserPresence(
      userId: userId,
      status: status,
      activity: status == PresenceStatus.offline ? null : activity,
      activityContext: status == PresenceStatus.offline ? null : activityContext,
      statusMessage: statusMessage ?? this.statusMessage,
      lastSeen: DateTime.now(),
      lastActivity: status == PresenceStatus.offline ? null : DateTime.now(),
      deviceInfo: deviceInfo,
      location: location,
      organizationId: organizationId,
      activeRooms: status == PresenceStatus.offline ? [] : activeRooms,
      isPresencePublic: isPresencePublic,
    );
  }

  /// Join a room/channel
  UserPresence joinRoom(String roomId) {
    if (activeRooms.contains(roomId)) return this;
    
    return UserPresence(
      userId: userId,
      status: status,
      activity: activity,
      activityContext: activityContext,
      statusMessage: statusMessage,
      lastSeen: DateTime.now(),
      lastActivity: DateTime.now(),
      deviceInfo: deviceInfo,
      location: location,
      organizationId: organizationId,
      activeRooms: [...activeRooms, roomId],
      isPresencePublic: isPresencePublic,
    );
  }

  /// Leave a room/channel
  UserPresence leaveRoom(String roomId) {
    return UserPresence(
      userId: userId,
      status: status,
      activity: activity,
      activityContext: activityContext,
      statusMessage: statusMessage,
      lastSeen: DateTime.now(),
      lastActivity: DateTime.now(),
      deviceInfo: deviceInfo,
      location: location,
      organizationId: organizationId,
      activeRooms: activeRooms.where((room) => room != roomId).toList(),
      isPresencePublic: isPresencePublic,
    );
  }

  @override
  List<Object?> get props => [
        userId,
        status,
        activity,
        activityContext,
        statusMessage,
        lastSeen,
        lastActivity,
        deviceInfo,
        location,
        organizationId,
        activeRooms,
        isPresencePublic,
      ];

  @override
  bool get stringify => true;
}
