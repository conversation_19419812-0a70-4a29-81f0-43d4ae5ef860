/// Tests for enhanced logging service
library;

import 'package:test/test.dart';
import 'package:server/services/logging_service.dart';

void main() {
  group('LoggingService', () {
    setUp(() {
      // Clear error statistics before each test
      LoggingService.clearErrorStats();
    });

    group('Basic Logging', () {
      test('should log messages at different levels', () {
        // These tests verify that logging methods don't throw exceptions
        expect(() => LoggingService.debug('Debug message'), returnsNormally);
        expect(() => LoggingService.info('Info message'), returnsNormally);
        expect(() => LoggingService.warning('Warning message'), returnsNormally);
        expect(() => LoggingService.error('Error message'), returnsNormally);
        expect(() => LoggingService.critical('Critical message'), returnsNormally);
      });

      test('should log with tags', () {
        expect(() => LoggingService.info('Tagged message', tag: 'TestTag'), returnsNormally);
        expect(() => LoggingService.error('Error with tag', tag: 'ErrorTag'), returnsNormally);
      });

      test('should log with errors and stack traces', () {
        final error = Exception('Test exception');
        final stackTrace = StackTrace.current;
        
        expect(() => LoggingService.error(
          'Error with exception',
          error: error,
          stackTrace: stackTrace,
        ), returnsNormally);
      });
    });

    group('Specialized Logging Methods', () {
      test('should log API events', () {
        expect(() => LoggingService.api(
          'API request received',
          endpoint: 'GET /api/users',
          statusCode: 200,
        ), returnsNormally);
      });

      test('should log security events', () {
        expect(() => LoggingService.security(
          'Failed login attempt',
          userId: 'user123',
          ipAddress: '***********',
        ), returnsNormally);
      });

      test('should log performance events', () {
        expect(() => LoggingService.performance(
          'Slow query detected',
          duration: const Duration(milliseconds: 500),
        ), returnsNormally);
      });

      test('should log database events', () {
        expect(() => LoggingService.database(
          'Query executed',
          query: 'SELECT * FROM users',
        ), returnsNormally);
      });

      test('should log cache events', () {
        expect(() => LoggingService.cache(
          'Cache hit',
          key: 'user:123',
        ), returnsNormally);
      });

      test('should log WebSocket events', () {
        expect(() => LoggingService.info(
          'Client connected',
          tag: 'WebSocket',
        ), returnsNormally);
      });

      test('should log gamification events', () {
        expect(() => LoggingService.info(
          'Points awarded',
          tag: 'Gamification',
        ), returnsNormally);
      });

      test('should log collaboration events', () {
        expect(() => LoggingService.info(
          'Quest shared',
          tag: 'Collaboration',
        ), returnsNormally);
      });

      test('should log analytics events', () {
        expect(() => LoggingService.analytics(
          'Event tracked',
          eventType: 'quest_completed',
        ), returnsNormally);
      });
    });

    group('Error Tracking', () {
      test('should track errors and increment counts', () {
        final error1 = Exception('Test error 1');
        final error2 = Exception('Test error 2');
        final error3 = Exception('Test error 1'); // Same as error1
        
        LoggingService.error('First error', error: error1);
        LoggingService.error('Second error', error: error2);
        LoggingService.error('Third error', error: error3);
        
        final stats = LoggingService.getErrorStats();
        expect(stats['totalErrors'], equals(3));
        expect(stats['uniqueErrors'], equals(2));
      });

      test('should track recent errors', () {
        final error = Exception('Recent error');
        
        LoggingService.error('Recent error message', error: error);
        
        final recentErrors = LoggingService.getRecentErrors(limit: 10);
        expect(recentErrors, isNotEmpty);
        expect(recentErrors.first['error'], contains('Recent error'));
        expect(recentErrors.first['type'], equals('_Exception'));
        expect(recentErrors.first['level'], equals('error'));
      });

      test('should limit recent errors to maximum count', () {
        // Add more than the maximum number of errors
        for (int i = 0; i < 150; i++) {
          LoggingService.error('Error $i', error: Exception('Error $i'));
        }
        
        final recentErrors = LoggingService.getRecentErrors();
        expect(recentErrors.length, lessThanOrEqualTo(100)); // Max recent errors
      });

      test('should provide error statistics', () {
        final error1 = Exception('Frequent error');
        final error2 = Exception('Rare error');
        
        // Add multiple instances of the same error
        for (int i = 0; i < 5; i++) {
          LoggingService.error('Frequent error $i', error: error1);
        }
        LoggingService.error('Rare error', error: error2);
        
        final stats = LoggingService.getErrorStats();
        expect(stats['totalErrors'], equals(6));
        expect(stats['uniqueErrors'], equals(2));
        expect(stats['mostFrequentErrors'], isA<List>());
        expect(stats['lastUpdated'], isNotNull);
        
        final mostFrequent = stats['mostFrequentErrors'] as List;
        expect(mostFrequent, isNotEmpty);
        expect(mostFrequent.first['count'], equals(5));
      });

      test('should filter recent errors by time period', () {
        final error = Exception('Time-filtered error');
        LoggingService.error('Error message', error: error);
        
        final stats = LoggingService.getErrorStats();
        expect(stats['recentErrors24h'], equals(1));
      });

      test('should group errors by type', () {
        LoggingService.error('Error 1', error: Exception('Exception error'));
        LoggingService.error('Error 2', error: ArgumentError('Argument error'));
        LoggingService.error('Error 3', error: Exception('Another exception'));
        
        final stats = LoggingService.getErrorStats();
        final errorsByType = stats['errorsByType'] as Map<String, int>;
        
        expect(errorsByType['_Exception'], equals(2));
        expect(errorsByType['ArgumentError'], equals(1));
      });

      test('should clear error statistics', () {
        final error = Exception('Test error');
        LoggingService.error('Error message', error: error);
        
        var stats = LoggingService.getErrorStats();
        expect(stats['totalErrors'], greaterThan(0));
        
        LoggingService.clearErrorStats();
        
        stats = LoggingService.getErrorStats();
        expect(stats['totalErrors'], equals(0));
        expect(stats['uniqueErrors'], equals(0));
        
        final recentErrors = LoggingService.getRecentErrors();
        expect(recentErrors, isEmpty);
      });
    });

    group('Structured Logging', () {
      test('should log structured data', () {
        final data = {
          'userId': 'user123',
          'action': 'login',
          'timestamp': DateTime.now().toIso8601String(),
        };
        
        expect(() => LoggingService.structured(
          LogLevel.info,
          'User action performed',
          tag: 'UserAction',
          data: data,
        ), returnsNormally);
      });

      test('should log structured data with errors', () {
        final error = Exception('Structured error');
        final data = {'context': 'test'};
        
        expect(() => LoggingService.structured(
          LogLevel.error,
          'Structured error occurred',
          tag: 'StructuredError',
          data: data,
          error: error,
        ), returnsNormally);
      });

      test('should handle null data in structured logging', () {
        expect(() => LoggingService.structured(
          LogLevel.info,
          'Message without data',
          tag: 'NoData',
        ), returnsNormally);
      });
    });

    group('Log Level Filtering', () {
      test('should respect log level threshold', () {
        final service = LoggingService();
        
        // Set log level to warning (should filter out debug and info)
        service.initialize(logLevel: LogLevel.warning);
        
        // These should not cause any issues even if filtered
        expect(() => LoggingService.debug('Debug message'), returnsNormally);
        expect(() => LoggingService.info('Info message'), returnsNormally);
        expect(() => LoggingService.warning('Warning message'), returnsNormally);
        expect(() => LoggingService.error('Error message'), returnsNormally);
      });
    });

    group('File Logging', () {
      test('should initialize with file logging enabled', () {
        final service = LoggingService();
        
        expect(() => service.initialize(
          logLevel: LogLevel.info,
          writeToFile: true,
          logFilePath: 'test_logs/test.log',
        ), returnsNormally);
      });

      test('should handle file logging errors gracefully', () {
        final service = LoggingService();
        
        // Try to write to an invalid path
        expect(() => service.initialize(
          logLevel: LogLevel.info,
          writeToFile: true,
          logFilePath: '/invalid/path/test.log',
        ), returnsNormally);
        
        // Logging should still work even if file writing fails
        expect(() => LoggingService.info('Test message'), returnsNormally);
      });
    });

    group('Error Statistics Edge Cases', () {
      test('should handle empty error statistics', () {
        LoggingService.clearErrorStats();
        
        final stats = LoggingService.getErrorStats();
        expect(stats['totalErrors'], equals(0));
        expect(stats['uniqueErrors'], equals(0));
        expect(stats['recentErrors24h'], equals(0));
        expect(stats['errorsByType'], isEmpty);
        expect(stats['mostFrequentErrors'], isEmpty);
      });

      test('should handle recent errors with limit', () {
        for (int i = 0; i < 10; i++) {
          LoggingService.error('Error $i', error: Exception('Error $i'));
        }
        
        final recentErrors = LoggingService.getRecentErrors(limit: 5);
        expect(recentErrors.length, equals(5));
        
        // Should return most recent errors first
        expect(recentErrors.first['error'], contains('Error 9'));
      });

      test('should handle most frequent errors with limit', () {
        // Create errors with different frequencies
        for (int i = 0; i < 3; i++) {
          LoggingService.error('Frequent', error: Exception('Frequent'));
        }
        for (int i = 0; i < 2; i++) {
          LoggingService.error('Medium', error: Exception('Medium'));
        }
        LoggingService.error('Rare', error: Exception('Rare'));
        
        final stats = LoggingService.getErrorStats();
        final mostFrequent = stats['mostFrequentErrors'] as List;
        
        expect(mostFrequent.length, equals(3));
        expect(mostFrequent[0]['count'], equals(3)); // Most frequent first
        expect(mostFrequent[1]['count'], equals(2));
        expect(mostFrequent[2]['count'], equals(1));
      });
    });
  });
}
