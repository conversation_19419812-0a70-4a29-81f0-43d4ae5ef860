import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../blocs/freelancing/freelancing_bloc.dart';

class FreelancingScreen extends StatefulWidget {
  const FreelancingScreen({super.key});

  @override
  State<FreelancingScreen> createState() => _FreelancingScreenState();
}

class _FreelancingScreenState extends State<FreelancingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load initial data
    _loadFreelancingData();
  }

  void _loadFreelancingData() {
    final bloc = context.read<FreelancingBloc>();
    bloc.add(const LoadProjects());
    bloc.add(const LoadFreelancerProfile());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Freelancing Marketplace'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.work), text: 'Projects'),
            Tab(icon: Icon(Icons.person), text: 'Profile'),
            Tab(icon: Icon(Icons.description), text: 'Proposals'),
            Tab(icon: Icon(Icons.handshake), text: 'Contracts'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFreelancingData,
          ),
        ],
      ),
      body: BlocConsumer<FreelancingBloc, FreelancingState>(
        listener: (context, state) {
          if (state is FreelancingError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is ProjectCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Project created successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            _loadFreelancingData(); // Reload data
          }
        },
        builder: (context, state) {
          if (state is FreelancingLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildProjectsTab(state),
              _buildProfileTab(state),
              _buildProposalsTab(state),
              _buildContractsTab(state),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateProjectDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildProjectsTab(FreelancingState state) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search and Filter Row
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search projects...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    // Implement search
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog(context),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Projects List
          Expanded(
            child: state is FreelancingLoaded && state.projects != null
                ? ListView.builder(
                    itemCount: state.projects!.length,
                    itemBuilder: (context, index) {
                      final project = state.projects![index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      project['title'] ?? 'Project ${index + 1}',
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  Chip(
                                    label: Text(project['status'] ?? 'Open'),
                                    backgroundColor: Colors.green.withValues(alpha: 0.2),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                project['description'] ?? 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 12),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(Icons.attach_money, size: 16),
                                      Text('\$${project['budget'] ?? (index + 1) * 500}'),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      const Icon(Icons.access_time, size: 16),
                                      Text('${project['duration'] ?? '2-4 weeks'}'),
                                    ],
                                  ),
                                  TextButton(
                                    onPressed: () => _viewProject(project),
                                    child: const Text('View Details'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  )
                : _buildEmptyState('No projects available', 'Check back later for new opportunities!'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileTab(FreelancingState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Freelancer Profile',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  if (state is FreelancingLoaded && state.freelancerProfile != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildProfileField('Name', state.freelancerProfile!['name'] ?? 'John Doe'),
                        _buildProfileField('Title', state.freelancerProfile!['title'] ?? 'Full Stack Developer'),
                        _buildProfileField('Hourly Rate', '\$${state.freelancerProfile!['hourly_rate'] ?? 50}/hr'),
                        _buildProfileField('Experience', '${state.freelancerProfile!['experience'] ?? 3} years'),
                        const SizedBox(height: 16),
                        const Text(
                          'Skills',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          children: (state.freelancerProfile!['skills'] as List<dynamic>? ?? ['Flutter', 'Dart', 'Firebase'])
                              .map((skill) => Chip(label: Text(skill)))
                              .toList(),
                        ),
                      ],
                    )
                  else
                    Column(
                      children: [
                        const Text('No profile found. Create your freelancer profile to get started.'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => _showCreateProfileDialog(context),
                          child: const Text('Create Profile'),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Portfolio Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Portfolio',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                      childAspectRatio: 1.5,
                    ),
                    itemCount: 4,
                    itemBuilder: (context, index) {
                      return Card(
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.image, size: 32, color: Colors.grey[400]),
                              const SizedBox(height: 4),
                              Text(
                                'Project ${index + 1}',
                                style: const TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProposalsTab(FreelancingState state) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'My Proposals',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: 5, // Mock proposals
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: ListTile(
                    title: Text('Proposal for Project ${index + 1}'),
                    subtitle: Text('Submitted ${index + 1} days ago'),
                    trailing: Chip(
                      label: Text(_getProposalStatus(index)),
                      backgroundColor: _getStatusColor(index),
                    ),
                    onTap: () {
                      // View proposal details
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContractsTab(FreelancingState state) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Active Contracts',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: 3, // Mock contracts
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Contract ${index + 1}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text('Client: Client ${index + 1}'),
                        Text('Value: \$${(index + 1) * 1000}'),
                        Text('Progress: ${(index + 1) * 25}%'),
                        const SizedBox(height: 12),
                        LinearProgressIndicator(
                          value: (index + 1) * 0.25,
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextButton(
                              onPressed: () {
                                // View contract details
                              },
                              child: const Text('View Details'),
                            ),
                            ElevatedButton(
                              onPressed: () {
                                // Submit work
                              },
                              child: const Text('Submit Work'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileField(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showCreateProjectDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Project'),
        content: const Text('Project creation form would be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Create mock project
              context.read<FreelancingBloc>().add(
                    CreateProject(projectData: {
                      'title': 'New Project',
                      'description': 'Project description',
                      'budget': 1000,
                    }),
                  );
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showCreateProfileDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Freelancer Profile'),
        content: const Text('Profile creation form would be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Profile creation coming soon!')),
              );
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Projects'),
        content: const Text('Filter options would be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _viewProject(Map<String, dynamic> project) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(project['title'] ?? 'Project Details'),
        content: Text(project['description'] ?? 'No description available'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Proposal submission coming soon!')),
              );
            },
            child: const Text('Submit Proposal'),
          ),
        ],
      ),
    );
  }

  String _getProposalStatus(int index) {
    final statuses = ['Pending', 'Accepted', 'Rejected', 'Under Review'];
    return statuses[index % statuses.length];
  }

  Color _getStatusColor(int index) {
    final colors = [Colors.orange, Colors.green, Colors.red, Colors.blue];
    return colors[index % colors.length].withValues(alpha: 0.2);
  }
}