import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'quest_progress.g.dart';

/// Quest progress milestone types
enum ProgressMilestone {
  @JsonValue('started')
  started,
  @JsonValue('quarter')
  quarter, // 25% complete
  @JsonValue('half')
  half, // 50% complete
  @JsonValue('three_quarters')
  threeQuarters, // 75% complete
  @JsonValue('near_complete')
  nearComplete, // 90% complete
  @JsonValue('completed')
  completed, // 100% complete
}

/// Progress tracking model for quests
@JsonSerializable()
class QuestProgress extends Equatable {
  /// Unique progress record identifier
  final String id;

  /// Associated quest ID
  final String questId;

  /// User ID this progress belongs to
  final String userId;

  /// Current progress percentage (0.0 - 1.0)
  final double progressPercentage;

  /// Points earned from this quest so far
  final int pointsEarned;

  /// Total points available for this quest
  final int totalPointsAvailable;

  /// Number of tasks completed
  final int tasksCompleted;

  /// Total number of tasks in the quest
  final int totalTasks;

  /// Time spent on quest (in minutes)
  final int timeSpentMinutes;

  /// Last milestone achieved
  final ProgressMilestone? lastMilestone;

  /// Timestamps when milestones were reached
  final Map<String, DateTime> milestoneTimestamps;

  /// Daily progress entries (date -> percentage gained that day)
  final Map<String, double> dailyProgress;

  /// Streak of consecutive days working on quest
  final int currentStreak;

  /// Longest streak achieved on this quest
  final int longestStreak;

  /// Whether the quest progress is active
  final bool isActive;

  /// Additional progress metadata
  final Map<String, dynamic>? metadata;

  /// Progress start timestamp
  final DateTime startedAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Completion timestamp (if completed)
  final DateTime? completedAt;

  const QuestProgress({
    required this.id,
    required this.questId,
    required this.userId,
    required this.progressPercentage,
    required this.pointsEarned,
    required this.totalPointsAvailable,
    required this.tasksCompleted,
    required this.totalTasks,
    required this.timeSpentMinutes,
    this.lastMilestone,
    required this.milestoneTimestamps,
    required this.dailyProgress,
    required this.currentStreak,
    required this.longestStreak,
    required this.isActive,
    this.metadata,
    required this.startedAt,
    required this.updatedAt,
    this.completedAt,
  });

  /// Create QuestProgress from JSON
  factory QuestProgress.fromJson(Map<String, dynamic> json) => _$QuestProgressFromJson(json);

  /// Convert QuestProgress to JSON
  Map<String, dynamic> toJson() => _$QuestProgressToJson(this);

  /// Check if quest is completed (100% progress)
  bool get isCompleted => progressPercentage >= 1.0;

  /// Get progress as percentage (0-100)
  double get progressPercent => progressPercentage * 100;

  /// Get remaining tasks count
  int get remainingTasks => totalTasks - tasksCompleted;

  /// Get progress efficiency (points per minute)
  double get efficiency {
    if (timeSpentMinutes <= 0) return 0.0;
    return pointsEarned / timeSpentMinutes;
  }

  /// Get average daily progress over the last 7 days
  double get averageDailyProgress {
    if (dailyProgress.isEmpty) return 0.0;
    
    final now = DateTime.now();
    double totalProgress = 0.0;
    int days = 0;
    
    for (int i = 0; i < 7; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey = _formatDate(date);
      if (dailyProgress.containsKey(dateKey)) {
        totalProgress += dailyProgress[dateKey]!;
        days++;
      }
    }
    
    return days > 0 ? totalProgress / days : 0.0;
  }

  /// Get estimated completion date based on current progress rate
  DateTime? get estimatedCompletion {
    if (isCompleted || averageDailyProgress <= 0) return null;
    
    final remainingProgress = 1.0 - progressPercentage;
    final daysNeeded = remainingProgress / averageDailyProgress;
    
    return DateTime.now().add(Duration(days: daysNeeded.ceil()));
  }

  /// Get time spent in hours
  double get timeSpentHours => timeSpentMinutes / 60.0;

  /// Get milestone progress (which milestones achieved)
  List<ProgressMilestone> get achievedMilestones {
    final achieved = <ProgressMilestone>[];
    
    if (progressPercentage >= 0.01) achieved.add(ProgressMilestone.started);
    if (progressPercentage >= 0.25) achieved.add(ProgressMilestone.quarter);
    if (progressPercentage >= 0.50) achieved.add(ProgressMilestone.half);
    if (progressPercentage >= 0.75) achieved.add(ProgressMilestone.threeQuarters);
    if (progressPercentage >= 0.90) achieved.add(ProgressMilestone.nearComplete);
    if (progressPercentage >= 1.0) achieved.add(ProgressMilestone.completed);
    
    return achieved;
  }

  /// Get next milestone to achieve
  ProgressMilestone? get nextMilestone {
    if (progressPercentage < 0.01) return ProgressMilestone.started;
    if (progressPercentage < 0.25) return ProgressMilestone.quarter;
    if (progressPercentage < 0.50) return ProgressMilestone.half;
    if (progressPercentage < 0.75) return ProgressMilestone.threeQuarters;
    if (progressPercentage < 0.90) return ProgressMilestone.nearComplete;
    if (progressPercentage < 1.0) return ProgressMilestone.completed;
    return null; // All milestones achieved
  }

  /// Check if worked on quest today
  bool get workedToday {
    final today = _formatDate(DateTime.now());
    return dailyProgress.containsKey(today) && dailyProgress[today]! > 0;
  }

  /// Get progress made today
  double get todayProgress {
    final today = _formatDate(DateTime.now());
    return dailyProgress[today] ?? 0.0;
  }

  /// Create a copy with updated fields
  QuestProgress copyWith({
    String? id,
    String? questId,
    String? userId,
    double? progressPercentage,
    int? pointsEarned,
    int? totalPointsAvailable,
    int? tasksCompleted,
    int? totalTasks,
    int? timeSpentMinutes,
    ProgressMilestone? lastMilestone,
    Map<String, DateTime>? milestoneTimestamps,
    Map<String, double>? dailyProgress,
    int? currentStreak,
    int? longestStreak,
    bool? isActive,
    Map<String, dynamic>? metadata,
    DateTime? startedAt,
    DateTime? updatedAt,
    DateTime? completedAt,
  }) {
    return QuestProgress(
      id: id ?? this.id,
      questId: questId ?? this.questId,
      userId: userId ?? this.userId,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      pointsEarned: pointsEarned ?? this.pointsEarned,
      totalPointsAvailable: totalPointsAvailable ?? this.totalPointsAvailable,
      tasksCompleted: tasksCompleted ?? this.tasksCompleted,
      totalTasks: totalTasks ?? this.totalTasks,
      timeSpentMinutes: timeSpentMinutes ?? this.timeSpentMinutes,
      lastMilestone: lastMilestone ?? this.lastMilestone,
      milestoneTimestamps: milestoneTimestamps ?? this.milestoneTimestamps,
      dailyProgress: dailyProgress ?? this.dailyProgress,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
      startedAt: startedAt ?? this.startedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  /// Create empty quest progress for initialization
  static QuestProgress empty() {
    final now = DateTime.now();
    return QuestProgress(
      id: '',
      questId: '',
      userId: '',
      progressPercentage: 0.0,
      pointsEarned: 0,
      totalPointsAvailable: 0,
      tasksCompleted: 0,
      totalTasks: 0,
      timeSpentMinutes: 0,
      milestoneTimestamps: {},
      dailyProgress: {},
      currentStreak: 0,
      longestStreak: 0,
      isActive: true,
      startedAt: now,
      updatedAt: now,
    );
  }

  /// Format date for daily progress keys (YYYY-MM-DD)
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  @override
  List<Object?> get props => [
        id,
        questId,
        userId,
        progressPercentage,
        pointsEarned,
        totalPointsAvailable,
        tasksCompleted,
        totalTasks,
        timeSpentMinutes,
        lastMilestone,
        milestoneTimestamps,
        dailyProgress,
        currentStreak,
        longestStreak,
        isActive,
        metadata,
        startedAt,
        updatedAt,
        completedAt,
      ];

  @override
  bool get stringify => true;
}