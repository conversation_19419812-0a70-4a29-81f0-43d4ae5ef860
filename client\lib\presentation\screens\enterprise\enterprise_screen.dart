import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/enterprise/enterprise_bloc.dart';

class EnterpriseScreen extends StatelessWidget {
  const EnterpriseScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enterprise Management'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: BlocConsumer<EnterpriseBloc, EnterpriseState>(
        listener: (context, state) {
          if (state is EnterpriseError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is EnterpriseLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return DefaultTabController(
            length: 4,
            child: Column(
              children: [
                const TabBar(
                  tabs: [
                    Tab(icon: Icon(Icons.business), text: 'Organizations'),
                    Tab(icon: Icon(Icons.people), text: 'Members'),
                    Tab(icon: Icon(Icons.admin_panel_settings), text: 'Roles'),
                    Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildOrganizations(context, state),
                      _buildMembers(context, state),
                      _buildRoles(context, state),
                      _buildAnalytics(context, state),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateDialog(context),
        tooltip: 'Create Organization',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOrganizations(BuildContext context, EnterpriseState state) {
    final organizations = state is EnterpriseLoaded ? state.userOrganizations : null;
    
    return RefreshIndicator(
      onRefresh: () async {
        context.read<EnterpriseBloc>().add(const RefreshEnterprise());
      },
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Organization Management',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Manage your organizations',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _showCreateDialog(context),
                    icon: const Icon(Icons.business),
                    label: const Text('Create Organization'),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (organizations?.isNotEmpty == true) ...[
            ...organizations!.map((org) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  child: Text(org['name']?.toString().substring(0, 1).toUpperCase() ?? 'O'),
                ),
                title: Text(org['name']?.toString() ?? 'Unknown Organization'),
                subtitle: Text(org['description']?.toString() ?? 'No description'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  // Navigate to organization details
                },
              ),
            )),
          ] else ...[
            _buildEmptyState(
              icon: Icons.business_outlined,
              title: 'No Organizations',
              subtitle: 'Create your first organization',
              buttonText: 'Create Organization',
              onPressed: () => _showCreateDialog(context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMembers(BuildContext context, EnterpriseState state) {
    final members = state is EnterpriseLoaded ? state.members : null;
    
    return RefreshIndicator(
      onRefresh: () async {
        context.read<EnterpriseBloc>().add(const RefreshEnterprise());
      },
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Member Management',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Manage organization members',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (members?.isNotEmpty == true) ...[
            ...members!.map((member) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  child: Text(
                    member['name']?.toString().substring(0, 1).toUpperCase() ?? 'U',
                  ),
                ),
                title: Text(member['name']?.toString() ?? 'Unknown User'),
                subtitle: Text(member['email']?.toString() ?? 'No email'),
                trailing: Text(member['role']?.toString() ?? 'Member'),
              ),
            )),
          ] else ...[
            _buildEmptyState(
              icon: Icons.people_outline,
              title: 'No Members',
              subtitle: 'Invite members to your organization',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRoles(BuildContext context, EnterpriseState state) {
    final roles = state is EnterpriseLoaded ? state.roles : null;
    
    return RefreshIndicator(
      onRefresh: () async {
        context.read<EnterpriseBloc>().add(const RefreshEnterprise());
      },
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Role Management',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Define roles and permissions',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (roles?.isNotEmpty == true) ...[
            ...roles!.map((role) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: Icon(
                  Icons.admin_panel_settings,
                  color: _getRoleColor(role['name']?.toString() ?? ''),
                ),
                title: Text(role['name']?.toString() ?? 'Unknown Role'),
                subtitle: Text(role['description']?.toString() ?? 'No description'),
              ),
            )),
          ] else ...[
            _buildEmptyState(
              icon: Icons.admin_panel_settings_outlined,
              title: 'No Roles',
              subtitle: 'Create roles to organize permissions',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAnalytics(BuildContext context, EnterpriseState state) {
    final analytics = state is EnterpriseLoaded ? state.analytics : null;
    
    return RefreshIndicator(
      onRefresh: () async {
        context.read<EnterpriseBloc>().add(const RefreshEnterprise());
      },
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Enterprise Analytics',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Monitor organization performance',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (analytics != null) ...[
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Total Members',
                    '${analytics['totalMembers'] ?? 0}',
                    Icons.people,
                    Colors.blue,
                    context,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildMetricCard(
                    'Active Projects',
                    '${analytics['activeProjects'] ?? 0}',
                    Icons.task_alt,
                    Colors.green,
                    context,
                  ),
                ),
              ],
            ),
          ] else ...[
            _buildEmptyState(
              icon: Icons.analytics_outlined,
              title: 'No Analytics Data',
              subtitle: 'Data will appear once organization is active',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    String? buttonText,
    VoidCallback? onPressed,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (buttonText != null && onPressed != null) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: onPressed,
                child: Text(buttonText),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
    BuildContext context,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Create Organization'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Organization Name',
                hintText: 'Enter organization name',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter organization description',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty) {
                // For now, just show success message
                Navigator.of(dialogContext).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Organization creation feature coming soon!'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(String roleName) {
    switch (roleName.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'manager':
        return Colors.orange;
      case 'member':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}