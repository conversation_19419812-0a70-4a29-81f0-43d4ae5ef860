# Development environment overrides for <PERSON><PERSON>
# Includes development tools and services

services:
  postgres:
    ports:
      - "${POSTGRES_EXTERNAL_PORT:-5432}:5432"

  redis:
    ports:
      - "${REDIS_EXTERNAL_PORT:-6379}:6379"

  server:
    volumes:
      - ../server:/app/server:cached
      - ../shared:/app/shared:cached
      - server_cache:/app/server/.dart_tool
    ports:
      - "${SERVER_EXTERNAL_PORT:-8080}:8080"
      - "${SERVER_DEBUG_PORT:-9229}:9229"
    working_dir: /app/server
    command: ["dart", "run", "--enable-vm-service=0.0.0.0:9229", "bin/server.dart"]

  client:
    volumes:
      - ../client:/app/client:cached
      - ../shared:/app/shared:cached
      - client_cache:/app/client/.dart_tool
      - client_build:/app/client/build
    ports:
      - "${CLIENT_EXTERNAL_PORT:-3000}:3000"
    working_dir: /app/client
    command: ["flutter", "run", "-d", "web-server", "--web-hostname", "0.0.0.0", "--web-port", "3000"]

  nginx:
    image: nginx:latest
    platform: linux/amd64
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    ports:
      - "${NGINX_EXTERNAL_PORT:-80}:80"
    depends_on:
      - server
      - client
    networks:
      - quester-network

  pgadmin:
    image: dpage/pgadmin4:latest
    platform: linux/amd64
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD:-adminpass}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "${PGADMIN_EXTERNAL_PORT:-5050}:80"
    depends_on:
      - postgres
    networks:
      - quester-network

  redis-commander:
    image: node:18-slim
    platform: linux/amd64
    restart: unless-stopped
    working_dir: /app
    command: >
      sh -c "npm install -g redis-commander &&
             redis-commander --redis-host redis --redis-port 6379 --redis-password $$REDIS_PASSWORD --port 8081 --address 0.0.0.0"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redispass}
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    depends_on:
      - redis
    networks:
      - quester-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mailhog:
    image: mailhog/mailhog:latest
    platform: linux/amd64
    restart: unless-stopped
    ports:
      - "${MAILHOG_WEB_PORT:-8025}:8025"
      - "${MAILHOG_SMTP_PORT:-1025}:1025"
    networks:
      - quester-network

  minio:
    image: bitnami/minio:latest
    platform: linux/amd64
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-quester}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-questerpass}
      MINIO_DEFAULT_BUCKETS: quester-dev
    volumes:
      - minio_data:/bitnami/minio/data
    ports:
      - "${MINIO_API_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    networks:
      - quester-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 120s

volumes:
  pgadmin_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-pgadmin-data
  minio_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-minio-data
  server_cache:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-server-cache
  client_cache:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-client-cache
  client_build:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-client-build
