// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequestDto _$LoginRequestDtoFromJson(Map<String, dynamic> json) =>
    LoginRequestDto(
      email: json['email'] as String,
      password: json['password'] as String,
      rememberMe: json['rememberMe'] as bool? ?? false,
    );

Map<String, dynamic> _$LoginRequestDtoToJson(LoginRequestDto instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'rememberMe': instance.rememberMe,
    };

RegisterRequestDto _$RegisterRequestDtoFromJson(Map<String, dynamic> json) =>
    RegisterRequestDto(
      email: json['email'] as String,
      password: json['password'] as String,
      displayName: json['displayName'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );

Map<String, dynamic> _$RegisterRequestDtoToJson(RegisterRequestDto instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'displayName': instance.displayName,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
    };

AuthResponseDto _$AuthResponseDtoFromJson(Map<String, dynamic> json) =>
    AuthResponseDto(
      token: json['token'] as String,
      refreshToken: json['refreshToken'] as String,
      userId: json['userId'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      role: json['role'] as String,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      userProfile: json['userProfile'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AuthResponseDtoToJson(AuthResponseDto instance) =>
    <String, dynamic>{
      'token': instance.token,
      'refreshToken': instance.refreshToken,
      'userId': instance.userId,
      'email': instance.email,
      'displayName': instance.displayName,
      'avatarUrl': instance.avatarUrl,
      'role': instance.role,
      'expiresAt': instance.expiresAt.toIso8601String(),
      'userProfile': instance.userProfile,
    };

PasswordResetRequestDto _$PasswordResetRequestDtoFromJson(
  Map<String, dynamic> json,
) => PasswordResetRequestDto(email: json['email'] as String);

Map<String, dynamic> _$PasswordResetRequestDtoToJson(
  PasswordResetRequestDto instance,
) => <String, dynamic>{'email': instance.email};

PasswordResetConfirmDto _$PasswordResetConfirmDtoFromJson(
  Map<String, dynamic> json,
) => PasswordResetConfirmDto(
  token: json['token'] as String,
  newPassword: json['newPassword'] as String,
);

Map<String, dynamic> _$PasswordResetConfirmDtoToJson(
  PasswordResetConfirmDto instance,
) => <String, dynamic>{
  'token': instance.token,
  'newPassword': instance.newPassword,
};

RefreshTokenRequestDto _$RefreshTokenRequestDtoFromJson(
  Map<String, dynamic> json,
) => RefreshTokenRequestDto(refreshToken: json['refreshToken'] as String);

Map<String, dynamic> _$RefreshTokenRequestDtoToJson(
  RefreshTokenRequestDto instance,
) => <String, dynamic>{'refreshToken': instance.refreshToken};

ProfileUpdateDto _$ProfileUpdateDtoFromJson(Map<String, dynamic> json) =>
    ProfileUpdateDto(
      displayName: json['displayName'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      preferences: json['preferences'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ProfileUpdateDtoToJson(ProfileUpdateDto instance) =>
    <String, dynamic>{
      'displayName': instance.displayName,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'avatarUrl': instance.avatarUrl,
      'preferences': instance.preferences,
    };

ChangePasswordDto _$ChangePasswordDtoFromJson(Map<String, dynamic> json) =>
    ChangePasswordDto(
      currentPassword: json['currentPassword'] as String,
      newPassword: json['newPassword'] as String,
    );

Map<String, dynamic> _$ChangePasswordDtoToJson(ChangePasswordDto instance) =>
    <String, dynamic>{
      'currentPassword': instance.currentPassword,
      'newPassword': instance.newPassword,
    };
