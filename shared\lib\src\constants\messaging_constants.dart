/// Messaging and chat constants for the Quester platform
library;

/// Constants for messaging and real-time chat functionality
class MessagingConstants {
  // Message types
  static const String textMessage = 'text';
  static const String imageMessage = 'image';
  static const String fileMessage = 'file';
  static const String systemMessage = 'system';
  static const String questInviteMessage = 'quest_invite';
  static const String achievementMessage = 'achievement';
  static const String reactionMessage = 'reaction';
  
  // Message status
  static const String messageSent = 'sent';
  static const String messageDelivered = 'delivered';
  static const String messageRead = 'read';
  static const String messageFailed = 'failed';
  static const String messagePending = 'pending';
  
  // Chat types
  static const String directChat = 'direct';
  static const String groupChat = 'group';
  static const String questChat = 'quest';
  static const String teamChat = 'team';
  static const String publicChat = 'public';
  
  // Chat member roles
  static const String chatOwner = 'owner';
  static const String chatAdmin = 'admin';
  static const String chatModerator = 'moderator';
  static const String chatMember = 'member';
  static const String chatGuest = 'guest';
  
  // Message limits
  static const int maxMessageLength = 2000;
  static const int maxGroupMembers = 100;
  static const int maxFileSize = 25 * 1024 * 1024; // 25MB
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxChatNameLength = 100;
  static const int maxChatDescriptionLength = 500;
  
  // Typing indicators
  static const Duration typingIndicatorTimeout = Duration(seconds: 3);
  static const Duration typingIndicatorDelay = Duration(milliseconds: 500);
  static const String typingStartEvent = 'typing_start';
  static const String typingStopEvent = 'typing_stop';
  
  // Message reactions
  static const List<String> defaultReactions = [
    '👍', '👎', '❤️', '😂', '😮', '😢', '😡', '🎉'
  ];
  static const int maxReactionsPerMessage = 50;
  static const int maxReactionTypesPerMessage = 8;
  
  // File upload types
  static const List<String> allowedImageTypes = [
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'
  ];
  static const List<String> allowedDocumentTypes = [
    'pdf', 'doc', 'docx', 'txt', 'md', 'rtf'
  ];
  static const List<String> allowedArchiveTypes = [
    'zip', 'rar', '7z', 'tar', 'gz'
  ];
  static const List<String> allowedAudioTypes = [
    'mp3', 'wav', 'ogg', 'm4a', 'aac'
  ];
  static const List<String> allowedVideoTypes = [
    'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'
  ];
  
  // Message threading
  static const int maxThreadDepth = 3;
  static const int maxRepliesPerThread = 100;
  
  // Search and filtering
  static const int maxSearchResults = 50;
  static const int minSearchQueryLength = 2;
  static const Duration searchDebounceDelay = Duration(milliseconds: 300);
  
  // Message history
  static const int defaultMessagePageSize = 50;
  static const int maxMessagePageSize = 100;
  static const Duration messageHistoryRetention = Duration(days: 365);
  
  // Notification settings for messages
  static const Map<String, bool> defaultMessageNotifications = {
    'direct_messages': true,
    'group_mentions': true,
    'quest_updates': true,
    'system_messages': false,
    'reactions': false,
    'typing_indicators': true,
  };
  
  // Message formatting
  static const Map<String, String> messageFormats = {
    'bold': '**text**',
    'italic': '*text*',
    'code': '`code`',
    'code_block': '```code```',
    'quote': '> quote',
    'mention': '@username',
    'channel': '#channel',
    'link': '[text](url)',
  };
  
  // Error messages
  static const Map<String, String> messagingErrors = {
    'message_too_long': 'Message exceeds maximum length',
    'file_too_large': 'File size exceeds limit',
    'unsupported_file_type': 'File type not supported',
    'chat_not_found': 'Chat not found',
    'permission_denied': 'Permission denied',
    'user_blocked': 'User is blocked',
    'chat_archived': 'Chat is archived',
    'rate_limit_exceeded': 'Too many messages sent',
    'network_error': 'Network connection error',
    'upload_failed': 'File upload failed',
  };
  
  // WebSocket events for messaging
  static const Map<String, String> messagingEvents = {
    'message_sent': 'message_sent',
    'message_received': 'message_received',
    'message_updated': 'message_updated',
    'message_deleted': 'message_deleted',
    'message_reaction_added': 'message_reaction_added',
    'message_reaction_removed': 'message_reaction_removed',
    'typing_start': 'typing_start',
    'typing_stop': 'typing_stop',
    'user_joined_chat': 'user_joined_chat',
    'user_left_chat': 'user_left_chat',
    'chat_created': 'chat_created',
    'chat_updated': 'chat_updated',
    'chat_deleted': 'chat_deleted',
    'user_online': 'user_online',
    'user_offline': 'user_offline',
    'message_read': 'message_read',
    'chat_archived': 'chat_archived',
    'chat_unarchived': 'chat_unarchived',
  };
  
  // Utility methods
  
  /// Check if file type is allowed for messaging
  static bool isFileTypeAllowed(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return allowedImageTypes.contains(extension) ||
           allowedDocumentTypes.contains(extension) ||
           allowedArchiveTypes.contains(extension) ||
           allowedAudioTypes.contains(extension) ||
           allowedVideoTypes.contains(extension);
  }
  
  /// Get file category by extension
  static String getFileCategory(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    if (allowedImageTypes.contains(extension)) return 'image';
    if (allowedDocumentTypes.contains(extension)) return 'document';
    if (allowedArchiveTypes.contains(extension)) return 'archive';
    if (allowedAudioTypes.contains(extension)) return 'audio';
    if (allowedVideoTypes.contains(extension)) return 'video';
    return 'unknown';
  }
  
  /// Check if user can perform action based on role
  static bool canPerformAction(String userRole, String action) {
    const roleHierarchy = {
      'owner': 5,
      'admin': 4,
      'moderator': 3,
      'member': 2,
      'guest': 1,
    };
    
    const actionRequirements = {
      'send_message': 1,
      'upload_file': 2,
      'delete_own_message': 2,
      'delete_any_message': 3,
      'add_members': 3,
      'remove_members': 4,
      'change_settings': 4,
      'delete_chat': 5,
    };
    
    final userLevel = roleHierarchy[userRole] ?? 0;
    final requiredLevel = actionRequirements[action] ?? 5;
    
    return userLevel >= requiredLevel;
  }
  
  /// Format message timestamp
  static String formatMessageTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
