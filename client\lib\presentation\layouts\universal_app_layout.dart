import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../widgets/common/responsive_builder.dart';
import '../widgets/navigation/app_bar_widget.dart';
import '../widgets/navigation/bottom_navigation_widget.dart';
import '../widgets/navigation/side_navigation_widget.dart';
import '../widgets/navigation/rail_navigation_widget.dart';
import '../widgets/common/notification_badge.dart';
import '../widgets/common/user_avatar.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/gamification/gamification_bloc.dart';

/// Universal responsive app layout that adapts to different screen sizes
/// Provides consistent navigation and layout structure across the app
class UniversalAppLayout extends StatefulWidget {
  /// The main content to display
  final Widget child;
  
  /// Current selected navigation index
  final int selectedIndex;
  
  /// Callback when navigation item is selected
  final ValueChanged<int>? onNavigationChanged;
  
  /// Whether to show the app bar
  final bool showAppBar;
  
  /// Custom app bar title
  final String? title;
  
  /// Whether to show back button
  final bool showBackButton;
  
  /// Custom app bar actions
  final List<Widget>? appBarActions;
  
  /// Whether to show floating action button
  final bool showFab;
  
  /// Custom floating action button
  final Widget? floatingActionButton;

  const UniversalAppLayout({
    super.key,
    required this.child,
    this.selectedIndex = 0,
    this.onNavigationChanged,
    this.showAppBar = true,
    this.title,
    this.showBackButton = false,
    this.appBarActions,
    this.showFab = false,
    this.floatingActionButton,
  });

  @override
  State<UniversalAppLayout> createState() => _UniversalAppLayoutState();
}

class _UniversalAppLayoutState extends State<UniversalAppLayout> {
  // final bool _isDrawerOpen = false; // TODO: Implement drawer state management
  bool _isRailExtended = false;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: _buildMobileLayout,
      tablet: _buildTabletLayout,
      desktop: _buildDesktopLayout,
    );
  }

  /// Mobile layout with bottom navigation
  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      appBar: widget.showAppBar ? _buildAppBar(context, isMobile: true) : null,
      body: widget.child,
      bottomNavigationBar: BottomNavigationWidget(
        selectedIndex: widget.selectedIndex,
        onItemSelected: widget.onNavigationChanged,
      ),
      floatingActionButton: widget.showFab ? widget.floatingActionButton : null,
      drawer: _buildDrawer(context),
    );
  }

  /// Tablet layout with navigation rail
  Widget _buildTabletLayout(BuildContext context) {
    return Scaffold(
      appBar: widget.showAppBar ? _buildAppBar(context, isTablet: true) : null,
      body: Row(
        children: [
          RailNavigationWidget(
            selectedIndex: widget.selectedIndex,
            onItemSelected: widget.onNavigationChanged,
            isExtended: _isRailExtended,
            onExtendedChanged: (extended) {
              setState(() {
                _isRailExtended = extended;
              });
            },
          ),
          Expanded(child: widget.child),
        ],
      ),
      floatingActionButton: widget.showFab ? widget.floatingActionButton : null,
    );
  }

  /// Desktop layout with side navigation
  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      appBar: widget.showAppBar ? _buildAppBar(context, isDesktop: true) : null,
      body: Row(
        children: [
          SideNavigationWidget(
            selectedIndex: widget.selectedIndex,
            onItemSelected: widget.onNavigationChanged,
            isCollapsed: false,
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppConstants.defaultBorderRadius),
                ),
              ),
              child: widget.child,
            ),
          ),
        ],
      ),
      floatingActionButton: widget.showFab ? widget.floatingActionButton : null,
    );
  }

  /// Build responsive app bar
  PreferredSizeWidget _buildAppBar(
    BuildContext context, {
    bool isMobile = false,
    bool isTablet = false,
    bool isDesktop = false,
  }) {
    return AppBarWidget(
      title: widget.title,
      showBackButton: widget.showBackButton,
      leading: isMobile ? _buildMenuButton(context) : null,
      actions: [
        if (widget.appBarActions != null) ...widget.appBarActions!,
        _buildSearchButton(context),
        _buildNotificationButton(context),
        _buildAccountButton(context),
      ],
    );
  }

  /// Build menu button for mobile
  Widget _buildMenuButton(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.menu),
      onPressed: () {
        Scaffold.of(context).openDrawer();
      },
    );
  }

  /// Build search button
  Widget _buildSearchButton(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.search),
      onPressed: () {
        // TODO: Implement search functionality
        _showSearchDialog(context);
      },
    );
  }

  /// Build notification button with badge
  Widget _buildNotificationButton(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        int notificationCount = 0;
        if (state is GamificationLoaded) {
          // TODO: Get actual notification count from state
          notificationCount = 3; // Placeholder
        }

        return NotificationBadge(
          count: notificationCount,
          child: IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              _showNotificationPanel(context);
            },
          ),
        );
      },
    );
  }

  /// Build account button
  Widget _buildAccountButton(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthAuthenticated) {
          return Padding(
            padding: const EdgeInsets.only(right: AppConstants.defaultPadding),
            child: UserAvatar(
              user: state.user,
              size: 32,
              onTap: () {
                _showAccountPanel(context);
              },
            ),
          );
        }
        return IconButton(
          icon: const Icon(Icons.account_circle_outlined),
          onPressed: () {
            _showAccountPanel(context);
          },
        );
      },
    );
  }

  /// Build navigation drawer for mobile
  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: SideNavigationWidget(
        selectedIndex: widget.selectedIndex,
        onItemSelected: (index) {
          widget.onNavigationChanged?.call(index);
          Navigator.of(context).pop(); // Close drawer
        },
        isCollapsed: false,
        showHeader: true,
      ),
    );
  }

  /// Show search dialog
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'Search quests, tasks, users...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement search
              Navigator.of(context).pop();
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  /// Show notification panel
  void _showNotificationPanel(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.3,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppConstants.largeBorderRadius),
            ),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Notifications',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: 10, // Placeholder
                  itemBuilder: (context, index) => ListTile(
                    leading: const CircleAvatar(
                      child: Icon(Icons.notifications),
                    ),
                    title: Text('Notification ${index + 1}'),
                    subtitle: const Text('This is a sample notification'),
                    trailing: const Text('2m ago'),
                    onTap: () {
                      // TODO: Handle notification tap
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show account panel
  void _showAccountPanel(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.5,
        maxChildSize: 0.8,
        minChildSize: 0.3,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppConstants.largeBorderRadius),
            ),
          ),
          child: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              if (state is AuthAuthenticated) {
                return Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Row(
                        children: [
                          UserAvatar(user: state.user, size: 64),
                          const SizedBox(width: AppConstants.defaultPadding),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  state.user.displayName,
                                  style: Theme.of(context).textTheme.headlineSmall,
                                ),
                                Text(
                                  state.user.email,
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        controller: scrollController,
                        children: [
                          ListTile(
                            leading: const Icon(Icons.person),
                            title: const Text('Profile'),
                            onTap: () {
                              Navigator.of(context).pop();
                              // TODO: Navigate to profile
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.settings),
                            title: const Text('Settings'),
                            onTap: () {
                              Navigator.of(context).pop();
                              // TODO: Navigate to settings
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.help),
                            title: const Text('Help & Support'),
                            onTap: () {
                              Navigator.of(context).pop();
                              // TODO: Navigate to help
                            },
                          ),
                          const Divider(),
                          ListTile(
                            leading: const Icon(Icons.logout),
                            title: const Text('Sign Out'),
                            onTap: () {
                              Navigator.of(context).pop();
                              context.read<AuthBloc>().add(const LogoutRequested());
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }
              return const Center(child: CircularProgressIndicator());
            },
          ),
        ),
      ),
    );
  }
}
