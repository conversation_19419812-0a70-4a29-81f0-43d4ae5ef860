import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('BackupCode Model Tests', () {
    test('should create BackupCode with required fields', () {
      final now = DateTime.now();
      final backupCode = BackupCode(
        id: 'backup_001',
        userId: 'user_123',
        codeHash: 'hash_123',
        createdAt: now,
      );

      expect(backupCode.id, 'backup_001');
      expect(backupCode.userId, 'user_123');
      expect(backupCode.codeHash, 'hash_123');
      expect(backupCode.createdAt, now);
      expect(backupCode.isUsed, false);
      expect(backupCode.isActive, true);
      expect(backupCode.usedAt, isNull);
      expect(backupCode.revokedAt, isNull);
    });

    test('should serialize and deserialize BackupCode correctly', () {
      final now = DateTime.now();
      final original = BackupCode(
        id: 'backup_001',
        userId: 'user_123',
        codeHash: 'hash_123',
        createdAt: now,
        usedAt: now.add(Duration(minutes: 5)),
        isUsed: true,
        ipAddress: '***********',
        userAgent: 'Test Agent',
      );

      final json = original.toJson();
      final restored = BackupCode.fromJson(json);

      expect(restored.id, original.id);
      expect(restored.userId, original.userId);
      expect(restored.codeHash, original.codeHash);
      expect(restored.createdAt, original.createdAt);
      expect(restored.usedAt, original.usedAt);
      expect(restored.isUsed, original.isUsed);
      expect(restored.ipAddress, original.ipAddress);
      expect(restored.userAgent, original.userAgent);
    });

    test('should support copyWith functionality', () {
      final original = BackupCode(
        id: 'backup_001',
        userId: 'user_123',
        codeHash: 'hash_123',
        createdAt: DateTime.now(),
      );

      final updated = original.copyWith(
        isUsed: true,
        usedAt: DateTime.now(),
      );

      expect(updated.id, original.id);
      expect(updated.userId, original.userId);
      expect(updated.isUsed, true);
      expect(updated.usedAt, isNotNull);
      expect(original.isUsed, false);
      expect(original.usedAt, isNull);
    });
  });

  group('BackupCodeSet Model Tests', () {
    test('should create BackupCodeSet with required fields', () {
      final now = DateTime.now();
      final codes = ['CODE1-234', 'CODE2-567', 'CODE3-890'];
      
      final codeSet = BackupCodeSet(
        codes: codes,
        generatedAt: now,
        userId: 'user_123',
        remainingCodes: 10,
      );

      expect(codeSet.codes, codes);
      expect(codeSet.generatedAt, now);
      expect(codeSet.userId, 'user_123');
      expect(codeSet.remainingCodes, 10);
    });

    test('should serialize and deserialize BackupCodeSet correctly', () {
      final now = DateTime.now();
      final codes = ['CODE1-234', 'CODE2-567'];
      
      final original = BackupCodeSet(
        codes: codes,
        generatedAt: now,
        userId: 'user_123',
        remainingCodes: 8,
        displayHint: 'Store these codes safely',
      );

      final json = original.toJson();
      final restored = BackupCodeSet.fromJson(json);

      expect(restored.codes, original.codes);
      expect(restored.generatedAt, original.generatedAt);
      expect(restored.userId, original.userId);
      expect(restored.remainingCodes, original.remainingCodes);
      expect(restored.displayHint, original.displayHint);
    });
  });

  group('BackupCodeValidationResult Model Tests', () {
    test('should create successful validation result', () {
      final now = DateTime.now();
      
      final result = BackupCodeValidationResult(
        isValid: true,
        backupCodeId: 'backup_001',
        validatedAt: now,
        remainingCodes: 9,
        codeConsumed: true,
      );

      expect(result.isValid, true);
      expect(result.backupCodeId, 'backup_001');
      expect(result.validatedAt, now);
      expect(result.remainingCodes, 9);
      expect(result.codeConsumed, true);
      expect(result.errorMessage, isNull);
    });

    test('should create failed validation result', () {
      final now = DateTime.now();
      
      final result = BackupCodeValidationResult(
        isValid: false,
        errorMessage: 'Invalid backup code',
        validatedAt: now,
        codeConsumed: false,
      );

      expect(result.isValid, false);
      expect(result.errorMessage, 'Invalid backup code');
      expect(result.validatedAt, now);
      expect(result.codeConsumed, false);
      expect(result.backupCodeId, isNull);
    });

    test('should serialize and deserialize validation result correctly', () {
      final now = DateTime.now();
      
      final original = BackupCodeValidationResult(
        isValid: true,
        backupCodeId: 'backup_001',
        warningMessage: 'Only 2 codes remaining',
        metadata: {'ip': '***********'},
        validatedAt: now,
        remainingCodes: 2,
        codeConsumed: true,
      );

      final json = original.toJson();
      final restored = BackupCodeValidationResult.fromJson(json);

      expect(restored.isValid, original.isValid);
      expect(restored.backupCodeId, original.backupCodeId);
      expect(restored.warningMessage, original.warningMessage);
      expect(restored.metadata, original.metadata);
      expect(restored.validatedAt, original.validatedAt);
      expect(restored.remainingCodes, original.remainingCodes);
      expect(restored.codeConsumed, original.codeConsumed);
    });
  });

  group('BackupCodeGenerationConfig Model Tests', () {
    test('should create default configuration', () {
      const config = BackupCodeGenerationConfig();

      expect(config.codeCount, 10);
      expect(config.codeLength, 8);
      expect(config.includeHyphens, true);
      expect(config.charset, '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ');
      expect(config.maxCodesPerUser, 10);
      expect(config.codeLifetimeDays, 365);
      expect(config.revokeExistingCodes, true);
      expect(config.codeLifetime.inDays, 365);
    });

    test('should create custom configuration', () {
      const config = BackupCodeGenerationConfig(
        codeCount: 20,
        codeLength: 12,
        includeHyphens: false,
        charset: '0123456789',
        maxCodesPerUser: 15,
        codeLifetimeDays: 180,
        revokeExistingCodes: false,
      );

      expect(config.codeCount, 20);
      expect(config.codeLength, 12);
      expect(config.includeHyphens, false);
      expect(config.charset, '0123456789');
      expect(config.maxCodesPerUser, 15);
      expect(config.codeLifetimeDays, 180);
      expect(config.revokeExistingCodes, false);
      expect(config.codeLifetime.inDays, 180);
    });

    test('should serialize and deserialize config correctly', () {
      const original = BackupCodeGenerationConfig(
        codeCount: 15,
        codeLength: 10,
        includeHyphens: false,
        maxCodesPerUser: 20,
      );

      final json = original.toJson();
      final restored = BackupCodeGenerationConfig.fromJson(json);

      expect(restored.codeCount, original.codeCount);
      expect(restored.codeLength, original.codeLength);
      expect(restored.includeHyphens, original.includeHyphens);
      expect(restored.maxCodesPerUser, original.maxCodesPerUser);
    });
  });

  group('BackupCodeStatus Model Tests', () {
    test('should create backup code status', () {      
      const status = BackupCodeStatus(
        hasActiveCodes: true,
        totalActiveCodes: 10,
        availableCodes: 7,
        usedCodes: 3,
        needsRegeneration: false,
        lowCodesWarning: true,
      );

      expect(status.hasActiveCodes, true);
      expect(status.totalActiveCodes, 10);
      expect(status.availableCodes, 7);
      expect(status.usedCodes, 3);
      expect(status.needsRegeneration, false);
      expect(status.lowCodesWarning, true);
    });

    test('should serialize and deserialize status correctly', () {
      final now = DateTime.now();
      
      final original = BackupCodeStatus(
        hasActiveCodes: true,
        totalActiveCodes: 5,
        availableCodes: 2,
        usedCodes: 3,
        lastGenerated: now.subtract(Duration(days: 30)),
        lastUsed: now.subtract(Duration(hours: 2)),
        needsRegeneration: true,
        lowCodesWarning: true,
      );

      final json = original.toJson();
      final restored = BackupCodeStatus.fromJson(json);

      expect(restored.hasActiveCodes, original.hasActiveCodes);
      expect(restored.totalActiveCodes, original.totalActiveCodes);
      expect(restored.availableCodes, original.availableCodes);
      expect(restored.usedCodes, original.usedCodes);
      expect(restored.lastGenerated, original.lastGenerated);
      expect(restored.lastUsed, original.lastUsed);
      expect(restored.needsRegeneration, original.needsRegeneration);
      expect(restored.lowCodesWarning, original.lowCodesWarning);
    });
  });

  group('Request/Response DTOs Tests', () {
    test('should create GenerateBackupCodesRequest', () {
      const config = BackupCodeGenerationConfig(codeCount: 15);
      const request = GenerateBackupCodesRequest(
        config: config,
        revokeExisting: false,
        reason: 'Test generation',
      );

      expect(request.config, config);
      expect(request.revokeExisting, false);
      expect(request.reason, 'Test generation');
    });

    test('should create ValidateBackupCodeRequest', () {
      const request = ValidateBackupCodeRequest(
        code: 'TEST-1234',
        consumeOnUse: false,
      );

      expect(request.code, 'TEST-1234');
      expect(request.consumeOnUse, false);
    });

    test('should create RevokeBackupCodesRequest', () {
      const request = RevokeBackupCodesRequest(
        specificCodes: ['CODE1', 'CODE2'],
        reason: 'Security breach',
      );

      expect(request.specificCodes, ['CODE1', 'CODE2']);
      expect(request.reason, 'Security breach');
    });

    test('should serialize and deserialize response DTOs', () {
      final codeSet = BackupCodeSet(
        codes: ['CODE1', 'CODE2'],
        generatedAt: DateTime.now(),
        userId: 'user_123',
        remainingCodes: 2,
      );

      final original = BackupCodesResponse(
        data: codeSet,
        message: 'Codes generated successfully',
        success: true,
      );

      final json = original.toJson();
      final restored = BackupCodesResponse.fromJson(json);

      expect(restored.data.codes, original.data.codes);
      expect(restored.message, original.message);
      expect(restored.success, original.success);
    });
  });

  group('Edge Cases and Validation Tests', () {
    test('should handle empty codes list', () {
      final codeSet = BackupCodeSet(
        codes: [],
        generatedAt: DateTime.now(),
        userId: 'user_123',
        remainingCodes: 0,
      );

      expect(codeSet.codes, isEmpty);
      expect(codeSet.remainingCodes, 0);
    });

    test('should handle null optional fields', () {
      final result = BackupCodeValidationResult(
        isValid: false,
        validatedAt: DateTime.now(),
      );

      expect(result.backupCodeId, isNull);
      expect(result.errorMessage, isNull);
      expect(result.warningMessage, isNull);
      expect(result.remainingCodes, isNull);
      expect(result.metadata, isEmpty);
    });

    test('should validate config constraints', () {
      const config = BackupCodeGenerationConfig(
        codeCount: 0,
        codeLength: 0,
      );

      // In a real implementation, you might want to validate these constraints
      expect(config.codeCount, 0);
      expect(config.codeLength, 0);
    });

    test('should handle copyWith null values correctly', () {
      const original = BackupCodeStatus(
        hasActiveCodes: true,
        totalActiveCodes: 10,
        availableCodes: 8,
        usedCodes: 2,
      );

      final updated = original.copyWith(
        needsRegeneration: true,
        lowCodesWarning: null, // Should keep original value
      );

      expect(updated.hasActiveCodes, original.hasActiveCodes);
      expect(updated.needsRegeneration, true);
      expect(updated.lowCodesWarning, original.lowCodesWarning);
    });
  });
}