import 'dart:io';
import 'package:shelf/shelf.dart';

/// Security headers middleware for production
class SecurityHeaders {
  static Middleware get middleware => (Handler innerHandler) {
    return (Request request) async {
      final response = await innerHandler(request);
      
      final headers = <String, String>{};
      
      // Force HTTPS in production
      if (_isProduction()) {
        headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
      }
      
      // Content Security Policy
      if (_shouldEnableCSP()) {
        headers['Content-Security-Policy'] = _buildCSPHeader();
      }
      
      // Frame options
      if (_shouldEnableFrameOptions()) {
        headers['X-Frame-Options'] = 'DENY';
      }
      
      // Additional security headers
      headers.addAll({
        'X-Content-Type-Options': 'nosniff',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
      });
      
      // Remove sensitive headers
      final newHeaders = Map<String, String>.from(response.headers);
      newHeaders.removeWhere((key, value) => 
          key.toLowerCase().startsWith('x-powered-by') ||
          key.toLowerCase().startsWith('server'));
      
      // Add security headers
      newHeaders.addAll(headers);
      
      return response.change(headers: newHeaders);
    };
  };

  static bool _isProduction() {
    return Platform.environment['NODE_ENV'] == 'production' ||
           Platform.environment['ENABLE_HSTS'] == 'true';
  }

  static bool _shouldEnableCSP() {
    return Platform.environment['ENABLE_CSP'] == 'true';
  }

  static bool _shouldEnableFrameOptions() {
    return Platform.environment['ENABLE_FRAME_OPTIONS'] == 'true';
  }

  static String _buildCSPHeader() {
    // Strict CSP for API endpoints
    return "default-src 'none'; "
           "script-src 'self'; "
           "style-src 'self' 'unsafe-inline'; "
           "img-src 'self' data: https:; "
           "font-src 'self'; "
           "connect-src 'self'; "
           "frame-ancestors 'none'; "
           "base-uri 'self'; "
           "form-action 'self';";
  }
}

/// HTTPS redirect middleware
class HTTPSRedirect {
  static Middleware get middleware => (Handler innerHandler) {
    return (Request request) async {
      // Only redirect in production or when explicitly enabled
      if (_shouldRedirectToHTTPS() && !_isSecureRequest(request)) {
        final httpsUrl = _buildHTTPSUrl(request);
        return Response.movedPermanently(httpsUrl);
      }
      
      return await innerHandler(request);
    };
  };

  static bool _shouldRedirectToHTTPS() {
    return Platform.environment['NODE_ENV'] == 'production' ||
           Platform.environment['FORCE_HTTPS'] == 'true';
  }

  static bool _isSecureRequest(Request request) {
    // Check various headers that indicate HTTPS
    final proto = request.headers['x-forwarded-proto'];
    final scheme = request.requestedUri.scheme;
    
    return scheme == 'https' || proto == 'https';
  }

  static String _buildHTTPSUrl(Request request) {
    final uri = request.requestedUri;
    return uri.replace(scheme: 'https', port: 443).toString();
  }
}

/// Combined security middleware
class ProductionSecurity {
  static List<Middleware> get middlewares => [
    HTTPSRedirect.middleware,
    SecurityHeaders.middleware,
  ];
}
