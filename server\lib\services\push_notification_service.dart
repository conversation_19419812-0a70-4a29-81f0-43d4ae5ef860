import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/environment_config.dart';

/// Service for sending push notifications to mobile devices
class PushNotificationService {
  final EnvironmentConfig _config;
  final http.Client _httpClient;
  
  /// Cache for device tokens
  final Map<String, List<String>> _userDeviceTokens = {};

  PushNotificationService({
    required EnvironmentConfig config,
    http.Client? httpClient,
  })  : _config = config,
        _httpClient = httpClient ?? http.Client();

  /// Registers a device token for a user
  Future<void> registerDevice(String deviceToken, String userId) async {
    if (!_userDeviceTokens.containsKey(userId)) {
      _userDeviceTokens[userId] = [];
    }
    
    if (!_userDeviceTokens[userId]!.contains(deviceToken)) {
      _userDeviceTokens[userId]!.add(deviceToken);
    }
  }

  /// Unregisters a device token for a user
  Future<void> unregisterDevice(String deviceToken, String userId) async {
    _userDeviceTokens[userId]?.remove(deviceToken);
    if (_userDeviceTokens[userId]?.isEmpty == true) {
      _userDeviceTokens.remove(userId);
    }
  }

  /// Sends a push notification to a specific user
  Future<void> sendNotification({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? sound,
    bool badge = true,
    String? imageUrl,
    List<NotificationAction>? actions,
  }) async {
    final deviceTokens = _userDeviceTokens[userId];
    if (deviceTokens == null || deviceTokens.isEmpty) {
      throw Exception('No device tokens found for user: $userId');
    }

    // Send to all user devices
    final futures = deviceTokens.map((token) => _sendToDevice(
      deviceToken: token,
      title: title,
      body: body,
      data: data,
      sound: sound,
      badge: badge,
      imageUrl: imageUrl,
      actions: actions,
    ));

    await Future.wait(futures);
  }

  /// Sends a push notification to multiple users
  Future<void> sendBulkNotification({
    required List<String> userIds,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? sound,
    bool badge = true,
    String? imageUrl,
    List<NotificationAction>? actions,
  }) async {
    final futures = userIds.map((userId) => sendNotification(
      userId: userId,
      title: title,
      body: body,
      data: data,
      sound: sound,
      badge: badge,
      imageUrl: imageUrl,
      actions: actions,
    ));

    await Future.wait(futures);
  }

  /// Sends a push notification to all registered devices
  Future<void> sendBroadcastNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? sound,
    bool badge = true,
    String? imageUrl,
    List<NotificationAction>? actions,
  }) async {
    final allTokens = _userDeviceTokens.values
        .expand((tokens) => tokens)
        .toList();

    if (allTokens.isEmpty) {
      return;
    }

    // Send in batches to avoid overwhelming the service
    const batchSize = 100;
    for (int i = 0; i < allTokens.length; i += batchSize) {
      final batch = allTokens.skip(i).take(batchSize).toList();
      final futures = batch.map((token) => _sendToDevice(
        deviceToken: token,
        title: title,
        body: body,
        data: data,
        sound: sound,
        badge: badge,
        imageUrl: imageUrl,
        actions: actions,
      ));

      await Future.wait(futures);
    }
  }

  /// Sends notification to a specific device token
  Future<void> _sendToDevice({
    required String deviceToken,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? sound,
    bool badge = true,
    String? imageUrl,
    List<NotificationAction>? actions,
  }) async {
    try {
      // Determine platform based on token format
      final platform = _detectPlatform(deviceToken);
      
      switch (platform) {
        case DevicePlatform.ios:
          await _sendAPNSNotification(
            deviceToken: deviceToken,
            title: title,
            body: body,
            data: data,
            sound: sound,
            badge: badge,
            imageUrl: imageUrl,
            actions: actions,
          );
          break;
        case DevicePlatform.android:
          await _sendFCMNotification(
            deviceToken: deviceToken,
            title: title,
            body: body,
            data: data,
            sound: sound,
            badge: badge,
            imageUrl: imageUrl,
            actions: actions,
          );
          break;
        case DevicePlatform.web:
          await _sendWebPushNotification(
            deviceToken: deviceToken,
            title: title,
            body: body,
            data: data,
            imageUrl: imageUrl,
            actions: actions,
          );
          break;
      }
    } catch (error) {
      print('Failed to send push notification to $deviceToken: $error');
      // TODO: Log error and potentially remove invalid tokens
    }
  }

  /// Sends notification via Apple Push Notification Service (APNS)
  Future<void> _sendAPNSNotification({
    required String deviceToken,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? sound,
    bool badge = true,
    String? imageUrl,
    List<NotificationAction>? actions,
  }) async {
    final payload = {
      'aps': {
        'alert': {
          'title': title,
          'body': body,
        },
        'sound': sound ?? 'default',
        'badge': badge ? 1 : 0,
        if (imageUrl != null) 'mutable-content': 1,
      },
      if (data != null) ...data,
      if (imageUrl != null) 'image': imageUrl,
      if (actions != null) 'actions': actions.map((a) => a.toJson()).toList(),
    };

    final response = await _httpClient.post(
      Uri.parse('https://api.push.apple.com/3/device/$deviceToken'),
      headers: {
        'Authorization': 'Bearer ${_config.apnsAuthKey}',
        'apns-topic': _config.apnsBundleId,
        'apns-push-type': 'alert',
        'Content-Type': 'application/json',
      },
      body: jsonEncode(payload),
    );

    if (response.statusCode != 200) {
      throw Exception('APNS request failed: ${response.statusCode} ${response.body}');
    }
  }

  /// Sends notification via Firebase Cloud Messaging (FCM)
  Future<void> _sendFCMNotification({
    required String deviceToken,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? sound,
    bool badge = true,
    String? imageUrl,
    List<NotificationAction>? actions,
  }) async {
    final payload = {
      'to': deviceToken,
      'notification': {
        'title': title,
        'body': body,
        if (sound != null) 'sound': sound,
        if (imageUrl != null) 'image': imageUrl,
      },
      'data': {
        'click_action': 'FLUTTER_NOTIFICATION_CLICK',
        if (data != null) ...data.map((k, v) => MapEntry(k, v.toString())),
      },
      if (actions != null) 'actions': actions.map((a) => a.toJson()).toList(),
      'android': {
        'notification': {
          'channel_id': 'default',
          'priority': 'high',
          'default_sound': true,
          'default_vibrate_timings': true,
        },
      },
    };

    final response = await _httpClient.post(
      Uri.parse('https://fcm.googleapis.com/fcm/send'),
      headers: {
        'Authorization': 'key=${_config.fcmServerKey}',
        'Content-Type': 'application/json',
      },
      body: jsonEncode(payload),
    );

    if (response.statusCode != 200) {
      throw Exception('FCM request failed: ${response.statusCode} ${response.body}');
    }

    final responseData = jsonDecode(response.body);
    if (responseData['failure'] > 0) {
      throw Exception('FCM delivery failed: ${responseData['results']}');
    }
  }

  /// Sends web push notification
  Future<void> _sendWebPushNotification({
    required String deviceToken,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
    List<NotificationAction>? actions,
  }) async {
    final payload = {
      'notification': {
        'title': title,
        'body': body,
        'icon': '/icons/notification-icon.png',
        if (imageUrl != null) 'image': imageUrl,
        if (actions != null) 'actions': actions.map((a) => {
          'action': a.id,
          'title': a.title,
          if (a.icon != null) 'icon': a.icon,
        }).toList(),
        'data': {
          'url': '/',
          if (data != null) ...data,
        },
      },
    };

    // For web push, we would typically use the Web Push Protocol
    // This is a simplified implementation
    final response = await _httpClient.post(
      Uri.parse(deviceToken), // Web push endpoint
      headers: {
        'Content-Type': 'application/json',
        'TTL': '86400', // 24 hours
      },
      body: jsonEncode(payload),
    );

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('Web Push request failed: ${response.statusCode} ${response.body}');
    }
  }

  /// Detects device platform based on token format
  DevicePlatform _detectPlatform(String deviceToken) {
    if (deviceToken.startsWith('https://')) {
      return DevicePlatform.web;
    } else if (deviceToken.length == 64 && RegExp(r'^[a-fA-F0-9]+$').hasMatch(deviceToken)) {
      return DevicePlatform.ios;
    } else {
      return DevicePlatform.android;
    }
  }

  /// Gets device tokens for a user
  List<String> getDeviceTokens(String userId) {
    return _userDeviceTokens[userId] ?? [];
  }

  /// Gets all registered users
  List<String> getRegisteredUsers() {
    return _userDeviceTokens.keys.toList();
  }

  /// Cleans up invalid device tokens
  Future<void> cleanupInvalidTokens() async {
    // TODO: Implement token validation and cleanup
    // This would involve checking with APNS/FCM for invalid tokens
  }

  /// Disposes resources
  void dispose() {
    _httpClient.close();
    _userDeviceTokens.clear();
  }
}

/// Device platforms for push notifications
enum DevicePlatform {
  ios,
  android,
  web,
}

/// Notification action for interactive notifications
class NotificationAction {
  final String id;
  final String title;
  final String? icon;
  final Map<String, dynamic>? data;

  const NotificationAction({
    required this.id,
    required this.title,
    this.icon,
    this.data,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    if (icon != null) 'icon': icon,
    if (data != null) 'data': data,
  };

  factory NotificationAction.fromJson(Map<String, dynamic> json) => NotificationAction(
    id: json['id'],
    title: json['title'],
    icon: json['icon'],
    data: json['data'],
  );
}
