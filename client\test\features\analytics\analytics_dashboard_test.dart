import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quester_client/presentation/screens/analytics/analytics_screen.dart';
import 'package:quester_client/presentation/blocs/analytics/analytics_bloc.dart';
import 'package:quester_client/core/services/api_service.dart';
import 'package:quester_client/core/models/api_response.dart' as client_api;
import 'package:quester_client/data/repositories/api_repository.dart';
import 'package:quester_client/data/datasources/remote_data_source.dart';
import 'package:quester_client/core/services/auth_service.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:typed_data';
import 'package:shared/shared.dart';

// Mock API service for testing
class MockApiService extends ApiService {
  @override
  Future<client_api.ApiResponse<T>> get<T>(String endpoint, {Map<String, String>? queryParams, String? token}) async {
    return client_api.ApiResponse.success({'message': 'test response'} as T);
  }

  @override
  Future<client_api.ApiResponse<T>> post<T>(String endpoint, {Map<String, dynamic>? body, String? token}) async {
    return client_api.ApiResponse.success({'message': 'test response'} as T);
  }

  @override
  Future<client_api.ApiResponse<T>> put<T>(String endpoint, {Map<String, dynamic>? body, String? token}) async {
    return client_api.ApiResponse.success({'message': 'test response'} as T);
  }

  @override
  Future<client_api.ApiResponse<T>> delete<T>(String endpoint, {String? token}) async {
    return client_api.ApiResponse.success({'message': 'test response'} as T);
  }
}

// Mock HTTP client for testing
class MockHttpClient implements http.Client {
  @override
  Future<http.Response> get(Uri url, {Map<String, String>? headers}) async {
    return http.Response('{"message": "test response"}', 200);
  }

  @override
  Future<http.Response> post(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    return http.Response('{"message": "test response"}', 200);
  }

  @override
  Future<http.Response> put(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    return http.Response('{"message": "test response"}', 200);
  }

  @override
  Future<http.Response> delete(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    return http.Response('{"message": "test response"}', 200);
  }

  @override
  Future<http.Response> patch(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    return http.Response('{"message": "test response"}', 200);
  }

  @override
  Future<http.Response> head(Uri url, {Map<String, String>? headers}) async {
    return http.Response('', 200);
  }

  @override
  Future<String> read(Uri url, {Map<String, String>? headers}) async {
    return '{"message": "test response"}';
  }

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    return http.StreamedResponse(
      Stream.fromIterable([utf8.encode('{"message": "test response"}')]),
      200,
    );
  }

  @override
  Future<Uint8List> readBytes(Uri url, {Map<String, String>? headers}) async {
    return Uint8List.fromList([123, 34, 109, 101, 115, 115, 97, 103, 101, 34, 58, 34, 116, 101, 115, 116, 34, 125]); // {"message":"test"}
  }

  @override
  void close() {}
}

// Mock remote data source for testing
class MockRemoteDataSource extends RemoteDataSource {
  MockRemoteDataSource() : super(apiService: MockApiService(), client: MockHttpClient());
}

// Mock auth service for testing
class MockAuthService extends AuthService {
  @override
  String? get currentToken => 'mock-token';

  @override
  User? get currentUser => null;
}

void main() {
  group('AnalyticsScreen Widget Tests', () {
    testWidgets('should display app bar with correct title', (tester) async {
      final mockRepository = MockApiRepository();
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => AnalyticsBloc(repository: mockRepository),
            child: const AnalyticsScreen(),
          ),
        ),
      );

      expect(find.text('Analytics'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('should display tab bar with correct tabs', (tester) async {
      final mockRepository = MockApiRepository();
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => AnalyticsBloc(repository: mockRepository),
            child: const AnalyticsScreen(),
          ),
        ),
      );

      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Metrics'), findsOneWidget);
      expect(find.text('Insights'), findsOneWidget);
    });

    testWidgets('should handle tab navigation', (tester) async {
      final mockRepository = MockApiRepository();
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => AnalyticsBloc(repository: mockRepository),
            child: const AnalyticsScreen(),
          ),
        ),
      );

      // Tap on Metrics tab
      await tester.tap(find.text('Metrics'));
      await tester.pumpAndSettle();

      // Should switch to metrics tab
      expect(find.text('Metrics'), findsOneWidget);
    });

    testWidgets('should display analytics content', (tester) async {
      final mockRepository = MockApiRepository();
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => AnalyticsBloc(repository: mockRepository),
            child: const AnalyticsScreen(),
          ),
        ),
      );

      await tester.pump();

      // Should display some analytics content
      expect(find.byType(TabBarView), findsOneWidget);
    });
  });

  group('Analytics Screen Integration Tests', () {
    test('should create analytics screen', () {
      const screen = AnalyticsScreen();
      expect(screen, isA<AnalyticsScreen>());
    });

    testWidgets('should integrate with AnalyticsBloc properly', (tester) async {
      final mockRepository = MockApiRepository();
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => AnalyticsBloc(repository: mockRepository),
            child: const AnalyticsScreen(),
          ),
        ),
      );

      expect(find.byType(AnalyticsScreen), findsOneWidget);
    });
  });
}

// Mock repository for testing
class MockApiRepository extends ApiRepository {
  MockApiRepository() : super(
    remoteDataSource: MockRemoteDataSource(),
    authService: MockAuthService(),
  );
}
