import 'dart:convert';
import '../services/database_service.dart';

/// Security audit service for tracking and monitoring security events
class SecurityAuditService {
  static final SecurityAuditService _instance = SecurityAuditService._internal();
  factory SecurityAuditService() => _instance;
  SecurityAuditService._internal();

  final DatabaseService _dbService = DatabaseService();

  /// Log security event
  Future<void> logSecurityEvent({
    required String eventType,
    required String userId,
    String? organizationId,
    required String description,
    Map<String, dynamic>? metadata,
    String? ipAddress,
    String? userAgent,
    String severity = 'info',
  }) async {
    try {
      await _dbService.execute(
        '''
          INSERT INTO security_audit_logs (
            event_type, user_id, organization_id, description, metadata,
            ip_address, user_agent, severity, created_at
          ) VALUES (
            @eventType, @userId::uuid, @organizationId::uuid, @description,
            @metadata::jsonb, @ipAddress::inet, @userAgent, @severity, NOW()
          )
        ''',
        parameters: {
          'eventType': eventType,
          'userId': userId,
          'organizationId': organizationId,
          'description': description,
          'metadata': jsonEncode(metadata ?? {}),
          'ipAddress': ipAddress,
          'userAgent': userAgent,
          'severity': severity,
        },
      );
    } catch (e) {
      print('❌ Failed to log security event: $e');
      // Don't throw - audit logging shouldn't break application flow
    }
  }

  /// Log authentication event
  Future<void> logAuthEvent({
    required String userId,
    required String eventType, // 'login', 'logout', 'failed_login', 'mfa_challenge', etc.
    String? organizationId,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? details,
  }) async {
    await logSecurityEvent(
      eventType: 'auth_$eventType',
      userId: userId,
      organizationId: organizationId,
      description: 'Authentication event: $eventType',
      metadata: details,
      ipAddress: ipAddress,
      userAgent: userAgent,
      severity: eventType.contains('failed') ? 'warning' : 'info',
    );
  }

  /// Log data access event
  Future<void> logDataAccess({
    required String userId,
    required String resourceType,
    required String resourceId,
    required String action, // 'read', 'write', 'delete'
    String? organizationId,
    Map<String, dynamic>? metadata,
  }) async {
    await logSecurityEvent(
      eventType: 'data_access',
      userId: userId,
      organizationId: organizationId,
      description: 'Data access: $action on $resourceType/$resourceId',
      metadata: {
        'resource_type': resourceType,
        'resource_id': resourceId,
        'action': action,
        ...?metadata,
      },
      severity: action == 'delete' ? 'warning' : 'info',
    );
  }

  /// Log permission change
  Future<void> logPermissionChange({
    required String userId,
    required String targetUserId,
    required String action, // 'grant', 'revoke'
    required String permission,
    String? organizationId,
    Map<String, dynamic>? details,
  }) async {
    await logSecurityEvent(
      eventType: 'permission_change',
      userId: userId,
      organizationId: organizationId,
      description: 'Permission $action: $permission for user $targetUserId',
      metadata: {
        'target_user_id': targetUserId,
        'action': action,
        'permission': permission,
        ...?details,
      },
      severity: 'warning',
    );
  }

  /// Log security policy change
  Future<void> logPolicyChange({
    required String userId,
    required String policyType,
    required String action,
    String? organizationId,
    Map<String, dynamic>? oldPolicy,
    Map<String, dynamic>? newPolicy,
  }) async {
    await logSecurityEvent(
      eventType: 'policy_change',
      userId: userId,
      organizationId: organizationId,
      description: 'Security policy $action: $policyType',
      metadata: {
        'policy_type': policyType,
        'action': action,
        'old_policy': oldPolicy,
        'new_policy': newPolicy,
      },
      severity: 'warning',
    );
  }

  /// Get security events for analysis
  Future<List<Map<String, dynamic>>> getSecurityEvents({
    String? userId,
    String? organizationId,
    String? eventType,
    DateTime? startDate,
    DateTime? endDate,
    String? severity,
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      final conditions = <String>[];
      final parameters = <String, dynamic>{};

      if (userId != null) {
        conditions.add('user_id = @userId::uuid');
        parameters['userId'] = userId;
      }

      if (organizationId != null) {
        conditions.add('organization_id = @organizationId::uuid');
        parameters['organizationId'] = organizationId;
      }

      if (eventType != null) {
        conditions.add('event_type = @eventType');
        parameters['eventType'] = eventType;
      }

      if (startDate != null) {
        conditions.add('created_at >= @startDate');
        parameters['startDate'] = startDate.toUtc();
      }

      if (endDate != null) {
        conditions.add('created_at <= @endDate');
        parameters['endDate'] = endDate.toUtc();
      }

      if (severity != null) {
        conditions.add('severity = @severity');
        parameters['severity'] = severity;
      }

      final whereClause = conditions.isNotEmpty ? 'WHERE ${conditions.join(' AND ')}' : '';

      final result = await _dbService.execute(
        '''
          SELECT id, event_type, user_id, organization_id, description,
                 metadata, ip_address, user_agent, severity, created_at
          FROM security_audit_logs
          $whereClause
          ORDER BY created_at DESC
          LIMIT @limit OFFSET @offset
        ''',
        parameters: {
          ...parameters,
          'limit': limit,
          'offset': offset,
        },
      );

      return result.map((row) => {
        'id': row[0],
        'event_type': row[1],
        'user_id': row[2],
        'organization_id': row[3],
        'description': row[4],
        'metadata': row[5] != null ? jsonDecode(row[5] as String) : {},
        'ip_address': row[6],
        'user_agent': row[7],
        'severity': row[8],
        'created_at': row[9],
      }).toList();
    } catch (e) {
      print('❌ Failed to get security events: $e');
      return [];
    }
  }

  /// Detect suspicious activity patterns
  Future<List<Map<String, dynamic>>> detectSuspiciousActivity({
    String? userId,
    String? organizationId,
    Duration lookbackPeriod = const Duration(hours: 24),
  }) async {
    final endTime = DateTime.now().toUtc();
    final startTime = endTime.subtract(lookbackPeriod);
    
    final suspiciousPatterns = <Map<String, dynamic>>[];

    try {
      // Check for multiple failed login attempts
      final failedLogins = await _dbService.execute(
        '''
          SELECT user_id, COUNT(*) as attempt_count, 
                 array_agg(DISTINCT ip_address) as ip_addresses
          FROM security_audit_logs
          WHERE event_type = 'auth_failed_login'
            AND created_at >= @startTime
            AND created_at <= @endTime
            ${userId != null ? 'AND user_id = @userId::uuid' : ''}
            ${organizationId != null ? 'AND organization_id = @organizationId::uuid' : ''}
          GROUP BY user_id
          HAVING COUNT(*) >= 5
        ''',
        parameters: {
          'startTime': startTime,
          'endTime': endTime,
          if (userId != null) 'userId': userId,
          if (organizationId != null) 'organizationId': organizationId,
        },
      );

      for (final row in failedLogins) {
        suspiciousPatterns.add({
          'type': 'multiple_failed_logins',
          'user_id': row[0],
          'severity': 'high',
          'details': {
            'attempt_count': row[1],
            'ip_addresses': row[2],
            'time_window': '${lookbackPeriod.inHours} hours',
          },
        });
      }

      // Check for unusual access patterns (multiple IPs for same user)
      final multipleIPs = await _dbService.execute(
        '''
          SELECT user_id, COUNT(DISTINCT ip_address) as ip_count,
                 array_agg(DISTINCT ip_address) as ip_addresses
          FROM security_audit_logs
          WHERE event_type LIKE 'auth_%'
            AND created_at >= @startTime
            AND created_at <= @endTime
            AND ip_address IS NOT NULL
            ${userId != null ? 'AND user_id = @userId::uuid' : ''}
            ${organizationId != null ? 'AND organization_id = @organizationId::uuid' : ''}
          GROUP BY user_id
          HAVING COUNT(DISTINCT ip_address) >= 3
        ''',
        parameters: {
          'startTime': startTime,
          'endTime': endTime,
          if (userId != null) 'userId': userId,
          if (organizationId != null) 'organizationId': organizationId,
        },
      );

      for (final row in multipleIPs) {
        suspiciousPatterns.add({
          'type': 'multiple_ip_access',
          'user_id': row[0],
          'severity': 'medium',
          'details': {
            'ip_count': row[1],
            'ip_addresses': row[2],
            'time_window': '${lookbackPeriod.inHours} hours',
          },
        });
      }

      return suspiciousPatterns;
    } catch (e) {
      print('❌ Failed to detect suspicious activity: $e');
      return [];
    }
  }

  /// Generate security report
  Future<Map<String, dynamic>> generateSecurityReport({
    String? organizationId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    startDate ??= DateTime.now().subtract(const Duration(days: 30));
    endDate ??= DateTime.now();

    try {
      final events = await getSecurityEvents(
        organizationId: organizationId,
        startDate: startDate,
        endDate: endDate,
        limit: 10000,
      );

      final eventsByType = <String, int>{};
      final eventsBySeverity = <String, int>{};
      final topUsers = <String, int>{};

      for (final event in events) {
        final eventType = event['event_type'] as String;
        final severity = event['severity'] as String;
        final userId = event['user_id'] as String?;

        eventsByType[eventType] = (eventsByType[eventType] ?? 0) + 1;
        eventsBySeverity[severity] = (eventsBySeverity[severity] ?? 0) + 1;
        
        if (userId != null) {
          topUsers[userId] = (topUsers[userId] ?? 0) + 1;
        }
      }

      final suspiciousActivity = await detectSuspiciousActivity(
        organizationId: organizationId,
        lookbackPeriod: endDate.difference(startDate),
      );

      return {
        'report_period': {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
        },
        'total_events': events.length,
        'events_by_type': eventsByType,
        'events_by_severity': eventsBySeverity,
        'top_users': topUsers.entries
            .toList()
            ..sort((a, b) => b.value.compareTo(a.value))
            ..take(10),
        'suspicious_activity_count': suspiciousActivity.length,
        'suspicious_patterns': suspiciousActivity,
        'generated_at': DateTime.now().toUtc().toIso8601String(),
      };
    } catch (e) {
      print('❌ Failed to generate security report: $e');
      return {
        'error': 'Failed to generate security report',
        'generated_at': DateTime.now().toUtc().toIso8601String(),
      };
    }
  }
}
