import 'package:equatable/equatable.dart';

/// IP access control types
enum IPAccessType {
  whitelist,
  blacklist,
  monitor;

  String get displayName {
    switch (this) {
      case IPAccessType.whitelist:
        return 'Whitelist';
      case IPAccessType.blacklist:
        return 'Blacklist';
      case IPAccessType.monitor:
        return 'Monitor';
    }
  }

  String get description {
    switch (this) {
      case IPAccessType.whitelist:
        return 'Allow access only from this IP/range';
      case IPAccessType.blacklist:
        return 'Block access from this IP/range';
      case IPAccessType.monitor:
        return 'Monitor and track access from this IP/range';
    }
  }
}

/// IP access rule types
enum IPRuleType {
  organization,
  user,
  automatic;

  String get displayName {
    switch (this) {
      case IPRuleType.organization:
        return 'Organization Rule';
      case IPRuleType.user:
        return 'User Rule';
      case IPRuleType.automatic:
        return 'Automatic Rule';
    }
  }
}

/// Geographic location information for IP access control
class IPGeoLocation extends Equatable {
  final String? country;
  final String? countryCode;
  final String? region;
  final String? city;
  final double? latitude;
  final double? longitude;
  final String? timezone;
  final String? isp;
  final String? organization;
  final String? asn;

  const IPGeoLocation({
    this.country,
    this.countryCode,
    this.region,
    this.city,
    this.latitude,
    this.longitude,
    this.timezone,
    this.isp,
    this.organization,
    this.asn,
  });

  factory IPGeoLocation.fromJson(Map<String, dynamic> json) {
    return IPGeoLocation(
      country: json['country'] as String?,
      countryCode: json['country_code'] as String?,
      region: json['region'] as String?,
      city: json['city'] as String?,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      timezone: json['timezone'] as String?,
      isp: json['isp'] as String?,
      organization: json['organization'] as String?,
      asn: json['asn'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'country_code': countryCode,
      'region': region,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'isp': isp,
      'organization': organization,
      'asn': asn,
    };
  }

  String get displayLocation {
    final parts = <String>[];
    if (city != null) parts.add(city!);
    if (region != null) parts.add(region!);
    if (country != null) parts.add(country!);
    return parts.join(', ');
  }

  String get fullDisplayInfo {
    final parts = <String>[];
    if (displayLocation.isNotEmpty) parts.add(displayLocation);
    if (isp != null) parts.add('ISP: $isp');
    if (organization != null) parts.add('Org: $organization');
    return parts.join(' • ');
  }

  /// Create a copy with updated fields
  IPGeoLocation copyWith({
    String? country,
    String? countryCode,
    String? region,
    String? city,
    double? latitude,
    double? longitude,
    String? timezone,
    String? isp,
    String? organization,
    String? asn,
  }) {
    return IPGeoLocation(
      country: country ?? this.country,
      countryCode: countryCode ?? this.countryCode,
      region: region ?? this.region,
      city: city ?? this.city,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timezone: timezone ?? this.timezone,
      isp: isp ?? this.isp,
      organization: organization ?? this.organization,
      asn: asn ?? this.asn,
    );
  }

  @override
  List<Object?> get props => [
        country,
        countryCode,
        region,
        city,
        latitude,
        longitude,
        timezone,
        isp,
        organization,
        asn,
      ];
}

/// IP Access Control model for database table: ip_access_control
class IPAccessControl extends Equatable {
  /// Unique access control identifier
  final String id;

  /// Organization ID
  final String? organizationId;

  /// User ID (for user-specific rules)
  final String? userId;

  /// IP address
  final String ipAddress;

  /// IP range in CIDR notation
  final String? ipRange;

  /// Access type
  final IPAccessType accessType;

  /// Rule type
  final IPRuleType ruleType;

  /// Description of the rule
  final String? description;

  /// Geographic location information
  final IPGeoLocation? geoLocation;

  /// First time this IP was seen
  final DateTime firstSeen;

  /// Last time this IP was seen
  final DateTime lastSeen;

  /// Number of access attempts
  final int accessCount;

  /// Whether this IP is suspicious
  final bool isSuspicious;

  /// Risk score (0-100)
  final int riskScore;

  /// Whether rule is active
  final bool isActive;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Created by user ID
  final String? createdBy;

  const IPAccessControl({
    required this.id,
    this.organizationId,
    this.userId,
    required this.ipAddress,
    this.ipRange,
    required this.accessType,
    required this.ruleType,
    this.description,
    this.geoLocation,
    required this.firstSeen,
    required this.lastSeen,
    required this.accessCount,
    required this.isSuspicious,
    required this.riskScore,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  /// Create empty IPAccessControl for testing
  factory IPAccessControl.empty() {
    final now = DateTime.now();
    return IPAccessControl(
      id: '',
      ipAddress: '',
      accessType: IPAccessType.monitor,
      ruleType: IPRuleType.organization,
      firstSeen: now,
      lastSeen: now,
      accessCount: 0,
      isSuspicious: false,
      riskScore: 0,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Create IPAccessControl from JSON
  factory IPAccessControl.fromJson(Map<String, dynamic> json) {
    return IPAccessControl(
      id: json['id'] as String,
      organizationId: json['organization_id'] as String?,
      userId: json['user_id'] as String?,
      ipAddress: json['ip_address'] as String,
      ipRange: json['ip_range'] as String?,
      accessType: IPAccessType.values.firstWhere(
        (e) => e.name == json['access_type'],
        orElse: () => IPAccessType.monitor,
      ),
      ruleType: IPRuleType.values.firstWhere(
        (e) => e.name == json['rule_type'],
        orElse: () => IPRuleType.organization,
      ),
      description: json['description'] as String?,
      geoLocation: json['geo_location'] != null
          ? IPGeoLocation.fromJson(json['geo_location'] as Map<String, dynamic>)
          : null,
      firstSeen: DateTime.parse(json['first_seen'] as String),
      lastSeen: DateTime.parse(json['last_seen'] as String),
      accessCount: json['access_count'] as int,
      isSuspicious: json['is_suspicious'] as bool,
      riskScore: json['risk_score'] as int,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String?,
    );
  }

  /// Convert IPAccessControl to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organization_id': organizationId,
      'user_id': userId,
      'ip_address': ipAddress,
      'ip_range': ipRange,
      'access_type': accessType.name,
      'rule_type': ruleType.name,
      'description': description,
      'geo_location': geoLocation?.toJson(),
      'first_seen': firstSeen.toIso8601String(),
      'last_seen': lastSeen.toIso8601String(),
      'access_count': accessCount,
      'is_suspicious': isSuspicious,
      'risk_score': riskScore,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  /// Create a copy with updated fields
  IPAccessControl copyWith({
    String? id,
    String? organizationId,
    String? userId,
    String? ipAddress,
    String? ipRange,
    IPAccessType? accessType,
    IPRuleType? ruleType,
    String? description,
    IPGeoLocation? geoLocation,
    DateTime? firstSeen,
    DateTime? lastSeen,
    int? accessCount,
    bool? isSuspicious,
    int? riskScore,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return IPAccessControl(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      userId: userId ?? this.userId,
      ipAddress: ipAddress ?? this.ipAddress,
      ipRange: ipRange ?? this.ipRange,
      accessType: accessType ?? this.accessType,
      ruleType: ruleType ?? this.ruleType,
      description: description ?? this.description,
      geoLocation: geoLocation ?? this.geoLocation,
      firstSeen: firstSeen ?? this.firstSeen,
      lastSeen: lastSeen ?? this.lastSeen,
      accessCount: accessCount ?? this.accessCount,
      isSuspicious: isSuspicious ?? this.isSuspicious,
      riskScore: riskScore ?? this.riskScore,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  /// Check if this is a high-risk IP
  bool get isHighRisk => riskScore >= 70;

  /// Check if this is a medium-risk IP
  bool get isMediumRisk => riskScore >= 40 && riskScore < 70;

  /// Check if this is a low-risk IP
  bool get isLowRisk => riskScore < 40;

  /// Get risk level display
  String get riskLevel {
    if (isHighRisk) return 'High';
    if (isMediumRisk) return 'Medium';
    return 'Low';
  }

  /// Get risk color
  String get riskColor {
    if (isHighRisk) return '#dc3545'; // Red
    if (isMediumRisk) return '#fd7e14'; // Orange
    return '#28a745'; // Green
  }

  /// Check if IP has been active recently
  bool get isRecentlyActive {
    final threshold = DateTime.now().subtract(const Duration(days: 7));
    return lastSeen.isAfter(threshold);
  }

  /// Get activity frequency (accesses per day)
  double get activityFrequency {
    final daysSinceFirst = DateTime.now().difference(firstSeen).inDays;
    if (daysSinceFirst <= 0) return accessCount.toDouble();
    return accessCount / daysSinceFirst;
  }

  /// Get display name for the IP/range
  String get displayName {
    if (ipRange != null && ipRange!.isNotEmpty) {
      return ipRange!;
    }
    return ipAddress;
  }

  /// Get effective IP or range for matching
  String get effectiveAddress => ipRange ?? ipAddress;

  /// Check if this rule applies to a given IP
  bool appliesToIP(String testIP) {
    if (ipRange != null && ipRange!.isNotEmpty) {
      // For CIDR ranges, would need proper CIDR matching logic
      // This is a simplified implementation
      return testIP.startsWith(ipAddress.split('.').take(3).join('.'));
    }
    return testIP == ipAddress;
  }

  /// Get time since last seen
  String get timeSinceLastSeen {
    final difference = DateTime.now().difference(lastSeen);
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Get location display
  String get locationDisplay {
    if (geoLocation != null) {
      return geoLocation!.displayLocation;
    }
    return 'Unknown Location';
  }

  /// Get ISP display
  String get ispDisplay {
    if (geoLocation?.isp != null) {
      return geoLocation!.isp!;
    }
    return 'Unknown ISP';
  }

  /// Check if this is a private IP address
  bool get isPrivateIP {
    return ipAddress.startsWith('10.') ||
           ipAddress.startsWith('172.') ||
           ipAddress.startsWith('192.168.') ||
           ipAddress.startsWith('127.');
  }

  /// Check if this is a known cloud provider IP
  bool get isCloudProvider {
    final cloudProviders = ['amazon', 'google', 'microsoft', 'digitalocean', 'cloudflare'];
    final orgLower = geoLocation?.organization?.toLowerCase() ?? '';
    final ispLower = geoLocation?.isp?.toLowerCase() ?? '';
    
    return cloudProviders.any((provider) => 
        orgLower.contains(provider) || ispLower.contains(provider));
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        userId,
        ipAddress,
        ipRange,
        accessType,
        ruleType,
        description,
        geoLocation,
        firstSeen,
        lastSeen,
        accessCount,
        isSuspicious,
        riskScore,
        isActive,
        createdAt,
        updatedAt,
        createdBy,
      ];

  @override
  bool get stringify => true;

  /// Create commonly used IP access control entries
  static IPAccessControl createWhitelistRule({
    required String organizationId,
    required String ipAddress,
    String? ipRange,
    required String description,
    String? createdBy,
  }) {
    final now = DateTime.now();
    return IPAccessControl(
      id: '',
      organizationId: organizationId,
      ipAddress: ipAddress,
      ipRange: ipRange,
      accessType: IPAccessType.whitelist,
      ruleType: IPRuleType.organization,
      description: description,
      firstSeen: now,
      lastSeen: now,
      accessCount: 0,
      isSuspicious: false,
      riskScore: 0,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy,
    );
  }

  static IPAccessControl createSuspiciousIP({
    required String organizationId,
    required String ipAddress,
    required int riskScore,
    IPGeoLocation? geoLocation,
    int accessCount = 1,
  }) {
    final now = DateTime.now();
    return IPAccessControl(
      id: '',
      organizationId: organizationId,
      ipAddress: ipAddress,
      accessType: IPAccessType.monitor,
      ruleType: IPRuleType.automatic,
      description: 'Automatically flagged as suspicious',
      geoLocation: geoLocation,
      firstSeen: now,
      lastSeen: now,
      accessCount: accessCount,
      isSuspicious: true,
      riskScore: riskScore,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }
}