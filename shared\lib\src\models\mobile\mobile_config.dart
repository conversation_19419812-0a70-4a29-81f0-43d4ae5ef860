import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'mobile_config.g.dart';

/// Platform types
enum Platform {
  @JsonValue('android')
  android,
  @JsonValue('ios')
  ios,
  @JsonValue('web')
  web,
  @JsonValue('windows')
  windows,
  @JsonValue('macos')
  macos,
  @JsonValue('linux')
  linux,
}

/// Device form factors
enum DeviceFormFactor {
  @JsonValue('phone')
  phone,
  @JsonValue('tablet')
  tablet,
  @JsonValue('desktop')
  desktop,
  @JsonValue('tv')
  tv,
  @JsonValue('watch')
  watch,
}

/// Network connectivity types
enum ConnectivityType {
  @JsonValue('wifi')
  wifi,
  @JsonValue('cellular')
  cellular,
  @JsonValue('ethernet')
  ethernet,
  @JsonValue('offline')
  offline,
  @JsonValue('unknown')
  unknown,
}

/// Performance tiers
enum PerformanceTier {
  @JsonValue('low_end')
  lowEnd,
  @JsonValue('mid_range')
  midRange,
  @JsonValue('high_end')
  highEnd,
  @JsonValue('flagship')
  flagship,
}

/// Theme preferences
enum ThemeMode {
  @JsonValue('light')
  light,
  @JsonValue('dark')
  dark,
  @JsonValue('system')
  system,
  @JsonValue('auto')
  auto,
}

/// Device capabilities
@JsonSerializable()
class DeviceCapabilities extends Equatable {
  /// Platform
  final Platform platform;

  /// Device form factor
  final DeviceFormFactor formFactor;

  /// Screen width in logical pixels
  final double screenWidth;

  /// Screen height in logical pixels
  final double screenHeight;

  /// Device pixel ratio
  final double devicePixelRatio;

  /// Whether device supports touch
  final bool supportsTouch;

  /// Whether device supports haptic feedback
  final bool supportsHaptics;

  /// Whether device has GPS
  final bool hasGPS;

  /// Whether device has camera
  final bool hasCamera;

  /// Whether device has microphone
  final bool hasMicrophone;

  /// Whether device supports biometrics
  final bool supportsBiometrics;

  /// Whether device supports notifications
  final bool supportsNotifications;

  /// Whether device supports background sync
  final bool supportsBackgroundSync;

  /// Available storage in MB
  final int? availableStorageMB;

  /// RAM amount in MB
  final int? ramMB;

  /// CPU core count
  final int? cpuCores;

  /// Performance tier
  final PerformanceTier performanceTier;

  /// Supported web APIs (for web platform)
  final List<String> supportedWebApis;

  const DeviceCapabilities({
    required this.platform,
    required this.formFactor,
    required this.screenWidth,
    required this.screenHeight,
    required this.devicePixelRatio,
    this.supportsTouch = true,
    this.supportsHaptics = false,
    this.hasGPS = false,
    this.hasCamera = false,
    this.hasMicrophone = false,
    this.supportsBiometrics = false,
    this.supportsNotifications = true,
    this.supportsBackgroundSync = false,
    this.availableStorageMB,
    this.ramMB,
    this.cpuCores,
    this.performanceTier = PerformanceTier.midRange,
    this.supportedWebApis = const [],
  });

  /// Create DeviceCapabilities from JSON
  factory DeviceCapabilities.fromJson(Map<String, dynamic> json) => _$DeviceCapabilitiesFromJson(json);

  /// Convert DeviceCapabilities to JSON
  Map<String, dynamic> toJson() => _$DeviceCapabilitiesToJson(this);

  /// Check if device is mobile
  bool get isMobile => formFactor == DeviceFormFactor.phone || formFactor == DeviceFormFactor.tablet;

  /// Check if device is desktop
  bool get isDesktop => formFactor == DeviceFormFactor.desktop;

  /// Check if device is considered low-end
  bool get isLowEndDevice => performanceTier == PerformanceTier.lowEnd;

  /// Get screen size category
  String get screenSizeCategory {
    if (screenWidth < 600) return 'small';
    if (screenWidth < 1024) return 'medium';
    if (screenWidth < 1440) return 'large';
    return 'extra_large';
  }

  @override
  List<Object?> get props => [
        platform,
        formFactor,
        screenWidth,
        screenHeight,
        devicePixelRatio,
        supportsTouch,
        supportsHaptics,
        hasGPS,
        hasCamera,
        hasMicrophone,
        supportsBiometrics,
        supportsNotifications,
        supportsBackgroundSync,
        availableStorageMB,
        ramMB,
        cpuCores,
        performanceTier,
        supportedWebApis,
      ];
}

/// Offline configuration
@JsonSerializable()
class OfflineConfig extends Equatable {
  /// Whether offline mode is enabled
  final bool isEnabled;

  /// Maximum cache size in MB
  final int maxCacheSizeMB;

  /// Cache retention period in days
  final int cacheRetentionDays;

  /// Data to sync when online
  final List<String> syncDataTypes;

  /// Offline storage strategy
  final String storageStrategy;

  /// Maximum offline actions to queue
  final int maxOfflineActions;

  /// Whether to show offline indicator
  final bool showOfflineIndicator;

  /// Offline fallback assets
  final Map<String, String> fallbackAssets;

  const OfflineConfig({
    this.isEnabled = true,
    this.maxCacheSizeMB = 100,
    this.cacheRetentionDays = 7,
    this.syncDataTypes = const ['quests', 'tasks', 'achievements'],
    this.storageStrategy = 'hybrid',
    this.maxOfflineActions = 50,
    this.showOfflineIndicator = true,
    this.fallbackAssets = const {},
  });

  /// Create OfflineConfig from JSON
  factory OfflineConfig.fromJson(Map<String, dynamic> json) => _$OfflineConfigFromJson(json);

  /// Convert OfflineConfig to JSON
  Map<String, dynamic> toJson() => _$OfflineConfigToJson(this);

  @override
  List<Object?> get props => [
        isEnabled,
        maxCacheSizeMB,
        cacheRetentionDays,
        syncDataTypes,
        storageStrategy,
        maxOfflineActions,
        showOfflineIndicator,
        fallbackAssets,
      ];
}

/// Notification settings
@JsonSerializable()
class NotificationConfig extends Equatable {
  /// Whether notifications are enabled
  final bool isEnabled;

  /// Push notification token
  final String? pushToken;

  /// Notification categories to enable
  final List<String> enabledCategories;

  /// Quiet hours start time (24-hour format)
  final String? quietHoursStart;

  /// Quiet hours end time (24-hour format)
  final String? quietHoursEnd;

  /// Whether to show notification badges
  final bool showBadges;

  /// Whether to play sound for notifications
  final bool playSound;

  /// Whether to vibrate for notifications
  final bool vibrate;

  /// Custom notification preferences by type
  final Map<String, Map<String, dynamic>> customPreferences;

  const NotificationConfig({
    this.isEnabled = true,
    this.pushToken,
    this.enabledCategories = const ['achievements', 'tasks', 'quests'],
    this.quietHoursStart,
    this.quietHoursEnd,
    this.showBadges = true,
    this.playSound = true,
    this.vibrate = true,
    this.customPreferences = const {},
  });

  /// Create NotificationConfig from JSON
  factory NotificationConfig.fromJson(Map<String, dynamic> json) => _$NotificationConfigFromJson(json);

  /// Convert NotificationConfig to JSON
  Map<String, dynamic> toJson() => _$NotificationConfigToJson(this);

  /// Check if currently in quiet hours
  bool get isInQuietHours {
    if (quietHoursStart == null || quietHoursEnd == null) return false;
    
    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    return _isTimeBetween(currentTime, quietHoursStart!, quietHoursEnd!);
  }

  bool _isTimeBetween(String current, String start, String end) {
    final currentMinutes = _timeToMinutes(current);
    final startMinutes = _timeToMinutes(start);
    final endMinutes = _timeToMinutes(end);
    
    if (startMinutes <= endMinutes) {
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // Spans midnight
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }

  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  @override
  List<Object?> get props => [
        isEnabled,
        pushToken,
        enabledCategories,
        quietHoursStart,
        quietHoursEnd,
        showBadges,
        playSound,
        vibrate,
        customPreferences,
      ];
}

/// Performance optimization settings
@JsonSerializable()
class PerformanceConfig extends Equatable {
  /// Whether to enable image optimization
  final bool enableImageOptimization;

  /// Image quality (0.0 to 1.0)
  final double imageQuality;

  /// Whether to enable lazy loading
  final bool enableLazyLoading;

  /// Whether to reduce animations on low-end devices
  final bool reduceAnimations;

  /// Frame rate target
  final int targetFrameRate;

  /// Whether to enable hardware acceleration
  final bool enableHardwareAcceleration;

  /// Memory usage limit in MB
  final int memoryLimitMB;

  /// Whether to preload critical data
  final bool preloadCriticalData;

  /// Battery optimization level (0-3)
  final int batteryOptimizationLevel;

  /// Data usage optimization
  final bool optimizeDataUsage;

  const PerformanceConfig({
    this.enableImageOptimization = true,
    this.imageQuality = 0.8,
    this.enableLazyLoading = true,
    this.reduceAnimations = false,
    this.targetFrameRate = 60,
    this.enableHardwareAcceleration = true,
    this.memoryLimitMB = 256,
    this.preloadCriticalData = true,
    this.batteryOptimizationLevel = 1,
    this.optimizeDataUsage = false,
  });

  /// Create PerformanceConfig from JSON
  factory PerformanceConfig.fromJson(Map<String, dynamic> json) => _$PerformanceConfigFromJson(json);

  /// Convert PerformanceConfig to JSON
  Map<String, dynamic> toJson() => _$PerformanceConfigToJson(this);

  @override
  List<Object?> get props => [
        enableImageOptimization,
        imageQuality,
        enableLazyLoading,
        reduceAnimations,
        targetFrameRate,
        enableHardwareAcceleration,
        memoryLimitMB,
        preloadCriticalData,
        batteryOptimizationLevel,
        optimizeDataUsage,
      ];
}

/// Accessibility configuration
@JsonSerializable()
class AccessibilityConfig extends Equatable {
  /// Font scale factor
  final double fontScale;

  /// Whether high contrast is enabled
  final bool enableHighContrast;

  /// Whether to reduce motion
  final bool reduceMotion;

  /// Whether screen reader is enabled
  final bool screenReaderEnabled;

  /// Preferred reading order (ltr, rtl)
  final String readingOrder;

  /// Voice control enabled
  final bool voiceControlEnabled;

  /// Switch control enabled
  final bool switchControlEnabled;

  /// Color blind assistance type
  final String? colorBlindAssistance;

  /// Custom accessibility settings
  final Map<String, dynamic> customSettings;

  const AccessibilityConfig({
    this.fontScale = 1.0,
    this.enableHighContrast = false,
    this.reduceMotion = false,
    this.screenReaderEnabled = false,
    this.readingOrder = 'ltr',
    this.voiceControlEnabled = false,
    this.switchControlEnabled = false,
    this.colorBlindAssistance,
    this.customSettings = const {},
  });

  /// Create AccessibilityConfig from JSON
  factory AccessibilityConfig.fromJson(Map<String, dynamic> json) => _$AccessibilityConfigFromJson(json);

  /// Convert AccessibilityConfig to JSON
  Map<String, dynamic> toJson() => _$AccessibilityConfigToJson(this);

  /// Check if any accessibility features are enabled
  bool get hasAccessibilityFeatures {
    return fontScale != 1.0 ||
           enableHighContrast ||
           reduceMotion ||
           screenReaderEnabled ||
           voiceControlEnabled ||
           switchControlEnabled ||
           colorBlindAssistance != null;
  }

  @override
  List<Object?> get props => [
        fontScale,
        enableHighContrast,
        reduceMotion,
        screenReaderEnabled,
        readingOrder,
        voiceControlEnabled,
        switchControlEnabled,
        colorBlindAssistance,
        customSettings,
      ];
}

/// Comprehensive mobile configuration
@JsonSerializable()
class MobileConfig extends Equatable {
  /// Device capabilities
  final DeviceCapabilities deviceCapabilities;

  /// Offline configuration
  final OfflineConfig offlineConfig;

  /// Notification configuration
  final NotificationConfig notificationConfig;

  /// Performance configuration
  final PerformanceConfig performanceConfig;

  /// Accessibility configuration
  final AccessibilityConfig accessibilityConfig;

  /// Theme mode preference
  final ThemeMode themeMode;

  /// Language/locale preference
  final String locale;

  /// App version
  final String appVersion;

  /// Configuration version
  final String configVersion;

  /// User ID this configuration belongs to
  final String? userId;

  /// Organization ID (if applicable)
  final String? organizationId;

  /// Last updated timestamp
  final DateTime lastUpdated;

  /// Feature flags specific to mobile
  final Map<String, bool> mobileFeatureFlags;

  /// Custom app settings
  final Map<String, dynamic> customSettings;

  const MobileConfig({
    required this.deviceCapabilities,
    this.offlineConfig = const OfflineConfig(),
    this.notificationConfig = const NotificationConfig(),
    this.performanceConfig = const PerformanceConfig(),
    this.accessibilityConfig = const AccessibilityConfig(),
    this.themeMode = ThemeMode.system,
    this.locale = 'en-US',
    required this.appVersion,
    required this.configVersion,
    this.userId,
    this.organizationId,
    required this.lastUpdated,
    this.mobileFeatureFlags = const {},
    this.customSettings = const {},
  });

  /// Create MobileConfig from JSON
  factory MobileConfig.fromJson(Map<String, dynamic> json) => _$MobileConfigFromJson(json);

  /// Convert MobileConfig to JSON
  Map<String, dynamic> toJson() => _$MobileConfigToJson(this);

  /// Create a default mobile configuration
  factory MobileConfig.defaultConfig({
    required Platform platform,
    required DeviceFormFactor formFactor,
    required double screenWidth,
    required double screenHeight,
    required String appVersion,
    String? userId,
    String? organizationId,
  }) {
    return MobileConfig(
      deviceCapabilities: DeviceCapabilities(
        platform: platform,
        formFactor: formFactor,
        screenWidth: screenWidth,
        screenHeight: screenHeight,
        devicePixelRatio: 2.0,
      ),
      appVersion: appVersion,
      configVersion: '1.0.0',
      userId: userId,
      organizationId: organizationId,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create optimized config for low-end devices
  MobileConfig optimizeForLowEndDevice() {
    return MobileConfig(
      deviceCapabilities: deviceCapabilities,
      offlineConfig: offlineConfig.copyWith(
        maxCacheSizeMB: 50,
        maxOfflineActions: 25,
      ),
      notificationConfig: notificationConfig,
      performanceConfig: PerformanceConfig(
        enableImageOptimization: true,
        imageQuality: 0.6,
        enableLazyLoading: true,
        reduceAnimations: true,
        targetFrameRate: 30,
        enableHardwareAcceleration: false,
        memoryLimitMB: 128,
        preloadCriticalData: false,
        batteryOptimizationLevel: 3,
        optimizeDataUsage: true,
      ),
      accessibilityConfig: accessibilityConfig,
      themeMode: themeMode,
      locale: locale,
      appVersion: appVersion,
      configVersion: configVersion,
      userId: userId,
      organizationId: organizationId,
      lastUpdated: DateTime.now(),
      mobileFeatureFlags: {
        ...mobileFeatureFlags,
        'enable_advanced_animations': false,
        'enable_background_sync': false,
        'enable_high_quality_images': false,
      },
      customSettings: customSettings,
    );
  }

  /// Update configuration
  MobileConfig copyWith({
    DeviceCapabilities? deviceCapabilities,
    OfflineConfig? offlineConfig,
    NotificationConfig? notificationConfig,
    PerformanceConfig? performanceConfig,
    AccessibilityConfig? accessibilityConfig,
    ThemeMode? themeMode,
    String? locale,
    String? appVersion,
    String? configVersion,
    String? userId,
    String? organizationId,
    DateTime? lastUpdated,
    Map<String, bool>? mobileFeatureFlags,
    Map<String, dynamic>? customSettings,
  }) {
    return MobileConfig(
      deviceCapabilities: deviceCapabilities ?? this.deviceCapabilities,
      offlineConfig: offlineConfig ?? this.offlineConfig,
      notificationConfig: notificationConfig ?? this.notificationConfig,
      performanceConfig: performanceConfig ?? this.performanceConfig,
      accessibilityConfig: accessibilityConfig ?? this.accessibilityConfig,
      themeMode: themeMode ?? this.themeMode,
      locale: locale ?? this.locale,
      appVersion: appVersion ?? this.appVersion,
      configVersion: configVersion ?? this.configVersion,
      userId: userId ?? this.userId,
      organizationId: organizationId ?? this.organizationId,
      lastUpdated: lastUpdated ?? DateTime.now(),
      mobileFeatureFlags: mobileFeatureFlags ?? this.mobileFeatureFlags,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  @override
  List<Object?> get props => [
        deviceCapabilities,
        offlineConfig,
        notificationConfig,
        performanceConfig,
        accessibilityConfig,
        themeMode,
        locale,
        appVersion,
        configVersion,
        userId,
        organizationId,
        lastUpdated,
        mobileFeatureFlags,
        customSettings,
      ];

  @override
  bool get stringify => true;
}

/// Extension methods for convenience
extension OfflineConfigExtension on OfflineConfig {
  OfflineConfig copyWith({
    bool? isEnabled,
    int? maxCacheSizeMB,
    int? cacheRetentionDays,
    List<String>? syncDataTypes,
    String? storageStrategy,
    int? maxOfflineActions,
    bool? showOfflineIndicator,
    Map<String, String>? fallbackAssets,
  }) {
    return OfflineConfig(
      isEnabled: isEnabled ?? this.isEnabled,
      maxCacheSizeMB: maxCacheSizeMB ?? this.maxCacheSizeMB,
      cacheRetentionDays: cacheRetentionDays ?? this.cacheRetentionDays,
      syncDataTypes: syncDataTypes ?? this.syncDataTypes,
      storageStrategy: storageStrategy ?? this.storageStrategy,
      maxOfflineActions: maxOfflineActions ?? this.maxOfflineActions,
      showOfflineIndicator: showOfflineIndicator ?? this.showOfflineIndicator,
      fallbackAssets: fallbackAssets ?? this.fallbackAssets,
    );
  }
}
