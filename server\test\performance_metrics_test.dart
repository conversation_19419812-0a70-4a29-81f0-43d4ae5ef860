import 'dart:math';
import 'package:test/test.dart';
import 'package:server/services/performance_service.dart';

/// Performance metrics collection tests for Phase 5 completion
/// 
/// Tests cover database query performance, API response times,
/// memory usage monitoring, and concurrent user scenarios
class PerformanceMetricsTest {
  static final Random _random = Random();
  
  /// Generate mock performance data for testing
  static Map<String, dynamic> generateMockMetrics() {
    return {
      'api_response_time': _random.nextDouble() * 100, // 0-100ms
      'database_query_time': _random.nextDouble() * 50, // 0-50ms
      'memory_usage_mb': 100 + _random.nextDouble() * 50, // 100-150MB
      'cpu_usage_percent': _random.nextDouble() * 100, // 0-100%
      'cache_hit_ratio': 0.9 + _random.nextDouble() * 0.1, // 90-100%
      'concurrent_users': _random.nextInt(1500), // 0-1500 users
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }
  
  /// Create mock database query performance data
  static Map<String, dynamic> createMockQueryMetrics(String queryType) {
    return {
      'query_type': queryType,
      'execution_time_ms': _random.nextDouble() * 25, // Target: <25ms
      'rows_affected': _random.nextInt(1000),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }
  
  /// Generate load testing scenario data
  static Map<String, dynamic> generateLoadTestData(int concurrentUsers) {
    return {
      'concurrent_users': concurrentUsers,
      'avg_response_time': 20 + _random.nextDouble() * 30, // 20-50ms
      'max_response_time': 40 + _random.nextDouble() * 60, // 40-100ms
      'error_rate': _random.nextDouble() * 0.01, // 0-1% error rate
      'throughput_rps': concurrentUsers * 2, // 2 requests per second per user
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }
}

void main() {
  group('Performance Metrics Collection Tests', () {
    late PerformanceService performanceService;
    
    setUp(() {
      performanceService = PerformanceService();
    });
    
    group('Database Query Performance Tests', () {
      test('should measure database query performance under 25ms', () async {
        // Arrange
        final queryTypes = [
          'user_points_lookup',
          'leaderboard_query',
          'achievement_check',
          'activity_log_insert'
        ];
        
        for (final queryType in queryTypes) {
          // Act
          final startTime = DateTime.now();
          await performanceService.measureDatabaseQuery(queryType, () async {
            // Simulate database query
            await Future.delayed(Duration(milliseconds: Random().nextInt(20)));
            return {'success': true, 'rows': Random().nextInt(100)};
          });
          final endTime = DateTime.now();
          
          // Assert
          final executionTime = endTime.difference(startTime).inMilliseconds;
          expect(executionTime, lessThan(25), 
              reason: '$queryType should complete under 25ms');
        }
      });
      
      test('should track query execution times with timestamps', () async {
        // Arrange
        final queryType = 'leaderboard_complex_query';
        
        // Act
        final metrics = await performanceService.measureDatabaseQuery(queryType, () async {
          await Future.delayed(Duration(milliseconds: 15));
          return {'leaderboard_data': 'mock_data'};
        });
        
        // Assert
        expect(metrics, containsPair('query_type', queryType));
        expect(metrics, contains('execution_time_ms'));
        expect(metrics, contains('timestamp'));
        expect(metrics['execution_time_ms'], isA<double>());
        expect(metrics['execution_time_ms'], lessThan(25));
      });
      
      test('should handle database query failures gracefully', () async {
        // Arrange
        final queryType = 'failing_query';
        
        // Act & Assert
        expect(
          () => performanceService.measureDatabaseQuery(queryType, () async {
            throw Exception('Database connection failed');
          }),
          throwsA(isA<Exception>())
        );
      });
    });
    
    group('API Response Time Tests', () {
      test('should measure API endpoint response times under 50ms', () async {
        // Arrange
        final endpoints = [
          '/gamification/user/stats',
          '/gamification/leaderboard',
          '/gamification/achievements',
          '/enterprise/analytics',
          '/api/health'
        ];
        
        for (final endpoint in endpoints) {
          // Act
          final responseTime = await performanceService.measureApiResponse(endpoint, () async {
            // Simulate API processing time
            await Future.delayed(Duration(milliseconds: Random().nextInt(45)));
            return {'status': 'success', 'data': {}};
          });
          
          // Assert
          expect(responseTime, lessThan(50.0), 
              reason: '$endpoint should respond under 50ms');
        }
      });
      
      test('should validate response compression effectiveness', () async {
        // Arrange
        final largePayload = List.generate(1000, (i) => 'test_data_$i').join('');
        
        // Act
        final compressionMetrics = await performanceService.measureResponseCompression(
          largePayload
        );
        
        // Assert
        expect(compressionMetrics, contains('original_size'));
        expect(compressionMetrics, contains('compressed_size'));
        expect(compressionMetrics, contains('compression_ratio'));
        expect(compressionMetrics['compression_ratio'], greaterThan(0.5),
            reason: 'Should achieve >50% compression for large payloads');
      });
      
      test('should track HTTP/2 connection multiplexing performance', () async {
        // Arrange
        final concurrentRequests = 10;
        
        // Act
        final multiplexingMetrics = await performanceService.measureHttp2Performance(
          concurrentRequests
        );
        
        // Assert
        expect(multiplexingMetrics, contains('concurrent_requests'));
        expect(multiplexingMetrics, contains('total_time_ms'));
        expect(multiplexingMetrics, contains('avg_time_per_request'));
        expect(multiplexingMetrics['concurrent_requests'], equals(concurrentRequests));
      });
    });
    
    group('Memory Usage and CPU Monitoring Tests', () {
      test('should monitor memory usage within acceptable limits', () async {
        // Act
        final memoryMetrics = await performanceService.measureMemoryUsage();
        
        // Assert
        expect(memoryMetrics, contains('current_memory_mb'));
        expect(memoryMetrics, contains('peak_memory_mb'));
        expect(memoryMetrics, contains('memory_growth_rate'));
        expect(memoryMetrics['current_memory_mb'], lessThan(200),
            reason: 'Memory usage should stay under 200MB for normal operations');
      });
      
      test('should track CPU utilization during load', () async {
        // Arrange
        cpuIntensiveTask() async {
          // Simulate CPU-intensive operation
          for (var i = 0; i < 100000; i++) {
            sqrt(i);
          }
        }
        
        // Act
        final cpuMetrics = await performanceService.measureCpuUsage(cpuIntensiveTask);
        
        // Assert
        expect(cpuMetrics, contains('cpu_usage_percent'));
        expect(cpuMetrics, contains('duration_ms'));
        expect(cpuMetrics['cpu_usage_percent'], isA<double>());
        expect(cpuMetrics['cpu_usage_percent'], lessThan(80),
            reason: 'CPU usage should not exceed 80% for normal operations');
      });
      
      test('should detect memory leaks over time', () async {
        // Arrange
        final measurements = <Map<String, dynamic>>[];
        
        // Act - Take multiple memory measurements
        for (var i = 0; i < 5; i++) {
          await Future.delayed(Duration(milliseconds: 100));
          final measurement = await performanceService.measureMemoryUsage();
          measurements.add(measurement);
        }
        
        // Assert - Memory should not continuously grow
        final memoryGrowth = performanceService.analyzeMemoryGrowth(measurements);
        expect(memoryGrowth['is_leak_detected'], isFalse,
            reason: 'No memory leaks should be detected in normal operations');
      });
    });
    
    group('Concurrent User Load Tests', () {
      test('should handle 1000+ concurrent users', () async {
        // Arrange
        final targetUsers = 1000;
        
        // Act
        final loadMetrics = await performanceService.measureConcurrentUserLoad(targetUsers);
        
        // Assert
        expect(loadMetrics, contains('concurrent_users'));
        expect(loadMetrics, contains('avg_response_time'));
        expect(loadMetrics, contains('max_response_time'));
        expect(loadMetrics, contains('error_rate'));
        expect(loadMetrics, contains('system_stability'));
        
        expect(loadMetrics['concurrent_users'], greaterThanOrEqualTo(targetUsers));
        expect(loadMetrics['avg_response_time'], lessThan(50),
            reason: 'Average response time should stay under 50ms with 1000 users');
        expect(loadMetrics['error_rate'], lessThan(0.01),
            reason: 'Error rate should be under 1% with 1000 concurrent users');
      });
      
      test('should maintain Redis cache hit ratio above 95%', () async {
        // Arrange
        final testRequests = 100;
        
        // Act
        final cacheMetrics = await performanceService.measureCachePerformance(testRequests);
        
        // Assert
        expect(cacheMetrics, contains('total_requests'));
        expect(cacheMetrics, contains('cache_hits'));
        expect(cacheMetrics, contains('cache_misses'));
        expect(cacheMetrics, contains('hit_ratio'));
        
        expect(cacheMetrics['hit_ratio'], greaterThan(0.95),
            reason: 'Cache hit ratio should exceed 95%');
      });
      
      test('should validate WebSocket connection stability under load', () async {
        // Arrange
        final concurrentConnections = 500;
        
        // Act
        final websocketMetrics = await performanceService.measureWebSocketLoad(
          concurrentConnections
        );
        
        // Assert
        expect(websocketMetrics, contains('concurrent_connections'));
        expect(websocketMetrics, contains('connection_success_rate'));
        expect(websocketMetrics, contains('message_latency_ms'));
        expect(websocketMetrics, contains('connection_stability'));
        
        expect(websocketMetrics['connection_success_rate'], greaterThan(0.99),
            reason: 'WebSocket connection success rate should exceed 99%');
        expect(websocketMetrics['message_latency_ms'], lessThan(100),
            reason: 'WebSocket message latency should be under 100ms');
      });
    });
    
    group('Integration Performance Tests', () {
      test('should measure full request cycle performance', () async {
        // Arrange
        final testScenarios = [
          'user_login_and_stats',
          'quest_completion_flow',
          'leaderboard_update_cycle',
          'achievement_unlock_notification'
        ];
        
        for (final scenario in testScenarios) {
          // Act
          final cycleMetrics = await performanceService.measureFullRequestCycle(scenario);
          
          // Assert
          expect(cycleMetrics, contains('scenario'));
          expect(cycleMetrics, contains('total_time_ms'));
          expect(cycleMetrics, contains('database_time_ms'));
          expect(cycleMetrics, contains('cache_time_ms'));
          expect(cycleMetrics, contains('processing_time_ms'));
          
          expect(cycleMetrics['total_time_ms'], lessThan(50),
              reason: '$scenario should complete full cycle under 50ms');
        }
      });
      
      test('should validate system performance under mixed load', () async {
        // Arrange - Simulate mixed operations
        final mixedLoad = {
          'read_operations': 70, // 70% reads
          'write_operations': 20, // 20% writes
          'complex_queries': 10, // 10% complex operations
        };
        
        // Act
        final mixedLoadMetrics = await performanceService.measureMixedLoad(mixedLoad);
        
        // Assert
        expect(mixedLoadMetrics, contains('read_avg_time'));
        expect(mixedLoadMetrics, contains('write_avg_time'));
        expect(mixedLoadMetrics, contains('complex_avg_time'));
        expect(mixedLoadMetrics, contains('overall_performance'));
        
        expect(mixedLoadMetrics['read_avg_time'], lessThan(25));
        expect(mixedLoadMetrics['write_avg_time'], lessThan(40));
        expect(mixedLoadMetrics['complex_avg_time'], lessThan(50));
      });
    });
    
    group('Performance Regression Tests', () {
      test('should detect performance regressions', () async {
        // Arrange
        final baselineMetrics = {
          'api_response_time': 30.0,
          'database_query_time': 15.0,
          'cache_hit_ratio': 0.97,
          'memory_usage_mb': 120.0,
        };
        
        // Act
        final currentMetrics = await performanceService.measureCurrentPerformance();
        final regressionAnalysis = performanceService.detectRegressions(
          baselineMetrics, 
          currentMetrics
        );
        
        // Assert
        expect(regressionAnalysis, contains('has_regression'));
        expect(regressionAnalysis, contains('regression_details'));
        
        if (regressionAnalysis['has_regression'] == true) {
          expect(regressionAnalysis['regression_details'], isNotEmpty,
              reason: 'Should provide details about detected regressions');
        }
      });
    });
  });
  
  group('Performance Alerts and Thresholds', () {
    late PerformanceService performanceService;
    
    setUp(() {
      performanceService = PerformanceService();
    });
    
    test('should trigger alerts for performance degradation', () async {
      // Arrange
      final thresholds = {
        'api_response_time_ms': 50.0,
        'database_query_time_ms': 25.0,
        'cache_hit_ratio': 0.95,
        'memory_usage_mb': 200.0,
        'error_rate': 0.01,
      };
      
      // Act
      await performanceService.configureAlertThresholds(thresholds);
      final alertStatus = await performanceService.checkAlertConditions();
      
      // Assert
      expect(alertStatus, contains('alerts_triggered'));
      expect(alertStatus, contains('performance_status'));
      expect(alertStatus['performance_status'], isIn(['healthy', 'warning', 'critical']));
    });
  });
}