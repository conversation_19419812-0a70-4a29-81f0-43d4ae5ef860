{"roots": ["shared"], "packages": [{"name": "shared", "version": "1.0.0", "dependencies": ["equatable", "intl", "json_annotation", "uuid", "validators"], "devDependencies": ["build_runner", "json_serializable", "lints", "<PERSON><PERSON>", "test"]}, {"name": "<PERSON><PERSON>", "version": "5.5.0", "dependencies": ["analyzer", "build", "code_builder", "collection", "dart_style", "matcher", "meta", "path", "source_gen", "test_api"]}, {"name": "test", "version": "1.26.3", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "lints", "version": "6.0.0", "dependencies": []}, {"name": "json_serializable", "version": "6.11.0", "dependencies": ["analyzer", "async", "build", "build_config", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "build_runner", "version": "2.7.0", "dependencies": ["args", "async", "build", "build_config", "build_daemon", "build_runner_core", "code_builder", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http_multi_server", "io", "logging", "mime", "path", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web_socket_channel"]}, {"name": "intl", "version": "0.19.0", "dependencies": ["clock", "meta", "path"]}, {"name": "validators", "version": "3.0.0", "dependencies": []}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "test_api", "version": "0.7.7", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "source_gen", "version": "3.1.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "meta", "version": "1.17.0", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "dart_style", "version": "3.1.1", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build", "version": "3.0.2", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path"]}, {"name": "analyzer", "version": "7.7.1", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "test_core", "version": "0.6.12", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "coverage", "version": "1.15.0", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service", "yaml"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "source_helper", "version": "1.3.7", "dependencies": ["analyzer", "source_gen"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "build_config", "version": "1.2.0", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse"]}, {"name": "watcher", "version": "1.1.3", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "build_runner_core", "version": "9.3.0", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "built_value", "version": "8.11.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "_fe_analyzer_shared", "version": "85.0.0", "dependencies": ["meta"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "build_resolvers", "version": "3.0.2", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "package_config", "path", "pool", "pub_semver"]}], "configVersion": 1}