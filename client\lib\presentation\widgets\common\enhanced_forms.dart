import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/app_theme.dart';
import 'enhanced_animations.dart';
import 'adaptive_layout.dart';

/// Enhanced text field with gamification elements
class EnhancedTextField extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final void Function(String)? onSubmitted;
  final FocusNode? focusNode;
  final bool showCharacterCount;
  final bool showValidationIcon;
  final EdgeInsets? contentPadding;

  const EnhancedTextField({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.controller,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.focusNode,
    this.showCharacterCount = false,
    this.showValidationIcon = true,
    this.contentPadding,
  });

  @override
  State<EnhancedTextField> createState() => _EnhancedTextFieldState();
}

class _EnhancedTextFieldState extends State<EnhancedTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _focusAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _focusAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused) {
        _animationController.forward();
      } else {
        _animationController.reverse();
        _validateInput();
      }
    });
  }

  void _validateInput() {
    if (widget.validator != null && widget.controller != null) {
      setState(() {
        _validationError = widget.validator!(widget.controller!.text);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasError = widget.errorText != null || _validationError != null;
    final errorText = widget.errorText ?? _validationError;

    return AnimatedBuilder(
      animation: _focusAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.label != null) ...[
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: theme.textTheme.labelMedium!.copyWith(
                  color: hasError
                      ? AppTheme.errorColor
                      : _isFocused
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: _isFocused ? FontWeight.w600 : FontWeight.normal,
                ),
                child: Text(widget.label!),
              ),
              const SizedBox(height: 8),
            ],
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: hasError
                      ? AppTheme.errorColor
                      : _isFocused
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline.withValues(alpha: 0.5),
                  width: _isFocused ? 2.0 : 1.0,
                ),
                boxShadow: _isFocused
                    ? [
                        BoxShadow(
                          color: (hasError ? AppTheme.errorColor : theme.colorScheme.primary)
                              .withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: TextFormField(
                controller: widget.controller,
                focusNode: _focusNode,
                keyboardType: widget.keyboardType,
                textInputAction: widget.textInputAction,
                obscureText: widget.obscureText,
                enabled: widget.enabled,
                readOnly: widget.readOnly,
                maxLines: widget.maxLines,
                maxLength: widget.maxLength,
                inputFormatters: widget.inputFormatters,
                onChanged: (value) {
                  widget.onChanged?.call(value);
                  if (widget.validator != null) {
                    setState(() {
                      _validationError = widget.validator!(value);
                    });
                  }
                },
                onTap: widget.onTap,
                onFieldSubmitted: widget.onSubmitted,
                decoration: InputDecoration(
                  hintText: widget.hint,
                  prefixIcon: widget.prefixIcon != null
                      ? Icon(
                          widget.prefixIcon,
                          color: hasError
                              ? AppTheme.errorColor
                              : _isFocused
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                        )
                      : null,
                  suffixIcon: _buildSuffixIcon(theme, hasError),
                  border: InputBorder.none,
                  contentPadding: widget.contentPadding ??
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  counterText: widget.showCharacterCount ? null : '',
                ),
              ),
            ),
            if (widget.helperText != null || errorText != null) ...[
              const SizedBox(height: 8),
              SlideInAnimation(
                beginOffset: const Offset(0, -10),
                duration: const Duration(milliseconds: 200),
                child: Text(
                  errorText ?? widget.helperText!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: hasError
                        ? AppTheme.errorColor
                        : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget? _buildSuffixIcon(ThemeData theme, bool hasError) {
    if (widget.suffixIcon != null) {
      return widget.suffixIcon;
    }

    if (widget.showValidationIcon && widget.controller != null) {
      final text = widget.controller!.text;
      if (text.isNotEmpty) {
        if (hasError) {
          return Icon(
            Icons.error_outline,
            color: AppTheme.errorColor,
          );
        } else if (_validationError == null) {
          return Icon(
            Icons.check_circle_outline,
            color: AppTheme.successColor,
          );
        }
      }
    }

    return null;
  }
}

/// Enhanced dropdown field with search functionality
class EnhancedDropdownField<T> extends StatefulWidget {
  final String? label;
  final String? hint;
  final T? value;
  final List<DropdownItem<T>> items;
  final void Function(T?)? onChanged;
  final String? Function(T?)? validator;
  final bool enabled;
  final bool searchable;
  final IconData? prefixIcon;
  final EdgeInsets? contentPadding;

  const EnhancedDropdownField({
    super.key,
    this.label,
    this.hint,
    this.value,
    required this.items,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.searchable = false,
    this.prefixIcon,
    this.contentPadding,
  });

  @override
  State<EnhancedDropdownField<T>> createState() => _EnhancedDropdownFieldState<T>();
}

class _EnhancedDropdownFieldState<T> extends State<EnhancedDropdownField<T>> {
  final TextEditingController _searchController = TextEditingController();
  List<DropdownItem<T>> _filteredItems = [];
  final bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterItems(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredItems = widget.items;
      } else {
        _filteredItems = widget.items
            .where((item) =>
                item.label.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedItem = widget.items.firstWhere(
      (item) => item.value == widget.value,
      orElse: () => DropdownItem<T>(value: widget.value, label: ''),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: theme.textTheme.labelMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.5),
            ),
          ),
          child: DropdownButtonFormField<T>(
            value: widget.value,
            items: widget.items.map((item) {
              return DropdownMenuItem<T>(
                value: item.value,
                child: Row(
                  children: [
                    if (item.icon != null) ...[
                      Icon(item.icon, size: 20),
                      const SizedBox(width: 8),
                    ],
                    Expanded(child: Text(item.label)),
                  ],
                ),
              );
            }).toList(),
            onChanged: widget.enabled ? widget.onChanged : null,
            decoration: InputDecoration(
              hintText: widget.hint,
              prefixIcon: widget.prefixIcon != null
                  ? Icon(widget.prefixIcon)
                  : null,
              border: InputBorder.none,
              contentPadding: widget.contentPadding ??
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            validator: widget.validator,
          ),
        ),
      ],
    );
  }
}

/// Dropdown item model
class DropdownItem<T> {
  final T? value;
  final String label;
  final IconData? icon;

  const DropdownItem({
    required this.value,
    required this.label,
    this.icon,
  });
}

/// Enhanced form wrapper with validation and submission handling
class EnhancedForm extends StatefulWidget {
  final Widget child;
  final GlobalKey<FormState>? formKey;
  final void Function()? onSubmit;
  final bool autovalidateMode;
  final EdgeInsets? padding;

  const EnhancedForm({
    super.key,
    required this.child,
    this.formKey,
    this.onSubmit,
    this.autovalidateMode = false,
    this.padding,
  });

  @override
  State<EnhancedForm> createState() => _EnhancedFormState();
}

class _EnhancedFormState extends State<EnhancedForm> {
  late GlobalKey<FormState> _formKey;

  @override
  void initState() {
    super.initState();
    _formKey = widget.formKey ?? GlobalKey<FormState>();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveContainer(
      padding: widget.padding ?? AdaptiveSpacing.padding(context),
      child: Form(
        key: _formKey,
        autovalidateMode: widget.autovalidateMode
            ? AutovalidateMode.onUserInteraction
            : AutovalidateMode.disabled,
        child: widget.child,
      ),
    );
  }

  bool validate() {
    return _formKey.currentState?.validate() ?? false;
  }

  void submit() {
    if (validate()) {
      widget.onSubmit?.call();
    }
  }
}

/// Enhanced submit button with loading state
class EnhancedSubmitButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsets? padding;
  final double? width;

  const EnhancedSubmitButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.width,
  });

  @override
  State<EnhancedSubmitButton> createState() => _EnhancedSubmitButtonState();
}

class _EnhancedSubmitButtonState extends State<EnhancedSubmitButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = widget.isEnabled && !widget.isLoading;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SizedBox(
            width: widget.width,
            child: ElevatedButton(
              onPressed: isEnabled ? () {
                _controller.forward().then((_) {
                  _controller.reverse();
                  widget.onPressed?.call();
                });
              } : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.backgroundColor ?? theme.colorScheme.primary,
                foregroundColor: widget.foregroundColor ?? Colors.white,
                padding: widget.padding ?? const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: widget.isLoading
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.foregroundColor ?? Colors.white,
                        ),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widget.icon != null) ...[
                          Icon(widget.icon, size: 20),
                          const SizedBox(width: 8),
                        ],
                        Text(
                          widget.text,
                          style: theme.textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        );
      },
    );
  }
}
