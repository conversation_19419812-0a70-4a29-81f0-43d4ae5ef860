import 'package:json_annotation/json_annotation.dart';

part 'threat_models.g.dart';

/// Enumeration of threat severity levels
enum ThreatSeverity {
  low,
  medium,
  high,
  critical
}

/// Enumeration of threat types
enum ThreatType {
  bruteForce,
  anomalousLogin,
  suspiciousDeviceAccess,
  dataExfiltration,
  malware,
  phishing,
  other
}

/// Represents a security threat indicator
@JsonSerializable()
class ThreatIndicator {
  final String id;
  final ThreatType type;
  final String description;
  final ThreatSeverity severity;
  final double confidence;
  final DateTime detectedAt;
  final Map<String, dynamic> metadata;

  const ThreatIndicator({
    required this.id,
    required this.type,
    required this.description,
    required this.severity,
    required this.confidence,
    required this.detectedAt,
    this.metadata = const {},
  });

  factory ThreatIndicator.fromJson(Map<String, dynamic> json) =>
      _$ThreatIndicatorFromJson(json);

  Map<String, dynamic> toJson() => _$ThreatIndicatorToJson(this);
}

/// Represents a security threat
@JsonSerializable()
class SecurityThreat {
  final String id;
  final ThreatType type;
  final ThreatSeverity severity;
  final String description;
  final String sourceIp;
  final String? userId;
  final String? organizationId;
  final DateTime detectedAt;
  final List<ThreatIndicator> indicators;
  final Map<String, dynamic> context;
  final double riskScore;
  final bool resolved;
  final DateTime? resolvedAt;

  const SecurityThreat({
    required this.id,
    required this.type,
    required this.severity,
    required this.description,
    required this.sourceIp,
    this.userId,
    this.organizationId,
    required this.detectedAt,
    this.indicators = const [],
    this.context = const {},
    required this.riskScore,
    this.resolved = false,
    this.resolvedAt,
  });

  factory SecurityThreat.fromJson(Map<String, dynamic> json) =>
      _$SecurityThreatFromJson(json);

  Map<String, dynamic> toJson() => _$SecurityThreatToJson(this);
}

/// Threat detection statistics
@JsonSerializable()
class ThreatDetectionStats {
  final int totalThreats;
  final int criticalThreats;
  final int highThreats;
  final int mediumThreats;
  final int lowThreats;
  final int resolvedThreats;
  final double averageRiskScore;
  final DateTime periodStart;
  final DateTime periodEnd;

  const ThreatDetectionStats({
    required this.totalThreats,
    required this.criticalThreats,
    required this.highThreats,
    required this.mediumThreats,
    required this.lowThreats,
    required this.resolvedThreats,
    required this.averageRiskScore,
    required this.periodStart,
    required this.periodEnd,
  });

  factory ThreatDetectionStats.fromJson(Map<String, dynamic> json) =>
      _$ThreatDetectionStatsFromJson(json);

  Map<String, dynamic> toJson() => _$ThreatDetectionStatsToJson(this);
}

/// Threat detection configuration
@JsonSerializable()
class ThreatDetectionConfig {
  final bool enabled;
  final ThreatSeverity minimumSeverityLevel;
  final Duration alertCooldown;
  final int maxAlertsPerHour;
  final List<String> excludedIps;
  final Map<ThreatType, bool> enabledThreatTypes;
  final NotificationSettings notificationSettings;

  const ThreatDetectionConfig({
    this.enabled = true,
    this.minimumSeverityLevel = ThreatSeverity.medium,
    this.alertCooldown = const Duration(minutes: 5),
    this.maxAlertsPerHour = 50,
    this.excludedIps = const [],
    this.enabledThreatTypes = const {},
    this.notificationSettings = const NotificationSettings(),
  });

  factory ThreatDetectionConfig.fromJson(Map<String, dynamic> json) =>
      _$ThreatDetectionConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ThreatDetectionConfigToJson(this);
}

/// Notification settings for security alerts
@JsonSerializable()
class NotificationSettings {
  final bool emailEnabled;
  final bool smsEnabled;
  final bool webhookEnabled;
  final List<String> emailRecipients;
  final List<String> smsRecipients;
  final String? webhookUrl;

  const NotificationSettings({
    this.emailEnabled = false,
    this.smsEnabled = false,
    this.webhookEnabled = false,
    this.emailRecipients = const [],
    this.smsRecipients = const [],
    this.webhookUrl,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSettingsToJson(this);
}

/// Detection rule for threat analysis
@JsonSerializable()
class DetectionRule {
  final String id;
  final String name;
  final String description;
  final ThreatType threatType;
  final ThreatSeverity severity;
  final bool enabled;
  final Map<String, dynamic> conditions;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const DetectionRule({
    required this.id,
    required this.name,
    required this.description,
    required this.threatType,
    required this.severity,
    this.enabled = true,
    this.conditions = const {},
    required this.createdAt,
    this.updatedAt,
  });

  factory DetectionRule.fromJson(Map<String, dynamic> json) =>
      _$DetectionRuleFromJson(json);

  Map<String, dynamic> toJson() => _$DetectionRuleToJson(this);
}
