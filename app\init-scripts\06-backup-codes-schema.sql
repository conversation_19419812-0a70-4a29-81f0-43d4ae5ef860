-- Backup Codes Schema for MFA System
-- Handles generation, validation, and management of backup recovery codes

-- MFA Backup Codes table
CREATE TABLE IF NOT EXISTS mfa_backup_codes (
    id VARCHAR(64) PRIMARY KEY,
    user_id UUID NOT NULL,
    code_hash VARCHAR(64) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    used_at TIMESTAMP WITH TIME ZONE NULL,
    is_used BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    revoked_at TIMESTAMP WITH TIME ZONE NULL,
    revocation_reason TEXT NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    used_ip_address INET NULL,
    used_user_agent TEXT NULL,
    created_by VA<PERSON>HAR(255) NULL,
    
    CONSTRAINT fk_backup_codes_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_backup_codes_code_hash 
        CHECK (LENGTH(code_hash) >= 32),
    
    CONSTRAINT chk_backup_codes_dates 
        CHECK (used_at IS NULL OR used_at >= created_at),
        
    CONSTRAINT chk_backup_codes_revocation 
        CHECK (revoked_at IS NULL OR revoked_at >= created_at),
    
    CONSTRAINT chk_backup_codes_usage 
        CHECK (
            (is_used = false AND used_at IS NULL) OR 
            (is_used = true AND used_at IS NOT NULL)
        )
);

-- Indexes for backup codes
CREATE INDEX IF NOT EXISTS idx_backup_codes_user_id 
    ON mfa_backup_codes(user_id);

CREATE INDEX IF NOT EXISTS idx_backup_codes_user_active 
    ON mfa_backup_codes(user_id, is_active) 
    WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_backup_codes_user_unused 
    ON mfa_backup_codes(user_id, is_used, is_active) 
    WHERE is_used = false AND is_active = true;

CREATE UNIQUE INDEX IF NOT EXISTS idx_backup_codes_user_hash 
    ON mfa_backup_codes(user_id, code_hash) 
    WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_backup_codes_created_at 
    ON mfa_backup_codes(created_at);

CREATE INDEX IF NOT EXISTS idx_backup_codes_cleanup 
    ON mfa_backup_codes(created_at, is_active) 
    WHERE is_active = true;

-- Backup code usage statistics view
CREATE OR REPLACE VIEW backup_codes_stats AS
SELECT 
    user_id,
    COUNT(*) as total_codes,
    COUNT(*) FILTER (WHERE is_active = true) as active_codes,
    COUNT(*) FILTER (WHERE is_used = true) as used_codes,
    COUNT(*) FILTER (WHERE is_active = true AND is_used = false) as available_codes,
    MIN(created_at) as first_generated,
    MAX(created_at) as last_generated,
    MAX(used_at) as last_used
FROM mfa_backup_codes 
GROUP BY user_id;

-- Function to automatically revoke old codes when new ones are generated
CREATE OR REPLACE FUNCTION revoke_old_backup_codes()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if this is a new code generation (not an update)
    IF TG_OP = 'INSERT' THEN
        -- Update any existing active codes for this user to inactive
        -- This ensures only one set of backup codes is active at a time
        UPDATE mfa_backup_codes 
        SET is_active = false, 
            revoked_at = NOW(), 
            revocation_reason = 'New codes generated'
        WHERE user_id = NEW.user_id 
          AND id != NEW.id 
          AND is_active = true
          AND created_at < NEW.created_at;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically revoke old codes
DROP TRIGGER IF EXISTS trigger_revoke_old_backup_codes ON mfa_backup_codes;
CREATE TRIGGER trigger_revoke_old_backup_codes
    AFTER INSERT ON mfa_backup_codes
    FOR EACH ROW
    EXECUTE FUNCTION revoke_old_backup_codes();

-- Function to cleanup expired backup codes
CREATE OR REPLACE FUNCTION cleanup_expired_backup_codes()
RETURNS INTEGER AS $$
DECLARE
    affected_count INTEGER;
BEGIN
    UPDATE mfa_backup_codes 
    SET is_active = false, 
        revoked_at = NOW(), 
        revocation_reason = 'Expired (older than 1 year)'
    WHERE created_at < NOW() - INTERVAL '1 year' 
      AND is_active = true;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    RETURN affected_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user backup code status
CREATE OR REPLACE FUNCTION get_user_backup_code_status(p_user_id UUID)
RETURNS TABLE (
    has_active_codes BOOLEAN,
    total_active_codes INTEGER,
    available_codes INTEGER,
    used_codes INTEGER,
    last_generated TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(stats.active_codes > 0, false) as has_active_codes,
        COALESCE(stats.active_codes, 0) as total_active_codes,
        COALESCE(stats.available_codes, 0) as available_codes,
        COALESCE(stats.used_codes, 0) as used_codes,
        stats.last_generated,
        stats.last_used
    FROM backup_codes_stats stats
    WHERE stats.user_id = p_user_id;
    
    -- If no records found, return default values
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT false, 0, 0, 0, NULL::TIMESTAMP WITH TIME ZONE, NULL::TIMESTAMP WITH TIME ZONE;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Security policies for backup codes (Row Level Security)
ALTER TABLE mfa_backup_codes ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own backup codes
CREATE POLICY backup_codes_user_access ON mfa_backup_codes
    FOR ALL 
    TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

-- Policy: Admins can access all backup codes
CREATE POLICY backup_codes_admin_access ON mfa_backup_codes
    FOR ALL 
    TO admin_users
    USING (true);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON mfa_backup_codes TO authenticated_users;
GRANT ALL ON mfa_backup_codes TO admin_users;
GRANT SELECT ON backup_codes_stats TO authenticated_users;
GRANT ALL ON backup_codes_stats TO admin_users;

-- Insert initial test data for development
DO $$
DECLARE
    test_user_id UUID;
    test_code_id VARCHAR(64);
BEGIN
    -- Only insert test data if in development mode
    IF current_setting('app.environment', true) = 'development' THEN
        -- Get or create a test user
        SELECT id INTO test_user_id FROM users WHERE email = '<EMAIL>' LIMIT 1;
        
        IF test_user_id IS NOT NULL THEN
            -- Generate a test backup code ID
            test_code_id := 'backup_' || extract(epoch from now()) || '_test001';
            
            INSERT INTO mfa_backup_codes (
                id, 
                user_id, 
                code_hash, 
                created_at,
                is_used,
                is_active,
                created_by
            ) VALUES (
                test_code_id,
                test_user_id,
                encode(sha256('TEST1234'::bytea), 'hex'),
                NOW(),
                false,
                true,
                'system_init'
            ) ON CONFLICT (id) DO NOTHING;
            
            RAISE NOTICE 'Inserted test backup code for development';
        END IF;
    END IF;
END $$;

-- Log the completion
DO $$
BEGIN
    RAISE NOTICE 'Backup codes schema initialized successfully';
    RAISE NOTICE 'Tables: mfa_backup_codes';
    RAISE NOTICE 'Views: backup_codes_stats';
    RAISE NOTICE 'Functions: revoke_old_backup_codes, cleanup_expired_backup_codes, get_user_backup_code_status';
    RAISE NOTICE 'Security: Row Level Security enabled with user and admin policies';
END $$;