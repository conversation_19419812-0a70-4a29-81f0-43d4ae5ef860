import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../common/responsive_builder.dart';

/// Comprehensive dashboard widget with customizable layout
class DashboardWidget extends StatefulWidget {
  /// List of dashboard widgets to display
  final List<DashboardWidgetConfig> widgets;
  
  /// Whether the dashboard is in edit mode
  final bool isEditMode;
  
  /// Callback when widget configuration changes
  final Function(List<DashboardWidgetConfig>)? onWidgetsChanged;
  
  /// Callback when edit mode changes
  final Function(bool)? onEditModeChanged;

  const DashboardWidget({
    super.key,
    required this.widgets,
    this.isEditMode = false,
    this.onWidgetsChanged,
    this.onEditModeChanged,
  });

  @override
  State<DashboardWidget> createState() => _DashboardWidgetState();
}

class _DashboardWidgetState extends State<DashboardWidget> {
  late List<DashboardWidgetConfig> _widgets;

  @override
  void initState() {
    super.initState();
    _widgets = List.from(widget.widgets);
  }

  @override
  void didUpdateWidget(DashboardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.widgets != oldWidget.widgets) {
      _widgets = List.from(widget.widgets);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: _buildMobileLayout,
      tablet: _buildTabletLayout,
      desktop: _buildDesktopLayout,
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: _widgets.map((config) => 
          Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: _buildDashboardWidget(context, config),
          ),
        ).toList(),
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: _buildGridLayout(context, 2),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: _buildGridLayout(context, 3),
    );
  }

  Widget _buildGridLayout(BuildContext context, int crossAxisCount) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
        childAspectRatio: _getAspectRatio(crossAxisCount),
      ),
      itemCount: _widgets.length,
      itemBuilder: (context, index) {
        return _buildDashboardWidget(context, _widgets[index]);
      },
    );
  }

  Widget _buildDashboardWidget(BuildContext context, DashboardWidgetConfig config) {
    Widget child;

    switch (config.type) {
      case DashboardWidgetType.statsCard:
        child = StatsCardWidget(config: config);
        break;
      case DashboardWidgetType.chart:
        child = ChartWidget(config: config);
        break;
      case DashboardWidgetType.progressBar:
        child = ProgressBarWidget(config: config);
        break;
      case DashboardWidgetType.activityFeed:
        child = ActivityFeedWidget(config: config);
        break;
      case DashboardWidgetType.quickActions:
        child = QuickActionsWidget(config: config);
        break;
      case DashboardWidgetType.leaderboard:
        child = LeaderboardWidget(config: config);
        break;
      case DashboardWidgetType.calendar:
        child = CalendarWidget(config: config);
        break;
      case DashboardWidgetType.notifications:
        child = NotificationsWidget(config: config);
        break;
    }

    if (widget.isEditMode) {
      return _buildEditableWidget(context, config, child);
    }

    return child;
  }

  Widget _buildEditableWidget(
    BuildContext context,
    DashboardWidgetConfig config,
    Widget child,
  ) {
    return Stack(
      children: [
        child,
        Positioned(
          top: 8,
          right: 8,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _editWidget(config),
                icon: const Icon(Icons.edit),
                iconSize: 20,
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(width: 4),
              IconButton(
                onPressed: () => _removeWidget(config),
                icon: const Icon(Icons.close),
                iconSize: 20,
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  double _getAspectRatio(int crossAxisCount) {
    switch (crossAxisCount) {
      case 2:
        return 1.2;
      case 3:
        return 1.0;
      default:
        return 1.5;
    }
  }

  void _editWidget(DashboardWidgetConfig config) {
    // TODO: Show widget configuration dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Widget editing coming soon!')),
    );
  }

  void _removeWidget(DashboardWidgetConfig config) {
    setState(() {
      _widgets.remove(config);
    });
    widget.onWidgetsChanged?.call(_widgets);
  }
}

/// Stats card widget for displaying key metrics
class StatsCardWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const StatsCardWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Stats';
    final value = data['value'] as String? ?? '0';
    final subtitle = data['subtitle'] as String? ?? '';
    final trend = data['trend'] as double? ?? 0.0;
    final icon = data['icon'] as IconData? ?? Icons.analytics;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            if (subtitle.isNotEmpty) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
            if (trend != 0) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Row(
                children: [
                  Icon(
                    trend > 0 ? Icons.trending_up : Icons.trending_down,
                    color: trend > 0 ? Colors.green : Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${trend > 0 ? '+' : ''}${trend.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: trend > 0 ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Chart widget for displaying data visualizations
class ChartWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const ChartWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Chart';
    final chartType = data['chartType'] as String? ?? 'line';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _getChartIcon(chartType),
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        '$chartType Chart',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getChartIcon(String chartType) {
    switch (chartType.toLowerCase()) {
      case 'bar':
        return Icons.bar_chart;
      case 'pie':
        return Icons.pie_chart;
      case 'line':
        return Icons.show_chart;
      default:
        return Icons.analytics;
    }
  }
}

/// Progress bar widget for showing completion status
class ProgressBarWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const ProgressBarWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Progress';
    final progress = (data['progress'] as num?)?.toDouble() ?? 0.0;
    final subtitle = data['subtitle'] as String? ?? '';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (subtitle.isNotEmpty)
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Activity feed widget for showing recent activities
class ActivityFeedWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const ActivityFeedWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Recent Activity';
    final activities = data['activities'] as List<Map<String, dynamic>>? ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: activities.isEmpty
                  ? Center(
                      child: Text(
                        'No recent activity',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: activities.length,
                      itemBuilder: (context, index) {
                        final activity = activities[index];
                        return ListTile(
                          leading: Icon(
                            activity['icon'] as IconData? ?? Icons.circle,
                            size: 16,
                          ),
                          title: Text(
                            activity['title'] as String? ?? '',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          subtitle: Text(
                            activity['time'] as String? ?? '',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          dense: true,
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Quick actions widget for common tasks
class QuickActionsWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const QuickActionsWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Quick Actions';
    final actions = data['actions'] as List<Map<String, dynamic>>? ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: AppConstants.smallPadding,
                  mainAxisSpacing: AppConstants.smallPadding,
                  childAspectRatio: 2,
                ),
                itemCount: actions.length,
                itemBuilder: (context, index) {
                  final action = actions[index];
                  return ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Handle action
                    },
                    icon: Icon(action['icon'] as IconData? ?? Icons.action),
                    label: Text(action['label'] as String? ?? ''),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Leaderboard widget for showing rankings
class LeaderboardWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const LeaderboardWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Leaderboard';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.leaderboard,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      'Leaderboard data',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Calendar widget for showing upcoming events
class CalendarWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const CalendarWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Calendar';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      'Calendar view',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Notifications widget for showing recent notifications
class NotificationsWidget extends StatelessWidget {
  final DashboardWidgetConfig config;

  const NotificationsWidget({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    final data = config.data as Map<String, dynamic>? ?? {};
    final title = data['title'] as String? ?? 'Notifications';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.notifications,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      'No new notifications',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
