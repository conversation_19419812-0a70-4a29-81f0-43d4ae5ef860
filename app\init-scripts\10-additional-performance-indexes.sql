-- Additional Performance Optimization Indexes for Quester Database
-- These indexes target specific query patterns identified in performance analysis

-- =============================================================================
-- ACTIVITY LOG PERFORMANCE INDEXES
-- =============================================================================

-- Global activity feed optimization (most frequently accessed)
DROP INDEX IF EXISTS idx_activity_log_global_feed;
CREATE INDEX idx_activity_log_global_feed 
ON activity_log (created_at DESC, user_id) 
INCLUDE (activity_type, points_earned, description);

-- User-specific activity history
DROP INDEX IF EXISTS idx_activity_log_user_history;
CREATE INDEX idx_activity_log_user_history 
ON activity_log (user_id, created_at DESC) 
INCLUDE (activity_type, points_earned, description);

-- Activity type filtering
DROP INDEX IF EXISTS idx_activity_log_type_filter;
CREATE INDEX idx_activity_log_type_filter 
ON activity_log (activity_type, created_at DESC) 
WHERE activity_type IS NOT NULL;

-- =============================================================================
-- USER STATISTICS OPTIMIZATION
-- =============================================================================

-- Comprehensive user stats lookup (covers getUserStats query)
DROP INDEX IF EXISTS idx_user_stats_comprehensive;
CREATE INDEX idx_user_stats_comprehensive 
ON users (id, is_active) 
INCLUDE (username, display_name, created_at, updated_at)
WHERE is_active = true;

-- User points with role filtering
DROP INDEX IF EXISTS idx_user_points_role_filter;
CREATE INDEX idx_user_points_role_filter 
ON user_points (user_id, role, total_points DESC) 
INCLUDE (current_level, points_to_next_level, updated_at);

-- Streaks lookup optimization
DROP INDEX IF EXISTS idx_streaks_user_lookup;
CREATE INDEX idx_streaks_user_lookup 
ON streaks (user_id) 
INCLUDE (current_streak, longest_streak, last_activity_date);

-- =============================================================================
-- LEADERBOARD PERFORMANCE INDEXES
-- =============================================================================

-- Leaderboard ranking queries
DROP INDEX IF EXISTS idx_leaderboards_ranking;
CREATE INDEX idx_leaderboards_ranking 
ON leaderboards (leaderboard_type, rank ASC, score DESC) 
INCLUDE (user_id, last_updated);

-- User leaderboard positions lookup
DROP INDEX IF EXISTS idx_leaderboards_user_positions;
CREATE INDEX idx_leaderboards_user_positions 
ON leaderboards (user_id, leaderboard_type) 
INCLUDE (rank, score, last_updated);

-- Top performers query optimization
DROP INDEX IF EXISTS idx_leaderboards_top_performers;
CREATE INDEX idx_leaderboards_top_performers 
ON leaderboards (leaderboard_type, score DESC, rank ASC) 
WHERE rank <= 100;

-- =============================================================================
-- ACHIEVEMENTS PERFORMANCE INDEXES
-- =============================================================================

-- User achievements count (frequently used in getUserStats)
DROP INDEX IF EXISTS idx_user_achievements_count;
CREATE INDEX idx_user_achievements_count 
ON user_achievements (user_id) 
INCLUDE (achievement_id, earned_at);

-- Achievement progress tracking
DROP INDEX IF EXISTS idx_user_achievements_progress;
CREATE INDEX idx_user_achievements_progress 
ON user_achievements (user_id, earned_at DESC) 
INCLUDE (achievement_id, progress_percentage);

-- Recent achievements for activity feed
DROP INDEX IF EXISTS idx_user_achievements_recent;
CREATE INDEX idx_user_achievements_recent 
ON user_achievements (earned_at DESC, user_id) 
WHERE earned_at >= NOW() - INTERVAL '30 days';

-- =============================================================================
-- COLLABORATION AND TEAM PERFORMANCE
-- =============================================================================

-- Team quest participation
DROP INDEX IF EXISTS idx_team_quest_participants;
CREATE INDEX idx_team_quest_participants 
ON team_quest_participants (user_id, joined_at DESC) 
INCLUDE (team_quest_id, role, contribution_points);

-- Active team quests
DROP INDEX IF EXISTS idx_team_quests_active;
CREATE INDEX idx_team_quests_active 
ON team_quests (status, created_at DESC) 
WHERE status IN ('active', 'in_progress');

-- Team messages for real-time features
DROP INDEX IF EXISTS idx_team_messages_realtime;
CREATE INDEX idx_team_messages_realtime 
ON team_messages (team_quest_id, created_at DESC) 
INCLUDE (user_id, message_content, message_type);

-- =============================================================================
-- SECURITY AND AUDIT LOG OPTIMIZATION
-- =============================================================================

-- Recent security events (dashboard queries)
DROP INDEX IF EXISTS idx_security_audit_recent;
CREATE INDEX idx_security_audit_recent 
ON security_audit_logs (created_at DESC, organization_id) 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- High-risk security events
DROP INDEX IF EXISTS idx_security_audit_high_risk;
CREATE INDEX idx_security_audit_high_risk 
ON security_audit_logs (risk_score DESC, created_at DESC) 
WHERE risk_score >= 7;

-- User security activity
DROP INDEX IF EXISTS idx_security_audit_user_activity;
CREATE INDEX idx_security_audit_user_activity 
ON security_audit_logs (user_id, created_at DESC) 
INCLUDE (event_type, event_category, risk_score)
WHERE user_id IS NOT NULL;

-- =============================================================================
-- SESSION MANAGEMENT OPTIMIZATION
-- =============================================================================

-- Active sessions lookup
DROP INDEX IF EXISTS idx_sessions_active_lookup;
CREATE INDEX idx_sessions_active_lookup 
ON user_sessions_enhanced (user_id, is_active, expires_at) 
WHERE is_active = true AND expires_at > NOW();

-- Session cleanup optimization
DROP INDEX IF EXISTS idx_sessions_cleanup;
CREATE INDEX idx_sessions_cleanup 
ON user_sessions_enhanced (expires_at, absolute_expires_at) 
WHERE is_active = true;

-- =============================================================================
-- ANALYTICS AND REPORTING INDEXES
-- =============================================================================

-- User engagement metrics
DROP INDEX IF EXISTS idx_user_engagement_metrics;
CREATE INDEX idx_user_engagement_metrics 
ON activity_log (user_id, created_at DESC, activity_type) 
WHERE created_at >= NOW() - INTERVAL '30 days';

-- Points distribution analysis
DROP INDEX IF EXISTS idx_points_distribution;
CREATE INDEX idx_points_distribution 
ON user_points (total_points DESC, current_level) 
INCLUDE (user_id, role, updated_at);

-- Achievement completion rates
DROP INDEX IF EXISTS idx_achievement_completion_rates;
CREATE INDEX idx_achievement_completion_rates 
ON user_achievements (achievement_id, earned_at) 
INCLUDE (user_id, progress_percentage);

-- =============================================================================
-- PARTIAL INDEXES FOR SPECIFIC CONDITIONS
-- =============================================================================

-- Active users only (most common filter)
DROP INDEX IF EXISTS idx_users_active_only;
CREATE INDEX idx_users_active_only 
ON users (id, username, display_name) 
WHERE is_active = true AND deleted_at IS NULL;

-- Recent activity (last 7 days)
DROP INDEX IF EXISTS idx_activity_recent_week;
CREATE INDEX idx_activity_recent_week 
ON activity_log (created_at DESC, user_id, activity_type) 
WHERE created_at >= NOW() - INTERVAL '7 days';

-- High-value achievements (rare/difficult)
DROP INDEX IF EXISTS idx_achievements_high_value;
CREATE INDEX idx_achievements_high_value 
ON achievements (points_reward DESC, difficulty_level) 
WHERE points_reward >= 100;

-- =============================================================================
-- COVERING INDEXES FOR COMMON QUERY PATTERNS
-- =============================================================================

-- User profile summary (covers most user lookup queries)
DROP INDEX IF EXISTS idx_user_profile_summary;
CREATE INDEX idx_user_profile_summary 
ON users (id) 
INCLUDE (username, display_name, email, avatar_url, created_at, last_login_at)
WHERE is_active = true;

-- Leaderboard summary (covers leaderboard display queries)
DROP INDEX IF EXISTS idx_leaderboard_summary;
CREATE INDEX idx_leaderboard_summary 
ON leaderboards (leaderboard_type, rank) 
INCLUDE (user_id, score, last_updated)
WHERE rank <= 50;

-- =============================================================================
-- MAINTENANCE AND MONITORING
-- =============================================================================

-- Index usage statistics view
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'quester'
ORDER BY idx_scan DESC;

-- Query performance monitoring
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE mean_time > 50 -- queries taking more than 50ms on average
ORDER BY mean_time DESC;

COMMENT ON VIEW index_usage_stats IS 'Monitor index usage to identify unused indexes';
COMMENT ON VIEW slow_queries IS 'Identify slow queries for optimization (requires pg_stat_statements extension)';

-- =============================================================================
-- PERFORMANCE ANALYSIS FUNCTIONS
-- =============================================================================

-- Function to analyze table bloat
CREATE OR REPLACE FUNCTION analyze_table_bloat()
RETURNS TABLE(
    table_name text,
    bloat_ratio numeric,
    wasted_bytes bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.table_name::text,
        ROUND((t.table_size - t.actual_size)::numeric / t.table_size * 100, 2) as bloat_ratio,
        (t.table_size - t.actual_size) as wasted_bytes
    FROM (
        SELECT 
            schemaname||'.'||tablename as table_name,
            pg_total_relation_size(schemaname||'.'||tablename) as table_size,
            pg_relation_size(schemaname||'.'||tablename) as actual_size
        FROM pg_tables 
        WHERE schemaname = 'quester'
    ) t
    WHERE t.table_size > 0
    ORDER BY bloat_ratio DESC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION analyze_table_bloat() IS 'Analyze table bloat to identify tables needing VACUUM FULL';

-- Performance optimization complete
SELECT 'Performance optimization indexes created successfully' as status;
