import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../../models/notifications/notification.dart';
import '../../models/notifications/notification_preferences.dart';
import '../../models/core/pagination.dart';

part 'notification_dtos.g.dart';

/// Request to create a new notification
@JsonSerializable()
class CreateNotificationRequest extends Equatable {
  /// ID of the user who will receive the notification
  final String userId;

  /// Type of notification
  final NotificationType type;

  /// Title of the notification
  final String title;

  /// Body/content of the notification
  final String body;

  /// Optional action text
  final String? actionText;

  /// Optional action URL
  final String? actionUrl;

  /// Priority level
  final NotificationPriority priority;

  /// Delivery methods
  final List<DeliveryMethod> deliveryMethods;

  /// Optional metadata
  final Map<String, dynamic>? metadata;

  /// Optional related entity ID
  final String? relatedEntityId;

  /// Optional related entity type
  final String? relatedEntityType;

  /// Optional icon
  final String? icon;

  /// Optional sound
  final String? sound;

  /// Optional vibration pattern
  final List<int>? vibrationPattern;

  /// Whether to show badge
  final bool showBadge;

  /// Optional channel ID
  final String? channelId;

  /// Optional group ID
  final String? groupId;

  /// Optional expiration time
  final DateTime? expiresAt;

  /// Optional scheduled delivery time
  final DateTime? scheduledFor;

  const CreateNotificationRequest({
    required this.userId,
    required this.type,
    required this.title,
    required this.body,
    this.actionText,
    this.actionUrl,
    required this.priority,
    required this.deliveryMethods,
    this.metadata,
    this.relatedEntityId,
    this.relatedEntityType,
    this.icon,
    this.sound,
    this.vibrationPattern,
    this.showBadge = true,
    this.channelId,
    this.groupId,
    this.expiresAt,
    this.scheduledFor,
  });

  /// Creates CreateNotificationRequest from JSON
  factory CreateNotificationRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateNotificationRequestFromJson(json);

  /// Converts CreateNotificationRequest to JSON
  Map<String, dynamic> toJson() => _$CreateNotificationRequestToJson(this);

  @override
  List<Object?> get props => [
        userId,
        type,
        title,
        body,
        actionText,
        actionUrl,
        priority,
        deliveryMethods,
        metadata,
        relatedEntityId,
        relatedEntityType,
        icon,
        sound,
        vibrationPattern,
        showBadge,
        channelId,
        groupId,
        expiresAt,
        scheduledFor,
      ];
}

/// Request to create notification from template
@JsonSerializable()
class CreateNotificationFromTemplateRequest extends Equatable {
  /// ID of the template to use
  final String templateId;

  /// ID of the user who will receive the notification
  final String userId;

  /// Variables to substitute in the template
  final Map<String, dynamic> variables;

  /// Optional language for localization
  final String? language;

  /// Optional scheduled delivery time
  final DateTime? scheduledFor;

  /// Optional group ID
  final String? groupId;

  /// Override delivery methods from template
  final List<DeliveryMethod>? deliveryMethods;

  const CreateNotificationFromTemplateRequest({
    required this.templateId,
    required this.userId,
    required this.variables,
    this.language,
    this.scheduledFor,
    this.groupId,
    this.deliveryMethods,
  });

  /// Creates CreateNotificationFromTemplateRequest from JSON
  factory CreateNotificationFromTemplateRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateNotificationFromTemplateRequestFromJson(json);

  /// Converts CreateNotificationFromTemplateRequest to JSON
  Map<String, dynamic> toJson() => _$CreateNotificationFromTemplateRequestToJson(this);

  @override
  List<Object?> get props => [
        templateId,
        userId,
        variables,
        language,
        scheduledFor,
        groupId,
        deliveryMethods,
      ];
}

/// Request to update notification preferences
@JsonSerializable()
class UpdateNotificationPreferencesRequest extends Equatable {
  /// Global notification settings
  final bool? enableNotifications;

  /// Enable push notifications
  final bool? enablePushNotifications;

  /// Enable email notifications
  final bool? enableEmailNotifications;

  /// Enable SMS notifications
  final bool? enableSmsNotifications;

  /// Enable in-app notifications
  final bool? enableInAppNotifications;

  /// Quiet hours settings
  final QuietHours? quietHours;

  /// Category-specific preferences
  final Map<NotificationCategory, CategoryPreferences>? categoryPreferences;

  /// Device-specific settings
  final DeviceNotificationSettings? deviceSettings;

  /// Frequency settings
  final NotificationFrequency? frequency;

  /// Language preference
  final String? language;

  /// Timezone
  final String? timezone;

  const UpdateNotificationPreferencesRequest({
    this.enableNotifications,
    this.enablePushNotifications,
    this.enableEmailNotifications,
    this.enableSmsNotifications,
    this.enableInAppNotifications,
    this.quietHours,
    this.categoryPreferences,
    this.deviceSettings,
    this.frequency,
    this.language,
    this.timezone,
  });

  /// Creates UpdateNotificationPreferencesRequest from JSON
  factory UpdateNotificationPreferencesRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateNotificationPreferencesRequestFromJson(json);

  /// Converts UpdateNotificationPreferencesRequest to JSON
  Map<String, dynamic> toJson() => _$UpdateNotificationPreferencesRequestToJson(this);

  @override
  List<Object?> get props => [
        enableNotifications,
        enablePushNotifications,
        enableEmailNotifications,
        enableSmsNotifications,
        enableInAppNotifications,
        quietHours,
        categoryPreferences,
        deviceSettings,
        frequency,
        language,
        timezone,
      ];
}

/// Request to mark notifications as read
@JsonSerializable()
class MarkNotificationsReadRequest extends Equatable {
  /// List of notification IDs to mark as read
  final List<String> notificationIds;

  const MarkNotificationsReadRequest({
    required this.notificationIds,
  });

  /// Creates MarkNotificationsReadRequest from JSON
  factory MarkNotificationsReadRequest.fromJson(Map<String, dynamic> json) =>
      _$MarkNotificationsReadRequestFromJson(json);

  /// Converts MarkNotificationsReadRequest to JSON
  Map<String, dynamic> toJson() => _$MarkNotificationsReadRequestToJson(this);

  @override
  List<Object?> get props => [notificationIds];
}

/// Request to get notifications with filters
@JsonSerializable()
class GetNotificationsRequest extends Equatable {
  /// Filter by notification status
  final NotificationStatus? status;

  /// Filter by notification type
  final NotificationType? type;

  /// Filter by notification category
  final NotificationCategory? category;

  /// Filter by priority
  final NotificationPriority? priority;

  /// Filter by read status
  final bool? isRead;

  /// Filter by date range - start
  final DateTime? startDate;

  /// Filter by date range - end
  final DateTime? endDate;

  /// Page number for pagination
  final int page;

  /// Number of items per page
  final int limit;

  /// Sort field
  final String? sortBy;

  /// Sort direction
  final SortDirection? sortDirection;

  const GetNotificationsRequest({
    this.status,
    this.type,
    this.category,
    this.priority,
    this.isRead,
    this.startDate,
    this.endDate,
    this.page = 1,
    this.limit = 20,
    this.sortBy,
    this.sortDirection,
  });

  /// Creates GetNotificationsRequest from JSON
  factory GetNotificationsRequest.fromJson(Map<String, dynamic> json) =>
      _$GetNotificationsRequestFromJson(json);

  /// Converts GetNotificationsRequest to JSON
  Map<String, dynamic> toJson() => _$GetNotificationsRequestToJson(this);

  @override
  List<Object?> get props => [
        status,
        type,
        category,
        priority,
        isRead,
        startDate,
        endDate,
        page,
        limit,
        sortBy,
        sortDirection,
      ];
}

/// Response containing paginated notifications
@JsonSerializable()
class GetNotificationsResponse extends Equatable {
  /// List of notifications
  final List<Notification> notifications;

  /// Pagination information
  final PaginationInfo pagination;

  /// Unread count
  final int unreadCount;

  const GetNotificationsResponse({
    required this.notifications,
    required this.pagination,
    required this.unreadCount,
  });

  /// Creates GetNotificationsResponse from JSON
  factory GetNotificationsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetNotificationsResponseFromJson(json);

  /// Converts GetNotificationsResponse to JSON
  Map<String, dynamic> toJson() => _$GetNotificationsResponseToJson(this);

  @override
  List<Object?> get props => [notifications, pagination, unreadCount];
}

/// Response for notification statistics
@JsonSerializable()
class NotificationStatsResponse extends Equatable {
  /// Total notifications count
  final int totalCount;

  /// Unread notifications count
  final int unreadCount;

  /// Count by category
  final Map<NotificationCategory, int> countByCategory;

  /// Count by priority
  final Map<NotificationPriority, int> countByPriority;

  /// Count by status
  final Map<NotificationStatus, int> countByStatus;

  /// Recent activity (last 7 days)
  final List<DailyNotificationCount> recentActivity;

  const NotificationStatsResponse({
    required this.totalCount,
    required this.unreadCount,
    required this.countByCategory,
    required this.countByPriority,
    required this.countByStatus,
    required this.recentActivity,
  });

  /// Creates NotificationStatsResponse from JSON
  factory NotificationStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationStatsResponseFromJson(json);

  /// Converts NotificationStatsResponse to JSON
  Map<String, dynamic> toJson() => _$NotificationStatsResponseToJson(this);

  @override
  List<Object?> get props => [
        totalCount,
        unreadCount,
        countByCategory,
        countByPriority,
        countByStatus,
        recentActivity,
      ];
}

/// Daily notification count for statistics
@JsonSerializable()
class DailyNotificationCount extends Equatable {
  /// Date
  final DateTime date;

  /// Number of notifications sent on this date
  final int count;

  /// Number of notifications read on this date
  final int readCount;

  const DailyNotificationCount({
    required this.date,
    required this.count,
    required this.readCount,
  });

  /// Creates DailyNotificationCount from JSON
  factory DailyNotificationCount.fromJson(Map<String, dynamic> json) =>
      _$DailyNotificationCountFromJson(json);

  /// Converts DailyNotificationCount to JSON
  Map<String, dynamic> toJson() => _$DailyNotificationCountToJson(this);

  @override
  List<Object?> get props => [date, count, readCount];
}

/// Request to register device for push notifications
@JsonSerializable()
class RegisterDeviceRequest extends Equatable {
  /// Device push token
  final String pushToken;

  /// Device platform
  final String platform;

  /// App version
  final String appVersion;

  /// Device timezone
  final String timezone;

  /// Device language
  final String language;

  /// Device capabilities
  final DeviceCapabilities capabilities;

  const RegisterDeviceRequest({
    required this.pushToken,
    required this.platform,
    required this.appVersion,
    required this.timezone,
    required this.language,
    required this.capabilities,
  });

  /// Creates RegisterDeviceRequest from JSON
  factory RegisterDeviceRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterDeviceRequestFromJson(json);

  /// Converts RegisterDeviceRequest to JSON
  Map<String, dynamic> toJson() => _$RegisterDeviceRequestToJson(this);

  @override
  List<Object?> get props => [
        pushToken,
        platform,
        appVersion,
        timezone,
        language,
        capabilities,
      ];
}

/// Device capabilities for notifications
@JsonSerializable()
class DeviceCapabilities extends Equatable {
  /// Supports rich notifications
  final bool supportsRichNotifications;

  /// Supports action buttons
  final bool supportsActions;

  /// Maximum number of actions
  final int maxActions;

  /// Supports custom sounds
  final bool supportsCustomSounds;

  /// Supports vibration
  final bool supportsVibration;

  /// Supports notification grouping
  final bool supportsGrouping;

  const DeviceCapabilities({
    this.supportsRichNotifications = false,
    this.supportsActions = false,
    this.maxActions = 2,
    this.supportsCustomSounds = false,
    this.supportsVibration = false,
    this.supportsGrouping = false,
  });

  /// Creates DeviceCapabilities from JSON
  factory DeviceCapabilities.fromJson(Map<String, dynamic> json) =>
      _$DeviceCapabilitiesFromJson(json);

  /// Converts DeviceCapabilities to JSON
  Map<String, dynamic> toJson() => _$DeviceCapabilitiesToJson(this);

  @override
  List<Object?> get props => [
        supportsRichNotifications,
        supportsActions,
        maxActions,
        supportsCustomSounds,
        supportsVibration,
        supportsGrouping,
      ];
}

/// Sort direction for queries
enum SortDirection {
  asc,
  desc,
}
