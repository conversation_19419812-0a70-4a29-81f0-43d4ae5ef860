import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import '../services/database_service.dart';

class BackupCode {
  final String id;
  final String userId;
  final String codeHash;
  final DateTime createdAt;
  final DateTime? usedAt;
  final bool isUsed;
  final String? ipAddress;
  final String? userAgent;

  BackupCode({
    required this.id,
    required this.userId,
    required this.codeHash,
    required this.createdAt,
    this.usedAt,
    this.isUsed = false,
    this.ipAddress,
    this.userAgent,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'code_hash': codeHash,
    'created_at': createdAt.toIso8601String(),
    'used_at': usedAt?.toIso8601String(),
    'is_used': isUsed,
    'ip_address': ipAddress,
    'user_agent': userAgent,
  };

  factory BackupCode.fromJson(Map<String, dynamic> json) => BackupCode(
    id: json['id'],
    userId: json['user_id'],
    codeHash: json['code_hash'],
    createdAt: DateTime.parse(json['created_at']),
    usedAt: json['used_at'] != null ? DateTime.parse(json['used_at']) : null,
    isUsed: json['is_used'] ?? false,
    ipAddress: json['ip_address'],
    userAgent: json['user_agent'],
  );
}

class BackupCodeSet {
  final List<String> codes;
  final DateTime generatedAt;
  final String userId;
  final int remainingCodes;

  BackupCodeSet({
    required this.codes,
    required this.generatedAt,
    required this.userId,
    required this.remainingCodes,
  });

  Map<String, dynamic> toJson() => {
    'codes': codes,
    'generated_at': generatedAt.toIso8601String(),
    'user_id': userId,
    'remaining_codes': remainingCodes,
  };
}

class BackupCodeValidationResult {
  final bool isValid;
  final String? backupCodeId;
  final String? errorMessage;
  final String? warningMessage;
  final Map<String, dynamic> metadata;
  final DateTime validatedAt;

  BackupCodeValidationResult({
    required this.isValid,
    this.backupCodeId,
    this.errorMessage,
    this.warningMessage,
    this.metadata = const {},
    required this.validatedAt,
  });

  Map<String, dynamic> toJson() => {
    'is_valid': isValid,
    'backup_code_id': backupCodeId,
    'error_message': errorMessage,
    'warning_message': warningMessage,
    'metadata': metadata,
    'validated_at': validatedAt.toIso8601String(),
  };
}

class BackupCodeGenerationConfig {
  final int codeCount;
  final int codeLength;
  final bool includeHyphens;
  final String charset;
  final int maxCodesPerUser;
  final Duration codeLifetime;
  final bool revokeExistingCodes;

  const BackupCodeGenerationConfig({
    this.codeCount = 10,
    this.codeLength = 8,
    this.includeHyphens = true,
    this.charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    this.maxCodesPerUser = 10,
    this.codeLifetime = const Duration(days: 365),
    this.revokeExistingCodes = true,
  });

  Map<String, dynamic> toJson() => {
    'code_count': codeCount,
    'code_length': codeLength,
    'include_hyphens': includeHyphens,
    'charset': charset,
    'max_codes_per_user': maxCodesPerUser,
    'code_lifetime_days': codeLifetime.inDays,
    'revoke_existing_codes': revokeExistingCodes,
  };
}

class BackupCodesService {
  final DatabaseService _databaseService;
  final Random _secureRandom;
  static const String _backupCodesTable = 'mfa_backup_codes';

  BackupCodesService(this._databaseService) : _secureRandom = Random.secure();

  Future<BackupCodeSet> generateBackupCodes(
    String userId, {
    BackupCodeGenerationConfig config = const BackupCodeGenerationConfig(),
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      await _validateUserExists(userId);
      
      if (config.revokeExistingCodes) {
        await _revokeExistingCodes(userId, 'New codes generated');
      }

      final existingCount = await _getActiveCodeCount(userId);
      if (existingCount >= config.maxCodesPerUser) {
        throw Exception('Maximum backup codes limit reached. Revoke existing codes first.');
      }

      final codes = <String>[];
      final codeHashes = <String>[];
      final codeRecords = <BackupCode>[];

      for (int i = 0; i < config.codeCount; i++) {
        final code = _generateSecureCode(config);
        final codeHash = _hashCode(code);
        
        codes.add(code);
        codeHashes.add(codeHash);
        
        final backupCode = BackupCode(
          id: _generateId(),
          userId: userId,
          codeHash: codeHash,
          createdAt: DateTime.now(),
          ipAddress: ipAddress,
          userAgent: userAgent,
        );
        
        codeRecords.add(backupCode);
      }

      await _storeCodes(codeRecords);
      
      await _logSecurityEvent(
        userId: userId,
        eventType: 'backup_codes_generated',
        details: {
          'code_count': config.codeCount,
          'ip_address': ipAddress,
          'user_agent': userAgent,
          'revoked_existing': config.revokeExistingCodes,
        },
      );

      return BackupCodeSet(
        codes: codes,
        generatedAt: DateTime.now(),
        userId: userId,
        remainingCodes: codes.length,
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'backup_codes_generation_failed',
        details: {
          'error': e.toString(),
          'ip_address': ipAddress,
          'user_agent': userAgent,
        },
      );
      rethrow;
    }
  }

  Future<BackupCodeValidationResult> validateBackupCode(
    String userId,
    String code, {
    String? ipAddress,
    String? userAgent,
    bool consumeOnUse = true,
  }) async {
    final validatedAt = DateTime.now();
    
    try {
      await _validateUserExists(userId);
      
      if (code.isEmpty || code.length < 6) {
        return BackupCodeValidationResult(
          isValid: false,
          errorMessage: 'Invalid backup code format',
          validatedAt: validatedAt,
        );
      }

      final cleanCode = code.replaceAll('-', '').toUpperCase();
      final codeHash = _hashCode(cleanCode);
      
      final backupCode = await _findActiveCode(userId, codeHash);
      
      if (backupCode == null) {
        await _logSecurityEvent(
          userId: userId,
          eventType: 'backup_code_validation_failed',
          details: {
            'reason': 'code_not_found',
            'ip_address': ipAddress,
            'user_agent': userAgent,
          },
        );
        
        return BackupCodeValidationResult(
          isValid: false,
          errorMessage: 'Invalid backup code',
          validatedAt: validatedAt,
        );
      }

      if (backupCode.isUsed) {
        await _logSecurityEvent(
          userId: userId,
          eventType: 'backup_code_reuse_attempt',
          details: {
            'backup_code_id': backupCode.id,
            'original_use_date': backupCode.usedAt?.toIso8601String(),
            'ip_address': ipAddress,
            'user_agent': userAgent,
          },
        );
        
        return BackupCodeValidationResult(
          isValid: false,
          errorMessage: 'Backup code has already been used',
          validatedAt: validatedAt,
        );
      }

      if (consumeOnUse) {
        await _markCodeAsUsed(backupCode.id, ipAddress, userAgent);
        
        final remainingCodes = await _getActiveCodeCount(userId);
        
        await _logSecurityEvent(
          userId: userId,
          eventType: 'backup_code_used',
          details: {
            'backup_code_id': backupCode.id,
            'remaining_codes': remainingCodes,
            'ip_address': ipAddress,
            'user_agent': userAgent,
          },
        );

        String? warningMessage;
        if (remainingCodes <= 2) {
          warningMessage = 'Warning: Only $remainingCodes backup codes remaining. Generate new codes soon.';
        }

        return BackupCodeValidationResult(
          isValid: true,
          backupCodeId: backupCode.id,
          warningMessage: warningMessage,
          metadata: {
            'remaining_codes': remainingCodes,
            'code_used_at': validatedAt.toIso8601String(),
          },
          validatedAt: validatedAt,
        );
      } else {
        await _logSecurityEvent(
          userId: userId,
          eventType: 'backup_code_validated',
          details: {
            'backup_code_id': backupCode.id,
            'consumed': false,
            'ip_address': ipAddress,
            'user_agent': userAgent,
          },
        );
        
        return BackupCodeValidationResult(
          isValid: true,
          backupCodeId: backupCode.id,
          validatedAt: validatedAt,
        );
      }
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'backup_code_validation_error',
        details: {
          'error': e.toString(),
          'ip_address': ipAddress,
          'user_agent': userAgent,
        },
      );
      
      return BackupCodeValidationResult(
        isValid: false,
        errorMessage: 'Validation failed: ${e.toString()}',
        validatedAt: validatedAt,
      );
    }
  }

  Future<List<BackupCode>> getUserBackupCodes(String userId) async {
    try {
      await _validateUserExists(userId);
      
      final result = await _databaseService.query('''
        SELECT id, user_id, code_hash, created_at, used_at, is_used, ip_address, user_agent
        FROM $_backupCodesTable 
        WHERE user_id = @user_id AND is_active = true
        ORDER BY created_at DESC
      ''', {'user_id': userId});
      
      return result.map((row) => BackupCode.fromJson(row)).toList();
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'backup_codes_list_error',
        details: {'error': e.toString()},
      );
      rethrow;
    }
  }

  Future<int> getActiveCodeCount(String userId) async {
    try {
      await _validateUserExists(userId);
      return await _getActiveCodeCount(userId);
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'backup_codes_count_error',
        details: {'error': e.toString()},
      );
      rethrow;
    }
  }

  Future<void> revokeBackupCodes(
    String userId, {
    List<String>? specificCodes,
    String reason = 'Manual revocation',
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      await _validateUserExists(userId);
      
      if (specificCodes != null && specificCodes.isNotEmpty) {
        final codeHashes = specificCodes.map(_hashCode).toList();
        
        await _databaseService.execute('''
          UPDATE $_backupCodesTable 
          SET is_active = false, revoked_at = NOW(), revocation_reason = @reason
          WHERE user_id = @user_id AND code_hash = ANY(@code_hashes) AND is_active = true
        ''', parameters: {
          'reason': reason,
          'user_id': userId,
          'code_hashes': codeHashes,
        });
        
        await _logSecurityEvent(
          userId: userId,
          eventType: 'backup_codes_specific_revoked',
          details: {
            'revoked_count': specificCodes.length,
            'reason': reason,
            'ip_address': ipAddress,
            'user_agent': userAgent,
          },
        );
      } else {
        await _revokeExistingCodes(userId, reason);
        
        await _logSecurityEvent(
          userId: userId,
          eventType: 'backup_codes_all_revoked',
          details: {
            'reason': reason,
            'ip_address': ipAddress,
            'user_agent': userAgent,
          },
        );
      }
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'backup_codes_revocation_error',
        details: {
          'error': e.toString(),
          'ip_address': ipAddress,
          'user_agent': userAgent,
        },
      );
      rethrow;
    }
  }

  Future<bool> hasValidBackupCodes(String userId) async {
    try {
      final count = await _getActiveCodeCount(userId);
      return count > 0;
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'backup_codes_check_error',
        details: {'error': e.toString()},
      );
      return false;
    }
  }

  Future<void> cleanupExpiredCodes() async {
    try {
      final result = await _databaseService.execute('''
        UPDATE $_backupCodesTable 
        SET is_active = false, revoked_at = NOW(), revocation_reason = 'Expired'
        WHERE created_at < NOW() - INTERVAL '1 year' AND is_active = true
      ''');
      
      final affectedRows = result.affectedRows;
      if (affectedRows > 0) {
        print('Cleaned up $affectedRows expired backup codes');
      }
    } catch (e) {
      print('Error cleaning up expired backup codes: $e');
    }
  }

  String _generateSecureCode(BackupCodeGenerationConfig config) {
    final codeBuffer = StringBuffer();
    
    for (int i = 0; i < config.codeLength; i++) {
      final randomIndex = _secureRandom.nextInt(config.charset.length);
      codeBuffer.write(config.charset[randomIndex]);
      
      if (config.includeHyphens && i == 3 && config.codeLength == 8) {
        codeBuffer.write('-');
      }
    }
    
    return codeBuffer.toString();
  }

  String _hashCode(String code) {
    final bytes = utf8.encode(code.toUpperCase());
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomBytes = Uint8List(8);
    for (int i = 0; i < 8; i++) {
      randomBytes[i] = _secureRandom.nextInt(256);
    }
    final randomHex = randomBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    return 'backup_${timestamp}_$randomHex';
  }

  Future<void> _validateUserExists(String userId) async {
    final result = await _databaseService.query(
      'SELECT id FROM users WHERE id = @user_id',
      {'user_id': userId},
    );
    
    if (result.isEmpty) {
      throw Exception('User not found: $userId');
    }
  }

  Future<void> _revokeExistingCodes(String userId, String reason) async {
    await _databaseService.execute('''
      UPDATE $_backupCodesTable 
      SET is_active = false, revoked_at = NOW(), revocation_reason = @reason
      WHERE user_id = @user_id AND is_active = true
    ''', parameters: {
      'reason': reason,
      'user_id': userId,
    });
  }

  Future<int> _getActiveCodeCount(String userId) async {
    final result = await _databaseService.query('''
      SELECT COUNT(*) as count 
      FROM $_backupCodesTable 
      WHERE user_id = @user_id AND is_active = true AND is_used = false
    ''', {'user_id': userId});
    
    return result.isNotEmpty ? result.first['count'] as int : 0;
  }

  Future<BackupCode?> _findActiveCode(String userId, String codeHash) async {
    final result = await _databaseService.query('''
      SELECT id, user_id, code_hash, created_at, used_at, is_used, ip_address, user_agent
      FROM $_backupCodesTable 
      WHERE user_id = @user_id AND code_hash = @code_hash AND is_active = true
      LIMIT 1
    ''', {'user_id': userId, 'code_hash': codeHash});
    
    return result.isNotEmpty ? BackupCode.fromJson(result.first) : null;
  }

  Future<void> _storeCodes(List<BackupCode> codes) async {
    for (final code in codes) {
      await _databaseService.execute('''
        INSERT INTO $_backupCodesTable 
        (id, user_id, code_hash, created_at, is_used, is_active, ip_address, user_agent)
        VALUES (@id, @user_id, @code_hash, @created_at, @is_used, @is_active, @ip_address, @user_agent)
      ''', parameters: {
        'id': code.id,
        'user_id': code.userId,
        'code_hash': code.codeHash,
        'created_at': code.createdAt,
        'is_used': code.isUsed,
        'is_active': true,
        'ip_address': code.ipAddress,
        'user_agent': code.userAgent,
      });
    }
  }

  Future<void> _markCodeAsUsed(String codeId, String? ipAddress, String? userAgent) async {
    await _databaseService.execute('''
      UPDATE $_backupCodesTable 
      SET is_used = true, used_at = NOW(), used_ip_address = @ip_address, used_user_agent = @user_agent
      WHERE id = @code_id
    ''', parameters: {
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'code_id': codeId,
    });
  }

  Future<void> _logSecurityEvent({
    required String userId,
    required String eventType,
    required Map<String, dynamic> details,
  }) async {
    try {
      await _databaseService.execute('''
        INSERT INTO security_audit_log (id, user_id, event_type, event_details, created_at)
        VALUES (@id, @user_id, @event_type, @event_details, NOW())
      ''', parameters: {
        'id': _generateId(),
        'user_id': userId,
        'event_type': eventType,
        'event_details': jsonEncode(details),
      });
    } catch (e) {
      print('Warning: Failed to log security event: $e');
    }
  }
}

extension BackupCodesServiceRegistration on DatabaseService {
  BackupCodesService get backupCodes => BackupCodesService(this);
}