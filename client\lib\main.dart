import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/di/dependency_injection.dart';
import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'data/repositories/api_repository.dart';
import 'presentation/blocs/auth/auth_bloc.dart';
import 'presentation/blocs/gamification/gamification_bloc.dart';
import 'presentation/blocs/freelancing/freelancing_bloc.dart';
import 'presentation/blocs/learning/learning_bloc.dart';
import 'presentation/blocs/analytics/analytics_bloc.dart';
import 'presentation/blocs/collaboration/collaboration_bloc.dart';
import 'presentation/blocs/enterprise/enterprise_bloc.dart';
import 'presentation/blocs/quest/quest_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize dependency injection
  await DependencyInjection.init();
  
  runApp(const QuesterApp());
}

class QuesterApp extends StatelessWidget {
  const QuesterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => AuthBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => GamificationBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => FreelancingBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => LearningBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => AnalyticsBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => CollaborationBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => EnterpriseBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => QuestBloc(
            repository: DependencyInjection.instance<ApiRepository>(),
          ),
        ),
      ],
      child: MaterialApp.router(
        title: 'Quester - Gamified Task Management',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        routerConfig: AppRouter.router,
      ),
    );
  }
}