import 'dart:io';

/// Environment configuration for different deployment targets
enum Environment {
  development,
  staging,
  production,
}

/// Environment-specific configuration for Quester platform
class EnvironmentConfig {
  final Environment environment;
  final String apiBaseUrl;
  final String webBaseUrl;
  final String websocketUrl;
  final String databaseUrl;
  final String redisUrl;
  final bool debugMode;
  final bool analyticsEnabled;
  final bool loggingEnabled;
  final String logLevel;
  final Map<String, dynamic> features;
  final Map<String, String> secrets;
  final Map<String, dynamic> gamification;
  final Map<String, int> limits;

  const EnvironmentConfig({
    required this.environment,
    required this.apiBaseUrl,
    required this.webBaseUrl,
    required this.websocketUrl,
    required this.databaseUrl,
    required this.redisUrl,
    required this.debugMode,
    required this.analyticsEnabled,
    required this.loggingEnabled,
    required this.logLevel,
    required this.features,
    required this.secrets,
    required this.gamification,
    required this.limits,
  });

  /// Current environment configuration instance
  static late EnvironmentConfig _current;

  /// Get current environment configuration
  static EnvironmentConfig get current => _current;

  /// Initialize environment configuration
  static void initialize([Environment? env]) {
    final environment = env ?? _getEnvironmentFromSystem();
    _current = _createConfigForEnvironment(environment);
  }

  /// Create configuration for specific environment
  static EnvironmentConfig _createConfigForEnvironment(Environment env) {
    switch (env) {
      case Environment.development:
        return _developmentConfig();
      case Environment.staging:
        return _stagingConfig();
      case Environment.production:
        return _productionConfig();
    }
  }

  /// Development environment configuration
  static EnvironmentConfig _developmentConfig() {
    return EnvironmentConfig(
      environment: Environment.development,
      apiBaseUrl: _getEnvVar('API_BASE_URL', 'http://localhost:8080'),
      webBaseUrl: _getEnvVar('WEB_BASE_URL', 'http://localhost:3000'),
      websocketUrl: _getEnvVar('WEBSOCKET_URL', 'ws://localhost:8080/ws'),
      databaseUrl: _getEnvVar('DATABASE_URL', 'postgresql://localhost:5432/quester_dev'),
      redisUrl: _getEnvVar('REDIS_URL', 'redis://localhost:6379/0'),
      debugMode: true,
      analyticsEnabled: false,
      loggingEnabled: true,
      logLevel: 'debug',
      features: {
        'gamification_enabled': true,
        'real_time_updates': true,
        'offline_mode': false,
        'analytics_tracking': false,
        'push_notifications': false,
        'file_attachments': true,
        'collaboration_features': true,
        'advanced_search': true,
        'bulk_operations': true,
        'custom_themes': true,
        'api_access': true,
        'admin_panel': true,
        'hot_reload': true,
        'debug_tools': true,
      },
      secrets: {
        'jwt_secret': _getEnvVar('JWT_SECRET', 'dev_jwt_secret_key'),
        'encryption_key': _getEnvVar('ENCRYPTION_KEY', 'dev_encryption_key'),
        'api_key': _getEnvVar('API_KEY', 'dev_api_key'),
      },
      gamification: {
        'points_multiplier': 1.0,
        'achievement_unlock_delay_ms': 0,
        'leaderboard_update_frequency_ms': 5000,
        'streak_grace_hours': 6,
        'bonus_points_enabled': true,
      },
      limits: {
        'max_file_size_mb': 50,
        'max_requests_per_minute': 1000,
        'max_concurrent_connections': 100,
        'cache_ttl_seconds': 300,
      },
    );
  }

  /// Staging environment configuration
  static EnvironmentConfig _stagingConfig() {
    return EnvironmentConfig(
      environment: Environment.staging,
      apiBaseUrl: _getEnvVar('API_BASE_URL', 'https://staging-api.quester.app'),
      webBaseUrl: _getEnvVar('WEB_BASE_URL', 'https://staging.quester.app'),
      websocketUrl: _getEnvVar('WEBSOCKET_URL', 'wss://staging-api.quester.app/ws'),
      databaseUrl: _getEnvVar('DATABASE_URL', ''),
      redisUrl: _getEnvVar('REDIS_URL', ''),
      debugMode: false,
      analyticsEnabled: true,
      loggingEnabled: true,
      logLevel: 'info',
      features: {
        'gamification_enabled': true,
        'real_time_updates': true,
        'offline_mode': false,
        'analytics_tracking': true,
        'push_notifications': true,
        'file_attachments': true,
        'collaboration_features': true,
        'advanced_search': true,
        'bulk_operations': true,
        'custom_themes': false,
        'api_access': false,
        'admin_panel': true,
        'hot_reload': false,
        'debug_tools': false,
      },
      secrets: {
        'jwt_secret': _getEnvVar('JWT_SECRET', ''),
        'encryption_key': _getEnvVar('ENCRYPTION_KEY', ''),
        'api_key': _getEnvVar('API_KEY', ''),
      },
      gamification: {
        'points_multiplier': 1.0,
        'achievement_unlock_delay_ms': 1000,
        'leaderboard_update_frequency_ms': 30000,
        'streak_grace_hours': 6,
        'bonus_points_enabled': true,
      },
      limits: {
        'max_file_size_mb': 25,
        'max_requests_per_minute': 500,
        'max_concurrent_connections': 200,
        'cache_ttl_seconds': 600,
      },
    );
  }

  /// Production environment configuration
  static EnvironmentConfig _productionConfig() {
    return EnvironmentConfig(
      environment: Environment.production,
      apiBaseUrl: _getEnvVar('API_BASE_URL', 'https://api.quester.app'),
      webBaseUrl: _getEnvVar('WEB_BASE_URL', 'https://quester.app'),
      websocketUrl: _getEnvVar('WEBSOCKET_URL', 'wss://api.quester.app/ws'),
      databaseUrl: _getEnvVar('DATABASE_URL', ''),
      redisUrl: _getEnvVar('REDIS_URL', ''),
      debugMode: false,
      analyticsEnabled: true,
      loggingEnabled: true,
      logLevel: 'warn',
      features: {
        'gamification_enabled': true,
        'real_time_updates': true,
        'offline_mode': true,
        'analytics_tracking': true,
        'push_notifications': true,
        'file_attachments': true,
        'collaboration_features': true,
        'advanced_search': true,
        'bulk_operations': true,
        'custom_themes': false,
        'api_access': false,
        'admin_panel': false,
        'hot_reload': false,
        'debug_tools': false,
      },
      secrets: {
        'jwt_secret': _getEnvVar('JWT_SECRET', ''),
        'encryption_key': _getEnvVar('ENCRYPTION_KEY', ''),
        'api_key': _getEnvVar('API_KEY', ''),
      },
      gamification: {
        'points_multiplier': 1.0,
        'achievement_unlock_delay_ms': 2000,
        'leaderboard_update_frequency_ms': 60000,
        'streak_grace_hours': 6,
        'bonus_points_enabled': true,
      },
      limits: {
        'max_file_size_mb': 10,
        'max_requests_per_minute': 100,
        'max_concurrent_connections': 1000,
        'cache_ttl_seconds': 1800,
      },
    );
  }

  /// Get environment variable with fallback
  static String _getEnvVar(String key, String defaultValue) {
    return Platform.environment[key] ?? defaultValue;
  }

  /// Determine environment from system environment variables
  static Environment _getEnvironmentFromSystem() {
    final envString = Platform.environment['ENVIRONMENT'] ?? 
                     Platform.environment['ENV'] ?? 
                     'development';
    
    switch (envString.toLowerCase()) {
      case 'prod':
      case 'production':
        return Environment.production;
      case 'stage':
      case 'staging':
        return Environment.staging;
      case 'dev':
      case 'development':
      default:
        return Environment.development;
    }
  }

  /// Check if feature is enabled
  bool isFeatureEnabled(String feature) {
    return features[feature] == true;
  }

  /// Get secret value
  String? getSecret(String key) {
    return secrets[key];
  }

  /// Get gamification setting
  T? getGamificationSetting<T>(String key) {
    return gamification[key] as T?;
  }

  /// Get limit value
  int? getLimit(String key) {
    return limits[key];
  }

  /// Check if running in development
  bool get isDevelopment => environment == Environment.development;

  /// Check if running in staging
  bool get isStaging => environment == Environment.staging;

  /// Check if running in production
  bool get isProduction => environment == Environment.production;

  /// Get environment name as string
  String get environmentName {
    switch (environment) {
      case Environment.development:
        return 'development';
      case Environment.staging:
        return 'staging';
      case Environment.production:
        return 'production';
    }
  }

  /// Create a copy with updated values
  EnvironmentConfig copyWith({
    Environment? environment,
    String? apiBaseUrl,
    String? webBaseUrl,
    String? websocketUrl,
    String? databaseUrl,
    String? redisUrl,
    bool? debugMode,
    bool? analyticsEnabled,
    bool? loggingEnabled,
    String? logLevel,
    Map<String, dynamic>? features,
    Map<String, String>? secrets,
    Map<String, dynamic>? gamification,
    Map<String, int>? limits,
  }) {
    return EnvironmentConfig(
      environment: environment ?? this.environment,
      apiBaseUrl: apiBaseUrl ?? this.apiBaseUrl,
      webBaseUrl: webBaseUrl ?? this.webBaseUrl,
      websocketUrl: websocketUrl ?? this.websocketUrl,
      databaseUrl: databaseUrl ?? this.databaseUrl,
      redisUrl: redisUrl ?? this.redisUrl,
      debugMode: debugMode ?? this.debugMode,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      loggingEnabled: loggingEnabled ?? this.loggingEnabled,
      logLevel: logLevel ?? this.logLevel,
      features: features ?? this.features,
      secrets: secrets ?? this.secrets,
      gamification: gamification ?? this.gamification,
      limits: limits ?? this.limits,
    );
  }

  /// Convert to JSON for debugging
  Map<String, dynamic> toJson() {
    return {
      'environment': environmentName,
      'apiBaseUrl': apiBaseUrl,
      'webBaseUrl': webBaseUrl,
      'websocketUrl': websocketUrl,
      'databaseUrl': _maskUrl(databaseUrl),
      'redisUrl': _maskUrl(redisUrl),
      'debugMode': debugMode,
      'analyticsEnabled': analyticsEnabled,
      'loggingEnabled': loggingEnabled,
      'logLevel': logLevel,
      'features': features,
      'secrets': _maskSecrets(),
      'gamification': gamification,
      'limits': limits,
    };
  }

  /// Mask sensitive information in URLs
  String _maskUrl(String url) {
    if (url.isEmpty) return url;
    try {
      final uri = Uri.parse(url);
      final userInfo = uri.hasAuthority && uri.userInfo.isNotEmpty 
          ? '***:***@' 
          : '';
      return '${uri.scheme}://$userInfo${uri.host}:${uri.port}${uri.path}';
    } catch (e) {
      return '***masked***';
    }
  }

  /// Mask secrets for logging
  Map<String, String> _maskSecrets() {
    return secrets.map((key, value) => MapEntry(key, '***'));
  }

  @override
  String toString() {
    return 'EnvironmentConfig(environment: $environmentName, apiBaseUrl: $apiBaseUrl)';
  }
}

/// Configuration validation utility
class ConfigValidator {
  /// Validate current configuration
  static List<String> validateConfig(EnvironmentConfig config) {
    final errors = <String>[];

    // Required URLs
    if (config.apiBaseUrl.isEmpty) {
      errors.add('API base URL is required');
    }
    if (config.webBaseUrl.isEmpty) {
      errors.add('Web base URL is required');
    }
    if (config.websocketUrl.isEmpty) {
      errors.add('WebSocket URL is required');
    }

    // Production-specific validations
    if (config.isProduction) {
      if (config.databaseUrl.isEmpty) {
        errors.add('Database URL is required for production');
      }
      if (config.redisUrl.isEmpty) {
        errors.add('Redis URL is required for production');
      }
      if (config.getSecret('jwt_secret')?.isEmpty ?? true) {
        errors.add('JWT secret is required for production');
      }
      if (config.debugMode) {
        errors.add('Debug mode should be disabled in production');
      }
    }

    // URL format validation
    if (!_isValidUrl(config.apiBaseUrl)) {
      errors.add('Invalid API base URL format');
    }
    if (!_isValidUrl(config.webBaseUrl)) {
      errors.add('Invalid web base URL format');
    }

    return errors;
  }

  /// Check if URL is valid
  static bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// Validate configuration and throw if invalid
  static void validateConfigOrThrow(EnvironmentConfig config) {
    final errors = validateConfig(config);
    if (errors.isNotEmpty) {
      throw ConfigurationException('Configuration validation failed: ${errors.join(', ')}');
    }
  }
}

/// Configuration exception
class ConfigurationException implements Exception {
  final String message;
  
  const ConfigurationException(this.message);
  
  @override
  String toString() => 'ConfigurationException: $message';
}