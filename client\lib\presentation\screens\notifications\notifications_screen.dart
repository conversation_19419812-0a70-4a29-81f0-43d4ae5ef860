import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/notification/notification_bloc.dart';
import '../../widgets/common/responsive_builder.dart';
import '../../widgets/common/app_bar_widget.dart';
import '../../widgets/notifications/notification_list.dart';

/// Main screen for displaying and managing notifications
class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  NotificationCategory? _selectedCategory;
  NotificationPriority? _selectedPriority;
  bool _showOnlyUnread = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    context.read<NotificationBloc>().add(const LoadNotifications());
    context.read<NotificationBloc>().add(const LoadNotificationStats());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: 'Notifications',
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter notifications',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'mark_all_read',
                child: ListTile(
                  leading: Icon(Icons.mark_email_read),
                  title: Text('Mark All as Read'),
                ),
              ),
              const PopupMenuItem(
                value: 'preferences',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Preferences'),
                ),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: ListTile(
                  leading: Icon(Icons.refresh),
                  title: Text('Refresh'),
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All', icon: Icon(Icons.notifications)),
            Tab(text: 'Unread', icon: Icon(Icons.mark_email_unread)),
            Tab(text: 'Important', icon: Icon(Icons.priority_high)),
          ],
        ),
      ),
      body: ResponsiveBuilder(
        mobile: (context) => _buildMobileLayout(context),
        tablet: (context) => _buildTabletLayout(context),
        desktop: (context) => _buildDesktopLayout(context),
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        NotificationList(
          categoryFilter: _selectedCategory,
          priorityFilter: _selectedPriority,
        ),
        NotificationList(
          categoryFilter: _selectedCategory,
          priorityFilter: _selectedPriority,
          showOnlyUnread: true,
        ),
        NotificationList(
          categoryFilter: _selectedCategory,
          priorityFilter: NotificationPriority.high,
        ),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Column(
            children: [
              _buildNotificationStats(context),
              const Divider(),
              _buildQuickFilters(context),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              NotificationList(
                categoryFilter: _selectedCategory,
                priorityFilter: _selectedPriority,
              ),
              NotificationList(
                categoryFilter: _selectedCategory,
                priorityFilter: _selectedPriority,
                showOnlyUnread: true,
              ),
              NotificationList(
                categoryFilter: _selectedCategory,
                priorityFilter: NotificationPriority.high,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Column(
            children: [
              _buildNotificationStats(context),
              const Divider(),
              _buildQuickFilters(context),
            ],
          ),
        ),
        Expanded(
          flex: 3,
          child: TabBarView(
            controller: _tabController,
            children: [
              NotificationList(
                categoryFilter: _selectedCategory,
                priorityFilter: _selectedPriority,
              ),
              NotificationList(
                categoryFilter: _selectedCategory,
                priorityFilter: _selectedPriority,
                showOnlyUnread: true,
              ),
              NotificationList(
                categoryFilter: _selectedCategory,
                priorityFilter: NotificationPriority.high,
              ),
            ],
          ),
        ),
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildNotificationDetails(context),
        ),
      ],
    );
  }

  Widget _buildNotificationStats(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, state) {
        if (state is NotificationStatsLoaded) {
          return Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Statistics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildStatCard(
                  context,
                  'Total',
                  state.stats.totalCount.toString(),
                  Icons.notifications,
                  Colors.blue,
                ),
                _buildStatCard(
                  context,
                  'Unread',
                  state.stats.unreadCount.toString(),
                  Icons.mark_email_unread,
                  Colors.orange,
                ),
                _buildStatCard(
                  context,
                  'Today',
                  state.stats.recentActivity.isNotEmpty
                      ? state.stats.recentActivity.last.count.toString()
                      : '0',
                  Icons.today,
                  Colors.green,
                ),
              ],
            ),
          );
        }
        return const Padding(
          padding: EdgeInsets.all(AppConstants.defaultPadding),
          child: CircularProgressIndicator(),
        );
      },
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilters(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Filters',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Category Filters
            Text(
              'Categories',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Wrap(
              spacing: AppConstants.smallPadding,
              runSpacing: AppConstants.smallPadding,
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedCategory == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = null;
                    });
                    _applyFilters();
                  },
                ),
                ...NotificationCategory.values.map((category) =>
                  FilterChip(
                    label: Text(_getCategoryName(category)),
                    selected: _selectedCategory == category,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = selected ? category : null;
                      });
                      _applyFilters();
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Priority Filters
            Text(
              'Priority',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Wrap(
              spacing: AppConstants.smallPadding,
              runSpacing: AppConstants.smallPadding,
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedPriority == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedPriority = null;
                    });
                    _applyFilters();
                  },
                ),
                ...NotificationPriority.values.map((priority) =>
                  FilterChip(
                    label: Text(priority.name.toUpperCase()),
                    selected: _selectedPriority == priority,
                    onSelected: (selected) {
                      setState(() {
                        _selectedPriority = selected ? priority : null;
                      });
                      _applyFilters();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationDetails(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Notification Details'),
          SizedBox(height: AppConstants.defaultPadding),
          Text('Select a notification to view details'),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context) {
    // Only show FAB on mobile
    return ResponsiveBuilder(
      mobile: (context) => FloatingActionButton(
        onPressed: () {
          context.read<NotificationBloc>().add(const RefreshNotifications());
        },
        child: const Icon(Icons.refresh),
      ),
      tablet: (context) => null,
      desktop: (context) => null,
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Notifications'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<NotificationCategory?>(
              decoration: const InputDecoration(labelText: 'Category'),
              value: _selectedCategory,
              items: [
                const DropdownMenuItem(value: null, child: Text('All Categories')),
                ...NotificationCategory.values.map((category) =>
                  DropdownMenuItem(
                    value: category,
                    child: Text(_getCategoryName(category)),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            DropdownButtonFormField<NotificationPriority?>(
              decoration: const InputDecoration(labelText: 'Priority'),
              value: _selectedPriority,
              items: [
                const DropdownMenuItem(value: null, child: Text('All Priorities')),
                ...NotificationPriority.values.map((priority) =>
                  DropdownMenuItem(
                    value: priority,
                    child: Text(priority.name.toUpperCase()),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value;
                });
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SwitchListTile(
              title: const Text('Show only unread'),
              value: _showOnlyUnread,
              onChanged: (value) {
                setState(() {
                  _showOnlyUnread = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedCategory = null;
                _selectedPriority = null;
                _showOnlyUnread = false;
              });
              Navigator.of(context).pop();
              _applyFilters();
            },
            child: const Text('Clear'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _applyFilters();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'mark_all_read':
        context.read<NotificationBloc>().add(const MarkAllNotificationsAsRead());
        break;
      case 'preferences':
        Navigator.of(context).pushNamed('/notifications/preferences');
        break;
      case 'refresh':
        context.read<NotificationBloc>().add(const RefreshNotifications());
        context.read<NotificationBloc>().add(const LoadNotificationStats());
        break;
    }
  }

  void _applyFilters() {
    context.read<NotificationBloc>().add(LoadNotifications(
      category: _selectedCategory,
      priority: _selectedPriority,
      isRead: _showOnlyUnread ? false : null,
      isRefresh: true,
    ));
  }

  String _getCategoryName(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.system:
        return 'System';
      case NotificationCategory.quest:
        return 'Quests';
      case NotificationCategory.achievement:
        return 'Achievements';
      case NotificationCategory.social:
        return 'Social';
      case NotificationCategory.reminder:
        return 'Reminders';
      case NotificationCategory.marketing:
        return 'Marketing';
      case NotificationCategory.security:
        return 'Security';
      case NotificationCategory.team:
        return 'Team';
    }
  }
}
