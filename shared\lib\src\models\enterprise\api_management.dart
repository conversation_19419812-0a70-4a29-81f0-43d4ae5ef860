import 'package:equatable/equatable.dart';

/// API key types
enum ApiKeyType {
  read,
  write,
  admin,
  webhook,
  integration,
  temporary,
}

/// API key status
enum ApiKeyStatus {
  active,
  inactive,
  revoked,
  expired,
  suspended,
}

/// Rate limit type
enum RateLimitType {
  perSecond,
  perMinute,
  perHour,
  perDay,
  perMonth,
}

/// Webhook event types
enum WebhookEventType {
  // User events
  userCreated,
  userUpdated,
  userDeleted,
  userLoggedIn,
  userLoggedOut,
  
  // Task events
  taskCreated,
  taskUpdated,
  taskCompleted,
  taskDeleted,
  taskAssigned,
  
  // Organization events
  organizationCreated,
  organizationUpdated,
  organizationDeleted,
  memberAdded,
  memberRemoved,
  
  // Achievement events
  achievementUnlocked,
  levelUp,
  pointsAwarded,
  
  // System events
  systemAlert,
  maintenanceScheduled,
  complianceViolation,
  securityIncident,
}

/// Webhook status
enum WebhookStatus {
  active,
  inactive,
  failed,
  suspended,
}

/// API key model
class A<PERSON><PERSON><PERSON> extends Equatable {
  /// Unique API key identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// API key name/description
  final String name;

  /// API key value (hashed in storage)
  final String keyValue;

  /// API key type
  final ApiKeyType type;

  /// API key status
  final ApiKeyStatus status;

  /// Key creation date
  final DateTime createdAt;

  /// Key expiration date
  final DateTime? expiresAt;

  /// Last used date
  final DateTime? lastUsedAt;

  /// Usage count
  final int usageCount;

  /// Rate limit configuration
  final Map<String, dynamic> rateLimits;

  /// Allowed IP addresses (if restricted)
  final List<String>? allowedIPs;

  /// Allowed domains (if restricted)
  final List<String>? allowedDomains;

  /// Scopes/permissions
  final List<String> scopes;

  /// Created by user ID
  final String createdBy;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  /// Last update date
  final DateTime updatedAt;

  const ApiKey({
    required this.id,
    required this.organizationId,
    required this.name,
    required this.keyValue,
    required this.type,
    required this.status,
    required this.createdAt,
    this.expiresAt,
    this.lastUsedAt,
    required this.usageCount,
    required this.rateLimits,
    this.allowedIPs,
    this.allowedDomains,
    required this.scopes,
    required this.createdBy,
    this.metadata,
    required this.updatedAt,
  });

  /// Create ApiKey from JSON
  factory ApiKey.fromJson(Map<String, dynamic> json) {
    return ApiKey(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      keyValue: json['keyValue'] as String,
      type: ApiKeyType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ApiKeyType.read,
      ),
      status: ApiKeyStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ApiKeyStatus.active,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      lastUsedAt: json['lastUsedAt'] != null
          ? DateTime.parse(json['lastUsedAt'] as String)
          : null,
      usageCount: json['usageCount'] as int,
      rateLimits: Map<String, dynamic>.from(json['rateLimits'] as Map),
      allowedIPs: json['allowedIPs'] != null
          ? List<String>.from(json['allowedIPs'] as List)
          : null,
      allowedDomains: json['allowedDomains'] != null
          ? List<String>.from(json['allowedDomains'] as List)
          : null,
      scopes: List<String>.from(json['scopes'] as List),
      createdBy: json['createdBy'] as String,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert ApiKey to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'name': name,
      'keyValue': keyValue,
      'type': type.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'lastUsedAt': lastUsedAt?.toIso8601String(),
      'usageCount': usageCount,
      'rateLimits': rateLimits,
      'allowedIPs': allowedIPs,
      'allowedDomains': allowedDomains,
      'scopes': scopes,
      'createdBy': createdBy,
      'metadata': metadata,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Check if API key is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if API key is active and usable
  bool get isUsable {
    return status == ApiKeyStatus.active && !isExpired;
  }

  /// Get masked key value for display
  String get maskedKeyValue {
    if (keyValue.length <= 8) return '***';
    return '${keyValue.substring(0, 4)}***${keyValue.substring(keyValue.length - 4)}';
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        keyValue,
        type,
        status,
        createdAt,
        expiresAt,
        lastUsedAt,
        usageCount,
        rateLimits,
        allowedIPs,
        allowedDomains,
        scopes,
        createdBy,
        metadata,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Rate limit configuration
class RateLimit extends Equatable {
  /// Unique rate limit identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// API key ID (if specific to a key)
  final String? apiKeyId;

  /// Rate limit name
  final String name;

  /// Rate limit type
  final RateLimitType type;

  /// Maximum requests allowed
  final int maxRequests;

  /// Time window duration
  final Duration timeWindow;

  /// Rate limit status
  final bool enabled;

  /// Endpoints this rate limit applies to
  final List<String> endpoints;

  /// IP addresses this rate limit applies to
  final List<String>? ipAddresses;

  /// User roles this rate limit applies to
  final List<String>? userRoles;

  /// Rate limit creation date
  final DateTime createdAt;

  /// Last update date
  final DateTime updatedAt;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  const RateLimit({
    required this.id,
    required this.organizationId,
    this.apiKeyId,
    required this.name,
    required this.type,
    required this.maxRequests,
    required this.timeWindow,
    required this.enabled,
    required this.endpoints,
    this.ipAddresses,
    this.userRoles,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  /// Create RateLimit from JSON
  factory RateLimit.fromJson(Map<String, dynamic> json) {
    return RateLimit(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      apiKeyId: json['apiKeyId'] as String?,
      name: json['name'] as String,
      type: RateLimitType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RateLimitType.perMinute,
      ),
      maxRequests: json['maxRequests'] as int,
      timeWindow: Duration(seconds: json['timeWindowSeconds'] as int),
      enabled: json['enabled'] as bool,
      endpoints: List<String>.from(json['endpoints'] as List),
      ipAddresses: json['ipAddresses'] != null
          ? List<String>.from(json['ipAddresses'] as List)
          : null,
      userRoles: json['userRoles'] != null
          ? List<String>.from(json['userRoles'] as List)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
    );
  }

  /// Convert RateLimit to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'apiKeyId': apiKeyId,
      'name': name,
      'type': type.name,
      'maxRequests': maxRequests,
      'timeWindowSeconds': timeWindow.inSeconds,
      'enabled': enabled,
      'endpoints': endpoints,
      'ipAddresses': ipAddresses,
      'userRoles': userRoles,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Get rate limit description
  String get description {
    final windowDescription = _getTimeWindowDescription();
    return '$maxRequests requests $windowDescription';
  }

  String _getTimeWindowDescription() {
    switch (type) {
      case RateLimitType.perSecond:
        return 'per second';
      case RateLimitType.perMinute:
        return 'per minute';
      case RateLimitType.perHour:
        return 'per hour';
      case RateLimitType.perDay:
        return 'per day';
      case RateLimitType.perMonth:
        return 'per month';
    }
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        apiKeyId,
        name,
        type,
        maxRequests,
        timeWindow,
        enabled,
        endpoints,
        ipAddresses,
        userRoles,
        createdAt,
        updatedAt,
        metadata,
      ];

  @override
  bool get stringify => true;
}

/// Webhook configuration
class Webhook extends Equatable {
  /// Unique webhook identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Webhook name
  final String name;

  /// Webhook URL
  final String url;

  /// Webhook status
  final WebhookStatus status;

  /// Events this webhook subscribes to
  final List<WebhookEventType> events;

  /// Webhook secret for signature verification
  final String secret;

  /// HTTP headers to include in requests
  final Map<String, String> headers;

  /// Maximum retry attempts
  final int maxRetries;

  /// Retry delay in seconds
  final int retryDelay;

  /// Timeout in seconds
  final int timeout;

  /// Whether to verify SSL certificates
  final bool verifySSL;

  /// Webhook creation date
  final DateTime createdAt;

  /// Last successful delivery
  final DateTime? lastSuccessAt;

  /// Last failed delivery
  final DateTime? lastFailureAt;

  /// Failure count
  final int failureCount;

  /// Total delivery count
  final int deliveryCount;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  /// Created by user ID
  final String createdBy;

  /// Last update date
  final DateTime updatedAt;

  const Webhook({
    required this.id,
    required this.organizationId,
    required this.name,
    required this.url,
    required this.status,
    required this.events,
    required this.secret,
    required this.headers,
    required this.maxRetries,
    required this.retryDelay,
    required this.timeout,
    required this.verifySSL,
    required this.createdAt,
    this.lastSuccessAt,
    this.lastFailureAt,
    required this.failureCount,
    required this.deliveryCount,
    this.metadata,
    required this.createdBy,
    required this.updatedAt,
  });

  /// Create Webhook from JSON
  factory Webhook.fromJson(Map<String, dynamic> json) {
    return Webhook(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      url: json['url'] as String,
      status: WebhookStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => WebhookStatus.active,
      ),
      events: (json['events'] as List<dynamic>)
          .map((e) => WebhookEventType.values.firstWhere(
                (type) => type.name == e,
                orElse: () => WebhookEventType.systemAlert,
              ))
          .toList(),
      secret: json['secret'] as String,
      headers: Map<String, String>.from(json['headers'] as Map),
      maxRetries: json['maxRetries'] as int,
      retryDelay: json['retryDelay'] as int,
      timeout: json['timeout'] as int,
      verifySSL: json['verifySSL'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastSuccessAt: json['lastSuccessAt'] != null
          ? DateTime.parse(json['lastSuccessAt'] as String)
          : null,
      lastFailureAt: json['lastFailureAt'] != null
          ? DateTime.parse(json['lastFailureAt'] as String)
          : null,
      failureCount: json['failureCount'] as int,
      deliveryCount: json['deliveryCount'] as int,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      createdBy: json['createdBy'] as String,
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert Webhook to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'name': name,
      'url': url,
      'status': status.name,
      'events': events.map((e) => e.name).toList(),
      'secret': secret,
      'headers': headers,
      'maxRetries': maxRetries,
      'retryDelay': retryDelay,
      'timeout': timeout,
      'verifySSL': verifySSL,
      'createdAt': createdAt.toIso8601String(),
      'lastSuccessAt': lastSuccessAt?.toIso8601String(),
      'lastFailureAt': lastFailureAt?.toIso8601String(),
      'failureCount': failureCount,
      'deliveryCount': deliveryCount,
      'metadata': metadata,
      'createdBy': createdBy,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Calculate success rate
  double get successRate {
    if (deliveryCount == 0) return 0.0;
    return ((deliveryCount - failureCount) / deliveryCount) * 100;
  }

  /// Check if webhook is healthy
  bool get isHealthy {
    return status == WebhookStatus.active && successRate >= 95.0;
  }

  /// Get masked secret for display
  String get maskedSecret {
    if (secret.length <= 8) return '***';
    return '${secret.substring(0, 4)}***${secret.substring(secret.length - 4)}';
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        name,
        url,
        status,
        events,
        secret,
        headers,
        maxRetries,
        retryDelay,
        timeout,
        verifySSL,
        createdAt,
        lastSuccessAt,
        lastFailureAt,
        failureCount,
        deliveryCount,
        metadata,
        createdBy,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Webhook delivery log
class WebhookDelivery extends Equatable {
  /// Unique delivery identifier
  final String id;

  /// Webhook ID
  final String webhookId;

  /// Organization ID
  final String organizationId;

  /// Event type that triggered the webhook
  final WebhookEventType eventType;

  /// Event payload
  final Map<String, dynamic> payload;

  /// HTTP response status code
  final int? responseStatusCode;

  /// HTTP response body
  final String? responseBody;

  /// HTTP response headers
  final Map<String, String>? responseHeaders;

  /// Delivery attempt number
  final int attemptNumber;

  /// Delivery timestamp
  final DateTime deliveredAt;

  /// Response time in milliseconds
  final int? responseTimeMs;

  /// Whether delivery was successful
  final bool successful;

  /// Error message (if failed)
  final String? errorMessage;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  const WebhookDelivery({
    required this.id,
    required this.webhookId,
    required this.organizationId,
    required this.eventType,
    required this.payload,
    this.responseStatusCode,
    this.responseBody,
    this.responseHeaders,
    required this.attemptNumber,
    required this.deliveredAt,
    this.responseTimeMs,
    required this.successful,
    this.errorMessage,
    this.metadata,
  });

  /// Create WebhookDelivery from JSON
  factory WebhookDelivery.fromJson(Map<String, dynamic> json) {
    return WebhookDelivery(
      id: json['id'] as String,
      webhookId: json['webhookId'] as String,
      organizationId: json['organizationId'] as String,
      eventType: WebhookEventType.values.firstWhere(
        (e) => e.name == json['eventType'],
        orElse: () => WebhookEventType.systemAlert,
      ),
      payload: Map<String, dynamic>.from(json['payload'] as Map),
      responseStatusCode: json['responseStatusCode'] as int?,
      responseBody: json['responseBody'] as String?,
      responseHeaders: json['responseHeaders'] != null
          ? Map<String, String>.from(json['responseHeaders'] as Map)
          : null,
      attemptNumber: json['attemptNumber'] as int,
      deliveredAt: DateTime.parse(json['deliveredAt'] as String),
      responseTimeMs: json['responseTimeMs'] as int?,
      successful: json['successful'] as bool,
      errorMessage: json['errorMessage'] as String?,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
    );
  }

  /// Convert WebhookDelivery to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'webhookId': webhookId,
      'organizationId': organizationId,
      'eventType': eventType.name,
      'payload': payload,
      'responseStatusCode': responseStatusCode,
      'responseBody': responseBody,
      'responseHeaders': responseHeaders,
      'attemptNumber': attemptNumber,
      'deliveredAt': deliveredAt.toIso8601String(),
      'responseTimeMs': responseTimeMs,
      'successful': successful,
      'errorMessage': errorMessage,
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [
        id,
        webhookId,
        organizationId,
        eventType,
        payload,
        responseStatusCode,
        responseBody,
        responseHeaders,
        attemptNumber,
        deliveredAt,
        responseTimeMs,
        successful,
        errorMessage,
        metadata,
      ];

  @override
  bool get stringify => true;
}
