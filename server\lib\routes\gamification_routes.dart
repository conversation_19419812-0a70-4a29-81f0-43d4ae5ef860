import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../services/gamification_service.dart';
import '../services/websocket_service.dart';

/// Gamification routes handler
class GamificationRoutes {
  static late GamificationService _gamificationService;

  static void initialize(GamificationService gamificationService) {
    _gamificationService = gamificationService;
  }

  static Router createRouter() {
    final router = Router()
      // Health check
      ..get('/health', _healthHandler)
      
      // User points endpoints
      ..get('/user/<userId>/points', _getUserPointsHandler)
      ..post('/user/<userId>/points', _awardPointsHandler)
      
      // User stats and progress
      ..get('/user/<userId>/stats', _getUserStatsHandler)
      ..get('/user/<userId>/progress', _getUserProgressHandler)
      
      // Achievements
      ..get('/user/<userId>/achievements', _getUserAchievementsHandler)
      ..post('/user/<userId>/achievement/check', _checkAchievementsHandler)
      ..get('/achievements', _getAllAchievementsHandler)
      
      // Leaderboard
      ..get('/leaderboard', _getLeaderboardHandler)
      
      // Rewards
      ..get('/rewards', _getAllRewardsHandler)
      ..post('/rewards/<rewardId>/purchase', _purchaseRewardHandler)
      
      // Activity feeds
      ..get('/activity/global', _getGlobalActivityHandler)
      ..get('/activity/user/<userId>', _getUserActivityHandler);

    return router;
  }

  static Response _healthHandler(Request request) {
    return Response.ok(jsonEncode({
      'status': 'operational',
      'timestamp': DateTime.now().toIso8601String(),
      'service': 'gamification',
      'version': '1.0.0',
    }), headers: {'content-type': 'application/json'});
  }

  static Future<Response> _getUserPointsHandler(Request request) async {
    final userId = request.params['userId']!;
    
    try {
      final result = await _gamificationService.getUserPoints(userId);
      if (result == null) {
        return Response.notFound(jsonEncode({'error': 'User not found'}));
      }
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get user points: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _awardPointsHandler(Request request) async {
    final userId = request.params['userId']!;
    
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      final points = data['points'] as int;
      final activityType = data['activity_type'] as String? ?? 'manual';
      final description = data['description'] as String? ?? 'Points awarded';
      
      final result = await _gamificationService.awardPoints(
        userId, 
        points, 
        activityType, 
        description
      );
      
      if (result == null) {
        return Response.internalServerError(
          body: jsonEncode({'error': 'Failed to award points'}),
          headers: {'content-type': 'application/json'},
        );
      }

      // Broadcast user activity
      WebSocketService.broadcastUserActivity({
        'user_id': userId,
        'action': 'points_awarded',
        'points': points,
        'activity_type': activityType,
        'description': description,
        'total_points': result['total_points'],
      });
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'error': 'Invalid request: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserStatsHandler(Request request) async {
    final userId = request.params['userId']!;
    
    try {
      final result = await _gamificationService.getUserStats(userId);
      if (result == null) {
        return Response.notFound(jsonEncode({'error': 'User not found'}));
      }
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get user stats: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserProgressHandler(Request request) async {
    final userId = request.params['userId']!;
    
    try {
      final result = await _gamificationService.getUserProgress(userId);
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get user progress: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserAchievementsHandler(Request request) async {
    final userId = request.params['userId']!;
    
    try {
      final result = await _gamificationService.getUserAchievements(userId);
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get user achievements: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _checkAchievementsHandler(Request request) async {
    final userId = request.params['userId']!;
    
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final result = await _gamificationService.checkAchievementsWithData(userId, data);
      
      // Broadcast achievement unlocks
      final unlockedAchievements = result['achievements_unlocked'] as List;
      for (final achievement in unlockedAchievements) {
        WebSocketService.broadcastAchievementUnlock(userId, achievement);
      }
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'error': 'Invalid request: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getAllAchievementsHandler(Request request) async {
    try {
      final result = await _gamificationService.getAllAchievements();
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get achievements: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getLeaderboardHandler(Request request) async {
    final type = request.url.queryParameters['type'] ?? 'global_points';
    final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
    
    try {
      final result = await _gamificationService.getLeaderboard(type: type, limit: limit);
      
      // Broadcast leaderboard update
      WebSocketService.broadcastLeaderboardUpdate(result);
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get leaderboard: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getAllRewardsHandler(Request request) async {
    try {
      final result = await _gamificationService.getAllRewards();
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get rewards: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _purchaseRewardHandler(Request request) async {
    final rewardId = request.params['rewardId']!;
    
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      final userId = data['user_id'] as String;
      
      final result = await _gamificationService.purchaseReward(userId, rewardId);
      
      if (result == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Failed to purchase reward'}),
          headers: {'content-type': 'application/json'},
        );
      }
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'error': 'Invalid request: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getGlobalActivityHandler(Request request) async {
    final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
    final offset = int.tryParse(request.url.queryParameters['offset'] ?? '0') ?? 0;
    
    try {
      final result = await _gamificationService.getGlobalActivity(limit: limit, offset: offset);
      
      // Broadcast activity feed update
      WebSocketService.broadcastGlobalActivity({
        'type': 'activity_feed_update',
        'data': result,
      });
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get global activity: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }

  static Future<Response> _getUserActivityHandler(Request request) async {
    final userId = request.params['userId']!;
    final limit = int.tryParse(request.url.queryParameters['limit'] ?? '20') ?? 20;
    
    try {
      final result = await _gamificationService.getUserActivity(userId, limit: limit);
      
      // Broadcast user activity update
      WebSocketService.broadcastUserActivity({
        'type': 'user_activity_update',
        'user_id': userId,
        'data': result,
      });
      
      return Response.ok(jsonEncode(result), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get user activity: $e'}),
        headers: {'content-type': 'application/json'},
      );
    }
  }
}