/// Environment configuration for the Quester server
library;

import 'dart:io';

/// Configuration class for environment variables and settings
class EnvironmentConfig {
  static EnvironmentConfig? _instance;
  
  /// Singleton instance
  static EnvironmentConfig get instance {
    _instance ??= EnvironmentConfig._();
    return _instance!;
  }
  
  EnvironmentConfig._();

  /// Current environment (development, staging, production)
  String get environment => Platform.environment['ENVIRONMENT'] ?? 'development';
  
  /// Whether running in development mode
  bool get isDevelopment => environment == 'development';
  
  /// Whether running in production mode
  bool get isProduction => environment == 'production';
  
  /// Whether running in staging mode
  bool get isStaging => environment == 'staging';

  // Server Configuration
  /// Server host
  String get host => Platform.environment['HOST'] ?? 'localhost';
  
  /// Server port
  int get port => int.tryParse(Platform.environment['PORT'] ?? '') ?? 8080;
  
  /// Base URL for the server
  String get baseUrl => Platform.environment['BASE_URL'] ?? 'http://$host:$port';

  // Database Configuration
  /// Database host
  String get dbHost => Platform.environment['DB_HOST'] ?? 'localhost';
  
  /// Database port
  int get dbPort => int.tryParse(Platform.environment['DB_PORT'] ?? '') ?? 5432;
  
  /// Database name
  String get dbName => Platform.environment['DB_NAME'] ?? 'quester_dev';
  
  /// Database username
  String get dbUsername => Platform.environment['DB_USERNAME'] ?? 'postgres';
  
  /// Database password
  String get dbPassword => Platform.environment['DB_PASSWORD'] ?? 'password';
  
  /// Database SSL mode
  bool get dbSslMode => Platform.environment['DB_SSL_MODE']?.toLowerCase() == 'true';
  
  /// Database connection string
  String get dbConnectionString => 
      'postgresql://$dbUsername:$dbPassword@$dbHost:$dbPort/$dbName';

  // Redis Configuration
  /// Redis host
  String get redisHost => Platform.environment['REDIS_HOST'] ?? 'localhost';
  
  /// Redis port
  int get redisPort => int.tryParse(Platform.environment['REDIS_PORT'] ?? '') ?? 6379;
  
  /// Redis password
  String? get redisPassword => Platform.environment['REDIS_PASSWORD'];
  
  /// Redis database number
  int get redisDb => int.tryParse(Platform.environment['REDIS_DB'] ?? '') ?? 0;

  // JWT Configuration
  /// JWT secret key
  String get jwtSecret => Platform.environment['JWT_SECRET'] ?? 'your-secret-key';
  
  /// JWT expiration time in hours
  int get jwtExpirationHours => 
      int.tryParse(Platform.environment['JWT_EXPIRATION_HOURS'] ?? '') ?? 24;
  
  /// JWT refresh token expiration in days
  int get jwtRefreshExpirationDays => 
      int.tryParse(Platform.environment['JWT_REFRESH_EXPIRATION_DAYS'] ?? '') ?? 30;

  // Email Configuration
  /// SMTP host
  String get smtpHost => Platform.environment['SMTP_HOST'] ?? 'localhost';
  
  /// SMTP port
  int get smtpPort => int.tryParse(Platform.environment['SMTP_PORT'] ?? '') ?? 587;
  
  /// SMTP username
  String get smtpUsername => Platform.environment['SMTP_USERNAME'] ?? '';
  
  /// SMTP password
  String get smtpPassword => Platform.environment['SMTP_PASSWORD'] ?? '';
  
  /// SMTP use TLS
  bool get smtpUseTls => Platform.environment['SMTP_USE_TLS']?.toLowerCase() == 'true';
  
  /// From email address
  String get fromEmail => Platform.environment['FROM_EMAIL'] ?? '<EMAIL>';

  // Push Notification Configuration
  /// Firebase server key
  String get firebaseServerKey => Platform.environment['FIREBASE_SERVER_KEY'] ?? '';

  /// FCM server key (alias for Firebase server key)
  String get fcmServerKey => firebaseServerKey;

  /// Firebase project ID
  String get firebaseProjectId => Platform.environment['FIREBASE_PROJECT_ID'] ?? '';

  /// APNs key ID
  String get apnsKeyId => Platform.environment['APNS_KEY_ID'] ?? '';

  /// APNs team ID
  String get apnsTeamId => Platform.environment['APNS_TEAM_ID'] ?? '';

  /// APNs bundle ID
  String get apnsBundleId => Platform.environment['APNS_BUNDLE_ID'] ?? '';

  /// APNs auth key (private key content)
  String get apnsAuthKey => Platform.environment['APNS_AUTH_KEY'] ?? '';

  // AWS Configuration
  /// AWS access key ID
  String get awsAccessKeyId => Platform.environment['AWS_ACCESS_KEY_ID'] ?? '';
  
  /// AWS secret access key
  String get awsSecretAccessKey => Platform.environment['AWS_SECRET_ACCESS_KEY'] ?? '';
  
  /// AWS region
  String get awsRegion => Platform.environment['AWS_REGION'] ?? 'us-east-1';
  
  /// S3 bucket name
  String get s3BucketName => Platform.environment['S3_BUCKET_NAME'] ?? '';

  // Logging Configuration
  /// Log level (debug, info, warning, error)
  String get logLevel => Platform.environment['LOG_LEVEL'] ?? 'info';
  
  /// Whether to enable request logging
  bool get enableRequestLogging => 
      Platform.environment['ENABLE_REQUEST_LOGGING']?.toLowerCase() != 'false';

  // Security Configuration
  /// CORS allowed origins
  List<String> get corsAllowedOrigins => 
      Platform.environment['CORS_ALLOWED_ORIGINS']?.split(',') ?? ['*'];
  
  /// Rate limit max requests per window
  int get rateLimitMaxRequests => 
      int.tryParse(Platform.environment['RATE_LIMIT_MAX_REQUESTS'] ?? '') ?? 100;
  
  /// Rate limit window in minutes
  int get rateLimitWindowMinutes => 
      int.tryParse(Platform.environment['RATE_LIMIT_WINDOW_MINUTES'] ?? '') ?? 15;

  // Feature Flags
  /// Whether to enable analytics
  bool get enableAnalytics => 
      Platform.environment['ENABLE_ANALYTICS']?.toLowerCase() != 'false';
  
  /// Whether to enable push notifications
  bool get enablePushNotifications => 
      Platform.environment['ENABLE_PUSH_NOTIFICATIONS']?.toLowerCase() != 'false';
  
  /// Whether to enable email notifications
  bool get enableEmailNotifications => 
      Platform.environment['ENABLE_EMAIL_NOTIFICATIONS']?.toLowerCase() != 'false';
  
  /// Whether to enable SMS notifications
  bool get enableSmsNotifications => 
      Platform.environment['ENABLE_SMS_NOTIFICATIONS']?.toLowerCase() == 'true';

  // API Keys
  /// Third-party API keys
  Map<String, String> get apiKeys => {
    'twilio': Platform.environment['TWILIO_API_KEY'] ?? '',
    'sendgrid': Platform.environment['SENDGRID_API_KEY'] ?? '',
    'stripe': Platform.environment['STRIPE_API_KEY'] ?? '',
    'google_maps': Platform.environment['GOOGLE_MAPS_API_KEY'] ?? '',
  };

  /// Validates that all required environment variables are set
  void validate() {
    final errors = <String>[];
    
    if (isProduction) {
      // Required in production
      if (jwtSecret == 'your-secret-key') {
        errors.add('JWT_SECRET must be set in production');
      }
      
      if (dbPassword == 'password') {
        errors.add('DB_PASSWORD must be set in production');
      }
      
      if (enablePushNotifications && firebaseServerKey.isEmpty) {
        errors.add('FIREBASE_SERVER_KEY must be set when push notifications are enabled');
      }
      
      if (enableEmailNotifications && (smtpUsername.isEmpty || smtpPassword.isEmpty)) {
        errors.add('SMTP credentials must be set when email notifications are enabled');
      }
    }
    
    if (errors.isNotEmpty) {
      throw StateError('Environment validation failed:\n${errors.join('\n')}');
    }
  }

  /// Loads configuration from a .env file
  static void loadFromFile(String filePath) {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        print('Warning: .env file not found at $filePath');
        return;
      }
      
      final lines = file.readAsLinesSync();
      for (final line in lines) {
        final trimmed = line.trim();
        if (trimmed.isEmpty || trimmed.startsWith('#')) continue;
        
        final parts = trimmed.split('=');
        if (parts.length >= 2) {
          final key = parts[0].trim();
          final value = parts.sublist(1).join('=').trim();
          Platform.environment[key] = value;
        }
      }
    } catch (e) {
      print('Warning: Failed to load .env file: $e');
    }
  }

  /// Prints current configuration (excluding sensitive data)
  void printConfig() {
    print('=== Quester Server Configuration ===');
    print('Environment: $environment');
    print('Host: $host');
    print('Port: $port');
    print('Database: $dbHost:$dbPort/$dbName');
    print('Redis: $redisHost:$redisPort');
    print('Log Level: $logLevel');
    print('Push Notifications: $enablePushNotifications');
    print('Email Notifications: $enableEmailNotifications');
    print('SMS Notifications: $enableSmsNotifications');
    print('Analytics: $enableAnalytics');
    print('=====================================');
  }

  /// Gets configuration as a map (excluding sensitive data)
  Map<String, dynamic> toMap() {
    return {
      'environment': environment,
      'host': host,
      'port': port,
      'database': {
        'host': dbHost,
        'port': dbPort,
        'name': dbName,
        'ssl': dbSslMode,
      },
      'redis': {
        'host': redisHost,
        'port': redisPort,
        'db': redisDb,
      },
      'features': {
        'analytics': enableAnalytics,
        'push_notifications': enablePushNotifications,
        'email_notifications': enableEmailNotifications,
        'sms_notifications': enableSmsNotifications,
      },
      'security': {
        'cors_origins': corsAllowedOrigins,
        'rate_limit': {
          'max_requests': rateLimitMaxRequests,
          'window_minutes': rateLimitWindowMinutes,
        },
      },
    };
  }
}
