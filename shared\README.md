<!-- 
This README describes the package. If you publish this package to pub.dev,
this README's contents appear on the landing page for your package.

For information about how to write a good package README, see the guide for
[writing package pages](https://dart.dev/tools/pub/writing-package-pages). 

For general information about developing packages, see the Dart guide for
[creating packages](https://dart.dev/guides/libraries/create-packages)
and the Flutter guide for
[developing packages and plugins](https://flutter.dev/to/develop-packages). 
-->

The shared package contains common models, DTOs, utilities, and constants used across the Quester platform. This package ensures type safety and consistency between the client (Flutter) and server (Dart) applications.

## Features

- **Shared Models**: Common data models for users, quests, achievements, and more
- **DTOs**: Data Transfer Objects for API communication
- **Utilities**: Helper functions for date formatting, validation, and gamification
- **Constants**: Shared constants for points, levels, and game mechanics
- **Type Safety**: Full type safety across client-server boundary
- **JSON Serialization**: Automatic JSON serialization/deserialization

## Getting started

Add this package as a dependency in your `pubspec.yaml`:

```yaml
dependencies:
  shared:
    path: ../shared
```

Then run:
```bash
dart pub get
```

## Usage

Import the shared models and utilities in your Dart/Flutter code:

```dart
import 'package:shared/shared.dart';

// Use shared models
final user = User(
  id: 'user123',
  email: '<EMAIL>',
  displayName: 'John Doe',
  role: UserRole.member,
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);

// Use shared DTOs for API communication
final createQuestDto = CreateQuestDto(
  title: 'Complete Flutter Tutorial',
  description: 'Learn Flutter by building a todo app',
  priority: QuestPriority.high,
  difficulty: QuestDifficulty.intermediate,
  category: QuestCategory.learning,
  participantIds: [],
  tags: ['flutter', 'tutorial'],
);

// Use shared utilities
final formattedDate = DateUtils.formatIsoDate(DateTime.now());
final isValidEmail = '<EMAIL>'.isEmail;
final titleCased = 'hello world'.titleCase;
```

## Additional information

This package is part of the Quester platform monorepo. For more information:

- **Main Repository**: [Quester Platform](https://github.com/your-org/quester)
- **Documentation**: See `/docs` directory in the main repository
- **Issues**: Report issues in the main repository
- **Contributing**: Follow the contributing guidelines in the main repository

The package uses code generation for JSON serialization. Run the following command after making changes to models:

```bash
dart run build_runner build
```
