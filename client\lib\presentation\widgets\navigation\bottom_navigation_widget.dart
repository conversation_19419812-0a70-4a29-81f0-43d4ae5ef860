import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import '../common/notification_badge.dart';

/// Bottom navigation widget for mobile devices
class BottomNavigationWidget extends StatelessWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Whether to show labels
  final bool showLabels;
  
  /// Custom background color
  final Color? backgroundColor;
  
  /// Custom selected item color
  final Color? selectedItemColor;
  
  /// Custom unselected item color
  final Color? unselectedItemColor;

  const BottomNavigationWidget({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.showLabels = true,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppConstants.bottomNavHeight,
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: _buildNavigationItems(context),
        ),
      ),
    );
  }

  /// Build navigation items
  List<Widget> _buildNavigationItems(BuildContext context) {
    final items = _getNavigationItems();
    
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final isSelected = index == selectedIndex;
      
      return Expanded(
        child: _BottomNavigationItem(
          item: item,
          isSelected: isSelected,
          showLabel: showLabels,
          selectedColor: selectedItemColor ?? Theme.of(context).colorScheme.primary,
          unselectedColor: unselectedItemColor ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          onTap: () => onItemSelected?.call(index),
        ),
      );
    }).toList();
  }

  /// Get navigation items configuration
  List<NavigationItemData> _getNavigationItems() {
    return [
      NavigationItemData(
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard,
        label: 'Dashboard',
      ),
      NavigationItemData(
        icon: Icons.explore_outlined,
        selectedIcon: Icons.explore,
        label: 'Quests',
      ),
      NavigationItemData(
        icon: Icons.task_outlined,
        selectedIcon: Icons.task,
        label: 'Tasks',
      ),
      NavigationItemData(
        icon: Icons.chat_bubble_outline,
        selectedIcon: Icons.chat_bubble,
        label: 'Messages',
        badgeCount: 3, // TODO: Get from state
      ),
      NavigationItemData(
        icon: Icons.person_outline,
        selectedIcon: Icons.person,
        label: 'Profile',
      ),
    ];
  }
}

/// Individual bottom navigation item
class _BottomNavigationItem extends StatelessWidget {
  final NavigationItemData item;
  final bool isSelected;
  final bool showLabel;
  final Color selectedColor;
  final Color unselectedColor;
  final VoidCallback? onTap;

  const _BottomNavigationItem({
    required this.item,
    required this.isSelected,
    required this.showLabel,
    required this.selectedColor,
    required this.unselectedColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final color = isSelected ? selectedColor : unselectedColor;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: AppConstants.smallPadding,
          horizontal: AppConstants.smallPadding,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildIcon(color),
            if (showLabel) ...[
              const SizedBox(height: 4),
              _buildLabel(context, color),
            ],
          ],
        ),
      ),
    );
  }

  /// Build icon with optional badge
  Widget _buildIcon(Color color) {
    Widget iconWidget = AnimatedSwitcher(
      duration: AppConstants.shortAnimation,
      child: Icon(
        isSelected ? item.selectedIcon : item.icon,
        key: ValueKey(isSelected),
        color: color,
        size: 24,
      ),
    );

    if (item.badgeCount != null && item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: item.badgeCount!,
        child: iconWidget,
      );
    }

    return iconWidget;
  }

  /// Build label text
  Widget _buildLabel(BuildContext context, Color color) {
    return AnimatedDefaultTextStyle(
      duration: AppConstants.shortAnimation,
      style: Theme.of(context).textTheme.labelSmall!.copyWith(
        color: color,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      child: Text(
        item.label,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

/// Navigation item data class
class NavigationItemData {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final int? badgeCount;

  const NavigationItemData({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    this.badgeCount,
  });
}

/// Floating bottom navigation bar with curved design
class FloatingBottomNavigationWidget extends StatelessWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Margin from screen edges
  final EdgeInsets margin;

  const FloatingBottomNavigationWidget({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.margin = const EdgeInsets.all(AppConstants.defaultPadding),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
        child: Container(
          height: 70,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: _buildFloatingItems(context),
          ),
        ),
      ),
    );
  }

  /// Build floating navigation items
  List<Widget> _buildFloatingItems(BuildContext context) {
    final items = _getNavigationItems();
    
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final isSelected = index == selectedIndex;
      
      return _FloatingNavigationItem(
        item: item,
        isSelected: isSelected,
        onTap: () => onItemSelected?.call(index),
      );
    }).toList();
  }

  /// Get navigation items configuration
  List<NavigationItemData> _getNavigationItems() {
    return [
      NavigationItemData(
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard,
        label: 'Dashboard',
      ),
      NavigationItemData(
        icon: Icons.explore_outlined,
        selectedIcon: Icons.explore,
        label: 'Quests',
      ),
      NavigationItemData(
        icon: Icons.task_outlined,
        selectedIcon: Icons.task,
        label: 'Tasks',
      ),
      NavigationItemData(
        icon: Icons.chat_bubble_outline,
        selectedIcon: Icons.chat_bubble,
        label: 'Messages',
        badgeCount: 3,
      ),
      NavigationItemData(
        icon: Icons.person_outline,
        selectedIcon: Icons.person,
        label: 'Profile',
      ),
    ];
  }
}

/// Individual floating navigation item
class _FloatingNavigationItem extends StatelessWidget {
  final NavigationItemData item;
  final bool isSelected;
  final VoidCallback? onTap;

  const _FloatingNavigationItem({
    required this.item,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: AppConstants.mediumAnimation,
        curve: Curves.easeInOut,
        padding: EdgeInsets.symmetric(
          horizontal: isSelected ? 16 : 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildIcon(context),
            if (isSelected) ...[
              const SizedBox(width: 8),
              Text(
                item.label,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build icon with optional badge
  Widget _buildIcon(BuildContext context) {
    final color = isSelected 
        ? Theme.of(context).colorScheme.primary
        : Theme.of(context).colorScheme.onSurface.withOpacity(0.6);

    Widget iconWidget = AnimatedSwitcher(
      duration: AppConstants.shortAnimation,
      child: Icon(
        isSelected ? item.selectedIcon : item.icon,
        key: ValueKey(isSelected),
        color: color,
        size: 24,
      ),
    );

    if (item.badgeCount != null && item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: item.badgeCount!,
        child: iconWidget,
      );
    }

    return iconWidget;
  }
}

/// Tab bar style bottom navigation
class TabBarBottomNavigation extends StatelessWidget {
  /// Currently selected index
  final int selectedIndex;
  
  /// Callback when item is selected
  final ValueChanged<int>? onItemSelected;
  
  /// Whether to show indicator
  final bool showIndicator;

  const TabBarBottomNavigation({
    super.key,
    required this.selectedIndex,
    this.onItemSelected,
    this.showIndicator = true,
  });

  @override
  Widget build(BuildContext context) {
    final items = _getNavigationItems();
    
    return Container(
      height: AppConstants.bottomNavHeight,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = index == selectedIndex;
            
            return Expanded(
              child: _TabBarNavigationItem(
                item: item,
                isSelected: isSelected,
                showIndicator: showIndicator,
                onTap: () => onItemSelected?.call(index),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Get navigation items configuration
  List<NavigationItemData> _getNavigationItems() {
    return [
      NavigationItemData(
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard,
        label: 'Dashboard',
      ),
      NavigationItemData(
        icon: Icons.explore_outlined,
        selectedIcon: Icons.explore,
        label: 'Quests',
      ),
      NavigationItemData(
        icon: Icons.task_outlined,
        selectedIcon: Icons.task,
        label: 'Tasks',
      ),
      NavigationItemData(
        icon: Icons.chat_bubble_outline,
        selectedIcon: Icons.chat_bubble,
        label: 'Messages',
        badgeCount: 3,
      ),
      NavigationItemData(
        icon: Icons.person_outline,
        selectedIcon: Icons.person,
        label: 'Profile',
      ),
    ];
  }
}

/// Individual tab bar navigation item
class _TabBarNavigationItem extends StatelessWidget {
  final NavigationItemData item;
  final bool isSelected;
  final bool showIndicator;
  final VoidCallback? onTap;

  const _TabBarNavigationItem({
    required this.item,
    required this.isSelected,
    required this.showIndicator,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final color = isSelected 
        ? Theme.of(context).colorScheme.primary
        : Theme.of(context).colorScheme.onSurface.withOpacity(0.6);

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
        decoration: showIndicator && isSelected ? BoxDecoration(
          border: Border(
            top: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 3,
            ),
          ),
        ) : null,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildIcon(color),
            const SizedBox(height: 4),
            Text(
              item.label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: color,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// Build icon with optional badge
  Widget _buildIcon(Color color) {
    Widget iconWidget = Icon(
      isSelected ? item.selectedIcon : item.icon,
      color: color,
      size: 24,
    );

    if (item.badgeCount != null && item.badgeCount! > 0) {
      iconWidget = NotificationBadge(
        count: item.badgeCount!,
        child: iconWidget,
      );
    }

    return iconWidget;
  }
}
