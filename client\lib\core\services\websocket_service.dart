import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

import '../config/app_config.dart';

/// Connection states for WebSocket
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// WebSocket service for real-time communication with enhanced reconnection
class WebSocketService {
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Enhanced connection management
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 2);
  static const Duration _heartbeatInterval = Duration(seconds: 30);

  String? _lastUrl;
  String? _lastToken;
  bool _shouldReconnect = true;

  // Connection state tracking
  final StreamController<WebSocketConnectionState> _connectionStateController =
      StreamController<WebSocketConnectionState>.broadcast();
  WebSocketConnectionState _currentState = WebSocketConnectionState.disconnected;

  /// Stream of incoming WebSocket messages
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;

  /// Stream of connection state changes
  Stream<WebSocketConnectionState> get connectionStateStream => _connectionStateController.stream;

  /// Connection status
  bool get isConnected => _channel != null && _currentState == WebSocketConnectionState.connected;

  /// Current connection state
  WebSocketConnectionState get connectionState => _currentState;

  /// Connect to WebSocket server with enhanced reconnection
  Future<void> connect({String? token}) async {
    final wsUrl = 'ws://${AppConfig.serverHost}:${AppConfig.serverPort}${AppConfig.wsEndpoint}';
    await _connectWithRetry(wsUrl, token);
  }

  /// Internal connection method with retry logic
  Future<void> _connectWithRetry(String wsUrl, String? token) async {
    if (_currentState == WebSocketConnectionState.connecting) {
      return; // Already connecting
    }

    _updateConnectionState(WebSocketConnectionState.connecting);
    _lastUrl = wsUrl;
    _lastToken = token;

    try {
      final uri = Uri.parse(wsUrl);
      _channel = WebSocketChannel.connect(uri);

      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDone,
      );

      // Send authentication if token is provided
      if (token != null) {
        _sendMessage({
          'type': 'user_auth',
          'user_id': token, // Simplified - in real app, extract user ID from token
          'token': token,
          'device_info': {
            'platform': 'flutter',
            'version': '1.0.0',
          },
        });
      }

      // Start heartbeat
      _startHeartbeat();

      _updateConnectionState(WebSocketConnectionState.connected);
      _reconnectAttempts = 0; // Reset on successful connection

      debugPrint('✅ WebSocket connected to $wsUrl');
    } catch (e) {
      debugPrint('❌ WebSocket connection failed: $e');
      _updateConnectionState(WebSocketConnectionState.error);
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (!_shouldReconnect || _reconnectAttempts >= _maxReconnectAttempts) {
      _updateConnectionState(WebSocketConnectionState.disconnected);
      return;
    }

    _reconnectAttempts++;
    _updateConnectionState(WebSocketConnectionState.reconnecting);

    final delay = Duration(seconds: _reconnectDelay.inSeconds * _reconnectAttempts);
    _reconnectTimer = Timer(delay, () {
      if (_lastUrl != null) {
        debugPrint('🔄 Reconnecting... (attempt $_reconnectAttempts/$_maxReconnectAttempts)');
        _connectWithRetry(_lastUrl!, _lastToken);
      }
    });
  }

  /// Start heartbeat to keep connection alive
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (isConnected) {
        _sendMessage({
          'type': 'ping',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
      } else {
        timer.cancel();
      }
    });
  }

  /// Update connection state and notify listeners
  void _updateConnectionState(WebSocketConnectionState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _connectionStateController.add(newState);
    }
  }

  /// Connect to gamification WebSocket
  Future<void> connectToGamification({String? token}) async {
    try {
      final wsUrl = 'ws://${AppConfig.serverHost}:${AppConfig.serverPort}${AppConfig.gamificationWsEndpoint}';
      final uri = Uri.parse(wsUrl);
      
      _channel = WebSocketChannel.connect(uri);
      
      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDone,
      );
      
      if (token != null) {
        _sendMessage({
          'type': 'auth',
          'token': token,
        });
      }
      
      debugPrint('✅ Gamification WebSocket connected');
    } catch (e) {
      debugPrint('❌ Gamification WebSocket connection failed: $e');
    }
  }

  /// Send message through WebSocket
  void sendMessage(Map<String, dynamic> message) {
    if (_channel != null) {
      _sendMessage(message);
    }
  }

  // Room management methods moved to enhanced versions below

  /// Subscribe to quest updates
  void subscribeToQuestUpdates(String questId) {
    sendMessage({
      'type': 'subscribe_quest',
      'questId': questId,
    });
  }

  /// Unsubscribe from quest updates
  void unsubscribeFromQuestUpdates(String questId) {
    sendMessage({
      'type': 'unsubscribe_quest',
      'questId': questId,
    });
  }

  /// Subscribe to gamification updates
  void subscribeToGamificationUpdates() {
    sendMessage({
      'type': 'subscribe_gamification',
    });
  }

  /// Send collaboration message
  void sendCollaborationMessage(String teamId, String message) {
    sendMessage({
      'type': 'collaboration_message',
      'teamId': teamId,
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Send real-time progress update
  void sendProgressUpdate(String questId, double progress) {
    sendMessage({
      'type': 'progress_update',
      'questId': questId,
      'progress': progress,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Disconnect from WebSocket
  Future<void> disconnect() async {
    _shouldReconnect = false; // Prevent automatic reconnection
    _reconnectTimer?.cancel();
    _heartbeatTimer?.cancel();
    await _subscription?.cancel();
    await _channel?.sink.close();
    _channel = null;
    _subscription = null;
    _updateConnectionState(WebSocketConnectionState.disconnected);
    debugPrint('🔌 WebSocket disconnected');
  }

  /// Join a room for real-time collaboration
  void joinRoom(String roomId) {
    if (!isConnected) {
      debugPrint('❌ Cannot join room: WebSocket not connected');
      return;
    }

    _sendMessage({
      'type': 'join_room',
      'room_id': roomId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Leave a room
  void leaveRoom(String roomId) {
    if (!isConnected) {
      debugPrint('❌ Cannot leave room: WebSocket not connected');
      return;
    }

    _sendMessage({
      'type': 'leave_room',
      'room_id': roomId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Update user presence status
  void updatePresence({
    String? status,
    String? activity,
    String? statusMessage,
  }) {
    if (!isConnected) {
      debugPrint('❌ Cannot update presence: WebSocket not connected');
      return;
    }

    final presenceData = <String, dynamic>{
      'type': 'update_presence',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    if (status != null) presenceData['status'] = status;
    if (activity != null) presenceData['activity'] = activity;
    if (statusMessage != null) presenceData['status_message'] = statusMessage;

    _sendMessage(presenceData);
  }

  /// Enable automatic reconnection
  void enableReconnection() {
    _shouldReconnect = true;
  }

  /// Disable automatic reconnection
  void disableReconnection() {
    _shouldReconnect = false;
    _reconnectTimer?.cancel();
  }

  void _sendMessage(Map<String, dynamic> message) {
    try {
      _channel?.sink.add(jsonEncode(message));
    } catch (e) {
      debugPrint('❌ Failed to send WebSocket message: $e');
    }
  }

  void _onMessage(dynamic data) {
    try {
      final message = jsonDecode(data);
      if (message is Map<String, dynamic>) {
        _messageController.add(message);
      }
    } catch (e) {
      debugPrint('❌ Failed to parse WebSocket message: $e');
    }
  }

  void _onError(dynamic error) {
    debugPrint('❌ WebSocket error: $error');
    _updateConnectionState(WebSocketConnectionState.error);

    // Schedule reconnection on error
    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }

  void _onDone() {
    debugPrint('🔌 WebSocket connection closed');
    _channel = null;
    _subscription = null;
    _heartbeatTimer?.cancel();

    if (_currentState != WebSocketConnectionState.disconnected) {
      _updateConnectionState(WebSocketConnectionState.disconnected);

      // Schedule reconnection if not intentionally disconnected
      if (_shouldReconnect) {
        _scheduleReconnect();
      }
    }
  }

  void dispose() {
    disconnect();
    _messageController.close();
  }
}