import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared/shared.dart';

/// Authentication service for managing user tokens and session
class AuthService {
  static const _storage = FlutterSecureStorage();
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'current_user';

  String? _currentToken;
  String? _refreshToken;
  User? _currentUser;

  /// Get current authentication token
  String? get currentToken => _currentToken;
  
  /// Get current user
  User? get currentUser => _currentUser;
  
  /// Check if user is authenticated
  bool get isAuthenticated => _currentToken != null && _currentUser != null;

  /// Initialize auth service by loading stored tokens
  Future<void> init() async {
    await _loadStoredAuth();
  }

  /// Login with token and user data
  Future<void> login(String token, String refreshToken, User user) async {
    _currentToken = token;
    _refreshToken = refreshToken;
    _currentUser = user;
    
    await _storage.write(key: _tokenKey, value: token);
    await _storage.write(key: _refreshTokenKey, value: refreshToken);
    await _storage.write(key: _userKey, value: jsonEncode(user.toJson()));
  }

  /// Logout and clear stored data
  Future<void> logout() async {
    _currentToken = null;
    _refreshToken = null;
    _currentUser = null;
    
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _refreshTokenKey);
    await _storage.delete(key: _userKey);
  }

  /// Update current user data
  Future<void> updateUser(User user) async {
    _currentUser = user;
    await _storage.write(key: _userKey, value: jsonEncode(user.toJson()));
  }

  /// Get refresh token for token renewal
  String? get refreshTokenValue => _refreshToken;

  /// Update tokens after refresh
  Future<void> updateTokens(String token, String refreshToken) async {
    _currentToken = token;
    _refreshToken = refreshToken;
    
    await _storage.write(key: _tokenKey, value: token);
    await _storage.write(key: _refreshTokenKey, value: refreshToken);
  }

  /// Load stored authentication data
  Future<void> _loadStoredAuth() async {
    try {
      _currentToken = await _storage.read(key: _tokenKey);
      _refreshToken = await _storage.read(key: _refreshTokenKey);
      
      final userJson = await _storage.read(key: _userKey);
      if (userJson != null) {
        final userData = jsonDecode(userJson);
        _currentUser = User.fromJson(userData);
      }
    } catch (e) {
      debugPrint('Error loading stored auth: $e');
      await logout(); // Clear potentially corrupted data
    }
  }
}