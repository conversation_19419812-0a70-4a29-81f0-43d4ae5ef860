import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shared/shared.dart';
import 'package:postgres/postgres.dart';

/// API management service for enterprise features
class ApiManagementService {
  final Connection _connection;
  
  ApiManagementService(this._connection);

  /// Get API keys for an organization
  Future<Response> getApiKeys(Request request) async {
    try {
      final organizationId = request.params['organizationId'];
      final queryParams = request.url.queryParameters;
      
      final page = int.tryParse(queryParams['page'] ?? '1') ?? 1;
      final limit = int.tryParse(queryParams['limit'] ?? '20') ?? 20;
      final offset = (page - 1) * limit;
      
      final result = await _connection.execute(
        Sql.named('''
          SELECT * FROM api_keys 
          WHERE organization_id = @organizationId::uuid
          ORDER BY created_at DESC 
          LIMIT @limit OFFSET @offset
        '''),
        parameters: {
          'organizationId': organizationId,
          'limit': limit,
          'offset': offset,
        },
      );
      
      final apiKeys = result.map((row) => {
        'id': row[0].toString(),
        'organizationId': row[1].toString(),
        'name': row[2].toString(),
        'keyValue': row[3].toString(),
        'type': row[4].toString(),
        'status': row[5].toString(),
        'createdAt': row[6].toString(),
        'expiresAt': row[7]?.toString(),
        'lastUsedAt': row[8]?.toString(),
        'usageCount': row[9] as int,
        'rateLimits': jsonDecode(row[10].toString()),
        'allowedIPs': row[11] != null ? jsonDecode(row[11].toString()) : null,
        'allowedDomains': row[12] != null ? jsonDecode(row[12].toString()) : null,
        'scopes': jsonDecode(row[13].toString()),
        'createdBy': row[14].toString(),
        'metadata': row[15] != null ? jsonDecode(row[15].toString()) : null,
        'updatedAt': row[16].toString(),
      }).toList();
      
      return Response.ok(
        jsonEncode({
          'apiKeys': apiKeys,
          'pagination': {
            'page': page,
            'limit': limit,
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get API keys: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Create API key
  Future<Response> createApiKey(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final apiKey = ApiKey(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        organizationId: data['organizationId'] as String,
        name: data['name'] as String,
        keyValue: _generateApiKey(),
        type: ApiKeyType.values.firstWhere(
          (e) => e.name == data['type'],
          orElse: () => ApiKeyType.read,
        ),
        status: ApiKeyStatus.active,
        createdAt: DateTime.now(),
        expiresAt: data['expiresAt'] != null 
            ? DateTime.parse(data['expiresAt'] as String)
            : null,
        usageCount: 0,
        rateLimits: data['rateLimits'] as Map<String, dynamic>? ?? {},
        allowedIPs: data['allowedIPs'] != null 
            ? List<String>.from(data['allowedIPs'] as List)
            : null,
        allowedDomains: data['allowedDomains'] != null
            ? List<String>.from(data['allowedDomains'] as List)
            : null,
        scopes: List<String>.from(data['scopes'] as List? ?? ['read']),
        createdBy: data['createdBy'] as String,
        updatedAt: DateTime.now(),
      );
      
      await _connection.execute(
        Sql.named('''
          INSERT INTO api_keys (
            id, organization_id, name, key_value, type, status,
            created_at, expires_at, usage_count, rate_limits,
            allowed_ips, allowed_domains, scopes, created_by, updated_at
          ) VALUES (
            @id::uuid, @organizationId::uuid, @name, @keyValue, @type, @status,
            @createdAt, @expiresAt, @usageCount, @rateLimits,
            @allowedIPs, @allowedDomains, @scopes, @createdBy::uuid, @updatedAt
          )
        '''),
        parameters: {
          'id': apiKey.id,
          'organizationId': apiKey.organizationId,
          'name': apiKey.name,
          'keyValue': apiKey.keyValue,
          'type': apiKey.type.name,
          'status': apiKey.status.name,
          'createdAt': apiKey.createdAt.toIso8601String(),
          'expiresAt': apiKey.expiresAt?.toIso8601String(),
          'usageCount': apiKey.usageCount,
          'rateLimits': jsonEncode(apiKey.rateLimits),
          'allowedIPs': apiKey.allowedIPs != null ? jsonEncode(apiKey.allowedIPs) : null,
          'allowedDomains': apiKey.allowedDomains != null ? jsonEncode(apiKey.allowedDomains) : null,
          'scopes': jsonEncode(apiKey.scopes),
          'createdBy': apiKey.createdBy,
          'updatedAt': apiKey.updatedAt.toIso8601String(),
        },
      );
      
      return Response.ok(
        jsonEncode(apiKey.toJson()),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create API key: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Revoke API key
  Future<Response> revokeApiKey(Request request) async {
    try {
      final keyId = request.params['keyId'];
      
      await _connection.execute(
        Sql.named('''
          UPDATE api_keys 
          SET status = 'revoked', updated_at = @updatedAt
          WHERE id = @keyId::uuid
        '''),
        parameters: {
          'keyId': keyId,
          'updatedAt': DateTime.now().toIso8601String(),
        },
      );
      
      return Response.ok(
        jsonEncode({'status': 'revoked', 'keyId': keyId}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to revoke API key: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get webhooks for an organization
  Future<Response> getWebhooks(Request request) async {
    try {
      final organizationId = request.params['organizationId'];
      
      final result = await _connection.execute(
        Sql.named('''
          SELECT * FROM webhooks 
          WHERE organization_id = @organizationId::uuid
          ORDER BY created_at DESC
        '''),
        parameters: {'organizationId': organizationId},
      );
      
      final webhooks = result.map((row) => {
        'id': row[0].toString(),
        'organizationId': row[1].toString(),
        'name': row[2].toString(),
        'url': row[3].toString(),
        'status': row[4].toString(),
        'events': jsonDecode(row[5].toString()),
        'secret': row[6].toString(),
        'headers': jsonDecode(row[7].toString()),
        'maxRetries': row[8] as int,
        'retryDelay': row[9] as int,
        'timeout': row[10] as int,
        'verifySSL': row[11] as bool,
        'createdAt': row[12].toString(),
        'lastSuccessAt': row[13]?.toString(),
        'lastFailureAt': row[14]?.toString(),
        'failureCount': row[15] as int,
        'deliveryCount': row[16] as int,
        'metadata': row[17] != null ? jsonDecode(row[17].toString()) : null,
        'createdBy': row[18].toString(),
        'updatedAt': row[19].toString(),
      }).toList();
      
      return Response.ok(
        jsonEncode({'webhooks': webhooks}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get webhooks: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Create webhook
  Future<Response> createWebhook(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final webhook = Webhook(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        organizationId: data['organizationId'] as String,
        name: data['name'] as String,
        url: data['url'] as String,
        status: WebhookStatus.active,
        events: (data['events'] as List<dynamic>)
            .map((e) => WebhookEventType.values.firstWhere(
                  (type) => type.name == e,
                  orElse: () => WebhookEventType.systemAlert,
                ))
            .toList(),
        secret: _generateWebhookSecret(),
        headers: Map<String, String>.from(data['headers'] as Map? ?? {}),
        maxRetries: data['maxRetries'] as int? ?? 3,
        retryDelay: data['retryDelay'] as int? ?? 60,
        timeout: data['timeout'] as int? ?? 30,
        verifySSL: data['verifySSL'] as bool? ?? true,
        createdAt: DateTime.now(),
        failureCount: 0,
        deliveryCount: 0,
        createdBy: data['createdBy'] as String,
        updatedAt: DateTime.now(),
      );
      
      await _connection.execute(
        Sql.named('''
          INSERT INTO webhooks (
            id, organization_id, name, url, status, events, secret,
            headers, max_retries, retry_delay, timeout, verify_ssl,
            created_at, failure_count, delivery_count, created_by, updated_at
          ) VALUES (
            @id::uuid, @organizationId::uuid, @name, @url, @status, @events, @secret,
            @headers, @maxRetries, @retryDelay, @timeout, @verifySSL,
            @createdAt, @failureCount, @deliveryCount, @createdBy::uuid, @updatedAt
          )
        '''),
        parameters: {
          'id': webhook.id,
          'organizationId': webhook.organizationId,
          'name': webhook.name,
          'url': webhook.url,
          'status': webhook.status.name,
          'events': jsonEncode(webhook.events.map((e) => e.name).toList()),
          'secret': webhook.secret,
          'headers': jsonEncode(webhook.headers),
          'maxRetries': webhook.maxRetries,
          'retryDelay': webhook.retryDelay,
          'timeout': webhook.timeout,
          'verifySSL': webhook.verifySSL,
          'createdAt': webhook.createdAt.toIso8601String(),
          'failureCount': webhook.failureCount,
          'deliveryCount': webhook.deliveryCount,
          'createdBy': webhook.createdBy,
          'updatedAt': webhook.updatedAt.toIso8601String(),
        },
      );
      
      return Response.ok(
        jsonEncode(webhook.toJson()),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create webhook: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get rate limits for an organization
  Future<Response> getRateLimits(Request request) async {
    try {
      final organizationId = request.params['organizationId'];
      
      final result = await _connection.execute(
        Sql.named('''
          SELECT * FROM rate_limits 
          WHERE organization_id = @organizationId::uuid
          ORDER BY created_at DESC
        '''),
        parameters: {'organizationId': organizationId},
      );
      
      final rateLimits = result.map((row) => {
        'id': row[0].toString(),
        'organizationId': row[1].toString(),
        'apiKeyId': row[2]?.toString(),
        'name': row[3].toString(),
        'type': row[4].toString(),
        'maxRequests': row[5] as int,
        'timeWindowSeconds': row[6] as int,
        'enabled': row[7] as bool,
        'endpoints': jsonDecode(row[8].toString()),
        'ipAddresses': row[9] != null ? jsonDecode(row[9].toString()) : null,
        'userRoles': row[10] != null ? jsonDecode(row[10].toString()) : null,
        'createdAt': row[11].toString(),
        'updatedAt': row[12].toString(),
        'metadata': row[13] != null ? jsonDecode(row[13].toString()) : null,
      }).toList();
      
      return Response.ok(
        jsonEncode({'rateLimits': rateLimits}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get rate limits: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Create rate limit
  Future<Response> createRateLimit(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final rateLimit = RateLimit(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        organizationId: data['organizationId'] as String,
        apiKeyId: data['apiKeyId'] as String?,
        name: data['name'] as String,
        type: RateLimitType.values.firstWhere(
          (e) => e.name == data['type'],
          orElse: () => RateLimitType.perMinute,
        ),
        maxRequests: data['maxRequests'] as int,
        timeWindow: Duration(seconds: data['timeWindowSeconds'] as int),
        enabled: data['enabled'] as bool? ?? true,
        endpoints: List<String>.from(data['endpoints'] as List),
        ipAddresses: data['ipAddresses'] != null
            ? List<String>.from(data['ipAddresses'] as List)
            : null,
        userRoles: data['userRoles'] != null
            ? List<String>.from(data['userRoles'] as List)
            : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await _connection.execute(
        Sql.named('''
          INSERT INTO rate_limits (
            id, organization_id, api_key_id, name, type, max_requests,
            time_window_seconds, enabled, endpoints, ip_addresses,
            user_roles, created_at, updated_at
          ) VALUES (
            @id::uuid, @organizationId::uuid, @apiKeyId::uuid, @name, @type, @maxRequests,
            @timeWindowSeconds, @enabled, @endpoints, @ipAddresses,
            @userRoles, @createdAt, @updatedAt
          )
        '''),
        parameters: {
          'id': rateLimit.id,
          'organizationId': rateLimit.organizationId,
          'apiKeyId': rateLimit.apiKeyId,
          'name': rateLimit.name,
          'type': rateLimit.type.name,
          'maxRequests': rateLimit.maxRequests,
          'timeWindowSeconds': rateLimit.timeWindow.inSeconds,
          'enabled': rateLimit.enabled,
          'endpoints': jsonEncode(rateLimit.endpoints),
          'ipAddresses': rateLimit.ipAddresses != null ? jsonEncode(rateLimit.ipAddresses) : null,
          'userRoles': rateLimit.userRoles != null ? jsonEncode(rateLimit.userRoles) : null,
          'createdAt': rateLimit.createdAt.toIso8601String(),
          'updatedAt': rateLimit.updatedAt.toIso8601String(),
        },
      );
      
      return Response.ok(
        jsonEncode(rateLimit.toJson()),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create rate limit: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Check rate limit for a request
  Future<bool> checkRateLimit(String organizationId, String? apiKeyId, String endpoint, String? ipAddress) async {
    try {
      // Get applicable rate limits
      final result = await _connection.execute(
        Sql.named('''
          SELECT id, max_requests, time_window_seconds, type
          FROM rate_limits 
          WHERE organization_id = @organizationId::uuid
          AND enabled = true
          AND (@apiKeyId::uuid IS NULL OR api_key_id = @apiKeyId::uuid OR api_key_id IS NULL)
          AND @endpoint = ANY(endpoints)
        '''),
        parameters: {
          'organizationId': organizationId,
          'apiKeyId': apiKeyId,
          'endpoint': endpoint,
        },
      );
      
      for (final row in result) {
        final rateLimitId = row[0].toString();
        final maxRequests = row[1] as int;
        final timeWindowSeconds = row[2] as int;
        // final type = row[3].toString(); // Type not used in current implementation
        
        // Check current usage
        final now = DateTime.now();
        final windowStart = now.subtract(Duration(seconds: timeWindowSeconds));
        
        final usageResult = await _connection.execute(
          Sql.named('''
            SELECT COUNT(*) as count
            FROM api_usage_log
            WHERE rate_limit_id = @rateLimitId::uuid
            AND created_at > @windowStart
          '''),
          parameters: {
            'rateLimitId': rateLimitId,
            'windowStart': windowStart.toIso8601String(),
          },
        );
        
        final currentUsage = usageResult.first[0] as int;
        
        if (currentUsage >= maxRequests) {
          return false; // Rate limit exceeded
        }
      }
      
      return true; // Within rate limits
    } catch (e) {
      print('Error checking rate limit: $e');
      return true; // Allow on error
    }
  }

  /// Log API usage
  Future<void> logApiUsage(String organizationId, String? apiKeyId, String endpoint, String? ipAddress) async {
    try {
      await _connection.execute(
        Sql.named('''
          INSERT INTO api_usage_log (
            organization_id, api_key_id, endpoint, ip_address, created_at
          ) VALUES (
            @organizationId::uuid, @apiKeyId::uuid, @endpoint, @ipAddress, @createdAt
          )
        '''),
        parameters: {
          'organizationId': organizationId,
          'apiKeyId': apiKeyId,
          'endpoint': endpoint,
          'ipAddress': ipAddress,
          'createdAt': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Error logging API usage: $e');
    }
  }

  // Helper methods
  String _generateApiKey() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    var result = 'qst_';
    
    for (var i = 0; i < 32; i++) {
      result += chars[(random + i) % chars.length];
    }
    
    return result;
  }

  String _generateWebhookSecret() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    var result = 'whsec_';
    
    for (var i = 0; i < 32; i++) {
      result += chars[(random + i) % chars.length];
    }
    
    return result;
  }
}
