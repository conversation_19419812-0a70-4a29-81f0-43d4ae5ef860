import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';
import '../../../data/repositories/messaging_repository.dart';

// Events
abstract class MessagingEvent extends Equatable {
  const MessagingEvent();

  @override
  List<Object?> get props => [];
}

class LoadChats extends MessagingEvent {
  final int page;
  final int pageSize;

  const LoadChats({
    this.page = 1,
    this.pageSize = 20,
  });

  @override
  List<Object?> get props => [page, pageSize];
}

class LoadMessages extends MessagingEvent {
  final String chatId;
  final int page;
  final int pageSize;
  final String? beforeMessageId;

  const LoadMessages({
    required this.chatId,
    this.page = 1,
    this.pageSize = 50,
    this.beforeMessageId,
  });

  @override
  List<Object?> get props => [chatId, page, pageSize, beforeMessageId];
}

class SendMessage extends MessagingEvent {
  final String chatId;
  final String content;
  final MessageType type;
  final String? replyToId;
  final Map<String, dynamic>? metadata;

  const SendMessage({
    required this.chatId,
    required this.content,
    this.type = MessageType.text,
    this.replyToId,
    this.metadata,
  });

  @override
  List<Object?> get props => [chatId, content, type, replyToId, metadata];
}

class CreateChat extends MessagingEvent {
  final ChatType type;
  final String? name;
  final String? description;
  final List<String> participantIds;
  final ChatSettings? settings;

  const CreateChat({
    required this.type,
    this.name,
    this.description,
    this.participantIds = const [],
    this.settings,
  });

  @override
  List<Object?> get props => [type, name, description, participantIds, settings];
}

class JoinChat extends MessagingEvent {
  final String chatId;

  const JoinChat({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class LeaveChat extends MessagingEvent {
  final String chatId;

  const LeaveChat({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class StartTyping extends MessagingEvent {
  final String chatId;

  const StartTyping({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class StopTyping extends MessagingEvent {
  final String chatId;

  const StopTyping({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class MarkMessageAsRead extends MessagingEvent {
  final String messageId;

  const MarkMessageAsRead({required this.messageId});

  @override
  List<Object?> get props => [messageId];
}

class AddReaction extends MessagingEvent {
  final String messageId;
  final String emoji;

  const AddReaction({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

class RemoveReaction extends MessagingEvent {
  final String messageId;
  final String emoji;

  const RemoveReaction({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

class SearchMessages extends MessagingEvent {
  final String? chatId;
  final String query;
  final int page;
  final int pageSize;

  const SearchMessages({
    this.chatId,
    required this.query,
    this.page = 1,
    this.pageSize = 20,
  });

  @override
  List<Object?> get props => [chatId, query, page, pageSize];
}

class LoadChatDetails extends MessagingEvent {
  final String chatId;

  const LoadChatDetails({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class UpdateChatSettings extends MessagingEvent {
  final String chatId;
  final ChatSettings settings;

  const UpdateChatSettings({
    required this.chatId,
    required this.settings,
  });

  @override
  List<Object?> get props => [chatId, settings];
}

// WebSocket events
class MessageReceived extends MessagingEvent {
  final Message message;

  const MessageReceived({required this.message});

  @override
  List<Object?> get props => [message];
}

class TypingIndicatorReceived extends MessagingEvent {
  final String chatId;
  final String userId;
  final String userName;
  final bool isTyping;

  const TypingIndicatorReceived({
    required this.chatId,
    required this.userId,
    required this.userName,
    required this.isTyping,
  });

  @override
  List<Object?> get props => [chatId, userId, userName, isTyping];
}

class UserPresenceUpdated extends MessagingEvent {
  final String userId;
  final bool isOnline;
  final DateTime? lastSeen;

  const UserPresenceUpdated({
    required this.userId,
    required this.isOnline,
    this.lastSeen,
  });

  @override
  List<Object?> get props => [userId, isOnline, lastSeen];
}

// States
abstract class MessagingState extends Equatable {
  const MessagingState();

  @override
  List<Object?> get props => [];
}

class MessagingInitial extends MessagingState {
  const MessagingInitial();
}

class MessagingLoading extends MessagingState {
  const MessagingLoading();
}

class MessagingError extends MessagingState {
  final String message;

  const MessagingError({required this.message});

  @override
  List<Object?> get props => [message];
}

class ChatsLoaded extends MessagingState {
  final List<Chat> chats;
  final bool hasMore;

  const ChatsLoaded({
    required this.chats,
    this.hasMore = false,
  });

  @override
  List<Object?> get props => [chats, hasMore];
}

class MessagesLoaded extends MessagingState {
  final String chatId;
  final List<Message> messages;
  final bool hasMore;

  const MessagesLoaded({
    required this.chatId,
    required this.messages,
    this.hasMore = false,
  });

  @override
  List<Object?> get props => [chatId, messages, hasMore];
}

class MessageSent extends MessagingState {
  final Message message;

  const MessageSent({required this.message});

  @override
  List<Object?> get props => [message];
}

class ChatCreated extends MessagingState {
  final Chat chat;

  const ChatCreated({required this.chat});

  @override
  List<Object?> get props => [chat];
}

class ChatJoined extends MessagingState {
  final String chatId;

  const ChatJoined({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class ChatLeft extends MessagingState {
  final String chatId;

  const ChatLeft({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class TypingStarted extends MessagingState {
  final String chatId;

  const TypingStarted({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class TypingStopped extends MessagingState {
  final String chatId;

  const TypingStopped({required this.chatId});

  @override
  List<Object?> get props => [chatId];
}

class MessageMarkedAsRead extends MessagingState {
  final String messageId;

  const MessageMarkedAsRead({required this.messageId});

  @override
  List<Object?> get props => [messageId];
}

class ReactionAdded extends MessagingState {
  final String messageId;
  final String emoji;

  const ReactionAdded({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

class ReactionRemoved extends MessagingState {
  final String messageId;
  final String emoji;

  const ReactionRemoved({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

class MessagesSearchResults extends MessagingState {
  final String query;
  final List<Message> results;
  final bool hasMore;

  const MessagesSearchResults({
    required this.query,
    required this.results,
    this.hasMore = false,
  });

  @override
  List<Object?> get props => [query, results, hasMore];
}

class ChatDetailsLoaded extends MessagingState {
  final Chat chat;

  const ChatDetailsLoaded({required this.chat});

  @override
  List<Object?> get props => [chat];
}

class ChatSettingsUpdated extends MessagingState {
  final Chat chat;

  const ChatSettingsUpdated({required this.chat});

  @override
  List<Object?> get props => [chat];
}

class NewMessageReceived extends MessagingState {
  final Message message;

  const NewMessageReceived({required this.message});

  @override
  List<Object?> get props => [message];
}

class UserTyping extends MessagingState {
  final String chatId;
  final String userId;
  final String userName;

  const UserTyping({
    required this.chatId,
    required this.userId,
    required this.userName,
  });

  @override
  List<Object?> get props => [chatId, userId, userName];
}

class UserStoppedTyping extends MessagingState {
  final String chatId;
  final String userId;

  const UserStoppedTyping({
    required this.chatId,
    required this.userId,
  });

  @override
  List<Object?> get props => [chatId, userId];
}

class UserPresenceChanged extends MessagingState {
  final String userId;
  final bool isOnline;
  final DateTime? lastSeen;

  const UserPresenceChanged({
    required this.userId,
    required this.isOnline,
    this.lastSeen,
  });

  @override
  List<Object?> get props => [userId, isOnline, lastSeen];
}

/// BLoC for managing messaging and real-time chat functionality
class MessagingBloc extends Bloc<MessagingEvent, MessagingState> {
  final MessagingRepository repository;

  MessagingBloc({required this.repository}) : super(const MessagingInitial()) {
    on<LoadChats>(_onLoadChats);
    on<LoadMessages>(_onLoadMessages);
    on<SendMessage>(_onSendMessage);
    on<CreateChat>(_onCreateChat);
    on<JoinChat>(_onJoinChat);
    on<LeaveChat>(_onLeaveChat);
    on<StartTyping>(_onStartTyping);
    on<StopTyping>(_onStopTyping);
    on<MarkMessageAsRead>(_onMarkMessageAsRead);
    on<AddReaction>(_onAddReaction);
    on<RemoveReaction>(_onRemoveReaction);
    on<SearchMessages>(_onSearchMessages);
    on<LoadChatDetails>(_onLoadChatDetails);
    on<UpdateChatSettings>(_onUpdateChatSettings);
    on<MessageReceived>(_onMessageReceived);
    on<TypingIndicatorReceived>(_onTypingIndicatorReceived);
    on<UserPresenceUpdated>(_onUserPresenceUpdated);
  }

  /// Load user's chats
  Future<void> _onLoadChats(LoadChats event, Emitter<MessagingState> emit) async {
    emit(const MessagingLoading());
    
    try {
      final response = await repository.getUserChats(
        page: event.page,
        pageSize: event.pageSize,
      );
      
      if (response.success && response.data != null) {
        emit(ChatsLoaded(
          chats: response.data!,
          hasMore: response.data!.length == event.pageSize,
        ));
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to load chats'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to load chats: $e'));
    }
  }

  /// Load messages for a specific chat
  Future<void> _onLoadMessages(LoadMessages event, Emitter<MessagingState> emit) async {
    if (event.page == 1) {
      emit(const MessagingLoading());
    }
    
    try {
      final response = await repository.getMessages(
        chatId: event.chatId,
        page: event.page,
        pageSize: event.pageSize,
        beforeMessageId: event.beforeMessageId,
      );
      
      if (response.success && response.data != null) {
        final currentState = state;
        List<Message> allMessages = response.data!;

        // If loading more messages, append to existing ones
        if (event.page > 1 && currentState is MessagesLoaded) {
          allMessages = [...currentState.messages, ...response.data!];
        }

        emit(MessagesLoaded(
          chatId: event.chatId,
          messages: allMessages,
          hasMore: response.data!.length == event.pageSize,
        ));
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to load messages'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to load messages: $e'));
    }
  }

  /// Send a message
  Future<void> _onSendMessage(SendMessage event, Emitter<MessagingState> emit) async {
    try {
      final response = await repository.sendMessage(
        chatId: event.chatId,
        content: event.content,
        type: event.type,
        replyToId: event.replyToId,
        metadata: event.metadata,
      );
      
      if (response.success && response.data != null) {
        emit(MessageSent(message: response.data!));

        // Reload messages to get the updated list
        add(LoadMessages(chatId: event.chatId));
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to send message'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to send message: $e'));
    }
  }

  /// Create a new chat
  Future<void> _onCreateChat(CreateChat event, Emitter<MessagingState> emit) async {
    emit(const MessagingLoading());
    
    try {
      final response = await repository.createChat(
        type: event.type,
        name: event.name,
        description: event.description,
        participantIds: event.participantIds,
        settings: event.settings,
      );
      
      if (response.success && response.data != null) {
        emit(ChatCreated(chat: response.data!));

        // Reload chats to include the new one
        add(const LoadChats());
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to create chat'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to create chat: $e'));
    }
  }

  /// Join a chat
  Future<void> _onJoinChat(JoinChat event, Emitter<MessagingState> emit) async {
    try {
      final response = await repository.joinChat(event.chatId);
      
      if (response.success) {
        emit(ChatJoined(chatId: event.chatId));

        // Reload chats to include the joined chat
        add(const LoadChats());
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to join chat'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to join chat: $e'));
    }
  }

  /// Leave a chat
  Future<void> _onLeaveChat(LeaveChat event, Emitter<MessagingState> emit) async {
    try {
      final response = await repository.leaveChat(event.chatId);
      
      if (response.success) {
        emit(ChatLeft(chatId: event.chatId));

        // Reload chats to remove the left chat
        add(const LoadChats());
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to leave chat'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to leave chat: $e'));
    }
  }

  /// Start typing indicator
  Future<void> _onStartTyping(StartTyping event, Emitter<MessagingState> emit) async {
    try {
      await repository.startTyping(event.chatId);
      emit(TypingStarted(chatId: event.chatId));
    } catch (e) {
      // Typing indicators are not critical, so we don't emit error state
    }
  }

  /// Stop typing indicator
  Future<void> _onStopTyping(StopTyping event, Emitter<MessagingState> emit) async {
    try {
      await repository.stopTyping(event.chatId);
      emit(TypingStopped(chatId: event.chatId));
    } catch (e) {
      // Typing indicators are not critical, so we don't emit error state
    }
  }

  /// Mark message as read
  Future<void> _onMarkMessageAsRead(MarkMessageAsRead event, Emitter<MessagingState> emit) async {
    try {
      await repository.markMessageAsRead(event.messageId);
      emit(MessageMarkedAsRead(messageId: event.messageId));
    } catch (e) {
      // Read receipts are not critical, so we don't emit error state
    }
  }

  /// Add reaction to message
  Future<void> _onAddReaction(AddReaction event, Emitter<MessagingState> emit) async {
    try {
      await repository.addReaction(event.messageId, event.emoji);
      emit(ReactionAdded(messageId: event.messageId, emoji: event.emoji));
      
      // Reload messages to show updated reactions
      if (state is MessagesLoaded) {
        final messagesState = state as MessagesLoaded;
        add(LoadMessages(chatId: messagesState.chatId));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to add reaction: $e'));
    }
  }

  /// Remove reaction from message
  Future<void> _onRemoveReaction(RemoveReaction event, Emitter<MessagingState> emit) async {
    try {
      await repository.removeReaction(event.messageId, event.emoji);
      emit(ReactionRemoved(messageId: event.messageId, emoji: event.emoji));
      
      // Reload messages to show updated reactions
      if (state is MessagesLoaded) {
        final messagesState = state as MessagesLoaded;
        add(LoadMessages(chatId: messagesState.chatId));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to remove reaction: $e'));
    }
  }

  /// Search messages
  Future<void> _onSearchMessages(SearchMessages event, Emitter<MessagingState> emit) async {
    emit(const MessagingLoading());
    
    try {
      final response = await repository.searchMessages(
        chatId: event.chatId,
        query: event.query,
        page: event.page,
        pageSize: event.pageSize,
      );
      
      if (response.success && response.data != null) {
        emit(MessagesSearchResults(
          query: event.query,
          results: response.data!,
          hasMore: response.data!.length == event.pageSize,
        ));
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to search messages'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to search messages: $e'));
    }
  }

  /// Load chat details
  Future<void> _onLoadChatDetails(LoadChatDetails event, Emitter<MessagingState> emit) async {
    emit(const MessagingLoading());
    
    try {
      final response = await repository.getChatDetails(event.chatId);
      
      if (response.success && response.data != null) {
        emit(ChatDetailsLoaded(chat: response.data!));
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to load chat details'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to load chat details: $e'));
    }
  }

  /// Update chat settings
  Future<void> _onUpdateChatSettings(UpdateChatSettings event, Emitter<MessagingState> emit) async {
    try {
      final response = await repository.updateChatSettings(
        event.chatId,
        event.settings,
      );
      
      if (response.success && response.data != null) {
        emit(ChatSettingsUpdated(chat: response.data!));
      } else {
        emit(MessagingError(message: response.message ?? 'Failed to update chat settings'));
      }
    } catch (e) {
      emit(MessagingError(message: 'Failed to update chat settings: $e'));
    }
  }

  /// Handle received message from WebSocket
  Future<void> _onMessageReceived(MessageReceived event, Emitter<MessagingState> emit) async {
    emit(NewMessageReceived(message: event.message));
    
    // If we're currently viewing this chat, reload messages
    if (state is MessagesLoaded) {
      final messagesState = state as MessagesLoaded;
      if (messagesState.chatId == event.message.chatId) {
        add(LoadMessages(chatId: event.message.chatId));
      }
    }
  }

  /// Handle typing indicator from WebSocket
  Future<void> _onTypingIndicatorReceived(TypingIndicatorReceived event, Emitter<MessagingState> emit) async {
    if (event.isTyping) {
      emit(UserTyping(
        chatId: event.chatId,
        userId: event.userId,
        userName: event.userName,
      ));
    } else {
      emit(UserStoppedTyping(
        chatId: event.chatId,
        userId: event.userId,
      ));
    }
  }

  /// Handle user presence update from WebSocket
  Future<void> _onUserPresenceUpdated(UserPresenceUpdated event, Emitter<MessagingState> emit) async {
    emit(UserPresenceChanged(
      userId: event.userId,
      isOnline: event.isOnline,
      lastSeen: event.lastSeen,
    ));
  }
}
