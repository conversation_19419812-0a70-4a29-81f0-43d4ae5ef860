{"workflows": {"setup": {"name": "Initialize Development Environment", "description": "Set up Quester development environment with all dependencies", "steps": [{"name": "check_prerequisites", "command": "bash auto-setup.sh --check-deps", "description": "Verify Docker, Dart, and Flutter installations"}, {"name": "install_dependencies", "parallel": true, "commands": ["cd shared && dart pub get", "cd server && dart pub get", "cd client && flutter pub get"]}, {"name": "generate_code", "command": "cd shared && dart run build_runner build", "description": "Generate JSON serialization code"}, {"name": "start_services", "command": "bash docker.sh dev start", "description": "Start all development services"}, {"name": "health_check", "command": "bash docker.sh health", "description": "Verify all services are healthy"}]}, "feature_development": {"name": "Implement New Feature", "description": "Structured approach to adding new features", "parameters": ["feature_type", "feature_name"], "steps": [{"name": "analyze_requirements", "description": "Analyze feature requirements and dependencies"}, {"name": "update_shared_models", "command": "cd shared && dart run build_runner build", "description": "Update shared models and DTOs if needed"}, {"name": "implement_server_logic", "description": "Add API endpoints and business logic"}, {"name": "implement_client_ui", "description": "Create Flutter UI components"}, {"name": "run_tests", "parallel": true, "commands": ["cd shared && dart test", "cd server && dart test", "cd client && flutter test"]}, {"name": "integration_test", "command": "bash validate-project.sh", "description": "Run full project validation"}]}, "database_setup": {"name": "Database Integration", "description": "Set up and configure PostgreSQL integration", "steps": [{"name": "start_postgres", "command": "bash docker.sh service start postgres", "description": "Start PostgreSQL container"}, {"name": "run_migrations", "command": "bash docker.sh exec postgres psql -U quester -d quester_db -f /init-scripts/01-init.sql", "description": "Run database migrations"}, {"name": "seed_data", "command": "bash docker.sh exec postgres psql -U quester -d quester_db -f /init-scripts/04-sample-data.sql", "description": "Load sample data"}, {"name": "verify_connection", "command": "cd server && dart test test/database_connection_test.dart", "description": "Test database connectivity"}]}, "testing": {"name": "Comprehensive Testing", "description": "Run all tests across the monorepo", "steps": [{"name": "unit_tests", "parallel": true, "commands": ["cd shared && dart test --reporter=json", "cd server && dart test --reporter=json", "cd client && flutter test --reporter=json"]}, {"name": "integration_tests", "command": "cd server && dart test test/integration/", "description": "Run API integration tests"}, {"name": "end_to_end_tests", "command": "cd client && flutter test integration_test/", "description": "Run Flutter integration tests"}, {"name": "generate_coverage", "command": "bash validate-project.sh --coverage", "description": "Generate test coverage reports"}]}, "deployment": {"name": "Deploy Environment", "description": "Deploy to specified environment", "parameters": ["environment"], "steps": [{"name": "validate_code", "command": "bash validate-project.sh", "description": "Validate project before deployment"}, {"name": "build_client", "command": "cd client && flutter build web --release", "description": "Build optimized Flutter web application"}, {"name": "build_server", "command": "cd server && dart compile exe bin/server.dart -o build/server", "description": "Compile server executable"}, {"name": "deploy_services", "command": "bash docker.sh ${environment} deploy", "description": "Deploy to target environment"}, {"name": "health_check", "command": "bash docker.sh ${environment} health", "description": "Verify deployment health"}]}, "maintenance": {"name": "Maintenance Tasks", "description": "Regular maintenance and updates", "steps": [{"name": "update_dependencies", "parallel": true, "commands": ["cd shared && dart pub upgrade", "cd server && dart pub upgrade", "cd client && flutter pub upgrade"]}, {"name": "security_audit", "parallel": true, "commands": ["cd shared && dart pub audit", "cd server && dart pub audit", "cd client && flutter pub audit"]}, {"name": "code_analysis", "parallel": true, "commands": ["cd shared && dart analyze", "cd server && dart analyze", "cd client && flutter analyze"]}, {"name": "format_code", "parallel": true, "commands": ["cd shared && dart format --set-exit-if-changed .", "cd server && dart format --set-exit-if-changed .", "cd client && dart format --set-exit-if-changed ."]}]}}, "aliases": {"start": "setup", "dev": "feature_development", "test": "testing", "deploy": "deployment", "maintain": "maintenance"}, "environments": {"development": {"compose_file": "app/docker-compose.dev.yml", "ports": {"client": 3000, "server": 8080, "postgres": 5432, "redis": 6379}}, "staging": {"compose_file": "app/docker-compose.staging.yml", "ports": {"client": 3001, "server": 8081, "postgres": 5433, "redis": 6380}}, "production": {"compose_file": "app/docker-compose.prod.yml", "ports": {"client": 80, "server": 8080, "postgres": 5432, "redis": 6379}}}}