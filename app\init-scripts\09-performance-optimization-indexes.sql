-- Performance Optimization Database Indexes for Phase 5 Completion
-- Target: Sub-50ms API response times with <25ms database queries
-- Based on gamification system usage patterns from server v2.1.0

-- Enable timing for performance validation
\timing

-- Extension for advanced indexing features
CREATE EXTENSION IF NOT EXISTS btree_gin;
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Schema context
SET search_path TO quester;

-- Performance optimization indexes for gamification tables
-- Based on technical specification requirements

-- 1. User Points Performance Index
-- Optimizes user stats lookup and leaderboard queries
DROP INDEX IF EXISTS idx_user_points_performance;
CREATE INDEX idx_user_points_performance 
ON user_points (user_id, total_points DESC, current_level DESC, updated_at DESC)
INCLUDE (role);

-- 2. Leaderboard Query Optimization
-- Composite index for leaderboard ranking and filtering
DROP INDEX IF EXISTS idx_leaderboards_performance;
CREATE INDEX idx_leaderboards_performance 
ON leaderboards (leaderboard_type, rank ASC, last_updated DESC)
INCLUDE (user_id, score);

-- 3. Activity Log Time-Series Index with Partitioning Support
-- Optimizes activity tracking and analytics queries
DROP INDEX IF EXISTS idx_activity_log_timeseries;
CREATE INDEX idx_activity_log_timeseries 
ON activity_log (created_at DESC, user_id)
INCLUDE (activity_type, points_earned);

-- Additional index for activity type filtering
DROP INDEX IF EXISTS idx_activity_log_type_user;
CREATE INDEX idx_activity_log_type_user 
ON activity_log (activity_type, user_id, created_at DESC);

-- 4. User Achievements Lookup Index
-- Optimizes achievement checking and progress tracking
DROP INDEX IF EXISTS idx_user_achievements_lookup;
CREATE INDEX idx_user_achievements_lookup 
ON user_achievements (user_id, earned_date DESC, achievement_id)
INCLUDE (points_awarded);

-- Achievement-specific index for unlock notifications
DROP INDEX IF EXISTS idx_achievements_category_rarity;
CREATE INDEX idx_achievements_category_rarity 
ON achievements (category, rarity, points_reward DESC);

-- 5. Streaks Performance Index
-- Optimizes streak calculation and bonus tracking
DROP INDEX IF EXISTS idx_streaks_active;
CREATE INDEX idx_streaks_active 
ON streaks (user_id, last_activity_date DESC)
INCLUDE (current_streak, longest_streak);

-- 6. User Rewards Purchase History Index
-- Optimizes reward shop and purchase validation
DROP INDEX IF EXISTS idx_user_rewards_history;
CREATE INDEX idx_user_rewards_history 
ON user_rewards (user_id, purchased_date DESC)
INCLUDE (reward_id, points_spent);

-- Reward catalog optimization
DROP INDEX IF EXISTS idx_rewards_cost_type;
CREATE INDEX idx_rewards_cost_type 
ON rewards (type, point_cost ASC)
WHERE point_cost IS NOT NULL;

-- 7. Enterprise Features Performance Indexes

-- Organization member lookup optimization
DROP INDEX IF EXISTS idx_org_members_user_role;
CREATE INDEX idx_org_members_user_role 
ON organization_members (user_id, organization_id)
INCLUDE (role, joined_date, is_active);

-- Organization role permissions index
DROP INDEX IF EXISTS idx_org_roles_permissions;
CREATE INDEX idx_org_roles_permissions 
ON organization_roles (organization_id, name)
INCLUDE (permissions);

-- 8. Authentication and Security Indexes

-- User session lookup optimization  
DROP INDEX IF EXISTS idx_user_sessions_active;
CREATE INDEX idx_user_sessions_active 
ON user_sessions (user_id, expires_at DESC)
WHERE is_active = true;

-- Audit log performance index
DROP INDEX IF EXISTS idx_audit_log_performance;
CREATE INDEX idx_audit_log_performance 
ON audit_log (created_at DESC, user_id, event_type);

-- 9. WebSocket and Real-time Feature Indexes

-- Active WebSocket connections tracking
DROP INDEX IF EXISTS idx_websocket_connections_active;
CREATE INDEX idx_websocket_connections_active 
ON websocket_connections (user_id, connected_at DESC)
WHERE is_connected = true;

-- Real-time notifications index
DROP INDEX IF EXISTS idx_notifications_unread;
CREATE INDEX idx_notifications_unread 
ON notifications (user_id, created_at DESC)
WHERE is_read = false;

-- 10. API Performance Statistics Index

-- Track API endpoint usage patterns
DROP INDEX IF EXISTS idx_api_usage_stats;
CREATE INDEX idx_api_usage_stats 
ON api_usage_log (endpoint, created_at DESC)
INCLUDE (response_time_ms, status_code);

-- Performance monitoring index
DROP INDEX IF EXISTS idx_performance_metrics_timeseries;
CREATE INDEX idx_performance_metrics_timeseries 
ON performance_metrics (metric_type, recorded_at DESC)
INCLUDE (metric_value);

-- 11. Partial Indexes for Common Queries

-- Active users only (last 30 days)
DROP INDEX IF EXISTS idx_users_active_30d;
CREATE INDEX idx_users_active_30d 
ON users (last_login_at DESC, id)
WHERE last_login_at >= NOW() - INTERVAL '30 days';

-- High-value achievements (rare and above)
DROP INDEX IF EXISTS idx_achievements_high_value;
CREATE INDEX idx_achievements_high_value 
ON achievements (rarity, points_reward DESC)
WHERE rarity IN ('rare', 'epic', 'legendary');

-- Recent activity (last 7 days)
DROP INDEX IF EXISTS idx_activity_recent;
CREATE INDEX idx_activity_recent 
ON activity_log (user_id, created_at DESC, points_earned)
WHERE created_at >= NOW() - INTERVAL '7 days';

-- 12. Full-Text Search Indexes (if needed for enterprise features)

-- Organization search
DROP INDEX IF EXISTS idx_organizations_search;
CREATE INDEX idx_organizations_search 
ON organizations USING gin(to_tsvector('english', name || ' ' || description));

-- User search
DROP INDEX IF EXISTS idx_users_search;
CREATE INDEX idx_users_search 
ON users USING gin(to_tsvector('english', username || ' ' || email || ' ' || display_name));

-- Connection Pool and Query Optimization Settings
-- These settings will be applied per connection

-- Optimize for read-heavy workload with some writes
-- Set work_mem for complex queries
COMMENT ON SCHEMA quester IS 'Recommended connection settings:
work_mem = 16MB for complex analytics queries
shared_buffers = 25% of RAM
effective_cache_size = 75% of RAM
random_page_cost = 1.1 (SSD optimized)
max_connections = 50 (production), 20 (development)
connection timeout = 30s, idle timeout = 5min';

-- Create materialized views for expensive aggregations
-- Leaderboard snapshot for faster loading
DROP MATERIALIZED VIEW IF EXISTS mv_leaderboard_snapshot;
CREATE MATERIALIZED VIEW mv_leaderboard_snapshot AS
SELECT 
    l.leaderboard_type,
    l.user_id,
    l.rank,
    l.score,
    u.username,
    u.display_name,
    up.role,
    l.last_updated
FROM leaderboards l
JOIN users u ON u.id = l.user_id
JOIN user_points up ON up.user_id = l.user_id
WHERE l.rank <= 100  -- Top 100 for each leaderboard type
ORDER BY l.leaderboard_type, l.rank;

-- Index for materialized view
CREATE UNIQUE INDEX idx_mv_leaderboard_unique 
ON mv_leaderboard_snapshot (leaderboard_type, rank);

-- User statistics summary for dashboard
DROP MATERIALIZED VIEW IF EXISTS mv_user_stats_summary;
CREATE MATERIALIZED VIEW mv_user_stats_summary AS
SELECT 
    u.id as user_id,
    u.username,
    up.total_points,
    up.current_level,
    up.role,
    s.current_streak,
    s.longest_streak,
    COUNT(ua.id) as achievement_count,
    SUM(ua.points_awarded) as achievement_points,
    MAX(al.created_at) as last_activity
FROM users u
LEFT JOIN user_points up ON up.user_id = u.id
LEFT JOIN streaks s ON s.user_id = u.id
LEFT JOIN user_achievements ua ON ua.user_id = u.id
LEFT JOIN activity_log al ON al.user_id = u.id
WHERE u.is_active = true
GROUP BY u.id, u.username, up.total_points, up.current_level, up.role, s.current_streak, s.longest_streak;

-- Index for user stats materialized view
CREATE UNIQUE INDEX idx_mv_user_stats_unique 
ON mv_user_stats_summary (user_id);

-- Performance monitoring functions
-- Function to analyze slow queries
CREATE OR REPLACE FUNCTION analyze_slow_queries(threshold_ms integer DEFAULT 100)
RETURNS TABLE (
    query text,
    calls bigint,
    total_time double precision,
    mean_time double precision,
    max_time double precision
) LANGUAGE sql AS $$
    SELECT 
        query,
        calls,
        total_time,
        mean_time,
        max_time
    FROM pg_stat_statements 
    WHERE mean_time > threshold_ms
    ORDER BY mean_time DESC
    LIMIT 20;
$$;

-- Function to refresh materialized views efficiently
CREATE OR REPLACE FUNCTION refresh_performance_views()
RETURNS void LANGUAGE plpgsql AS $$
BEGIN
    -- Refresh leaderboard snapshot
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_leaderboard_snapshot;
    
    -- Refresh user stats summary
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_stats_summary;
    
    -- Log refresh completion
    INSERT INTO performance_log (metric_type, metric_value, created_at)
    VALUES ('materialized_view_refresh', 1, NOW());
    
    RAISE NOTICE 'Performance materialized views refreshed at %', NOW();
END;
$$;

-- Index maintenance function
CREATE OR REPLACE FUNCTION analyze_index_usage()
RETURNS TABLE (
    schemaname text,
    tablename text,
    indexname text,
    idx_tup_read bigint,
    idx_tup_fetch bigint,
    usage_ratio numeric
) LANGUAGE sql AS $$
    SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch,
        CASE 
            WHEN idx_tup_read > 0 
            THEN round((idx_tup_fetch::numeric / idx_tup_read::numeric) * 100, 2)
            ELSE 0
        END as usage_ratio
    FROM pg_stat_user_indexes 
    WHERE schemaname = 'quester'
    ORDER BY usage_ratio DESC;
$$;

-- Performance validation queries
-- Test query performance with EXPLAIN ANALYZE

-- Validate user points lookup performance (target: <5ms)
EXPLAIN (ANALYZE, BUFFERS) 
SELECT up.total_points, up.current_level, up.role::text, s.current_streak
FROM user_points up
LEFT JOIN streaks s ON s.user_id = up.user_id
WHERE up.user_id = gen_random_uuid();

-- Validate leaderboard query performance (target: <10ms)
EXPLAIN (ANALYZE, BUFFERS)
SELECT l.rank, l.score, u.username, up.role::text
FROM leaderboards l
JOIN users u ON u.id = l.user_id
JOIN user_points up ON up.user_id = l.user_id
WHERE l.leaderboard_type = 'global'
ORDER BY l.rank
LIMIT 50;

-- Validate activity log query performance (target: <8ms)
EXPLAIN (ANALYZE, BUFFERS)
SELECT activity_type, points_earned, description, created_at
FROM activity_log
WHERE user_id = gen_random_uuid()
  AND created_at >= NOW() - INTERVAL '7 days'
ORDER BY created_at DESC
LIMIT 20;

-- Create performance baseline
INSERT INTO performance_baseline (
    metric_name,
    target_value_ms,
    description,
    created_at
) VALUES 
    ('user_points_lookup', 5, 'User points and stats lookup query', NOW()),
    ('leaderboard_query', 10, 'Leaderboard ranking query with user details', NOW()),
    ('activity_log_recent', 8, 'Recent user activity log query', NOW()),
    ('achievement_check', 12, 'Achievement eligibility check query', NOW()),
    ('user_stats_summary', 15, 'Complete user statistics summary', NOW())
ON CONFLICT (metric_name) DO UPDATE SET
    target_value_ms = EXCLUDED.target_value_ms,
    updated_at = NOW();

-- Performance monitoring triggers
-- Trigger to log slow queries automatically
CREATE OR REPLACE FUNCTION log_slow_query() 
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    -- This would be implemented with pg_stat_statements
    -- For now, just log the operation
    INSERT INTO query_performance_log (
        table_name,
        operation_type, 
        execution_time_estimate,
        created_at
    ) VALUES (
        TG_TABLE_NAME,
        TG_OP,
        EXTRACT(EPOCH FROM NOW() - statement_timestamp()) * 1000,
        NOW()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Apply performance monitoring to key tables
-- (Commented out to avoid overhead in development)
-- CREATE TRIGGER trig_user_points_performance 
--   AFTER INSERT OR UPDATE OR DELETE ON user_points
--   FOR EACH STATEMENT EXECUTE FUNCTION log_slow_query();

-- Performance optimization summary
DO $$
BEGIN
    RAISE NOTICE 'Performance optimization indexes created successfully!';
    RAISE NOTICE 'Target metrics:';
    RAISE NOTICE '  - API response time: <50ms';
    RAISE NOTICE '  - Database query time: <25ms average, <5ms for simple lookups';
    RAISE NOTICE '  - Cache hit ratio: >95%%';
    RAISE NOTICE '  - Concurrent users: 1000+ supported';
    RAISE NOTICE '';
    RAISE NOTICE 'Created indexes for:';
    RAISE NOTICE '  - User points and stats lookup';
    RAISE NOTICE '  - Leaderboard queries and ranking';
    RAISE NOTICE '  - Activity log time-series queries';
    RAISE NOTICE '  - Achievement checking and progress';
    RAISE NOTICE '  - Streak calculations';
    RAISE NOTICE '  - Enterprise organization features';
    RAISE NOTICE '  - Authentication and security';
    RAISE NOTICE '  - WebSocket and real-time features';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '  1. Monitor query performance with analyze_slow_queries()';
    RAISE NOTICE '  2. Refresh materialized views with refresh_performance_views()';
    RAISE NOTICE '  3. Validate index usage with analyze_index_usage()';
    RAISE NOTICE '  4. Run ANALYZE on all tables to update statistics';
END $$;

-- Update table statistics for optimal query planning
ANALYZE user_points;
ANALYZE leaderboards;
ANALYZE activity_log;
ANALYZE user_achievements;
ANALYZE achievements;
ANALYZE streaks;
ANALYZE user_rewards;
ANALYZE rewards;
ANALYZE users;
ANALYZE organization_members;
ANALYZE organization_roles;

-- Final performance validation
\echo 'Database performance optimization completed!'
\echo 'Run the following to validate performance:'
\echo 'SELECT * FROM analyze_slow_queries(25);'
\echo 'SELECT * FROM analyze_index_usage();'