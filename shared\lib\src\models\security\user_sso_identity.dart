import 'package:equatable/equatable.dart';

/// User SSO Identity model for database table: user_sso_identities
class UserSSOIdentity extends Equatable {
  /// Unique identity identifier
  final String id;

  /// Internal user ID
  final String userId;

  /// SSO provider ID
  final String providerId;

  /// External identity ID from the SSO provider
  final String externalId;

  /// External username from the SSO provider
  final String? externalUsername;

  /// External email from the SSO provider
  final String? externalEmail;

  /// Provider-specific attributes (JSONB)
  final Map<String, dynamic>? providerAttributes;

  /// Last login timestamp
  final DateTime? lastLogin;

  /// Whether this identity is active
  final bool isActive;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const UserSSOIdentity({
    required this.id,
    required this.userId,
    required this.providerId,
    required this.externalId,
    this.externalUsername,
    this.externalEmail,
    this.providerAttributes,
    this.lastLogin,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create empty UserSSOIdentity for testing
  factory UserSSOIdentity.empty() {
    return UserSSOIdentity(
      id: '',
      userId: '',
      providerId: '',
      externalId: '',
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create UserSSOIdentity from JSON
  factory UserSSOIdentity.fromJson(Map<String, dynamic> json) {
    return UserSSOIdentity(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      providerId: json['provider_id'] as String,
      externalId: json['external_id'] as String,
      externalUsername: json['external_username'] as String?,
      externalEmail: json['external_email'] as String?,
      providerAttributes: json['provider_attributes'] != null
          ? Map<String, dynamic>.from(json['provider_attributes'] as Map)
          : null,
      lastLogin: json['last_login'] != null
          ? DateTime.parse(json['last_login'] as String)
          : null,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert UserSSOIdentity to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'provider_id': providerId,
      'external_id': externalId,
      'external_username': externalUsername,
      'external_email': externalEmail,
      'provider_attributes': providerAttributes,
      'last_login': lastLogin?.toIso8601String(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  UserSSOIdentity copyWith({
    String? id,
    String? userId,
    String? providerId,
    String? externalId,
    String? externalUsername,
    String? externalEmail,
    Map<String, dynamic>? providerAttributes,
    DateTime? lastLogin,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserSSOIdentity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      providerId: providerId ?? this.providerId,
      externalId: externalId ?? this.externalId,
      externalUsername: externalUsername ?? this.externalUsername,
      externalEmail: externalEmail ?? this.externalEmail,
      providerAttributes: providerAttributes ?? this.providerAttributes,
      lastLogin: lastLogin ?? this.lastLogin,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if the identity has recent login activity
  bool get hasRecentActivity {
    if (lastLogin == null) return false;
    return DateTime.now().difference(lastLogin!).inDays <= 30;
  }

  /// Get display name from provider attributes or fallbacks
  String get displayName {
    if (providerAttributes != null) {
      final attrs = providerAttributes!;
      if (attrs.containsKey('name') && attrs['name'] != null) {
        return attrs['name'] as String;
      }
      if (attrs.containsKey('displayName') && attrs['displayName'] != null) {
        return attrs['displayName'] as String;
      }
      if (attrs.containsKey('given_name') && attrs.containsKey('family_name')) {
        final given = attrs['given_name'] as String?;
        final family = attrs['family_name'] as String?;
        if (given != null && family != null) {
          return '$given $family';
        }
      }
    }
    return externalUsername ?? externalEmail ?? 'Unknown User';
  }

  /// Get email from provider attributes or fallback
  String? get effectiveEmail {
    if (providerAttributes != null) {
      final attrs = providerAttributes!;
      if (attrs.containsKey('email') && attrs['email'] != null) {
        return attrs['email'] as String;
      }
    }
    return externalEmail;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        providerId,
        externalId,
        externalUsername,
        externalEmail,
        providerAttributes,
        lastLogin,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}