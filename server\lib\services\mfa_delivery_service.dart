/// MFA Delivery Service
library;

/// Comprehensive service for delivering MFA codes via SMS and email.
/// Supports multiple providers, templates, rate limiting, and delivery
/// confirmation tracking.
/// 
/// Key Features:
/// - SMS delivery via multiple providers (Twilio, AWS SNS, etc.)
/// - Email delivery with customizable templates
/// - Code generation with expiration and rate limiting  
/// - Delivery status tracking and confirmation
/// - Provider failover and load balancing
/// - Template customization and localization
/// - Delivery analytics and monitoring

// import 'dart:convert'; // Not currently used
// import 'dart:io'; // Not currently used
import 'dart:math';
import '../services/database_service.dart';
// import 'package:shared/shared.dart'; // Not currently used

/// MFA code delivery methods
enum MFADeliveryMethod {
  sms('sms'),
  email('email'),
  voice('voice'); // For future implementation

  const MFADeliveryMethod(this.value);
  final String value;
}

/// SMS provider types
enum SMSProviderType {
  twilio('twilio'),
  awsSns('aws_sns'),
  nexmo('nexmo'),
  messagebird('messagebird'),
  test('test'); // For testing

  const SMSProviderType(this.value);
  final String value;
}

/// Email provider types
enum EmailProviderType {
  smtp('smtp'),
  sendgrid('sendgrid'),
  mailgun('mailgun'),
  awsSes('aws_ses'),
  test('test'); // For testing

  const EmailProviderType(this.value);
  final String value;
}

/// Delivery configuration
class MFADeliveryConfig {
  final int codeLength;
  final int expirationMinutes;
  final int maxAttemptsPerHour;
  final int maxDeliveryRetries;
  final Map<String, dynamic> smsConfig;
  final Map<String, dynamic> emailConfig;
  final Map<String, String> templates;

  const MFADeliveryConfig({
    this.codeLength = 6,
    this.expirationMinutes = 10,
    this.maxAttemptsPerHour = 6,
    this.maxDeliveryRetries = 3,
    this.smsConfig = const {},
    this.emailConfig = const {},
    this.templates = const {},
  });

  factory MFADeliveryConfig.fromJson(Map<String, dynamic> json) {
    return MFADeliveryConfig(
      codeLength: json['code_length'] as int? ?? 6,
      expirationMinutes: json['expiration_minutes'] as int? ?? 10,
      maxAttemptsPerHour: json['max_attempts_per_hour'] as int? ?? 6,
      maxDeliveryRetries: json['max_delivery_retries'] as int? ?? 3,
      smsConfig: Map<String, dynamic>.from(json['sms_config'] ?? {}),
      emailConfig: Map<String, dynamic>.from(json['email_config'] ?? {}),
      templates: Map<String, String>.from(json['templates'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code_length': codeLength,
      'expiration_minutes': expirationMinutes,
      'max_attempts_per_hour': maxAttemptsPerHour,
      'max_delivery_retries': maxDeliveryRetries,
      'sms_config': smsConfig,
      'email_config': emailConfig,
      'templates': templates,
    };
  }
}

/// MFA delivery attempt
class MFADeliveryAttempt {
  final String id;
  final String userId;
  final String code;
  final MFADeliveryMethod method;
  final String destination;
  final DateTime expiresAt;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;

  const MFADeliveryAttempt({
    required this.id,
    required this.userId,
    required this.code,
    required this.method,
    required this.destination,
    required this.expiresAt,
    required this.createdAt,
    this.metadata = const {},
  });

  factory MFADeliveryAttempt.fromJson(Map<String, dynamic> json) {
    return MFADeliveryAttempt(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      code: json['code'] as String,
      method: MFADeliveryMethod.values.firstWhere(
        (e) => e.value == json['method'],
        orElse: () => MFADeliveryMethod.sms,
      ),
      destination: json['destination'] as String,
      expiresAt: DateTime.parse(json['expires_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'code': code,
      'method': method.value,
      'destination': destination,
      'expires_at': expiresAt.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Delivery result
class MFADeliveryResult {
  final bool success;
  final String attemptId;
  final MFADeliveryMethod method;
  final String? messageId;
  final String? error;
  final Map<String, dynamic> metadata;

  const MFADeliveryResult({
    required this.success,
    required this.attemptId,
    required this.method,
    this.messageId,
    this.error,
    this.metadata = const {},
  });

  factory MFADeliveryResult.success({
    required String attemptId,
    required MFADeliveryMethod method,
    String? messageId,
    Map<String, dynamic> metadata = const {},
  }) {
    return MFADeliveryResult(
      success: true,
      attemptId: attemptId,
      method: method,
      messageId: messageId,
      metadata: metadata,
    );
  }

  factory MFADeliveryResult.failure({
    required String attemptId,
    required MFADeliveryMethod method,
    required String error,
    Map<String, dynamic> metadata = const {},
  }) {
    return MFADeliveryResult(
      success: false,
      attemptId: attemptId,
      method: method,
      error: error,
      metadata: metadata,
    );
  }
}

/// Main MFA Delivery Service
class MFADeliveryService {
  final DatabaseService _databaseService;
  final MFADeliveryConfig _config;
  
  // Rate limiting tracking
  final Map<String, List<DateTime>> _deliveryAttempts = {};
  
  // Active delivery attempts cache
  final Map<String, MFADeliveryAttempt> _activeAttempts = {};
  
  // Provider configurations
  Map<SMSProviderType, Map<String, dynamic>> _smsProviders = {};
  Map<EmailProviderType, Map<String, dynamic>> _emailProviders = {};

  MFADeliveryService(this._databaseService, {MFADeliveryConfig? config})
      : _config = config ?? const MFADeliveryConfig() {
    _initializeProviders();
  }

  /// Send MFA code via SMS
  Future<MFADeliveryResult> sendSMSCode({
    required String userId,
    required String phoneNumber,
    String? organizationId,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // Check rate limiting
      if (!_checkRateLimit(userId)) {
        return MFADeliveryResult.failure(
          attemptId: '',
          method: MFADeliveryMethod.sms,
          error: 'Rate limit exceeded. Too many SMS requests.',
          metadata: {'rate_limited': true},
        );
      }

      // Validate phone number
      final validatedPhone = _validatePhoneNumber(phoneNumber);
      if (validatedPhone == null) {
        return MFADeliveryResult.failure(
          attemptId: '',
          method: MFADeliveryMethod.sms,
          error: 'Invalid phone number format',
          metadata: {'invalid_phone': phoneNumber},
        );
      }

      // Generate MFA code
      final code = _generateMFACode();
      final attemptId = _generateAttemptId();
      final expiresAt = DateTime.now().add(Duration(minutes: _config.expirationMinutes));

      // Create delivery attempt record
      final attempt = MFADeliveryAttempt(
        id: attemptId,
        userId: userId,
        code: code,
        method: MFADeliveryMethod.sms,
        destination: validatedPhone,
        expiresAt: expiresAt,
        createdAt: DateTime.now(),
        metadata: metadata,
      );

      // Store attempt
      _activeAttempts[attemptId] = attempt;
      await _storeDeliveryAttempt(attempt);

      // Send SMS
      final deliveryResult = await _sendSMS(
        phoneNumber: validatedPhone,
        code: code,
        attemptId: attemptId,
        userId: userId,
        metadata: metadata,
      );

      // Log delivery attempt
      await _logDeliveryEvent(
        userId: userId,
        organizationId: organizationId,
        method: MFADeliveryMethod.sms,
        attemptId: attemptId,
        success: deliveryResult.success,
        details: {
          'destination': _maskPhoneNumber(validatedPhone),
          'message_id': deliveryResult.messageId,
          'error': deliveryResult.error,
        },
      );

      return deliveryResult;

    } catch (e) {
      return MFADeliveryResult.failure(
        attemptId: '',
        method: MFADeliveryMethod.sms,
        error: 'SMS delivery error: $e',
        metadata: {'exception': e.toString()},
      );
    }
  }

  /// Send MFA code via email
  Future<MFADeliveryResult> sendEmailCode({
    required String userId,
    required String email,
    String? organizationId,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // Check rate limiting
      if (!_checkRateLimit(userId)) {
        return MFADeliveryResult.failure(
          attemptId: '',
          method: MFADeliveryMethod.email,
          error: 'Rate limit exceeded. Too many email requests.',
          metadata: {'rate_limited': true},
        );
      }

      // Validate email
      if (!_isValidEmail(email)) {
        return MFADeliveryResult.failure(
          attemptId: '',
          method: MFADeliveryMethod.email,
          error: 'Invalid email address format',
          metadata: {'invalid_email': email},
        );
      }

      // Generate MFA code
      final code = _generateMFACode();
      final attemptId = _generateAttemptId();
      final expiresAt = DateTime.now().add(Duration(minutes: _config.expirationMinutes));

      // Create delivery attempt record
      final attempt = MFADeliveryAttempt(
        id: attemptId,
        userId: userId,
        code: code,
        method: MFADeliveryMethod.email,
        destination: email,
        expiresAt: expiresAt,
        createdAt: DateTime.now(),
        metadata: metadata,
      );

      // Store attempt
      _activeAttempts[attemptId] = attempt;
      await _storeDeliveryAttempt(attempt);

      // Send email
      final deliveryResult = await _sendEmail(
        email: email,
        code: code,
        attemptId: attemptId,
        userId: userId,
        metadata: metadata,
      );

      // Log delivery attempt
      await _logDeliveryEvent(
        userId: userId,
        organizationId: organizationId,
        method: MFADeliveryMethod.email,
        attemptId: attemptId,
        success: deliveryResult.success,
        details: {
          'destination': _maskEmail(email),
          'message_id': deliveryResult.messageId,
          'error': deliveryResult.error,
        },
      );

      return deliveryResult;

    } catch (e) {
      return MFADeliveryResult.failure(
        attemptId: '',
        method: MFADeliveryMethod.email,
        error: 'Email delivery error: $e',
        metadata: {'exception': e.toString()},
      );
    }
  }

  /// Validate MFA code
  Future<bool> validateCode({
    required String userId,
    required String code,
    required MFADeliveryMethod method,
  }) async {
    try {
      // Find active attempts for this user and method
      final userAttempts = _activeAttempts.values
          .where((attempt) => 
              attempt.userId == userId && 
              attempt.method == method &&
              attempt.expiresAt.isAfter(DateTime.now()))
          .toList();

      // Sort by creation time (most recent first)
      userAttempts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Check code against attempts
      for (final attempt in userAttempts) {
        if (attempt.code == code) {
          // Remove used attempt
          _activeAttempts.remove(attempt.id);
          await _markAttemptAsUsed(attempt.id);

          await _logDeliveryEvent(
            userId: userId,
            organizationId: null,
            method: method,
            attemptId: attempt.id,
            success: true,
            details: {
              'validation_successful': true,
              'attempt_age_seconds': DateTime.now().difference(attempt.createdAt).inSeconds,
            },
          );

          return true;
        }
      }

      await _logDeliveryEvent(
        userId: userId,
        organizationId: null,
        method: method,
        attemptId: '',
        success: false,
        details: {
          'validation_failed': true,
          'code_provided': code.isNotEmpty,
          'active_attempts': userAttempts.length,
        },
      );

      return false;

    } catch (e) {
      print('MFA code validation error: $e');
      return false;
    }
  }

  /// Send SMS via configured provider
  Future<MFADeliveryResult> _sendSMS({
    required String phoneNumber,
    required String code,
    required String attemptId,
    required String userId,
    Map<String, dynamic> metadata = const {},
  }) async {
    // Try each SMS provider until one succeeds
    for (final provider in _smsProviders.entries) {
      try {
        final result = await _sendViaSMSProvider(
          provider: provider.key,
          config: provider.value,
          phoneNumber: phoneNumber,
          code: code,
          attemptId: attemptId,
        );

        if (result.success) {
          return result;
        }

        // Log provider failure and try next
        print('SMS provider ${provider.key.value} failed: ${result.error}');
        
      } catch (e) {
        print('SMS provider ${provider.key.value} exception: $e');
        continue;
      }
    }

    return MFADeliveryResult.failure(
      attemptId: attemptId,
      method: MFADeliveryMethod.sms,
      error: 'All SMS providers failed',
      metadata: {'providers_tried': _smsProviders.keys.map((k) => k.value).toList()},
    );
  }

  /// Send email via configured provider
  Future<MFADeliveryResult> _sendEmail({
    required String email,
    required String code,
    required String attemptId,
    required String userId,
    Map<String, dynamic> metadata = const {},
  }) async {
    // Try each email provider until one succeeds
    for (final provider in _emailProviders.entries) {
      try {
        final result = await _sendViaEmailProvider(
          provider: provider.key,
          config: provider.value,
          email: email,
          code: code,
          attemptId: attemptId,
        );

        if (result.success) {
          return result;
        }

        // Log provider failure and try next
        print('Email provider ${provider.key.value} failed: ${result.error}');
        
      } catch (e) {
        print('Email provider ${provider.key.value} exception: $e');
        continue;
      }
    }

    return MFADeliveryResult.failure(
      attemptId: attemptId,
      method: MFADeliveryMethod.email,
      error: 'All email providers failed',
      metadata: {'providers_tried': _emailProviders.keys.map((k) => k.value).toList()},
    );
  }

  /// Send via specific SMS provider
  Future<MFADeliveryResult> _sendViaSMSProvider({
    required SMSProviderType provider,
    required Map<String, dynamic> config,
    required String phoneNumber,
    required String code,
    required String attemptId,
  }) async {
    final message = _formatSMSMessage(code);

    switch (provider) {
      case SMSProviderType.test:
        // Test provider - just log the message
        print('TEST SMS to $phoneNumber: $message');
        return MFADeliveryResult.success(
          attemptId: attemptId,
          method: MFADeliveryMethod.sms,
          messageId: 'test_${DateTime.now().millisecondsSinceEpoch}',
          metadata: {'provider': 'test'},
        );

      case SMSProviderType.twilio:
        return await _sendViaTwilio(config, phoneNumber, message, attemptId);

      case SMSProviderType.awsSns:
        return await _sendViaAWSSNS(config, phoneNumber, message, attemptId);

      default:
        return MFADeliveryResult.failure(
          attemptId: attemptId,
          method: MFADeliveryMethod.sms,
          error: 'SMS provider not implemented: ${provider.value}',
        );
    }
  }

  /// Send via specific email provider
  Future<MFADeliveryResult> _sendViaEmailProvider({
    required EmailProviderType provider,
    required Map<String, dynamic> config,
    required String email,
    required String code,
    required String attemptId,
  }) async {
    final subject = _formatEmailSubject();
    final body = _formatEmailBody(code);

    switch (provider) {
      case EmailProviderType.test:
        // Test provider - just log the email
        print('TEST EMAIL to $email: $subject\n$body');
        return MFADeliveryResult.success(
          attemptId: attemptId,
          method: MFADeliveryMethod.email,
          messageId: 'test_${DateTime.now().millisecondsSinceEpoch}',
          metadata: {'provider': 'test'},
        );

      case EmailProviderType.smtp:
        return await _sendViaSMTP(config, email, subject, body, attemptId);

      case EmailProviderType.sendgrid:
        return await _sendViaSendGrid(config, email, subject, body, attemptId);

      default:
        return MFADeliveryResult.failure(
          attemptId: attemptId,
          method: MFADeliveryMethod.email,
          error: 'Email provider not implemented: ${provider.value}',
        );
    }
  }

  /// Send via Twilio
  Future<MFADeliveryResult> _sendViaTwilio(
    Map<String, dynamic> config,
    String phoneNumber,
    String message,
    String attemptId,
  ) async {
    // Simplified Twilio implementation
    // In production, you would use the Twilio SDK
    
    final accountSid = config['account_sid'] as String?;
    final authToken = config['auth_token'] as String?;
    final fromNumber = config['from_number'] as String?;

    if (accountSid == null || authToken == null || fromNumber == null) {
      return MFADeliveryResult.failure(
        attemptId: attemptId,
        method: MFADeliveryMethod.sms,
        error: 'Twilio configuration incomplete',
      );
    }

    try {
      // Simulate Twilio API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      return MFADeliveryResult.success(
        attemptId: attemptId,
        method: MFADeliveryMethod.sms,
        messageId: 'twilio_${DateTime.now().millisecondsSinceEpoch}',
        metadata: {'provider': 'twilio'},
      );

    } catch (e) {
      return MFADeliveryResult.failure(
        attemptId: attemptId,
        method: MFADeliveryMethod.sms,
        error: 'Twilio API error: $e',
      );
    }
  }

  /// Send via AWS SNS
  Future<MFADeliveryResult> _sendViaAWSSNS(
    Map<String, dynamic> config,
    String phoneNumber,
    String message,
    String attemptId,
  ) async {
    // Simplified AWS SNS implementation
    // In production, you would use the AWS SDK
    
    try {
      // Simulate AWS SNS API call
      await Future.delayed(const Duration(milliseconds: 300));
      
      return MFADeliveryResult.success(
        attemptId: attemptId,
        method: MFADeliveryMethod.sms,
        messageId: 'sns_${DateTime.now().millisecondsSinceEpoch}',
        metadata: {'provider': 'aws_sns'},
      );

    } catch (e) {
      return MFADeliveryResult.failure(
        attemptId: attemptId,
        method: MFADeliveryMethod.sms,
        error: 'AWS SNS error: $e',
      );
    }
  }

  /// Send via SMTP
  Future<MFADeliveryResult> _sendViaSMTP(
    Map<String, dynamic> config,
    String email,
    String subject,
    String body,
    String attemptId,
  ) async {
    // Simplified SMTP implementation
    // In production, you would use an SMTP library
    
    try {
      // Simulate SMTP sending
      await Future.delayed(const Duration(milliseconds: 800));
      
      return MFADeliveryResult.success(
        attemptId: attemptId,
        method: MFADeliveryMethod.email,
        messageId: 'smtp_${DateTime.now().millisecondsSinceEpoch}',
        metadata: {'provider': 'smtp'},
      );

    } catch (e) {
      return MFADeliveryResult.failure(
        attemptId: attemptId,
        method: MFADeliveryMethod.email,
        error: 'SMTP error: $e',
      );
    }
  }

  /// Send via SendGrid
  Future<MFADeliveryResult> _sendViaSendGrid(
    Map<String, dynamic> config,
    String email,
    String subject,
    String body,
    String attemptId,
  ) async {
    // Simplified SendGrid implementation
    // In production, you would use the SendGrid SDK
    
    try {
      // Simulate SendGrid API call
      await Future.delayed(const Duration(milliseconds: 400));
      
      return MFADeliveryResult.success(
        attemptId: attemptId,
        method: MFADeliveryMethod.email,
        messageId: 'sendgrid_${DateTime.now().millisecondsSinceEpoch}',
        metadata: {'provider': 'sendgrid'},
      );

    } catch (e) {
      return MFADeliveryResult.failure(
        attemptId: attemptId,
        method: MFADeliveryMethod.email,
        error: 'SendGrid error: $e',
      );
    }
  }

  /// Initialize provider configurations
  void _initializeProviders() {
    // Initialize with test providers by default
    _smsProviders = {
      SMSProviderType.test: {'enabled': true},
    };
    
    _emailProviders = {
      EmailProviderType.test: {'enabled': true},
    };

    // Add real providers from configuration
    final smsConfig = _config.smsConfig;
    final emailConfig = _config.emailConfig;

    // Configure SMS providers
    if (smsConfig['twilio'] != null) {
      _smsProviders[SMSProviderType.twilio] = Map<String, dynamic>.from(smsConfig['twilio']);
    }

    if (smsConfig['aws_sns'] != null) {
      _smsProviders[SMSProviderType.awsSns] = Map<String, dynamic>.from(smsConfig['aws_sns']);
    }

    // Configure email providers
    if (emailConfig['smtp'] != null) {
      _emailProviders[EmailProviderType.smtp] = Map<String, dynamic>.from(emailConfig['smtp']);
    }

    if (emailConfig['sendgrid'] != null) {
      _emailProviders[EmailProviderType.sendgrid] = Map<String, dynamic>.from(emailConfig['sendgrid']);
    }
  }

  /// Generate MFA code
  String _generateMFACode() {
    final random = Random.secure();
    final code = random.nextInt(pow(10, _config.codeLength).toInt());
    return code.toString().padLeft(_config.codeLength, '0');
  }

  /// Generate unique attempt ID
  String _generateAttemptId() {
    return 'mfa_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(9999).toString().padLeft(4, '0')}';
  }

  /// Check rate limiting
  bool _checkRateLimit(String userId) {
    final now = DateTime.now();
    final attempts = _deliveryAttempts[userId] ?? [];
    
    // Remove attempts older than 1 hour
    attempts.removeWhere((time) => now.difference(time).inHours >= 1);
    
    if (attempts.length >= _config.maxAttemptsPerHour) {
      return false;
    }
    
    // Add current attempt
    attempts.add(now);
    _deliveryAttempts[userId] = attempts;
    
    return true;
  }

  /// Validate phone number format
  String? _validatePhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Check if it looks like a valid phone number
    if (digits.length >= 10 && digits.length <= 15) {
      // Add + prefix if not present
      return digits.startsWith('+') ? digits : '+$digits';
    }
    
    return null;
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    const emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
    return RegExp(emailRegex).hasMatch(email);
  }

  /// Format SMS message
  String _formatSMSMessage(String code) {
    final template = _config.templates['sms'] ?? 
        'Your Quester verification code is: {code}. Valid for {expiration} minutes.';
    
    return template
        .replaceAll('{code}', code)
        .replaceAll('{expiration}', _config.expirationMinutes.toString());
  }

  /// Format email subject
  String _formatEmailSubject() {
    return _config.templates['email_subject'] ?? 'Your Quester verification code';
  }

  /// Format email body
  String _formatEmailBody(String code) {
    final template = _config.templates['email_body'] ?? '''
Hello,

Your Quester verification code is: {code}

This code will expire in {expiration} minutes.

If you didn't request this code, please ignore this email.

Best regards,
The Quester Team
''';
    
    return template
        .replaceAll('{code}', code)
        .replaceAll('{expiration}', _config.expirationMinutes.toString());
  }

  /// Mask phone number for logging
  String _maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length <= 4) return '***';
    return phoneNumber.substring(0, phoneNumber.length - 4).replaceAll(RegExp(r'.'), '*') + 
           phoneNumber.substring(phoneNumber.length - 4);
  }

  /// Mask email for logging
  String _maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return '***@***.***';
    
    final local = parts[0];
    final domain = parts[1];
    
    final maskedLocal = local.length > 2 
        ? local.substring(0, 2) + '*' * (local.length - 2)
        : '**';
    
    return '$maskedLocal@$domain';
  }

  /// Store delivery attempt in database
  Future<void> _storeDeliveryAttempt(MFADeliveryAttempt attempt) async {
    try {
      // This would store in a temporary table or cache
      // For now, we'll just cache in memory
      print('Stored MFA attempt: ${attempt.id} for user: ${attempt.userId}');
    } catch (e) {
      print('Failed to store delivery attempt: $e');
    }
  }

  /// Mark attempt as used
  Future<void> _markAttemptAsUsed(String attemptId) async {
    try {
      // This would update the database record
      print('Marked MFA attempt as used: $attemptId');
    } catch (e) {
      print('Failed to mark attempt as used: $e');
    }
  }

  /// Log delivery event
  Future<void> _logDeliveryEvent({
    required String userId,
    String? organizationId,
    required MFADeliveryMethod method,
    required String attemptId,
    required bool success,
    required Map<String, dynamic> details,
  }) async {
    try {
      // Get user's organization if not provided
      if (organizationId == null) {
        final user = await _databaseService.getUser(userId);
        organizationId = user?['organization_id'] as String?;
      }

      await _databaseService.createSecurityAuditLog({
        'organization_id': organizationId,
        'user_id': userId,
        'event_type': 'mfa_delivery',
        'event_category': success ? 'delivery_success' : 'delivery_failed',
        'event_description': 'MFA code delivery via ${method.value}',
        'event_severity': success ? 'info' : 'warning',
        'details': {
          'method': method.value,
          'attempt_id': attemptId,
          ...details,
        },
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Failed to log delivery event: $e');
    }
  }

  /// Get delivery statistics
  Future<Map<String, dynamic>> getDeliveryStats({
    String? userId,
    String? organizationId,
    Duration? period,
  }) async {
    // This would query the database for delivery statistics
    // For now, return mock data
    return {
      'total_deliveries': 150,
      'successful_deliveries': 142,
      'failed_deliveries': 8,
      'success_rate': 0.947,
      'average_delivery_time_ms': 850,
      'methods': {
        'sms': {'count': 90, 'success_rate': 0.944},
        'email': {'count': 60, 'success_rate': 0.950},
      },
      'providers': {
        'twilio': {'count': 50, 'success_rate': 0.960},
        'sendgrid': {'count': 40, 'success_rate': 0.975},
        'test': {'count': 60, 'success_rate': 1.000},
      },
    };
  }

  /// Clean up expired attempts
  void cleanupExpiredAttempts() {
    final now = DateTime.now();
    _activeAttempts.removeWhere((key, attempt) => attempt.expiresAt.isBefore(now));
  }
}