import 'package:shared/shared.dart';
import '../services/database_service.dart';
import '../services/websocket_service.dart';

/// Real-time Notification Service
/// Manages and delivers notifications across the gamification system
class NotificationService {
  // ignore: unused_field
  final DatabaseService _databaseService;
  
  NotificationService(this._databaseService);
  
  /// Send achievement unlock notification
  Future<void> sendAchievementNotification({
    required String userId,
    required Map<String, dynamic> achievement,
  }) async {
    final notification = {
      'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
      'type': 'achievement',
      'title': '🏆 Achievement Unlocked!',
      'message': 'You earned "${achievement['name']}"',
      'data': {
        'achievement_id': achievement['id'],
        'achievement_name': achievement['name'],
        'points_awarded': achievement['points_reward'],
        'rarity': achievement['rarity'],
      },
      'priority': _getAchievementPriority(achievement['rarity']),
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
    };
    
    // Store notification in database
    await _storeNotification(userId, notification);
    
    // Send real-time notification
    WebSocketService.sendNotificationToUser(userId, notification);
    
    print('🔔 Achievement notification sent to user $userId: ${achievement['name']}');
  }
  
  /// Send quest completion notification
  Future<void> sendQuestCompletionNotification({
    required String userId,
    required Map<String, dynamic> quest,
    required int pointsEarned,
  }) async {
    final notification = {
      'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
      'type': 'quest_completion',
      'title': '🎯 Quest Completed!',
      'message': 'You completed "${quest['title']}" and earned $pointsEarned points',
      'data': {
        'quest_id': quest['id'],
        'quest_title': quest['title'],
        'points_earned': pointsEarned,
        'completion_time': DateTime.now().toIso8601String(),
      },
      'priority': 'medium',
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
    };
    
    await _storeNotification(userId, notification);
    WebSocketService.sendNotificationToUser(userId, notification);
    
    print('🔔 Quest completion notification sent to user $userId');
  }
  
  /// Send team invitation notification
  Future<void> sendTeamInvitationNotification({
    required String userId,
    required String teamId,
    required String teamName,
    required String inviterName,
  }) async {
    final notification = {
      'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
      'type': 'team_invitation',
      'title': '👥 Team Invitation',
      'message': '$inviterName invited you to join "$teamName"',
      'data': {
        'team_id': teamId,
        'team_name': teamName,
        'inviter_name': inviterName,
        'invitation_expires': DateTime.now().add(Duration(days: 7)).toIso8601String(),
      },
      'priority': 'high',
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
      'actions': [
        {'type': 'accept', 'label': 'Accept'},
        {'type': 'decline', 'label': 'Decline'},
      ],
    };
    
    await _storeNotification(userId, notification);
    WebSocketService.sendNotificationToUser(userId, notification);
    
    print('🔔 Team invitation notification sent to user $userId');
  }
  
  /// Send streak milestone notification
  Future<void> sendStreakMilestoneNotification({
    required String userId,
    required int streakCount,
    required int pointsBonus,
  }) async {
    final notification = {
      'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
      'type': 'streak_milestone',
      'title': '🔥 Streak Milestone!',
      'message': 'Amazing! You\'ve reached a $streakCount-day streak! Bonus: $pointsBonus points',
      'data': {
        'streak_count': streakCount,
        'points_bonus': pointsBonus,
        'streak_type': 'daily_activity',
      },
      'priority': _getStreakPriority(streakCount),
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
    };
    
    await _storeNotification(userId, notification);
    WebSocketService.sendNotificationToUser(userId, notification);
    
    print('🔔 Streak milestone notification sent to user $userId: $streakCount days');
  }
  
  /// Send leaderboard position notification
  Future<void> sendLeaderboardPositionNotification({
    required String userId,
    required int newRank,
    required int previousRank,
    required String category,
  }) async {
    final isImprovement = newRank < previousRank;
    final rankChange = (previousRank - newRank).abs();
    
    final notification = {
      'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
      'type': 'leaderboard_update',
      'title': isImprovement ? '📈 Rank Up!' : '📉 Rank Change',
      'message': isImprovement 
          ? 'You moved up $rankChange positions to #$newRank in $category!'
          : 'Your rank changed to #$newRank in $category',
      'data': {
        'new_rank': newRank,
        'previous_rank': previousRank,
        'rank_change': rankChange,
        'category': category,
        'is_improvement': isImprovement,
      },
      'priority': isImprovement ? 'medium' : 'low',
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
    };
    
    await _storeNotification(userId, notification);
    WebSocketService.sendNotificationToUser(userId, notification);
    
    print('🔔 Leaderboard position notification sent to user $userId');
  }
  
  /// Send competition start notification
  Future<void> sendCompetitionStartNotification({
    required String userId,
    required Map<String, dynamic> competition,
  }) async {
    final notification = {
      'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
      'type': 'competition_start',
      'title': '🏁 Competition Started!',
      'message': '${competition['name']} has begun! Join now to compete for great rewards.',
      'data': {
        'competition_id': competition['id'],
        'competition_name': competition['name'],
        'end_date': competition['end_date'],
        'prize_pool': competition['prize_pool'],
      },
      'priority': 'high',
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
      'actions': [
        {'type': 'join_competition', 'label': 'Join Now'},
        {'type': 'view_details', 'label': 'View Details'},
      ],
    };
    
    await _storeNotification(userId, notification);
    WebSocketService.sendNotificationToUser(userId, notification);
    
    print('🔔 Competition start notification sent to user $userId');
  }
  
  /// Send friend activity notification
  Future<void> sendFriendActivityNotification({
    required String userId,
    required String friendName,
    required String activityType,
    required Map<String, dynamic> activityData,
  }) async {
    String title = '👤 Friend Activity';
    String message = '';
    
    switch (activityType) {
      case 'achievement':
        title = '🏆 Friend Achievement';
        message = '$friendName earned "${activityData['achievement_name']}"!';
        break;
      case 'quest_completion':
        title = '🎯 Friend Quest';
        message = '$friendName completed "${activityData['quest_title']}"';
        break;
      case 'level_up':
        title = '⬆️ Friend Level Up';
        message = '$friendName reached level ${activityData['new_level']}!';
        break;
    }
    
    final notification = {
      'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
      'type': 'friend_activity',
      'title': title,
      'message': message,
      'data': {
        'friend_name': friendName,
        'activity_type': activityType,
        'activity_data': activityData,
      },
      'priority': 'low',
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
    };
    
    await _storeNotification(userId, notification);
    WebSocketService.sendNotificationToUser(userId, notification);
    
    print('🔔 Friend activity notification sent to user $userId');
  }
  
  /// Get user's notifications with pagination
  Future<Map<String, dynamic>> getUserNotifications({
    required String userId,
    int page = 1,
    int limit = 20,
    String? type,
    bool? unreadOnly,
  }) async {
    try {
      // Mock data - would query database in real implementation
      final notifications = <Map<String, dynamic>>[
        {
          'id': 'notif_001',
          'type': 'achievement',
          'title': '🏆 Achievement Unlocked!',
          'message': 'You earned "First Quest Complete"',
          'priority': 'medium',
          'created_at': DateTime.now().subtract(Duration(hours: 2)).toIso8601String(),
          'read': false,
        },
        {
          'id': 'notif_002',
          'type': 'quest_completion',
          'title': '🎯 Quest Completed!',
          'message': 'You completed "Morning Exercise" and earned 100 points',
          'priority': 'medium',
          'created_at': DateTime.now().subtract(Duration(hours: 6)).toIso8601String(),
          'read': true,
        },
      ];
      
      return {
        'notifications': notifications,
        'pagination': {
          'current_page': page,
          'total_pages': 1,
          'total_count': notifications.length,
          'has_more': false,
        },
        'unread_count': notifications.where((n) => n['read'] == false).length,
      };
    } catch (e) {
      print('❌ Error getting user notifications: $e');
      return {
        'notifications': [],
        'pagination': {'current_page': 1, 'total_pages': 0, 'total_count': 0, 'has_more': false},
        'unread_count': 0,
      };
    }
  }
  
  /// Mark notification as read
  Future<bool> markNotificationAsRead(String userId, String notificationId) async {
    try {
      // Mock implementation - would update database
      print('📖 Marked notification $notificationId as read for user $userId');
      return true;
    } catch (e) {
      print('❌ Error marking notification as read: $e');
      return false;
    }
  }
  
  /// Mark all notifications as read
  Future<bool> markAllNotificationsAsRead(String userId) async {
    try {
      // Mock implementation - would update database
      print('📖 Marked all notifications as read for user $userId');
      return true;
    } catch (e) {
      print('❌ Error marking all notifications as read: $e');
      return false;
    }
  }
  
  /// Delete notification
  Future<bool> deleteNotification(String userId, String notificationId) async {
    try {
      // Mock implementation - would delete from database
      print('🗑️ Deleted notification $notificationId for user $userId');
      return true;
    } catch (e) {
      print('❌ Error deleting notification: $e');
      return false;
    }
  }
  
  /// Get notification preferences for user
  Future<Map<String, dynamic>> getNotificationPreferences(String userId) async {
    // Mock data - would query user preferences
    return {
      'achievements': true,
      'quest_completions': true,
      'team_invitations': true,
      'streak_milestones': true,
      'leaderboard_updates': false,
      'competition_updates': true,
      'friend_activities': true,
      'email_notifications': false,
      'push_notifications': true,
      'quiet_hours': {
        'enabled': true,
        'start_time': '22:00',
        'end_time': '08:00',
      },
    };
  }
  
  /// Update notification preferences
  Future<bool> updateNotificationPreferences(String userId, Map<String, dynamic> preferences) async {
    try {
      // Mock implementation - would update database
      print('⚙️ Updated notification preferences for user $userId');
      return true;
    } catch (e) {
      print('❌ Error updating notification preferences: $e');
      return false;
    }
  }
  
  /// Helper methods
  
  String _getAchievementPriority(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'critical';
      case 'epic':
        return 'high';
      case 'rare':
        return 'medium';
      default:
        return 'low';
    }
  }
  
  String _getStreakPriority(int streakCount) {
    if (streakCount >= 100) return 'critical';
    if (streakCount >= 30) return 'high';
    if (streakCount >= 7) return 'medium';
    return 'low';
  }
  
  Future<void> _storeNotification(String userId, Map<String, dynamic> notification) async {
    // Mock implementation - would store in database
    print('💾 Stored notification for user $userId: ${notification['title']}');
  }

  // Additional methods required by routes

  /// Get notifications with pagination and filtering
  Future<GetNotificationsResponse> getNotifications(String userId, GetNotificationsRequest request) async {
    try {
      // Mock implementation - would query database
      final notifications = <Notification>[
        Notification(
          id: 'notif_001',
          userId: userId,
          type: NotificationType.achievementUnlocked,
          title: '🏆 Achievement Unlocked!',
          body: 'You earned "First Quest Complete"',
          priority: NotificationPriority.normal,
          status: NotificationStatus.pending,
          deliveryMethods: [DeliveryMethod.push, DeliveryMethod.inApp],
          createdAt: DateTime.now().subtract(Duration(hours: 2)),
        ),
      ];

      return GetNotificationsResponse(
        notifications: notifications,
        pagination: PaginationInfo(
          page: request.page,
          limit: request.limit,
          total: notifications.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: request.page > 1,
        ),
        unreadCount: notifications.where((n) => n.status == NotificationStatus.pending).length,
      );
    } catch (e) {
      print('❌ Error getting notifications: $e');
      rethrow;
    }
  }

  /// Get notification by ID
  Future<Notification?> getNotificationById(String userId, String notificationId) async {
    try {
      // Mock implementation - would query database
      return Notification(
        id: notificationId,
        userId: userId,
        type: NotificationType.achievementUnlocked,
        title: '🏆 Achievement Unlocked!',
        body: 'You earned "First Quest Complete"',
        priority: NotificationPriority.normal,
        status: NotificationStatus.pending,
        deliveryMethods: [DeliveryMethod.push, DeliveryMethod.inApp],
        createdAt: DateTime.now().subtract(Duration(hours: 2)),
      );
    } catch (e) {
      print('❌ Error getting notification by ID: $e');
      return null;
    }
  }

  /// Create a new notification
  Future<Notification> createNotification(CreateNotificationRequest request) async {
    try {
      final notification = Notification(
        id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
        userId: request.userId,
        type: request.type,
        title: request.title,
        body: request.body,
        priority: request.priority,
        status: NotificationStatus.pending,
        deliveryMethods: request.deliveryMethods,
        createdAt: DateTime.now(),
        actionText: request.actionText,
        actionUrl: request.actionUrl,
        metadata: request.metadata,
        relatedEntityId: request.relatedEntityId,
        relatedEntityType: request.relatedEntityType,
        icon: request.icon,
        sound: request.sound,
        expiresAt: request.expiresAt,
        scheduledAt: null, // scheduledAt not available in CreateNotificationRequest
      );

      await _storeNotification(request.userId, {
        'id': notification.id,
        'title': notification.title,
        'message': notification.body,
      });

      return notification;
    } catch (e) {
      print('❌ Error creating notification: $e');
      rethrow;
    }
  }

  /// Create notification from template
  Future<Notification> createNotificationFromTemplate(CreateNotificationFromTemplateRequest request) async {
    try {
      // Mock implementation - create notification from template data
      final notification = Notification(
        id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
        userId: request.userId,
        type: NotificationType.systemAlert,
        title: 'Template Notification',
        body: 'Notification created from template',
        priority: NotificationPriority.normal,
        status: NotificationStatus.pending,
        deliveryMethods: [DeliveryMethod.inApp],
        createdAt: DateTime.now(),
        scheduledAt: request.scheduledFor,
        expiresAt: null, // expiresAt not available in CreateNotificationFromTemplateRequest
      );

      await _storeNotification(request.userId, {
        'id': notification.id,
        'title': notification.title,
        'message': notification.body,
      });

      return notification;
    } catch (e) {
      print('❌ Error creating notification from template: $e');
      rethrow;
    }
  }

  /// Mark notifications as read
  Future<void> markNotificationsAsRead(String userId, MarkNotificationsReadRequest request) async {
    try {
      // Mock implementation - would update database
      print('📖 Marked ${request.notificationIds.length} notifications as read for user $userId');
    } catch (e) {
      print('❌ Error marking notifications as read: $e');
      rethrow;
    }
  }

  /// Get notification statistics
  Future<NotificationStatsResponse> getNotificationStats(String userId) async {
    try {
      // Mock implementation - would query database
      return NotificationStatsResponse(
        totalCount: 25,
        unreadCount: 5,
        countByCategory: {
          NotificationCategory.system: 8,
          NotificationCategory.achievement: 12,
          NotificationCategory.social: 5,
        },
        countByPriority: {
          NotificationPriority.low: 10,
          NotificationPriority.normal: 12,
          NotificationPriority.high: 3,
        },
        countByStatus: {
          NotificationStatus.pending: 5,
          NotificationStatus.sent: 15,
          NotificationStatus.delivered: 5,
        },
        recentActivity: [],
      );
    } catch (e) {
      print('❌ Error getting notification stats: $e');
      rethrow;
    }
  }

  /// Register device for push notifications
  Future<void> registerDevice(RegisterDeviceRequest request) async {
    try {
      // Mock implementation - would store device token
      print('📱 Registered device ${request.pushToken} on platform ${request.platform}');
    } catch (e) {
      print('❌ Error registering device: $e');
      rethrow;
    }
  }

  /// Unregister device
  Future<void> unregisterDevice(String deviceToken) async {
    try {
      // Mock implementation - would remove device token
      print('📱 Unregistered device $deviceToken');
    } catch (e) {
      print('❌ Error unregistering device: $e');
      rethrow;
    }
  }

  /// Get notification templates
  Future<List<NotificationTemplate>> getNotificationTemplates() async {
    try {
      // Return mock predefined templates
      return [
        NotificationTemplate(
          id: 'quest_completed',
          name: 'Quest Completed',
          description: 'Template for quest completion notifications',
          category: NotificationCategory.achievement,
          type: NotificationType.achievementUnlocked,
          priority: NotificationPriority.normal,
          titleTemplate: 'Quest Completed: {{questName}}',
          bodyTemplate: 'Congratulations! You completed {{questName}} and earned {{points}} points.',
          defaultDeliveryMethods: [DeliveryMethod.push, DeliveryMethod.inApp],
          variables: {
            'questName': TemplateVariable(
              name: 'questName',
              description: 'Name of the completed quest',
              type: VariableType.string,
              required: true,
            ),
            'points': TemplateVariable(
              name: 'points',
              description: 'Points earned from quest',
              type: VariableType.number,
              required: true,
            ),
          },
          createdBy: 'system',
          createdAt: DateTime.now().subtract(Duration(days: 30)),
          updatedAt: DateTime.now().subtract(Duration(days: 1)),
        ),
        NotificationTemplate(
          id: 'achievement_unlocked',
          name: 'Achievement Unlocked',
          description: 'Template for achievement unlock notifications',
          category: NotificationCategory.achievement,
          type: NotificationType.achievementUnlocked,
          priority: NotificationPriority.high,
          titleTemplate: 'Achievement Unlocked: {{achievementName}}',
          bodyTemplate: 'You unlocked the {{achievementName}} achievement!',
          defaultDeliveryMethods: [DeliveryMethod.push, DeliveryMethod.inApp],
          variables: {
            'achievementName': TemplateVariable(
              name: 'achievementName',
              description: 'Name of the unlocked achievement',
              type: VariableType.string,
              required: true,
            ),
          },
          createdBy: 'system',
          createdAt: DateTime.now().subtract(Duration(days: 30)),
          updatedAt: DateTime.now().subtract(Duration(days: 1)),
        ),
      ];
    } catch (e) {
      print('❌ Error getting notification templates: $e');
      rethrow;
    }
  }

  /// Create notification template
  Future<NotificationTemplate> createNotificationTemplate(Map<String, dynamic> request) async {
    try {
      // Mock implementation - would store template
      final template = NotificationTemplate(
        id: 'template_${DateTime.now().millisecondsSinceEpoch}',
        name: request['name'] ?? 'New Template',
        description: request['description'] ?? 'Template description',
        category: NotificationCategory.system,
        type: NotificationType.systemAlert,
        priority: NotificationPriority.normal,
        titleTemplate: request['titleTemplate'] ?? 'Title',
        bodyTemplate: request['bodyTemplate'] ?? 'Body',
        defaultDeliveryMethods: [DeliveryMethod.inApp],
        variables: {},
        createdBy: 'system',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      print('📝 Created notification template: ${template.name}');
      return template;
    } catch (e) {
      print('❌ Error creating notification template: $e');
      rethrow;
    }
  }

  /// Update notification template
  Future<NotificationTemplate> updateNotificationTemplate(String templateId, Map<String, dynamic> request) async {
    try {
      // Mock implementation - would update template
      final template = NotificationTemplate(
        id: templateId,
        name: request['name'] ?? 'Updated Template',
        description: request['description'] ?? 'Updated description',
        category: NotificationCategory.system,
        type: NotificationType.systemAlert,
        priority: NotificationPriority.normal,
        titleTemplate: request['titleTemplate'] ?? 'Updated Title',
        bodyTemplate: request['bodyTemplate'] ?? 'Updated Body',
        defaultDeliveryMethods: [DeliveryMethod.inApp],
        variables: {},
        createdBy: 'system',
        createdAt: DateTime.now().subtract(Duration(days: 1)),
        updatedAt: DateTime.now(),
      );

      print('📝 Updated notification template: ${template.name}');
      return template;
    } catch (e) {
      print('❌ Error updating notification template: $e');
      rethrow;
    }
  }

  /// Delete notification template
  Future<void> deleteNotificationTemplate(String templateId) async {
    try {
      // Mock implementation - would delete template
      print('🗑️ Deleted notification template: $templateId');
    } catch (e) {
      print('❌ Error deleting notification template: $e');
      rethrow;
    }
  }

  /// Send notification (generic method)
  Future<void> sendNotification({
    required String userId,
    required NotificationType type,
    required String title,
    required String body,
    String? actionText,
    String? actionUrl,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? metadata,
    String? relatedEntityId,
    String? relatedEntityType,
    String? icon,
    String? sound,
    DateTime? expiresAt,
    DateTime? scheduledAt,
  }) async {
    try {
      final notification = {
        'id': 'notif_${DateTime.now().millisecondsSinceEpoch}',
        'type': type.toString(),
        'title': title,
        'message': body,
        'priority': priority.toString(),
        'created_at': DateTime.now().toIso8601String(),
        'read': false,
        if (actionText != null) 'action_text': actionText,
        if (actionUrl != null) 'action_url': actionUrl,
        if (metadata != null) 'metadata': metadata,
      };

      await _storeNotification(userId, notification);
      WebSocketService.sendNotificationToUser(userId, notification);

      print('🔔 Notification sent to user $userId: $title');
    } catch (e) {
      print('❌ Error sending notification: $e');
      rethrow;
    }
  }
}
