import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'analytics_metrics.dart' show DateTimeRange;

part 'user_behavior_analytics.g.dart';

/// User behavior analytics for engagement and retention tracking
@JsonSerializable()
class UserBehaviorAnalytics extends Equatable {
  /// Unique identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// User identifier
  final String userId;

  /// Analysis date
  final DateTime analysisDate;

  /// Number of sessions for this user on this date
  final int sessionCount;

  /// Total page views
  final int pageViews;

  /// Total actions performed
  final int actionsCount;

  /// Time spent in seconds
  final int timeSpentSeconds;

  /// Features/screens used
  final List<String> featuresUsed;

  /// Calculated engagement score (0-10)
  final double engagementScore;

  /// Behavior patterns detected
  final Map<String, dynamic> behaviorPatterns;

  /// Cohort analysis data
  final Map<String, dynamic> cohortData;

  /// Retention metrics
  final Map<String, dynamic> retentionMetrics;

  /// Creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const UserBehaviorAnalytics({
    required this.id,
    required this.organizationId,
    required this.userId,
    required this.analysisDate,
    required this.sessionCount,
    required this.pageViews,
    required this.actionsCount,
    required this.timeSpentSeconds,
    required this.featuresUsed,
    required this.engagementScore,
    required this.behaviorPatterns,
    required this.cohortData,
    required this.retentionMetrics,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create UserBehaviorAnalytics from JSON
  factory UserBehaviorAnalytics.fromJson(Map<String, dynamic> json) =>
      _$UserBehaviorAnalyticsFromJson(json);

  /// Convert UserBehaviorAnalytics to JSON
  Map<String, dynamic> toJson() => _$UserBehaviorAnalyticsToJson(this);

  /// Get engagement category based on score
  EngagementCategory get engagementCategory {
    if (engagementScore >= 8.0) return EngagementCategory.highlyEngaged;
    if (engagementScore >= 6.0) return EngagementCategory.moderatelyEngaged;
    if (engagementScore >= 4.0) return EngagementCategory.lightlyEngaged;
    return EngagementCategory.atRisk;
  }

  /// Get average session duration in minutes
  double get avgSessionDuration {
    if (sessionCount == 0) return 0.0;
    return (timeSpentSeconds / sessionCount) / 60.0;
  }

  /// Get actions per session
  double get actionsPerSession {
    if (sessionCount == 0) return 0.0;
    return actionsCount / sessionCount;
  }

  /// Check if user was active on this date
  bool get isActiveUser {
    return sessionCount > 0 && actionsCount > 0;
  }

  /// Check if this represents a power user session
  bool get isPowerUser {
    return engagementScore >= 7.0 &&
           sessionCount >= 3 &&
           featuresUsed.length >= 5;
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        userId,
        analysisDate,
        sessionCount,
        pageViews,
        actionsCount,
        timeSpentSeconds,
        featuresUsed,
        engagementScore,
        behaviorPatterns,
        cohortData,
        retentionMetrics,
        createdAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Engagement categories for user classification
enum EngagementCategory {
  @JsonValue('highly_engaged')
  highlyEngaged,
  @JsonValue('moderately_engaged')
  moderatelyEngaged,
  @JsonValue('lightly_engaged')
  lightlyEngaged,
  @JsonValue('at_risk')
  atRisk,
}

/// Engagement score calculation result
@JsonSerializable()
class EngagementScore extends Equatable {
  /// User identifier
  final String userId;

  /// Organization identifier
  final String organizationId;

  /// Overall engagement score (0-10)
  final double overallScore;

  /// Component scores that make up overall score
  final EngagementComponents components;

  /// Time range for calculation
  final DateTimeRange timeRange;

  /// Factors that contributed positively
  final List<String> positiveFactors;

  /// Factors that contributed negatively
  final List<String> negativeFactors;

  /// Recommendations for improvement
  final List<String> recommendations;

  /// Calculation timestamp
  final DateTime calculatedAt;

  const EngagementScore({
    required this.userId,
    required this.organizationId,
    required this.overallScore,
    required this.components,
    required this.timeRange,
    required this.positiveFactors,
    required this.negativeFactors,
    required this.recommendations,
    required this.calculatedAt,
  });

  /// Create EngagementScore from JSON
  factory EngagementScore.fromJson(Map<String, dynamic> json) =>
      _$EngagementScoreFromJson(json);

  /// Convert EngagementScore to JSON
  Map<String, dynamic> toJson() => _$EngagementScoreToJson(this);

  @override
  List<Object?> get props => [
        userId,
        organizationId,
        overallScore,
        components,
        timeRange,
        positiveFactors,
        negativeFactors,
        recommendations,
        calculatedAt,
      ];

  @override
  bool get stringify => true;
}

/// Components of engagement score calculation
@JsonSerializable()
class EngagementComponents extends Equatable {
  /// Frequency score (how often user is active)
  final double frequency;

  /// Depth score (how deeply user engages)
  final double depth;

  /// Breadth score (how many features user uses)
  final double breadth;

  /// Consistency score (how consistently user is active)
  final double consistency;

  /// Gamification score (engagement with game elements)
  final double gamification;

  /// Collaboration score (social engagement)
  final double collaboration;

  const EngagementComponents({
    required this.frequency,
    required this.depth,
    required this.breadth,
    required this.consistency,
    required this.gamification,
    required this.collaboration,
  });

  /// Create EngagementComponents from JSON
  factory EngagementComponents.fromJson(Map<String, dynamic> json) =>
      _$EngagementComponentsFromJson(json);

  /// Convert EngagementComponents to JSON
  Map<String, dynamic> toJson() => _$EngagementComponentsToJson(this);

  @override
  List<Object?> get props => [
        frequency,
        depth,
        breadth,
        consistency,
        gamification,
        collaboration,
      ];

  @override
  bool get stringify => true;
}

// DateTimeRange is defined in analytics_metrics.dart - import that file when needed

/// Cohort analysis result
@JsonSerializable()
class CohortAnalysis extends Equatable {
  /// Organization identifier
  final String organizationId;

  /// Cohort definition (e.g., signup month)
  final String cohortDefinition;

  /// Cohort period (e.g., "2024-08")
  final String cohortPeriod;

  /// Initial cohort size
  final int initialSize;

  /// Retention data by period
  final Map<String, CohortPeriodData> retentionData;

  /// Analysis timestamp
  final DateTime analyzedAt;

  const CohortAnalysis({
    required this.organizationId,
    required this.cohortDefinition,
    required this.cohortPeriod,
    required this.initialSize,
    required this.retentionData,
    required this.analyzedAt,
  });

  /// Create CohortAnalysis from JSON
  factory CohortAnalysis.fromJson(Map<String, dynamic> json) =>
      _$CohortAnalysisFromJson(json);

  /// Convert CohortAnalysis to JSON
  Map<String, dynamic> toJson() => _$CohortAnalysisToJson(this);

  @override
  List<Object?> get props => [
        organizationId,
        cohortDefinition,
        cohortPeriod,
        initialSize,
        retentionData,
        analyzedAt,
      ];

  @override
  bool get stringify => true;
}

/// Cohort period data
@JsonSerializable()
class CohortPeriodData extends Equatable {
  /// Period number (0 = initial period, 1 = first period after, etc.)
  final int periodNumber;

  /// Number of active users in this period
  final int activeUsers;

  /// Retention rate (active_users / initial_size)
  final double retentionRate;

  /// Average engagement score for this period
  final double avgEngagementScore;

  const CohortPeriodData({
    required this.periodNumber,
    required this.activeUsers,
    required this.retentionRate,
    required this.avgEngagementScore,
  });

  /// Create CohortPeriodData from JSON
  factory CohortPeriodData.fromJson(Map<String, dynamic> json) =>
      _$CohortPeriodDataFromJson(json);

  /// Convert CohortPeriodData to JSON
  Map<String, dynamic> toJson() => _$CohortPeriodDataToJson(this);

  @override
  List<Object?> get props => [
        periodNumber,
        activeUsers,
        retentionRate,
        avgEngagementScore,
      ];

  @override
  bool get stringify => true;
}

/// User journey mapping data
@JsonSerializable()
class UserJourney extends Equatable {
  /// User identifier
  final String userId;

  /// Organization identifier
  final String organizationId;

  /// Journey start timestamp
  final DateTime startTime;

  /// Journey end timestamp
  final DateTime endTime;

  /// Steps in the journey
  final List<JourneyStep> steps;

  /// Journey metadata
  final Map<String, dynamic> metadata;

  /// Total journey duration
  Duration get duration => endTime.difference(startTime);

  /// Number of steps
  int get stepCount => steps.length;

  const UserJourney({
    required this.userId,
    required this.organizationId,
    required this.startTime,
    required this.endTime,
    required this.steps,
    required this.metadata,
  });

  /// Create UserJourney from JSON
  factory UserJourney.fromJson(Map<String, dynamic> json) =>
      _$UserJourneyFromJson(json);

  /// Convert UserJourney to JSON
  Map<String, dynamic> toJson() => _$UserJourneyToJson(this);

  @override
  List<Object?> get props => [
        userId,
        organizationId,
        startTime,
        endTime,
        steps,
        metadata,
      ];

  @override
  bool get stringify => true;
}

/// Individual step in user journey
@JsonSerializable()
class JourneyStep extends Equatable {
  /// Step sequence number
  final int sequence;

  /// Event type
  final String eventType;

  /// Event name
  final String eventName;

  /// Page or screen
  final String? page;

  /// Timestamp
  final DateTime timestamp;

  /// Time spent on this step (seconds)
  final int? duration;

  /// Step metadata
  final Map<String, dynamic>? metadata;

  const JourneyStep({
    required this.sequence,
    required this.eventType,
    required this.eventName,
    this.page,
    required this.timestamp,
    this.duration,
    this.metadata,
  });

  /// Create JourneyStep from JSON
  factory JourneyStep.fromJson(Map<String, dynamic> json) =>
      _$JourneyStepFromJson(json);

  /// Convert JourneyStep to JSON
  Map<String, dynamic> toJson() => _$JourneyStepToJson(this);

  @override
  List<Object?> get props => [
        sequence,
        eventType,
        eventName,
        page,
        timestamp,
        duration,
        metadata,
      ];

  @override
  bool get stringify => true;
}