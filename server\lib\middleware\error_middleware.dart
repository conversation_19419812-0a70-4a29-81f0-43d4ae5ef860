/// Comprehensive error handling middleware for the Quester server
library;

import 'dart:convert';
import 'dart:io';
import 'package:shelf/shelf.dart';
import '../services/logging_service.dart';
import '../utils/response_utils.dart';

/// Custom exception types for better error handling
class ValidationException implements Exception {
  final String message;
  final Map<String, List<String>>? fieldErrors;
  
  const ValidationException(this.message, {this.fieldErrors});
  
  @override
  String toString() => 'ValidationException: $message';
}

class AuthenticationException implements Exception {
  final String message;
  final String? code;
  
  const AuthenticationException(this.message, {this.code});
  
  @override
  String toString() => 'AuthenticationException: $message';
}

class AuthorizationException implements Exception {
  final String message;
  final String? requiredPermission;
  
  const AuthorizationException(this.message, {this.requiredPermission});
  
  @override
  String toString() => 'AuthorizationException: $message';
}

class BusinessLogicException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? context;
  
  const BusinessLogicException(this.message, {this.code, this.context});
  
  @override
  String toString() => 'BusinessLogicException: $message';
}

class DatabaseException implements Exception {
  final String message;
  final String? query;
  final dynamic originalError;
  
  const DatabaseException(this.message, {this.query, this.originalError});
  
  @override
  String toString() => 'DatabaseException: $message';
}

class ExternalServiceException implements Exception {
  final String message;
  final String? service;
  final int? statusCode;
  
  const ExternalServiceException(this.message, {this.service, this.statusCode});
  
  @override
  String toString() => 'ExternalServiceException: $message';
}

/// Comprehensive error handling middleware
class ErrorMiddleware {
  /// Creates error handling middleware with structured error responses
  static Middleware errorHandler() {
    return (Handler innerHandler) {
      return (Request request) async {
        try {
          return await innerHandler(request);
        } catch (error, stackTrace) {
          return _handleError(error, stackTrace, request);
        }
      };
    };
  }

  /// Handle different types of errors with appropriate responses
  static Response _handleError(dynamic error, StackTrace stackTrace, Request request) {
    final requestId = _generateRequestId();
    final endpoint = '${request.method} ${request.requestedUri.path}';
    
    // Log the error with context
    _logError(error, stackTrace, request, requestId);
    
    // Handle specific exception types
    switch (error.runtimeType) {
      case ValidationException:
        final validationError = error as ValidationException;
        return ResponseUtils.validationError(
          message: validationError.message,
          fieldErrors: validationError.fieldErrors,
          headers: {'X-Request-ID': requestId},
        );
        
      case AuthenticationException:
        final authError = error as AuthenticationException;
        return ResponseUtils.unauthorized(
          message: authError.message,
          headers: {'X-Request-ID': requestId},
        );
        
      case AuthorizationException:
        final authzError = error as AuthorizationException;
        return ResponseUtils.forbidden(
          message: authzError.message,
          headers: {'X-Request-ID': requestId},
        );
        
      case BusinessLogicException:
        final businessError = error as BusinessLogicException;
        return ResponseUtils.badRequest(
          message: businessError.message,
          details: businessError.context,
          headers: {'X-Request-ID': requestId},
        );
        
      case DatabaseException:
        final dbError = error as DatabaseException;
        LoggingService.critical(
          'Database error on $endpoint',
          tag: 'Database',
          error: dbError,
        );
        return ResponseUtils.internalServerError(
          message: 'Database operation failed',
          headers: {'X-Request-ID': requestId},
        );
        
      case ExternalServiceException:
        final serviceError = error as ExternalServiceException;
        LoggingService.error(
          'External service error: ${serviceError.service}',
          tag: 'ExternalService',
          error: serviceError,
        );
        return ResponseUtils.error(
          message: 'External service unavailable',
          statusCode: 503,
          errorCode: 'SERVICE_UNAVAILABLE',
          headers: {'X-Request-ID': requestId},
        );
        
      case FormatException:
        return ResponseUtils.badRequest(
          message: 'Invalid request format',
          details: error.toString(),
          headers: {'X-Request-ID': requestId},
        );
        
      case SocketException:
        LoggingService.error(
          'Network error on $endpoint',
          tag: 'Network',
          error: error,
        );
        return ResponseUtils.error(
          message: 'Network connectivity issue',
          statusCode: 503,
          errorCode: 'NETWORK_ERROR',
          headers: {'X-Request-ID': requestId},
        );
        
      default:
        // Log unexpected errors as critical
        LoggingService.critical(
          'Unexpected error on $endpoint',
          tag: 'UnhandledError',
          error: error,
          stackTrace: stackTrace,
        );
        
        return ResponseUtils.internalServerError(
          message: 'An unexpected error occurred',
          headers: {'X-Request-ID': requestId},
        );
    }
  }

  /// Log error with appropriate level and context
  static void _logError(dynamic error, StackTrace stackTrace, Request request, String requestId) {
    final endpoint = '${request.method} ${request.requestedUri.path}';
    final userAgent = request.headers['user-agent'] ?? 'Unknown';
    final clientIp = _getClientIp(request);
    
    final context = {
      'requestId': requestId,
      'endpoint': endpoint,
      'userAgent': userAgent,
      'clientIp': clientIp,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    // Log based on error type
    if (error is ValidationException || error is BusinessLogicException) {
      LoggingService.warning(
        'Client error: ${error.toString()}',
        tag: 'ClientError',
        error: error,
      );
    } else if (error is AuthenticationException || error is AuthorizationException) {
      LoggingService.security(
        'Security error: ${error.toString()}',
        userId: request.headers['x-user-id'],
        ipAddress: clientIp,
        error: error,
      );
    } else {
      LoggingService.error(
        'Server error: ${error.toString()}',
        tag: 'ServerError',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Generate unique request ID for tracking
  static String _generateRequestId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'req_${timestamp}_$random';
  }

  /// Extract client IP from request
  static String _getClientIp(Request request) {
    // Check for forwarded headers first (for reverse proxies)
    final forwarded = request.headers['x-forwarded-for'];
    if (forwarded != null && forwarded.isNotEmpty) {
      return forwarded.split(',').first.trim();
    }

    final realIp = request.headers['x-real-ip'];
    if (realIp != null && realIp.isNotEmpty) {
      return realIp;
    }

    // Fall back to connection info (with proper type checking)
    final connectionInfo = request.context['shelf.io.connection_info'];
    if (connectionInfo != null && connectionInfo is HttpConnectionInfo) {
      return connectionInfo.remoteAddress.address;
    }

    return 'unknown';
  }

  /// Create middleware for request/response logging
  static Middleware requestLogger() {
    return (Handler innerHandler) {
      return (Request request) async {
        final requestId = _generateRequestId();
        final startTime = DateTime.now();
        final stopwatch = Stopwatch()..start();
        
        // Log incoming request
        LoggingService.api(
          'Incoming request: ${request.method} ${request.requestedUri.path}',
          endpoint: '${request.method} ${request.requestedUri.path}',
        );
        
        try {
          // Add request ID to context for downstream handlers
          final updatedRequest = request.change(context: {
            ...request.context,
            'requestId': requestId,
            'startTime': startTime,
          });
          
          final response = await innerHandler(updatedRequest);
          stopwatch.stop();
          
          // Log successful response
          LoggingService.api(
            'Response sent: ${response.statusCode}',
            endpoint: '${request.method} ${request.requestedUri.path}',
            statusCode: response.statusCode,
          );
          
          LoggingService.performance(
            'Request completed: ${request.method} ${request.requestedUri.path}',
            duration: stopwatch.elapsed,
          );
          
          // Add request ID to response headers
          return response.change(headers: {
            ...response.headers,
            'X-Request-ID': requestId,
          });
          
        } catch (error, stackTrace) {
          stopwatch.stop();

          LoggingService.error(
            'Request failed: ${request.method} ${request.requestedUri.path}',
            tag: 'RequestFailed',
            error: error,
            stackTrace: stackTrace,
          );
          
          rethrow; // Let error middleware handle it
        }
      };
    };
  }
}
