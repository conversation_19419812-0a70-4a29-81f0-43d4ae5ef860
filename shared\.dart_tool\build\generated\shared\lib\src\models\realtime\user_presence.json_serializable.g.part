// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserPresence _$UserPresenceFromJson(Map<String, dynamic> json) => UserPresence(
  userId: json['userId'] as String,
  status: $enumDecode(_$PresenceStatusEnumMap, json['status']),
  activity: $enumDecodeNullable(_$UserActivityEnumMap, json['activity']),
  activityContext: json['activityContext'] as String?,
  statusMessage: json['statusMessage'] as String?,
  lastSeen: DateTime.parse(json['lastSeen'] as String),
  lastActivity: json['lastActivity'] == null
      ? null
      : DateTime.parse(json['lastActivity'] as String),
  deviceInfo: json['deviceInfo'] as Map<String, dynamic>?,
  location: json['location'] as Map<String, dynamic>?,
  organizationId: json['organizationId'] as String?,
  activeRooms:
      (json['activeRooms'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  isPresencePublic: json['isPresencePublic'] as bool? ?? true,
);

Map<String, dynamic> _$UserPresenceToJson(UserPresence instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'status': _$PresenceStatusEnumMap[instance.status]!,
      'activity': _$UserActivityEnumMap[instance.activity],
      'activityContext': instance.activityContext,
      'statusMessage': instance.statusMessage,
      'lastSeen': instance.lastSeen.toIso8601String(),
      'lastActivity': instance.lastActivity?.toIso8601String(),
      'deviceInfo': instance.deviceInfo,
      'location': instance.location,
      'organizationId': instance.organizationId,
      'activeRooms': instance.activeRooms,
      'isPresencePublic': instance.isPresencePublic,
    };

const _$PresenceStatusEnumMap = {
  PresenceStatus.online: 'online',
  PresenceStatus.away: 'away',
  PresenceStatus.busy: 'busy',
  PresenceStatus.offline: 'offline',
  PresenceStatus.invisible: 'invisible',
};

const _$UserActivityEnumMap = {
  UserActivity.idle: 'idle',
  UserActivity.typing: 'typing',
  UserActivity.editingQuest: 'editing_quest',
  UserActivity.editingTask: 'editing_task',
  UserActivity.viewingDashboard: 'viewing_dashboard',
  UserActivity.inMeeting: 'in_meeting',
  UserActivity.workingOnTask: 'working_on_task',
};
