/// Test helpers for MFA system integration tests
library;
import 'dart:math';

class TestHelpers {
  static final Random _random = Random();

  /// Generate a mock TOTP code from secret
  static String generateMockTOTPCode(String secret) {
    // In real implementation, this would use the TOTP algorithm
    // For testing, we'll generate a 6-digit code based on the secret
    final hash = secret.hashCode.abs();
    return (hash % 1000000).toString().padLeft(6, '0');
  }

  /// Generate a mock TOTP secret
  static String generateMockSecret() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    return List.generate(32, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// Extract verification code from recovery ID (mock implementation)
  static String extractVerificationCode(String recoveryId) {
    // Mock implementation - extract code from recovery ID
    return recoveryId.substring(recoveryId.length - 6);
  }

  /// Create mock recovery result
  static Map<String, dynamic> createMockRecoveryResult(String method, String reason) {
    return {
      'success': true,
      'recovery_id': 'rec_${_random.nextInt(1000000)}',
      'method': method,
      'reason': reason,
      'expires_at': DateTime.now().add(Duration(minutes: 15)).toIso8601String(),
    };
  }

  /// Create mock verification result
  static Map<String, dynamic> createMockVerificationResult(bool success) {
    return {
      'success': success,
      'message': success ? 'Verification successful' : 'Verification failed',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock completion result
  static Map<String, dynamic> createMockCompletionResult(bool success) {
    return {
      'success': success,
      'completed_at': DateTime.now().toIso8601String(),
      'recovery_complete': success,
    };
  }
}

/// Enhanced test database utilities for integration tests
class TestDatabase {
  late String _connectionString;
  final String _testId;

  TestDatabase._(this._testId) {
    _connectionString = 'test_db_${_testId}_${DateTime.now().millisecondsSinceEpoch}';
  }

  static Future<TestDatabase> create({String? testId}) async {
    final id = testId ?? 'test_${Random().nextInt(10000)}';
    final db = TestDatabase._(id);
    await db._initialize();
    return db;
  }

  Future<void> _initialize() async {
    // Initialize test database connection
    print('🔧 Initializing test database: $_connectionString');
  }

  Future<void> cleanup() async {
    print('🧹 Cleaning up test database: $_connectionString');
    // Clean up test database resources
  }

  String get connectionString => _connectionString;
  String get testId => _testId;
}

/// Test data factory for creating consistent test objects
class TestDataFactory {
  static final Random _random = Random();

  /// Create test user data
  static Map<String, dynamic> createTestUser({
    String? id,
    String? username,
    String? email,
    String? displayName,
  }) {
    final userId = id ?? 'test-user-${_random.nextInt(10000)}';
    return {
      'id': userId,
      'username': username ?? 'testuser_${_random.nextInt(1000)}',
      'email': email ?? 'test${_random.nextInt(1000)}@example.com',
      'display_name': displayName ?? 'Test User ${_random.nextInt(100)}',
      'is_active': true,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Create test achievement data
  static Map<String, dynamic> createTestAchievement({
    String? name,
    String? description,
    int? points,
  }) {
    return {
      'name': name ?? 'Test Achievement ${_random.nextInt(100)}',
      'description': description ?? 'Test achievement description',
      'category': 'Testing',
      'rarity': ['Common', 'Uncommon', 'Rare'][_random.nextInt(3)],
      'points_reward': points ?? (_random.nextInt(500) + 50),
    };
  }

  /// Create test activity log entry
  static Map<String, dynamic> createTestActivity({
    String? userId,
    String? activityType,
    int? points,
  }) {
    return {
      'user_id': userId ?? 'test-user-${_random.nextInt(1000)}',
      'activity_type': activityType ?? ['quest_completed', 'achievement_earned'][_random.nextInt(2)],
      'points_earned': points ?? (_random.nextInt(100) + 10),
      'description': 'Test activity description',
      'created_at': DateTime.now().subtract(Duration(hours: _random.nextInt(24))).toIso8601String(),
    };
  }

  /// Create test user stats
  static Map<String, dynamic> createTestUserStats({
    String? userId,
    int? totalPoints,
    int? level,
  }) {
    final points = totalPoints ?? (_random.nextInt(1000) + 100);
    return {
      'user_id': userId ?? 'test-user-${_random.nextInt(1000)}',
      'total_points': points,
      'current_level': level ?? ((points / 100).floor() + 1),
      'role': 'adventurer',
      'points_to_next_level': 100 - (points % 100),
    };
  }
}
