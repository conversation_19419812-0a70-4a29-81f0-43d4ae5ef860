import 'package:test/test.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  group('Freelancing Workflow E2E Tests', () {
    const baseUrl = 'http://localhost:8080';
    late http.Client client;
    String? authToken;
    String? projectId;
    String? proposalId;

    setUp(() {
      client = http.Client();
    });

    tearDown(() {
      client.close();
    });

    group('Complete Freelancing Workflow', () {
      test('should complete full project lifecycle', () async {
        try {
          // Step 1: Client creates a project
          final createProjectResponse = await client.post(
            Uri.parse('$baseUrl/api/freelancing/projects'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
            body: jsonEncode({
              'title': 'Test Project',
              'description': 'A test freelancing project',
              'budget_min': 500,
              'budget_max': 1000,
              'skills_required': ['dart', 'flutter'],
              'project_type': 'fixed',
              'deadline': DateTime.now().add(Duration(days: 30)).toIso8601String(),
            }),
          );
          
          expect(createProjectResponse.statusCode, anyOf([200, 201, 401]));
          
          if (createProjectResponse.statusCode == 200 || createProjectResponse.statusCode == 201) {
            final projectData = jsonDecode(createProjectResponse.body);
            projectId = projectData['project_id'];
            expect(projectId, isNotNull);
          }
        } catch (e) {
          print('⚠️  Skipping project creation test: $e');
        }
      });

      test('should allow freelancer to submit proposal', () async {
        if (projectId == null) {
          print('⚠️  Skipping proposal test: No project ID available');
          return;
        }

        try {
          final proposalResponse = await client.post(
            Uri.parse('$baseUrl/api/freelancing/proposals'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
            body: jsonEncode({
              'project_id': projectId,
              'proposal_text': 'I am interested in working on this project.',
              'proposed_budget': 750,
              'estimated_duration': '2 weeks',
            }),
          );
          
          expect(proposalResponse.statusCode, anyOf([200, 201, 401, 404]));
          
          if (proposalResponse.statusCode == 200 || proposalResponse.statusCode == 201) {
            final proposalData = jsonDecode(proposalResponse.body);
            proposalId = proposalData['proposal_id'];
            expect(proposalId, isNotNull);
          }
        } catch (e) {
          print('⚠️  Skipping proposal submission test: $e');
        }
      });

      test('should handle proposal acceptance workflow', () async {
        if (projectId == null || proposalId == null) {
          print('⚠️  Skipping proposal acceptance test: Missing IDs');
          return;
        }

        try {
          final acceptResponse = await client.post(
            Uri.parse('$baseUrl/api/freelancing/proposals/$proposalId/accept'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
          );
          
          expect(acceptResponse.statusCode, anyOf([200, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping proposal acceptance test: $e');
        }
      });
    });

    group('Project Management', () {
      test('should get project details', () async {
        try {
          final response = await client.get(
            Uri.parse('$baseUrl/api/freelancing/projects/test-project-id'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
          );
          
          expect(response.statusCode, anyOf([200, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping project details test: $e');
        }
      });

      test('should update project status', () async {
        try {
          final response = await client.put(
            Uri.parse('$baseUrl/api/freelancing/projects/test-project-id/status'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
            body: jsonEncode({
              'status': 'in_progress',
            }),
          );
          
          expect(response.statusCode, anyOf([200, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping project status update test: $e');
        }
      });
    });

    group('Payment Integration', () {
      test('should handle milestone payments', () async {
        try {
          final response = await client.post(
            Uri.parse('$baseUrl/api/freelancing/payments/milestone'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
            body: jsonEncode({
              'project_id': 'test-project-id',
              'amount': 250,
              'milestone_description': 'First milestone completed',
            }),
          );
          
          expect(response.statusCode, anyOf([200, 201, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping milestone payment test: $e');
        }
      });

      test('should handle project completion payment', () async {
        try {
          final response = await client.post(
            Uri.parse('$baseUrl/api/freelancing/payments/complete'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
            body: jsonEncode({
              'project_id': 'test-project-id',
              'final_amount': 750,
            }),
          );
          
          expect(response.statusCode, anyOf([200, 201, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping completion payment test: $e');
        }
      });
    });

    group('Communication Features', () {
      test('should handle project messaging', () async {
        try {
          final response = await client.post(
            Uri.parse('$baseUrl/api/freelancing/messages'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? 'test-token'}',
            },
            body: jsonEncode({
              'project_id': 'test-project-id',
              'message': 'Project update: Making good progress!',
              'message_type': 'update',
            }),
          );
          
          expect(response.statusCode, anyOf([200, 201, 401, 404]));
        } catch (e) {
          print('⚠️  Skipping project messaging test: $e');
        }
      });
    });
  });
}
