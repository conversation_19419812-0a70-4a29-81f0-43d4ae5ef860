import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'pagination.g.dart';

/// Pagination information for API responses
@JsonSerializable()
class PaginationInfo extends Equatable {
  /// Current page number (1-based)
  final int page;

  /// Number of items per page
  final int limit;

  /// Total number of items
  final int total;

  /// Total number of pages
  final int totalPages;

  /// Whether there is a next page
  final bool hasNext;

  /// Whether there is a previous page
  final bool hasPrev;

  const PaginationInfo({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  /// Creates PaginationInfo from JSON
  factory PaginationInfo.fromJson(Map<String, dynamic> json) =>
      _$PaginationInfoFromJson(json);

  /// Converts PaginationInfo to JSON
  Map<String, dynamic> toJson() => _$PaginationInfoToJson(this);

  /// Creates pagination info from basic parameters
  factory PaginationInfo.fromParams({
    required int page,
    required int limit,
    required int total,
  }) {
    final totalPages = (total / limit).ceil();
    return PaginationInfo(
      page: page,
      limit: limit,
      total: total,
      totalPages: totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    );
  }

  @override
  List<Object> get props => [page, limit, total, totalPages, hasNext, hasPrev];
}
