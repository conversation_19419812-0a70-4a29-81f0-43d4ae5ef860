#!/bin/bash

# Quester Test Runner Script
# Provides comprehensive test execution with database setup and cleanup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
TEST_COMPOSE_FILE="$PROJECT_ROOT/app/docker-compose.test.yml"
TEST_ENV_FILE="$PROJECT_ROOT/.env.test"

# Default values
TEST_TYPE="all"
CLEANUP_AFTER="true"
VERBOSE="false"
PARALLEL="false"
COVERAGE="false"
PERFORMANCE_TEST="false"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Quester Test Runner

Usage: $0 [OPTIONS]

OPTIONS:
    -t, --type TYPE         Test type: unit, integration, performance, all (default: all)
    -c, --no-cleanup        Don't cleanup test environment after tests
    -v, --verbose           Enable verbose output
    -p, --parallel          Run tests in parallel where possible
    --coverage              Generate test coverage reports
    --performance           Run performance tests
    -h, --help              Show this help message

EXAMPLES:
    $0                      # Run all tests
    $0 -t unit              # Run only unit tests
    $0 -t integration -v    # Run integration tests with verbose output
    $0 --performance        # Run performance tests
    $0 --coverage           # Run tests with coverage

ENVIRONMENT:
    Set TEST_POSTGRES_PORT, TEST_REDIS_PORT to customize ports
    Set DART_TEST_CONCURRENCY to control test parallelism
EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                TEST_TYPE="$2"
                shift 2
                ;;
            -c|--no-cleanup)
                CLEANUP_AFTER="false"
                shift
                ;;
            -v|--verbose)
                VERBOSE="true"
                shift
                ;;
            -p|--parallel)
                PARALLEL="true"
                shift
                ;;
            --coverage)
                COVERAGE="true"
                shift
                ;;
            --performance)
                PERFORMANCE_TEST="true"
                TEST_TYPE="performance"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Setup test environment
setup_test_environment() {
    print_status "Setting up test environment..."
    
    # Create test environment file if it doesn't exist
    if [[ ! -f "$TEST_ENV_FILE" ]]; then
        cat > "$TEST_ENV_FILE" << EOF
# Test Environment Configuration
TEST_POSTGRES_USER=quester
TEST_POSTGRES_PASSWORD=questerpass
TEST_POSTGRES_DB=questerdb
TEST_POSTGRES_PORT=5433
TEST_REDIS_PORT=6380
DART_ENV=test
LOG_LEVEL=info
EOF
        print_status "Created test environment file: $TEST_ENV_FILE"
    fi
    
    # Load test environment
    if [[ -f "$TEST_ENV_FILE" ]]; then
        export $(grep -v '^#' "$TEST_ENV_FILE" | xargs)
    fi
    
    # Start test services
    print_status "Starting test database services..."
    
    if [[ "$PERFORMANCE_TEST" == "true" ]]; then
        docker-compose -f "$TEST_COMPOSE_FILE" --profile performance-test up -d postgres-perf-test
    else
        docker-compose -f "$TEST_COMPOSE_FILE" up -d postgres-test redis-test
    fi
    
    # Wait for services to be healthy
    print_status "Waiting for test services to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose -f "$TEST_COMPOSE_FILE" ps | grep -q "healthy"; then
            print_success "Test services are ready!"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            print_error "Test services failed to start within timeout"
            docker-compose -f "$TEST_COMPOSE_FILE" logs
            exit 1
        fi
        
        print_status "Attempt $attempt/$max_attempts - waiting for services..."
        sleep 2
        ((attempt++))
    done
}

# Run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    
    local test_args=""
    if [[ "$VERBOSE" == "true" ]]; then
        test_args="$test_args --reporter=verbose"
    fi
    
    if [[ "$PARALLEL" == "true" ]]; then
        test_args="$test_args --concurrency=4"
    fi
    
    # Shared package tests
    print_status "Running shared package tests..."
    cd "$PROJECT_ROOT/shared"
    dart pub get
    dart test $test_args
    
    # Server package tests (excluding integration tests)
    print_status "Running server unit tests..."
    cd "$PROJECT_ROOT/server"
    dart pub get
    dart test $test_args --exclude-tags=integration
    
    # Client package tests
    if [[ -d "$PROJECT_ROOT/client" ]]; then
        print_status "Running client tests..."
        cd "$PROJECT_ROOT/client"
        flutter pub get
        flutter test $test_args
    fi
    
    cd "$PROJECT_ROOT"
}

# Run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    local test_args=""
    if [[ "$VERBOSE" == "true" ]]; then
        test_args="$test_args --reporter=verbose"
    fi
    
    # Set test environment variables
    export TEST_POSTGRES_HOST=localhost
    export TEST_POSTGRES_PORT=${TEST_POSTGRES_PORT:-5433}
    export TEST_REDIS_HOST=localhost
    export TEST_REDIS_PORT=${TEST_REDIS_PORT:-6380}
    
    cd "$PROJECT_ROOT/server"
    dart test $test_args --tags=integration test/integration/
    
    cd "$PROJECT_ROOT"
}

# Run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    export TEST_POSTGRES_HOST=localhost
    export TEST_POSTGRES_PORT=${PERF_TEST_POSTGRES_PORT:-5434}
    
    cd "$PROJECT_ROOT/server"
    dart test --reporter=verbose test/performance_optimization_test.dart
    
    cd "$PROJECT_ROOT"
}

# Generate coverage report
generate_coverage() {
    print_status "Generating test coverage report..."
    
    cd "$PROJECT_ROOT/server"
    dart pub global activate coverage
    dart test --coverage=coverage
    dart pub global run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --packages=.dart_tool/package_config.json --report-on=lib
    
    if command -v genhtml &> /dev/null; then
        genhtml coverage/lcov.info -o coverage/html
        print_success "Coverage report generated: coverage/html/index.html"
    else
        print_warning "genhtml not found. Install lcov to generate HTML coverage report."
        print_success "LCOV coverage data: coverage/lcov.info"
    fi
    
    cd "$PROJECT_ROOT"
}

# Cleanup test environment
cleanup_test_environment() {
    if [[ "$CLEANUP_AFTER" == "true" ]]; then
        print_status "Cleaning up test environment..."
        docker-compose -f "$TEST_COMPOSE_FILE" down -v --remove-orphans
        print_success "Test environment cleaned up"
    else
        print_warning "Skipping cleanup (use --no-cleanup to keep test environment running)"
    fi
}

# Main execution
main() {
    print_status "Starting Quester Test Runner..."
    
    # Parse arguments
    parse_args "$@"
    
    # Setup test environment
    setup_test_environment
    
    # Run tests based on type
    case "$TEST_TYPE" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "all")
            run_unit_tests
            run_integration_tests
            if [[ "$PERFORMANCE_TEST" == "true" ]]; then
                run_performance_tests
            fi
            ;;
        *)
            print_error "Invalid test type: $TEST_TYPE"
            show_usage
            exit 1
            ;;
    esac
    
    # Generate coverage if requested
    if [[ "$COVERAGE" == "true" ]]; then
        generate_coverage
    fi
    
    # Cleanup
    cleanup_test_environment
    
    print_success "All tests completed successfully!"
}

# Trap to ensure cleanup on script exit
trap cleanup_test_environment EXIT

# Run main function
main "$@"
