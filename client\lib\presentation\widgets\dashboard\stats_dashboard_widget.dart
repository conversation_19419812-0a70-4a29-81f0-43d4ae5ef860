import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../gamification/progress_indicator_widget.dart';

/// Comprehensive stats dashboard with gamification metrics
class StatsDashboard extends StatelessWidget {
  final Map<String, dynamic> userStats;
  final bool isLoading;

  const StatsDashboard({
    super.key,
    required this.userStats,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLevelSection(context),
          const SizedBox(height: 24),
          _buildStatsGrid(context),
          const SizedBox(height: 24),
          _buildProgressSection(context),
          const SizedBox(height: 24),
          _buildAchievementsPreview(context),
        ],
      ),
    );
  }

  Widget _buildLevelSection(BuildContext context) {
    final theme = Theme.of(context);
    final currentLevel = userStats['level'] ?? 1;
    final currentXP = userStats['experience'] ?? 0;
    final nextLevelXP = userStats['nextLevelExperience'] ?? 1000;
    final progress = currentXP / nextLevelXP;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            LevelProgressIndicator(
              currentLevel: currentLevel,
              progress: progress,
              size: 100,
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Level $currentLevel',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$currentXP / $nextLevelXP XP',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 12),
                  GameProgressIndicator(
                    progress: progress,
                    label: 'Progress to Level ${currentLevel + 1}',
                    showPercentage: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid(BuildContext context) {
    final stats = [
      {
        'title': 'Total Points',
        'value': userStats['totalPoints'] ?? 0,
        'icon': Icons.stars,
        'color': AppTheme.successColor,
      },
      {
        'title': 'Quests Completed',
        'value': userStats['completedQuests'] ?? 0,
        'icon': Icons.task_alt,
        'color': AppTheme.infoColor,
      },
      {
        'title': 'Current Streak',
        'value': userStats['currentStreak'] ?? 0,
        'icon': Icons.local_fire_department,
        'color': AppTheme.warningColor,
      },
      {
        'title': 'Achievements',
        'value': userStats['achievementCount'] ?? 0,
        'icon': Icons.emoji_events,
        'color': AppTheme.legendaryColor,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return _buildStatCard(context, stat);
      },
    );
  }

  Widget _buildStatCard(BuildContext context, Map<String, dynamic> stat) {
    final theme = Theme.of(context);
    final color = stat['color'] as Color;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                stat['icon'] as IconData,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '${stat['value']}',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              stat['title'] as String,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    final theme = Theme.of(context);
    final weeklyGoal = userStats['weeklyGoal'] ?? 100;
    final weeklyProgress = userStats['weeklyProgress'] ?? 0;
    final dailyGoal = userStats['dailyGoal'] ?? 20;
    final dailyProgress = userStats['dailyProgress'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goals Progress',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GameProgressIndicator(
              progress: weeklyProgress / weeklyGoal,
              label: 'Weekly Goal',
              color: AppTheme.infoColor,
              leading: const Icon(Icons.calendar_view_week, size: 16),
              trailing: Text(
                '$weeklyProgress / $weeklyGoal',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 16),
            GameProgressIndicator(
              progress: dailyProgress / dailyGoal,
              label: 'Daily Goal',
              color: AppTheme.successColor,
              leading: const Icon(Icons.today, size: 16),
              trailing: Text(
                '$dailyProgress / $dailyGoal',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementsPreview(BuildContext context) {
    final theme = Theme.of(context);
    final recentAchievements = userStats['recentAchievements'] as List? ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Achievements',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to achievements page
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (recentAchievements.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.emoji_events_outlined,
                      size: 48,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No achievements yet',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Complete quests to earn achievements!',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                    ),
                  ],
                ),
              )
            else
              SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: recentAchievements.length,
                  itemBuilder: (context, index) {
                    final achievement = recentAchievements[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: Column(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppTheme.getRarityColor(
                                achievement['rarity'] ?? 'common',
                              ).withValues(alpha: 0.2),
                              border: Border.all(
                                color: AppTheme.getRarityColor(
                                  achievement['rarity'] ?? 'common',
                                ),
                                width: 2,
                              ),
                            ),
                            child: Icon(
                              Icons.emoji_events,
                              color: AppTheme.getRarityColor(
                                achievement['rarity'] ?? 'common',
                              ),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            achievement['title'] ?? '',
                            style: theme.textTheme.labelSmall,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Quick stats widget for app bar or drawer
class QuickStats extends StatelessWidget {
  final int level;
  final int points;
  final int streak;

  const QuickStats({
    super.key,
    required this.level,
    required this.points,
    required this.streak,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildQuickStat(
          context,
          'LV $level',
          Icons.trending_up,
          theme.colorScheme.primary,
        ),
        const SizedBox(width: 16),
        _buildQuickStat(
          context,
          '$points',
          Icons.stars,
          AppTheme.successColor,
        ),
        const SizedBox(width: 16),
        _buildQuickStat(
          context,
          '$streak',
          Icons.local_fire_department,
          AppTheme.warningColor,
        ),
      ],
    );
  }

  Widget _buildQuickStat(
    BuildContext context,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          value,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
