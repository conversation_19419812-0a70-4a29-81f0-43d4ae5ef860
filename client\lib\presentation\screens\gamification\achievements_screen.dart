import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/gamification/gamification_bloc.dart';
import '../../widgets/gamification/achievement_widget.dart';
import '../../widgets/common/responsive_builder.dart';

/// Comprehensive achievements screen showing all user achievements
class AchievementsScreen extends StatefulWidget {
  const AchievementsScreen({super.key});

  @override
  State<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends State<AchievementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  AchievementCategory _selectedCategory = AchievementCategory.all;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadAchievements();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Achievements'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All', icon: Icon(Icons.all_inclusive)),
            Tab(text: 'General', icon: Icon(Icons.star)),
            Tab(text: 'Quest', icon: Icon(Icons.explore)),
            Tab(text: 'Social', icon: Icon(Icons.people)),
            Tab(text: 'Special', icon: Icon(Icons.military_tech)),
          ],
          onTap: (index) {
            setState(() {
              _selectedCategory = AchievementCategory.values[index];
            });
          },
        ),
      ),
      body: ResponsiveBuilder(
        mobile: _buildMobileLayout,
        tablet: _buildTabletLayout,
        desktop: _buildDesktopLayout,
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildAchievementsList(AchievementCategory.all),
        _buildAchievementsList(AchievementCategory.general),
        _buildAchievementsList(AchievementCategory.quest),
        _buildAchievementsList(AchievementCategory.social),
        _buildAchievementsList(AchievementCategory.special),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 200,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: _buildCategoryList(context),
        ),
        Expanded(
          child: _buildAchievementsList(_selectedCategory),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Column(
            children: [
              _buildCategoryList(context),
              const Divider(),
              _buildAchievementStats(context),
            ],
          ),
        ),
        Expanded(
          child: _buildAchievementsList(_selectedCategory),
        ),
      ],
    );
  }

  Widget _buildCategoryList(BuildContext context) {
    final categories = [
      (AchievementCategory.all, 'All Achievements', Icons.all_inclusive),
      (AchievementCategory.general, 'General', Icons.star),
      (AchievementCategory.quest, 'Quest Master', Icons.explore),
      (AchievementCategory.social, 'Social', Icons.people),
      (AchievementCategory.special, 'Special', Icons.military_tech),
    ];

    return ListView.builder(
      shrinkWrap: true,
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final (category, title, icon) = categories[index];
        final isSelected = _selectedCategory == category;

        return ListTile(
          leading: Icon(
            icon,
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          title: Text(
            title,
            style: TextStyle(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          selected: isSelected,
          onTap: () {
            setState(() {
              _selectedCategory = category;
            });
          },
        );
      },
    );
  }

  Widget _buildAchievementStats(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoaded && state.achievements != null) {
          final achievements = state.achievements!;
          final unlockedCount = achievements.where((a) => a.isActive).length;
          final totalCount = achievements.length;
          final progress = totalCount > 0 ? unlockedCount / totalCount : 0.0;

          return Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Progress',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  '$unlockedCount / $totalCount unlocked',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildRarityBreakdown(context, achievements),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildRarityBreakdown(BuildContext context, List<Achievement> achievements) {
    final rarityCount = <AchievementRarity, int>{};
    for (final achievement in achievements.where((a) => a.isActive)) {
      rarityCount[achievement.rarity] = (rarityCount[achievement.rarity] ?? 0) + 1;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'By Rarity',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        ...AchievementRarity.values.map((rarity) {
          final count = rarityCount[rarity] ?? 0;
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  rarity.name.toUpperCase(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _getRarityColor(rarity),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  count.toString(),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildAchievementsList(AchievementCategory category) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is GamificationError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Failed to load achievements',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: _loadAchievements,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        } else if (state is GamificationLoaded && state.achievements != null) {
          final achievements = _filterAchievements(state.achievements!, category);
          
          if (achievements.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'No achievements in this category',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: AchievementWidget(
              showAll: true,
              layoutStyle: context.isMobile 
                  ? AchievementLayoutStyle.list 
                  : AchievementLayoutStyle.grid,
            ),
          );
        }

        return const Center(
          child: Text('No achievements available'),
        );
      },
    );
  }

  List<Achievement> _filterAchievements(List<Achievement> achievements, AchievementCategory category) {
    if (category == AchievementCategory.all) {
      return achievements;
    }

    // Map UI categories to Achievement types
    AchievementType? targetType;
    switch (category) {
      case AchievementCategory.quest:
        targetType = AchievementType.progress;
        break;
      case AchievementCategory.social:
        targetType = AchievementType.collaboration;
        break;
      case AchievementCategory.special:
        targetType = AchievementType.special;
        break;
      case AchievementCategory.general:
        targetType = AchievementType.skill;
        break;
      default:
        return achievements;
    }

    return achievements.where((achievement) => achievement.type == targetType).toList();
  }

  Color _getRarityColor(AchievementRarity rarity) {
    switch (rarity) {
      case AchievementRarity.common:
        return Colors.grey;
      case AchievementRarity.uncommon:
        return Colors.green;
      case AchievementRarity.rare:
        return Colors.blue;
      case AchievementRarity.epic:
        return Colors.purple;
      case AchievementRarity.legendary:
        return Colors.orange;
    }
  }

  void _loadAchievements() {
    context.read<GamificationBloc>().add(const LoadUserAchievements());
  }
}

/// Achievement categories for filtering
enum AchievementCategory {
  all,
  general,
  quest,
  social,
  special,
}
