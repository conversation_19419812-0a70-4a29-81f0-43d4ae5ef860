/// Tests for error handling middleware
library;

import 'dart:convert';
import 'dart:io';
import 'package:test/test.dart';
import 'package:shelf/shelf.dart';
import 'package:server/middleware/error_middleware.dart';
import 'package:server/services/logging_service.dart';

void main() {
  group('ErrorMiddleware', () {
    setUp(() {
      // Clear error statistics before each test
      LoggingService.clearErrorStats();
    });

    group('Custom Exceptions', () {
      test('ValidationException should contain message and field errors', () {
        const exception = ValidationException(
          'Validation failed',
          fieldErrors: {'email': ['Invalid email format']},
        );
        
        expect(exception.message, equals('Validation failed'));
        expect(exception.fieldErrors, isNotNull);
        expect(exception.fieldErrors!['email'], contains('Invalid email format'));
        expect(exception.toString(), contains('ValidationException'));
      });

      test('AuthenticationException should contain message and code', () {
        const exception = AuthenticationException(
          'Invalid credentials',
          code: 'INVALID_CREDENTIALS',
        );
        
        expect(exception.message, equals('Invalid credentials'));
        expect(exception.code, equals('INVALID_CREDENTIALS'));
        expect(exception.toString(), contains('AuthenticationException'));
      });

      test('BusinessLogicException should contain context', () {
        const exception = BusinessLogicException(
          'Business rule violated',
          code: 'RULE_VIOLATION',
          context: {'rule': 'max_quests_per_user', 'limit': 10},
        );
        
        expect(exception.message, equals('Business rule violated'));
        expect(exception.code, equals('RULE_VIOLATION'));
        expect(exception.context, isNotNull);
        expect(exception.context!['rule'], equals('max_quests_per_user'));
      });
    });

    group('Error Handler', () {
      test('should handle ValidationException with 422 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const ValidationException(
            'Invalid input',
            fieldErrors: {'name': ['Required field']},
          );
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(422));
        expect(response.headers['content-type'], contains('application/json'));
        expect(response.headers['X-Request-ID'], isNotNull);

        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('Invalid input'));
        expect(data['error']['field_errors'], isNotNull);
        expect(data['error']['field_errors']['name'], contains('Required field'));
      });

      test('should handle AuthenticationException with 401 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const AuthenticationException('Token expired');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(401));
        
        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('Token expired'));
      });

      test('should handle AuthorizationException with 403 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const AuthorizationException(
            'Insufficient permissions',
            requiredPermission: 'admin',
          );
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(403));
        
        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('Insufficient permissions'));
      });

      test('should handle DatabaseException with 500 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const DatabaseException(
            'Connection failed',
            query: 'SELECT * FROM users',
          );
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(500));
        
        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('Database operation failed'));
      });

      test('should handle ExternalServiceException with 503 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const ExternalServiceException(
            'Service unavailable',
            service: 'payment_gateway',
            statusCode: 503,
          );
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(503));
        
        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('External service unavailable'));
        expect(data['error']['code'], equals('SERVICE_UNAVAILABLE'));
      });

      test('should handle FormatException with 400 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const FormatException('Invalid JSON format');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(400));
        
        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('Invalid request format'));
      });

      test('should handle SocketException with 503 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const SocketException('Network unreachable');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(503));
        
        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('Network connectivity issue'));
        expect(data['error']['code'], equals('NETWORK_ERROR'));
      });

      test('should handle unexpected errors with 500 status', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw Exception('Unexpected error');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(500));
        
        final body = await response.readAsString();
        final data = jsonDecode(body);
        expect(data['success'], isFalse);
        expect(data['error']['message'], equals('An unexpected error occurred'));
      });

      test('should add request ID to all error responses', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const ValidationException('Test error');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.headers['X-Request-ID'], isNotNull);
        expect(response.headers['X-Request-ID'], matches(RegExp(r'^req_\d+_\d{4}$')));
      });
    });

    group('Request Logger', () {
      test('should log incoming requests and responses', () async {
        final middleware = ErrorMiddleware.requestLogger();
        final handler = middleware((request) async {
          return Response.ok('Success');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        final response = await handler(request);

        expect(response.statusCode, equals(200));
        expect(response.headers['X-Request-ID'], isNotNull);
        expect(response.headers['X-Request-ID'], matches(RegExp(r'^req_\d+_\d{4}$')));
      });

      test('should add request context for downstream handlers', () async {
        final middleware = ErrorMiddleware.requestLogger();
        final handler = middleware((request) async {
          expect(request.context['requestId'], isNotNull);
          expect(request.context['startTime'], isA<DateTime>());
          return Response.ok('Success');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        await handler(request);
      });

      test('should handle errors in downstream handlers', () async {
        final middleware = ErrorMiddleware.requestLogger();
        final handler = middleware((request) async {
          throw Exception('Test error');
        });

        final request = Request('GET', Uri.parse('http://localhost/test'));
        
        expect(() => handler(request), throwsException);
      });
    });

    group('Client IP Extraction', () {
      test('should extract IP from X-Forwarded-For header', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const ValidationException('Test error');
        });

        final request = Request(
          'GET',
          Uri.parse('http://localhost/test'),
          headers: {'x-forwarded-for': '***********, ********'},
        );
        
        final response = await handler(request);
        expect(response.statusCode, equals(422));
        // IP extraction is tested indirectly through error logging
      });

      test('should extract IP from X-Real-IP header', () async {
        final middleware = ErrorMiddleware.errorHandler();
        final handler = middleware((request) async {
          throw const ValidationException('Test error');
        });

        final request = Request(
          'GET',
          Uri.parse('http://localhost/test'),
          headers: {'x-real-ip': '*************'},
        );
        
        final response = await handler(request);
        expect(response.statusCode, equals(422));
        // IP extraction is tested indirectly through error logging
      });
    });
  });
}
