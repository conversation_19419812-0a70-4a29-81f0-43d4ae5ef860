import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Widget for composing and sending messages
class MessageInputWidget extends StatefulWidget {
  /// Text controller for the input field
  final TextEditingController controller;
  
  /// Focus node for the input field
  final FocusNode focusNode;
  
  /// Callback when a message is sent
  final Function(String) onSendMessage;
  
  /// Callback when user starts typing
  final VoidCallback? onStartTyping;
  
  /// Callback when user stops typing
  final VoidCallback? onStopTyping;
  
  /// Callback when attach file is pressed
  final VoidCallback? onAttachFile;
  
  /// Callback when record voice is pressed
  final VoidCallback? onRecordVoice;
  
  /// Whether the input is enabled
  final bool enabled;

  const MessageInputWidget({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onSendMessage,
    this.onStartTyping,
    this.onStopTyping,
    this.onAttachFile,
    this.onRecordVoice,
    this.enabled = true,
  });

  @override
  State<MessageInputWidget> createState() => _MessageInputWidgetState();
}

class _MessageInputWidgetState extends State<MessageInputWidget> {
  bool _isTyping = false;
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Attach button
                IconButton(
                  onPressed: widget.enabled ? widget.onAttachFile : null,
                  icon: const Icon(Icons.attach_file),
                  tooltip: 'Attach file',
                ),
                
                // Text input field
                Expanded(
                  child: Container(
                    constraints: const BoxConstraints(
                      minHeight: 40,
                      maxHeight: 120,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                      ),
                    ),
                    child: TextField(
                      controller: widget.controller,
                      focusNode: widget.focusNode,
                      enabled: widget.enabled,
                      maxLines: null,
                      textCapitalization: TextCapitalization.sentences,
                      decoration: InputDecoration(
                        hintText: 'Type a message...',
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.defaultPadding,
                          vertical: AppConstants.smallPadding,
                        ),
                        suffixIcon: _buildEmojiButton(),
                      ),
                      onSubmitted: _handleSubmit,
                    ),
                  ),
                ),
                
                const SizedBox(width: AppConstants.smallPadding),
                
                // Send/Voice button
                _buildSendButton(),
              ],
            ),
            
            // Quick actions row
            if (_shouldShowQuickActions()) _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmojiButton() {
    return IconButton(
      onPressed: widget.enabled ? _showEmojiPicker : null,
      icon: const Icon(Icons.emoji_emotions_outlined),
      tooltip: 'Add emoji',
    );
  }

  Widget _buildSendButton() {
    final hasText = widget.controller.text.trim().isNotEmpty;
    
    if (_isRecording) {
      return _buildRecordingButton();
    }
    
    if (hasText) {
      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          shape: BoxShape.circle,
        ),
        child: IconButton(
          onPressed: widget.enabled ? _handleSend : null,
          icon: Icon(
            Icons.send,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
          tooltip: 'Send message',
        ),
      );
    } else {
      return IconButton(
        onPressed: widget.enabled ? _startVoiceRecording : null,
        icon: const Icon(Icons.mic),
        tooltip: 'Record voice message',
      );
    }
  }

  Widget _buildRecordingButton() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.error,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: _stopVoiceRecording,
        icon: Icon(
          Icons.stop,
          color: Theme.of(context).colorScheme.onError,
        ),
        tooltip: 'Stop recording',
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.only(top: AppConstants.smallPadding),
      child: Row(
        children: [
          _buildQuickActionChip(
            icon: Icons.camera_alt,
            label: 'Camera',
            onTap: _openCamera,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          _buildQuickActionChip(
            icon: Icons.photo_library,
            label: 'Gallery',
            onTap: _openGallery,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          _buildQuickActionChip(
            icon: Icons.location_on,
            label: 'Location',
            onTap: _shareLocation,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          _buildQuickActionChip(
            icon: Icons.insert_drive_file,
            label: 'File',
            onTap: widget.onAttachFile,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionChip({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _shouldShowQuickActions() {
    // Show quick actions when input is focused and empty
    return widget.focusNode.hasFocus && widget.controller.text.isEmpty;
  }

  void _onTextChanged() {
    final hasText = widget.controller.text.trim().isNotEmpty;
    
    if (hasText && !_isTyping) {
      setState(() {
        _isTyping = true;
      });
      widget.onStartTyping?.call();
    } else if (!hasText && _isTyping) {
      setState(() {
        _isTyping = false;
      });
      widget.onStopTyping?.call();
    }
  }

  void _handleSend() {
    final text = widget.controller.text.trim();
    if (text.isNotEmpty) {
      widget.onSendMessage(text);
      widget.controller.clear();
      setState(() {
        _isTyping = false;
      });
      widget.onStopTyping?.call();
    }
  }

  void _handleSubmit(String text) {
    _handleSend();
  }

  void _showEmojiPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 300,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Text(
              'Emoji Picker',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 8,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _getCommonEmojis().length,
                itemBuilder: (context, index) {
                  final emoji = _getCommonEmojis()[index];
                  return GestureDetector(
                    onTap: () {
                      _insertEmoji(emoji);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                      ),
                      child: Center(
                        child: Text(
                          emoji,
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _insertEmoji(String emoji) {
    final text = widget.controller.text;
    final selection = widget.controller.selection;
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      emoji,
    );
    
    widget.controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(
        offset: selection.start + emoji.length,
      ),
    );
  }

  List<String> _getCommonEmojis() {
    return [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
      '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
      '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
      '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
      '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
      '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
      '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
    ];
  }

  void _startVoiceRecording() {
    setState(() {
      _isRecording = true;
    });
    widget.onRecordVoice?.call();
  }

  void _stopVoiceRecording() {
    setState(() {
      _isRecording = false;
    });
    // TODO: Handle voice recording stop
  }

  void _openCamera() {
    // TODO: Implement camera functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Camera feature coming soon!')),
    );
  }

  void _openGallery() {
    // TODO: Implement gallery functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Gallery feature coming soon!')),
    );
  }

  void _shareLocation() {
    // TODO: Implement location sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Location sharing coming soon!')),
    );
  }
}
