import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Custom button widget for authentication screens
class AuthButton extends StatelessWidget {
  /// Button text
  final String text;
  
  /// Whether the button is loading
  final bool isLoading;
  
  /// Whether the button is enabled
  final bool enabled;
  
  /// Button press callback
  final VoidCallback? onPressed;
  
  /// Button style variant
  final AuthButtonStyle style;
  
  /// Custom width
  final double? width;
  
  /// Custom height
  final double? height;
  
  /// Icon to display
  final IconData? icon;
  
  /// Whether icon should be at the end
  final bool iconAtEnd;

  const AuthButton({
    super.key,
    required this.text,
    this.isLoading = false,
    this.enabled = true,
    this.onPressed,
    this.style = AuthButtonStyle.primary,
    this.width,
    this.height,
    this.icon,
    this.iconAtEnd = false,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = width ?? double.infinity;
    final effectiveHeight = height ?? AppConstants.buttonHeight;
    
    return SizedBox(
      width: effectiveWidth,
      height: effectiveHeight,
      child: _buildButton(context),
    );
  }

  /// Build the button based on style
  Widget _buildButton(BuildContext context) {
    switch (style) {
      case AuthButtonStyle.primary:
        return _buildPrimaryButton(context);
      case AuthButtonStyle.secondary:
        return _buildSecondaryButton(context);
      case AuthButtonStyle.outline:
        return _buildOutlineButton(context);
      case AuthButtonStyle.text:
        return _buildTextButton(context);
    }
  }

  /// Build primary button
  Widget _buildPrimaryButton(BuildContext context) {
    return ElevatedButton(
      onPressed: _getEffectiveOnPressed(),
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        disabledBackgroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.12),
        disabledForegroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
        elevation: 2,
        shadowColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.largePadding,
          vertical: AppConstants.defaultPadding,
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  /// Build secondary button
  Widget _buildSecondaryButton(BuildContext context) {
    return ElevatedButton(
      onPressed: _getEffectiveOnPressed(),
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.secondary,
        foregroundColor: Theme.of(context).colorScheme.onSecondary,
        disabledBackgroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.12),
        disabledForegroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
        elevation: 2,
        shadowColor: Theme.of(context).colorScheme.secondary.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.largePadding,
          vertical: AppConstants.defaultPadding,
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  /// Build outline button
  Widget _buildOutlineButton(BuildContext context) {
    return OutlinedButton(
      onPressed: _getEffectiveOnPressed(),
      style: OutlinedButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.primary,
        disabledForegroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
        side: BorderSide(
          color: enabled 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurface.withOpacity(0.12),
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.largePadding,
          vertical: AppConstants.defaultPadding,
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  /// Build text button
  Widget _buildTextButton(BuildContext context) {
    return TextButton(
      onPressed: _getEffectiveOnPressed(),
      style: TextButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.primary,
        disabledForegroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.largePadding,
          vertical: AppConstants.defaultPadding,
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  /// Build button content
  Widget _buildButtonContent(BuildContext context) {
    if (isLoading) {
      return _buildLoadingContent(context);
    }

    if (icon != null) {
      return _buildIconContent(context);
    }

    return _buildTextContent(context);
  }

  /// Build loading content
  Widget _buildLoadingContent(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getContentColor(context),
            ),
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Text(
          'Loading...',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: _getContentColor(context),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// Build icon content
  Widget _buildIconContent(BuildContext context) {
    final iconWidget = Icon(
      icon,
      size: 20,
      color: _getContentColor(context),
    );

    final textWidget = Text(
      text,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        color: _getContentColor(context),
        fontWeight: FontWeight.w600,
      ),
    );

    if (iconAtEnd) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          textWidget,
          const SizedBox(width: AppConstants.smallPadding),
          iconWidget,
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          iconWidget,
          const SizedBox(width: AppConstants.smallPadding),
          textWidget,
        ],
      );
    }
  }

  /// Build text content
  Widget _buildTextContent(BuildContext context) {
    return Text(
      text,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        color: _getContentColor(context),
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// Get effective onPressed callback
  VoidCallback? _getEffectiveOnPressed() {
    if (!enabled || isLoading) return null;
    return onPressed;
  }

  /// Get content color based on button style and state
  Color _getContentColor(BuildContext context) {
    if (!enabled) {
      return Theme.of(context).colorScheme.onSurface.withOpacity(0.38);
    }

    switch (style) {
      case AuthButtonStyle.primary:
        return Theme.of(context).colorScheme.onPrimary;
      case AuthButtonStyle.secondary:
        return Theme.of(context).colorScheme.onSecondary;
      case AuthButtonStyle.outline:
      case AuthButtonStyle.text:
        return Theme.of(context).colorScheme.primary;
    }
  }
}

/// Gradient button for special actions
class GradientAuthButton extends StatelessWidget {
  /// Button text
  final String text;
  
  /// Whether the button is loading
  final bool isLoading;
  
  /// Whether the button is enabled
  final bool enabled;
  
  /// Button press callback
  final VoidCallback? onPressed;
  
  /// Gradient colors
  final List<Color>? gradientColors;
  
  /// Custom width
  final double? width;
  
  /// Custom height
  final double? height;
  
  /// Icon to display
  final IconData? icon;

  const GradientAuthButton({
    super.key,
    required this.text,
    this.isLoading = false,
    this.enabled = true,
    this.onPressed,
    this.gradientColors,
    this.width,
    this.height,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = width ?? double.infinity;
    final effectiveHeight = height ?? AppConstants.buttonHeight;
    final effectiveGradientColors = gradientColors ?? [
      Theme.of(context).colorScheme.primary,
      Theme.of(context).colorScheme.secondary,
    ];

    return SizedBox(
      width: effectiveWidth,
      height: effectiveHeight,
      child: Container(
        decoration: BoxDecoration(
          gradient: enabled
              ? LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: effectiveGradientColors,
                )
              : null,
          color: enabled ? null : Theme.of(context).colorScheme.onSurface.withOpacity(0.12),
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          boxShadow: enabled && !isLoading ? [
            BoxShadow(
              color: effectiveGradientColors.first.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _getEffectiveOnPressed(),
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.largePadding,
                vertical: AppConstants.defaultPadding,
              ),
              child: _buildContent(context),
            ),
          ),
        ),
      ),
    );
  }

  /// Build button content
  Widget _buildContent(BuildContext context) {
    if (isLoading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                enabled 
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
              ),
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            'Loading...',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: enabled 
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 20,
            color: enabled 
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            text,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: enabled 
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        color: enabled 
            ? Theme.of(context).colorScheme.onPrimary
            : Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// Get effective onPressed callback
  VoidCallback? _getEffectiveOnPressed() {
    if (!enabled || isLoading) return null;
    return onPressed;
  }
}

/// Button style enumeration
enum AuthButtonStyle {
  primary,
  secondary,
  outline,
  text,
}
