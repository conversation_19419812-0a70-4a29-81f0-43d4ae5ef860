import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../blocs/collaboration/collaboration_bloc.dart';

class CollaborationScreen extends StatefulWidget {
  const CollaborationScreen({super.key});

  @override
  State<CollaborationScreen> createState() => _CollaborationScreenState();
}

class _CollaborationScreenState extends State<CollaborationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load initial collaboration data
    _loadCollaborationData();
  }

  void _loadCollaborationData() {
    final bloc = context.read<CollaborationBloc>();
    bloc.add(const LoadUserTeamQuests(userId: 'current-user-id')); // Would use actual user ID
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Collaboration'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.groups), text: 'Team Quests'),
            Tab(icon: Icon(Icons.message), text: 'Messages'),
            Tab(icon: Icon(Icons.history), text: 'Activity'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<CollaborationBloc>().add(const RefreshCollaboration());
            },
          ),
        ],
      ),
      body: BlocConsumer<CollaborationBloc, CollaborationState>(
        listener: (context, state) {
          if (state is CollaborationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is TeamQuestCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Team quest created successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is TeamQuestJoined) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is MessageSent) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is CollaborationLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildTeamQuests(state),
              _buildMessages(state),
              _buildActivity(state),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateTeamQuestDialog(context),
        tooltip: 'Create Team Quest',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTeamQuests(CollaborationState state) {
    final teamQuests = state is CollaborationLoaded ? state.userTeamQuests : null;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Your Team Quests',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              OutlinedButton.icon(
                onPressed: () => _showJoinQuestDialog(context),
                icon: const Icon(Icons.group_add),
                label: const Text('Join Quest'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (teamQuests != null && teamQuests.isNotEmpty)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: teamQuests.length,
              itemBuilder: (context, index) {
                final quest = teamQuests[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: InkWell(
                    onTap: () => _showQuestDetails(context, quest),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getQuestStatusIcon(quest['status']),
                                color: _getQuestStatusColor(quest['status']),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  quest['title'] ?? 'Untitled Quest',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Chip(
                                label: Text(quest['status'] ?? 'Unknown'),
                                backgroundColor: _getQuestStatusColor(quest['status']).withValues(alpha: 0.2),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            quest['description'] ?? 'No description available',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Icon(Icons.people, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                '${quest['member_count'] ?? 0} members',
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                              const SizedBox(width: 16),
                              Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                'Created: ${_formatDate(quest['created_at'])}',
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: (quest['progress'] ?? 0.0) / 100.0,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getQuestStatusColor(quest['status']),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${quest['progress'] ?? 0}% Complete',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            )
          else
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.groups,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No team quests yet',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Create or join a team quest to start collaborating!',
                    style: TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMessages(CollaborationState state) {
    final messages = state is CollaborationLoaded ? state.teamMessages : null;
    
    return Column(
      children: [
        // Messages Header
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Team Messages',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  // Load messages for current quest - would need quest ID
                  // context.read<CollaborationBloc>().add(LoadTeamMessages(questId: 'current-quest-id'));
                },
              ),
            ],
          ),
        ),
        
        // Messages List
        Expanded(
          child: messages != null && messages.isNotEmpty
              ? ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isCurrentUser = message['sender_id'] == 'current-user-id'; // Would use actual user ID
                    
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        mainAxisAlignment: isCurrentUser 
                            ? MainAxisAlignment.end 
                            : MainAxisAlignment.start,
                        children: [
                          if (!isCurrentUser) ...[
                            CircleAvatar(
                              radius: 16,
                              child: Text(
                                (message['sender_name'] ?? 'U')[0].toUpperCase(),
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: isCurrentUser 
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[300],
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (!isCurrentUser)
                                    Text(
                                      message['sender_name'] ?? 'Unknown',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  Text(
                                    message['content'] ?? '',
                                    style: TextStyle(
                                      color: isCurrentUser ? Colors.white : Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _formatTime(message['timestamp']),
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: isCurrentUser 
                                          ? Colors.white70 
                                          : Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (isCurrentUser) ...[
                            const SizedBox(width: 8),
                            CircleAvatar(
                              radius: 16,
                              backgroundColor: Theme.of(context).primaryColor,
                              child: const Text(
                                'You',
                                style: TextStyle(fontSize: 10, color: Colors.white),
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                )
              : Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.message,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No messages yet',
                        style: TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Start a conversation with your team!',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
        ),
        
        // Message Input
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border(top: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Type a message...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  onSubmitted: (text) {
                    if (text.trim().isNotEmpty) {
                      _sendMessage(context, text.trim());
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.send),
                onPressed: () {
                  // Would get text from controller
                  _sendMessage(context, 'Sample message');
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivity(CollaborationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recent Activity',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // Mock activity data
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 10, // Mock count
            itemBuilder: (context, index) {
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                    child: Icon(
                      _getActivityIcon(index % 4),
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  title: Text(_getActivityTitle(index % 4)),
                  subtitle: Text(_getActivitySubtitle(index % 4)),
                  trailing: Text(
                    '${index + 1}h ago',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showCreateTeamQuestDialog(BuildContext context) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    final maxParticipantsController = TextEditingController(text: '5');
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Create Team Quest'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Quest Title',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: maxParticipantsController,
                  decoration: const InputDecoration(
                    labelText: 'Max Participants',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  context.read<CollaborationBloc>().add(CreateTeamQuest(questData: {
                    'creator_id': 'current-user-id', // Would use actual user ID
                    'title': titleController.text,
                    'description': descriptionController.text,
                    'max_participants': int.tryParse(maxParticipantsController.text) ?? 5,
                    'invited_users': [], // Could add user selection
                  }));
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Create'),
            ),
          ],
        );
      },
    );
  }

  void _showJoinQuestDialog(BuildContext context) {
    final questIdController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Join Team Quest'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: questIdController,
                decoration: const InputDecoration(
                  labelText: 'Quest ID or Invite Code',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Enter the quest ID or invite code shared by your team',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (questIdController.text.isNotEmpty) {
                  context.read<CollaborationBloc>().add(JoinTeamQuest(
                    questId: questIdController.text,
                    userId: 'current-user-id', // Would use actual user ID
                  ));
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Join'),
            ),
          ],
        );
      },
    );
  }

  void _showQuestDetails(BuildContext context, Map<String, dynamic> quest) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(quest['title'] ?? 'Quest Details'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Description',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(quest['description'] ?? 'No description'),
                const SizedBox(height: 16),
                Text(
                  'Progress',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                LinearProgressIndicator(
                  value: (quest['progress'] ?? 0.0) / 100.0,
                ),
                Text('${quest['progress'] ?? 0}% Complete'),
                const SizedBox(height: 16),
                Text(
                  'Team Members',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('${quest['member_count'] ?? 0} members'),
                const SizedBox(height: 16),
                Text(
                  'Status',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Chip(
                  label: Text(quest['status'] ?? 'Unknown'),
                  backgroundColor: _getQuestStatusColor(quest['status']).withValues(alpha: 0.2),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
            if (quest['status'] == 'active')
              ElevatedButton(
                onPressed: () {
                  // Load messages for this quest
                  context.read<CollaborationBloc>().add(LoadTeamMessages(questId: quest['id']));
                  Navigator.of(context).pop();
                  // Switch to messages tab
                  _tabController.animateTo(1);
                },
                child: const Text('Open Chat'),
              ),
          ],
        );
      },
    );
  }

  void _sendMessage(BuildContext context, String message) {
    // Would use actual quest ID
    context.read<CollaborationBloc>().add(SendTeamMessage(
      questId: 'current-quest-id',
      messageData: {
        'sender_id': 'current-user-id',
        'message': message,
      },
    ));
  }

  IconData _getQuestStatusIcon(String? status) {
    switch (status) {
      case 'active':
        return Icons.play_circle;
      case 'completed':
        return Icons.check_circle;
      case 'paused':
        return Icons.pause_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  Color _getQuestStatusColor(String? status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'completed':
        return Colors.blue;
      case 'paused':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getActivityIcon(int type) {
    switch (type) {
      case 0:
        return Icons.person_add;
      case 1:
        return Icons.task_alt;
      case 2:
        return Icons.message;
      case 3:
        return Icons.emoji_events;
      default:
        return Icons.event;
    }
  }

  String _getActivityTitle(int type) {
    switch (type) {
      case 0:
        return 'New member joined';
      case 1:
        return 'Task completed';
      case 2:
        return 'New message posted';
      case 3:
        return 'Achievement unlocked';
      default:
        return 'Activity occurred';
    }
  }

  String _getActivitySubtitle(int type) {
    switch (type) {
      case 0:
        return 'John Doe joined "Web Development Quest"';
      case 1:
        return 'Alice completed "Setup Development Environment"';
      case 2:
        return 'Bob posted in "Design Review Team"';
      case 3:
        return 'Team unlocked "Collaboration Master" badge';
      default:
        return 'Something happened in your team';
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'Unknown';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Unknown';
    }
  }

  String _formatTime(String? dateString) {
    if (dateString == null) return 'Unknown';
    try {
      final date = DateTime.parse(dateString);
      return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Unknown';
    }
  }
}