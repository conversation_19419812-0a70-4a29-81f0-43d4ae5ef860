import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../blocs/notification/notification_bloc.dart';
import '../../widgets/common/responsive_builder.dart';
import '../../widgets/common/app_bar_widget.dart';

/// Screen for managing notification preferences
class NotificationPreferencesScreen extends StatefulWidget {
  const NotificationPreferencesScreen({super.key});

  @override
  State<NotificationPreferencesScreen> createState() => _NotificationPreferencesScreenState();
}

class _NotificationPreferencesScreenState extends State<NotificationPreferencesScreen> {
  NotificationPreferences? _preferences;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    context.read<NotificationBloc>().add(const LoadNotificationPreferences());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: 'Notification Preferences',
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _savePreferences,
              child: const Text('Save'),
            ),
        ],
      ),
      body: BlocConsumer<NotificationBloc, NotificationState>(
        listener: (context, state) {
          if (state is NotificationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is NotificationPreferencesUpdated) {
            setState(() {
              _preferences = state.preferences;
              _hasChanges = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Preferences updated successfully')),
            );
          }
        },
        builder: (context, state) {
          if (state is NotificationLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is NotificationPreferencesLoaded) {
            _preferences ??= state.preferences;
            return _buildPreferencesContent(context);
          } else if (state is NotificationError) {
            return _buildErrorState(context, state.message);
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildPreferencesContent(BuildContext context) {
    if (_preferences == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return ResponsiveBuilder(
      mobile: (context) => _buildMobileLayout(context),
      tablet: (context) => _buildTabletLayout(context),
      desktop: (context) => _buildDesktopLayout(context),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGlobalSettings(context),
          const SizedBox(height: AppConstants.largePadding),
          _buildDeliveryMethodSettings(context),
          const SizedBox(height: AppConstants.largePadding),
          _buildCategorySettings(context),
          const SizedBox(height: AppConstants.largePadding),
          _buildQuietHoursSettings(context),
          const SizedBox(height: AppConstants.largePadding),
          _buildFrequencySettings(context),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              children: [
                _buildGlobalSettings(context),
                const SizedBox(height: AppConstants.largePadding),
                _buildDeliveryMethodSettings(context),
                const SizedBox(height: AppConstants.largePadding),
                _buildFrequencySettings(context),
              ],
            ),
          ),
          const SizedBox(width: AppConstants.largePadding),
          Expanded(
            child: Column(
              children: [
                _buildCategorySettings(context),
                const SizedBox(height: AppConstants.largePadding),
                _buildQuietHoursSettings(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.largePadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Column(
              children: [
                _buildGlobalSettings(context),
                const SizedBox(height: AppConstants.largePadding),
                _buildDeliveryMethodSettings(context),
              ],
            ),
          ),
          const SizedBox(width: AppConstants.largePadding),
          Expanded(
            flex: 3,
            child: _buildCategorySettings(context),
          ),
          const SizedBox(width: AppConstants.largePadding),
          Expanded(
            flex: 2,
            child: Column(
              children: [
                _buildQuietHoursSettings(context),
                const SizedBox(height: AppConstants.largePadding),
                _buildFrequencySettings(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlobalSettings(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Global Settings',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SwitchListTile(
              title: const Text('Enable Notifications'),
              subtitle: const Text('Turn all notifications on or off'),
              value: _preferences!.globalEnabled,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences!.copyWith(globalEnabled: value);
                  _hasChanges = true;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryMethodSettings(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Delivery Methods',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...DeliveryMethod.values.map((method) {
              final isEnabled = _preferences!.deliveryMethodSettings[method] ?? false;
              return SwitchListTile(
                title: Text(_getDeliveryMethodName(method)),
                subtitle: Text(_getDeliveryMethodDescription(method)),
                value: isEnabled,
                onChanged: _preferences!.globalEnabled ? (value) {
                  setState(() {
                    final newSettings = Map<DeliveryMethod, bool>.from(
                      _preferences!.deliveryMethodSettings,
                    );
                    newSettings[method] = value;
                    _preferences = _preferences!.copyWith(
                      deliveryMethodSettings: newSettings,
                    );
                    _hasChanges = true;
                  });
                } : null,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySettings(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Categories',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...NotificationCategory.values.map((category) {
              final settings = _preferences!.typeSettings[category];
              final isEnabled = settings?.enabled ?? true;
              
              return ExpansionTile(
                leading: Icon(_getCategoryIcon(category)),
                title: Text(_getCategoryName(category)),
                subtitle: Text(isEnabled ? 'Enabled' : 'Disabled'),
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        SwitchListTile(
                          title: const Text('Enable'),
                          value: isEnabled,
                          onChanged: _preferences!.globalEnabled ? (value) {
                            _updateCategorySettings(category, enabled: value);
                          } : null,
                        ),
                        if (isEnabled) ...[
                          ...NotificationPriority.values.map((priority) {
                            final priorityEnabled = settings?.allowedPriorities.contains(priority) ?? true;
                            return CheckboxListTile(
                              title: Text('${priority.name.toUpperCase()} Priority'),
                              value: priorityEnabled,
                              onChanged: (value) {
                                _updateCategoryPriority(category, priority, value ?? false);
                              },
                            );
                          }),
                        ],
                      ],
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildQuietHoursSettings(BuildContext context) {
    final quietHours = _preferences!.quietHours;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quiet Hours',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SwitchListTile(
              title: const Text('Enable Quiet Hours'),
              subtitle: const Text('Reduce notifications during specified hours'),
              value: quietHours?.enabled ?? false,
              onChanged: _preferences!.globalEnabled ? (value) {
                setState(() {
                  _preferences = _preferences!.copyWith(
                    quietHours: QuietHours(
                      enabled: value,
                      startHour: quietHours?.startHour ?? 22,
                      endHour: quietHours?.endHour ?? 8,
                      allowCritical: quietHours?.allowCritical ?? true,
                    ),
                  );
                  _hasChanges = true;
                });
              } : null,
            ),
            if (quietHours?.enabled == true) ...[
              const Divider(),
              ListTile(
                title: const Text('Start Time'),
                subtitle: Text('${quietHours!.startHour}:00'),
                trailing: const Icon(Icons.access_time),
                onTap: () => _selectTime(context, true),
              ),
              ListTile(
                title: const Text('End Time'),
                subtitle: Text('${quietHours.endHour}:00'),
                trailing: const Icon(Icons.access_time),
                onTap: () => _selectTime(context, false),
              ),
              SwitchListTile(
                title: const Text('Allow Critical Notifications'),
                subtitle: const Text('Allow high-priority notifications during quiet hours'),
                value: quietHours.allowCritical,
                onChanged: (value) {
                  setState(() {
                    _preferences = _preferences!.copyWith(
                      quietHours: quietHours.copyWith(allowCritical: value),
                    );
                    _hasChanges = true;
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFrequencySettings(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Frequency',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...NotificationFrequency.values.map((frequency) {
              return RadioListTile<NotificationFrequency>(
                title: Text(_getFrequencyName(frequency)),
                subtitle: Text(_getFrequencyDescription(frequency)),
                value: frequency,
                groupValue: _preferences!.frequency,
                onChanged: _preferences!.globalEnabled ? (value) {
                  setState(() {
                    _preferences = _preferences!.copyWith(frequency: value);
                    _hasChanges = true;
                  });
                } : null,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load preferences',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () {
              context.read<NotificationBloc>().add(const LoadNotificationPreferences());
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _updateCategorySettings(NotificationCategory category, {bool? enabled}) {
    setState(() {
      final currentSettings = _preferences!.typeSettings[category];
      final newSettings = Map<NotificationCategory, NotificationTypeSettings>.from(
        _preferences!.typeSettings,
      );
      
      newSettings[category] = NotificationTypeSettings(
        enabled: enabled ?? currentSettings?.enabled ?? true,
        enabledMethods: currentSettings?.enabledMethods ?? [DeliveryMethod.inApp, DeliveryMethod.push],
      );
      
      _preferences = _preferences!.copyWith(typeSettings: newSettings);
      _hasChanges = true;
    });
  }

  void _updateCategoryPriority(NotificationCategory category, NotificationPriority priority, bool enabled) {
    setState(() {
      final currentSettings = _preferences!.typeSettings[category];

      // Since allowedPriorities doesn't exist in NotificationTypeSettings,
      // we'll just update the enabled status for now
      final newSettings = Map<NotificationCategory, NotificationTypeSettings>.from(
        _preferences!.typeSettings,
      );

      newSettings[category] = NotificationTypeSettings(
        enabled: enabled,
        enabledMethods: currentSettings?.enabledMethods ?? [DeliveryMethod.inApp, DeliveryMethod.push],
      );
      
      _preferences = _preferences!.copyWith(typeSettings: newSettings);
      _hasChanges = true;
    });
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final currentTime = isStartTime 
        ? _preferences!.quietHours?.startHour ?? 22
        : _preferences!.quietHours?.endHour ?? 8;
    
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(hour: currentTime, minute: 0),
    );
    
    if (time != null) {
      setState(() {
        final quietHours = _preferences!.quietHours!;
        _preferences = _preferences!.copyWith(
          quietHours: isStartTime
              ? quietHours.copyWith(startHour: time.hour)
              : quietHours.copyWith(endHour: time.hour),
        );
        _hasChanges = true;
      });
    }
  }

  void _savePreferences() {
    if (_preferences != null) {
      final request = UpdateNotificationPreferencesRequest(
        globalEnabled: _preferences!.globalEnabled,
        deliveryMethodSettings: _preferences!.deliveryMethodSettings,
        typeSettings: _preferences!.typeSettings,
        quietHours: _preferences!.quietHours,
        frequency: _preferences!.frequency,
      );
      
      context.read<NotificationBloc>().add(
        UpdateNotificationPreferences(request: request),
      );
    }
  }

  String _getDeliveryMethodName(DeliveryMethod method) {
    switch (method) {
      case DeliveryMethod.push:
        return 'Push Notifications';
      case DeliveryMethod.email:
        return 'Email';
      case DeliveryMethod.sms:
        return 'SMS';
      case DeliveryMethod.inApp:
        return 'In-App';
      case DeliveryMethod.webhook:
        return 'Webhook';
    }
  }

  String _getDeliveryMethodDescription(DeliveryMethod method) {
    switch (method) {
      case DeliveryMethod.push:
        return 'Receive notifications on your device';
      case DeliveryMethod.email:
        return 'Receive notifications via email';
      case DeliveryMethod.sms:
        return 'Receive notifications via text message';
      case DeliveryMethod.inApp:
        return 'Show notifications within the app';
      case DeliveryMethod.webhook:
        return 'Send notifications to external services';
    }
  }

  String _getCategoryName(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.system:
        return 'System';
      case NotificationCategory.quest:
        return 'Quests';
      case NotificationCategory.achievement:
        return 'Achievements';
      case NotificationCategory.social:
        return 'Social';
      case NotificationCategory.reminder:
        return 'Reminders';
      case NotificationCategory.marketing:
        return 'Marketing';
      case NotificationCategory.security:
        return 'Security';
      case NotificationCategory.team:
        return 'Team';
    }
  }

  IconData _getCategoryIcon(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.system:
        return Icons.settings;
      case NotificationCategory.quest:
        return Icons.flag;
      case NotificationCategory.achievement:
        return Icons.emoji_events;
      case NotificationCategory.social:
        return Icons.people;
      case NotificationCategory.reminder:
        return Icons.alarm;
      case NotificationCategory.marketing:
        return Icons.campaign;
      case NotificationCategory.security:
        return Icons.security;
      case NotificationCategory.team:
        return Icons.group;
    }
  }

  String _getFrequencyName(NotificationFrequency frequency) {
    switch (frequency) {
      case NotificationFrequency.immediate:
        return 'Immediate';
      case NotificationFrequency.every15Minutes:
        return 'Every 15 Minutes';
      case NotificationFrequency.hourly:
        return 'Hourly';
      case NotificationFrequency.daily:
        return 'Daily';
      case NotificationFrequency.weekly:
        return 'Weekly';
      case NotificationFrequency.never:
        return 'Never';
    }
  }

  String _getFrequencyDescription(NotificationFrequency frequency) {
    switch (frequency) {
      case NotificationFrequency.immediate:
        return 'Receive notifications as they happen';
      case NotificationFrequency.every15Minutes:
        return 'Batch notifications every 15 minutes';
      case NotificationFrequency.hourly:
        return 'Batch notifications every hour';
      case NotificationFrequency.daily:
        return 'Receive a daily digest';
      case NotificationFrequency.weekly:
        return 'Receive a weekly digest';
      case NotificationFrequency.never:
        return 'Disable all notifications';
    }
  }
}
