import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../gamification/progress_indicator_widget.dart';

/// Enhanced quest card with gamification elements
class QuestCard extends StatefulWidget {
  final String title;
  final String description;
  final String difficulty;
  final String status;
  final double progress;
  final int points;
  final int? timeLimit;
  final List<String> tags;
  final VoidCallback? onTap;
  final bool isExpanded;

  const QuestCard({
    super.key,
    required this.title,
    required this.description,
    required this.difficulty,
    required this.status,
    required this.progress,
    required this.points,
    this.timeLimit,
    this.tags = const [],
    this.onTap,
    this.isExpanded = false,
  });

  @override
  State<QuestCard> createState() => _QuestCardState();
}

class _QuestCardState extends State<QuestCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (widget.isExpanded) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(QuestCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isExpanded != widget.isExpanded) {
      if (widget.isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final difficultyColor = AppTheme.getDifficultyColor(widget.difficulty);
    final statusColor = AppTheme.getStatusColor(widget.status);

    return Card(
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(theme, statusColor),
                ],
              ),
              const SizedBox(height: 8),

              // Description
              Text(
                widget.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
                maxLines: widget.isExpanded ? null : 2,
                overflow: widget.isExpanded ? null : TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Progress indicator
              GameProgressIndicator(
                progress: widget.progress,
                label: 'Progress',
                color: statusColor,
                leading: Icon(
                  Icons.trending_up,
                  size: 16,
                  color: statusColor,
                ),
              ),
              const SizedBox(height: 12),

              // Quest metadata
              Row(
                children: [
                  _buildDifficultyChip(theme, difficultyColor),
                  const SizedBox(width: 8),
                  _buildPointsChip(theme),
                  if (widget.timeLimit != null) ...[
                    const SizedBox(width: 8),
                    _buildTimeChip(theme),
                  ],
                  const Spacer(),
                  if (widget.tags.isNotEmpty)
                    IconButton(
                      icon: AnimatedRotation(
                        turns: widget.isExpanded ? 0.5 : 0,
                        duration: const Duration(milliseconds: 300),
                        child: const Icon(Icons.expand_more),
                      ),
                      onPressed: () {
                        setState(() {
                          // Toggle expansion would be handled by parent
                        });
                      },
                    ),
                ],
              ),

              // Expandable tags section
              SizeTransition(
                sizeFactor: _expandAnimation,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 12),
                    const Divider(),
                    const SizedBox(height: 8),
                    Text(
                      'Tags',
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: widget.tags.map((tag) => Chip(
                        label: Text(tag),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      )).toList(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(ThemeData theme, Color statusColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        widget.status.toUpperCase(),
        style: theme.textTheme.labelSmall?.copyWith(
          color: statusColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDifficultyChip(ThemeData theme, Color difficultyColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: difficultyColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getDifficultyIcon(widget.difficulty),
            size: 12,
            color: difficultyColor,
          ),
          const SizedBox(width: 4),
          Text(
            widget.difficulty.toUpperCase(),
            style: theme.textTheme.labelSmall?.copyWith(
              color: difficultyColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointsChip(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.successColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.stars,
            size: 12,
            color: AppTheme.successColor,
          ),
          const SizedBox(width: 4),
          Text(
            '${widget.points}',
            style: theme.textTheme.labelSmall?.copyWith(
              color: AppTheme.successColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeChip(ThemeData theme) {
    final hours = widget.timeLimit! ~/ 60;
    final minutes = widget.timeLimit! % 60;
    final timeText = hours > 0 ? '${hours}h ${minutes}m' : '${minutes}m';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.warningColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            size: 12,
            color: AppTheme.warningColor,
          ),
          const SizedBox(width: 4),
          Text(
            timeText,
            style: theme.textTheme.labelSmall?.copyWith(
              color: AppTheme.warningColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Icons.sentiment_satisfied;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_dissatisfied;
      case 'expert':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }
}

/// Compact quest list item
class QuestListItem extends StatelessWidget {
  final String title;
  final String status;
  final double progress;
  final int points;
  final VoidCallback? onTap;

  const QuestListItem({
    super.key,
    required this.title,
    required this.status,
    required this.progress,
    required this.points,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = AppTheme.getStatusColor(status);

    return ListTile(
      onTap: onTap,
      leading: CircleAvatar(
        backgroundColor: statusColor.withValues(alpha: 0.2),
        child: Icon(
          _getStatusIcon(status),
          color: statusColor,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: LinearProgressIndicator(
        value: progress,
        backgroundColor: statusColor.withValues(alpha: 0.2),
        valueColor: AlwaysStoppedAnimation<Color>(statusColor),
        borderRadius: BorderRadius.circular(2),
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppTheme.successColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '$points pts',
          style: theme.textTheme.labelSmall?.copyWith(
            color: AppTheme.successColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Icons.check_circle;
      case 'in_progress':
        return Icons.play_circle;
      case 'pending':
        return Icons.schedule;
      case 'failed':
        return Icons.error;
      default:
        return Icons.help;
    }
  }
}
