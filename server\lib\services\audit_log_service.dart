import 'dart:convert';
import 'dart:io';
import 'package:uuid/uuid.dart';
import '../services/database_service.dart';

/// Audit logging service for security events
class AuditLogService {
  static final AuditLogService _instance = AuditLogService._internal();
  factory AuditLogService() => _instance;
  AuditLogService._internal();

  final DatabaseService _dbService = DatabaseService();
  final Uuid _uuid = const Uuid();

  /// Log authentication event
  Future<void> logAuthEvent({
    required String userId,
    required String action,
    required bool success,
    String? organizationId,
    Map<String, dynamic>? metadata,
    required String ipAddress,
    required String userAgent,
    String? errorMessage,
    String? sessionId,
  }) async {
    try {
      await _logSecurityEvent(
        eventType: 'authentication',
        userId: userId,
        action: action,
        success: success,
        organizationId: organizationId,
        metadata: metadata,
        ipAddress: ipAddress,
        userAgent: userAgent,
        errorMessage: errorMessage,
        sessionId: sessionId,
      );
    } catch (e) {
      print('❌ Error logging auth event: $e');
      // Don't throw - logging failures shouldn't break authentication
    }
  }

  /// Log authorization event (permission checks)
  Future<void> logAuthzEvent({
    required String userId,
    required String resource,
    required String action,
    required bool granted,
    String? organizationId,
    String? roleId,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
  }) async {
    try {
      await _logSecurityEvent(
        eventType: 'authorization',
        userId: userId,
        action: '$action on $resource',
        success: granted,
        organizationId: organizationId,
        metadata: {
          'resource': resource,
          'permission_action': action,
          'role_id': roleId,
        },
        ipAddress: ipAddress ?? 'unknown',
        userAgent: userAgent ?? 'unknown',
        sessionId: sessionId,
      );
    } catch (e) {
      print('❌ Error logging authz event: $e');
    }
  }

  /// Log data access event
  Future<void> logDataAccess({
    required String userId,
    required String resourceType,
    required String resourceId,
    required String action,
    String? organizationId,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      await _logSecurityEvent(
        eventType: 'data_access',
        userId: userId,
        action: '$action $resourceType',
        success: true,
        organizationId: organizationId,
        metadata: {
          'resource_type': resourceType,
          'resource_id': resourceId,
          'access_action': action,
          ...?additionalData,
        },
        ipAddress: ipAddress ?? 'unknown',
        userAgent: userAgent ?? 'unknown',
        sessionId: sessionId,
      );
    } catch (e) {
      print('❌ Error logging data access: $e');
    }
  }

  /// Log admin action
  Future<void> logAdminAction({
    required String adminUserId,
    required String action,
    required bool success,
    String? targetUserId,
    String? organizationId,
    Map<String, dynamic>? metadata,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    String? errorMessage,
  }) async {
    try {
      await _logSecurityEvent(
        eventType: 'admin_action',
        userId: adminUserId,
        action: action,
        success: success,
        organizationId: organizationId,
        metadata: {
          'target_user_id': targetUserId,
          ...?metadata,
        },
        ipAddress: ipAddress ?? 'unknown',
        userAgent: userAgent ?? 'unknown',
        sessionId: sessionId,
        errorMessage: errorMessage,
      );
    } catch (e) {
      print('❌ Error logging admin action: $e');
    }
  }

  /// Log security event (rate limiting, suspicious activity, etc.)
  Future<void> logSecurityEvent({
    required String eventType,
    required String action,
    String? userId,
    String? organizationId,
    required String ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
    String? sessionId,
    String? severity = 'medium',
  }) async {
    try {
      await _logSecurityEvent(
        eventType: eventType,
        userId: userId ?? 'anonymous',
        action: action,
        success: false, // Security events are typically threats/failures
        organizationId: organizationId,
        metadata: {
          'severity': severity,
          ...?metadata,
        },
        ipAddress: ipAddress,
        userAgent: userAgent ?? 'unknown',
        sessionId: sessionId,
      );
    } catch (e) {
      print('❌ Error logging security event: $e');
    }
  }

  /// Core logging method
  Future<void> _logSecurityEvent({
    required String eventType,
    required String userId,
    required String action,
    required bool success,
    String? organizationId,
    Map<String, dynamic>? metadata,
    required String ipAddress,
    required String userAgent,
    String? errorMessage,
    String? sessionId,
  }) async {
    final logEntry = {
      'id': _uuid.v4(),
      'event_type': eventType,
      'user_id': userId,
      'organization_id': organizationId,
      'session_id': sessionId,
      'action': action,
      'success': success,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'metadata': jsonEncode(metadata ?? {}),
      'error_message': errorMessage,
      'timestamp': DateTime.now().toIso8601String(),
    };

    await _storeAuditLog(logEntry);
    
    // Also log to console in development
    if (Platform.environment['NODE_ENV'] != 'production') {
      final status = success ? '✅' : '❌';
      print('$status Audit Log [$eventType]: $action by $userId from $ipAddress');
    }
  }

  /// Store audit log in database
  Future<void> _storeAuditLog(Map<String, dynamic> logEntry) async {
    try {
      await _dbService.execute('''
        INSERT INTO quester.security_audit_logs (
          id, event_type, user_id, organization_id, session_id,
          action, success, ip_address, user_agent, 
          metadata, error_message, timestamp
        ) VALUES (
          @id::uuid, @event_type, @user_id::uuid, @organization_id::uuid, @session_id::uuid,
          @action, @success, @ip_address, @user_agent,
          @metadata::jsonb, @error_message, @timestamp::timestamptz
        )
      ''', parameters: {
        'id': logEntry['id'],
        'event_type': logEntry['event_type'],
        'user_id': logEntry['user_id'],
        'organization_id': logEntry['organization_id'],
        'session_id': logEntry['session_id'],
        'action': logEntry['action'],
        'success': logEntry['success'],
        'ip_address': logEntry['ip_address'],
        'user_agent': logEntry['user_agent'],
        'metadata': logEntry['metadata'],
        'error_message': logEntry['error_message'],
        'timestamp': logEntry['timestamp'],
      });
    } catch (e) {
      print('❌ Error storing audit log: $e');
      // Don't throw - we still want to preserve the main operation
    }
  }

  /// Query audit logs with filters
  Future<List<Map<String, dynamic>>> queryAuditLogs({
    String? userId,
    String? organizationId,
    String? eventType,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    bool? success,
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      final conditions = <String>[];
      final parameters = <String, dynamic>{
        'limit': limit,
        'offset': offset,
      };

      if (userId != null) {
        conditions.add('user_id = @userId::uuid');
        parameters['userId'] = userId;
      }

      if (organizationId != null) {
        conditions.add('organization_id = @organizationId::uuid');
        parameters['organizationId'] = organizationId;
      }

      if (eventType != null) {
        conditions.add('event_type = @eventType');
        parameters['eventType'] = eventType;
      }

      if (action != null) {
        conditions.add('action ILIKE @action');
        parameters['action'] = '%$action%';
      }

      if (startDate != null) {
        conditions.add('timestamp >= @startDate::timestamptz');
        parameters['startDate'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        conditions.add('timestamp <= @endDate::timestamptz');
        parameters['endDate'] = endDate.toIso8601String();
      }

      if (success != null) {
        conditions.add('success = @success');
        parameters['success'] = success;
      }

      final whereClause = conditions.isEmpty ? '' : 'WHERE ${conditions.join(' AND ')}';

      final result = await _dbService.execute('''
        SELECT id, event_type, user_id, organization_id, session_id,
               action, success, ip_address, user_agent,
               metadata, error_message, timestamp
        FROM quester.security_audit_logs
        $whereClause
        ORDER BY timestamp DESC
        LIMIT @limit OFFSET @offset
      ''', parameters: parameters);

      return result.map((row) => {
        'id': row[0].toString(),
        'event_type': row[1].toString(),
        'user_id': row[2]?.toString(),
        'organization_id': row[3]?.toString(),
        'session_id': row[4]?.toString(),
        'action': row[5].toString(),
        'success': row[6] as bool,
        'ip_address': row[7].toString(),
        'user_agent': row[8]?.toString(),
        'metadata': row[9] != null ? jsonDecode(row[9].toString()) : {},
        'error_message': row[10]?.toString(),
        'timestamp': row[11].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error querying audit logs: $e');
      return [];
    }
  }

  /// Get audit statistics
  Future<Map<String, dynamic>> getAuditStats({
    String? userId,
    String? organizationId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final conditions = <String>[];
      final parameters = <String, dynamic>{};

      if (userId != null) {
        conditions.add('user_id = @userId::uuid');
        parameters['userId'] = userId;
      }

      if (organizationId != null) {
        conditions.add('organization_id = @organizationId::uuid');
        parameters['organizationId'] = organizationId;
      }

      if (startDate != null) {
        conditions.add('timestamp >= @startDate::timestamptz');
        parameters['startDate'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        conditions.add('timestamp <= @endDate::timestamptz');
        parameters['endDate'] = endDate.toIso8601String();
      }

      final whereClause = conditions.isEmpty ? '' : 'WHERE ${conditions.join(' AND ')}';

      final result = await _dbService.execute('''
        SELECT 
          COUNT(*) as total_events,
          COUNT(CASE WHEN success = true THEN 1 END) as successful_events,
          COUNT(CASE WHEN success = false THEN 1 END) as failed_events,
          COUNT(DISTINCT user_id) as unique_users,
          COUNT(DISTINCT ip_address) as unique_ips,
          COUNT(CASE WHEN event_type = 'authentication' THEN 1 END) as auth_events,
          COUNT(CASE WHEN event_type = 'authorization' THEN 1 END) as authz_events,
          COUNT(CASE WHEN event_type = 'data_access' THEN 1 END) as data_events,
          COUNT(CASE WHEN event_type = 'admin_action' THEN 1 END) as admin_events
        FROM quester.security_audit_logs
        $whereClause
      ''', parameters: parameters);

      if (result.isEmpty) {
        return {};
      }

      final row = result.first;
      return {
        'total_events': row[0],
        'successful_events': row[1],
        'failed_events': row[2],
        'unique_users': row[3],
        'unique_ips': row[4],
        'auth_events': row[5],
        'authz_events': row[6],
        'data_events': row[7],
        'admin_events': row[8],
      };
    } catch (e) {
      print('❌ Error getting audit stats: $e');
      return {};
    }
  }
}
