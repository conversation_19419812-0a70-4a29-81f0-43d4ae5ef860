import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Widget that displays typing indicators for active users
class TypingIndicatorWidget extends StatefulWidget {
  /// List of users currently typing
  final List<String> typingUsers;
  
  /// Maximum number of users to show in the indicator
  final int maxUsers;
  
  /// Animation duration for the typing dots
  final Duration animationDuration;

  const TypingIndicatorWidget({
    super.key,
    required this.typingUsers,
    this.maxUsers = 3,
    this.animationDuration = const Duration(milliseconds: 1500),
  });

  @override
  State<TypingIndicatorWidget> createState() => _TypingIndicatorWidgetState();
}

class _TypingIndicatorWidgetState extends State<TypingIndicatorWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    if (widget.typingUsers.isNotEmpty) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(TypingIndicatorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.typingUsers.isNotEmpty && oldWidget.typingUsers.isEmpty) {
      _animationController.forward();
    } else if (widget.typingUsers.isEmpty && oldWidget.typingUsers.isNotEmpty) {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.typingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          child: Row(
            children: [
              const SizedBox(width: 40), // Space for avatar alignment
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding,
                  vertical: AppConstants.smallPadding,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _getTypingText(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    TypingDotsAnimation(
                      duration: widget.animationDuration,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTypingText() {
    final users = widget.typingUsers.take(widget.maxUsers).toList();
    
    if (users.length == 1) {
      return '${users[0]} is typing';
    } else if (users.length == 2) {
      return '${users[0]} and ${users[1]} are typing';
    } else if (users.length == 3) {
      return '${users[0]}, ${users[1]} and ${users[2]} are typing';
    } else {
      return '${users[0]}, ${users[1]} and ${users.length - 2} others are typing';
    }
  }
}

/// Animated dots that indicate typing activity
class TypingDotsAnimation extends StatefulWidget {
  /// Duration of the animation cycle
  final Duration duration;
  
  /// Color of the dots
  final Color? color;
  
  /// Size of each dot
  final double dotSize;
  
  /// Number of dots to display
  final int dotCount;

  const TypingDotsAnimation({
    super.key,
    this.duration = const Duration(milliseconds: 1500),
    this.color,
    this.dotSize = 4.0,
    this.dotCount = 3,
  });

  @override
  State<TypingDotsAnimation> createState() => _TypingDotsAnimationState();
}

class _TypingDotsAnimationState extends State<TypingDotsAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    _controllers = List.generate(
      widget.dotCount,
      (index) => AnimationController(
        duration: widget.duration,
        vsync: this,
      ),
    );
    
    _animations = _controllers.map((controller) =>
      Tween<double>(begin: 0.4, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut,
        ),
      ),
    ).toList();
    
    _startAnimations();
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final effectiveColor = widget.color ?? 
        Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.6);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        widget.dotCount,
        (index) => AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.only(
                right: index < widget.dotCount - 1 ? 2 : 0,
              ),
              child: Opacity(
                opacity: _animations[index].value,
                child: Container(
                  width: widget.dotSize,
                  height: widget.dotSize,
                  decoration: BoxDecoration(
                    color: effectiveColor,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

/// Compact typing indicator for smaller spaces
class CompactTypingIndicator extends StatelessWidget {
  /// List of users currently typing
  final List<String> typingUsers;

  const CompactTypingIndicator({
    super.key,
    required this.typingUsers,
  });

  @override
  Widget build(BuildContext context) {
    if (typingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.smallPadding,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.edit,
            size: 12,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 4),
          Text(
            '${typingUsers.length}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Typing indicator with user avatars
class AvatarTypingIndicator extends StatelessWidget {
  /// List of users currently typing with their avatar URLs
  final List<TypingUser> typingUsers;
  
  /// Maximum number of avatars to show
  final int maxAvatars;

  const AvatarTypingIndicator({
    super.key,
    required this.typingUsers,
    this.maxAvatars = 3,
  });

  @override
  Widget build(BuildContext context) {
    if (typingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    final displayUsers = typingUsers.take(maxAvatars).toList();
    final remainingCount = typingUsers.length - displayUsers.length;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding,
      ),
      child: Row(
        children: [
          // User avatars
          SizedBox(
            width: displayUsers.length * 20.0 + (remainingCount > 0 ? 20 : 0),
            height: 24,
            child: Stack(
              children: [
                ...displayUsers.asMap().entries.map((entry) {
                  final index = entry.key;
                  final user = entry.value;
                  
                  return Positioned(
                    left: index * 16.0,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context).colorScheme.surface,
                          width: 2,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 10,
                        backgroundImage: user.avatarUrl != null
                            ? NetworkImage(user.avatarUrl!)
                            : null,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        child: user.avatarUrl == null
                            ? Text(
                                user.name.substring(0, 1).toUpperCase(),
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                ),
                              )
                            : null,
                      ),
                    ),
                  );
                }),
                if (remainingCount > 0)
                  Positioned(
                    left: displayUsers.length * 16.0,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context).colorScheme.surface,
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          '+$remainingCount',
                          style: TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          const SizedBox(width: AppConstants.smallPadding),
          
          // Typing animation
          const TypingDotsAnimation(dotSize: 3),
        ],
      ),
    );
  }
}

/// Data class for typing user information
class TypingUser {
  final String id;
  final String name;
  final String? avatarUrl;

  const TypingUser({
    required this.id,
    required this.name,
    this.avatarUrl,
  });
}
