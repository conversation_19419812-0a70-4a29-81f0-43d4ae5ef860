// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'threat_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ThreatIndicator _$ThreatIndicatorFromJson(Map<String, dynamic> json) =>
    ThreatIndicator(
      id: json['id'] as String,
      type: $enumDecode(_$ThreatTypeEnumMap, json['type']),
      description: json['description'] as String,
      severity: $enumDecode(_$ThreatSeverityEnumMap, json['severity']),
      confidence: (json['confidence'] as num).toDouble(),
      detectedAt: DateTime.parse(json['detectedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$ThreatIndicatorToJson(ThreatIndicator instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$ThreatTypeEnumMap[instance.type]!,
      'description': instance.description,
      'severity': _$ThreatSeverityEnumMap[instance.severity]!,
      'confidence': instance.confidence,
      'detectedAt': instance.detectedAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$ThreatTypeEnumMap = {
  ThreatType.bruteForce: 'bruteForce',
  ThreatType.anomalousLogin: 'anomalousLogin',
  ThreatType.suspiciousDeviceAccess: 'suspiciousDeviceAccess',
  ThreatType.dataExfiltration: 'dataExfiltration',
  ThreatType.malware: 'malware',
  ThreatType.phishing: 'phishing',
  ThreatType.other: 'other',
};

const _$ThreatSeverityEnumMap = {
  ThreatSeverity.low: 'low',
  ThreatSeverity.medium: 'medium',
  ThreatSeverity.high: 'high',
  ThreatSeverity.critical: 'critical',
};

SecurityThreat _$SecurityThreatFromJson(Map<String, dynamic> json) =>
    SecurityThreat(
      id: json['id'] as String,
      type: $enumDecode(_$ThreatTypeEnumMap, json['type']),
      severity: $enumDecode(_$ThreatSeverityEnumMap, json['severity']),
      description: json['description'] as String,
      sourceIp: json['sourceIp'] as String,
      userId: json['userId'] as String?,
      organizationId: json['organizationId'] as String?,
      detectedAt: DateTime.parse(json['detectedAt'] as String),
      indicators:
          (json['indicators'] as List<dynamic>?)
              ?.map((e) => ThreatIndicator.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      context: json['context'] as Map<String, dynamic>? ?? const {},
      riskScore: (json['riskScore'] as num).toDouble(),
      resolved: json['resolved'] as bool? ?? false,
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
    );

Map<String, dynamic> _$SecurityThreatToJson(SecurityThreat instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$ThreatTypeEnumMap[instance.type]!,
      'severity': _$ThreatSeverityEnumMap[instance.severity]!,
      'description': instance.description,
      'sourceIp': instance.sourceIp,
      'userId': instance.userId,
      'organizationId': instance.organizationId,
      'detectedAt': instance.detectedAt.toIso8601String(),
      'indicators': instance.indicators,
      'context': instance.context,
      'riskScore': instance.riskScore,
      'resolved': instance.resolved,
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
    };

ThreatDetectionStats _$ThreatDetectionStatsFromJson(
  Map<String, dynamic> json,
) => ThreatDetectionStats(
  totalThreats: (json['totalThreats'] as num).toInt(),
  criticalThreats: (json['criticalThreats'] as num).toInt(),
  highThreats: (json['highThreats'] as num).toInt(),
  mediumThreats: (json['mediumThreats'] as num).toInt(),
  lowThreats: (json['lowThreats'] as num).toInt(),
  resolvedThreats: (json['resolvedThreats'] as num).toInt(),
  averageRiskScore: (json['averageRiskScore'] as num).toDouble(),
  periodStart: DateTime.parse(json['periodStart'] as String),
  periodEnd: DateTime.parse(json['periodEnd'] as String),
);

Map<String, dynamic> _$ThreatDetectionStatsToJson(
  ThreatDetectionStats instance,
) => <String, dynamic>{
  'totalThreats': instance.totalThreats,
  'criticalThreats': instance.criticalThreats,
  'highThreats': instance.highThreats,
  'mediumThreats': instance.mediumThreats,
  'lowThreats': instance.lowThreats,
  'resolvedThreats': instance.resolvedThreats,
  'averageRiskScore': instance.averageRiskScore,
  'periodStart': instance.periodStart.toIso8601String(),
  'periodEnd': instance.periodEnd.toIso8601String(),
};

ThreatDetectionConfig _$ThreatDetectionConfigFromJson(
  Map<String, dynamic> json,
) => ThreatDetectionConfig(
  enabled: json['enabled'] as bool? ?? true,
  minimumSeverityLevel:
      $enumDecodeNullable(
        _$ThreatSeverityEnumMap,
        json['minimumSeverityLevel'],
      ) ??
      ThreatSeverity.medium,
  alertCooldown: json['alertCooldown'] == null
      ? const Duration(minutes: 5)
      : Duration(microseconds: (json['alertCooldown'] as num).toInt()),
  maxAlertsPerHour: (json['maxAlertsPerHour'] as num?)?.toInt() ?? 50,
  excludedIps:
      (json['excludedIps'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  enabledThreatTypes:
      (json['enabledThreatTypes'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry($enumDecode(_$ThreatTypeEnumMap, k), e as bool),
      ) ??
      const {},
  notificationSettings: json['notificationSettings'] == null
      ? const NotificationSettings()
      : NotificationSettings.fromJson(
          json['notificationSettings'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$ThreatDetectionConfigToJson(
  ThreatDetectionConfig instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'minimumSeverityLevel':
      _$ThreatSeverityEnumMap[instance.minimumSeverityLevel]!,
  'alertCooldown': instance.alertCooldown.inMicroseconds,
  'maxAlertsPerHour': instance.maxAlertsPerHour,
  'excludedIps': instance.excludedIps,
  'enabledThreatTypes': instance.enabledThreatTypes.map(
    (k, e) => MapEntry(_$ThreatTypeEnumMap[k]!, e),
  ),
  'notificationSettings': instance.notificationSettings,
};

NotificationSettings _$NotificationSettingsFromJson(
  Map<String, dynamic> json,
) => NotificationSettings(
  emailEnabled: json['emailEnabled'] as bool? ?? false,
  smsEnabled: json['smsEnabled'] as bool? ?? false,
  webhookEnabled: json['webhookEnabled'] as bool? ?? false,
  emailRecipients:
      (json['emailRecipients'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  smsRecipients:
      (json['smsRecipients'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  webhookUrl: json['webhookUrl'] as String?,
);

Map<String, dynamic> _$NotificationSettingsToJson(
  NotificationSettings instance,
) => <String, dynamic>{
  'emailEnabled': instance.emailEnabled,
  'smsEnabled': instance.smsEnabled,
  'webhookEnabled': instance.webhookEnabled,
  'emailRecipients': instance.emailRecipients,
  'smsRecipients': instance.smsRecipients,
  'webhookUrl': instance.webhookUrl,
};

DetectionRule _$DetectionRuleFromJson(Map<String, dynamic> json) =>
    DetectionRule(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      threatType: $enumDecode(_$ThreatTypeEnumMap, json['threatType']),
      severity: $enumDecode(_$ThreatSeverityEnumMap, json['severity']),
      enabled: json['enabled'] as bool? ?? true,
      conditions: json['conditions'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DetectionRuleToJson(DetectionRule instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'threatType': _$ThreatTypeEnumMap[instance.threatType]!,
      'severity': _$ThreatSeverityEnumMap[instance.severity]!,
      'enabled': instance.enabled,
      'conditions': instance.conditions,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
