// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'achievement.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  category: $enumDecode(_$AchievementCategoryEnumMap, json['category']),
  rarity: $enumDecode(_$AchievementRarityEnumMap, json['rarity']),
  icon: json['icon'] as String,
  points: (json['points'] as num).toInt(),
  unlockCondition: $enumDecode(
    _$UnlockConditionEnumMap,
    json['unlockCondition'],
  ),
  requiredValue: (json['requiredValue'] as num).toInt(),
  secondaryValue: (json['secondaryValue'] as num?)?.toInt(),
  isHidden: json['isHidden'] as bool,
  isActive: json['isActive'] as bool,
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'category': _$AchievementCategoryEnumMap[instance.category]!,
      'rarity': _$AchievementRarityEnumMap[instance.rarity]!,
      'icon': instance.icon,
      'points': instance.points,
      'unlockCondition': _$UnlockConditionEnumMap[instance.unlockCondition]!,
      'requiredValue': instance.requiredValue,
      'secondaryValue': instance.secondaryValue,
      'isHidden': instance.isHidden,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$AchievementCategoryEnumMap = {
  AchievementCategory.completion: 'completion',
  AchievementCategory.streak: 'streak',
  AchievementCategory.speed: 'speed',
  AchievementCategory.collaboration: 'collaboration',
  AchievementCategory.quality: 'quality',
  AchievementCategory.milestone: 'milestone',
  AchievementCategory.special: 'special',
  AchievementCategory.exploration: 'exploration',
};

const _$AchievementRarityEnumMap = {
  AchievementRarity.common: 'common',
  AchievementRarity.uncommon: 'uncommon',
  AchievementRarity.rare: 'rare',
  AchievementRarity.epic: 'epic',
  AchievementRarity.legendary: 'legendary',
};

const _$UnlockConditionEnumMap = {
  UnlockCondition.questCompletion: 'quest_completion',
  UnlockCondition.taskCompletion: 'task_completion',
  UnlockCondition.streakDays: 'streak_days',
  UnlockCondition.pointsEarned: 'points_earned',
  UnlockCondition.levelReached: 'level_reached',
  UnlockCondition.timeEfficiency: 'time_efficiency',
  UnlockCondition.collaborationCount: 'collaboration_count',
  UnlockCondition.featureUsage: 'feature_usage',
};
