/// Application constants for Quester platform
class AppConstants {
  // App information
  static const String appName = 'Quester';
  static const String appDescription = 'Quest and task management with gamification';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  static const String companyName = 'Quester Team';
  static const String supportEmail = '<EMAIL>';
  static const String privacyPolicyUrl = 'https://quester.app/privacy';
  static const String termsOfServiceUrl = 'https://quester.app/terms';
  
  // Storage keys for local storage
  static const String authTokenKey = 'quester_auth_token';
  static const String refreshTokenKey = 'quester_refresh_token';
  static const String userIdKey = 'quester_user_id';
  static const String userPreferencesKey = 'quester_user_preferences';
  static const String themeKey = 'quester_theme';
  static const String languageKey = 'quester_language';
  static const String onboardingCompletedKey = 'quester_onboarding_completed';
  static const String lastSyncKey = 'quester_last_sync';
  static const String offlineDataKey = 'quester_offline_data';
  static const String cacheVersionKey = 'quester_cache_version';
  
  // Default pagination settings
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int defaultPage = 1;
  
  // File upload constraints
  static const int maxFileSize = 10 * 1024 * 1024; // 10 MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'txt', 'md'];
  static const List<String> allowedArchiveTypes = ['zip', 'rar', '7z'];
  static const int maxFilesPerUpload = 5;
  static const int avatarMaxSize = 2 * 1024 * 1024; // 2 MB for avatars
  
  // Validation limits
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int maxUsernameLength = 50;
  static const int maxDisplayNameLength = 100;
  static const int maxEmailLength = 254;
  static const int maxTitleLength = 200;
  static const int maxDescriptionLength = 5000;
  static const int maxTagLength = 30;
  static const int maxTagsCount = 20;
  static const int maxCommentsLength = 1000;
  static const int maxSearchQueryLength = 100;
  
  // Time constants (in milliseconds)
  static const int networkTimeoutMs = 30000; // 30 seconds
  static const int shortTimeoutMs = 10000; // 10 seconds
  static const int connectionTimeoutMs = 15000; // 15 seconds
  static const int cacheExpirationMs = 300000; // 5 minutes
  static const int refreshTokenExpirationMs = 2592000000; // 30 days
  static const int accessTokenExpirationMs = 3600000; // 1 hour
  static const int websocketReconnectDelayMs = 5000; // 5 seconds
  static const int heartbeatIntervalMs = 30000; // 30 seconds
  
  // Animation durations (in milliseconds)
  static const int shortAnimationMs = 200;
  static const int mediumAnimationMs = 300;
  static const int longAnimationMs = 500;
  static const int splashScreenDurationMs = 2000;
  static const int snackbarDurationMs = 4000;
  static const int tooltipDelayMs = 1000;

  // Animation durations as Duration objects for Flutter
  static const Duration shortAnimation = Duration(milliseconds: shortAnimationMs);
  static const Duration mediumAnimation = Duration(milliseconds: mediumAnimationMs);
  static const Duration longAnimation = Duration(milliseconds: longAnimationMs);
  static const Duration splashScreenDuration = Duration(milliseconds: splashScreenDurationMs);
  static const Duration snackbarDuration = Duration(milliseconds: snackbarDurationMs);
  static const Duration tooltipDelay = Duration(milliseconds: tooltipDelayMs);
  
  // UI constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  static const double defaultBorderRadius = 8.0;
  static const double smallBorderRadius = 4.0;
  static const double largeBorderRadius = 12.0;
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  static const double avatarSize = 40.0;
  static const double largeAvatarSize = 80.0;
  static const double iconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double largeIconSize = 32.0;

  // App Layout Constants
  static const double appBarHeight = 64.0;
  static const double appBarElevation = 2.0;
  static const double bottomNavHeight = 80.0;
  static const double sideNavWidth = 280.0;
  static const double sideNavCollapsedWidth = 72.0;
  static const double railWidth = 72.0;
  static const double extendedRailWidth = 256.0;

  // Chat and Messaging UI
  static const double messageMaxWidth = 280.0;
  static const double messageBubbleRadius = 18.0;
  static const double chatInputHeight = 56.0;
  static const double chatAvatarSize = 32.0;
  static const int maxMessageLength = 2000;
  static const int maxGroupMembers = 100;

  // Notification UI
  static const double notificationItemHeight = 72.0;
  static const double notificationIconSize = 24.0;
  static const int maxNotificationCount = 99;
  static const Duration notificationDisplayDuration = Duration(seconds: 4);
  
  // Responsive breakpoints (in pixels)
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  static const double wideScreenBreakpoint = 1600;
  
  // Colors (Material 3 compatible)
  static const String primaryColorLight = '#6750A4';
  static const String primaryColorDark = '#D0BCFF';
  static const String secondaryColorLight = '#625B71';
  static const String secondaryColorDark = '#CCC2DC';
  static const String errorColorLight = '#BA1A1A';
  static const String errorColorDark = '#FFB4AB';
  static const String warningColorLight = '#F57C00';
  static const String warningColorDark = '#FFB74D';
  static const String successColorLight = '#388E3C';
  static const String successColorDark = '#81C784';
  static const String infoColorLight = '#1976D2';
  static const String infoColorDark = '#64B5F6';
  
  // Quest/Task status colors
  static const Map<String, String> statusColors = {
    'draft': '#9E9E9E',
    'active': '#2196F3',
    'in_progress': '#FF9800',
    'completed': '#4CAF50',
    'archived': '#607D8B',
    'cancelled': '#F44336',
    'blocked': '#E91E63',
    'todo': '#9C27B0',
    'paused': '#795548',
  };
  
  // Priority colors
  static const Map<String, String> priorityColors = {
    'low': '#4CAF50',
    'medium': '#FF9800',
    'high': '#FF5722',
    'urgent': '#F44336',
    'critical': '#9C27B0',
  };
  
  // Difficulty colors
  static const Map<String, String> difficultyColors = {
    'beginner': '#4CAF50',
    'intermediate': '#2196F3',
    'advanced': '#FF9800',
    'expert': '#FF5722',
    'master': '#9C27B0',
  };
  
  // Default values
  static const String defaultAvatarUrl = 'https://ui-avatars.com/api/?name=';
  static const String defaultLocale = 'en_US';
  static const String defaultTimeZone = 'UTC';
  static const String defaultDateFormat = 'yyyy-MM-dd';
  static const String defaultTimeFormat = 'HH:mm';
  static const String defaultDateTimeFormat = 'yyyy-MM-dd HH:mm';
  
  // Feature flags (can be overridden by backend)
  static const Map<String, bool> defaultFeatureFlags = {
    'gamification_enabled': true,
    'real_time_updates': true,
    'offline_mode': false,
    'analytics_tracking': true,
    'push_notifications': true,
    'file_attachments': true,
    'collaboration_features': true,
    'advanced_search': true,
    'bulk_operations': true,
    'custom_themes': false,
    'api_access': false,
    'admin_panel': false,
  };
  
  // Notification settings
  static const Map<String, dynamic> defaultNotificationSettings = {
    'push_enabled': true,
    'email_enabled': true,
    'quest_reminders': true,
    'task_reminders': true,
    'deadline_alerts': true,
    'achievement_notifications': true,
    'collaboration_notifications': true,
    'system_notifications': true,
    'marketing_notifications': false,
    'reminder_hours_before': 24,
    'quiet_hours_start': '22:00',
    'quiet_hours_end': '08:00',
  };
  
  // Search configuration
  static const Map<String, dynamic> searchConfig = {
    'min_query_length': 2,
    'max_results': 50,
    'highlight_matches': true,
    'fuzzy_search': true,
    'search_delay_ms': 300,
    'search_history_limit': 10,
    'auto_suggest': true,
  };
  
  // Cache configuration
  static const Map<String, int> cacheConfig = {
    'user_profile_ttl': 300, // 5 minutes
    'quests_list_ttl': 180, // 3 minutes
    'tasks_list_ttl': 60, // 1 minute
    'achievements_ttl': 600, // 10 minutes
    'leaderboard_ttl': 120, // 2 minutes
    'notifications_ttl': 30, // 30 seconds
    'max_cache_entries': 1000,
  };
  
  // Error codes
  static const Map<String, String> errorCodes = {
    'network_error': 'NETWORK_ERROR',
    'timeout_error': 'TIMEOUT_ERROR',
    'auth_error': 'AUTHENTICATION_ERROR',
    'permission_error': 'PERMISSION_ERROR',
    'validation_error': 'VALIDATION_ERROR',
    'server_error': 'SERVER_ERROR',
    'not_found_error': 'NOT_FOUND_ERROR',
    'conflict_error': 'CONFLICT_ERROR',
    'rate_limit_error': 'RATE_LIMIT_ERROR',
    'file_size_error': 'FILE_SIZE_ERROR',
    'file_type_error': 'FILE_TYPE_ERROR',
    'offline_error': 'OFFLINE_ERROR',
    'unknown_error': 'UNKNOWN_ERROR',
  };
  
  // WebSocket events
  static const Map<String, String> websocketEvents = {
    'connect': 'connect',
    'disconnect': 'disconnect',
    'error': 'error',
    'quest_updated': 'quest_updated',
    'task_updated': 'task_updated',
    'user_updated': 'user_updated',
    'achievement_unlocked': 'achievement_unlocked',
    'points_earned': 'points_earned',
    'leaderboard_updated': 'leaderboard_updated',
    'notification_received': 'notification_received',
    'collaboration_invited': 'collaboration_invited',
    'heartbeat': 'heartbeat',
  };
  
  // Regular expressions for validation
  static const Map<String, String> validationPatterns = {
    'email': r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$',
    'password': r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$',
    'username': r'^[a-zA-Z0-9_-]{3,20}$',
    'phone': r'^\+?[\d\s\-\(\)]{10,15}$',
    'url': r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
    'hex_color': r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
  };
  
  // Date and time formats
  static const Map<String, String> dateTimeFormats = {
    'iso_date': 'yyyy-MM-dd',
    'iso_datetime': 'yyyy-MM-ddTHH:mm:ss.SSSZ',
    'display_date': 'MMM dd, yyyy',
    'display_datetime': 'MMM dd, yyyy HH:mm',
    'short_date': 'M/d',
    'long_date': 'EEEE, MMMM dd, yyyy',
    'time_12h': 'h:mm a',
    'time_24h': 'HH:mm',
    'month_year': 'MMMM yyyy',
    'weekday': 'EEEE',
  };
  
  // Environment-specific URLs (will be overridden by environment config)
  static const Map<String, Map<String, String>> environmentUrls = {
    'development': {
      'api_base_url': 'http://localhost:8080',
      'web_base_url': 'http://localhost:3000',
      'websocket_url': 'ws://localhost:8080/ws',
    },
    'staging': {
      'api_base_url': 'https://staging-api.quester.app',
      'web_base_url': 'https://staging.quester.app',
      'websocket_url': 'wss://staging-api.quester.app/ws',
    },
    'production': {
      'api_base_url': 'https://api.quester.app',
      'web_base_url': 'https://quester.app',
      'websocket_url': 'wss://api.quester.app/ws',
    },
  };
  
  // Utility methods
  
  /// Get color by status
  static String getStatusColor(String status) {
    return statusColors[status] ?? statusColors['draft']!;
  }
  
  /// Get color by priority
  static String getPriorityColor(String priority) {
    return priorityColors[priority] ?? priorityColors['medium']!;
  }
  
  /// Get color by difficulty
  static String getDifficultyColor(String difficulty) {
    return difficultyColors[difficulty] ?? difficultyColors['beginner']!;
  }
  
  /// Check if feature is enabled
  static bool isFeatureEnabled(String feature) {
    return defaultFeatureFlags[feature] ?? false;
  }
  
  /// Get validation pattern
  static String getValidationPattern(String type) {
    return validationPatterns[type] ?? '';
  }
  
  /// Get environment URL
  static String getEnvironmentUrl(String environment, String type) {
    return environmentUrls[environment]?[type] ?? '';
  }
  
  /// Get cache TTL for resource type
  static int getCacheTtl(String resourceType) {
    return cacheConfig['${resourceType}_ttl'] ?? cacheConfig['user_profile_ttl']!;
  }
  
  /// Format file size
  static String formatFileSize(int bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    double size = bytes.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }
  
  /// Check if file type is allowed
  static bool isFileTypeAllowed(String fileName, List<String> allowedTypes) {
    final extension = fileName.split('.').last.toLowerCase();
    return allowedTypes.contains(extension);
  }
  
  /// Generate avatar URL with initials
  static String generateAvatarUrl(String name, {String? backgroundColor, String? color}) {
    final params = <String>[];
    params.add('name=${Uri.encodeComponent(name)}');
    if (backgroundColor != null) params.add('background=$backgroundColor');
    if (color != null) params.add('color=$color');
    return '$defaultAvatarUrl&${params.join('&')}';
  }
}