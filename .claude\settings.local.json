{"permissions": {"allow": ["Read(/D:\\d/**)", "Read(/D:\\d/**)", "Read(/D:\\d\\Quester\\server/**)", "Read(/D:\\d\\Quester\\client/**)", "Bash(grep:*)", "Bash(rm:*)", "Read(/D:\\d\\Quester\\shared/**)", "Bash(dart pub:*)", "Bash(dart analyze:*)", "<PERSON><PERSON>(dart run:*)", "Bash(find:*)", "<PERSON><PERSON>(sed:*)", "Bash(flutter pub deps:*)", "Bash(dart compile:*)", "Bash(flutter build:*)", "Bash(flutter analyze:*)", "Bash(flutter pub:*)", "Read(/C:\\Users/**)", "Read(/D:\\c\\Users\\Keshab_Das\\.agent-os/**)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(bash:*)", "Ba<PERSON>(flutter:*)", "<PERSON><PERSON>(dart:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(R)", "WebFetch(domain:localhost)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker ps:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(chmod:*)"], "deny": [], "ask": [], "additionalDirectories": ["D:\\d\\Quester\\client\\lib", "D:\\d\\Quester", "C:\\Users\\<USER>\\.agent-os", "D:\\D\\Quester"]}}