import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';
import '../../../data/repositories/api_repository.dart';

/// BLoC for managing quest-related state and operations
class QuestBloc extends Bloc<QuestEvent, QuestState> {
  final ApiRepository _repository;

  QuestBloc({required ApiRepository repository})
      : _repository = repository,
        super(const QuestInitial()) {
    on<LoadQuests>(_onLoadQuests);
    on<LoadQuestDetails>(_onLoadQuestDetails);
    on<CreateQuest>(_onCreateQuest);
    on<UpdateQuest>(_onUpdateQuest);
    on<UpdateQuestStatus>(_onUpdateQuestStatus);
    on<UpdateQuestPriority>(_onUpdateQuestPriority);
    on<DeleteQuest>(_onDeleteQuest);
    on<AddTaskToQuest>(_onAddTaskToQuest);
    on<UpdateTask>(_onUpdateTask);
    on<DeleteTask>(_onDeleteTask);
    on<AssignQuestToUser>(_onAssignQuestToUser);
    on<UnassignQuestFromUser>(_onUnassignQuestFromUser);
    on<SearchQuests>(_onSearchQuests);
  }

  Future<void> _onLoadQuests(LoadQuests event, Emitter<QuestState> emit) async {
    try {
      emit(const QuestLoading());
      
      // TODO: Replace with actual API call
      final filters = <String, String>{};
      if (event.status != null) filters['status'] = event.status!.name;
      if (event.priority != null) filters['priority'] = event.priority!.name;
      if (event.type != null) filters['type'] = event.type!.name;
      filters['page'] = event.page.toString();
      filters['limit'] = event.limit.toString();

      final quests = await _repository.getQuests(filters: filters.isNotEmpty ? filters : null);
      
      emit(QuestsLoaded(quests: quests));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onLoadQuestDetails(LoadQuestDetails event, Emitter<QuestState> emit) async {
    try {
      emit(const QuestLoading());
      
      // TODO: Replace with actual API call
      final quest = await _repository.getQuestById(event.questId);
      
      emit(QuestDetailsLoaded(quest: quest));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onCreateQuest(CreateQuest event, Emitter<QuestState> emit) async {
    try {
      emit(const QuestLoading());
      
      // TODO: Replace with actual API call
      final createdQuest = await _repository.createQuest(event.questData);
      
      emit(QuestCreated(quest: createdQuest));
      
      // Reload quests to update the list
      add(const LoadQuests());
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onUpdateQuest(UpdateQuest event, Emitter<QuestState> emit) async {
    try {
      emit(const QuestLoading());
      
      // TODO: Replace with actual API call
      final updatedQuest = await _repository.updateQuest(event.questId, event.questData);
      
      emit(QuestUpdated(quest: updatedQuest));
      
      // Reload quests to update the list
      add(const LoadQuests());
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onUpdateQuestStatus(UpdateQuestStatus event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      await _repository.updateQuestStatus(event.questId, event.status);
      
      emit(QuestStatusUpdated(questId: event.questId, status: event.status));
      
      // Reload quests to update the list
      add(const LoadQuests());
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onUpdateQuestPriority(UpdateQuestPriority event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      await _repository.updateQuestPriority(event.questId, event.priority);
      
      emit(QuestPriorityUpdated(questId: event.questId, priority: event.priority));
      
      // Reload quests to update the list
      add(const LoadQuests());
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onDeleteQuest(DeleteQuest event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      await _repository.deleteQuest(event.questId);
      
      emit(QuestDeleted(questId: event.questId));
      
      // Reload quests to update the list
      add(const LoadQuests());
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onAddTaskToQuest(AddTaskToQuest event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      final task = await _repository.addTaskToQuest(event.questId, event.taskData);
      
      emit(TaskAddedToQuest(questId: event.questId, task: task));
      
      // Reload quest details to update the task list
      add(LoadQuestDetails(questId: event.questId));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onUpdateTask(UpdateTask event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      final updatedTask = await _repository.updateTask(event.taskId, event.taskData);
      
      emit(TaskUpdated(task: updatedTask));
      
      // Reload quest details to update the task list
      add(LoadQuestDetails(questId: event.questId));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onDeleteTask(DeleteTask event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      await _repository.deleteTask(event.taskId);
      
      emit(TaskDeleted(taskId: event.taskId));
      
      // Reload quest details to update the task list
      add(LoadQuestDetails(questId: event.questId));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onAssignQuestToUser(AssignQuestToUser event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      await _repository.assignQuestToUser(event.questId, event.userId);
      
      emit(QuestAssigned(questId: event.questId, userId: event.userId));
      
      // Reload quest details to update the assignment
      add(LoadQuestDetails(questId: event.questId));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onUnassignQuestFromUser(UnassignQuestFromUser event, Emitter<QuestState> emit) async {
    try {
      // TODO: Replace with actual API call
      await _repository.unassignQuestFromUser(event.questId, event.userId);
      
      emit(QuestUnassigned(questId: event.questId, userId: event.userId));
      
      // Reload quest details to update the assignment
      add(LoadQuestDetails(questId: event.questId));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }

  Future<void> _onSearchQuests(SearchQuests event, Emitter<QuestState> emit) async {
    try {
      emit(const QuestLoading());
      
      // TODO: Replace with actual API call
      final filters = event.filters?.map((key, value) => MapEntry(key, value.toString()));
      final quests = await _repository.searchQuests(
        event.query,
        filters: filters,
      );
      
      emit(QuestsLoaded(quests: quests));
    } catch (error) {
      emit(QuestError(message: error.toString()));
    }
  }
}

/// Base class for all quest events
abstract class QuestEvent extends Equatable {
  const QuestEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load quests with optional filters
class LoadQuests extends QuestEvent {
  final QuestStatus? status;
  final QuestPriority? priority;
  final QuestType? type;
  final int page;
  final int limit;

  const LoadQuests({
    this.status,
    this.priority,
    this.type,
    this.page = 1,
    this.limit = 20,
  });

  @override
  List<Object?> get props => [status, priority, type, page, limit];
}

/// Event to load detailed information about a specific quest
class LoadQuestDetails extends QuestEvent {
  final String questId;

  const LoadQuestDetails({required this.questId});

  @override
  List<Object> get props => [questId];
}

/// Event to create a new quest
class CreateQuest extends QuestEvent {
  final Map<String, dynamic> questData;

  const CreateQuest({required this.questData});

  @override
  List<Object> get props => [questData];
}

/// Event to update an existing quest
class UpdateQuest extends QuestEvent {
  final String questId;
  final Map<String, dynamic> questData;

  const UpdateQuest({required this.questId, required this.questData});

  @override
  List<Object> get props => [questId, questData];
}

/// Event to update quest status
class UpdateQuestStatus extends QuestEvent {
  final String questId;
  final QuestStatus status;

  const UpdateQuestStatus({required this.questId, required this.status});

  @override
  List<Object> get props => [questId, status];
}

/// Event to update quest priority
class UpdateQuestPriority extends QuestEvent {
  final String questId;
  final QuestPriority priority;

  const UpdateQuestPriority({required this.questId, required this.priority});

  @override
  List<Object> get props => [questId, priority];
}

/// Event to delete a quest
class DeleteQuest extends QuestEvent {
  final String questId;

  const DeleteQuest({required this.questId});

  @override
  List<Object> get props => [questId];
}

/// Event to add a task to a quest
class AddTaskToQuest extends QuestEvent {
  final String questId;
  final Map<String, dynamic> taskData;

  const AddTaskToQuest({required this.questId, required this.taskData});

  @override
  List<Object> get props => [questId, taskData];
}

/// Event to update a task
class UpdateTask extends QuestEvent {
  final String questId;
  final String taskId;
  final Map<String, dynamic> taskData;

  const UpdateTask({
    required this.questId,
    required this.taskId,
    required this.taskData,
  });

  @override
  List<Object> get props => [questId, taskId, taskData];
}

/// Event to delete a task
class DeleteTask extends QuestEvent {
  final String questId;
  final String taskId;

  const DeleteTask({required this.questId, required this.taskId});

  @override
  List<Object> get props => [questId, taskId];
}

/// Event to assign a quest to a user
class AssignQuestToUser extends QuestEvent {
  final String questId;
  final String userId;

  const AssignQuestToUser({required this.questId, required this.userId});

  @override
  List<Object> get props => [questId, userId];
}

/// Event to unassign a quest from a user
class UnassignQuestFromUser extends QuestEvent {
  final String questId;
  final String userId;

  const UnassignQuestFromUser({required this.questId, required this.userId});

  @override
  List<Object> get props => [questId, userId];
}

/// Event to search quests
class SearchQuests extends QuestEvent {
  final String query;
  final Map<String, dynamic>? filters;

  const SearchQuests({required this.query, this.filters});

  @override
  List<Object?> get props => [query, filters];
}

/// Base class for all quest states
abstract class QuestState extends Equatable {
  const QuestState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class QuestInitial extends QuestState {
  const QuestInitial();
}

/// Loading state
class QuestLoading extends QuestState {
  const QuestLoading();
}

/// State when quests are successfully loaded
class QuestsLoaded extends QuestState {
  final List<Quest> quests;

  const QuestsLoaded({required this.quests});

  @override
  List<Object> get props => [quests];
}

/// State when quest details are successfully loaded
class QuestDetailsLoaded extends QuestState {
  final Quest quest;

  const QuestDetailsLoaded({required this.quest});

  @override
  List<Object> get props => [quest];
}

/// State when a quest is successfully created
class QuestCreated extends QuestState {
  final Quest quest;

  const QuestCreated({required this.quest});

  @override
  List<Object> get props => [quest];
}

/// State when a quest is successfully updated
class QuestUpdated extends QuestState {
  final Quest quest;

  const QuestUpdated({required this.quest});

  @override
  List<Object> get props => [quest];
}

/// State when quest status is updated
class QuestStatusUpdated extends QuestState {
  final String questId;
  final QuestStatus status;

  const QuestStatusUpdated({required this.questId, required this.status});

  @override
  List<Object> get props => [questId, status];
}

/// State when quest priority is updated
class QuestPriorityUpdated extends QuestState {
  final String questId;
  final QuestPriority priority;

  const QuestPriorityUpdated({required this.questId, required this.priority});

  @override
  List<Object> get props => [questId, priority];
}

/// State when a quest is successfully deleted
class QuestDeleted extends QuestState {
  final String questId;

  const QuestDeleted({required this.questId});

  @override
  List<Object> get props => [questId];
}

/// State when a task is added to a quest
class TaskAddedToQuest extends QuestState {
  final String questId;
  final Task task;

  const TaskAddedToQuest({required this.questId, required this.task});

  @override
  List<Object> get props => [questId, task];
}

/// State when a task is updated
class TaskUpdated extends QuestState {
  final Task task;

  const TaskUpdated({required this.task});

  @override
  List<Object> get props => [task];
}

/// State when a task is deleted
class TaskDeleted extends QuestState {
  final String taskId;

  const TaskDeleted({required this.taskId});

  @override
  List<Object> get props => [taskId];
}

/// State when a quest is assigned to a user
class QuestAssigned extends QuestState {
  final String questId;
  final String userId;

  const QuestAssigned({required this.questId, required this.userId});

  @override
  List<Object> get props => [questId, userId];
}

/// State when a quest is unassigned from a user
class QuestUnassigned extends QuestState {
  final String questId;
  final String userId;

  const QuestUnassigned({required this.questId, required this.userId});

  @override
  List<Object> get props => [questId, userId];
}

/// Error state
class QuestError extends QuestState {
  final String message;

  const QuestError({required this.message});

  @override
  List<Object> get props => [message];
}
