import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared/shared.dart';
import 'package:client/presentation/widgets/quests/quest_card_widget.dart';

void main() {
  group('QuestCardWidget', () {
    late Quest testQuest;

    setUp(() {
      testQuest = Quest(
        id: '1',
        title: 'Test Quest',
        description: 'This is a test quest description',
        status: QuestStatus.active,
        priority: QuestPriority.high,
        type: QuestType.personal,
        createdBy: 'user1',
        assignedTo: ['user1'],
        tasks: [
          Task(
            id: 'task1',
            title: 'Test Task 1',
            description: 'Task 1 description',
            status: TaskStatus.completed,
            priority: TaskPriority.medium,
            assignedTo: 'user1',
            createdBy: 'user1',
            questId: '1',
            pointsReward: 25,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Task(
            id: 'task2',
            title: 'Test Task 2',
            description: 'Task 2 description',
            status: TaskStatus.pending,
            priority: TaskPriority.high,
            assignedTo: 'user1',
            createdBy: 'user1',
            questId: '1',
            pointsReward: 25,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ],
        pointsReward: 100,
        dueDate: DateTime.now().add(const Duration(days: 7)),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    Widget createWidgetUnderTest({
      Quest? quest,
      VoidCallback? onTap,
      Function(QuestStatus)? onStatusChanged,
      Function(QuestPriority)? onPriorityChanged,
      bool showDetails = true,
      bool showActions = true,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: QuestCardWidget(
            quest: quest ?? testQuest,
            onTap: onTap,
            onStatusChanged: onStatusChanged,
            onPriorityChanged: onPriorityChanged,
            showDetails: showDetails,
            showActions: showActions,
          ),
        ),
      );
    }

    testWidgets('displays quest title and description', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('Test Quest'), findsOneWidget);
      expect(find.text('This is a test quest description'), findsOneWidget);
    });

    testWidgets('displays priority and status chips', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('HIGH'), findsOneWidget);
      expect(find.text('ACTIVE'), findsOneWidget);
    });

    testWidgets('displays progress information', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('Progress'), findsOneWidget);
      expect(find.text('1 / 2 tasks'), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('displays metadata information', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('100 points'), findsOneWidget);
      expect(find.text('1 assigned'), findsOneWidget);
      expect(find.textContaining('Due:'), findsOneWidget);
    });

    testWidgets('displays action buttons when showActions is true', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest(showActions: true));

      expect(find.text('Pause'), findsOneWidget);
      expect(find.text('Edit'), findsOneWidget);
      expect(find.byIcon(Icons.more_vert), findsOneWidget);
    });

    testWidgets('hides action buttons when showActions is false', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest(showActions: false));

      expect(find.text('Pause'), findsNothing);
      expect(find.text('Edit'), findsNothing);
      expect(find.byIcon(Icons.more_vert), findsNothing);
    });

    testWidgets('hides details when showDetails is false', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest(showDetails: false));

      expect(find.text('This is a test quest description'), findsNothing);
      expect(find.text('Progress'), findsNothing);
      expect(find.byType(LinearProgressIndicator), findsNothing);
    });

    testWidgets('calls onTap when card is tapped', (tester) async {
      bool tapped = false;
      await tester.pumpWidget(createWidgetUnderTest(
        onTap: () => tapped = true,
      ));

      await tester.tap(find.byType(Card));
      expect(tapped, isTrue);
    });

    testWidgets('calls onStatusChanged when pause button is tapped', (tester) async {
      QuestStatus? changedStatus;
      await tester.pumpWidget(createWidgetUnderTest(
        onStatusChanged: (status) => changedStatus = status,
      ));

      await tester.tap(find.text('Pause'));
      expect(changedStatus, equals(QuestStatus.paused));
    });

    testWidgets('shows Resume button for paused quest', (tester) async {
      final pausedQuest = testQuest.copyWith(status: QuestStatus.paused);
      await tester.pumpWidget(createWidgetUnderTest(quest: pausedQuest));

      expect(find.text('Resume'), findsOneWidget);
      expect(find.text('Pause'), findsNothing);
    });

    testWidgets('does not show pause/resume button for completed quest', (tester) async {
      final completedQuest = testQuest.copyWith(status: QuestStatus.completed);
      await tester.pumpWidget(createWidgetUnderTest(quest: completedQuest));

      expect(find.text('Pause'), findsNothing);
      expect(find.text('Resume'), findsNothing);
    });

    testWidgets('displays correct progress color based on completion', (tester) async {
      // Test with low progress (red)
      final lowProgressQuest = testQuest.copyWith(
        tasks: [
          testQuest.tasks[0].copyWith(status: TaskStatus.pending),
          testQuest.tasks[1].copyWith(status: TaskStatus.pending),
        ],
      );
      await tester.pumpWidget(createWidgetUnderTest(quest: lowProgressQuest));
      await tester.pump();

      var progressIndicator = tester.widget<LinearProgressIndicator>(
        find.byType(LinearProgressIndicator),
      );
      expect(progressIndicator.value, equals(0.0));

      // Test with high progress (green)
      final highProgressQuest = testQuest.copyWith(
        tasks: [
          testQuest.tasks[0].copyWith(status: TaskStatus.completed),
          testQuest.tasks[1].copyWith(status: TaskStatus.completed),
        ],
      );
      await tester.pumpWidget(createWidgetUnderTest(quest: highProgressQuest));
      await tester.pump();

      progressIndicator = tester.widget<LinearProgressIndicator>(
        find.byType(LinearProgressIndicator),
      );
      expect(progressIndicator.value, equals(1.0));
    });

    testWidgets('shows priority dialog when priority menu item is tapped', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      // Tap the more menu
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // Tap the priority menu item
      await tester.tap(find.text('Change Priority'));
      await tester.pumpAndSettle();

      // Verify priority dialog is shown
      expect(find.text('Change Priority'), findsOneWidget);
      expect(find.text('LOW'), findsOneWidget);
      expect(find.text('MEDIUM'), findsOneWidget);
      expect(find.text('HIGH'), findsOneWidget);
      expect(find.text('URGENT'), findsOneWidget);
    });

    testWidgets('shows delete confirmation dialog when delete menu item is tapped', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      // Tap the more menu
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // Tap the delete menu item
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      // Verify delete dialog is shown
      expect(find.text('Delete Quest'), findsOneWidget);
      expect(find.text('Are you sure you want to delete "Test Quest"?'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Delete'), findsAtLeastNWidgets(1));
    });

    testWidgets('displays correct quest type icon', (tester) async {
      final personalQuest = testQuest.copyWith(type: QuestType.personal);
      await tester.pumpWidget(createWidgetUnderTest(quest: personalQuest));

      expect(find.byIcon(Icons.person), findsOneWidget);

      final teamQuest = testQuest.copyWith(type: QuestType.team);
      await tester.pumpWidget(createWidgetUnderTest(quest: teamQuest));

      expect(find.byIcon(Icons.group), findsOneWidget);
    });

    testWidgets('displays correct status icon', (tester) async {
      final activeQuest = testQuest.copyWith(status: QuestStatus.active);
      await tester.pumpWidget(createWidgetUnderTest(quest: activeQuest));

      expect(find.byIcon(Icons.play_arrow), findsOneWidget);

      final completedQuest = testQuest.copyWith(status: QuestStatus.completed);
      await tester.pumpWidget(createWidgetUnderTest(quest: completedQuest));

      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('formats due date correctly', (tester) async {
      final todayQuest = testQuest.copyWith(dueDate: DateTime.now());
      await tester.pumpWidget(createWidgetUnderTest(quest: todayQuest));

      expect(find.text('Due: Today'), findsOneWidget);

      final tomorrowQuest = testQuest.copyWith(
        dueDate: DateTime.now().add(const Duration(days: 1)),
      );
      await tester.pumpWidget(createWidgetUnderTest(quest: tomorrowQuest));

      expect(find.text('Due: Tomorrow'), findsOneWidget);

      final noDueDateQuest = testQuest.copyWith(dueDate: null);
      await tester.pumpWidget(createWidgetUnderTest(quest: noDueDateQuest));

      expect(find.text('Due: No due date'), findsOneWidget);
    });

    testWidgets('handles empty task list', (tester) async {
      final emptyTasksQuest = testQuest.copyWith(tasks: []);
      await tester.pumpWidget(createWidgetUnderTest(quest: emptyTasksQuest));

      expect(find.text('0 / 0 tasks'), findsOneWidget);
      
      final progressIndicator = tester.widget<LinearProgressIndicator>(
        find.byType(LinearProgressIndicator),
      );
      expect(progressIndicator.value, equals(0.0));
    });

    testWidgets('handles quest with no description', (tester) async {
      final noDescQuest = testQuest.copyWith(description: '');
      await tester.pumpWidget(createWidgetUnderTest(quest: noDescQuest));

      expect(find.text('Test Quest'), findsOneWidget);
      expect(find.text('This is a test quest description'), findsNothing);
    });
  });
}
