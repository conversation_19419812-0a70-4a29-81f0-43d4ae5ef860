import 'dart:convert';
import 'dart:io';
import 'package:test/test.dart';
import 'package:http/http.dart' as http;

void main() {
  group('Quest API Integration Tests', () {
    late HttpServer server;
    late String baseUrl;
    
    setUpAll(() async {
      // Start test server
      server = await HttpServer.bind('localhost', 0);
      baseUrl = 'http://localhost:${server.port}';
      
      // Set up basic routing for tests
      server.listen((HttpRequest request) async {
        final response = request.response;
        response.headers.contentType = ContentType.json;
        
        try {
          switch (request.uri.path) {
            case '/api/v1/gamification/quests':
              await _handleQuestsEndpoint(request, response);
              break;
            case '/api/v1/gamification/quest-templates':
              await _handleQuestTemplatesEndpoint(request, response);
              break;
            default:
              response.statusCode = HttpStatus.notFound;
              response.write(jsonEncode({'error': 'Endpoint not found'}));
          }
        } catch (e) {
          response.statusCode = HttpStatus.internalServerError;
          response.write(jsonEncode({'error': 'Internal server error: $e'}));
        } finally {
          await response.close();
        }
      });
    });

    tearDownAll(() async {
      await server.close();
    });

    test('GET /api/v1/gamification/quests returns quest list', () async {
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/gamification/quests'),
        headers: {'Content-Type': 'application/json'},
      );

      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data, isA<Map<String, dynamic>>());
      expect(data['success'], isTrue);
      expect(data['quests'], isA<List>());
    });

    test('POST /api/v1/gamification/quests creates new quest', () async {
      final questData = {
        'title': 'Test Quest',
        'description': 'A test quest for integration testing',
        'priority': 'high',
        'difficulty': 'intermediate',
        'category': 'personal',
        'participantIds': <String>[],
        'tags': ['test', 'integration'],
      };

      final response = await http.post(
        Uri.parse('$baseUrl/api/v1/gamification/quests'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(questData),
      );

      expect(response.statusCode, equals(201));
      
      final data = jsonDecode(response.body);
      expect(data, isA<Map<String, dynamic>>());
      expect(data['success'], isTrue);
      expect(data['quest'], isA<Map<String, dynamic>>());
      expect(data['quest']['title'], equals('Test Quest'));
    });

    test('GET /api/v1/gamification/quest-templates returns templates', () async {
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/gamification/quest-templates'),
        headers: {'Content-Type': 'application/json'},
      );

      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data, isA<Map<String, dynamic>>());
      expect(data['success'], isTrue);
      expect(data['templates'], isA<List>());
    });

    test('handles invalid quest data gracefully', () async {
      final invalidQuestData = {
        'title': '', // Empty title should cause validation error
        'description': '',
      };

      final response = await http.post(
        Uri.parse('$baseUrl/api/v1/gamification/quests'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(invalidQuestData),
      );

      expect(response.statusCode, equals(400));
      
      final data = jsonDecode(response.body);
      expect(data, isA<Map<String, dynamic>>());
      expect(data['success'], isFalse);
      expect(data['error'], isA<String>());
    });
  });
}

// Mock endpoint handlers
Future<void> _handleQuestsEndpoint(HttpRequest request, HttpResponse response) async {
  if (request.method == 'GET') {
    // Return mock quest list
    final mockQuests = [
      {
        'id': 'quest_1',
        'title': 'Sample Quest 1',
        'description': 'A sample quest for testing',
        'status': 'active',
        'priority': 'medium',
        'difficulty': 'beginner',
        'category': 'personal',
        'basePoints': 100,
        'totalPoints': 150,
        'progressPercentage': 25.0,
        'createdAt': DateTime.now().toIso8601String(),
      },
    ];

    response.statusCode = HttpStatus.ok;
    response.write(jsonEncode({
      'success': true,
      'quests': mockQuests,
      'total': mockQuests.length,
    }));
  } else if (request.method == 'POST') {
    // Create new quest
    final bodyBytes = await request.toList();
    final bodyString = utf8.decode(bodyBytes.expand((x) => x).toList());
    final questData = jsonDecode(bodyString) as Map<String, dynamic>;

    // Basic validation
    if (questData['title'] == null || questData['title'].toString().isEmpty) {
      response.statusCode = HttpStatus.badRequest;
      response.write(jsonEncode({
        'success': false,
        'error': 'Title is required',
      }));
      return;
    }

    // Return created quest
    final createdQuest = {
      'id': 'quest_${DateTime.now().millisecondsSinceEpoch}',
      ...questData,
      'status': 'draft',
      'basePoints': 100,
      'totalPoints': 100,
      'progressPercentage': 0.0,
      'createdAt': DateTime.now().toIso8601String(),
    };

    response.statusCode = HttpStatus.created;
    response.write(jsonEncode({
      'success': true,
      'quest': createdQuest,
    }));
  }
}

Future<void> _handleQuestTemplatesEndpoint(HttpRequest request, HttpResponse response) async {
  final mockTemplates = [
    {
      'id': 'template_1',
      'title': 'Daily Fitness Challenge',
      'description': 'Complete daily workout routines',
      'category': 'health',
      'difficulty': 'intermediate',
      'basePoints': 150,
      'taskTitles': ['Morning stretches', '30-minute workout', 'Track progress'],
      'tags': ['fitness', 'health', 'daily'],
    },
  ];

  response.statusCode = HttpStatus.ok;
  response.write(jsonEncode({
    'success': true,
    'templates': mockTemplates,
  }));
}
