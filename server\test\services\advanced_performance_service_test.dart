/// Tests for advanced performance optimization service
library;

import 'package:test/test.dart';
import 'package:server/services/advanced_performance_service.dart';

void main() {
  group('AdvancedPerformanceService', () {
    late AdvancedPerformanceService performanceService;

    setUp(() {
      performanceService = AdvancedPerformanceService();
      // Clear any existing state since it's a singleton
      performanceService.cleanup();
    });

    group('Performance Metrics Recording', () {
      test('should record performance metrics correctly', () {
        performanceService.recordPerformanceMetric('test_operation', {
          'duration_ms': 150.0,
          'memory_usage': 1024,
          'cpu_usage': 0.25,
        });

        final report = performanceService.getComprehensivePerformanceReport();
        
        expect(report['total_operations'], equals(1));
        expect(report['operations']['test_operation']['count'], equals(1));
        expect(report['operations']['test_operation']['avg_duration_ms'], equals(150.0));
      });

      test('should track multiple operations', () {
        performanceService.recordPerformanceMetric('operation_1', {'duration_ms': 100.0});
        performanceService.recordPerformanceMetric('operation_2', {'duration_ms': 200.0});
        performanceService.recordPerformanceMetric('operation_1', {'duration_ms': 120.0});

        final report = performanceService.getComprehensivePerformanceReport();

        expect(report['total_operations'], greaterThanOrEqualTo(3));
        expect(report['operations']['operation_1']['count'], greaterThanOrEqualTo(2));
        expect(report['operations']['operation_2']['count'], greaterThanOrEqualTo(1));
      });

      test('should calculate performance trends', () {
        // Record multiple metrics to establish a trend
        for (int i = 0; i < 15; i++) {
          performanceService.recordPerformanceMetric('trending_operation', {
            'duration_ms': 100.0 + (i * 10), // Increasing duration
          });
        }

        final report = performanceService.getComprehensivePerformanceReport();
        final trend = report['operations']['trending_operation']['performance_trend'];
        
        expect(trend, isA<String>());
        expect(['improving', 'stable', 'degrading'], contains(trend));
      });
    });

    group('Optimization Recommendations', () {
      test('should generate recommendations for slow operations', () {
        performanceService.recordPerformanceMetric('slow_operation', {
          'duration_ms': 1500.0, // Above slow threshold
        });

        final report = performanceService.getComprehensivePerformanceReport();
        final recommendations = report['optimization_recommendations'] as List;
        
        expect(recommendations, isNotEmpty);
        
        final slowOpRecommendation = recommendations.cast<Map<String, dynamic>>().firstWhere(
          (rec) => rec['type'] == 'slow_operation',
          orElse: () => <String, dynamic>{},
        );

        expect(slowOpRecommendation, isNotEmpty);
        expect(slowOpRecommendation['priority'], equals('high'));
        expect(slowOpRecommendation['suggested_actions'], isA<List>());
      });

      test('should generate recommendations for high-frequency operations', () {
        // Simulate high-frequency operation
        for (int i = 0; i < 1500; i++) {
          performanceService.recordPerformanceMetric('frequent_operation', {
            'duration_ms': 50.0,
          });
        }

        final report = performanceService.getComprehensivePerformanceReport();
        final recommendations = report['optimization_recommendations'] as List;
        
        final frequentOpRecommendation = recommendations.cast<Map<String, dynamic>>().firstWhere(
          (rec) => rec['type'] == 'high_frequency_operation',
          orElse: () => <String, dynamic>{},
        );

        expect(frequentOpRecommendation, isNotEmpty);
        expect(frequentOpRecommendation['count'], greaterThan(1000));
      });

      test('should not generate recommendations for optimal operations', () {
        performanceService.recordPerformanceMetric('optimal_operation', {
          'duration_ms': 50.0, // Fast operation
        });

        final report = performanceService.getComprehensivePerformanceReport();
        final recommendations = report['optimization_recommendations'] as List;
        
        final optimalOpRecommendations = recommendations.where(
          (rec) => rec['operation'] == 'optimal_operation',
        ).toList();
        
        expect(optimalOpRecommendations, isEmpty);
      });
    });

    group('Health Indicators', () {
      test('should calculate system health indicators', () {
        performanceService.recordPerformanceMetric('test_operation', {
          'duration_ms': 100.0,
        });

        final report = performanceService.getComprehensivePerformanceReport();
        final healthIndicators = report['health_indicators'] as Map<String, dynamic>;
        
        expect(healthIndicators['memory_pressure'], isA<double>());
        expect(healthIndicators['cpu_efficiency'], isA<double>());
        expect(healthIndicators['response_time_health'], isA<double>());
        expect(healthIndicators['cache_efficiency'], isA<double>());
        
        // All health indicators should be between 0 and 1
        for (final value in healthIndicators.values) {
          expect(value as double, greaterThanOrEqualTo(0.0));
          expect(value as double, lessThanOrEqualTo(1.0));
        }
      });

      test('should calculate system health score', () {
        performanceService.recordPerformanceMetric('test_operation', {
          'duration_ms': 100.0,
        });

        final report = performanceService.getComprehensivePerformanceReport();
        final insights = report['performance_insights'] as Map<String, dynamic>;
        final summary = insights['summary'] as Map<String, dynamic>;
        
        expect(summary['system_health_score'], isA<double>());
        expect(summary['system_health_score'] as double, greaterThanOrEqualTo(0.0));
        expect(summary['system_health_score'] as double, lessThanOrEqualTo(1.0));
      });
    });

    group('Resource Utilization', () {
      test('should track resource utilization metrics', () {
        performanceService.recordPerformanceMetric('database_query', {
          'duration_ms': 200.0,
        });
        
        performanceService.recordPerformanceMetric('cache_operation', {
          'duration_ms': 10.0,
        });

        final report = performanceService.getComprehensivePerformanceReport();
        final resourceUtilization = report['resource_utilization'] as Map<String, dynamic>;
        
        expect(resourceUtilization['memory_utilization'], isA<Map>());
        expect(resourceUtilization['cpu_utilization'], isA<Map>());
        expect(resourceUtilization['io_utilization'], isA<Map>());
        
        final ioUtilization = resourceUtilization['io_utilization'] as Map<String, dynamic>;
        expect(ioUtilization['database_query_efficiency'], isA<double>());
        expect(ioUtilization['cache_hit_ratio'], isA<double>());
        expect(ioUtilization['network_efficiency'], isA<double>());
      });

      test('should calculate optimization potential', () {
        performanceService.recordPerformanceMetric('test_operation', {
          'duration_ms': 100.0,
        });

        final report = performanceService.getComprehensivePerformanceReport();
        final resourceUtilization = report['resource_utilization'] as Map<String, dynamic>;
        
        final memoryUtilization = resourceUtilization['memory_utilization'] as Map<String, dynamic>;
        final cpuUtilization = resourceUtilization['cpu_utilization'] as Map<String, dynamic>;
        
        expect(memoryUtilization['optimization_potential'], isA<double>());
        expect(cpuUtilization['optimization_potential'], isA<double>());
        
        // Optimization potential should be between 0 and 1
        expect(memoryUtilization['optimization_potential'] as double, greaterThanOrEqualTo(0.0));
        expect(memoryUtilization['optimization_potential'] as double, lessThanOrEqualTo(1.0));
      });
    });

    group('Performance Insights', () {
      test('should generate performance insights', () {
        performanceService.recordPerformanceMetric('popular_operation', {'duration_ms': 100.0});
        performanceService.recordPerformanceMetric('rare_operation', {'duration_ms': 150.0});
        performanceService.recordPerformanceMetric('popular_operation', {'duration_ms': 110.0});

        final report = performanceService.getComprehensivePerformanceReport();
        final insights = report['performance_insights'] as Map<String, dynamic>;
        
        expect(insights['top_operations'], isA<List>());
        expect(insights['summary'], isA<Map>());
        
        final topOperations = insights['top_operations'] as List;
        expect(topOperations, isNotEmpty);
        
        final topOperation = topOperations.first as Map<String, dynamic>;
        expect(topOperation['operation'], isA<String>());
        expect(topOperation['count'], greaterThanOrEqualTo(1));
      });

      test('should provide comprehensive summary', () {
        performanceService.recordPerformanceMetric('operation_1', {'duration_ms': 100.0});
        performanceService.recordPerformanceMetric('operation_2', {'duration_ms': 200.0});

        final report = performanceService.getComprehensivePerformanceReport();
        final insights = report['performance_insights'] as Map<String, dynamic>;
        final summary = insights['summary'] as Map<String, dynamic>;
        
        expect(summary['total_operations'], greaterThanOrEqualTo(2));
        expect(summary['unique_operations'], greaterThanOrEqualTo(2));
        expect(summary['avg_response_time_ms'], isA<double>());
        expect(summary['system_health_score'], isA<double>());
        expect(summary['optimization_opportunities'], isA<int>());
      });
    });

    group('Data Management', () {
      test('should limit metrics history to prevent memory leaks', () {
        // Record more than 1000 metrics for a single operation
        for (int i = 0; i < 1200; i++) {
          performanceService.recordPerformanceMetric('test_operation', {
            'duration_ms': 100.0 + i,
          });
        }

        final report = performanceService.getComprehensivePerformanceReport();
        
        // Should still work without memory issues
        expect(report['total_operations'], greaterThanOrEqualTo(1200));
        expect(report['operations']['test_operation']['count'], greaterThanOrEqualTo(1200));
      });

      test('should handle cleanup operations', () {
        performanceService.recordPerformanceMetric('test_operation', {
          'duration_ms': 100.0,
        });

        // Cleanup should not throw errors
        expect(() => performanceService.cleanup(), returnsNormally);
        
        // Should still be able to generate reports after cleanup
        final report = performanceService.getComprehensivePerformanceReport();
        expect(report, isA<Map<String, dynamic>>());
      });
    });

    group('Edge Cases', () {
      test('should handle empty metrics gracefully', () {
        // Clear any existing state first
        performanceService.cleanup();

        final report = performanceService.getComprehensivePerformanceReport();

        expect(report['total_operations'], greaterThanOrEqualTo(0));
        expect(report['operations'], isA<Map>());
        expect(report['health_indicators'], isA<Map>());
        expect(report['optimization_recommendations'], isA<List>());
      });

      test('should handle zero duration metrics', () {
        performanceService.recordPerformanceMetric('instant_operation', {
          'duration_ms': 0.0,
        });

        final report = performanceService.getComprehensivePerformanceReport();

        expect(report['total_operations'], greaterThanOrEqualTo(1));
        expect(report['operations']['instant_operation']['avg_duration_ms'], equals(0.0));
      });

      test('should handle negative duration metrics', () {
        performanceService.recordPerformanceMetric('negative_operation', {
          'duration_ms': -10.0,
        });

        final report = performanceService.getComprehensivePerformanceReport();

        // Should handle gracefully without crashing
        expect(report['total_operations'], greaterThanOrEqualTo(1));
        expect(report['operations']['negative_operation']['avg_duration_ms'], equals(-10.0));
      });
    });
  });
}
