import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ml_insights.g.dart';

/// ML insight types
enum InsightType {
  @JsonValue('pattern_detection')
  patternDetection,
  @JsonValue('anomaly_detection')
  anomalyDetection,
  @JsonValue('trend_analysis')
  trendAnalysis,
  @JsonValue('correlation_analysis')
  correlationAnalysis,
  @JsonValue('optimization_opportunity')
  optimizationOpportunity,
  @JsonValue('risk_assessment')
  riskAssessment,
  @JsonValue('recommendation')
  recommendation,
}

/// Insight priority levels
enum InsightPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

/// Actionability levels
enum ActionabilityLevel {
  @JsonValue('not_actionable')
  notActionable,
  @JsonValue('needs_investigation')
  needsInvestigation,
  @JsonValue('actionable')
  actionable,
  @JsonValue('immediate_action')
  immediateAction,
}

/// Machine learning insight
@JsonSerializable()
class MLInsight extends Equatable {
  /// Insight ID
  final String id;

  /// Insight type
  final InsightType type;

  /// Insight title
  final String title;

  /// Insight description
  final String description;

  /// Priority level
  final InsightPriority priority;

  /// Actionability level
  final ActionabilityLevel actionabilityLevel;

  /// Confidence score (0.0 to 1.0)
  final double confidence;

  /// Impact score (0.0 to 1.0)
  final double impactScore;

  /// Organization ID (if applicable)
  final String? organizationId;

  /// Entity ID this insight relates to
  final String? entityId;

  /// Entity type (user, quest, task, project, etc.)
  final String? entityType;

  /// Insight data and evidence
  final Map<String, dynamic> data;

  /// Recommended actions
  final List<String> recommendedActions;

  /// Supporting evidence
  final List<Map<String, dynamic>> evidence;

  /// Related insights
  final List<String> relatedInsightIds;

  /// Tags for categorization
  final List<String> tags;

  /// Insight creation timestamp
  final DateTime createdAt;

  /// Insight validity period
  final DateTime? validUntil;

  /// Whether insight has been acted upon
  final bool isActedUpon;

  /// Action taken timestamp
  final DateTime? actionTakenAt;

  /// Feedback on insight usefulness
  final Map<String, dynamic>? feedback;

  const MLInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
    required this.actionabilityLevel,
    required this.confidence,
    required this.impactScore,
    this.organizationId,
    this.entityId,
    this.entityType,
    required this.data,
    this.recommendedActions = const [],
    this.evidence = const [],
    this.relatedInsightIds = const [],
    this.tags = const [],
    required this.createdAt,
    this.validUntil,
    this.isActedUpon = false,
    this.actionTakenAt,
    this.feedback,
  });

  /// Create MLInsight from JSON
  factory MLInsight.fromJson(Map<String, dynamic> json) => _$MLInsightFromJson(json);

  /// Convert MLInsight to JSON
  Map<String, dynamic> toJson() => _$MLInsightToJson(this);

  /// Check if insight is expired
  bool get isExpired {
    if (validUntil == null) return false;
    return DateTime.now().isAfter(validUntil!);
  }

  /// Get priority as numeric value for sorting
  int get priorityValue {
    switch (priority) {
      case InsightPriority.low:
        return 1;
      case InsightPriority.medium:
        return 2;
      case InsightPriority.high:
        return 3;
      case InsightPriority.critical:
        return 4;
    }
  }

  /// Get confidence as percentage string
  String get confidencePercentage => '${(confidence * 100).toStringAsFixed(1)}%';

  /// Get impact as percentage string
  String get impactPercentage => '${(impactScore * 100).toStringAsFixed(1)}%';

  @override
  List<Object?> get props => [
        id,
        type,
        title,
        description,
        priority,
        actionabilityLevel,
        confidence,
        impactScore,
        organizationId,
        entityId,
        entityType,
        data,
        recommendedActions,
        evidence,
        relatedInsightIds,
        tags,
        createdAt,
        validUntil,
        isActedUpon,
        actionTakenAt,
        feedback,
      ];
}

/// Smart recommendation model
@JsonSerializable()
class SmartRecommendation extends Equatable {
  /// Recommendation ID
  final String id;

  /// Recommendation title
  final String title;

  /// Recommendation description
  final String description;

  /// Recommendation type
  final String type;

  /// Target user ID
  final String? userId;

  /// Organization ID (if applicable)
  final String? organizationId;

  /// Confidence score (0.0 to 1.0)
  final double confidence;

  /// Expected impact score (0.0 to 1.0)
  final double expectedImpact;

  /// Reasoning behind the recommendation
  final String reasoning;

  /// Supporting data
  final Map<String, dynamic> supportingData;

  /// Action steps
  final List<String> actionSteps;

  /// Expected outcomes
  final List<String> expectedOutcomes;

  /// Success metrics
  final Map<String, dynamic> successMetrics;

  /// Prerequisites
  final List<String> prerequisites;

  /// Estimated effort (in hours)
  final double? estimatedEffort;

  /// Estimated timeframe (in days)
  final int? estimatedTimeframeDays;

  /// Category
  final String category;

  /// Tags
  final List<String> tags;

  /// Creation timestamp
  final DateTime createdAt;

  /// Expiry timestamp
  final DateTime? expiresAt;

  /// Whether recommendation is accepted
  final bool isAccepted;

  /// Whether recommendation is dismissed
  final bool isDismissed;

  /// Implementation status
  final String? implementationStatus;

  const SmartRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.userId,
    this.organizationId,
    required this.confidence,
    required this.expectedImpact,
    required this.reasoning,
    required this.supportingData,
    this.actionSteps = const [],
    this.expectedOutcomes = const [],
    this.successMetrics = const {},
    this.prerequisites = const [],
    this.estimatedEffort,
    this.estimatedTimeframeDays,
    required this.category,
    this.tags = const [],
    required this.createdAt,
    this.expiresAt,
    this.isAccepted = false,
    this.isDismissed = false,
    this.implementationStatus,
  });

  /// Create SmartRecommendation from JSON
  factory SmartRecommendation.fromJson(Map<String, dynamic> json) => _$SmartRecommendationFromJson(json);

  /// Convert SmartRecommendation to JSON
  Map<String, dynamic> toJson() => _$SmartRecommendationToJson(this);

  /// Check if recommendation is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Get priority score based on confidence and impact
  double get priorityScore => (confidence + expectedImpact) / 2;

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        userId,
        organizationId,
        confidence,
        expectedImpact,
        reasoning,
        supportingData,
        actionSteps,
        expectedOutcomes,
        successMetrics,
        prerequisites,
        estimatedEffort,
        estimatedTimeframeDays,
        category,
        tags,
        createdAt,
        expiresAt,
        isAccepted,
        isDismissed,
        implementationStatus,
      ];
}
