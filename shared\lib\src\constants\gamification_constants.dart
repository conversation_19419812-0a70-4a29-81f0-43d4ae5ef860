/// Gamification constants for Quester platform based on CLAUDE.md Phase 2 system
class GamificationConstants {
  // Role-based point system thresholds (from CLAUDE.md: Newcomer 0-499 → Mythic 25000+)
  static const Map<String, int> roleThresholds = {
    'newcomer': 0,
    'apprentice': 500,
    'journeyman': 2000,
    'expert': 5000,
    'master': 10000,
    'grandmaster': 15000,
    'legendary': 20000,
    'mythic': 25000,
  };

  // Role multipliers (from CLAUDE.md implementation)
  static const Map<String, double> roleMultipliers = {
    'newcomer': 1.0,
    'apprentice': 1.2,
    'journeyman': 1.4,
    'expert': 1.6,
    'master': 1.8,
    'grandmaster': 2.0,
    'legendary': 2.5,
    'mythic': 3.0,
  };

  // Achievement rarity multipliers (5 levels as per CLAUDE.md)
  static const Map<String, double> rarityMultipliers = {
    'common': 1.0,
    'uncommon': 1.5,
    'rare': 2.0,
    'epic': 3.0,
    'legendary': 5.0,
  };

  // Base point values for different activities
  static const Map<String, int> basePointValues = {
    'task_simple': 10,
    'task_moderate': 25,
    'task_complex': 50,
    'task_very_complex': 100,
    'quest_beginner': 100,
    'quest_intermediate': 250,
    'quest_advanced': 500,
    'quest_expert': 750,
    'quest_master': 1000,
    'achievement_common': 50,
    'achievement_uncommon': 100,
    'achievement_rare': 200,
    'achievement_epic': 500,
    'achievement_legendary': 1000,
    'daily_login': 5,
    'streak_bonus_weekly': 50,
    'streak_bonus_monthly': 200,
    'collaboration_bonus': 25,
    'early_completion_bonus': 50,
  };

  // Achievement categories (8 categories as per CLAUDE.md)
  static const List<String> achievementCategories = [
    'completion',
    'streak',
    'speed',
    'collaboration',
    'quality',
    'milestone',
    'special',
    'exploration',
  ];

  // Achievement unlock conditions
  static const Map<String, String> unlockConditionTypes = {
    'quest_completion': 'Complete quests',
    'task_completion': 'Complete tasks',
    'streak_days': 'Maintain daily streak',
    'points_earned': 'Earn total points',
    'level_reached': 'Reach user level',
    'time_efficiency': 'Complete tasks efficiently',
    'collaboration_count': 'Collaborate with others',
    'feature_usage': 'Use platform features',
  };

  // Streak types and their multipliers
  static const Map<String, Map<String, dynamic>> streakConfigs = {
    'daily_login': {
      'name': 'Daily Login',
      'description': 'Log in every day',
      'base_multiplier': 1.0,
      'max_multiplier': 2.0,
      'bonus_threshold': 7, // Days to get bonus
      'bonus_increment': 0.1,
    },
    'daily_quest': {
      'name': 'Daily Quest',
      'description': 'Complete a quest every day',
      'base_multiplier': 1.2,
      'max_multiplier': 3.0,
      'bonus_threshold': 7,
      'bonus_increment': 0.2,
    },
    'daily_task': {
      'name': 'Daily Task',
      'description': 'Complete a task every day',
      'base_multiplier': 1.1,
      'max_multiplier': 2.5,
      'bonus_threshold': 5,
      'bonus_increment': 0.15,
    },
    'weekly_goal': {
      'name': 'Weekly Goal',
      'description': 'Complete weekly goals',
      'base_multiplier': 1.5,
      'max_multiplier': 4.0,
      'bonus_threshold': 4, // Weeks
      'bonus_increment': 0.25,
    },
  };

  // Leaderboard categories (multi-category as per CLAUDE.md)
  static const List<Map<String, dynamic>> leaderboardCategories = [
    {
      'id': 'total_points',
      'name': 'Total Points',
      'description': 'All-time points earned',
      'icon': '🏆',
      'period': 'all_time',
    },
    {
      'id': 'monthly_points',
      'name': 'Monthly Points',
      'description': 'Points earned this month',
      'icon': '📅',
      'period': 'monthly',
    },
    {
      'id': 'weekly_points',
      'name': 'Weekly Points',
      'description': 'Points earned this week',
      'icon': '📊',
      'period': 'weekly',
    },
    {
      'id': 'daily_points',
      'name': 'Daily Points',
      'description': 'Points earned today',
      'icon': '⚡',
      'period': 'daily',
    },
    {
      'id': 'quests_completed',
      'name': 'Quest Master',
      'description': 'Total quests completed',
      'icon': '🗺️',
      'period': 'all_time',
    },
    {
      'id': 'tasks_completed',
      'name': 'Task Champion',
      'description': 'Total tasks completed',
      'icon': '✅',
      'period': 'all_time',
    },
    {
      'id': 'current_streak',
      'name': 'Streak Warriors',
      'description': 'Current daily streak',
      'icon': '🔥',
      'period': 'current',
    },
    {
      'id': 'longest_streak',
      'name': 'Streak Legends',
      'description': 'Longest streak achieved',
      'icon': '🏅',
      'period': 'all_time',
    },
    {
      'id': 'achievements_count',
      'name': 'Achievement Hunters',
      'description': 'Total achievements unlocked',
      'icon': '🎖️',
      'period': 'all_time',
    },
    {
      'id': 'collaboration_score',
      'name': 'Team Players',
      'description': 'Collaboration contributions',
      'icon': '🤝',
      'period': 'all_time',
    },
    {
      'id': 'efficiency_rating',
      'name': 'Efficiency Experts',
      'description': 'Task completion efficiency',
      'icon': '⚙️',
      'period': 'all_time',
    },
  ];

  // Reward types and their properties
  static const Map<String, Map<String, dynamic>> rewardTypes = {
    'virtual': {
      'name': 'Virtual Items',
      'description': 'Digital badges, titles, themes',
      'icon': '🎁',
      'examples': ['badges', 'titles', 'avatars', 'themes'],
    },
    'privilege': {
      'name': 'Special Privileges',
      'description': 'Enhanced platform features',
      'icon': '👑',
      'examples': ['priority_support', 'advanced_analytics', 'custom_themes'],
    },
    'cosmetic': {
      'name': 'Cosmetic Items',
      'description': 'Visual customizations',
      'icon': '🎨',
      'examples': ['profile_frames', 'custom_colors', 'emoji_packs'],
    },
    'functionality': {
      'name': 'Feature Access',
      'description': 'Additional platform functionality',
      'icon': '⚡',
      'examples': ['advanced_filtering', 'bulk_operations', 'api_access'],
    },
    'recognition': {
      'name': 'Public Recognition',
      'description': 'Showcase achievements publicly',
      'icon': '🏆',
      'examples': ['hall_of_fame', 'featured_profile', 'special_mention'],
    },
  };

  // Default achievement definitions (50+ achievements across 8 categories)
  static const Map<String, List<Map<String, dynamic>>> defaultAchievements = {
    'completion': [
      {
        'id': 'first_quest',
        'title': 'First Steps',
        'description': 'Complete your first quest',
        'rarity': 'common',
        'points': 50,
        'condition': 'quest_completion',
        'required_value': 1,
        'icon': '🏁',
      },
      {
        'id': 'quest_novice',
        'title': 'Quest Novice',
        'description': 'Complete 10 quests',
        'rarity': 'uncommon',
        'points': 100,
        'condition': 'quest_completion',
        'required_value': 10,
        'icon': '📝',
      },
      {
        'id': 'quest_expert',
        'title': 'Quest Expert',
        'description': 'Complete 100 quests',
        'rarity': 'rare',
        'points': 200,
        'condition': 'quest_completion',
        'required_value': 100,
        'icon': '🎯',
      },
      {
        'id': 'quest_master',
        'title': 'Quest Master',
        'description': 'Complete 500 quests',
        'rarity': 'epic',
        'points': 500,
        'condition': 'quest_completion',
        'required_value': 500,
        'icon': '👑',
      },
      {
        'id': 'quest_legend',
        'title': 'Quest Legend',
        'description': 'Complete 1000 quests',
        'rarity': 'legendary',
        'points': 1000,
        'condition': 'quest_completion',
        'required_value': 1000,
        'icon': '⭐',
      },
    ],
    'streak': [
      {
        'id': 'streak_week',
        'title': 'Week Warrior',
        'description': 'Maintain a 7-day streak',
        'rarity': 'common',
        'points': 50,
        'condition': 'streak_days',
        'required_value': 7,
        'icon': '🔥',
      },
      {
        'id': 'streak_month',
        'title': 'Monthly Master',
        'description': 'Maintain a 30-day streak',
        'rarity': 'rare',
        'points': 200,
        'condition': 'streak_days',
        'required_value': 30,
        'icon': '📅',
      },
      {
        'id': 'streak_year',
        'title': 'Annual Achiever',
        'description': 'Maintain a 365-day streak',
        'rarity': 'legendary',
        'points': 1000,
        'condition': 'streak_days',
        'required_value': 365,
        'icon': '🏆',
      },
    ],
    'speed': [
      {
        'id': 'speed_demon',
        'title': 'Speed Demon',
        'description': 'Complete 10 tasks in under estimated time',
        'rarity': 'uncommon',
        'points': 100,
        'condition': 'time_efficiency',
        'required_value': 10,
        'icon': '⚡',
      },
      {
        'id': 'efficiency_expert',
        'title': 'Efficiency Expert',
        'description': 'Complete 50 tasks in under estimated time',
        'rarity': 'rare',
        'points': 200,
        'condition': 'time_efficiency',
        'required_value': 50,
        'icon': '⚙️',
      },
    ],
    'collaboration': [
      {
        'id': 'team_player',
        'title': 'Team Player',
        'description': 'Collaborate on 5 quests',
        'rarity': 'common',
        'points': 50,
        'condition': 'collaboration_count',
        'required_value': 5,
        'icon': '🤝',
      },
      {
        'id': 'collaboration_king',
        'title': 'Collaboration King',
        'description': 'Collaborate on 50 quests',
        'rarity': 'epic',
        'points': 500,
        'condition': 'collaboration_count',
        'required_value': 50,
        'icon': '👑',
      },
    ],
    'milestone': [
      {
        'id': 'level_10',
        'title': 'Rising Star',
        'description': 'Reach level 10',
        'rarity': 'uncommon',
        'points': 100,
        'condition': 'level_reached',
        'required_value': 10,
        'icon': '🌟',
      },
      {
        'id': 'level_50',
        'title': 'Veteran',
        'description': 'Reach level 50',
        'rarity': 'rare',
        'points': 200,
        'condition': 'level_reached',
        'required_value': 50,
        'icon': '🎖️',
      },
    ],
  };

  // Level progression system (progressive: level^2 * 100 points)
  static int getLevelThreshold(int level) {
    return level * level * 100;
  }

  static int getPointsForNextLevel(int currentLevel, int currentPoints) {
    final nextLevelThreshold = getLevelThreshold(currentLevel + 1);
    return nextLevelThreshold - currentPoints;
  }

  static double getLevelProgress(int currentLevel, int currentPoints) {
    final currentLevelThreshold = getLevelThreshold(currentLevel);
    final nextLevelThreshold = getLevelThreshold(currentLevel + 1);
    final levelRange = nextLevelThreshold - currentLevelThreshold;
    final pointsInCurrentLevel = currentPoints - currentLevelThreshold;
    
    return levelRange > 0 ? pointsInCurrentLevel / levelRange : 0.0;
  }

  // Notification types for gamification events
  static const Map<String, String> notificationTypes = {
    'achievement_unlocked': 'Achievement Unlocked',
    'level_up': 'Level Up',
    'streak_milestone': 'Streak Milestone',
    'points_earned': 'Points Earned',
    'reward_available': 'New Reward Available',
    'leaderboard_position': 'Leaderboard Update',
    'challenge_completed': 'Challenge Completed',
    'collaboration_invite': 'Collaboration Invite',
  };

  // Time-based bonus multipliers
  static const Map<String, double> timeBonusMultipliers = {
    'early_completion_50': 1.5, // Completed 50% early
    'early_completion_25': 1.25, // Completed 25% early
    'on_time_completion': 1.0, // Completed on time
    'late_completion': 0.8, // Completed late
  };

  // Difficulty multipliers for quests/tasks
  static const Map<String, double> difficultyMultipliers = {
    'beginner': 1.0,
    'intermediate': 1.5,
    'advanced': 2.0,
    'expert': 2.5,
    'master': 3.0,
  };

  // Priority multipliers for urgency
  static const Map<String, double> priorityMultipliers = {
    'low': 1.0,
    'medium': 1.2,
    'high': 1.5,
    'urgent': 2.0,
    'critical': 2.5,
  };

  // Complexity multipliers for tasks
  static const Map<String, double> complexityMultipliers = {
    'simple': 1.0,
    'moderate': 1.5,
    'complex': 2.0,
    'very_complex': 3.0,
  };

  // Gamification limits and constraints
  static const Map<String, int> limits = {
    'max_daily_points': 10000,
    'max_streak_bonus_multiplier': 300, // 3.0x
    'max_level': 1000,
    'max_achievements_per_category': 20,
    'max_leaderboard_entries': 1000,
    'max_reward_inventory': 100,
    'streak_break_grace_hours': 6, // Hours of grace period
    'achievement_progress_cache_minutes': 15,
    'leaderboard_update_minutes': 5,
  };

  // Default user gamification settings
  static const Map<String, dynamic> defaultUserSettings = {
    'notifications_enabled': true,
    'achievement_notifications': true,
    'level_up_notifications': true,
    'streak_notifications': true,
    'leaderboard_notifications': false,
    'public_profile': true,
    'show_in_leaderboards': true,
    'streak_reminders': true,
    'daily_goal_reminders': true,
  };

  // Utility methods
  static double calculatePointsWithMultipliers({
    required int basePoints,
    required double roleMultiplier,
    double streakMultiplier = 1.0,
    double difficultyMultiplier = 1.0,
    double priorityMultiplier = 1.0,
    double timeBonusMultiplier = 1.0,
  }) {
    return basePoints * 
           roleMultiplier * 
           streakMultiplier * 
           difficultyMultiplier * 
           priorityMultiplier * 
           timeBonusMultiplier;
  }

  static String getUserRole(int totalPoints) {
    for (final entry in roleThresholds.entries.toList().reversed) {
      if (totalPoints >= entry.value) {
        return entry.key;
      }
    }
    return 'newcomer';
  }

  static double getRoleMultiplier(String role) {
    return roleMultipliers[role] ?? 1.0;
  }

  static double getRarityMultiplier(String rarity) {
    return rarityMultipliers[rarity] ?? 1.0;
  }

  static int getBasePoints(String activityType) {
    return basePointValues[activityType] ?? 0;
  }
}