import 'package:equatable/equatable.dart';

/// Audit event types
enum AuditEventType {
  // Authentication events
  userLogin,
  userLogout,
  userLoginFailed,
  passwordChanged,
  mfaEnabled,
  mfaDisabled,
  
  // Organization events
  organizationCreated,
  organizationUpdated,
  organizationDeleted,
  
  // User management events
  userCreated,
  userUpdated,
  userDeleted,
  userInvited,
  userActivated,
  userDeactivated,
  
  // Role and permission events
  roleCreated,
  roleUpdated,
  roleDeleted,
  roleAssigned,
  roleUnassigned,
  permissionGranted,
  permissionRevoked,
  
  // Data events
  dataExported,
  dataImported,
  dataDeleted,
  dataAccessed,
  
  // System events
  configurationChanged,
  apiKeyCreated,
  apiKeyRevoked,
  webhookCreated,
  webhookUpdated,
  
  // Security events
  securityPolicyUpdated,
  suspiciousActivity,
  dataBreachDetected,
  complianceViolation,
}

/// Audit event severity levels
enum AuditSeverity {
  low,
  medium,
  high,
  critical,
}

/// Compliance framework types
enum ComplianceFramework {
  gdpr,
  soc2,
  hipaa,
  pci,
  iso27001,
  ccpa,
}

/// Audit event model for compliance tracking
class AuditEvent extends Equatable {
  /// Unique audit event identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// User ID who performed the action
  final String? userId;

  /// Event type
  final AuditEventType eventType;

  /// Event severity
  final AuditSeverity severity;

  /// Event description
  final String description;

  /// Resource type affected
  final String? resourceType;

  /// Resource ID affected
  final String? resourceId;

  /// IP address of the user
  final String? ipAddress;

  /// User agent string
  final String? userAgent;

  /// Session ID
  final String? sessionId;

  /// Additional event metadata
  final Map<String, dynamic>? metadata;

  /// Request details
  final Map<String, dynamic>? requestDetails;

  /// Response details
  final Map<String, dynamic>? responseDetails;

  /// Event timestamp
  final DateTime timestamp;

  /// Geolocation data
  final Map<String, dynamic>? geolocation;

  /// Whether this event requires attention
  final bool requiresAttention;

  /// Compliance frameworks this event relates to
  final List<ComplianceFramework> complianceFrameworks;

  const AuditEvent({
    required this.id,
    required this.organizationId,
    this.userId,
    required this.eventType,
    required this.severity,
    required this.description,
    this.resourceType,
    this.resourceId,
    this.ipAddress,
    this.userAgent,
    this.sessionId,
    this.metadata,
    this.requestDetails,
    this.responseDetails,
    required this.timestamp,
    this.geolocation,
    required this.requiresAttention,
    required this.complianceFrameworks,
  });

  /// Create AuditEvent from JSON
  factory AuditEvent.fromJson(Map<String, dynamic> json) {
    return AuditEvent(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      userId: json['userId'] as String?,
      eventType: AuditEventType.values.firstWhere(
        (e) => e.name == json['eventType'],
        orElse: () => AuditEventType.dataAccessed,
      ),
      severity: AuditSeverity.values.firstWhere(
        (e) => e.name == json['severity'],
        orElse: () => AuditSeverity.low,
      ),
      description: json['description'] as String,
      resourceType: json['resourceType'] as String?,
      resourceId: json['resourceId'] as String?,
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      sessionId: json['sessionId'] as String?,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      requestDetails: json['requestDetails'] != null
          ? Map<String, dynamic>.from(json['requestDetails'] as Map)
          : null,
      responseDetails: json['responseDetails'] != null
          ? Map<String, dynamic>.from(json['responseDetails'] as Map)
          : null,
      timestamp: DateTime.parse(json['timestamp'] as String),
      geolocation: json['geolocation'] != null
          ? Map<String, dynamic>.from(json['geolocation'] as Map)
          : null,
      requiresAttention: json['requiresAttention'] as bool,
      complianceFrameworks: (json['complianceFrameworks'] as List<dynamic>)
          .map((f) => ComplianceFramework.values.firstWhere(
                (e) => e.name == f,
                orElse: () => ComplianceFramework.gdpr,
              ))
          .toList(),
    );
  }

  /// Convert AuditEvent to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'userId': userId,
      'eventType': eventType.name,
      'severity': severity.name,
      'description': description,
      'resourceType': resourceType,
      'resourceId': resourceId,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'sessionId': sessionId,
      'metadata': metadata,
      'requestDetails': requestDetails,
      'responseDetails': responseDetails,
      'timestamp': timestamp.toIso8601String(),
      'geolocation': geolocation,
      'requiresAttention': requiresAttention,
      'complianceFrameworks': complianceFrameworks.map((f) => f.name).toList(),
    };
  }

  /// Get event category
  String get category {
    switch (eventType) {
      case AuditEventType.userLogin:
      case AuditEventType.userLogout:
      case AuditEventType.userLoginFailed:
      case AuditEventType.passwordChanged:
      case AuditEventType.mfaEnabled:
      case AuditEventType.mfaDisabled:
        return 'Authentication';
      
      case AuditEventType.organizationCreated:
      case AuditEventType.organizationUpdated:
      case AuditEventType.organizationDeleted:
        return 'Organization';
      
      case AuditEventType.userCreated:
      case AuditEventType.userUpdated:
      case AuditEventType.userDeleted:
      case AuditEventType.userInvited:
      case AuditEventType.userActivated:
      case AuditEventType.userDeactivated:
        return 'User Management';
      
      case AuditEventType.roleCreated:
      case AuditEventType.roleUpdated:
      case AuditEventType.roleDeleted:
      case AuditEventType.roleAssigned:
      case AuditEventType.roleUnassigned:
      case AuditEventType.permissionGranted:
      case AuditEventType.permissionRevoked:
        return 'Access Control';
      
      case AuditEventType.dataExported:
      case AuditEventType.dataImported:
      case AuditEventType.dataDeleted:
      case AuditEventType.dataAccessed:
        return 'Data Management';
      
      case AuditEventType.configurationChanged:
      case AuditEventType.apiKeyCreated:
      case AuditEventType.apiKeyRevoked:
      case AuditEventType.webhookCreated:
      case AuditEventType.webhookUpdated:
        return 'System Administration';
      
      case AuditEventType.securityPolicyUpdated:
      case AuditEventType.suspiciousActivity:
      case AuditEventType.dataBreachDetected:
      case AuditEventType.complianceViolation:
        return 'Security';
    }
  }

  /// Get severity color
  String get severityColor {
    switch (severity) {
      case AuditSeverity.low:
        return '#4CAF50';
      case AuditSeverity.medium:
        return '#FF9800';
      case AuditSeverity.high:
        return '#FF5722';
      case AuditSeverity.critical:
        return '#F44336';
    }
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        userId,
        eventType,
        severity,
        description,
        resourceType,
        resourceId,
        ipAddress,
        userAgent,
        sessionId,
        metadata,
        requestDetails,
        responseDetails,
        timestamp,
        geolocation,
        requiresAttention,
        complianceFrameworks,
      ];

  @override
  bool get stringify => true;
}

/// Compliance report model
class ComplianceReport extends Equatable {
  /// Unique report identifier
  final String id;

  /// Organization ID
  final String organizationId;

  /// Compliance framework
  final ComplianceFramework framework;

  /// Report title
  final String title;

  /// Report description
  final String? description;

  /// Report period start
  final DateTime periodStart;

  /// Report period end
  final DateTime periodEnd;

  /// Report status
  final String status;

  /// Compliance score (0-100)
  final double complianceScore;

  /// Total number of requirements
  final int totalRequirements;

  /// Number of compliant requirements
  final int compliantRequirements;

  /// Number of non-compliant requirements
  final int nonCompliantRequirements;

  /// Number of pending requirements
  final int pendingRequirements;

  /// Detailed findings
  final List<Map<String, dynamic>> findings;

  /// Recommendations
  final List<Map<String, dynamic>> recommendations;

  /// Evidence files
  final List<Map<String, dynamic>> evidenceFiles;

  /// Risk assessment
  final Map<String, dynamic> riskAssessment;

  /// Action items
  final List<Map<String, dynamic>> actionItems;

  /// Report metadata
  final Map<String, dynamic>? metadata;

  /// Generated by user ID
  final String generatedBy;

  /// Generation timestamp
  final DateTime generatedAt;

  /// Last update timestamp
  final DateTime updatedAt;

  const ComplianceReport({
    required this.id,
    required this.organizationId,
    required this.framework,
    required this.title,
    this.description,
    required this.periodStart,
    required this.periodEnd,
    required this.status,
    required this.complianceScore,
    required this.totalRequirements,
    required this.compliantRequirements,
    required this.nonCompliantRequirements,
    required this.pendingRequirements,
    required this.findings,
    required this.recommendations,
    required this.evidenceFiles,
    required this.riskAssessment,
    required this.actionItems,
    this.metadata,
    required this.generatedBy,
    required this.generatedAt,
    required this.updatedAt,
  });

  /// Create ComplianceReport from JSON
  factory ComplianceReport.fromJson(Map<String, dynamic> json) {
    return ComplianceReport(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      framework: ComplianceFramework.values.firstWhere(
        (e) => e.name == json['framework'],
        orElse: () => ComplianceFramework.gdpr,
      ),
      title: json['title'] as String,
      description: json['description'] as String?,
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
      status: json['status'] as String,
      complianceScore: (json['complianceScore'] as num).toDouble(),
      totalRequirements: json['totalRequirements'] as int,
      compliantRequirements: json['compliantRequirements'] as int,
      nonCompliantRequirements: json['nonCompliantRequirements'] as int,
      pendingRequirements: json['pendingRequirements'] as int,
      findings: List<Map<String, dynamic>>.from(
        (json['findings'] as List).map((f) => Map<String, dynamic>.from(f as Map)),
      ),
      recommendations: List<Map<String, dynamic>>.from(
        (json['recommendations'] as List).map((r) => Map<String, dynamic>.from(r as Map)),
      ),
      evidenceFiles: List<Map<String, dynamic>>.from(
        (json['evidenceFiles'] as List).map((e) => Map<String, dynamic>.from(e as Map)),
      ),
      riskAssessment: Map<String, dynamic>.from(json['riskAssessment'] as Map),
      actionItems: List<Map<String, dynamic>>.from(
        (json['actionItems'] as List).map((a) => Map<String, dynamic>.from(a as Map)),
      ),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      generatedBy: json['generatedBy'] as String,
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert ComplianceReport to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'organizationId': organizationId,
      'framework': framework.name,
      'title': title,
      'description': description,
      'periodStart': periodStart.toIso8601String(),
      'periodEnd': periodEnd.toIso8601String(),
      'status': status,
      'complianceScore': complianceScore,
      'totalRequirements': totalRequirements,
      'compliantRequirements': compliantRequirements,
      'nonCompliantRequirements': nonCompliantRequirements,
      'pendingRequirements': pendingRequirements,
      'findings': findings,
      'recommendations': recommendations,
      'evidenceFiles': evidenceFiles,
      'riskAssessment': riskAssessment,
      'actionItems': actionItems,
      'metadata': metadata,
      'generatedBy': generatedBy,
      'generatedAt': generatedAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Get framework display name
  String get frameworkDisplayName {
    switch (framework) {
      case ComplianceFramework.gdpr:
        return 'GDPR';
      case ComplianceFramework.soc2:
        return 'SOC 2';
      case ComplianceFramework.hipaa:
        return 'HIPAA';
      case ComplianceFramework.pci:
        return 'PCI DSS';
      case ComplianceFramework.iso27001:
        return 'ISO 27001';
      case ComplianceFramework.ccpa:
        return 'CCPA';
    }
  }

  /// Get compliance status
  String get complianceStatus {
    if (complianceScore >= 95) return 'Excellent';
    if (complianceScore >= 85) return 'Good';
    if (complianceScore >= 70) return 'Acceptable';
    if (complianceScore >= 50) return 'Needs Improvement';
    return 'Poor';
  }

  /// Get compliance percentage
  double get compliancePercentage {
    if (totalRequirements == 0) return 0;
    return (compliantRequirements / totalRequirements) * 100;
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        framework,
        title,
        description,
        periodStart,
        periodEnd,
        status,
        complianceScore,
        totalRequirements,
        compliantRequirements,
        nonCompliantRequirements,
        pendingRequirements,
        findings,
        recommendations,
        evidenceFiles,
        riskAssessment,
        actionItems,
        metadata,
        generatedBy,
        generatedAt,
        updatedAt,
      ];

  @override
  bool get stringify => true;
}
