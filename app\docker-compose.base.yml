# Base Docker Compose configuration for <PERSON><PERSON>
# This file contains common service definitions used across all environments

services:
  postgres:
    image: postgres:16
    platform: linux/amd64
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-quester}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-questerpass}
      POSTGRES_DB: ${POSTGRES_DB:-questerdb}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      PGUSER: ${POSTGRES_USER:-quester}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-quester} -d ${POSTGRES_DB:-questerdb}"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - quester-network

  redis:
    image: redis:7
    platform: linux/amd64
    restart: unless-stopped
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - quester-network

  server:
    build:
      context: ..
      dockerfile: app/server.${ENVIRONMENT:-dev}.dockerfile
      target: ${BUILD_TARGET:-development}
    restart: unless-stopped
    environment:
      - DART_ENV=${DART_ENV:-development}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-quester}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB:-questerdb}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - SERVER_HOST=${SERVER_HOST:-0.0.0.0}
      - PORT=${SERVER_PORT:-8080}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - quester-network

  client:
    build:
      context: ..
      dockerfile: app/client.${ENVIRONMENT:-dev}.dockerfile
      target: ${BUILD_TARGET:-development}
    restart: unless-stopped
    environment:
      - FLUTTER_WEB=${FLUTTER_WEB:-true}
      - API_BASE_URL=${API_BASE_URL:-http://localhost:8080}
      - WS_BASE_URL=${WS_BASE_URL:-ws://localhost:8080}
      - CLIENT_HOST=${CLIENT_HOST:-0.0.0.0}
      - CLIENT_PORT=${CLIENT_PORT:-3000}
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - quester-network

networks:
  quester-network:
    driver: bridge
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-network

volumes:
  postgres_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-postgres-data
  redis_data:
    name: ${COMPOSE_PROJECT_NAME:-quester-dev}-redis-data
