#!/bin/bash

# Quester Project Validation Script
echo "🔍 Quester Project Comprehensive Validation"
echo "=========================================="

# Test 1: Health Check
echo "1. Testing Health Check..."
health_response=$(curl -s -X GET "http://localhost:8080/health")
if echo "$health_response" | grep -q '"success":true'; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
fi

# Test 2: API Endpoints
echo "2. Testing API Endpoints..."

# Test Demo Users
demo_users=$(curl -s -X GET "http://localhost:8080/api/v1/auth/demo-users")
if echo "$demo_users" | grep -q '"success":true'; then
    echo "✅ Demo users endpoint working"
else
    echo "❌ Demo users endpoint failed"
fi

# Test Demo Login
login_response=$(curl -s -X POST "http://localhost:8080/api/v1/auth/demo-login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>"}')
if echo "$login_response" | grep -q '"success":true'; then
    echo "✅ Demo login working"
    # Extract token for subsequent tests
    token=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
else
    echo "❌ Demo login failed"
fi

# Test Users Endpoint (requires shared package DTOs)
users_response=$(curl -s -X GET "http://localhost:8080/api/v1/users" \
    -H "Authorization: Bearer $token")
if echo "$users_response" | grep -q '"success":true'; then
    echo "✅ Users endpoint with shared DTOs working"
else
    echo "❌ Users endpoint failed"
fi

# Test Tasks Endpoint (requires shared package models)
tasks_response=$(curl -s -X GET "http://localhost:8080/api/v1/tasks" \
    -H "Authorization: Bearer $token")
if echo "$tasks_response" | grep -q '"success":true'; then
    echo "✅ Tasks endpoint with shared models working"
else
    echo "❌ Tasks endpoint failed"
fi

# Test 3: Client Accessibility
echo "3. Testing Client Accessibility..."
client_response=$(curl -s -X GET "http://localhost:3000")
if echo "$client_response" | grep -q "<!DOCTYPE html>"; then
    echo "✅ Flutter client serving correctly"
else
    echo "❌ Flutter client not accessible"
fi

# Test 4: Database Services
echo "4. Testing Database Services..."
postgres_status=$(docker ps --filter "name=quester-dev-postgres" --filter "status=running" --format "table {{.Names}}")
if echo "$postgres_status" | grep -q "quester-dev-postgres"; then
    echo "✅ PostgreSQL running"
else
    echo "❌ PostgreSQL not running"
fi

redis_status=$(docker ps --filter "name=quester-dev-redis" --filter "status=running" --format "table {{.Names}}")
if echo "$redis_status" | grep -q "quester-dev-redis"; then
    echo "✅ Redis running"
else
    echo "❌ Redis not running"
fi

# Test 5: Development Tools
echo "5. Testing Development Tools..."
pgadmin_status=$(docker ps --filter "name=quester-dev-pgadmin" --filter "status=running" --format "table {{.Names}}")
if echo "$pgadmin_status" | grep -q "quester-dev-pgadmin"; then
    echo "✅ PgAdmin running on http://localhost:5050"
else
    echo "❌ PgAdmin not running"
fi

minio_status=$(docker ps --filter "name=quester-dev-minio" --filter "status=running" --format "table {{.Names}}")
if echo "$minio_status" | grep -q "quester-dev-minio"; then
    echo "✅ MinIO running on http://localhost:9001"
else
    echo "❌ MinIO not running"
fi

mailhog_status=$(docker ps --filter "name=quester-dev-mailhog" --filter "status=running" --format "table {{.Names}}")
if echo "$mailhog_status" | grep -q "quester-dev-mailhog"; then
    echo "✅ MailHog running on http://localhost:8025"
else
    echo "❌ MailHog not running"
fi

echo ""
echo "=========================================="
echo "🎉 Validation Complete!"
echo ""
echo "📋 Access Information:"
echo "   • Client (Flutter Web): http://localhost:3000"
echo "   • Server API: http://localhost:8080"
echo "   • Health Check: http://localhost:8080/health"
echo "   • API Documentation: http://localhost:8080/api/v1"
echo "   • PgAdmin: http://localhost:5050 (<EMAIL> / adminpass)"
echo "   • MailHog: http://localhost:8025"
echo "   • MinIO Console: http://localhost:9001 (quester / questerpass)"
echo "   • Redis Commander: http://localhost:8081"
echo ""
echo "👨‍💻 Demo Users:"
echo "   • <EMAIL> (password123) - Regular user"
echo "   • <EMAIL> (password123) - Admin user"
echo "   • <EMAIL> (password123) - Test user"
