import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import '../services/database_service.dart';

enum DeviceStatus { active, revoked, expired, suspicious }
enum DeviceTrustLevel { low, medium, high, verified }
enum DeviceType { mobile, desktop, tablet, unknown }

class TrustedDevice {
  final String id;
  final String userId;
  final String deviceFingerprint;
  final String deviceName;
  final DeviceType deviceType;
  final String? deviceModel;
  final String? operatingSystem;
  final String? browserInfo;
  final String ipAddress;
  final String? userAgent;
  final DeviceStatus status;
  final DeviceTrustLevel trustLevel;
  final DateTime firstSeen;
  final DateTime lastSeen;
  final DateTime? trustedAt;
  final DateTime? expiresAt;
  final int accessCount;
  final Map<String, dynamic> metadata;
  final String? revokedBy;
  final String? revocationReason;
  final DateTime? revokedAt;

  TrustedDevice({
    required this.id,
    required this.userId,
    required this.deviceFingerprint,
    required this.deviceName,
    required this.deviceType,
    this.deviceModel,
    this.operatingSystem,
    this.browserInfo,
    required this.ipAddress,
    this.userAgent,
    this.status = DeviceStatus.active,
    this.trustLevel = DeviceTrustLevel.low,
    required this.firstSeen,
    required this.lastSeen,
    this.trustedAt,
    this.expiresAt,
    this.accessCount = 1,
    this.metadata = const {},
    this.revokedBy,
    this.revocationReason,
    this.revokedAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'device_fingerprint': deviceFingerprint,
    'device_name': deviceName,
    'device_type': deviceType.name,
    'device_model': deviceModel,
    'operating_system': operatingSystem,
    'browser_info': browserInfo,
    'ip_address': ipAddress,
    'user_agent': userAgent,
    'status': status.name,
    'trust_level': trustLevel.name,
    'first_seen': firstSeen.toIso8601String(),
    'last_seen': lastSeen.toIso8601String(),
    'trusted_at': trustedAt?.toIso8601String(),
    'expires_at': expiresAt?.toIso8601String(),
    'access_count': accessCount,
    'metadata': metadata,
    'revoked_by': revokedBy,
    'revocation_reason': revocationReason,
    'revoked_at': revokedAt?.toIso8601String(),
  };

  factory TrustedDevice.fromJson(Map<String, dynamic> json) => TrustedDevice(
    id: json['id'],
    userId: json['user_id'],
    deviceFingerprint: json['device_fingerprint'],
    deviceName: json['device_name'],
    deviceType: DeviceType.values.byName(json['device_type'] ?? 'unknown'),
    deviceModel: json['device_model'],
    operatingSystem: json['operating_system'],
    browserInfo: json['browser_info'],
    ipAddress: json['ip_address'],
    userAgent: json['user_agent'],
    status: DeviceStatus.values.byName(json['status'] ?? 'active'),
    trustLevel: DeviceTrustLevel.values.byName(json['trust_level'] ?? 'low'),
    firstSeen: DateTime.parse(json['first_seen']),
    lastSeen: DateTime.parse(json['last_seen']),
    trustedAt: json['trusted_at'] != null ? DateTime.parse(json['trusted_at']) : null,
    expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
    accessCount: json['access_count'] ?? 1,
    metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    revokedBy: json['revoked_by'],
    revocationReason: json['revocation_reason'],
    revokedAt: json['revoked_at'] != null ? DateTime.parse(json['revoked_at']) : null,
  );
}

class DeviceRegistrationRequest {
  final String deviceFingerprint;
  final String deviceName;
  final DeviceType deviceType;
  final String? deviceModel;
  final String? operatingSystem;
  final String? browserInfo;
  final String ipAddress;
  final String? userAgent;
  final bool trustImmediately;
  final Duration? trustDuration;
  final Map<String, dynamic> metadata;

  DeviceRegistrationRequest({
    required this.deviceFingerprint,
    required this.deviceName,
    required this.deviceType,
    this.deviceModel,
    this.operatingSystem,
    this.browserInfo,
    required this.ipAddress,
    this.userAgent,
    this.trustImmediately = false,
    this.trustDuration,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() => {
    'device_fingerprint': deviceFingerprint,
    'device_name': deviceName,
    'device_type': deviceType.name,
    'device_model': deviceModel,
    'operating_system': operatingSystem,
    'browser_info': browserInfo,
    'ip_address': ipAddress,
    'user_agent': userAgent,
    'trust_immediately': trustImmediately,
    'trust_duration_hours': trustDuration?.inHours,
    'metadata': metadata,
  };
}

class DeviceVerificationResult {
  final bool isTrusted;
  final bool requiresMFA;
  final bool isNewDevice;
  final bool isSuspicious;
  final TrustedDevice? device;
  final String? warningMessage;
  final List<String> riskFactors;
  final Map<String, dynamic> metadata;

  DeviceVerificationResult({
    required this.isTrusted,
    required this.requiresMFA,
    required this.isNewDevice,
    this.isSuspicious = false,
    this.device,
    this.warningMessage,
    this.riskFactors = const [],
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() => {
    'is_trusted': isTrusted,
    'requires_mfa': requiresMFA,
    'is_new_device': isNewDevice,
    'is_suspicious': isSuspicious,
    'device': device?.toJson(),
    'warning_message': warningMessage,
    'risk_factors': riskFactors,
    'metadata': metadata,
  };
}

class TrustedDeviceConfig {
  final Duration defaultTrustDuration;
  final int maxTrustedDevices;
  final bool requireMFAForNewDevices;
  final bool autoTrustAfterMFA;
  final Duration suspiciousDeviceTimeout;
  final int maxFailedAttempts;
  final List<String> allowedCountries;
  final bool enableLocationTracking;

  const TrustedDeviceConfig({
    this.defaultTrustDuration = const Duration(days: 30),
    this.maxTrustedDevices = 10,
    this.requireMFAForNewDevices = true,
    this.autoTrustAfterMFA = true,
    this.suspiciousDeviceTimeout = const Duration(hours: 24),
    this.maxFailedAttempts = 5,
    this.allowedCountries = const [],
    this.enableLocationTracking = false,
  });

  Map<String, dynamic> toJson() => {
    'default_trust_duration_days': defaultTrustDuration.inDays,
    'max_trusted_devices': maxTrustedDevices,
    'require_mfa_for_new_devices': requireMFAForNewDevices,
    'auto_trust_after_mfa': autoTrustAfterMFA,
    'suspicious_device_timeout_hours': suspiciousDeviceTimeout.inHours,
    'max_failed_attempts': maxFailedAttempts,
    'allowed_countries': allowedCountries,
    'enable_location_tracking': enableLocationTracking,
  };
}

class TrustedDeviceService {
  final DatabaseService _databaseService;
  final Random _secureRandom;
  static const String _trustedDevicesTable = 'trusted_devices';
  static const String _deviceAccessLogTable = 'device_access_log';
  static const int _maxDeviceNameLength = 100;
  static const int _maxFingerprintLength = 256;

  TrustedDeviceService(this._databaseService) : _secureRandom = Random.secure();

  Future<TrustedDevice> registerDevice(
    String userId,
    DeviceRegistrationRequest request, {
    TrustedDeviceConfig config = const TrustedDeviceConfig(),
  }) async {
    try {
      await _validateUserExists(userId);
      _validateRegistrationRequest(request);

      // Check if device already exists
      final existingDevice = await _findDeviceByFingerprint(userId, request.deviceFingerprint);
      if (existingDevice != null) {
        return await _updateExistingDevice(existingDevice, request);
      }

      // Check device limits
      final currentDeviceCount = await _getActiveDeviceCount(userId);
      if (currentDeviceCount >= config.maxTrustedDevices) {
        throw Exception('Maximum trusted devices limit reached (${config.maxTrustedDevices})');
      }

      // Analyze device risk
      final riskAnalysis = await _analyzeDeviceRisk(userId, request);

      final now = DateTime.now();
      final deviceId = _generateDeviceId();
      
      final trustLevel = _calculateTrustLevel(riskAnalysis, request);
      final expiresAt = request.trustImmediately || config.autoTrustAfterMFA
          ? now.add(request.trustDuration ?? config.defaultTrustDuration)
          : null;

      final device = TrustedDevice(
        id: deviceId,
        userId: userId,
        deviceFingerprint: request.deviceFingerprint,
        deviceName: _sanitizeDeviceName(request.deviceName),
        deviceType: request.deviceType,
        deviceModel: request.deviceModel,
        operatingSystem: request.operatingSystem,
        browserInfo: request.browserInfo,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        status: riskAnalysis['is_suspicious'] == true ? DeviceStatus.suspicious : DeviceStatus.active,
        trustLevel: trustLevel,
        firstSeen: now,
        lastSeen: now,
        trustedAt: request.trustImmediately ? now : null,
        expiresAt: expiresAt,
        metadata: {
          ...request.metadata,
          'registration_ip': request.ipAddress,
          'risk_score': riskAnalysis['risk_score'],
          'risk_factors': riskAnalysis['risk_factors'],
        },
      );

      await _storeDevice(device);
      await _logDeviceAccess(device, 'device_registered');

      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_registered',
        details: {
          'device_id': deviceId,
          'device_name': device.deviceName,
          'device_type': device.deviceType.name,
          'trust_level': device.trustLevel.name,
          'ip_address': request.ipAddress,
          'is_suspicious': riskAnalysis['is_suspicious'],
          'risk_score': riskAnalysis['risk_score'],
        },
      );

      return device;
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_registration_failed',
        details: {
          'error': e.toString(),
          'device_fingerprint': request.deviceFingerprint,
          'ip_address': request.ipAddress,
        },
      );
      rethrow;
    }
  }

  Future<DeviceVerificationResult> verifyDevice(
    String userId,
    String deviceFingerprint, {
    String? ipAddress,
    String? userAgent,
    TrustedDeviceConfig config = const TrustedDeviceConfig(),
  }) async {
    try {
      await _validateUserExists(userId);

      final device = await _findDeviceByFingerprint(userId, deviceFingerprint);
      
      if (device == null) {
        await _logSecurityEvent(
          userId: userId,
          eventType: 'unknown_device_access',
          details: {
            'device_fingerprint': deviceFingerprint,
            'ip_address': ipAddress,
            'user_agent': userAgent,
          },
        );

        return DeviceVerificationResult(
          isTrusted: false,
          requiresMFA: config.requireMFAForNewDevices,
          isNewDevice: true,
          warningMessage: 'This device is not recognized. MFA verification required.',
          riskFactors: ['unknown_device'],
        );
      }

      // Update device activity
      await _updateDeviceLastSeen(device.id, ipAddress, userAgent);

      // Check device status
      if (device.status == DeviceStatus.revoked) {
        await _logSecurityEvent(
          userId: userId,
          eventType: 'revoked_device_access_attempt',
          details: {
            'device_id': device.id,
            'device_name': device.deviceName,
            'ip_address': ipAddress,
          },
        );

        return DeviceVerificationResult(
          isTrusted: false,
          requiresMFA: true,
          isNewDevice: false,
          device: device,
          warningMessage: 'This device has been revoked. Please contact support.',
          riskFactors: ['revoked_device'],
        );
      }

      if (device.status == DeviceStatus.expired || 
          (device.expiresAt != null && device.expiresAt!.isBefore(DateTime.now()))) {
        await _expireDevice(device.id);
        
        return DeviceVerificationResult(
          isTrusted: false,
          requiresMFA: true,
          isNewDevice: false,
          device: device,
          warningMessage: 'Device trust has expired. Please re-verify.',
          riskFactors: ['expired_device'],
        );
      }

      // Check for suspicious activity
      final riskFactors = await _analyzeCurrentRisk(device, ipAddress, userAgent);
      final isSuspicious = riskFactors.isNotEmpty;

      if (isSuspicious) {
        await _markDeviceSuspicious(device.id, riskFactors);
        
        return DeviceVerificationResult(
          isTrusted: false,
          requiresMFA: true,
          isNewDevice: false,
          isSuspicious: true,
          device: device,
          warningMessage: 'Suspicious activity detected. Additional verification required.',
          riskFactors: riskFactors,
        );
      }

      // Device is trusted
      final isTrusted = device.status == DeviceStatus.active && 
                      device.trustedAt != null &&
                      (device.expiresAt == null || device.expiresAt!.isAfter(DateTime.now()));

      return DeviceVerificationResult(
        isTrusted: isTrusted,
        requiresMFA: !isTrusted,
        isNewDevice: false,
        device: device,
        metadata: {
          'last_seen': device.lastSeen.toIso8601String(),
          'access_count': device.accessCount,
          'trust_level': device.trustLevel.name,
        },
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_verification_error',
        details: {
          'error': e.toString(),
          'device_fingerprint': deviceFingerprint,
          'ip_address': ipAddress,
        },
      );
      
      return DeviceVerificationResult(
        isTrusted: false,
        requiresMFA: true,
        isNewDevice: true,
        warningMessage: 'Verification failed. Please try again.',
        riskFactors: ['verification_error'],
      );
    }
  }

  Future<void> trustDevice(
    String userId,
    String deviceId, {
    Duration? trustDuration,
    String? trustedBy,
    String reason = 'Manual trust',
  }) async {
    try {
      await _validateUserExists(userId);
      
      final device = await _getDeviceById(deviceId);
      if (device == null || device.userId != userId) {
        throw Exception('Device not found or access denied');
      }

      final now = DateTime.now();
      final expiresAt = trustDuration != null ? now.add(trustDuration) : null;

      await _databaseService.execute('''
        UPDATE $_trustedDevicesTable 
        SET 
          status = @status,
          trust_level = CASE 
            WHEN trust_level = 'low' THEN 'medium'
            WHEN trust_level = 'medium' THEN 'high'
            ELSE trust_level
          END,
          trusted_at = @trusted_at,
          expires_at = @expires_at,
          metadata = metadata || @metadata
        WHERE id = @device_id
      ''', parameters: {
        'status': DeviceStatus.active.name,
        'trusted_at': now,
        'expires_at': expiresAt,
        'metadata': jsonEncode({
          'trusted_by': trustedBy,
          'trust_reason': reason,
          'trusted_manually': true,
        }),
        'device_id': deviceId,
      });

      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_trusted',
        details: {
          'device_id': deviceId,
          'device_name': device.deviceName,
          'trusted_by': trustedBy,
          'reason': reason,
          'expires_at': expiresAt?.toIso8601String(),
        },
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_trust_failed',
        details: {
          'error': e.toString(),
          'device_id': deviceId,
          'trusted_by': trustedBy,
        },
      );
      rethrow;
    }
  }

  Future<void> revokeDevice(
    String userId,
    String deviceId, {
    String? revokedBy,
    String reason = 'Manual revocation',
  }) async {
    try {
      await _validateUserExists(userId);
      
      final device = await _getDeviceById(deviceId);
      if (device == null || device.userId != userId) {
        throw Exception('Device not found or access denied');
      }

      await _databaseService.execute('''
        UPDATE $_trustedDevicesTable 
        SET 
          status = @status,
          revoked_at = NOW(),
          revoked_by = @revoked_by,
          revocation_reason = @reason
        WHERE id = @device_id
      ''', parameters: {
        'status': DeviceStatus.revoked.name,
        'revoked_by': revokedBy,
        'reason': reason,
        'device_id': deviceId,
      });

      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_revoked',
        details: {
          'device_id': deviceId,
          'device_name': device.deviceName,
          'revoked_by': revokedBy,
          'reason': reason,
        },
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_revocation_failed',
        details: {
          'error': e.toString(),
          'device_id': deviceId,
          'revoked_by': revokedBy,
        },
      );
      rethrow;
    }
  }

  Future<List<TrustedDevice>> getUserDevices(String userId, {bool activeOnly = false}) async {
    try {
      await _validateUserExists(userId);
      
      String whereClause = 'user_id = @user_id';
      if (activeOnly) {
        whereClause += ' AND status = \'active\'';
      }

      final result = await _databaseService.query('''
        SELECT * FROM $_trustedDevicesTable 
        WHERE $whereClause
        ORDER BY last_seen DESC
      ''', {'user_id': userId});
      
      return result.map((row) => TrustedDevice.fromJson(row)).toList();
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'device_list_error',
        details: {'error': e.toString()},
      );
      rethrow;
    }
  }

  Future<void> cleanupExpiredDevices() async {
    try {
      final result = await _databaseService.execute('''
        UPDATE $_trustedDevicesTable 
        SET status = 'expired'
        WHERE expires_at < NOW() AND status = 'active'
      ''');
      
      final affectedRows = result.affectedRows;
      if (affectedRows > 0) {
        print('Expired $affectedRows trusted devices');
      }
    } catch (e) {
      print('Error cleaning up expired devices: $e');
    }
  }

  String _generateDeviceId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomBytes = Uint8List(8);
    for (int i = 0; i < 8; i++) {
      randomBytes[i] = _secureRandom.nextInt(256);
    }
    final randomHex = randomBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    return 'device_${timestamp}_$randomHex';
  }

  String _sanitizeDeviceName(String name) {
    final sanitized = name.trim().replaceAll(RegExp(r'''[<>"']'''), '');
    return sanitized.length > _maxDeviceNameLength 
        ? sanitized.substring(0, _maxDeviceNameLength)
        : sanitized;
  }

  void _validateRegistrationRequest(DeviceRegistrationRequest request) {
    if (request.deviceFingerprint.isEmpty || 
        request.deviceFingerprint.length > _maxFingerprintLength) {
      throw Exception('Invalid device fingerprint');
    }
    
    if (request.deviceName.isEmpty || request.deviceName.length > _maxDeviceNameLength) {
      throw Exception('Invalid device name');
    }
  }

  Future<void> _validateUserExists(String userId) async {
    final result = await _databaseService.query(
      'SELECT id FROM users WHERE id = @user_id',
      {'user_id': userId},
    );
    
    if (result.isEmpty) {
      throw Exception('User not found: $userId');
    }
  }

  Future<TrustedDevice?> _findDeviceByFingerprint(String userId, String fingerprint) async {
    final result = await _databaseService.query('''
      SELECT * FROM $_trustedDevicesTable 
      WHERE user_id = @user_id AND device_fingerprint = @fingerprint
      LIMIT 1
    ''', {'user_id': userId, 'fingerprint': fingerprint});
    
    return result.isNotEmpty ? TrustedDevice.fromJson(result.first) : null;
  }

  Future<TrustedDevice?> _getDeviceById(String deviceId) async {
    final result = await _databaseService.query('''
      SELECT * FROM $_trustedDevicesTable 
      WHERE id = @device_id
      LIMIT 1
    ''', {'device_id': deviceId});
    
    return result.isNotEmpty ? TrustedDevice.fromJson(result.first) : null;
  }

  Future<int> _getActiveDeviceCount(String userId) async {
    final result = await _databaseService.query('''
      SELECT COUNT(*) as count 
      FROM $_trustedDevicesTable 
      WHERE user_id = @user_id AND status = 'active'
    ''', {'user_id': userId});
    
    return result.isNotEmpty ? result.first['count'] as int : 0;
  }

  Future<Map<String, dynamic>> _analyzeDeviceRisk(String userId, DeviceRegistrationRequest request) async {
    final riskFactors = <String>[];
    var riskScore = 0.0;

    // Check for multiple devices from same IP
    final sameIPCount = await _databaseService.query('''
      SELECT COUNT(*) as count 
      FROM $_trustedDevicesTable 
      WHERE user_id = @user_id AND ip_address = @ip_address AND status = 'active'
    ''', {'user_id': userId, 'ip_address': request.ipAddress});
    
    if (sameIPCount.isNotEmpty && (sameIPCount.first['count'] as int) > 3) {
      riskFactors.add('multiple_devices_same_ip');
      riskScore += 0.3;
    }

    // Check for rapid device registrations
    final recentRegistrations = await _databaseService.query('''
      SELECT COUNT(*) as count 
      FROM $_trustedDevicesTable 
      WHERE user_id = @user_id AND first_seen > NOW() - INTERVAL '1 hour'
    ''', {'user_id': userId});
    
    if (recentRegistrations.isNotEmpty && (recentRegistrations.first['count'] as int) > 2) {
      riskFactors.add('rapid_registrations');
      riskScore += 0.4;
    }

    // Add more risk factors based on device characteristics
    if (request.deviceType == DeviceType.unknown) {
      riskFactors.add('unknown_device_type');
      riskScore += 0.2;
    }

    final isSuspicious = riskScore > 0.5;

    return {
      'risk_score': riskScore,
      'risk_factors': riskFactors,
      'is_suspicious': isSuspicious,
    };
  }

  DeviceTrustLevel _calculateTrustLevel(Map<String, dynamic> riskAnalysis, DeviceRegistrationRequest request) {
    final riskScore = riskAnalysis['risk_score'] as double;
    
    if (riskScore > 0.7) return DeviceTrustLevel.low;
    if (riskScore > 0.4) return DeviceTrustLevel.medium;
    if (request.trustImmediately) return DeviceTrustLevel.high;
    
    return DeviceTrustLevel.medium;
  }

  Future<List<String>> _analyzeCurrentRisk(TrustedDevice device, String? currentIP, String? currentUA) async {
    final riskFactors = <String>[];

    // Check for IP address changes
    if (currentIP != null && currentIP != device.ipAddress) {
      riskFactors.add('ip_address_change');
    }

    // Check for user agent changes (significant changes)
    if (currentUA != null && device.userAgent != null) {
      final similarity = _calculateStringSimilarity(currentUA, device.userAgent!);
      if (similarity < 0.7) {
        riskFactors.add('user_agent_change');
      }
    }

    // Check for access from different countries (if location tracking enabled)
    // This would require additional geolocation service integration

    return riskFactors;
  }

  double _calculateStringSimilarity(String str1, String str2) {
    if (str1 == str2) return 1.0;
    if (str1.isEmpty || str2.isEmpty) return 0.0;
    
    final longer = str1.length > str2.length ? str1 : str2;
    final shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.isEmpty) return 1.0;
    
    final editDistance = _calculateLevenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  int _calculateLevenshteinDistance(String str1, String str2) {
    final matrix = List.generate(
      str1.length + 1,
      (i) => List.generate(str2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= str1.length; i++) {
      matrix[i][0] = i;
    }
    
    for (int j = 0; j <= str2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= str1.length; i++) {
      for (int j = 1; j <= str2.length; j++) {
        final cost = str1[i - 1] == str2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[str1.length][str2.length];
  }

  Future<TrustedDevice> _updateExistingDevice(TrustedDevice existingDevice, DeviceRegistrationRequest request) async {
    await _databaseService.execute('''
      UPDATE $_trustedDevicesTable 
      SET 
        last_seen = NOW(),
        access_count = access_count + 1,
        ip_address = @ip_address,
        user_agent = @user_agent,
        device_name = @device_name
      WHERE id = @device_id
    ''', parameters: {
      'ip_address': request.ipAddress,
      'user_agent': request.userAgent,
      'device_name': _sanitizeDeviceName(request.deviceName),
      'device_id': existingDevice.id,
    });

    await _logDeviceAccess(existingDevice, 'device_reused');
    return existingDevice;
  }

  Future<void> _storeDevice(TrustedDevice device) async {
    await _databaseService.execute('''
      INSERT INTO $_trustedDevicesTable (
        id, user_id, device_fingerprint, device_name, device_type,
        device_model, operating_system, browser_info, ip_address, user_agent,
        status, trust_level, first_seen, last_seen, trusted_at, expires_at,
        access_count, metadata
      ) VALUES (
        @id, @user_id, @device_fingerprint, @device_name, @device_type,
        @device_model, @operating_system, @browser_info, @ip_address, @user_agent,
        @status, @trust_level, @first_seen, @last_seen, @trusted_at, @expires_at,
        @access_count, @metadata
      )
    ''', parameters: {
      'id': device.id,
      'user_id': device.userId,
      'device_fingerprint': device.deviceFingerprint,
      'device_name': device.deviceName,
      'device_type': device.deviceType.name,
      'device_model': device.deviceModel,
      'operating_system': device.operatingSystem,
      'browser_info': device.browserInfo,
      'ip_address': device.ipAddress,
      'user_agent': device.userAgent,
      'status': device.status.name,
      'trust_level': device.trustLevel.name,
      'first_seen': device.firstSeen,
      'last_seen': device.lastSeen,
      'trusted_at': device.trustedAt,
      'expires_at': device.expiresAt,
      'access_count': device.accessCount,
      'metadata': jsonEncode(device.metadata),
    });
  }

  Future<void> _updateDeviceLastSeen(String deviceId, String? ipAddress, String? userAgent) async {
    await _databaseService.execute('''
      UPDATE $_trustedDevicesTable 
      SET 
        last_seen = NOW(),
        access_count = access_count + 1,
        ip_address = COALESCE(@ip_address, ip_address),
        user_agent = COALESCE(@user_agent, user_agent)
      WHERE id = @device_id
    ''', parameters: {
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'device_id': deviceId,
    });
  }

  Future<void> _expireDevice(String deviceId) async {
    await _databaseService.execute('''
      UPDATE $_trustedDevicesTable 
      SET status = 'expired'
      WHERE id = @device_id
    ''', parameters: {'device_id': deviceId});
  }

  Future<void> _markDeviceSuspicious(String deviceId, List<String> riskFactors) async {
    await _databaseService.execute('''
      UPDATE $_trustedDevicesTable 
      SET 
        status = 'suspicious',
        metadata = metadata || @metadata
      WHERE id = @device_id
    ''', parameters: {
      'metadata': jsonEncode({
        'suspicious_since': DateTime.now().toIso8601String(),
        'risk_factors': riskFactors,
      }),
      'device_id': deviceId,
    });
  }

  Future<void> _logDeviceAccess(TrustedDevice device, String accessType) async {
    try {
      await _databaseService.execute('''
        INSERT INTO $_deviceAccessLogTable (
          id, device_id, user_id, access_type, ip_address, user_agent, accessed_at
        ) VALUES (@id, @device_id, @user_id, @access_type, @ip_address, @user_agent, NOW())
      ''', parameters: {
        'id': _generateDeviceId(),
        'device_id': device.id,
        'user_id': device.userId,
        'access_type': accessType,
        'ip_address': device.ipAddress,
        'user_agent': device.userAgent,
      });
    } catch (e) {
      print('Warning: Failed to log device access: $e');
    }
  }

  Future<void> _logSecurityEvent({
    required String userId,
    required String eventType,
    required Map<String, dynamic> details,
  }) async {
    try {
      await _databaseService.execute('''
        INSERT INTO security_audit_log (id, user_id, event_type, event_details, created_at)
        VALUES (@id, @user_id, @event_type, @event_details, NOW())
      ''', parameters: {
        'id': _generateDeviceId(),
        'user_id': userId,
        'event_type': eventType,
        'event_details': jsonEncode(details),
      });
    } catch (e) {
      print('Warning: Failed to log security event: $e');
    }
  }
}

extension TrustedDeviceServiceRegistration on DatabaseService {
  TrustedDeviceService get trustedDevices => TrustedDeviceService(this);
}