import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// A widget that displays a notification badge over another widget
class NotificationBadge extends StatelessWidget {
  /// The child widget to display the badge over
  final Widget child;
  
  /// The number to display in the badge
  final int count;
  
  /// Maximum count to display before showing "99+"
  final int maxCount;
  
  /// Badge background color
  final Color? backgroundColor;
  
  /// Badge text color
  final Color? textColor;
  
  /// Badge size
  final double size;
  
  /// Offset from the top-right corner
  final Offset offset;
  
  /// Whether to show the badge when count is 0
  final bool showZero;
  
  /// Custom text to display instead of count
  final String? customText;

  const NotificationBadge({
    super.key,
    required this.child,
    required this.count,
    this.maxCount = AppConstants.maxNotificationCount,
    this.backgroundColor,
    this.textColor,
    this.size = 20,
    this.offset = const Offset(8, -8),
    this.showZero = false,
    this.customText,
  });

  @override
  Widget build(BuildContext context) {
    if (!showZero && count <= 0 && customText == null) {
      return child;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        Positioned(
          top: offset.dy,
          right: offset.dx,
          child: AnimatedScale(
            scale: count > 0 || customText != null ? 1.0 : 0.0,
            duration: AppConstants.shortAnimation,
            curve: Curves.elasticOut,
            child: _buildBadge(context),
          ),
        ),
      ],
    );
  }

  /// Build the badge widget
  Widget _buildBadge(BuildContext context) {
    final displayText = customText ?? _getDisplayText();
    final badgeColor = backgroundColor ?? Theme.of(context).colorScheme.error;
    final textColorValue = textColor ?? Theme.of(context).colorScheme.onError;

    return Container(
      constraints: BoxConstraints(
        minWidth: size,
        minHeight: size,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: displayText.length > 2 ? 6 : 0,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(size / 2),
        border: Border.all(
          color: Theme.of(context).colorScheme.surface,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: badgeColor.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          displayText,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: textColorValue,
            fontWeight: FontWeight.bold,
            fontSize: 11,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Get display text for the badge
  String _getDisplayText() {
    if (count <= 0) return '';
    if (count > maxCount) return '$maxCount+';
    return count.toString();
  }
}

/// A pulsing notification badge for urgent notifications
class PulsingNotificationBadge extends StatefulWidget {
  /// The child widget to display the badge over
  final Widget child;
  
  /// The number to display in the badge
  final int count;
  
  /// Whether the badge should pulse
  final bool shouldPulse;
  
  /// Badge background color
  final Color? backgroundColor;
  
  /// Badge text color
  final Color? textColor;

  const PulsingNotificationBadge({
    super.key,
    required this.child,
    required this.count,
    this.shouldPulse = true,
    this.backgroundColor,
    this.textColor,
  });

  @override
  State<PulsingNotificationBadge> createState() => _PulsingNotificationBadgeState();
}

class _PulsingNotificationBadgeState extends State<PulsingNotificationBadge>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    if (widget.shouldPulse && widget.count > 0) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(PulsingNotificationBadge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.shouldPulse && widget.count > 0) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.shouldPulse && widget.count > 0 ? _pulseAnimation.value : 1.0,
          child: NotificationBadge(
            count: widget.count,
            backgroundColor: widget.backgroundColor,
            textColor: widget.textColor,
            child: widget.child,
          ),
        );
      },
    );
  }
}

/// A dot-style notification badge for minimal design
class DotNotificationBadge extends StatelessWidget {
  /// The child widget to display the badge over
  final Widget child;
  
  /// Whether to show the dot
  final bool showDot;
  
  /// Dot color
  final Color? color;
  
  /// Dot size
  final double size;
  
  /// Offset from the top-right corner
  final Offset offset;

  const DotNotificationBadge({
    super.key,
    required this.child,
    required this.showDot,
    this.color,
    this.size = 12,
    this.offset = const Offset(6, -6),
  });

  @override
  Widget build(BuildContext context) {
    if (!showDot) {
      return child;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        Positioned(
          top: offset.dy,
          right: offset.dx,
          child: AnimatedScale(
            scale: showDot ? 1.0 : 0.0,
            duration: AppConstants.shortAnimation,
            curve: Curves.elasticOut,
            child: Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: color ?? Theme.of(context).colorScheme.error,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Theme.of(context).colorScheme.surface,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (color ?? Theme.of(context).colorScheme.error).withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// A custom shaped notification badge
class CustomNotificationBadge extends StatelessWidget {
  /// The child widget to display the badge over
  final Widget child;
  
  /// The content to display in the badge
  final Widget badgeContent;
  
  /// Badge background color
  final Color? backgroundColor;
  
  /// Badge shape
  final ShapeBorder? shape;
  
  /// Badge padding
  final EdgeInsets padding;
  
  /// Offset from the top-right corner
  final Offset offset;
  
  /// Whether to show the badge
  final bool show;

  const CustomNotificationBadge({
    super.key,
    required this.child,
    required this.badgeContent,
    this.backgroundColor,
    this.shape,
    this.padding = const EdgeInsets.all(4),
    this.offset = const Offset(8, -8),
    this.show = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!show) {
      return child;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        Positioned(
          top: offset.dy,
          right: offset.dx,
          child: AnimatedScale(
            scale: show ? 1.0 : 0.0,
            duration: AppConstants.shortAnimation,
            curve: Curves.elasticOut,
            child: Container(
              padding: padding,
              decoration: ShapeDecoration(
                color: backgroundColor ?? Theme.of(context).colorScheme.error,
                shape: shape ?? const CircleBorder(),
                shadows: [
                  BoxShadow(
                    color: (backgroundColor ?? Theme.of(context).colorScheme.error).withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: badgeContent,
            ),
          ),
        ),
      ],
    );
  }
}

/// A notification badge with animation effects
class AnimatedNotificationBadge extends StatefulWidget {
  /// The child widget to display the badge over
  final Widget child;
  
  /// The number to display in the badge
  final int count;
  
  /// Animation type
  final BadgeAnimationType animationType;
  
  /// Badge background color
  final Color? backgroundColor;
  
  /// Badge text color
  final Color? textColor;

  const AnimatedNotificationBadge({
    super.key,
    required this.child,
    required this.count,
    this.animationType = BadgeAnimationType.scale,
    this.backgroundColor,
    this.textColor,
  });

  @override
  State<AnimatedNotificationBadge> createState() => _AnimatedNotificationBadgeState();
}

class _AnimatedNotificationBadgeState extends State<AnimatedNotificationBadge>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int _previousCount = 0;

  @override
  void initState() {
    super.initState();
    _previousCount = widget.count;
    _controller = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _setupAnimation();
  }

  @override
  void didUpdateWidget(AnimatedNotificationBadge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.count != _previousCount) {
      _controller.forward(from: 0);
      _previousCount = widget.count;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _setupAnimation() {
    switch (widget.animationType) {
      case BadgeAnimationType.scale:
        _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
        );
        break;
      case BadgeAnimationType.bounce:
        _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(parent: _controller, curve: Curves.bounceOut),
        );
        break;
      case BadgeAnimationType.fade:
        _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
        );
        break;
    }

    if (widget.count > 0) {
      _controller.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        Widget badge = NotificationBadge(
          count: widget.count,
          backgroundColor: widget.backgroundColor,
          textColor: widget.textColor,
          child: widget.child,
        );

        switch (widget.animationType) {
          case BadgeAnimationType.scale:
          case BadgeAnimationType.bounce:
            return Transform.scale(
              scale: _animation.value,
              child: badge,
            );
          case BadgeAnimationType.fade:
            return Opacity(
              opacity: _animation.value,
              child: badge,
            );
        }
      },
    );
  }
}

/// Badge animation types
enum BadgeAnimationType {
  scale,
  bounce,
  fade,
}
