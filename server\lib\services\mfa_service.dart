import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared/shared.dart';
import '../services/database_service.dart';
import 'logging_service.dart';
import 'sms_service.dart';
import 'email_service.dart';

/// MFA Service for handling multi-factor authentication
class MFAService {
  static final MFAService _instance = MFAService._internal();
  factory MFAService() => _instance;
  MFAService._internal();

  final DatabaseService _dbService = DatabaseService();
  final SMSService _smsService = SMSService();
  final EmailService _emailService = EmailService();
  final Random _random = Random.secure();

  /// Setup TOTP (Time-based One-Time Password) for user
  Future<Map<String, dynamic>> setupTOTP(String userId) async {
    try {
      // Generate a secret for TOTP
      final secret = _generateTOTPSecret();
      final issuer = 'Quester';
      
      // Get user info for QR code
      final user = await _getUserInfo(userId);
      if (user == null) {
        return {'success': false, 'error': 'User not found'};
      }

      // Store encrypted secret in database
      await _storeMFASettings(userId, {
        'method': 'totp',
        'secret_encrypted': _encryptSecret(secret),
        'enabled': false, // Not enabled until verified
      });

      // Generate QR code data
      final qrCodeData = _generateTOTPQRCode(
        secret: secret,
        issuer: issuer,
        accountName: user['email'],
      );

      return {
        'success': true,
        'secret': secret,
        'qrCode': qrCodeData,
        'backupCodes': await _generateBackupCodes(userId),
      };
    } catch (e) {
      print('❌ Error setting up TOTP: $e');
      return {'success': false, 'error': 'Setup failed'};
    }
  }

  /// Setup SMS-based MFA
  Future<Map<String, dynamic>> setupSMS(String userId, String phoneNumber) async {
    try {
      // Validate phone number format
      if (!_isValidPhoneNumber(phoneNumber)) {
        return {'success': false, 'error': 'Invalid phone number format'};
      }

      // Store encrypted phone number
      await _storeMFASettings(userId, {
        'method': 'sms',
        'phone_number_encrypted': _encryptSecret(phoneNumber),
        'enabled': false,
      });

      // Send verification SMS
      final verificationCode = _generateVerificationCode();
      await _storeVerificationCode(userId, verificationCode);
      await _sendSMS(phoneNumber, 'Your Quester verification code: $verificationCode');

      return {
        'success': true,
        'phoneNumber': _maskPhoneNumber(phoneNumber),
        'message': 'Verification code sent to your phone',
      };
    } catch (e) {
      print('❌ Error setting up SMS MFA: $e');
      return {'success': false, 'error': 'Setup failed'};
    }
  }

  /// Setup Email-based MFA
  Future<Map<String, dynamic>> setupEmail(String userId, String email) async {
    try {
      await _storeMFASettings(userId, {
        'method': 'email',
        'backup_email_encrypted': _encryptSecret(email),
        'enabled': false,
      });

      // Send verification email
      final verificationCode = _generateVerificationCode();
      await _storeVerificationCode(userId, verificationCode);
      await _sendEmail(email, 'Quester MFA Setup', 
          'Your verification code: $verificationCode');

      return {
        'success': true,
        'email': _maskEmail(email),
        'message': 'Verification code sent to your email',
      };
    } catch (e) {
      print('❌ Error setting up Email MFA: $e');
      return {'success': false, 'error': 'Setup failed'};
    }
  }

  /// Verify MFA setup with code
  Future<Map<String, dynamic>> verifySetup(String userId, String code) async {
    try {
      final mfaSettings = await _getMFASettings(userId);
      if (mfaSettings == null) {
        return {'success': false, 'error': 'MFA not set up'};
      }

      bool isValid = false;
      
      switch (mfaSettings['method']) {
        case 'totp':
          isValid = _verifyTOTPCode(mfaSettings['secret_encrypted'], code);
          break;
        case 'sms':
        case 'email':
          isValid = await _verifyStoredCode(userId, code);
          break;
      }

      if (isValid) {
        // Enable MFA
        await _enableMFA(userId);
        return {
          'success': true,
          'message': 'MFA enabled successfully',
          'backupCodes': await _getBackupCodes(userId),
        };
      } else {
        return {'success': false, 'error': 'Invalid verification code'};
      }
    } catch (e) {
      print('❌ Error verifying MFA setup: $e');
      return {'success': false, 'error': 'Verification failed'};
    }
  }

  /// Verify MFA code during authentication
  Future<bool> verifyMFACode(String userId, String code, TwoFactorMethod method) async {
    try {
      final mfaSettings = await _getMFASettings(userId);
      if (mfaSettings == null || !mfaSettings['enabled']) {
        return false;
      }

      switch (method) {
        case TwoFactorMethod.totp:
          return _verifyTOTPCode(mfaSettings['secret_encrypted'], code);
        case TwoFactorMethod.sms:
          await _sendSMSCode(userId);
          return await _verifyStoredCode(userId, code);
        case TwoFactorMethod.email:
          await _sendEmailCode(userId);
          return await _verifyStoredCode(userId, code);
        case TwoFactorMethod.backupCodes:
          return await _verifyBackupCode(userId, code);
      }
    } catch (e) {
      print('❌ Error verifying MFA code: $e');
      return false;
    }
  }

  /// Get available MFA methods for user
  Future<List<TwoFactorMethod>> getAvailableMethods(String userId) async {
    try {
      final mfaSettings = await _getMFASettings(userId);
      if (mfaSettings == null || !mfaSettings['enabled']) {
        return [];
      }

      final methods = <TwoFactorMethod>[];
      
      if (mfaSettings['secret_encrypted'] != null) {
        methods.add(TwoFactorMethod.totp);
      }
      if (mfaSettings['phone_number_encrypted'] != null) {
        methods.add(TwoFactorMethod.sms);
      }
      if (mfaSettings['backup_email_encrypted'] != null) {
        methods.add(TwoFactorMethod.email);
      }
      
      // Backup codes are always available if MFA is enabled
      methods.add(TwoFactorMethod.backupCodes);

      return methods;
    } catch (e) {
      print('❌ Error getting MFA methods: $e');
      return [];
    }
  }

  /// Generate backup codes
  Future<List<String>> _generateBackupCodes(String userId) async {
    const count = 10;
    final codes = <String>[];
    
    for (int i = 0; i < count; i++) {
      codes.add(_generateBackupCode());
    }

    // Store hashed backup codes
    await _storeBackupCodes(userId, codes);
    
    return codes;
  }

  String _generateBackupCode() {
    return List.generate(8, (index) => _random.nextInt(10)).join();
  }

  String _generateTOTPSecret() {
    final bytes = List<int>.generate(20, (i) => _random.nextInt(256));
    return base32Encode(bytes);
  }

  String _generateVerificationCode() {
    return List.generate(6, (index) => _random.nextInt(10)).join();
  }

  String _generateTOTPQRCode({
    required String secret,
    required String issuer,
    required String accountName,
  }) {
    final otpauthUrl = 'otpauth://totp/$issuer:$accountName?'
        'secret=$secret&issuer=$issuer';
    return otpauthUrl;
  }

  bool _verifyTOTPCode(String encryptedSecret, String code) {
    try {
      final secret = _decryptSecret(encryptedSecret);
      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final timeStep = currentTime ~/ 30; // 30-second time window
      
      // Check current time and ±1 time step for clock drift
      for (int i = -1; i <= 1; i++) {
        final testCode = _generateTOTPCode(secret, timeStep + i);
        if (testCode == code) {
          return true;
        }
      }
      
      return false;
    } catch (e) {
      print('❌ Error verifying TOTP code: $e');
      return false;
    }
  }

  String _generateTOTPCode(String secret, int timeStep) {
    final secretBytes = base32Decode(secret);
    final timeBytes = _intToBytes(timeStep);
    
    final hmac = Hmac(sha1, secretBytes);
    final hash = hmac.convert(timeBytes).bytes;
    
    final offset = hash[hash.length - 1] & 0xf;
    final truncatedHash = (hash[offset] & 0x7f) << 24 |
                         (hash[offset + 1] & 0xff) << 16 |
                         (hash[offset + 2] & 0xff) << 8 |
                         (hash[offset + 3] & 0xff);
    
    final code = truncatedHash % 1000000;
    return code.toString().padLeft(6, '0');
  }

  List<int> _intToBytes(int value) {
    return [
      (value >> 56) & 0xff,
      (value >> 48) & 0xff,
      (value >> 40) & 0xff,
      (value >> 32) & 0xff,
      (value >> 24) & 0xff,
      (value >> 16) & 0xff,
      (value >> 8) & 0xff,
      value & 0xff,
    ];
  }

  // Database operations
  Future<Map<String, dynamic>?> _getUserInfo(String userId) async {
    final result = await _dbService.execute('''
      SELECT email, display_name FROM quester.users WHERE id = @userId::uuid
    ''', parameters: {'userId': userId});
    
    if (result.isEmpty) return null;
    
    final row = result.first;
    return {
      'email': row[0],
      'display_name': row[1],
    };
  }

  Future<void> _storeMFASettings(String userId, Map<String, dynamic> settings) async {
    await _dbService.execute('''
      INSERT INTO quester.user_mfa_settings (
        user_id, method, secret_encrypted, phone_number_encrypted,
        backup_email_encrypted, enabled, created_at
      ) VALUES (
        @userId::uuid, @method, @secret_encrypted, @phone_number_encrypted,
        @backup_email_encrypted, @enabled, NOW()
      ) ON CONFLICT (user_id) DO UPDATE SET
        method = EXCLUDED.method,
        secret_encrypted = EXCLUDED.secret_encrypted,
        phone_number_encrypted = EXCLUDED.phone_number_encrypted,
        backup_email_encrypted = EXCLUDED.backup_email_encrypted,
        enabled = EXCLUDED.enabled,
        updated_at = NOW()
    ''', parameters: {
      'userId': userId,
      'method': settings['method'],
      'secret_encrypted': settings['secret_encrypted'],
      'phone_number_encrypted': settings['phone_number_encrypted'],
      'backup_email_encrypted': settings['backup_email_encrypted'],
      'enabled': settings['enabled'],
    });
  }

  Future<Map<String, dynamic>?> _getMFASettings(String userId) async {
    final result = await _dbService.execute('''
      SELECT method, secret_encrypted, phone_number_encrypted,
             backup_email_encrypted, enabled
      FROM quester.user_mfa_settings
      WHERE user_id = @userId::uuid
    ''', parameters: {'userId': userId});
    
    if (result.isEmpty) return null;
    
    final row = result.first;
    return {
      'method': row[0],
      'secret_encrypted': row[1],
      'phone_number_encrypted': row[2],
      'backup_email_encrypted': row[3],
      'enabled': row[4],
    };
  }

  Future<void> _enableMFA(String userId) async {
    await _dbService.execute('''
      UPDATE quester.user_mfa_settings 
      SET enabled = true, updated_at = NOW()
      WHERE user_id = @userId::uuid
    ''', parameters: {'userId': userId});
  }

  // Utility methods
  String _encryptSecret(String secret) {
    // Get encryption key from environment or use secure default
    final encryptionKey = _generateSecureKey(); // Use secure default for development
    final keyBytes = sha256.convert(utf8.encode(encryptionKey)).bytes;
    final secretBytes = utf8.encode(secret);

    // Generate random IV for each encryption
    final iv = List<int>.generate(16, (i) => Random.secure().nextInt(256));

    // Simple XOR encryption with IV (use AES-256-GCM in production)
    final encrypted = <int>[];
    encrypted.addAll(iv); // Prepend IV to encrypted data

    for (int i = 0; i < secretBytes.length; i++) {
      final keyIndex = (i + iv[i % iv.length]) % keyBytes.length;
      encrypted.add(secretBytes[i] ^ keyBytes[keyIndex]);
    }

    return base64.encode(encrypted);
  }

  /// Generate a secure encryption key
  String _generateSecureKey() {
    // Generate a secure 256-bit key
    final random = Random.secure();
    final keyBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(keyBytes);
  }

  String _decryptSecret(String encryptedSecret) {
    const key = 'default-key';
    final keyBytes = sha256.convert(utf8.encode(key)).bytes;
    final encryptedBytes = base64.decode(encryptedSecret);
    
    final decrypted = <int>[];
    for (int i = 0; i < encryptedBytes.length; i++) {
      decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return utf8.decode(decrypted);
  }

  bool _isValidPhoneNumber(String phone) {
    // Simple phone number validation
    final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
    return phoneRegex.hasMatch(phone);
  }

  String _maskPhoneNumber(String phone) {
    if (phone.length < 4) return phone;
    return '***${phone.substring(phone.length - 4)}';
  }

  String _maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 2) return email;
    
    final masked = username[0] + '*' * (username.length - 2) + username[username.length - 1];
    return '$masked@$domain';
  }

  // Placeholder methods for external services
  Future<void> _sendSMS(String phoneNumber, String message) async {
    try {
      // Send SMS using SMS service
      final success = await _smsService.sendMFACode(phoneNumber, message);
      if (success) {
        LoggingService.mfa('SMS sent successfully to $phoneNumber');
      } else {
        LoggingService.error('Failed to send SMS to $phoneNumber');
        throw Exception('SMS delivery failed');
      }
    } catch (e) {
      LoggingService.error('Failed to send SMS to $phoneNumber', tag: 'MFAService', error: e);
      rethrow;
    }
  }

  Future<void> _sendEmail(String email, String subject, String body) async {
    try {
      // Send email using email service
      final success = await _emailService.sendMFACode(email, body);
      if (success) {
        LoggingService.email('MFA email sent successfully to $email', recipient: email);
      } else {
        LoggingService.error('Failed to send MFA email to $email');
        throw Exception('Email delivery failed');
      }
    } catch (e) {
      LoggingService.error('Failed to send email to $email', tag: 'MFAService', error: e);
      rethrow;
    }
  }

  // Additional placeholder methods for backup codes and verification
  Future<void> _storeVerificationCode(String userId, String code) async {
    // Store with expiration time
  }

  Future<bool> _verifyStoredCode(String userId, String code) async {
    // Verify and consume the code
    return true; // Placeholder
  }

  Future<void> _storeBackupCodes(String userId, List<String> codes) async {
    // Store hashed backup codes
  }

  Future<List<String>> _getBackupCodes(String userId) async {
    return []; // Return unhashed codes for display
  }

  Future<bool> _verifyBackupCode(String userId, String code) async {
    // Verify and consume backup code
    return false; // Placeholder
  }

  Future<void> _sendSMSCode(String userId) async {
    // Send SMS verification code
  }

  Future<void> _sendEmailCode(String userId) async {
    // Send email verification code
  }
}

// Base32 encoding/decoding utilities
String base32Encode(List<int> bytes) {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
  String result = '';
  int buffer = 0;
  int bitsLeft = 0;
  
  for (int byte in bytes) {
    buffer = (buffer << 8) | byte;
    bitsLeft += 8;
    
    while (bitsLeft >= 5) {
      result += alphabet[(buffer >> (bitsLeft - 5)) & 31];
      bitsLeft -= 5;
    }
  }
  
  if (bitsLeft > 0) {
    result += alphabet[(buffer << (5 - bitsLeft)) & 31];
  }
  
  return result;
}

List<int> base32Decode(String encoded) {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
  final result = <int>[];
  int buffer = 0;
  int bitsLeft = 0;
  
  for (int i = 0; i < encoded.length; i++) {
    final char = encoded[i].toUpperCase();
    final value = alphabet.indexOf(char);
    if (value == -1) continue;
    
    buffer = (buffer << 5) | value;
    bitsLeft += 5;
    
    if (bitsLeft >= 8) {
      result.add((buffer >> (bitsLeft - 8)) & 255);
      bitsLeft -= 8;
    }
  }
  
  return result;
}
