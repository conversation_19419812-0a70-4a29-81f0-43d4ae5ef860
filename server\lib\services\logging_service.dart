import 'dart:convert';
import 'dart:io';

/// Logging levels
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Server-side logging service with structured logging and error tracking
class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();

  /// Log level threshold
  LogLevel _logLevel = LogLevel.info;

  /// Whether to write logs to file
  bool _writeToFile = false;

  /// Log file path
  String? _logFilePath;

  /// Error tracking
  static final Map<String, int> _errorCounts = {};
  static final List<Map<String, dynamic>> _recentErrors = [];
  static const int _maxRecentErrors = 100;

  /// Initialize logging service
  void initialize({
    LogLevel logLevel = LogLevel.info,
    bool writeToFile = false,
    String? logFilePath,
  }) {
    _logLevel = logLevel;
    _writeToFile = writeToFile;
    _logFilePath = logFilePath ?? 'logs/app.log';
    
    if (_writeToFile) {
      _ensureLogDirectory();
    }
  }

  /// Ensure log directory exists
  void _ensureLogDirectory() {
    if (_logFilePath != null) {
      final file = File(_logFilePath!);
      final directory = file.parent;
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }
    }
  }

  /// Log a debug message
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log an info message
  static void info(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log an error message
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a critical error message
  static void critical(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.critical, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Internal logging method with error tracking
  void _log(LogLevel level, String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    // Check if this log level should be output
    if (level.index < _logLevel.index) return;

    final timestamp = DateTime.now().toUtc().toIso8601String();
    final levelStr = level.name.toUpperCase().padRight(8);
    final tagStr = tag != null ? '[$tag] ' : '';
    final logMessage = '$timestamp $levelStr $tagStr$message';

    // Output to console with colors
    final colorCode = _getColorCode(level);
    final resetCode = '\x1B[0m';
    final coloredMessage = '$colorCode$logMessage$resetCode';

    print(coloredMessage);

    // Add error details if present
    if (error != null) {
      print('$colorCode   Error: $error$resetCode');
    }
    if (stackTrace != null && level.index >= LogLevel.error.index) {
      print('$colorCode   Stack: $stackTrace$resetCode');
    }

    // Track errors for monitoring
    if (level.index >= LogLevel.error.index && error != null) {
      _trackError(error, level, tag, stackTrace);
    }

    // Write to file if enabled
    if (_writeToFile && _logFilePath != null) {
      _writeToLogFile(logMessage, error, stackTrace);
    }
  }

  /// Track errors for monitoring and alerting
  void _trackError(Object error, LogLevel level, String? tag, StackTrace? stackTrace) {
    final errorString = error.toString();
    final errorKey = '${error.runtimeType}:${errorString.length > 100 ? errorString.substring(0, 100) : errorString}';
    _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;

    final errorEntry = {
      'timestamp': DateTime.now().toIso8601String(),
      'level': level.name,
      'tag': tag,
      'error': error.toString(),
      'type': error.runtimeType.toString(),
      'count': _errorCounts[errorKey],
      if (stackTrace != null) 'stackTrace': stackTrace.toString(),
    };

    _recentErrors.add(errorEntry);

    // Keep only recent errors
    if (_recentErrors.length > _maxRecentErrors) {
      _recentErrors.removeAt(0);
    }
  }

  /// Get ANSI color code for log level
  String _getColorCode(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '\x1B[36m'; // Cyan
      case LogLevel.info:
        return '\x1B[32m'; // Green
      case LogLevel.warning:
        return '\x1B[33m'; // Yellow
      case LogLevel.error:
        return '\x1B[31m'; // Red
      case LogLevel.critical:
        return '\x1B[35m'; // Magenta
    }
  }

  /// Write log entry to file
  void _writeToLogFile(String message, Object? error, StackTrace? stackTrace) {
    try {
      final file = File(_logFilePath!);
      final logEntry = {
        'message': message,
        if (error != null) 'error': error.toString(),
        if (stackTrace != null) 'stackTrace': stackTrace.toString(),
      };
      
      file.writeAsStringSync(
        '${jsonEncode(logEntry)}\n',
        mode: FileMode.append,
      );
    } catch (e) {
      // Don't throw - logging shouldn't break the application
      print('Failed to write to log file: $e');
    }
  }

  /// Log authentication events
  static void auth(String message, {String? userId, Object? error}) {
    info(message, tag: 'Auth', error: error);
  }

  /// Log API events
  static void api(String message, {String? endpoint, int? statusCode, Object? error}) {
    final statusStr = statusCode != null ? ' [$statusCode]' : '';
    info('$message$statusStr', tag: 'API', error: error);
  }

  /// Log database events
  static void database(String message, {String? query, Object? error}) {
    info(message, tag: 'Database', error: error);
  }

  /// Log business logic events
  static void business(String message, {String? feature, Object? error}) {
    info(message, tag: feature ?? 'Business', error: error);
  }

  /// Log performance events
  static void performance(String message, {Duration? duration}) {
    final durationStr = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    info('$message$durationStr', tag: 'Performance');
  }

  /// Log security events
  static void security(String message, {String? userId, String? ipAddress, Object? error}) {
    final contextStr = userId != null || ipAddress != null 
        ? ' [User: $userId, IP: $ipAddress]' 
        : '';
    warning('$message$contextStr', tag: 'Security', error: error);
  }

  /// Log cache events
  static void cache(String message, {String? key, Object? error}) {
    debug(message, tag: 'Cache', error: error);
  }

  /// Log email events
  static void email(String message, {String? recipient, Object? error}) {
    info(message, tag: 'Email', error: error);
  }

  /// Log export events
  static void export(String message, {String? exportId, Object? error}) {
    info(message, tag: 'Export', error: error);
  }

  /// Log MFA events
  static void mfa(String message, {String? userId, Object? error}) {
    info(message, tag: 'MFA', error: error);
  }

  /// Log analytics events
  static void analytics(String message, {String? eventType, Object? error}) {
    debug(message, tag: 'Analytics', error: error);
  }

  /// Get error statistics for monitoring
  static Map<String, dynamic> getErrorStats() {
    final now = DateTime.now();
    final recentErrorsLast24h = _recentErrors.where((error) {
      final errorTime = DateTime.parse(error['timestamp'] as String);
      return now.difference(errorTime).inHours < 24;
    }).toList();

    final errorsByType = <String, int>{};
    for (final error in recentErrorsLast24h) {
      final type = error['type'] as String;
      errorsByType[type] = (errorsByType[type] ?? 0) + 1;
    }

    return {
      'totalErrors': _errorCounts.values.fold(0, (sum, count) => sum + count),
      'uniqueErrors': _errorCounts.length,
      'recentErrors24h': recentErrorsLast24h.length,
      'errorsByType': errorsByType,
      'mostFrequentErrors': _getMostFrequentErrors(),
      'lastUpdated': now.toIso8601String(),
    };
  }

  /// Get most frequent errors
  static List<Map<String, dynamic>> _getMostFrequentErrors({int limit = 10}) {
    final sortedErrors = _errorCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedErrors.take(limit).map((entry) => {
      'error': entry.key,
      'count': entry.value,
    }).toList();
  }

  /// Get recent errors
  static List<Map<String, dynamic>> getRecentErrors({int limit = 50}) {
    final errors = _recentErrors.reversed.take(limit).toList();
    return errors;
  }

  /// Clear error statistics (useful for testing or periodic cleanup)
  static void clearErrorStats() {
    _errorCounts.clear();
    _recentErrors.clear();
  }

  /// Log structured data with additional context
  static void structured(
    LogLevel level,
    String message, {
    String? tag,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  }) {
    final structuredMessage = data != null
        ? '$message | Data: ${jsonEncode(data)}'
        : message;

    _instance._log(level, structuredMessage, tag: tag, error: error, stackTrace: stackTrace);
  }
}

/// Extension for easier logging from any class
extension LoggingExtension on Object {
  void logDebug(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.debug(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logInfo(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.info(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logWarning(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.warning(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.error(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logCritical(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.critical(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
}
