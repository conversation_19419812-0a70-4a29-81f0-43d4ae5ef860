-- Authentication Security Schema for Quester platform
-- Contains tables for user sessions, MFA, and security audit logging

-- Enhanced user sessions table with security features
CREATE TABLE IF NOT EXISTS quester.user_sessions_enhanced (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    session_token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMPTZ NOT NULL,
    absolute_expires_at TIMESTAMPTZ NOT NULL,
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    location_info JSONB DEFAULT '{}',
    security_flags JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- MFA settings table
CREATE TABLE IF NOT EXISTS quester.user_mfa_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    method VARCHAR(20) NOT NULL, -- 'totp', 'sms', 'email'
    secret_encrypted TEXT, -- Encrypted TOTP secret or phone/email
    backup_codes_encrypted TEXT[], -- Encrypted backup codes
    is_enabled BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    phone_number_encrypted TEXT, -- For SMS MFA
    email_address VARCHAR(255), -- For email MFA
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, method)
);

-- MFA verification attempts
CREATE TABLE IF NOT EXISTS quester.mfa_verification_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES quester.user_sessions_enhanced(id) ON DELETE CASCADE,
    method VARCHAR(20) NOT NULL,
    code_provided VARCHAR(20),
    success BOOLEAN NOT NULL,
    ip_address INET,
    user_agent TEXT,
    error_message TEXT,
    attempted_at TIMESTAMPTZ DEFAULT NOW()
);

-- Security audit logs table (comprehensive logging)
CREATE TABLE IF NOT EXISTS quester.security_audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL, -- 'authentication', 'authorization', 'data_access', 'admin_action', 'security_event'
    user_id UUID, -- Can be null for anonymous events
    organization_id UUID REFERENCES quester.organizations(id) ON DELETE SET NULL,
    session_id UUID,
    action VARCHAR(255) NOT NULL,
    success BOOLEAN NOT NULL,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    error_message TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS quester.rate_limit_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identifier VARCHAR(255) NOT NULL, -- IP address or user ID
    action VARCHAR(100) NOT NULL, -- 'login', 'api_call', etc.
    window_start TIMESTAMPTZ NOT NULL,
    attempt_count INTEGER DEFAULT 1,
    is_blocked BOOLEAN DEFAULT false,
    blocked_until TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(identifier, action, window_start)
);

-- CSRF tokens table
CREATE TABLE IF NOT EXISTS quester.csrf_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_hash VARCHAR(255) NOT NULL,
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES quester.user_sessions_enhanced(id) ON DELETE CASCADE,
    expires_at TIMESTAMPTZ NOT NULL,
    is_used BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trusted devices table
CREATE TABLE IF NOT EXISTS quester.trusted_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    device_fingerprint VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    device_type VARCHAR(50), -- 'mobile', 'desktop', 'tablet'
    browser_info JSONB DEFAULT '{}',
    is_trusted BOOLEAN DEFAULT false,
    trust_expires_at TIMESTAMPTZ,
    last_seen_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, device_fingerprint)
);

-- Password history table (prevent password reuse)
CREATE TABLE IF NOT EXISTS quester.password_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES quester.users(id) ON DELETE CASCADE,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON quester.user_sessions_enhanced(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON quester.user_sessions_enhanced(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON quester.user_sessions_enhanced(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON quester.user_sessions_enhanced(session_token_hash);

CREATE INDEX IF NOT EXISTS idx_mfa_settings_user_id ON quester.user_mfa_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_settings_method ON quester.user_mfa_settings(method);
CREATE INDEX IF NOT EXISTS idx_mfa_settings_enabled ON quester.user_mfa_settings(is_enabled);

CREATE INDEX IF NOT EXISTS idx_mfa_attempts_user_id ON quester.mfa_verification_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_attempts_session_id ON quester.mfa_verification_attempts(session_id);
CREATE INDEX IF NOT EXISTS idx_mfa_attempts_timestamp ON quester.mfa_verification_attempts(attempted_at);

CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON quester.security_audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON quester.security_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON quester.security_audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_success ON quester.security_audit_logs(success);

CREATE INDEX IF NOT EXISTS idx_rate_limit_identifier ON quester.rate_limit_records(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limit_action ON quester.rate_limit_records(action);
CREATE INDEX IF NOT EXISTS idx_rate_limit_window ON quester.rate_limit_records(window_start);

CREATE INDEX IF NOT EXISTS idx_csrf_tokens_hash ON quester.csrf_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_csrf_tokens_user_id ON quester.csrf_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_csrf_tokens_expires ON quester.csrf_tokens(expires_at);

CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_id ON quester.trusted_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_trusted_devices_fingerprint ON quester.trusted_devices(device_fingerprint);

CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON quester.password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_created ON quester.password_history(created_at);

-- Grant permissions
GRANT ALL PRIVILEGES ON quester.user_sessions_enhanced TO quester;
GRANT ALL PRIVILEGES ON quester.user_mfa_settings TO quester;
GRANT ALL PRIVILEGES ON quester.mfa_verification_attempts TO quester;
GRANT ALL PRIVILEGES ON quester.security_audit_logs TO quester;
GRANT ALL PRIVILEGES ON quester.rate_limit_records TO quester;
GRANT ALL PRIVILEGES ON quester.csrf_tokens TO quester;
GRANT ALL PRIVILEGES ON quester.trusted_devices TO quester;
GRANT ALL PRIVILEGES ON quester.password_history TO quester;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Authentication security schema created successfully!';
    RAISE NOTICE 'Tables created: user_sessions_enhanced, user_mfa_settings, mfa_verification_attempts, security_audit_logs, rate_limit_records, csrf_tokens, trusted_devices, password_history';
    RAISE NOTICE 'Indexes and permissions configured for optimal security and performance';
END $$;
