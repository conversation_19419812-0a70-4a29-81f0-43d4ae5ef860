// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_behavior_analytics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserBehaviorAnalytics _$UserBehaviorAnalyticsFromJson(
  Map<String, dynamic> json,
) => UserBehaviorAnalytics(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  userId: json['userId'] as String,
  analysisDate: DateTime.parse(json['analysisDate'] as String),
  sessionCount: (json['sessionCount'] as num).toInt(),
  pageViews: (json['pageViews'] as num).toInt(),
  actionsCount: (json['actionsCount'] as num).toInt(),
  timeSpentSeconds: (json['timeSpentSeconds'] as num).toInt(),
  featuresUsed: (json['featuresUsed'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  engagementScore: (json['engagementScore'] as num).toDouble(),
  behaviorPatterns: json['behaviorPatterns'] as Map<String, dynamic>,
  cohortData: json['cohortData'] as Map<String, dynamic>,
  retentionMetrics: json['retentionMetrics'] as Map<String, dynamic>,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$UserBehaviorAnalyticsToJson(
  UserBehaviorAnalytics instance,
) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'userId': instance.userId,
  'analysisDate': instance.analysisDate.toIso8601String(),
  'sessionCount': instance.sessionCount,
  'pageViews': instance.pageViews,
  'actionsCount': instance.actionsCount,
  'timeSpentSeconds': instance.timeSpentSeconds,
  'featuresUsed': instance.featuresUsed,
  'engagementScore': instance.engagementScore,
  'behaviorPatterns': instance.behaviorPatterns,
  'cohortData': instance.cohortData,
  'retentionMetrics': instance.retentionMetrics,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

EngagementScore _$EngagementScoreFromJson(Map<String, dynamic> json) =>
    EngagementScore(
      userId: json['userId'] as String,
      organizationId: json['organizationId'] as String,
      overallScore: (json['overallScore'] as num).toDouble(),
      components: EngagementComponents.fromJson(
        json['components'] as Map<String, dynamic>,
      ),
      timeRange: DateTimeRange.fromJson(
        json['timeRange'] as Map<String, dynamic>,
      ),
      positiveFactors: (json['positiveFactors'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      negativeFactors: (json['negativeFactors'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
    );

Map<String, dynamic> _$EngagementScoreToJson(EngagementScore instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'organizationId': instance.organizationId,
      'overallScore': instance.overallScore,
      'components': instance.components,
      'timeRange': instance.timeRange,
      'positiveFactors': instance.positiveFactors,
      'negativeFactors': instance.negativeFactors,
      'recommendations': instance.recommendations,
      'calculatedAt': instance.calculatedAt.toIso8601String(),
    };

EngagementComponents _$EngagementComponentsFromJson(
  Map<String, dynamic> json,
) => EngagementComponents(
  frequency: (json['frequency'] as num).toDouble(),
  depth: (json['depth'] as num).toDouble(),
  breadth: (json['breadth'] as num).toDouble(),
  consistency: (json['consistency'] as num).toDouble(),
  gamification: (json['gamification'] as num).toDouble(),
  collaboration: (json['collaboration'] as num).toDouble(),
);

Map<String, dynamic> _$EngagementComponentsToJson(
  EngagementComponents instance,
) => <String, dynamic>{
  'frequency': instance.frequency,
  'depth': instance.depth,
  'breadth': instance.breadth,
  'consistency': instance.consistency,
  'gamification': instance.gamification,
  'collaboration': instance.collaboration,
};

CohortAnalysis _$CohortAnalysisFromJson(Map<String, dynamic> json) =>
    CohortAnalysis(
      organizationId: json['organizationId'] as String,
      cohortDefinition: json['cohortDefinition'] as String,
      cohortPeriod: json['cohortPeriod'] as String,
      initialSize: (json['initialSize'] as num).toInt(),
      retentionData: (json['retentionData'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry(k, CohortPeriodData.fromJson(e as Map<String, dynamic>)),
      ),
      analyzedAt: DateTime.parse(json['analyzedAt'] as String),
    );

Map<String, dynamic> _$CohortAnalysisToJson(CohortAnalysis instance) =>
    <String, dynamic>{
      'organizationId': instance.organizationId,
      'cohortDefinition': instance.cohortDefinition,
      'cohortPeriod': instance.cohortPeriod,
      'initialSize': instance.initialSize,
      'retentionData': instance.retentionData,
      'analyzedAt': instance.analyzedAt.toIso8601String(),
    };

CohortPeriodData _$CohortPeriodDataFromJson(Map<String, dynamic> json) =>
    CohortPeriodData(
      periodNumber: (json['periodNumber'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      retentionRate: (json['retentionRate'] as num).toDouble(),
      avgEngagementScore: (json['avgEngagementScore'] as num).toDouble(),
    );

Map<String, dynamic> _$CohortPeriodDataToJson(CohortPeriodData instance) =>
    <String, dynamic>{
      'periodNumber': instance.periodNumber,
      'activeUsers': instance.activeUsers,
      'retentionRate': instance.retentionRate,
      'avgEngagementScore': instance.avgEngagementScore,
    };

UserJourney _$UserJourneyFromJson(Map<String, dynamic> json) => UserJourney(
  userId: json['userId'] as String,
  organizationId: json['organizationId'] as String,
  startTime: DateTime.parse(json['startTime'] as String),
  endTime: DateTime.parse(json['endTime'] as String),
  steps: (json['steps'] as List<dynamic>)
      .map((e) => JourneyStep.fromJson(e as Map<String, dynamic>))
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>,
);

Map<String, dynamic> _$UserJourneyToJson(UserJourney instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'organizationId': instance.organizationId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'steps': instance.steps,
      'metadata': instance.metadata,
    };

JourneyStep _$JourneyStepFromJson(Map<String, dynamic> json) => JourneyStep(
  sequence: (json['sequence'] as num).toInt(),
  eventType: json['eventType'] as String,
  eventName: json['eventName'] as String,
  page: json['page'] as String?,
  timestamp: DateTime.parse(json['timestamp'] as String),
  duration: (json['duration'] as num?)?.toInt(),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$JourneyStepToJson(JourneyStep instance) =>
    <String, dynamic>{
      'sequence': instance.sequence,
      'eventType': instance.eventType,
      'eventName': instance.eventName,
      'page': instance.page,
      'timestamp': instance.timestamp.toIso8601String(),
      'duration': instance.duration,
      'metadata': instance.metadata,
    };
