import 'dart:io';
import 'dart:math';
import 'package:postgres/postgres.dart';

/// Test database service for integration tests
/// Provides isolated test database instances with seeding and cleanup
class TestDatabaseService {
  static final Map<String, TestDatabaseService> _instances = {};
  
  Connection? _connection;
  bool _isConnected = false;
  late String _testDatabaseName;
  final String _testId;
  
  // Test database configuration
  static String get _host => Platform.environment['TEST_POSTGRES_HOST'] ?? 'localhost';
  static int get _port => int.parse(Platform.environment['TEST_POSTGRES_PORT'] ?? '5432');
  static String get _username => Platform.environment['TEST_POSTGRES_USER'] ?? 'quester';
  static String get _password => Platform.environment['TEST_POSTGRES_PASSWORD'] ?? 'questerpass';
  static String get _templateDatabase => Platform.environment['TEST_POSTGRES_TEMPLATE'] ?? 'questerdb';

  TestDatabaseService._(this._testId) {
    _testDatabaseName = 'test_quester_${_testId}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Create a new test database instance
  static Future<TestDatabaseService> create({String? testId}) async {
    final id = testId ?? 'test_${Random().nextInt(10000)}';
    
    if (_instances.containsKey(id)) {
      return _instances[id]!;
    }
    
    final instance = TestDatabaseService._(id);
    await instance._initialize();
    _instances[id] = instance;
    
    return instance;
  }

  /// Initialize test database
  Future<void> _initialize() async {
    try {
      print('🔧 Creating test database: $_testDatabaseName');
      
      // Connect to template database to create test database
      final adminConnection = await Connection.open(
        Endpoint(
          host: _host,
          port: _port,
          database: 'postgres', // Connect to postgres database to create new DB
          username: _username,
          password: _password,
        ),
        settings: ConnectionSettings(
          sslMode: SslMode.disable,
          connectTimeout: const Duration(seconds: 10),
          queryTimeout: const Duration(seconds: 30),
        ),
      );

      // Create test database from template
      await adminConnection.execute(
        'CREATE DATABASE $_testDatabaseName WITH TEMPLATE $_templateDatabase'
      );
      
      await adminConnection.close();
      
      // Connect to the new test database
      _connection = await Connection.open(
        Endpoint(
          host: _host,
          port: _port,
          database: _testDatabaseName,
          username: _username,
          password: _password,
        ),
        settings: ConnectionSettings(
          sslMode: SslMode.disable,
          connectTimeout: const Duration(seconds: 10),
          queryTimeout: const Duration(seconds: 30),
          applicationName: 'Quester-Test-$_testId',
        ),
      );
      
      _isConnected = true;
      print('✅ Test database created and connected: $_testDatabaseName');
      
      // Seed test data
      await _seedTestData();
      
    } catch (e) {
      print('❌ Failed to initialize test database: $e');
      rethrow;
    }
  }

  /// Seed test database with sample data
  Future<void> _seedTestData() async {
    if (!_isConnected) throw Exception('Test database not connected');
    
    try {
      print('🌱 Seeding test database with sample data...');
      
      // Clear existing data first
      await _clearTestData();
      
      // Insert test users
      final testUsers = await _insertTestUsers();
      
      // Insert test achievements
      await _insertTestAchievements();
      
      // Insert test user points and stats
      await _insertTestUserStats(testUsers);
      
      // Insert test activity logs
      await _insertTestActivityLogs(testUsers);
      
      // Insert test leaderboards
      await _insertTestLeaderboards(testUsers);
      
      print('✅ Test database seeded successfully');
      
    } catch (e) {
      print('❌ Failed to seed test database: $e');
      rethrow;
    }
  }

  /// Clear all test data
  Future<void> _clearTestData() async {
    final tables = [
      'quester.activity_log',
      'quester.user_achievements', 
      'quester.leaderboards',
      'quester.streaks',
      'quester.user_points',
      'quester.achievements',
      'quester.users',
    ];
    
    for (final table in tables) {
      await _connection!.execute('DELETE FROM $table');
    }
  }

  /// Insert test users
  Future<List<String>> _insertTestUsers() async {
    final userIds = <String>[];
    
    final testUsers = [
      {'username': 'test_alice', 'email': '<EMAIL>', 'display_name': 'Test Alice'},
      {'username': 'test_bob', 'email': '<EMAIL>', 'display_name': 'Test Bob'},
      {'username': 'test_charlie', 'email': '<EMAIL>', 'display_name': 'Test Charlie'},
      {'username': 'test_diana', 'email': '<EMAIL>', 'display_name': 'Test Diana'},
      {'username': 'test_eve', 'email': '<EMAIL>', 'display_name': 'Test Eve'},
    ];
    
    for (final user in testUsers) {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.users (username, email, display_name, is_active)
          VALUES (@username, @email, @displayName, true)
          RETURNING id
        '''),
        parameters: {
          'username': user['username'],
          'email': user['email'],
          'displayName': user['display_name'],
        },
      );
      
      userIds.add(result.first[0].toString());
    }
    
    return userIds;
  }

  /// Insert test achievements
  Future<void> _insertTestAchievements() async {
    final achievements = [
      {'name': 'Test First Quest', 'description': 'Complete first test quest', 'category': 'Testing', 'rarity': 'Common', 'points': 50},
      {'name': 'Test Team Player', 'description': 'Test collaboration', 'category': 'Testing', 'rarity': 'Uncommon', 'points': 150},
      {'name': 'Test Streak Master', 'description': 'Test activity streak', 'category': 'Testing', 'rarity': 'Rare', 'points': 400},
    ];
    
    for (final achievement in achievements) {
      await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.achievements (name, description, category, rarity, points_reward)
          VALUES (@name, @description, @category, @rarity::achievement_rarity, @points)
        '''),
        parameters: achievement,
      );
    }
  }

  /// Insert test user stats
  Future<void> _insertTestUserStats(List<String> userIds) async {
    for (int i = 0; i < userIds.length; i++) {
      final userId = userIds[i];
      final points = (i + 1) * 100 + Random().nextInt(500);
      final level = (points / 100).floor() + 1;
      
      // Insert user points
      await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.user_points (user_id, total_points, current_level, role)
          VALUES (@userId::uuid, @points, @level, 'adventurer'::user_role)
        '''),
        parameters: {
          'userId': userId,
          'points': points,
          'level': level,
        },
      );
      
      // Insert streak data
      await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.streaks (user_id, current_streak, longest_streak, last_activity_date)
          VALUES (@userId::uuid, @current, @longest, @lastActivity)
        '''),
        parameters: {
          'userId': userId,
          'current': Random().nextInt(10) + 1,
          'longest': Random().nextInt(20) + 5,
          'lastActivity': DateTime.now().subtract(Duration(days: Random().nextInt(3))).toIso8601String(),
        },
      );
    }
  }

  /// Insert test activity logs
  Future<void> _insertTestActivityLogs(List<String> userIds) async {
    final activities = ['quest_completed', 'achievement_earned', 'collaboration_joined', 'streak_maintained'];
    
    for (final userId in userIds) {
      for (int i = 0; i < 5; i++) {
        await _connection!.execute(
          Sql.named('''
            INSERT INTO quester.activity_log (user_id, activity_type, points_earned, description, created_at)
            VALUES (@userId::uuid, @activityType::activity_type, @points, @description, @createdAt)
          '''),
          parameters: {
            'userId': userId,
            'activityType': activities[Random().nextInt(activities.length)],
            'points': Random().nextInt(100) + 10,
            'description': 'Test activity ${i + 1}',
            'createdAt': DateTime.now().subtract(Duration(hours: Random().nextInt(72))).toIso8601String(),
          },
        );
      }
    }
  }

  /// Insert test leaderboards
  Future<void> _insertTestLeaderboards(List<String> userIds) async {
    final leaderboardTypes = ['weekly_points', 'monthly_points', 'all_time_points'];
    
    for (final type in leaderboardTypes) {
      for (int i = 0; i < userIds.length; i++) {
        await _connection!.execute(
          Sql.named('''
            INSERT INTO quester.leaderboards (user_id, leaderboard_type, rank, score)
            VALUES (@userId::uuid, @type::leaderboard_type, @rank, @score)
          '''),
          parameters: {
            'userId': userIds[i],
            'type': type,
            'rank': i + 1,
            'score': (userIds.length - i) * 100 + Random().nextInt(50),
          },
        );
      }
    }
  }

  /// Execute query on test database
  Future<Result> execute(String sql, {Map<String, dynamic>? parameters}) async {
    if (!_isConnected) throw Exception('Test database not connected');
    
    if (parameters != null && parameters.isNotEmpty) {
      return await _connection!.execute(Sql.named(sql), parameters: parameters);
    } else {
      return await _connection!.execute(sql);
    }
  }

  /// Get test database connection
  Connection get connection {
    if (!_isConnected) throw Exception('Test database not connected');
    return _connection!;
  }

  /// Check if test database is connected
  bool get isConnected => _isConnected;

  /// Get test database name
  String get databaseName => _testDatabaseName;

  /// Cleanup test database
  Future<void> cleanup() async {
    try {
      if (_isConnected && _connection != null) {
        await _connection!.close();
        _isConnected = false;
      }
      
      // Connect to postgres database to drop test database
      final adminConnection = await Connection.open(
        Endpoint(
          host: _host,
          port: _port,
          database: 'postgres',
          username: _username,
          password: _password,
        ),
        settings: ConnectionSettings(
          sslMode: SslMode.disable,
          connectTimeout: const Duration(seconds: 10),
        ),
      );

      // Terminate connections to test database
      await adminConnection.execute('''
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = '$_testDatabaseName' AND pid <> pg_backend_pid()
      ''');

      // Drop test database
      await adminConnection.execute('DROP DATABASE IF EXISTS $_testDatabaseName');
      await adminConnection.close();
      
      print('🧹 Test database cleaned up: $_testDatabaseName');
      
    } catch (e) {
      print('⚠️ Error cleaning up test database: $e');
    }
    
    _instances.remove(_testId);
  }

  /// Cleanup all test databases
  static Future<void> cleanupAll() async {
    final futures = _instances.values.map((instance) => instance.cleanup());
    await Future.wait(futures);
    _instances.clear();
  }
}
