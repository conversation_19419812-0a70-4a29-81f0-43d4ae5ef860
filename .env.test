# Test Environment Configuration for Quest<PERSON>
# This file contains environment variables specifically for testing

# Database Configuration
TEST_POSTGRES_HOST=localhost
TEST_POSTGRES_PORT=5433
TEST_POSTGRES_USER=quester
TEST_POSTGRES_PASSWORD=questerpass
TEST_POSTGRES_DB=questerdb
TEST_POSTGRES_TEMPLATE=questerdb

# Performance Test Database (separate instance)
PERF_TEST_POSTGRES_HOST=localhost
PERF_TEST_POSTGRES_PORT=5434
PERF_TEST_POSTGRES_USER=quester_perf
PERF_TEST_POSTGRES_PASSWORD=questerpass_perf
PERF_TEST_POSTGRES_DB=questerdb_perf

# Redis Configuration
TEST_REDIS_HOST=localhost
TEST_REDIS_PORT=6380
TEST_REDIS_PASSWORD=

# Application Configuration
DART_ENV=test
LOG_LEVEL=info
DEBUG_MODE=true

# Test Execution Configuration
DART_TEST_CONCURRENCY=4
TEST_TIMEOUT_SECONDS=30
INTEGRATION_TEST_TIMEOUT_SECONDS=60
PERFORMANCE_TEST_TIMEOUT_SECONDS=120

# Security Configuration (test values)
JWT_SECRET=test_jwt_secret_key_for_testing_only
ENCRYPTION_KEY=test_encryption_key_for_testing_only
API_KEY=test_api_key_for_testing_only

# Email Service Configuration (test/mock)
EMAIL_PROVIDER=mock
SENDGRID_API_KEY=test_sendgrid_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Quester Test

# SMS Service Configuration (test/mock)
SMS_PROVIDER=mock
TWILIO_ACCOUNT_SID=test_twilio_sid
TWILIO_AUTH_TOKEN=test_twilio_token
TWILIO_FROM_PHONE=+**********

# Feature Flags for Testing
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_QUERY_OPTIMIZATION=true
ENABLE_CACHE_OPTIMIZATION=true
ENABLE_SECURITY_MONITORING=true
ENABLE_AUDIT_LOGGING=true

# Test Data Configuration
SEED_TEST_DATA=true
TEST_USER_COUNT=10
TEST_ACHIEVEMENT_COUNT=20
TEST_ACTIVITY_COUNT=100

# Performance Test Configuration
PERFORMANCE_TEST_DURATION_SECONDS=60
PERFORMANCE_TEST_CONCURRENT_USERS=10
PERFORMANCE_TEST_REQUESTS_PER_SECOND=50
PERFORMANCE_THRESHOLD_MS=50

# Coverage Configuration
COVERAGE_THRESHOLD=80
COVERAGE_EXCLUDE_PATTERNS=test/**,**/*.g.dart,**/*.freezed.dart

# Docker Configuration
COMPOSE_PROJECT_NAME=quester_test
COMPOSE_FILE=app/docker-compose.test.yml
