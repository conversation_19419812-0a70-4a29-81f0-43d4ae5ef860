// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Streak _$StreakFromJson(Map<String, dynamic> json) => Streak(
  id: json['id'] as String,
  userId: json['userId'] as String,
  type: $enumDecode(_$StreakTypeEnumMap, json['type']),
  currentStreak: (json['currentStreak'] as num).toInt(),
  longestStreak: (json['longestStreak'] as num).toInt(),
  totalCount: (json['totalCount'] as num).toInt(),
  status: $enumDecode(_$StreakStatusEnumMap, json['status']),
  lastActivityDate: DateTime.parse(json['lastActivityDate'] as String),
  streakStartDate: DateTime.parse(json['streakStartDate'] as String),
  targetGoal: (json['targetGoal'] as num?)?.toInt(),
  activityHistory: Map<String, bool>.from(json['activityHistory'] as Map),
  multiplierBonus: (json['multiplierBonus'] as num).toDouble(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$StreakToJson(Streak instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'type': _$StreakTypeEnumMap[instance.type]!,
  'currentStreak': instance.currentStreak,
  'longestStreak': instance.longestStreak,
  'totalCount': instance.totalCount,
  'status': _$StreakStatusEnumMap[instance.status]!,
  'lastActivityDate': instance.lastActivityDate.toIso8601String(),
  'streakStartDate': instance.streakStartDate.toIso8601String(),
  'targetGoal': instance.targetGoal,
  'activityHistory': instance.activityHistory,
  'multiplierBonus': instance.multiplierBonus,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$StreakTypeEnumMap = {
  StreakType.dailyLogin: 'daily_login',
  StreakType.dailyQuest: 'daily_quest',
  StreakType.dailyTask: 'daily_task',
  StreakType.weeklyGoal: 'weekly_goal',
  StreakType.monthlyChallenge: 'monthly_challenge',
  StreakType.custom: 'custom',
};

const _$StreakStatusEnumMap = {
  StreakStatus.active: 'active',
  StreakStatus.broken: 'broken',
  StreakStatus.paused: 'paused',
  StreakStatus.completed: 'completed',
};
