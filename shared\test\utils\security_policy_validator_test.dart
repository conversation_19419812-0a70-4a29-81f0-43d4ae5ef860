import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('SecurityPolicyValidator Tests', () {
    late OrganizationSecurityPolicy validPolicy;

    setUp(() {
      // Create a valid baseline policy using the factory
      validPolicy = OrganizationSecurityPolicy.empty();
    });

    group('Basic Validation', () {
      test('should validate empty policy successfully', () {
        final result = SecurityPolicyValidator.validateOrganizationSecurityPolicy(validPolicy);
        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should create successful validation result', () {
        final result = ValidationUtils.valid();
        expect(result.isValid, isTrue);
      });

      test('should create failed validation result', () {
        final result = ValidationUtils.invalid('Test error');
        expect(result.isValid, isFalse);
        expect(result.message, contains('Test error'));
      });
    });

    group('Policy Validation Methods', () {
      test('should validate password', () {
        final result = SecurityPolicyValidator.validatePassword('TestPass123!', validPolicy.passwordPolicy);
        expect(result.isValid, isTrue);
      });

      test('should validate MFA policy', () {
        final result = SecurityPolicyValidator.validateMFAPolicy(validPolicy.mfaPolicy);
        expect(result.isValid, isTrue);
      });

      test('should validate session policy', () {
        final result = SecurityPolicyValidator.validateSessionPolicy(validPolicy.sessionPolicy);
        expect(result.isValid, isTrue);
      });

      test('should validate organization policy', () {
        final result = SecurityPolicyValidator.validateOrganizationSecurityPolicy(validPolicy);
        expect(result.isValid, isTrue);
      });

      test('should validate password strength', () {
        final result = SecurityPolicyValidator.validatePasswordStrength('StrongPassword123!');
        expect(result.isValid, isTrue);
      });
    });
  });
}
