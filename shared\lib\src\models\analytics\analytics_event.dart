import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'analytics_event.g.dart';

/// Analytics event types for categorizing user interactions
enum AnalyticsEventType {
  @JsonValue('page_view')
  pageView,
  @JsonValue('button_click')
  buttonClick,
  @JsonValue('task_action')
  taskAction,
  @JsonValue('quest_action')
  questAction,
  @JsonValue('achievement_unlock')
  achievementUnlock,
  @JsonValue('reward_purchase')
  rewardPurchase,
  @JsonValue('login')
  login,
  @JsonValue('logout')
  logout,
  @JsonValue('search')
  search,
  @JsonValue('export')
  export,
  @JsonValue('report_generate')
  reportGenerate,
  @JsonValue('dashboard_view')
  dashboardView,
  @JsonValue('collaboration_action')
  collaborationAction,
  @JsonValue('gamification_interaction')
  gamificationInteraction,
  @JsonValue('enterprise_action')
  enterpriseAction,
  @JsonValue('api_call')
  apiCall,
}

/// Core analytics event model for tracking user interactions
@JsonSerializable()
class AnalyticsEvent extends Equatable {
  /// Unique event identifier
  final String id;

  /// Organization identifier
  final String organizationId;

  /// User identifier (null for anonymous events)
  final String? userId;

  /// Session identifier for grouping events
  final String? sessionId;

  /// Type of event
  final AnalyticsEventType eventType;

  /// Human-readable event name
  final String eventName;

  /// Event-specific data payload
  final Map<String, dynamic> eventData;

  /// Additional event properties and metadata
  final Map<String, dynamic>? eventProperties;

  /// User agent string from the client
  final String? userAgent;

  /// IP address of the client
  final String? ipAddress;

  /// Referrer URL
  final String? referrer;

  /// Current page/screen URL
  final String? pageUrl;

  /// Event timestamp
  final DateTime timestamp;

  /// Creation timestamp
  final DateTime createdAt;

  const AnalyticsEvent({
    required this.id,
    required this.organizationId,
    this.userId,
    this.sessionId,
    required this.eventType,
    required this.eventName,
    required this.eventData,
    this.eventProperties,
    this.userAgent,
    this.ipAddress,
    this.referrer,
    this.pageUrl,
    required this.timestamp,
    required this.createdAt,
  });

  /// Create AnalyticsEvent from JSON
  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsEventFromJson(json);

  /// Convert AnalyticsEvent to JSON
  Map<String, dynamic> toJson() => _$AnalyticsEventToJson(this);

  /// Create a copy with updated fields
  AnalyticsEvent copyWith({
    String? id,
    String? organizationId,
    String? userId,
    String? sessionId,
    AnalyticsEventType? eventType,
    String? eventName,
    Map<String, dynamic>? eventData,
    Map<String, dynamic>? eventProperties,
    String? userAgent,
    String? ipAddress,
    String? referrer,
    String? pageUrl,
    DateTime? timestamp,
    DateTime? createdAt,
  }) {
    return AnalyticsEvent(
      id: id ?? this.id,
      organizationId: organizationId ?? this.organizationId,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
      eventType: eventType ?? this.eventType,
      eventName: eventName ?? this.eventName,
      eventData: eventData ?? this.eventData,
      eventProperties: eventProperties ?? this.eventProperties,
      userAgent: userAgent ?? this.userAgent,
      ipAddress: ipAddress ?? this.ipAddress,
      referrer: referrer ?? this.referrer,
      pageUrl: pageUrl ?? this.pageUrl,
      timestamp: timestamp ?? this.timestamp,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Create a new analytics event with current timestamp
  factory AnalyticsEvent.create({
    required String organizationId,
    String? userId,
    String? sessionId,
    required AnalyticsEventType eventType,
    required String eventName,
    Map<String, dynamic>? eventData,
    Map<String, dynamic>? eventProperties,
    String? userAgent,
    String? ipAddress,
    String? referrer,
    String? pageUrl,
  }) {
    final now = DateTime.now().toUtc();
    return AnalyticsEvent(
      id: '', // Will be generated by database
      organizationId: organizationId,
      userId: userId,
      sessionId: sessionId,
      eventType: eventType,
      eventName: eventName,
      eventData: eventData ?? {},
      eventProperties: eventProperties,
      userAgent: userAgent,
      ipAddress: ipAddress,
      referrer: referrer,
      pageUrl: pageUrl,
      timestamp: now,
      createdAt: now,
    );
  }

  /// Get event category for grouping
  String get eventCategory {
    switch (eventType) {
      case AnalyticsEventType.pageView:
      case AnalyticsEventType.dashboardView:
        return 'navigation';
      case AnalyticsEventType.buttonClick:
      case AnalyticsEventType.search:
        return 'interaction';
      case AnalyticsEventType.taskAction:
      case AnalyticsEventType.questAction:
        return 'productivity';
      case AnalyticsEventType.achievementUnlock:
      case AnalyticsEventType.rewardPurchase:
      case AnalyticsEventType.gamificationInteraction:
        return 'gamification';
      case AnalyticsEventType.login:
      case AnalyticsEventType.logout:
        return 'authentication';
      case AnalyticsEventType.export:
      case AnalyticsEventType.reportGenerate:
        return 'analytics';
      case AnalyticsEventType.collaborationAction:
        return 'collaboration';
      case AnalyticsEventType.enterpriseAction:
        return 'enterprise';
      case AnalyticsEventType.apiCall:
        return 'system';
    }
  }

  /// Check if this event represents a conversion action
  bool get isConversionEvent {
    return [
      AnalyticsEventType.taskAction,
      AnalyticsEventType.questAction,
      AnalyticsEventType.achievementUnlock,
      AnalyticsEventType.collaborationAction,
    ].contains(eventType);
  }

  /// Check if this event should be included in engagement calculations
  bool get isEngagementEvent {
    return [
      AnalyticsEventType.taskAction,
      AnalyticsEventType.questAction,
      AnalyticsEventType.achievementUnlock,
      AnalyticsEventType.rewardPurchase,
      AnalyticsEventType.collaborationAction,
      AnalyticsEventType.gamificationInteraction,
      AnalyticsEventType.search,
      AnalyticsEventType.reportGenerate,
      AnalyticsEventType.export,
    ].contains(eventType);
  }

  @override
  List<Object?> get props => [
        id,
        organizationId,
        userId,
        sessionId,
        eventType,
        eventName,
        eventData,
        eventProperties,
        userAgent,
        ipAddress,
        referrer,
        pageUrl,
        timestamp,
        createdAt,
      ];

  @override
  bool get stringify => true;
}

/// Analytics event creation request model
@JsonSerializable()
class AnalyticsEventRequest extends Equatable {
  /// Organization identifier
  final String organizationId;

  /// User identifier (optional for anonymous tracking)
  final String? userId;

  /// Session identifier
  final String? sessionId;

  /// Type of event
  final AnalyticsEventType eventType;

  /// Human-readable event name
  final String eventName;

  /// Event-specific data
  final Map<String, dynamic> eventData;

  /// Additional properties
  final Map<String, dynamic>? eventProperties;

  /// Page URL where event occurred
  final String? pageUrl;

  /// Referrer URL
  final String? referrer;

  const AnalyticsEventRequest({
    required this.organizationId,
    this.userId,
    this.sessionId,
    required this.eventType,
    required this.eventName,
    required this.eventData,
    this.eventProperties,
    this.pageUrl,
    this.referrer,
  });

  /// Create AnalyticsEventRequest from JSON
  factory AnalyticsEventRequest.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsEventRequestFromJson(json);

  /// Convert AnalyticsEventRequest to JSON
  Map<String, dynamic> toJson() => _$AnalyticsEventRequestToJson(this);

  @override
  List<Object?> get props => [
        organizationId,
        userId,
        sessionId,
        eventType,
        eventName,
        eventData,
        eventProperties,
        pageUrl,
        referrer,
      ];

  @override
  bool get stringify => true;
}

/// Batch analytics events request for efficient bulk tracking
@JsonSerializable()
class AnalyticsEventsBatch extends Equatable {
  /// Organization identifier
  final String organizationId;

  /// List of events in this batch
  final List<AnalyticsEventRequest> events;

  /// Batch timestamp
  final DateTime timestamp;

  /// Client information
  final Map<String, dynamic>? clientInfo;

  const AnalyticsEventsBatch({
    required this.organizationId,
    required this.events,
    required this.timestamp,
    this.clientInfo,
  });

  /// Create AnalyticsEventsBatch from JSON
  factory AnalyticsEventsBatch.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsEventsBatchFromJson(json);

  /// Convert AnalyticsEventsBatch to JSON
  Map<String, dynamic> toJson() => _$AnalyticsEventsBatchToJson(this);

  /// Validate batch constraints
  bool get isValid {
    if (events.isEmpty || events.length > 100) return false;
    return events.every((event) => event.organizationId == organizationId);
  }

  @override
  List<Object?> get props => [organizationId, events, timestamp, clientInfo];

  @override
  bool get stringify => true;
}