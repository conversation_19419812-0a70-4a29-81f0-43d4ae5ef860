import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../models/core/task.dart';

part 'task_dto.g.dart';

/// Create task request DTO
@JsonSerializable()
class CreateTaskDto extends Equatable {
  final String title;
  final String description;
  final String? questId;
  final String? assignedToId;
  final TaskPriority priority;
  final TaskComplexity complexity;
  final int? estimatedMinutes;
  final DateTime? deadline;
  final List<String> dependencies;
  final List<String> tags;
  final Map<String, dynamic>? metadata;

  const CreateTaskDto({
    required this.title,
    required this.description,
    this.questId,
    this.assignedToId,
    required this.priority,
    required this.complexity,
    this.estimatedMinutes,
    this.deadline,
    required this.dependencies,
    required this.tags,
    this.metadata,
  });

  factory CreateTaskDto.fromJson(Map<String, dynamic> json) => _$CreateTaskDtoFromJson(json);
  Map<String, dynamic> toJson() => _$CreateTaskDtoToJson(this);

  @override
  List<Object?> get props => [
        title,
        description,
        questId,
        assignedToId,
        priority,
        complexity,
        estimatedMinutes,
        deadline,
        dependencies,
        tags,
        metadata,
      ];
}

/// Update task request DTO
@JsonSerializable()
class UpdateTaskDto extends Equatable {
  final String? title;
  final String? description;
  final String? questId;
  final String? assignedToId;
  final TaskStatus? status;
  final TaskPriority? priority;
  final TaskComplexity? complexity;
  final int? estimatedMinutes;
  final int? actualMinutes;
  final DateTime? deadline;
  final List<String>? dependencies;
  final List<String>? tags;
  final List<String>? attachments;
  final List<String>? comments;
  final Map<String, dynamic>? metadata;

  const UpdateTaskDto({
    this.title,
    this.description,
    this.questId,
    this.assignedToId,
    this.status,
    this.priority,
    this.complexity,
    this.estimatedMinutes,
    this.actualMinutes,
    this.deadline,
    this.dependencies,
    this.tags,
    this.attachments,
    this.comments,
    this.metadata,
  });

  factory UpdateTaskDto.fromJson(Map<String, dynamic> json) => _$UpdateTaskDtoFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateTaskDtoToJson(this);

  @override
  List<Object?> get props => [
        title,
        description,
        questId,
        assignedToId,
        status,
        priority,
        complexity,
        estimatedMinutes,
        actualMinutes,
        deadline,
        dependencies,
        tags,
        attachments,
        comments,
        metadata,
      ];
}

/// Task filter parameters DTO
@JsonSerializable()
class TaskFilterDto extends Equatable {
  final List<TaskStatus>? statuses;
  final List<TaskPriority>? priorities;
  final List<TaskComplexity>? complexities;
  final String? questId;
  final String? createdById;
  final String? assignedToId;
  final List<String>? tags;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final DateTime? deadlineAfter;
  final DateTime? deadlineBefore;
  final bool? isOverdue;
  final bool? hasAttachments;
  final bool? hasDependencies;
  final String? search;
  final int? limit;
  final int? offset;
  final String? sortBy;
  final String? sortOrder;

  const TaskFilterDto({
    this.statuses,
    this.priorities,
    this.complexities,
    this.questId,
    this.createdById,
    this.assignedToId,
    this.tags,
    this.createdAfter,
    this.createdBefore,
    this.deadlineAfter,
    this.deadlineBefore,
    this.isOverdue,
    this.hasAttachments,
    this.hasDependencies,
    this.search,
    this.limit,
    this.offset,
    this.sortBy,
    this.sortOrder,
  });

  factory TaskFilterDto.fromJson(Map<String, dynamic> json) => _$TaskFilterDtoFromJson(json);
  Map<String, dynamic> toJson() => _$TaskFilterDtoToJson(this);

  @override
  List<Object?> get props => [
        statuses,
        priorities,
        complexities,
        questId,
        createdById,
        assignedToId,
        tags,
        createdAfter,
        createdBefore,
        deadlineAfter,
        deadlineBefore,
        isOverdue,
        hasAttachments,
        hasDependencies,
        search,
        limit,
        offset,
        sortBy,
        sortOrder,
      ];
}

/// Task list response DTO
@JsonSerializable()
class TaskListResponseDto extends Equatable {
  final List<Task> tasks;
  final int totalCount;
  final int pageCount;
  final int currentPage;
  final bool hasNext;
  final bool hasPrevious;

  const TaskListResponseDto({
    required this.tasks,
    required this.totalCount,
    required this.pageCount,
    required this.currentPage,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory TaskListResponseDto.fromJson(Map<String, dynamic> json) => _$TaskListResponseDtoFromJson(json);
  Map<String, dynamic> toJson() => _$TaskListResponseDtoToJson(this);

  @override
  List<Object?> get props => [
        tasks,
        totalCount,
        pageCount,
        currentPage,
        hasNext,
        hasPrevious,
      ];
}

/// Task statistics DTO
@JsonSerializable()
class TaskStatsDto extends Equatable {
  final int totalTasks;
  final int todoTasks;
  final int inProgressTasks;
  final int completedTasks;
  final int blockedTasks;
  final int overdueTasks;
  final int todayDeadlines;
  final double completionRate;
  final double averageCompletionTime;
  final int totalPointsEarned;
  final Map<String, int> statusBreakdown;
  final Map<String, int> priorityBreakdown;
  final Map<String, int> complexityBreakdown;

  const TaskStatsDto({
    required this.totalTasks,
    required this.todoTasks,
    required this.inProgressTasks,
    required this.completedTasks,
    required this.blockedTasks,
    required this.overdueTasks,
    required this.todayDeadlines,
    required this.completionRate,
    required this.averageCompletionTime,
    required this.totalPointsEarned,
    required this.statusBreakdown,
    required this.priorityBreakdown,
    required this.complexityBreakdown,
  });

  factory TaskStatsDto.fromJson(Map<String, dynamic> json) => _$TaskStatsDtoFromJson(json);
  Map<String, dynamic> toJson() => _$TaskStatsDtoToJson(this);

  @override
  List<Object?> get props => [
        totalTasks,
        todoTasks,
        inProgressTasks,
        completedTasks,
        blockedTasks,
        overdueTasks,
        todayDeadlines,
        completionRate,
        averageCompletionTime,
        totalPointsEarned,
        statusBreakdown,
        priorityBreakdown,
        complexityBreakdown,
      ];
}

/// Task completion DTO
@JsonSerializable()
class TaskCompletionDto extends Equatable {
  final String taskId;
  final int actualMinutes;
  final String? completionNotes;
  final List<String>? attachments;
  final Map<String, dynamic>? completionData;

  const TaskCompletionDto({
    required this.taskId,
    required this.actualMinutes,
    this.completionNotes,
    this.attachments,
    this.completionData,
  });

  factory TaskCompletionDto.fromJson(Map<String, dynamic> json) => _$TaskCompletionDtoFromJson(json);
  Map<String, dynamic> toJson() => _$TaskCompletionDtoToJson(this);

  @override
  List<Object?> get props => [taskId, actualMinutes, completionNotes, attachments, completionData];
}

/// Bulk task update DTO
@JsonSerializable()
class BulkTaskUpdateDto extends Equatable {
  final List<String> taskIds;
  final TaskStatus? status;
  final TaskPriority? priority;
  final String? assignedToId;
  final List<String>? tags;
  final DateTime? deadline;

  const BulkTaskUpdateDto({
    required this.taskIds,
    this.status,
    this.priority,
    this.assignedToId,
    this.tags,
    this.deadline,
  });

  factory BulkTaskUpdateDto.fromJson(Map<String, dynamic> json) => _$BulkTaskUpdateDtoFromJson(json);
  Map<String, dynamic> toJson() => _$BulkTaskUpdateDtoToJson(this);

  @override
  List<Object?> get props => [taskIds, status, priority, assignedToId, tags, deadline];
}

/// Task comment DTO
@JsonSerializable()
class TaskCommentDto extends Equatable {
  final String taskId;
  final String comment;
  final List<String>? attachments;
  final String? replyToCommentId;

  const TaskCommentDto({
    required this.taskId,
    required this.comment,
    this.attachments,
    this.replyToCommentId,
  });

  factory TaskCommentDto.fromJson(Map<String, dynamic> json) => _$TaskCommentDtoFromJson(json);
  Map<String, dynamic> toJson() => _$TaskCommentDtoToJson(this);

  @override
  List<Object?> get props => [taskId, comment, attachments, replyToCommentId];
}