/// Notification constants for the Quester platform
library;

/// Constants for notification system and push notifications
class NotificationConstants {
  // Notification types
  static const String questReminder = 'quest_reminder';
  static const String taskReminder = 'task_reminder';
  static const String deadlineAlert = 'deadline_alert';
  static const String achievementUnlocked = 'achievement_unlocked';
  static const String pointsEarned = 'points_earned';
  static const String levelUp = 'level_up';
  static const String messageReceived = 'message_received';
  static const String collaborationInvite = 'collaboration_invite';
  static const String questInvite = 'quest_invite';
  static const String teamInvite = 'team_invite';
  static const String systemAlert = 'system_alert';
  static const String maintenanceNotice = 'maintenance_notice';
  static const String updateAvailable = 'update_available';
  static const String streakReminder = 'streak_reminder';
  static const String leaderboardUpdate = 'leaderboard_update';
  static const String rewardAvailable = 'reward_available';
  
  // Notification priorities
  static const String lowPriority = 'low';
  static const String normalPriority = 'normal';
  static const String highPriority = 'high';
  static const String urgentPriority = 'urgent';
  
  // Notification channels (for Android)
  static const String defaultChannel = 'default';
  static const String questsChannel = 'quests';
  static const String tasksChannel = 'tasks';
  static const String messagesChannel = 'messages';
  static const String achievementsChannel = 'achievements';
  static const String systemChannel = 'system';
  static const String socialChannel = 'social';
  
  // Notification status
  static const String unread = 'unread';
  static const String read = 'read';
  static const String dismissed = 'dismissed';
  static const String archived = 'archived';
  
  // Delivery methods
  static const String inApp = 'in_app';
  static const String push = 'push';
  static const String email = 'email';
  static const String sms = 'sms';
  static const String webhook = 'webhook';
  
  // Timing settings
  static const Duration defaultRetryDelay = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  static const Duration batchProcessingInterval = Duration(minutes: 1);
  static const Duration notificationExpiry = Duration(days: 30);
  static const Duration reminderAdvanceTime = Duration(hours: 24);
  
  // UI settings
  static const int maxNotificationCount = 99;
  static const int maxTitleLength = 100;
  static const int maxBodyLength = 500;
  static const Duration displayDuration = Duration(seconds: 4);
  static const Duration fadeAnimationDuration = Duration(milliseconds: 300);
  
  // Batching and grouping
  static const int maxBatchSize = 50;
  static const Duration batchWindow = Duration(minutes: 5);
  static const int maxGroupSize = 10;
  
  // Default notification settings by type
  static const Map<String, Map<String, dynamic>> defaultSettings = {
    'quest_reminder': {
      'enabled': true,
      'priority': 'normal',
      'channels': ['in_app', 'push'],
      'advance_time_hours': 24,
      'repeat': false,
    },
    'task_reminder': {
      'enabled': true,
      'priority': 'normal',
      'channels': ['in_app', 'push'],
      'advance_time_hours': 2,
      'repeat': false,
    },
    'deadline_alert': {
      'enabled': true,
      'priority': 'high',
      'channels': ['in_app', 'push', 'email'],
      'advance_time_hours': 1,
      'repeat': true,
    },
    'achievement_unlocked': {
      'enabled': true,
      'priority': 'high',
      'channels': ['in_app', 'push'],
      'advance_time_hours': 0,
      'repeat': false,
    },
    'points_earned': {
      'enabled': true,
      'priority': 'normal',
      'channels': ['in_app'],
      'advance_time_hours': 0,
      'repeat': false,
    },
    'level_up': {
      'enabled': true,
      'priority': 'high',
      'channels': ['in_app', 'push'],
      'advance_time_hours': 0,
      'repeat': false,
    },
    'message_received': {
      'enabled': true,
      'priority': 'normal',
      'channels': ['in_app', 'push'],
      'advance_time_hours': 0,
      'repeat': false,
    },
    'collaboration_invite': {
      'enabled': true,
      'priority': 'high',
      'channels': ['in_app', 'push', 'email'],
      'advance_time_hours': 0,
      'repeat': false,
    },
    'system_alert': {
      'enabled': true,
      'priority': 'urgent',
      'channels': ['in_app', 'push', 'email'],
      'advance_time_hours': 0,
      'repeat': false,
    },
  };
  
  // Notification templates
  static const Map<String, Map<String, String>> templates = {
    'quest_reminder': {
      'title': 'Quest Reminder',
      'body': 'Don\'t forget about your quest: {quest_title}',
      'action': 'View Quest',
    },
    'task_reminder': {
      'title': 'Task Reminder',
      'body': 'Task "{task_title}" is due soon',
      'action': 'View Task',
    },
    'deadline_alert': {
      'title': 'Deadline Alert',
      'body': 'Urgent: {item_title} is due in {time_remaining}',
      'action': 'Take Action',
    },
    'achievement_unlocked': {
      'title': 'Achievement Unlocked!',
      'body': 'Congratulations! You earned "{achievement_name}"',
      'action': 'View Achievement',
    },
    'points_earned': {
      'title': 'Points Earned',
      'body': 'You earned {points} points! Total: {total_points}',
      'action': 'View Profile',
    },
    'level_up': {
      'title': 'Level Up!',
      'body': 'Amazing! You reached level {level}',
      'action': 'View Profile',
    },
    'message_received': {
      'title': 'New Message',
      'body': '{sender_name}: {message_preview}',
      'action': 'View Message',
    },
    'collaboration_invite': {
      'title': 'Collaboration Invite',
      'body': '{inviter_name} invited you to collaborate on "{quest_title}"',
      'action': 'View Invite',
    },
  };
  
  // Sound settings
  static const Map<String, String> notificationSounds = {
    'default': 'default',
    'achievement': 'achievement_sound.mp3',
    'message': 'message_sound.mp3',
    'reminder': 'reminder_sound.mp3',
    'alert': 'alert_sound.mp3',
    'level_up': 'level_up_sound.mp3',
  };
  
  // Icon settings
  static const Map<String, String> notificationIcons = {
    'quest_reminder': 'quest_icon',
    'task_reminder': 'task_icon',
    'achievement_unlocked': 'achievement_icon',
    'points_earned': 'points_icon',
    'level_up': 'level_up_icon',
    'message_received': 'message_icon',
    'collaboration_invite': 'collaboration_icon',
    'system_alert': 'alert_icon',
  };
  
  // Quiet hours settings
  static const String defaultQuietStart = '22:00';
  static const String defaultQuietEnd = '08:00';
  static const List<String> quietHoursExceptions = [
    'system_alert',
    'deadline_alert',
  ];
  
  // Utility methods
  
  /// Get notification priority level (1-4)
  static int getPriorityLevel(String priority) {
    switch (priority) {
      case 'low': return 1;
      case 'normal': return 2;
      case 'high': return 3;
      case 'urgent': return 4;
      default: return 2;
    }
  }
  
  /// Check if notification type should bypass quiet hours
  static bool bypassesQuietHours(String notificationType) {
    return quietHoursExceptions.contains(notificationType);
  }
  
  /// Get default settings for notification type
  static Map<String, dynamic> getDefaultSettings(String notificationType) {
    return Map<String, dynamic>.from(
      defaultSettings[notificationType] ?? defaultSettings['quest_reminder']!
    );
  }
  
  /// Get notification template
  static Map<String, String> getTemplate(String notificationType) {
    return Map<String, String>.from(
      templates[notificationType] ?? templates['quest_reminder']!
    );
  }
  
  /// Format notification body with variables
  static String formatNotificationBody(String template, Map<String, String> variables) {
    String result = template;
    variables.forEach((key, value) {
      result = result.replaceAll('{$key}', value);
    });
    return result;
  }
  
  /// Get notification sound for type
  static String getNotificationSound(String notificationType) {
    return notificationSounds[notificationType] ?? notificationSounds['default']!;
  }
  
  /// Get notification icon for type
  static String getNotificationIcon(String notificationType) {
    return notificationIcons[notificationType] ?? 'default_icon';
  }
  
  /// Check if notification should be batched
  static bool shouldBatch(String notificationType) {
    const batchableTypes = [
      'points_earned',
      'message_received',
      'leaderboard_update',
    ];
    return batchableTypes.contains(notificationType);
  }
  
  /// Generate notification ID
  static String generateNotificationId(String type, String userId, [String? itemId]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final suffix = itemId != null ? '_$itemId' : '';
    return '${type}_${userId}_$timestamp$suffix';
  }
  
  /// Check if notification is expired
  static bool isExpired(DateTime createdAt) {
    final now = DateTime.now();
    return now.difference(createdAt) > notificationExpiry;
  }
}
