import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'websocket_event.g.dart';

/// WebSocket event types for real-time communication
enum WebSocketEventType {
  @JsonValue('connection')
  connection,
  @JsonValue('disconnection')
  disconnection,
  @JsonValue('user_presence')
  userPresence,
  @JsonValue('quest_update')
  questUpdate,
  @JsonValue('task_update')
  taskUpdate,
  @JsonValue('achievement_unlock')
  achievementUnlock,
  @JsonValue('leaderboard_update')
  leaderboardUpdate,
  @JsonValue('notification')
  notification,
  @JsonValue('collaboration_invite')
  collaborationInvite,
  @JsonValue('live_editing')
  liveEditing,
  @JsonValue('chat_message')
  chatMessage,
  @JsonValue('typing_indicator')
  typingIndicator,
  @JsonValue('system_announcement')
  systemAnnouncement,
  @JsonValue('error')
  error,
  @JsonValue('heartbeat')
  heartbeat,
}

/// Priority levels for WebSocket events
enum EventPriority {
  @JsonValue('low')
  low,
  @JsonValue('normal')
  normal,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

/// WebSocket event model for real-time communication
@JsonSerializable()
class WebSocketEvent extends Equatable {
  /// Unique event identifier
  final String id;

  /// Event type
  final WebSocketEventType type;

  /// Event priority
  final EventPriority priority;

  /// Source user ID (if applicable)
  final String? userId;

  /// Target user IDs (for directed events)
  final List<String>? targetUserIds;

  /// Organization ID (for multi-tenant events)
  final String? organizationId;

  /// Room/channel ID (for scoped events)
  final String? roomId;

  /// Event payload data
  final Map<String, dynamic> data;

  /// Event metadata
  final Map<String, dynamic>? metadata;

  /// Event timestamp
  final DateTime timestamp;

  /// Time-to-live for event (in seconds)
  final int? ttl;

  /// Whether event requires acknowledgment
  final bool requiresAck;

  /// Retry count for failed deliveries
  final int retryCount;

  /// Maximum retry attempts
  final int maxRetries;

  const WebSocketEvent({
    required this.id,
    required this.type,
    this.priority = EventPriority.normal,
    this.userId,
    this.targetUserIds,
    this.organizationId,
    this.roomId,
    required this.data,
    this.metadata,
    required this.timestamp,
    this.ttl,
    this.requiresAck = false,
    this.retryCount = 0,
    this.maxRetries = 3,
  });

  /// Create WebSocketEvent from JSON
  factory WebSocketEvent.fromJson(Map<String, dynamic> json) => _$WebSocketEventFromJson(json);

  /// Convert WebSocketEvent to JSON
  Map<String, dynamic> toJson() => _$WebSocketEventToJson(this);

  /// Create a user presence event
  factory WebSocketEvent.userPresence({
    required String userId,
    required String status,
    String? organizationId,
    Map<String, dynamic>? additionalData,
  }) {
    return WebSocketEvent(
      id: 'presence_${DateTime.now().millisecondsSinceEpoch}',
      type: WebSocketEventType.userPresence,
      priority: EventPriority.low,
      userId: userId,
      organizationId: organizationId,
      data: {
        'status': status,
        'last_seen': DateTime.now().toIso8601String(),
        ...?additionalData,
      },
      timestamp: DateTime.now(),
      ttl: 300, // 5 minutes
    );
  }

  /// Create a quest update event
  factory WebSocketEvent.questUpdate({
    required String questId,
    required String userId,
    required String action,
    required Map<String, dynamic> questData,
    String? organizationId,
    List<String>? targetUserIds,
  }) {
    return WebSocketEvent(
      id: 'quest_${questId}_${DateTime.now().millisecondsSinceEpoch}',
      type: WebSocketEventType.questUpdate,
      priority: EventPriority.normal,
      userId: userId,
      targetUserIds: targetUserIds,
      organizationId: organizationId,
      data: {
        'quest_id': questId,
        'action': action,
        'quest_data': questData,
      },
      timestamp: DateTime.now(),
      requiresAck: true,
    );
  }

  /// Create an achievement unlock event
  factory WebSocketEvent.achievementUnlock({
    required String userId,
    required String achievementId,
    required int pointsAwarded,
    String? organizationId,
  }) {
    return WebSocketEvent(
      id: 'achievement_${achievementId}_${DateTime.now().millisecondsSinceEpoch}',
      type: WebSocketEventType.achievementUnlock,
      priority: EventPriority.high,
      userId: userId,
      organizationId: organizationId,
      data: {
        'achievement_id': achievementId,
        'points_awarded': pointsAwarded,
        'unlock_time': DateTime.now().toIso8601String(),
      },
      timestamp: DateTime.now(),
      requiresAck: true,
    );
  }

  /// Create a live editing event
  factory WebSocketEvent.liveEditing({
    required String documentId,
    required String userId,
    required String operation,
    required Map<String, dynamic> changes,
    String? roomId,
  }) {
    return WebSocketEvent(
      id: 'edit_${documentId}_${DateTime.now().millisecondsSinceEpoch}',
      type: WebSocketEventType.liveEditing,
      priority: EventPriority.high,
      userId: userId,
      roomId: roomId,
      data: {
        'document_id': documentId,
        'operation': operation,
        'changes': changes,
        'cursor_position': null,
      },
      timestamp: DateTime.now(),
      requiresAck: true,
      ttl: 60, // 1 minute
    );
  }

  /// Create a notification event
  factory WebSocketEvent.notification({
    required String userId,
    required String title,
    required String message,
    String? type,
    Map<String, dynamic>? actionData,
    String? organizationId,
  }) {
    return WebSocketEvent(
      id: 'notification_${DateTime.now().millisecondsSinceEpoch}',
      type: WebSocketEventType.notification,
      priority: EventPriority.normal,
      userId: userId,
      organizationId: organizationId,
      data: {
        'title': title,
        'message': message,
        'notification_type': type,
        'action_data': actionData,
      },
      timestamp: DateTime.now(),
      requiresAck: true,
    );
  }

  /// Check if event has expired based on TTL
  bool get isExpired {
    if (ttl == null) return false;
    final expirationTime = timestamp.add(Duration(seconds: ttl!));
    return DateTime.now().isAfter(expirationTime);
  }

  /// Check if event can be retried
  bool get canRetry => retryCount < maxRetries;

  /// Get priority as integer for sorting
  int get priorityValue {
    switch (priority) {
      case EventPriority.low:
        return 1;
      case EventPriority.normal:
        return 2;
      case EventPriority.high:
        return 3;
      case EventPriority.critical:
        return 4;
    }
  }

  /// Create a copy with updated retry count
  WebSocketEvent copyWithRetry() {
    return WebSocketEvent(
      id: id,
      type: type,
      priority: priority,
      userId: userId,
      targetUserIds: targetUserIds,
      organizationId: organizationId,
      roomId: roomId,
      data: data,
      metadata: metadata,
      timestamp: timestamp,
      ttl: ttl,
      requiresAck: requiresAck,
      retryCount: retryCount + 1,
      maxRetries: maxRetries,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        priority,
        userId,
        targetUserIds,
        organizationId,
        roomId,
        data,
        metadata,
        timestamp,
        ttl,
        requiresAck,
        retryCount,
        maxRetries,
      ];

  @override
  bool get stringify => true;
}
