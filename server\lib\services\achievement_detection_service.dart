import '../services/database_service.dart';
import '../services/websocket_service.dart';

/// Advanced Achievement Detection Engine
/// Automatically detects and awards achievements based on user activity patterns
class AchievementDetectionService {
  final DatabaseService _databaseService;
  
  AchievementDetectionService(this._databaseService);
  
  /// Check for achievements after any user activity
  Future<List<Map<String, dynamic>>> checkAndAwardAchievements({
    required String userId,
    required String activityType,
    Map<String, dynamic>? activityData,
  }) async {
    final awardedAchievements = <Map<String, dynamic>>[];
    
    try {
      // Get user's current stats for achievement evaluation
      final userStats = await _databaseService.getUserStats(userId);
      if (userStats == null) return awardedAchievements;
      
      // Get all available achievements that user hasn't earned yet
      final availableAchievements = await _getUnearnedAchievements(userId);
      
      // Check each achievement against user's current progress
      for (final achievement in availableAchievements) {
        if (await _evaluateAchievement(achievement, userStats, activityType, activityData)) {
          // Award the achievement
          final awarded = await _awardAchievement(userId, achievement);
          if (awarded != null) {
            awardedAchievements.add(awarded);
            
            // Broadcast achievement unlock in real-time
            WebSocketService.broadcastAchievementUnlock(userId, awarded);
            
            // Log achievement activity
            await _logAchievementActivity(userId, achievement);
          }
        }
      }
      
      return awardedAchievements;
    } catch (e) {
      print('❌ Error in achievement detection: $e');
      return awardedAchievements;
    }
  }
  
  /// Get achievements user hasn't earned yet
  Future<List<Map<String, dynamic>>> _getUnearnedAchievements(String userId) async {
    try {
      // Get all achievements
      final allAchievements = await _databaseService.getAllAchievements();
      
      // Get user's earned achievements
      final userAchievements = await _databaseService.getUserAchievements(userId);
      final earnedIds = userAchievements.map((a) => a['id']).toSet();
      
      // Return only unearned achievements
      return allAchievements.where((a) => !earnedIds.contains(a['id'])).toList();
    } catch (e) {
      print('❌ Error getting unearned achievements: $e');
      return [];
    }
  }
  
  /// Evaluate if user qualifies for an achievement
  Future<bool> _evaluateAchievement(
    Map<String, dynamic> achievement, 
    Map<String, dynamic> userStats,
    String activityType,
    Map<String, dynamic>? activityData,
  ) async {
    final achievementId = achievement['id'] as String;
    final requirements = achievement['requirements'] as Map<String, dynamic>;
    
    switch (achievementId) {
      // Quest completion achievements
      case 'first_quest':
        return _checkQuestCount(userStats, 1);
      case 'quest_novice':
        return _checkQuestCount(userStats, 5);
      case 'quest_explorer':
        return _checkQuestCount(userStats, 25);
      case 'quest_master':
        return _checkQuestCount(userStats, 100);
      case 'quest_legend':
        return _checkQuestCount(userStats, 500);
        
      // Point accumulation achievements
      case 'milestone_achiever':
        return _checkTotalPoints(userStats, 10000);
      case 'point_accumulator':
        return _checkTotalPoints(userStats, 50000);
        
      // Streak achievements
      case 'streak_starter':
        return _checkStreak(userStats, 3);
      case 'consistent_achiever':
        return _checkStreak(userStats, 7);
      case 'dedication_master':
        return _checkStreak(userStats, 30);
      case 'unstoppable_force':
        return _checkStreak(userStats, 100);
      case 'eternal_dedication':
        return _checkStreak(userStats, 365);
        
      // Collaboration achievements
      case 'team_player':
        return _checkCollaborativeQuests(userStats, 1);
      case 'collaboration_expert':
        return _checkCollaborativeQuests(userStats, 50);
      case 'mentor':
        return _checkHelpCount(userStats, 10);
      case 'social_connector':
        return _checkConnections(userStats, 50);
        
      // Innovation achievements
      case 'creative_mind':
        return _checkCreatedQuests(userStats, 1);
      case 'innovator':
        return _checkPopularQuests(userStats, 10);
      case 'game_changer':
        return _checkQuestParticipants(userStats, 100);
        
      // Leadership achievements
      case 'natural_leader':
        return _checkLedQuests(userStats, 1);
      case 'inspiring_leader':
        return _checkLedQuests(userStats, 10);
      case 'visionary':
        return _checkCreatedLedQuests(userStats, 25);
        
      // Quality work achievements
      case 'perfectionist':
        return _checkPerfectQuests(userStats, 10);
      case 'quality_assurance':
        return _checkQualityQuests(userStats, 50);
      case 'efficiency_expert':
        return _checkEfficiencyRating(userStats, 1.5);
        
      // Consistency achievements
      case 'daily_achiever':
        return _checkDailyTasks(userStats, 7);
      case 'weekend_warrior':
        return _checkWeekendActivity(userStats, 20);
      case 'monthly_champion':
        return _checkMonthlyTasks(userStats, 100);
      case 'early_bird':
        return _checkMorningTasks(userStats, 50);
      case 'night_owl':
        return _checkNightTasks(userStats, 50);
        
      default:
        // Dynamic achievement evaluation based on requirements
        return _evaluateDynamicRequirements(requirements, userStats, activityType, activityData);
    }
  }
  
  /// Award achievement to user
  Future<Map<String, dynamic>?> _awardAchievement(String userId, Map<String, dynamic> achievement) async {
    try {
      // This would normally insert into user_achievements table
      // For now, return mock achievement award data
      final awardedAchievement = {
        'id': achievement['id'],
        'name': achievement['name'],
        'description': achievement['description'],
        'category': achievement['category'],
        'rarity': achievement['rarity'],
        'points_awarded': achievement['points_reward'],
        'earned_date': DateTime.now().toIso8601String(),
        'user_id': userId,
      };
      
      print('🏆 Achievement awarded: ${achievement['name']} to user $userId');
      return awardedAchievement;
    } catch (e) {
      print('❌ Error awarding achievement: $e');
      return null;
    }
  }
  
  /// Log achievement earning activity
  Future<void> _logAchievementActivity(String userId, Map<String, dynamic> achievement) async {
    try {
      // This would log to activity_log table
      print('📝 Logged achievement activity: ${achievement['name']} for user $userId');
    } catch (e) {
      print('❌ Error logging achievement activity: $e');
    }
  }
  
  // Achievement evaluation helper methods
  bool _checkQuestCount(Map<String, dynamic> userStats, int required) {
    final questCount = userStats['quest_count'] as int? ?? 0;
    return questCount >= required;
  }
  
  bool _checkTotalPoints(Map<String, dynamic> userStats, int required) {
    final totalPoints = userStats['total_points'] as int? ?? 0;
    return totalPoints >= required;
  }
  
  bool _checkStreak(Map<String, dynamic> userStats, int required) {
    final currentStreak = userStats['current_streak'] as int? ?? 0;
    return currentStreak >= required;
  }
  
  bool _checkCollaborativeQuests(Map<String, dynamic> userStats, int required) {
    final collaborativeQuests = userStats['collaborative_quests'] as int? ?? 0;
    return collaborativeQuests >= required;
  }
  
  bool _checkHelpCount(Map<String, dynamic> userStats, int required) {
    final helpCount = userStats['help_count'] as int? ?? 0;
    return helpCount >= required;
  }
  
  bool _checkConnections(Map<String, dynamic> userStats, int required) {
    final connections = userStats['connections'] as int? ?? 0;
    return connections >= required;
  }
  
  bool _checkCreatedQuests(Map<String, dynamic> userStats, int required) {
    final createdQuests = userStats['created_quests'] as int? ?? 0;
    return createdQuests >= required;
  }
  
  bool _checkPopularQuests(Map<String, dynamic> userStats, int required) {
    final popularQuests = userStats['popular_quests'] as int? ?? 0;
    return popularQuests >= required;
  }
  
  bool _checkQuestParticipants(Map<String, dynamic> userStats, int required) {
    final questParticipants = userStats['quest_participants'] as int? ?? 0;
    return questParticipants >= required;
  }
  
  bool _checkLedQuests(Map<String, dynamic> userStats, int required) {
    final ledQuests = userStats['led_quests'] as int? ?? 0;
    return ledQuests >= required;
  }
  
  bool _checkCreatedLedQuests(Map<String, dynamic> userStats, int required) {
    final createdLedQuests = userStats['created_led_quests'] as int? ?? 0;
    return createdLedQuests >= required;
  }
  
  bool _checkPerfectQuests(Map<String, dynamic> userStats, int required) {
    final perfectQuests = userStats['perfect_quests'] as int? ?? 0;
    return perfectQuests >= required;
  }
  
  bool _checkQualityQuests(Map<String, dynamic> userStats, int required) {
    final qualityQuests = userStats['quality_quests'] as int? ?? 0;
    return qualityQuests >= required;
  }
  
  bool _checkEfficiencyRating(Map<String, dynamic> userStats, double required) {
    final efficiencyRating = userStats['efficiency_rating'] as double? ?? 0.0;
    return efficiencyRating >= required;
  }
  
  bool _checkDailyTasks(Map<String, dynamic> userStats, int required) {
    final dailyTasks = userStats['daily_tasks'] as int? ?? 0;
    return dailyTasks >= required;
  }
  
  bool _checkWeekendActivity(Map<String, dynamic> userStats, int required) {
    final weekendActivity = userStats['weekend_activity'] as int? ?? 0;
    return weekendActivity >= required;
  }
  
  bool _checkMonthlyTasks(Map<String, dynamic> userStats, int required) {
    final monthlyTasks = userStats['monthly_tasks'] as int? ?? 0;
    return monthlyTasks >= required;
  }
  
  bool _checkMorningTasks(Map<String, dynamic> userStats, int required) {
    final morningTasks = userStats['morning_tasks'] as int? ?? 0;
    return morningTasks >= required;
  }
  
  bool _checkNightTasks(Map<String, dynamic> userStats, int required) {
    final nightTasks = userStats['night_tasks'] as int? ?? 0;
    return nightTasks >= required;
  }
  
  /// Evaluate dynamic requirements from achievement definition
  bool _evaluateDynamicRequirements(
    Map<String, dynamic> requirements,
    Map<String, dynamic> userStats,
    String activityType,
    Map<String, dynamic>? activityData,
  ) {
    // Check each requirement against user stats
    for (final entry in requirements.entries) {
      final requiredValue = entry.value;
      final userValue = userStats[entry.key];
      
      if (userValue == null) return false;
      
      // Handle different value types
      if (requiredValue is int && userValue is int) {
        if (userValue < requiredValue) return false;
      } else if (requiredValue is double && userValue is num) {
        if (userValue.toDouble() < requiredValue) return false;
      } else if (requiredValue is String && userValue is String) {
        if (userValue != requiredValue) return false;
      } else if (requiredValue is bool && userValue is bool) {
        if (userValue != requiredValue) return false;
      }
    }
    
    return true;
  }
}
