import 'package:json_annotation/json_annotation.dart';

part 'mfa_models.g.dart';

enum MFAMethod { totp, sms, email, backupCode, trustedDevice }
enum MFAPolicyType { always, riskBased, deviceBased, timeBased, locationBased }
enum MFAEnforcementLevel { disabled, optional, required, strict }
enum MFABypassReason { trustedDevice, emergencyAccess, adminOverride, temporaryException }

@JsonSerializable()
class MFASetupStatus {
  @Json<PERSON>ey(name: 'user_id')
  final String userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'totp_enabled')
  final bool totpEnabled;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'totp_verified')
  final bool totpVerified;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'backup_codes_count')
  final int backupCodesCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'trusted_devices_count')
  final int trustedDevicesCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_configured')
  final bool phoneConfigured;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email_configured')
  final bool emailConfigured;
  @J<PERSON><PERSON><PERSON>(name: 'setup_complete')
  final bool setupComplete;
  @Json<PERSON>ey(name: 'last_updated')
  final DateTime lastUpdated;

  const MFASetupStatus({
    required this.userId,
    required this.totpEnabled,
    required this.totpVerified,
    required this.backupCodesCount,
    required this.trustedDevicesCount,
    required this.phoneConfigured,
    required this.emailConfigured,
    required this.setupComplete,
    required this.lastUpdated,
  });

  factory MFASetupStatus.fromJson(Map<String, dynamic> json) =>
      _$MFASetupStatusFromJson(json);

  Map<String, dynamic> toJson() => _$MFASetupStatusToJson(this);

  bool get isMFAEnabled => totpVerified || backupCodesCount > 0;
  bool get hasRecoveryOptions => backupCodesCount > 0 || phoneConfigured || emailConfigured;
  double get completionScore {
    var score = 0.0;
    if (totpVerified) score += 0.4;
    if (backupCodesCount > 0) score += 0.3;
    if (trustedDevicesCount > 0) score += 0.2;
    if (phoneConfigured) score += 0.1;
    return score.clamp(0.0, 1.0);
  }

  MFASetupStatus copyWith({
    String? userId,
    bool? totpEnabled,
    bool? totpVerified,
    int? backupCodesCount,
    int? trustedDevicesCount,
    bool? phoneConfigured,
    bool? emailConfigured,
    bool? setupComplete,
    DateTime? lastUpdated,
  }) {
    return MFASetupStatus(
      userId: userId ?? this.userId,
      totpEnabled: totpEnabled ?? this.totpEnabled,
      totpVerified: totpVerified ?? this.totpVerified,
      backupCodesCount: backupCodesCount ?? this.backupCodesCount,
      trustedDevicesCount: trustedDevicesCount ?? this.trustedDevicesCount,
      phoneConfigured: phoneConfigured ?? this.phoneConfigured,
      emailConfigured: emailConfigured ?? this.emailConfigured,
      setupComplete: setupComplete ?? this.setupComplete,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

@JsonSerializable()
class MFAEnforcementResult {
  @JsonKey(name: 'is_mfa_required')
  final bool isMFARequired;
  @JsonKey(name: 'can_bypass_mfa')
  final bool canBypassMFA;
  @JsonKey(name: 'available_methods')
  final List<MFAMethod> availableMethods;
  @JsonKey(name: 'recommended_methods')
  final List<MFAMethod> recommendedMethods;
  @JsonKey(name: 'bypass_reason')
  final MFABypassReason? bypassReason;
  @JsonKey(name: 'challenge_id')
  final String? challengeId;
  @JsonKey(name: 'policy_context')
  final Map<String, dynamic> policyContext;
  final List<String> warnings;
  @JsonKey(name: 'max_attempts')
  final int maxAttempts;
  @JsonKey(name: 'challenge_valid_duration_minutes')
  final int challengeValidDurationMinutes;
  @JsonKey(name: 'enforced_at')
  final DateTime enforcedAt;

  const MFAEnforcementResult({
    required this.isMFARequired,
    required this.canBypassMFA,
    required this.availableMethods,
    required this.recommendedMethods,
    this.bypassReason,
    this.challengeId,
    this.policyContext = const {},
    this.warnings = const [],
    this.maxAttempts = 3,
    this.challengeValidDurationMinutes = 5,
    required this.enforcedAt,
  });

  factory MFAEnforcementResult.fromJson(Map<String, dynamic> json) =>
      _$MFAEnforcementResultFromJson(json);

  Map<String, dynamic> toJson() => _$MFAEnforcementResultToJson(this);

  Duration get challengeValidDuration => Duration(minutes: challengeValidDurationMinutes);
  bool get hasWarnings => warnings.isNotEmpty;
  String get primaryRecommendedMethod => recommendedMethods.isNotEmpty 
      ? recommendedMethods.first.name 
      : 'none';

  MFAEnforcementResult copyWith({
    bool? isMFARequired,
    bool? canBypassMFA,
    List<MFAMethod>? availableMethods,
    List<MFAMethod>? recommendedMethods,
    MFABypassReason? bypassReason,
    String? challengeId,
    Map<String, dynamic>? policyContext,
    List<String>? warnings,
    int? maxAttempts,
    int? challengeValidDurationMinutes,
    DateTime? enforcedAt,
  }) {
    return MFAEnforcementResult(
      isMFARequired: isMFARequired ?? this.isMFARequired,
      canBypassMFA: canBypassMFA ?? this.canBypassMFA,
      availableMethods: availableMethods ?? this.availableMethods,
      recommendedMethods: recommendedMethods ?? this.recommendedMethods,
      bypassReason: bypassReason ?? this.bypassReason,
      challengeId: challengeId ?? this.challengeId,
      policyContext: policyContext ?? this.policyContext,
      warnings: warnings ?? this.warnings,
      maxAttempts: maxAttempts ?? this.maxAttempts,
      challengeValidDurationMinutes: challengeValidDurationMinutes ?? this.challengeValidDurationMinutes,
      enforcedAt: enforcedAt ?? this.enforcedAt,
    );
  }
}

@JsonSerializable()
class MFAChallenge {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final MFAMethod method;
  @JsonKey(name: 'challenge_data')
  final String? challengeData;
  @JsonKey(name: 'destination_hint')
  final String? destinationHint;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'expires_at')
  final DateTime expiresAt;
  @JsonKey(name: 'is_completed')
  final bool isCompleted;
  @JsonKey(name: 'completed_at')
  final DateTime? completedAt;
  @JsonKey(name: 'attempt_count')
  final int attemptCount;
  @JsonKey(name: 'max_attempts')
  final int maxAttempts;
  final Map<String, dynamic> metadata;

  const MFAChallenge({
    required this.id,
    required this.userId,
    required this.method,
    this.challengeData,
    this.destinationHint,
    required this.createdAt,
    required this.expiresAt,
    this.isCompleted = false,
    this.completedAt,
    this.attemptCount = 0,
    this.maxAttempts = 3,
    this.metadata = const {},
  });

  factory MFAChallenge.fromJson(Map<String, dynamic> json) =>
      _$MFAChallengeFromJson(json);

  Map<String, dynamic> toJson() => _$MFAChallengeToJson(this);

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get hasAttemptsRemaining => attemptCount < maxAttempts;
  Duration get timeRemaining => expiresAt.difference(DateTime.now());
  String get statusDescription {
    if (isCompleted) return 'Completed';
    if (isExpired) return 'Expired';
    if (!hasAttemptsRemaining) return 'Failed';
    return 'Active';
  }

  MFAChallenge copyWith({
    String? id,
    String? userId,
    MFAMethod? method,
    String? challengeData,
    String? destinationHint,
    DateTime? createdAt,
    DateTime? expiresAt,
    bool? isCompleted,
    DateTime? completedAt,
    int? attemptCount,
    int? maxAttempts,
    Map<String, dynamic>? metadata,
  }) {
    return MFAChallenge(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      method: method ?? this.method,
      challengeData: challengeData ?? this.challengeData,
      destinationHint: destinationHint ?? this.destinationHint,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      attemptCount: attemptCount ?? this.attemptCount,
      maxAttempts: maxAttempts ?? this.maxAttempts,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable()
class MFAVerificationResult {
  @JsonKey(name: 'is_valid')
  final bool isValid;
  @JsonKey(name: 'is_completed')
  final bool isCompleted;
  @JsonKey(name: 'session_token')
  final String? sessionToken;
  @JsonKey(name: 'error_message')
  final String? errorMessage;
  @JsonKey(name: 'attempts_remaining')
  final int attemptsRemaining;
  @JsonKey(name: 'device_trusted')
  final bool deviceTrusted;
  final Map<String, dynamic> metadata;
  @JsonKey(name: 'verified_at')
  final DateTime verifiedAt;

  const MFAVerificationResult({
    required this.isValid,
    required this.isCompleted,
    this.sessionToken,
    this.errorMessage,
    required this.attemptsRemaining,
    this.deviceTrusted = false,
    this.metadata = const {},
    required this.verifiedAt,
  });

  factory MFAVerificationResult.fromJson(Map<String, dynamic> json) =>
      _$MFAVerificationResultFromJson(json);

  Map<String, dynamic> toJson() => _$MFAVerificationResultToJson(this);

  bool get hasSessionToken => sessionToken != null && sessionToken!.isNotEmpty;
  bool get canRetry => !isCompleted && attemptsRemaining > 0;
  
  MFAVerificationResult copyWith({
    bool? isValid,
    bool? isCompleted,
    String? sessionToken,
    String? errorMessage,
    int? attemptsRemaining,
    bool? deviceTrusted,
    Map<String, dynamic>? metadata,
    DateTime? verifiedAt,
  }) {
    return MFAVerificationResult(
      isValid: isValid ?? this.isValid,
      isCompleted: isCompleted ?? this.isCompleted,
      sessionToken: sessionToken ?? this.sessionToken,
      errorMessage: errorMessage ?? this.errorMessage,
      attemptsRemaining: attemptsRemaining ?? this.attemptsRemaining,
      deviceTrusted: deviceTrusted ?? this.deviceTrusted,
      metadata: metadata ?? this.metadata,
      verifiedAt: verifiedAt ?? this.verifiedAt,
    );
  }
}

@JsonSerializable()
class MFASettings {
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'mfa_enabled')
  final bool mfaEnabled;
  @JsonKey(name: 'totp_configured')
  final bool totpConfigured;
  @JsonKey(name: 'sms_configured')
  final bool smsConfigured;
  @JsonKey(name: 'email_configured')
  final bool emailConfigured;
  @JsonKey(name: 'backup_codes_available')
  final bool backupCodesAvailable;
  @JsonKey(name: 'backup_codes_count')
  final int backupCodesCount;
  @JsonKey(name: 'trusted_devices_count')
  final int trustedDevicesCount;
  @JsonKey(name: 'recovery_options')
  final List<String> recoveryOptions;
  @JsonKey(name: 'last_updated')
  final DateTime lastUpdated;

  const MFASettings({
    required this.userId,
    required this.mfaEnabled,
    required this.totpConfigured,
    required this.smsConfigured,
    required this.emailConfigured,
    required this.backupCodesAvailable,
    required this.backupCodesCount,
    required this.trustedDevicesCount,
    required this.recoveryOptions,
    required this.lastUpdated,
  });

  factory MFASettings.fromJson(Map<String, dynamic> json) =>
      _$MFASettingsFromJson(json);

  Map<String, dynamic> toJson() => _$MFASettingsToJson(this);

  bool get hasRecoveryMethods => recoveryOptions.isNotEmpty;
  bool get needsBackupCodes => totpConfigured && backupCodesCount == 0;
  int get configuredMethodsCount {
    var count = 0;
    if (totpConfigured) count++;
    if (smsConfigured) count++;
    if (emailConfigured) count++;
    return count;
  }

  String get securityLevel {
    if (!mfaEnabled) return 'None';
    if (totpConfigured && backupCodesCount > 2) return 'Excellent';
    if (totpConfigured && backupCodesCount > 0) return 'Good';
    if (totpConfigured) return 'Fair';
    return 'Basic';
  }

  MFASettings copyWith({
    String? userId,
    bool? mfaEnabled,
    bool? totpConfigured,
    bool? smsConfigured,
    bool? emailConfigured,
    bool? backupCodesAvailable,
    int? backupCodesCount,
    int? trustedDevicesCount,
    List<String>? recoveryOptions,
    DateTime? lastUpdated,
  }) {
    return MFASettings(
      userId: userId ?? this.userId,
      mfaEnabled: mfaEnabled ?? this.mfaEnabled,
      totpConfigured: totpConfigured ?? this.totpConfigured,
      smsConfigured: smsConfigured ?? this.smsConfigured,
      emailConfigured: emailConfigured ?? this.emailConfigured,
      backupCodesAvailable: backupCodesAvailable ?? this.backupCodesAvailable,
      backupCodesCount: backupCodesCount ?? this.backupCodesCount,
      trustedDevicesCount: trustedDevicesCount ?? this.trustedDevicesCount,
      recoveryOptions: recoveryOptions ?? this.recoveryOptions,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

@JsonSerializable()
class MFAUserStats {
  @JsonKey(name: 'user_id')
  final String userId;
  final String email;
  @JsonKey(name: 'totp_enabled')
  final bool totpEnabled;
  @JsonKey(name: 'backup_codes_count')
  final int backupCodesCount;
  @JsonKey(name: 'backup_codes_used')
  final int backupCodesUsed;
  @JsonKey(name: 'trusted_devices_count')
  final int trustedDevicesCount;
  @JsonKey(name: 'active_mfa_sessions')
  final int activeMfaSessions;
  @JsonKey(name: 'last_mfa_activity')
  final DateTime? lastMfaActivity;
  @JsonKey(name: 'security_score')
  final double securityScore;

  const MFAUserStats({
    required this.userId,
    required this.email,
    required this.totpEnabled,
    required this.backupCodesCount,
    required this.backupCodesUsed,
    required this.trustedDevicesCount,
    required this.activeMfaSessions,
    this.lastMfaActivity,
    required this.securityScore,
  });

  factory MFAUserStats.fromJson(Map<String, dynamic> json) =>
      _$MFAUserStatsFromJson(json);

  Map<String, dynamic> toJson() => _$MFAUserStatsToJson(this);

  bool get hasMFA => totpEnabled || backupCodesCount > 0;
  bool get hasActiveSessions => activeMfaSessions > 0;
  int get availableBackupCodes => backupCodesCount - backupCodesUsed;
  
  String get securityGrade {
    if (securityScore >= 0.9) return 'A';
    if (securityScore >= 0.8) return 'B';
    if (securityScore >= 0.6) return 'C';
    if (securityScore >= 0.4) return 'D';
    return 'F';
  }

  String get securityDescription {
    if (securityScore >= 0.9) return 'Excellent security setup';
    if (securityScore >= 0.8) return 'Good security setup';
    if (securityScore >= 0.6) return 'Fair security setup';
    if (securityScore >= 0.4) return 'Basic security setup';
    return 'Poor security setup - needs improvement';
  }
}

// Request/Response DTOs

@JsonSerializable()
class CreateMFAChallengeRequest {
  @JsonKey(name: 'user_id')
  final String userId;
  final MFAMethod method;
  final String? destination;
  @JsonKey(name: 'valid_duration_minutes')
  final int? validDurationMinutes;
  @JsonKey(name: 'max_attempts')
  final int? maxAttempts;
  final Map<String, dynamic>? metadata;

  const CreateMFAChallengeRequest({
    required this.userId,
    required this.method,
    this.destination,
    this.validDurationMinutes,
    this.maxAttempts,
    this.metadata,
  });

  factory CreateMFAChallengeRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateMFAChallengeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateMFAChallengeRequestToJson(this);
}

@JsonSerializable()
class VerifyMFAChallengeRequest {
  @JsonKey(name: 'challenge_id')
  final String challengeId;
  @JsonKey(name: 'user_id')
  final String userId;
  final MFAMethod method;
  @JsonKey(name: 'verification_code')
  final String verificationCode;
  @JsonKey(name: 'device_fingerprint')
  final String? deviceFingerprint;
  @JsonKey(name: 'trust_device')
  final bool trustDevice;

  const VerifyMFAChallengeRequest({
    required this.challengeId,
    required this.userId,
    required this.method,
    required this.verificationCode,
    this.deviceFingerprint,
    this.trustDevice = false,
  });

  factory VerifyMFAChallengeRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifyMFAChallengeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$VerifyMFAChallengeRequestToJson(this);
}

@JsonSerializable()
class EvaluateMFARequirementRequest {
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'organization_id')
  final String? organizationId;
  @JsonKey(name: 'device_fingerprint')
  final String? deviceFingerprint;
  final Map<String, dynamic>? context;

  const EvaluateMFARequirementRequest({
    required this.userId,
    this.organizationId,
    this.deviceFingerprint,
    this.context,
  });

  factory EvaluateMFARequirementRequest.fromJson(Map<String, dynamic> json) =>
      _$EvaluateMFARequirementRequestFromJson(json);

  Map<String, dynamic> toJson() => _$EvaluateMFARequirementRequestToJson(this);
}

@JsonSerializable()
class VerifyPhoneRequest {
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'phone_number')
  final String phoneNumber;

  const VerifyPhoneRequest({
    required this.userId,
    required this.phoneNumber,
  });

  factory VerifyPhoneRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifyPhoneRequestFromJson(json);

  Map<String, dynamic> toJson() => _$VerifyPhoneRequestToJson(this);
}

@JsonSerializable()
class VerifyEmailRequest {
  @JsonKey(name: 'user_id')
  final String userId;
  final String email;

  const VerifyEmailRequest({
    required this.userId,
    required this.email,
  });

  factory VerifyEmailRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifyEmailRequestFromJson(json);

  Map<String, dynamic> toJson() => _$VerifyEmailRequestToJson(this);
}

@JsonSerializable()
class DisableMFARequest {
  @JsonKey(name: 'user_id')
  final String userId;
  final String? reason;
  @JsonKey(name: 'verification_code')
  final String? verificationCode;

  const DisableMFARequest({
    required this.userId,
    this.reason,
    this.verificationCode,
  });

  factory DisableMFARequest.fromJson(Map<String, dynamic> json) =>
      _$DisableMFARequestFromJson(json);

  Map<String, dynamic> toJson() => _$DisableMFARequestToJson(this);
}

// Response DTOs

@JsonSerializable()
class MFASetupStatusResponse {
  final MFASetupStatus data;
  final String message;
  final bool success;

  const MFASetupStatusResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory MFASetupStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$MFASetupStatusResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MFASetupStatusResponseToJson(this);
}

@JsonSerializable()
class MFAEnforcementResponse {
  final MFAEnforcementResult data;
  final String message;
  final bool success;

  const MFAEnforcementResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory MFAEnforcementResponse.fromJson(Map<String, dynamic> json) =>
      _$MFAEnforcementResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MFAEnforcementResponseToJson(this);
}

@JsonSerializable()
class MFAChallengeResponse {
  final Map<String, dynamic> data;
  final String message;
  final bool success;

  const MFAChallengeResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory MFAChallengeResponse.fromJson(Map<String, dynamic> json) =>
      _$MFAChallengeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MFAChallengeResponseToJson(this);
}

@JsonSerializable()
class MFAVerificationResponse {
  final MFAVerificationResult data;
  final String message;
  final bool success;

  const MFAVerificationResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory MFAVerificationResponse.fromJson(Map<String, dynamic> json) =>
      _$MFAVerificationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MFAVerificationResponseToJson(this);
}

@JsonSerializable()
class MFASettingsResponse {
  final MFASettings data;
  final String message;
  final bool success;

  const MFASettingsResponse({
    required this.data,
    required this.message,
    this.success = true,
  });

  factory MFASettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$MFASettingsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MFASettingsResponseToJson(this);
}

/// Device Registration Request
@JsonSerializable()
class DeviceRegistrationRequest {
  @JsonKey(name: 'device_fingerprint')
  final String deviceFingerprint;
  @JsonKey(name: 'device_name')
  final String deviceName;
  @JsonKey(name: 'device_type')
  final String deviceType;
  @JsonKey(name: 'ip_address')
  final String ipAddress;
  @JsonKey(name: 'user_agent')
  final String? userAgent;
  @JsonKey(name: 'trust_immediately')
  final bool trustImmediately;

  const DeviceRegistrationRequest({
    required this.deviceFingerprint,
    required this.deviceName,
    required this.deviceType,
    required this.ipAddress,
    this.userAgent,
    this.trustImmediately = false,
  });

  factory DeviceRegistrationRequest.fromJson(Map<String, dynamic> json) =>
      _$DeviceRegistrationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceRegistrationRequestToJson(this);
}

/// MFA Verification Request
@JsonSerializable()
class MFAVerificationRequest {
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'method')
  final MFAMethod method;
  @JsonKey(name: 'code')
  final String code;
  @JsonKey(name: 'challenge_id')
  final String? challengeId;

  const MFAVerificationRequest({
    required this.userId,
    required this.method,
    required this.code,
    this.challengeId,
  });

  factory MFAVerificationRequest.fromJson(Map<String, dynamic> json) =>
      _$MFAVerificationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$MFAVerificationRequestToJson(this);
}

/// Device Verification Result
@JsonSerializable()
class DeviceVerificationResult {
  @JsonKey(name: 'is_trusted')
  final bool isTrusted;
  @JsonKey(name: 'requires_mfa')
  final bool requiresMFA;
  @JsonKey(name: 'device')
  final Map<String, dynamic>? device;

  const DeviceVerificationResult({
    required this.isTrusted,
    required this.requiresMFA,
    this.device,
  });

  factory DeviceVerificationResult.fromJson(Map<String, dynamic> json) =>
      _$DeviceVerificationResultFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceVerificationResultToJson(this);
}

/// TOTP Setup 
@JsonSerializable()
class TOTPSetup {
  @JsonKey(name: 'secret')
  final String secret;
  @JsonKey(name: 'qr_code')
  final String qrCode;
  @JsonKey(name: 'backup_codes')
  final List<String> backupCodes;

  const TOTPSetup({
    required this.secret,
    required this.qrCode,
    required this.backupCodes,
  });

  factory TOTPSetup.fromJson(Map<String, dynamic> json) =>
      _$TOTPSetupFromJson(json);

  Map<String, dynamic> toJson() => _$TOTPSetupToJson(this);
}

/// TOTP Verification Result
@JsonSerializable()
class TOTPVerificationResult {
  @JsonKey(name: 'is_valid')
  final bool isValid;
  @JsonKey(name: 'message')
  final String? message;

  const TOTPVerificationResult({
    required this.isValid,
    this.message,
  });

  factory TOTPVerificationResult.fromJson(Map<String, dynamic> json) =>
      _$TOTPVerificationResultFromJson(json);

  Map<String, dynamic> toJson() => _$TOTPVerificationResultToJson(this);
}

/// TOTP Validation Result
@JsonSerializable()
class TOTPValidationResult {
  @JsonKey(name: 'is_valid')
  final bool isValid;
  @JsonKey(name: 'remaining_attempts')
  final int? remainingAttempts;

  const TOTPValidationResult({
    required this.isValid,
    this.remainingAttempts,
  });

  factory TOTPValidationResult.fromJson(Map<String, dynamic> json) =>
      _$TOTPValidationResultFromJson(json);

  Map<String, dynamic> toJson() => _$TOTPValidationResultToJson(this);
}

/// Additional enums and types
enum DeviceType { desktop, mobile, tablet, unknown }
enum RecoveryMethod { email, sms, backupCode }
enum UnlockReason { userRequest, adminUnlock, automaticExpiry }
enum RecoveryStatus { pending, completed, failed }