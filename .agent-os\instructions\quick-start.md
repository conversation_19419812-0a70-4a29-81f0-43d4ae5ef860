# Quester Agent OS Quick Start Guide

## 🚀 Development Environment Setup

### Prerequisites
- Docker Desktop (for development environment)
- Dart SDK 3.8.1+
- Flutter SDK 3.32.8+

### Quick Setup Commands
```bash
# Initialize project and dependencies
bash auto-setup.sh

# Start development environment
bash docker.sh dev start

# Check service health
bash docker.sh health

# Validate project setup
bash validate-project.sh
```

## 📦 Package Development

### Shared Package
```bash
cd shared/
dart pub get                    # Install dependencies
dart run build_runner build    # Generate JSON serialization
dart test                      # Run unit tests
```

### Server Package  
```bash
cd server/
dart pub get                    # Install dependencies
dart run bin/server.dart       # Start development server
dart test                      # Run API tests
```

### Client Package
```bash
cd client/
flutter pub get                 # Install dependencies
flutter run -d web-server      # Start development server
flutter test                   # Run widget tests
```

## 🐳 Service URLs (When Running)

- **Client Application:** http://localhost:3000
- **Server API:** http://localhost:8080
- **Database Admin:** http://localhost:5050 (pgAdmin)
- **Cache Manager:** http://localhost:8081 (Redis Commander)
- **Mail Testing:** http://localhost:8025 (MailHog)
- **File Storage:** http://localhost:9001 (MinIO Console)

## 🔧 Common Development Tasks

### API Development
```bash
# Test API endpoints
curl http://localhost:8080/health
curl http://localhost:8080/api/v1/gamification/achievements

# Monitor server logs
bash docker.sh logs server --follow
```

### Database Management
```bash
# Access PostgreSQL via pgAdmin
open http://localhost:5050

# Run database migrations
bash docker.sh exec postgres psql -U quester -d quester_db -f /init-scripts/01-init.sql
```

### Testing Workflows
```bash
# Run all tests
bash validate-project.sh

# Run specific package tests
cd shared && dart test
cd server && dart test  
cd client && flutter test
```

## 🎯 Agent OS Compatible Commands

### Feature Development
```bash
# Implement new gamification feature
agent-os implement-feature --type=gamification --name="daily_challenges"

# Add new authentication method
agent-os implement-feature --type=auth --name="social_login"
```

### Analysis & Validation
```bash
# Analyze codebase structure
agent-os analyze-product

# Validate project health
agent-os validate-project

# Generate performance report
agent-os analyze-performance
```

### Deployment Management  
```bash
# Deploy to staging
agent-os deploy --env=staging

# Deploy to production
agent-os deploy --env=production

# Rollback deployment
agent-os rollback --env=production --version=previous
```

## 🐛 Troubleshooting

### Common Issues

**Docker Services Not Starting**
```bash
# Check Docker status
docker --version
docker-compose --version

# Restart Docker Desktop
# Then try: bash docker.sh dev start
```

**Flutter Web Build Issues**
```bash
cd client/
flutter clean
flutter pub get
flutter run -d web-server --web-port=3000
```

**Database Connection Issues**
```bash
# Check PostgreSQL container
bash docker.sh logs postgres

# Reset database
bash docker.sh restart postgres
```

**Port Conflicts**
```bash
# Check port usage
netstat -tulpn | grep :3000
netstat -tulpn | grep :8080

# Kill processes on port
npx kill-port 3000
npx kill-port 8080
```

## 📊 Development Status

**Current Completion: 80%**
- ✅ Shared Package: 100%
- ✅ Server APIs: 90%  
- 🚧 Client UI: 75%
- ❌ Database Layer: 20%
- ✅ Infrastructure: 95%

**Next Priorities:**
1. Complete database integration
2. Finish authentication flows
3. Polish gamification UI
4. Add real-time features
5. Production deployment setup

## 🔗 Useful Resources

- **Project Documentation:** `CLAUDE.md`
- **Architecture Overview:** `agents.md`
- **Framework Details:** `FRAMEWORK.md`
- **Development Instructions:** `Instructions.md`

---

**Agent OS Integration Status:** ✅ Fully Compatible  
**Last Updated:** August 26, 2025