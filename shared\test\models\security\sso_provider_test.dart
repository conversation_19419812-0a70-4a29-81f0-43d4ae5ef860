import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('SSOProvider Tests', () {
    late SSOProvider testProvider;
    
    setUp(() {
      testProvider = SSOProvider(
        id: 'sso_123',
        organizationId: 'org_456',
        providerName: 'Google OAuth',
        providerType: SSOProviderType.oauth2,
        providerConfig: {
          'client_id': 'test_client_id',
          'client_secret': 'test_client_secret',
          'authorization_url': 'https://accounts.google.com/oauth/authorize',
          'token_url': 'https://oauth2.googleapis.com/token',
        },
        metadataUrl: 'https://accounts.google.com/.well-known/openid_connect_configuration',
        entityId: 'test_entity_123',
        certificate: null,
        isActive: true,
        isPrimary: false,
        createdAt: DateTime.parse('2025-01-15T10:00:00.000Z'),
        updatedAt: DateTime.parse('2025-01-15T10:00:00.000Z'),
        createdBy: 'user_123',
      );
    });

    test('should create valid SSOProvider instance', () {
      expect(testProvider.id, equals('sso_123'));
      expect(testProvider.organizationId, equals('org_456'));
      expect(testProvider.providerName, equals('Google OAuth'));
      expect(testProvider.providerType, equals(SSOProviderType.oauth2));
      expect(testProvider.isActive, isTrue);
      expect(testProvider.isPrimary, isFalse);
    });

    test('should create empty SSOProvider for testing', () {
      final empty = SSOProvider.empty();
      expect(empty.id, isEmpty);
      expect(empty.organizationId, isEmpty);
      expect(empty.providerName, isEmpty);
      expect(empty.providerType, equals(SSOProviderType.oauth2));
      expect(empty.isActive, isTrue);
      expect(empty.isPrimary, isFalse);
      expect(empty.providerConfig, isEmpty);
    });

    test('should validate OAuth2 configuration correctly', () {
      expect(testProvider.isConfigurationValid, isTrue);
      
      final invalidProvider = testProvider.copyWith(
        providerConfig: {'client_id': 'test'}, // Missing required fields
      );
      expect(invalidProvider.isConfigurationValid, isFalse);
    });

    test('should validate SAML configuration correctly', () {
      final samlProvider = testProvider.copyWith(
        providerType: SSOProviderType.saml,
        providerConfig: {
          'entity_id': 'test_entity',
          'sso_url': 'https://example.com/saml/sso',
          'certificate': 'test_cert',
        },
      );
      expect(samlProvider.isConfigurationValid, isTrue);

      final invalidSamlProvider = samlProvider.copyWith(
        providerConfig: {'entity_id': 'test'}, // Missing required fields
      );
      expect(invalidSamlProvider.isConfigurationValid, isFalse);
    });

    test('should validate OIDC configuration correctly', () {
      final oidcProvider = testProvider.copyWith(
        providerType: SSOProviderType.oidc,
        providerConfig: {
          'client_id': 'test_client',
          'client_secret': 'test_secret',
          'discovery_url': 'https://example.com/.well-known/openid_connect_configuration',
        },
      );
      expect(oidcProvider.isConfigurationValid, isTrue);
    });

    test('should return correct required configuration keys for OAuth2', () {
      expect(testProvider.requiredConfigKeys, contains('client_id'));
      expect(testProvider.requiredConfigKeys, contains('client_secret'));
      expect(testProvider.requiredConfigKeys, contains('authorization_url'));
      expect(testProvider.requiredConfigKeys, contains('token_url'));
    });

    test('should return correct required configuration keys for SAML', () {
      final samlProvider = testProvider.copyWith(providerType: SSOProviderType.saml);
      expect(samlProvider.requiredConfigKeys, contains('entity_id'));
      expect(samlProvider.requiredConfigKeys, contains('sso_url'));
      expect(samlProvider.requiredConfigKeys, contains('certificate'));
      expect(samlProvider.requiredConfigKeys, contains('signature_algorithm'));
    });

    test('should return correct display names for provider types', () {
      expect(SSOProviderType.saml.displayName, equals('SAML 2.0'));
      expect(SSOProviderType.oauth2.displayName, equals('OAuth 2.0'));
      expect(SSOProviderType.oidc.displayName, equals('OpenID Connect'));
    });

    test('should serialize to JSON correctly', () {
      final json = testProvider.toJson();
      expect(json['id'], equals('sso_123'));
      expect(json['organization_id'], equals('org_456'));
      expect(json['provider_name'], equals('Google OAuth'));
      expect(json['provider_type'], equals('oauth2'));
      expect(json['is_active'], isTrue);
      expect(json['is_primary'], isFalse);
      expect(json['provider_config'], isA<Map<String, dynamic>>());
      expect(json['created_at'], isA<String>());
      expect(json['updated_at'], isA<String>());
    });

    test('should deserialize from JSON correctly', () {
      final json = testProvider.toJson();
      final deserialized = SSOProvider.fromJson(json);
      
      expect(deserialized.id, equals(testProvider.id));
      expect(deserialized.organizationId, equals(testProvider.organizationId));
      expect(deserialized.providerName, equals(testProvider.providerName));
      expect(deserialized.providerType, equals(testProvider.providerType));
      expect(deserialized.providerConfig, equals(testProvider.providerConfig));
      expect(deserialized.isActive, equals(testProvider.isActive));
      expect(deserialized.isPrimary, equals(testProvider.isPrimary));
    });

    test('should handle JSON deserialization with invalid provider type', () {
      final json = testProvider.toJson();
      json['provider_type'] = 'invalid_type';
      
      final deserialized = SSOProvider.fromJson(json);
      expect(deserialized.providerType, equals(SSOProviderType.oauth2)); // Default fallback
    });

    test('should create copy with updated fields', () {
      final updated = testProvider.copyWith(
        providerName: 'Updated Google OAuth',
        isActive: false,
        isPrimary: true,
      );
      
      expect(updated.providerName, equals('Updated Google OAuth'));
      expect(updated.isActive, isFalse);
      expect(updated.isPrimary, isTrue);
      expect(updated.id, equals(testProvider.id)); // Unchanged
      expect(updated.organizationId, equals(testProvider.organizationId)); // Unchanged
    });

    test('should maintain equality for identical instances', () {
      final identical = SSOProvider(
        id: testProvider.id,
        organizationId: testProvider.organizationId,
        providerName: testProvider.providerName,
        providerType: testProvider.providerType,
        providerConfig: testProvider.providerConfig,
        metadataUrl: testProvider.metadataUrl,
        entityId: testProvider.entityId,
        certificate: testProvider.certificate,
        isActive: testProvider.isActive,
        isPrimary: testProvider.isPrimary,
        createdAt: testProvider.createdAt,
        updatedAt: testProvider.updatedAt,
        createdBy: testProvider.createdBy,
      );
      
      expect(testProvider, equals(identical));
    });

    test('should not be equal for different instances', () {
      final different = testProvider.copyWith(id: 'different_id');
      expect(testProvider, isNot(equals(different)));
    });

    test('should have string representation enabled', () {
      expect(testProvider.toString(), isA<String>());
      expect(testProvider.toString(), contains('SSOProvider'));
    });

    group('Edge Cases', () {
      test('should handle null optional fields in JSON', () {
        final json = {
          'id': 'test_id',
          'organization_id': 'org_123',
          'provider_name': 'Test Provider',
          'provider_type': 'oauth2',
          'provider_config': {},
          'metadata_url': null,
          'entity_id': null,
          'certificate': null,
          'is_active': true,
          'is_primary': false,
          'created_at': '2025-01-15T10:00:00.000Z',
          'updated_at': '2025-01-15T10:00:00.000Z',
          'created_by': null,
        };
        
        final provider = SSOProvider.fromJson(json);
        expect(provider.metadataUrl, isNull);
        expect(provider.entityId, isNull);
        expect(provider.certificate, isNull);
        expect(provider.createdBy, isNull);
      });

      test('should handle empty provider config', () {
        final provider = testProvider.copyWith(providerConfig: {});
        expect(provider.isConfigurationValid, isFalse);
        expect(provider.providerConfig, isEmpty);
      });

      test('should handle complex provider config', () {
        final complexConfig = {
          'client_id': 'test_client_id',
          'client_secret': 'test_client_secret',
          'authorization_url': 'https://accounts.google.com/oauth/authorize',
          'token_url': 'https://oauth2.googleapis.com/token',
          'scopes': ['openid', 'profile', 'email'],
          'additional_params': {
            'access_type': 'offline',
            'prompt': 'consent',
          },
        };
        
        final provider = testProvider.copyWith(providerConfig: complexConfig);
        expect(provider.isConfigurationValid, isTrue);
        expect(provider.providerConfig['scopes'], isA<List>());
        expect(provider.providerConfig['additional_params'], isA<Map>());
      });
    });

    group('Validation Tests', () {
      test('should validate required fields for each provider type', () {
        // OAuth2 validation
        expect(testProvider.requiredConfigKeys.length, greaterThan(0));
        
        // SAML validation
        final samlProvider = testProvider.copyWith(providerType: SSOProviderType.saml);
        expect(samlProvider.requiredConfigKeys, contains('entity_id'));
        expect(samlProvider.requiredConfigKeys, contains('sso_url'));
        
        // OIDC validation
        final oidcProvider = testProvider.copyWith(providerType: SSOProviderType.oidc);
        expect(oidcProvider.requiredConfigKeys, contains('discovery_url'));
      });

      test('should handle partial configuration validation', () {
        final partialConfig = {
          'client_id': 'test_client_id',
          // Missing client_secret, authorization_url, token_url
        };
        
        final provider = testProvider.copyWith(providerConfig: partialConfig);
        expect(provider.isConfigurationValid, isFalse);
      });
    });
  });
}