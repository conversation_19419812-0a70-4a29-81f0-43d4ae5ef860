import 'package:shelf/shelf.dart';

/// Simple in-memory rate limiter middleware
class RateLimiter {
  final Map<String, List<DateTime>> _requests = {};
  final int maxRequests;
  final Duration timeWindow;

  RateLimiter({
    this.maxRequests = 100,
    this.timeWindow = const Duration(minutes: 1),
  });

  /// Create rate limiting middleware
  Middleware get middleware => (Handler innerHandler) {
    return (Request request) async {
      final clientId = _getClientId(request);
      
      if (_isRateLimited(clientId)) {
        return Response(429, 
          headers: {'Content-Type': 'application/json'},
          body: '{"error": "Rate limit exceeded", "errorCode": "RATE_LIMIT_EXCEEDED"}',
        );
      }

      _recordRequest(clientId);
      return await innerHandler(request);
    };
  };

  String _getClientId(Request request) {
    // Use IP address as client identifier
    final forwarded = request.headers['x-forwarded-for'];
    if (forwarded != null) {
      return forwarded.split(',').first.trim();
    }
    // Fallback to a generic identifier
    return request.headers['user-agent'] ?? 'unknown';
  }

  bool _isRateLimited(String clientId) {
    final now = DateTime.now();
    final requests = _requests[clientId] ?? [];
    
    // Remove old requests outside the time window
    requests.removeWhere((time) => now.difference(time) > timeWindow);
    
    return requests.length >= maxRequests;
  }

  void _recordRequest(String clientId) {
    final now = DateTime.now();
    _requests[clientId] ??= [];
    _requests[clientId]!.add(now);
  }

  /// Create stricter rate limiter for authentication endpoints
  static RateLimiter auth() => RateLimiter(
    maxRequests: 5,
    timeWindow: Duration(minutes: 1),
  );

  /// Create moderate rate limiter for API endpoints
  static RateLimiter api() => RateLimiter(
    maxRequests: 60,
    timeWindow: Duration(minutes: 1),
  );
}
