// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AdminUser _$AdminUserFromJson(Map<String, dynamic> json) => AdminUser(
  user: User.fromJson(json['user'] as Map<String, dynamic>),
  adminLevel: $enumDecode(_$AdminLevelEnumMap, json['adminLevel']),
  systemPermissions: (json['systemPermissions'] as List<dynamic>)
      .map((e) => $enumDecode(_$SystemPermissionEnumMap, e))
      .toSet(),
  organizationAccess: (json['organizationAccess'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  adminSettings: AdminSettings.fromJson(
    json['adminSettings'] as Map<String, dynamic>,
  ),
  lastAdminActivity: json['lastAdminActivity'] == null
      ? null
      : DateTime.parse(json['lastAdminActivity'] as String),
  adminCreatedAt: DateTime.parse(json['adminCreatedAt'] as String),
  adminStatus: $enumDecode(_$AdminStatusEnumMap, json['adminStatus']),
  adminMetadata: json['adminMetadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$AdminUserToJson(AdminUser instance) => <String, dynamic>{
  'user': instance.user,
  'adminLevel': _$AdminLevelEnumMap[instance.adminLevel]!,
  'systemPermissions': instance.systemPermissions
      .map((e) => _$SystemPermissionEnumMap[e]!)
      .toList(),
  'organizationAccess': instance.organizationAccess,
  'adminSettings': instance.adminSettings,
  'lastAdminActivity': instance.lastAdminActivity?.toIso8601String(),
  'adminCreatedAt': instance.adminCreatedAt.toIso8601String(),
  'adminStatus': _$AdminStatusEnumMap[instance.adminStatus]!,
  'adminMetadata': instance.adminMetadata,
};

const _$AdminLevelEnumMap = {
  AdminLevel.superAdmin: 'super_admin',
  AdminLevel.systemAdmin: 'system_admin',
  AdminLevel.organizationAdmin: 'organization_admin',
  AdminLevel.supportAdmin: 'support_admin',
};

const _$SystemPermissionEnumMap = {
  SystemPermission.userManagement: 'user_management',
  SystemPermission.userImpersonation: 'user_impersonation',
  SystemPermission.userDataExport: 'user_data_export',
  SystemPermission.organizationManagement: 'organization_management',
  SystemPermission.organizationBilling: 'organization_billing',
  SystemPermission.organizationDeletion: 'organization_deletion',
  SystemPermission.systemLogs: 'system_logs',
  SystemPermission.systemHealth: 'system_health',
  SystemPermission.systemMaintenance: 'system_maintenance',
  SystemPermission.databaseAccess: 'database_access',
  SystemPermission.billingManagement: 'billing_management',
  SystemPermission.subscriptionManagement: 'subscription_management',
  SystemPermission.paymentProcessing: 'payment_processing',
  SystemPermission.supportTickets: 'support_tickets',
  SystemPermission.userAssistance: 'user_assistance',
  SystemPermission.chatSupport: 'chat_support',
  SystemPermission.globalAnalytics: 'global_analytics',
  SystemPermission.systemReports: 'system_reports',
  SystemPermission.dataExports: 'data_exports',
  SystemPermission.featureFlags: 'feature_flags',
  SystemPermission.abTesting: 'ab_testing',
  SystemPermission.configurationManagement: 'configuration_management',
};

const _$AdminStatusEnumMap = {
  AdminStatus.active: 'active',
  AdminStatus.inactive: 'inactive',
  AdminStatus.suspended: 'suspended',
  AdminStatus.pendingReview: 'pending_review',
};

AdminSettings _$AdminSettingsFromJson(Map<String, dynamic> json) =>
    AdminSettings(
      dashboardLayout: json['dashboardLayout'] as Map<String, dynamic>,
      notifications: AdminNotificationSettings.fromJson(
        json['notifications'] as Map<String, dynamic>,
      ),
      security: AdminSecuritySettings.fromJson(
        json['security'] as Map<String, dynamic>,
      ),
      display: AdminDisplaySettings.fromJson(
        json['display'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$AdminSettingsToJson(AdminSettings instance) =>
    <String, dynamic>{
      'dashboardLayout': instance.dashboardLayout,
      'notifications': instance.notifications,
      'security': instance.security,
      'display': instance.display,
    };

AdminNotificationSettings _$AdminNotificationSettingsFromJson(
  Map<String, dynamic> json,
) => AdminNotificationSettings(
  systemHealthAlerts: json['systemHealthAlerts'] as bool,
  userActivityAlerts: json['userActivityAlerts'] as bool,
  securityAlerts: json['securityAlerts'] as bool,
  billingAlerts: json['billingAlerts'] as bool,
  supportTicketAlerts: json['supportTicketAlerts'] as bool,
  emailNotifications: json['emailNotifications'] as bool,
  pushNotifications: json['pushNotifications'] as bool,
);

Map<String, dynamic> _$AdminNotificationSettingsToJson(
  AdminNotificationSettings instance,
) => <String, dynamic>{
  'systemHealthAlerts': instance.systemHealthAlerts,
  'userActivityAlerts': instance.userActivityAlerts,
  'securityAlerts': instance.securityAlerts,
  'billingAlerts': instance.billingAlerts,
  'supportTicketAlerts': instance.supportTicketAlerts,
  'emailNotifications': instance.emailNotifications,
  'pushNotifications': instance.pushNotifications,
};

AdminSecuritySettings _$AdminSecuritySettingsFromJson(
  Map<String, dynamic> json,
) => AdminSecuritySettings(
  requireTwoFactor: json['requireTwoFactor'] as bool,
  sessionTimeoutMinutes: (json['sessionTimeoutMinutes'] as num).toInt(),
  requirePasswordConfirmation: json['requirePasswordConfirmation'] as bool,
  logAllActions: json['logAllActions'] as bool,
  allowedIpRanges: (json['allowedIpRanges'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$AdminSecuritySettingsToJson(
  AdminSecuritySettings instance,
) => <String, dynamic>{
  'requireTwoFactor': instance.requireTwoFactor,
  'sessionTimeoutMinutes': instance.sessionTimeoutMinutes,
  'requirePasswordConfirmation': instance.requirePasswordConfirmation,
  'logAllActions': instance.logAllActions,
  'allowedIpRanges': instance.allowedIpRanges,
};

AdminDisplaySettings _$AdminDisplaySettingsFromJson(
  Map<String, dynamic> json,
) => AdminDisplaySettings(
  theme: json['theme'] as String,
  defaultPageSize: (json['defaultPageSize'] as num).toInt(),
  showAdvancedFeatures: json['showAdvancedFeatures'] as bool,
  timezone: json['timezone'] as String,
  language: json['language'] as String,
);

Map<String, dynamic> _$AdminDisplaySettingsToJson(
  AdminDisplaySettings instance,
) => <String, dynamic>{
  'theme': instance.theme,
  'defaultPageSize': instance.defaultPageSize,
  'showAdvancedFeatures': instance.showAdvancedFeatures,
  'timezone': instance.timezone,
  'language': instance.language,
};
