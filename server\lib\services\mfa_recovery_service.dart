import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
// import 'package:crypto/crypto.dart'; // Not currently used 
import '../services/database_service.dart';
import 'mfa_delivery_service.dart';

enum RecoveryMethod { email, sms, adminApproval, securityQuestions, identityVerification }
enum RecoveryStatus { initiated, pendingVerification, underReview, approved, rejected, completed, expired }
enum UnlockReason { forgotMFA, lostDevice, suspiciousActivity, adminRequest, userRequest }

class MFARecoveryRequest {
  final String id;
  final String userId;
  final RecoveryMethod method;
  final String recoveryDestination;
  final String verificationCode;
  final RecoveryStatus status;
  final UnlockReason reason;
  final DateTime createdAt;
  final DateTime expiresAt;
  final DateTime? verifiedAt;
  final DateTime? completedAt;
  final int attemptCount;
  final int maxAttempts;
  final String? ipAddress;
  final String? userAgent;
  final Map<String, dynamic> metadata;
  final String? reviewedBy;
  final String? reviewNotes;

  MFARecoveryRequest({
    required this.id,
    required this.userId,
    required this.method,
    required this.recoveryDestination,
    required this.verificationCode,
    this.status = RecoveryStatus.initiated,
    required this.reason,
    required this.createdAt,
    required this.expiresAt,
    this.verifiedAt,
    this.completedAt,
    this.attemptCount = 0,
    this.maxAttempts = 3,
    this.ipAddress,
    this.userAgent,
    this.metadata = const {},
    this.reviewedBy,
    this.reviewNotes,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get hasAttemptsRemaining => attemptCount < maxAttempts;
  bool get isCompleted => status == RecoveryStatus.completed;
  bool get needsReview => method == RecoveryMethod.adminApproval && status == RecoveryStatus.underReview;

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'method': method.name,
    'recovery_destination': recoveryDestination,
    'verification_code': verificationCode,
    'status': status.name,
    'reason': reason.name,
    'created_at': createdAt.toIso8601String(),
    'expires_at': expiresAt.toIso8601String(),
    'verified_at': verifiedAt?.toIso8601String(),
    'completed_at': completedAt?.toIso8601String(),
    'attempt_count': attemptCount,
    'max_attempts': maxAttempts,
    'ip_address': ipAddress,
    'user_agent': userAgent,
    'metadata': metadata,
    'reviewed_by': reviewedBy,
    'review_notes': reviewNotes,
  };

  factory MFARecoveryRequest.fromJson(Map<String, dynamic> json) => MFARecoveryRequest(
    id: json['id'],
    userId: json['user_id'],
    method: RecoveryMethod.values.byName(json['method']),
    recoveryDestination: json['recovery_destination'],
    verificationCode: json['verification_code'],
    status: RecoveryStatus.values.byName(json['status'] ?? 'initiated'),
    reason: UnlockReason.values.byName(json['reason']),
    createdAt: DateTime.parse(json['created_at']),
    expiresAt: DateTime.parse(json['expires_at']),
    verifiedAt: json['verified_at'] != null ? DateTime.parse(json['verified_at']) : null,
    completedAt: json['completed_at'] != null ? DateTime.parse(json['completed_at']) : null,
    attemptCount: json['attempt_count'] ?? 0,
    maxAttempts: json['max_attempts'] ?? 3,
    ipAddress: json['ip_address'],
    userAgent: json['user_agent'],
    metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    reviewedBy: json['reviewed_by'],
    reviewNotes: json['review_notes'],
  );
}

class RecoveryInitiationResult {
  final bool success;
  final String? recoveryId;
  final RecoveryMethod method;
  final String? destinationHint;
  final Duration? expiresIn;
  final String message;
  final List<String> requiredSteps;
  final Map<String, dynamic> metadata;

  RecoveryInitiationResult({
    required this.success,
    this.recoveryId,
    required this.method,
    this.destinationHint,
    this.expiresIn,
    required this.message,
    this.requiredSteps = const [],
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() => {
    'success': success,
    'recovery_id': recoveryId,
    'method': method.name,
    'destination_hint': destinationHint,
    'expires_in_minutes': expiresIn?.inMinutes,
    'message': message,
    'required_steps': requiredSteps,
    'metadata': metadata,
  };
}

class RecoveryVerificationResult {
  final bool isValid;
  final bool isCompleted;
  final String? nextStep;
  final String? errorMessage;
  final int attemptsRemaining;
  final Map<String, dynamic> metadata;
  final DateTime verifiedAt;

  RecoveryVerificationResult({
    required this.isValid,
    required this.isCompleted,
    this.nextStep,
    this.errorMessage,
    required this.attemptsRemaining,
    this.metadata = const {},
    required this.verifiedAt,
  });

  Map<String, dynamic> toJson() => {
    'is_valid': isValid,
    'is_completed': isCompleted,
    'next_step': nextStep,
    'error_message': errorMessage,
    'attempts_remaining': attemptsRemaining,
    'metadata': metadata,
    'verified_at': verifiedAt.toIso8601String(),
  };
}

class AccountUnlockResult {
  final bool success;
  final String message;
  final List<String> actionsPerformed;
  final List<String> recommendations;
  final Map<String, dynamic> metadata;
  final DateTime unlockedAt;

  AccountUnlockResult({
    required this.success,
    required this.message,
    this.actionsPerformed = const [],
    this.recommendations = const [],
    this.metadata = const {},
    required this.unlockedAt,
  });

  Map<String, dynamic> toJson() => {
    'success': success,
    'message': message,
    'actions_performed': actionsPerformed,
    'recommendations': recommendations,
    'metadata': metadata,
    'unlocked_at': unlockedAt.toIso8601String(),
  };
}

class MFARecoveryService {
  final DatabaseService _databaseService;
  final MFADeliveryService _deliveryService;
  final Random _secureRandom;
  
  static const String _recoveryRequestsTable = 'mfa_recovery_requests';
  static const String _accountLocksTable = 'account_locks';
  // ignore: unused_field
  static const String _recoveryAttemptsTable = 'recovery_attempts';
  static const int _maxRecoveryAttempts = 3;
  // ignore: unused_field
  static const Duration _defaultRecoveryExpiry = Duration(hours: 24);

  MFARecoveryService(this._databaseService, this._deliveryService) : _secureRandom = Random.secure();

  Future<RecoveryInitiationResult> initiateRecovery(
    String userId, {
    required RecoveryMethod method,
    required UnlockReason reason,
    String? recoveryDestination,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic> additionalData = const {},
  }) async {
    try {
      // Validate user exists and check current lock status
      await _validateUserExists(userId);
      final lockStatus = await _getAccountLockStatus(userId);
      
      if (lockStatus != null && lockStatus['is_permanent'] == true) {
        return RecoveryInitiationResult(
          success: false,
          method: method,
          message: 'Account is permanently locked. Contact support.',
        );
      }

      // Check for existing active recovery requests
      final existingRequest = await _getActiveRecoveryRequest(userId);
      if (existingRequest != null) {
        return RecoveryInitiationResult(
          success: false,
          method: method,
          message: 'Recovery already in progress. Check your ${existingRequest.method.name}.',
          requiredSteps: ['Complete existing recovery request'],
        );
      }

      // Determine recovery destination if not provided
      if (recoveryDestination == null) {
        recoveryDestination = await _determineRecoveryDestination(userId, method);
        if (recoveryDestination == null) {
          return RecoveryInitiationResult(
            success: false,
            method: method,
            message: 'No recovery method available for ${method.name}.',
          );
        }
      }

      // Generate recovery request
      final recoveryId = _generateRecoveryId();
      final verificationCode = _generateVerificationCode(length: 8);
      final expiresAt = DateTime.now().add(_getRecoveryExpiryDuration(method));
      
      final request = MFARecoveryRequest(
        id: recoveryId,
        userId: userId,
        method: method,
        recoveryDestination: recoveryDestination,
        verificationCode: verificationCode,
        status: method == RecoveryMethod.adminApproval ? RecoveryStatus.underReview : RecoveryStatus.initiated,
        reason: reason,
        createdAt: DateTime.now(),
        expiresAt: expiresAt,
        ipAddress: ipAddress,
        userAgent: userAgent,
        metadata: {
          ...additionalData,
          'initiation_ip': ipAddress,
          'user_agent': userAgent,
          'reason_details': reason.name,
        },
      );

      await _storeRecoveryRequest(request);

      // Send recovery notification based on method
      String? destinationHint;
      List<String> requiredSteps = [];

      switch (method) {
        case RecoveryMethod.email:
          await _deliveryService.sendEmailCode(
            userId: userId,
            email: recoveryDestination,
          );
          destinationHint = _maskEmail(recoveryDestination);
          requiredSteps = [
            'Check your email for the verification code',
            'Enter the code to verify your identity',
            'Complete the account recovery process'
          ];
          break;

        case RecoveryMethod.sms:
          await _deliveryService.sendSMSCode(
            userId: userId,
            phoneNumber: recoveryDestination,
          );
          destinationHint = _maskPhoneNumber(recoveryDestination);
          requiredSteps = [
            'Check your SMS for the verification code',
            'Enter the code to verify your identity',
            'Complete the account recovery process'
          ];
          break;

        case RecoveryMethod.adminApproval:
          destinationHint = 'Admin Review Required';
          requiredSteps = [
            'Your request is under admin review',
            'You will be notified once approved',
            'Complete identity verification if requested'
          ];
          break;

        case RecoveryMethod.securityQuestions:
          destinationHint = 'Security Questions';
          requiredSteps = [
            'Answer your security questions',
            'Verify your identity',
            'Reset your MFA settings'
          ];
          break;

        case RecoveryMethod.identityVerification:
          destinationHint = 'Identity Documents';
          requiredSteps = [
            'Provide identity verification documents',
            'Wait for manual review',
            'Complete the recovery process'
          ];
          break;
      }

      await _logSecurityEvent(
        userId: userId,
        eventType: 'mfa_recovery_initiated',
        details: {
          'recovery_id': recoveryId,
          'method': method.name,
          'reason': reason.name,
          'destination_hint': destinationHint,
          'ip_address': ipAddress,
          'user_agent': userAgent,
        },
      );

      return RecoveryInitiationResult(
        success: true,
        recoveryId: recoveryId,
        method: method,
        destinationHint: destinationHint,
        expiresIn: expiresAt.difference(DateTime.now()),
        message: 'Recovery initiated successfully. ${_getRecoveryInstructions(method)}',
        requiredSteps: requiredSteps,
        metadata: {
          'expires_at': expiresAt.toIso8601String(),
          'max_attempts': _maxRecoveryAttempts,
        },
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: userId,
        eventType: 'mfa_recovery_initiation_failed',
        details: {
          'error': e.toString(),
          'method': method.name,
          'reason': reason.name,
          'ip_address': ipAddress,
        },
      );
      
      return RecoveryInitiationResult(
        success: false,
        method: method,
        message: 'Failed to initiate recovery: ${e.toString()}',
      );
    }
  }

  Future<RecoveryVerificationResult> verifyRecovery(
    String recoveryId,
    String verificationCode, {
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic> additionalData = const {},
  }) async {
    try {
      final request = await _getRecoveryRequest(recoveryId);
      if (request == null) {
        return RecoveryVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Invalid or expired recovery request',
          attemptsRemaining: 0,
          verifiedAt: DateTime.now(),
        );
      }

      if (request.isExpired) {
        await _expireRecoveryRequest(recoveryId);
        return RecoveryVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Recovery request has expired',
          attemptsRemaining: 0,
          verifiedAt: DateTime.now(),
        );
      }

      if (!request.hasAttemptsRemaining) {
        return RecoveryVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Maximum verification attempts exceeded',
          attemptsRemaining: 0,
          verifiedAt: DateTime.now(),
        );
      }

      // Increment attempt count
      await _incrementRecoveryAttempt(recoveryId);

      // Verify the code
      bool isCodeValid = false;
      switch (request.method) {
        case RecoveryMethod.email:
        case RecoveryMethod.sms:
          isCodeValid = request.verificationCode == verificationCode.toUpperCase();
          break;

        case RecoveryMethod.adminApproval:
          // Admin approval doesn't use verification codes
          isCodeValid = false;
          break;

        case RecoveryMethod.securityQuestions:
          // Security questions would have different validation
          isCodeValid = await _validateSecurityAnswers(request.userId, verificationCode);
          break;

        case RecoveryMethod.identityVerification:
          // Identity verification requires manual review
          isCodeValid = false;
          break;
      }

      if (!isCodeValid) {
        await _logSecurityEvent(
          userId: request.userId,
          eventType: 'mfa_recovery_verification_failed',
          details: {
            'recovery_id': recoveryId,
            'method': request.method.name,
            'attempts_remaining': request.maxAttempts - request.attemptCount - 1,
            'ip_address': ipAddress,
          },
        );

        return RecoveryVerificationResult(
          isValid: false,
          isCompleted: false,
          errorMessage: 'Invalid verification code',
          attemptsRemaining: request.maxAttempts - request.attemptCount - 1,
          verifiedAt: DateTime.now(),
        );
      }

      // Mark recovery as verified
      await _verifyRecoveryRequest(recoveryId);

      await _logSecurityEvent(
        userId: request.userId,
        eventType: 'mfa_recovery_verified',
        details: {
          'recovery_id': recoveryId,
          'method': request.method.name,
          'ip_address': ipAddress,
          'user_agent': userAgent,
        },
      );

      // Determine next step
      String? nextStep;
      bool isCompleted = false;
      
      switch (request.method) {
        case RecoveryMethod.email:
        case RecoveryMethod.sms:
          nextStep = 'complete_recovery';
          isCompleted = false;
          break;

        case RecoveryMethod.adminApproval:
          nextStep = 'pending_admin_approval';
          isCompleted = false;
          break;

        case RecoveryMethod.securityQuestions:
          nextStep = 'complete_recovery';
          isCompleted = false;
          break;

        case RecoveryMethod.identityVerification:
          nextStep = 'pending_identity_review';
          isCompleted = false;
          break;
      }

      return RecoveryVerificationResult(
        isValid: true,
        isCompleted: isCompleted,
        nextStep: nextStep,
        attemptsRemaining: request.maxAttempts - request.attemptCount - 1,
        metadata: {
          'recovery_method': request.method.name,
          'next_step_description': _getNextStepDescription(nextStep),
        },
        verifiedAt: DateTime.now(),
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: '',
        eventType: 'mfa_recovery_verification_error',
        details: {
          'error': e.toString(),
          'recovery_id': recoveryId,
          'ip_address': ipAddress,
        },
      );
      
      return RecoveryVerificationResult(
        isValid: false,
        isCompleted: false,
        errorMessage: 'Verification failed: ${e.toString()}',
        attemptsRemaining: 0,
        verifiedAt: DateTime.now(),
      );
    }
  }

  Future<AccountUnlockResult> completeRecovery(
    String recoveryId, {
    String? approvedBy,
    String? notes,
    bool resetAllMFA = true,
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      final request = await _getRecoveryRequest(recoveryId);
      if (request == null) {
        return AccountUnlockResult(
          success: false,
          message: 'Recovery request not found',
          unlockedAt: DateTime.now(),
        );
      }

      if (request.status != RecoveryStatus.pendingVerification && request.status != RecoveryStatus.approved) {
        return AccountUnlockResult(
          success: false,
          message: 'Recovery request is not ready for completion',
          unlockedAt: DateTime.now(),
        );
      }

      final actionsPerformed = <String>[];
      final recommendations = <String>[];

      // Unlock the account
      await _unlockAccount(request.userId);
      actionsPerformed.add('Account unlocked');

      // Reset MFA settings if requested
      if (resetAllMFA) {
        await _resetUserMFA(request.userId);
        actionsPerformed.add('MFA settings reset');
        recommendations.add('Set up new MFA methods immediately');
        recommendations.add('Use a secure authenticator app');
        recommendations.add('Generate new backup codes');
      }

      // Revoke all active MFA sessions
      await _revokeAllMFASessions(request.userId, 'Account recovery completed');
      actionsPerformed.add('All MFA sessions revoked');

      // Mark recovery as completed
      await _completeRecoveryRequest(recoveryId, approvedBy, notes);

      // Send completion notification
      await _sendRecoveryCompletionNotification(request);

      await _logSecurityEvent(
        userId: request.userId,
        eventType: 'mfa_recovery_completed',
        details: {
          'recovery_id': recoveryId,
          'method': request.method.name,
          'approved_by': approvedBy,
          'actions_performed': actionsPerformed,
          'reset_all_mfa': resetAllMFA,
          'ip_address': ipAddress,
          'user_agent': userAgent,
        },
      );

      return AccountUnlockResult(
        success: true,
        message: 'Account recovery completed successfully',
        actionsPerformed: actionsPerformed,
        recommendations: [
          ...recommendations,
          'Change your password',
          'Review your security settings',
          'Monitor your account activity',
        ],
        metadata: {
          'recovery_method': request.method.name,
          'completed_by': approvedBy,
        },
        unlockedAt: DateTime.now(),
      );
    } catch (e) {
      await _logSecurityEvent(
        userId: '',
        eventType: 'mfa_recovery_completion_failed',
        details: {
          'error': e.toString(),
          'recovery_id': recoveryId,
          'approved_by': approvedBy,
        },
      );
      
      return AccountUnlockResult(
        success: false,
        message: 'Failed to complete recovery: ${e.toString()}',
        unlockedAt: DateTime.now(),
      );
    }
  }

  Future<List<MFARecoveryRequest>> getPendingRecoveryRequests({String? organizationId}) async {
    try {
      String whereClause = 'status = @status';
      Map<String, dynamic> params = {'status': RecoveryStatus.underReview.name};

      if (organizationId != null) {
        whereClause += ' AND user_id IN (SELECT id FROM users WHERE organization_id = @organizationId)';
        params['organizationId'] = organizationId;
      }

      final result = await _databaseService.query('''
        SELECT * FROM $_recoveryRequestsTable 
        WHERE $whereClause
        ORDER BY created_at ASC
      ''', params);
      
      return result.map((row) => MFARecoveryRequest.fromJson(row)).toList();
    } catch (e) {
      print('Error getting pending recovery requests: $e');
      return [];
    }
  }

  Future<void> approveRecoveryRequest(
    String recoveryId,
    String approvedBy, {
    String? notes,
  }) async {
    try {
      await _databaseService.execute('''
        UPDATE $_recoveryRequestsTable 
        SET 
          status = @status,
          reviewed_by = @reviewedBy,
          review_notes = @notes,
          verified_at = NOW()
        WHERE id = @recoveryId
      ''', parameters: {
        'status': RecoveryStatus.approved.name,
        'reviewedBy': approvedBy,
        'notes': notes,
        'recoveryId': recoveryId,
      });

      await _logSecurityEvent(
        userId: '',
        eventType: 'mfa_recovery_approved',
        details: {
          'recovery_id': recoveryId,
          'approved_by': approvedBy,
          'notes': notes,
        },
      );
    } catch (e) {
      print('Error approving recovery request: $e');
      rethrow;
    }
  }

  Future<void> rejectRecoveryRequest(
    String recoveryId,
    String rejectedBy, {
    String? reason,
  }) async {
    try {
      await _databaseService.execute('''
        UPDATE $_recoveryRequestsTable 
        SET 
          status = @status,
          reviewed_by = @reviewedBy,
          review_notes = @reason
        WHERE id = @recoveryId
      ''', parameters: {
        'status': RecoveryStatus.rejected.name,
        'reviewedBy': rejectedBy,
        'reason': reason,
        'recoveryId': recoveryId,
      });

      await _logSecurityEvent(
        userId: '',
        eventType: 'mfa_recovery_rejected',
        details: {
          'recovery_id': recoveryId,
          'rejected_by': rejectedBy,
          'reason': reason,
        },
      );
    } catch (e) {
      print('Error rejecting recovery request: $e');
      rethrow;
    }
  }

  Future<void> cleanupExpiredRecoveryRequests() async {
    try {
      final result = await _databaseService.execute('''
        UPDATE $_recoveryRequestsTable 
        SET status = @expiredStatus
        WHERE expires_at < NOW() AND status NOT IN (@completedStatus, @rejectedStatus)
      ''', parameters: {
        'expiredStatus': RecoveryStatus.expired.name,
        'completedStatus': RecoveryStatus.completed.name,
        'rejectedStatus': RecoveryStatus.rejected.name,
      });
      
      final affectedRows = result.length;
      if (affectedRows > 0) {
        print('Expired $affectedRows recovery requests');
      }
    } catch (e) {
      print('Error cleaning up expired recovery requests: $e');
    }
  }

  // Helper methods
  String _generateRecoveryId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomBytes = Uint8List(12);
    for (int i = 0; i < 12; i++) {
      randomBytes[i] = _secureRandom.nextInt(256);
    }
    final randomHex = randomBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    return 'recovery_${timestamp}_$randomHex';
  }

  String _generateVerificationCode({int length = 8}) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final code = StringBuffer();
    for (int i = 0; i < length; i++) {
      code.write(charset[_secureRandom.nextInt(charset.length)]);
    }
    return code.toString();
  }

  String _maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2 || parts[0].length < 2) return email;
    return '${parts[0].substring(0, 2)}***@${parts[1]}';
  }

  String _maskPhoneNumber(String phone) {
    if (phone.length < 4) return phone;
    return '***-***-${phone.substring(phone.length - 4)}';
  }

  Duration _getRecoveryExpiryDuration(RecoveryMethod method) {
    switch (method) {
      case RecoveryMethod.email:
      case RecoveryMethod.sms:
        return Duration(hours: 2);
      case RecoveryMethod.adminApproval:
        return Duration(days: 7);
      case RecoveryMethod.securityQuestions:
        return Duration(hours: 1);
      case RecoveryMethod.identityVerification:
        return Duration(days: 14);
    }
  }

  String _getRecoveryInstructions(RecoveryMethod method) {
    switch (method) {
      case RecoveryMethod.email:
        return 'Check your email for the verification code.';
      case RecoveryMethod.sms:
        return 'Check your SMS for the verification code.';
      case RecoveryMethod.adminApproval:
        return 'Your request is under review by an administrator.';
      case RecoveryMethod.securityQuestions:
        return 'Answer your security questions to proceed.';
      case RecoveryMethod.identityVerification:
        return 'Upload identity documents for manual verification.';
    }
  }

  String? _getNextStepDescription(String? nextStep) {
    switch (nextStep) {
      case 'complete_recovery':
        return 'Your identity has been verified. Complete the recovery process to regain access.';
      case 'pending_admin_approval':
        return 'Your request is being reviewed by an administrator. You will be notified of the decision.';
      case 'pending_identity_review':
        return 'Your identity documents are being reviewed. This may take up to 3 business days.';
      default:
        return null;
    }
  }

  Future<void> _validateUserExists(String userId) async {
    final result = await _databaseService.query(
      'SELECT id FROM users WHERE id = @userId',
      {'userId': userId},
    );
    
    if (result.isEmpty) {
      throw Exception('User not found: $userId');
    }
  }

  Future<Map<String, dynamic>?> _getAccountLockStatus(String userId) async {
    final result = await _databaseService.query('''
      SELECT is_locked, is_permanent, locked_reason, locked_at 
      FROM $_accountLocksTable 
      WHERE user_id = @userId
    ''', {'userId': userId});
    
    return result.isNotEmpty ? result.first : null;
  }

  Future<MFARecoveryRequest?> _getActiveRecoveryRequest(String userId) async {
    final result = await _databaseService.query('''
      SELECT * FROM $_recoveryRequestsTable 
      WHERE user_id = @userId AND status NOT IN (@completedStatus, @rejectedStatus, @expiredStatus)
      ORDER BY created_at DESC
      LIMIT 1
    ''', {
      'userId': userId,
      'completedStatus': RecoveryStatus.completed.name,
      'rejectedStatus': RecoveryStatus.rejected.name,
      'expiredStatus': RecoveryStatus.expired.name,
    });
    
    return result.isNotEmpty ? MFARecoveryRequest.fromJson(result.first) : null;
  }

  Future<String?> _determineRecoveryDestination(String userId, RecoveryMethod method) async {
    switch (method) {
      case RecoveryMethod.email:
        final result = await _databaseService.query(
          'SELECT email FROM users WHERE id = @userId',
          {'userId': userId},
        );
        return result.isNotEmpty ? result.first['email'] : null;

      case RecoveryMethod.sms:
        final result = await _databaseService.query(
          'SELECT phone FROM users WHERE id = @userId',
          {'userId': userId},
        );
        return result.isNotEmpty ? result.first['phone'] : null;

      default:
        return 'system';
    }
  }

  Future<MFARecoveryRequest?> _getRecoveryRequest(String recoveryId) async {
    final result = await _databaseService.query('''
      SELECT * FROM $_recoveryRequestsTable 
      WHERE id = @recoveryId
    ''', {'recoveryId': recoveryId});
    
    return result.isNotEmpty ? MFARecoveryRequest.fromJson(result.first) : null;
  }

  Future<void> _storeRecoveryRequest(MFARecoveryRequest request) async {
    await _databaseService.execute('''
      INSERT INTO $_recoveryRequestsTable (
        id, user_id, recovery_method, recovery_destination, verification_code,
        created_at, expires_at, is_completed, attempt_count, max_attempts,
        ip_address, user_agent, metadata
      ) VALUES (
        @id, @userId, @method, @destination, @code, @createdAt, @expiresAt, 
        @isCompleted, @attemptCount, @maxAttempts, @ipAddress, @userAgent, @metadata
      )
    ''', parameters: {
      'id': request.id,
      'userId': request.userId,
      'method': request.method.name,
      'destination': request.recoveryDestination,
      'code': request.verificationCode,
      'createdAt': request.createdAt,
      'expiresAt': request.expiresAt,
      'isCompleted': false,
      'attemptCount': request.attemptCount,
      'maxAttempts': request.maxAttempts,
      'ipAddress': request.ipAddress,
      'userAgent': request.userAgent,
      'metadata': jsonEncode(request.metadata),
    });
  }

  Future<void> _incrementRecoveryAttempt(String recoveryId) async {
    await _databaseService.execute('''
      UPDATE $_recoveryRequestsTable 
      SET attempt_count = attempt_count + 1 
      WHERE id = @recoveryId
    ''', parameters: {'recoveryId': recoveryId});
  }

  Future<void> _verifyRecoveryRequest(String recoveryId) async {
    await _databaseService.execute('''
      UPDATE $_recoveryRequestsTable 
      SET status = @status, verified_at = NOW()
      WHERE id = @recoveryId
    ''', parameters: {
      'status': RecoveryStatus.pendingVerification.name,
      'recoveryId': recoveryId,
    });
  }

  Future<void> _expireRecoveryRequest(String recoveryId) async {
    await _databaseService.execute('''
      UPDATE $_recoveryRequestsTable 
      SET status = @status
      WHERE id = @recoveryId
    ''', parameters: {
      'status': RecoveryStatus.expired.name,
      'recoveryId': recoveryId,
    });
  }

  Future<void> _completeRecoveryRequest(String recoveryId, String? approvedBy, String? notes) async {
    await _databaseService.execute('''
      UPDATE $_recoveryRequestsTable 
      SET 
        status = @status,
        completed_at = NOW(),
        reviewed_by = COALESCE(@approvedBy, reviewed_by),
        review_notes = COALESCE(@notes, review_notes)
      WHERE id = @recoveryId
    ''', parameters: {
      'status': RecoveryStatus.completed.name,
      'approvedBy': approvedBy,
      'notes': notes,
      'recoveryId': recoveryId,
    });
  }

  Future<bool> _validateSecurityAnswers(String userId, String answers) async {
    // This would validate security question answers
    // For now, return false as security questions aren't implemented
    return false;
  }

  Future<void> _unlockAccount(String userId) async {
    await _databaseService.execute('''
      UPDATE $_accountLocksTable 
      SET is_locked = false, unlocked_at = NOW(), unlocked_by = 'recovery_service'
      WHERE user_id = @userId
    ''', parameters: {'userId': userId});
  }

  Future<void> _resetUserMFA(String userId) async {
    // Reset TOTP
    await _databaseService.execute('''
      UPDATE totp_secrets 
      SET is_verified = false, disabled_at = NOW(), disabled_reason = 'account_recovery'
      WHERE user_id = @userId
    ''', parameters: {'userId': userId});

    // Revoke backup codes
    await _databaseService.execute('''
      UPDATE mfa_backup_codes 
      SET is_active = false, revoked_at = NOW(), revocation_reason = 'account_recovery'
      WHERE user_id = @userId
    ''', parameters: {'userId': userId});

    // Revoke trusted devices
    await _databaseService.execute('''
      UPDATE trusted_devices 
      SET status = 'revoked', revoked_at = NOW(), revocation_reason = 'account_recovery'
      WHERE user_id = @userId
    ''', parameters: {'userId': userId});
  }

  Future<void> _revokeAllMFASessions(String userId, String reason) async {
    await _databaseService.execute('''
      UPDATE mfa_sessions 
      SET is_revoked = true, revoked_at = NOW(), revocation_reason = @reason
      WHERE user_id = @userId AND is_revoked = false
    ''', parameters: {
      'reason': reason,
      'userId': userId,
    });
  }

  Future<void> _sendRecoveryCompletionNotification(MFARecoveryRequest request) async {
    try {
      if (request.method == RecoveryMethod.email) {
        await _deliveryService.sendEmailCode(
          userId: request.userId,
          email: request.recoveryDestination,
        );
      }
    } catch (e) {
      print('Warning: Failed to send recovery completion notification: $e');
    }
  }

  Future<void> _logSecurityEvent({
    required String userId,
    required String eventType,
    required Map<String, dynamic> details,
  }) async {
    try {
      await _databaseService.execute('''
        INSERT INTO security_audit_log (id, user_id, event_type, event_details, created_at)
        VALUES (@id, @userId, @eventType, @details, NOW())
      ''', parameters: {
        'id': _generateRecoveryId(),
        'userId': userId.isEmpty ? null : userId,
        'eventType': eventType,
        'details': jsonEncode(details),
      });
    } catch (e) {
      print('Warning: Failed to log security event: $e');
    }
  }
}

extension MFARecoveryServiceRegistration on DatabaseService {
  MFARecoveryService createMFARecoveryService(MFADeliveryService deliveryService) => 
      MFARecoveryService(this, deliveryService);
}