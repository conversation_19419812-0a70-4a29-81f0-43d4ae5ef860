// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_insights.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalyticsInsights _$AnalyticsInsightsFromJson(Map<String, dynamic> json) =>
    AnalyticsInsights(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      insightType: json['insightType'] as String,
      insightCategory: json['insightCategory'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      insightData: json['insightData'] as Map<String, dynamic>,
      confidenceScore: (json['confidenceScore'] as num?)?.toDouble(),
      impactLevel: $enumDecode(_$ImpactLevelEnumMap, json['impactLevel']),
      actionRecommendations:
          json['actionRecommendations'] as Map<String, dynamic>,
      relatedMetrics: (json['relatedMetrics'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      validFrom: DateTime.parse(json['validFrom'] as String),
      validUntil: json['validUntil'] == null
          ? null
          : DateTime.parse(json['validUntil'] as String),
      isActive: json['isActive'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$AnalyticsInsightsToJson(AnalyticsInsights instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'insightType': instance.insightType,
      'insightCategory': instance.insightCategory,
      'title': instance.title,
      'description': instance.description,
      'insightData': instance.insightData,
      'confidenceScore': instance.confidenceScore,
      'impactLevel': _$ImpactLevelEnumMap[instance.impactLevel]!,
      'actionRecommendations': instance.actionRecommendations,
      'relatedMetrics': instance.relatedMetrics,
      'validFrom': instance.validFrom.toIso8601String(),
      'validUntil': instance.validUntil?.toIso8601String(),
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$ImpactLevelEnumMap = {
  ImpactLevel.low: 'low',
  ImpactLevel.medium: 'medium',
  ImpactLevel.high: 'high',
  ImpactLevel.critical: 'critical',
};

PredictionResults _$PredictionResultsFromJson(Map<String, dynamic> json) =>
    PredictionResults(
      organizationId: json['organizationId'] as String,
      predictionType: json['predictionType'] as String,
      modelName: json['modelName'] as String,
      modelVersion: json['modelVersion'] as String,
      predictions: json['predictions'] as Map<String, dynamic>,
      featureImportance: (json['featureImportance'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(k, (e as num).toDouble())),
      performanceMetrics: (json['performanceMetrics'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(k, (e as num).toDouble())),
      confidenceIntervals:
          (json['confidenceIntervals'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
              k,
              ConfidenceInterval.fromJson(e as Map<String, dynamic>),
            ),
          ),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      validityPeriod: Duration(
        microseconds: (json['validityPeriod'] as num).toInt(),
      ),
    );

Map<String, dynamic> _$PredictionResultsToJson(PredictionResults instance) =>
    <String, dynamic>{
      'organizationId': instance.organizationId,
      'predictionType': instance.predictionType,
      'modelName': instance.modelName,
      'modelVersion': instance.modelVersion,
      'predictions': instance.predictions,
      'featureImportance': instance.featureImportance,
      'performanceMetrics': instance.performanceMetrics,
      'confidenceIntervals': instance.confidenceIntervals,
      'generatedAt': instance.generatedAt.toIso8601String(),
      'validityPeriod': instance.validityPeriod.inMicroseconds,
    };

ConfidenceInterval _$ConfidenceIntervalFromJson(Map<String, dynamic> json) =>
    ConfidenceInterval(
      lowerBound: (json['lowerBound'] as num).toDouble(),
      upperBound: (json['upperBound'] as num).toDouble(),
      confidenceLevel: (json['confidenceLevel'] as num).toDouble(),
    );

Map<String, dynamic> _$ConfidenceIntervalToJson(ConfidenceInterval instance) =>
    <String, dynamic>{
      'lowerBound': instance.lowerBound,
      'upperBound': instance.upperBound,
      'confidenceLevel': instance.confidenceLevel,
    };

AnalyticsIntegration _$AnalyticsIntegrationFromJson(
  Map<String, dynamic> json,
) => AnalyticsIntegration(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  integrationName: json['integrationName'] as String,
  integrationType: $enumDecode(
    _$IntegrationTypeEnumMap,
    json['integrationType'],
  ),
  endpointUrl: json['endpointUrl'] as String?,
  apiKeyHash: json['apiKeyHash'] as String?,
  configuration: json['configuration'] as Map<String, dynamic>,
  authentication: json['authentication'] as Map<String, dynamic>,
  dataMapping: json['dataMapping'] as Map<String, dynamic>,
  syncSchedule: json['syncSchedule'] as String?,
  lastSyncAt: json['lastSyncAt'] == null
      ? null
      : DateTime.parse(json['lastSyncAt'] as String),
  syncStatus: $enumDecode(_$SyncStatusEnumMap, json['syncStatus']),
  errorCount: (json['errorCount'] as num).toInt(),
  lastErrorMessage: json['lastErrorMessage'] as String?,
  isActive: json['isActive'] as bool,
  createdBy: json['createdBy'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$AnalyticsIntegrationToJson(
  AnalyticsIntegration instance,
) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'integrationName': instance.integrationName,
  'integrationType': _$IntegrationTypeEnumMap[instance.integrationType]!,
  'endpointUrl': instance.endpointUrl,
  'apiKeyHash': instance.apiKeyHash,
  'configuration': instance.configuration,
  'authentication': instance.authentication,
  'dataMapping': instance.dataMapping,
  'syncSchedule': instance.syncSchedule,
  'lastSyncAt': instance.lastSyncAt?.toIso8601String(),
  'syncStatus': _$SyncStatusEnumMap[instance.syncStatus]!,
  'errorCount': instance.errorCount,
  'lastErrorMessage': instance.lastErrorMessage,
  'isActive': instance.isActive,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$IntegrationTypeEnumMap = {
  IntegrationType.webhook: 'webhook',
  IntegrationType.api: 'api',
  IntegrationType.biTool: 'bi_tool',
};

const _$SyncStatusEnumMap = {
  SyncStatus.active: 'active',
  SyncStatus.paused: 'paused',
  SyncStatus.failed: 'failed',
};

AnalyticsQuery _$AnalyticsQueryFromJson(Map<String, dynamic> json) =>
    AnalyticsQuery(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      dataSources: (json['dataSources'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      metrics: (json['metrics'] as List<dynamic>)
          .map((e) => QueryMetric.fromJson(e as Map<String, dynamic>))
          .toList(),
      dimensions: (json['dimensions'] as List<dynamic>)
          .map((e) => QueryDimension.fromJson(e as Map<String, dynamic>))
          .toList(),
      filters: (json['filters'] as List<dynamic>)
          .map((e) => QueryFilter.fromJson(e as Map<String, dynamic>))
          .toList(),
      timeRange: QueryTimeRange.fromJson(
        json['timeRange'] as Map<String, dynamic>,
      ),
      sorts: (json['sorts'] as List<dynamic>?)
          ?.map((e) => QuerySort.fromJson(e as Map<String, dynamic>))
          .toList(),
      limit: (json['limit'] as num?)?.toInt(),
      metadata: json['metadata'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$AnalyticsQueryToJson(AnalyticsQuery instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'name': instance.name,
      'dataSources': instance.dataSources,
      'metrics': instance.metrics,
      'dimensions': instance.dimensions,
      'filters': instance.filters,
      'timeRange': instance.timeRange,
      'sorts': instance.sorts,
      'limit': instance.limit,
      'metadata': instance.metadata,
    };

QueryMetric _$QueryMetricFromJson(Map<String, dynamic> json) => QueryMetric(
  fieldName: json['fieldName'] as String,
  aggregation: json['aggregation'] as String,
  alias: json['alias'] as String?,
  filters: (json['filters'] as List<dynamic>?)
      ?.map((e) => QueryFilter.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$QueryMetricToJson(QueryMetric instance) =>
    <String, dynamic>{
      'fieldName': instance.fieldName,
      'aggregation': instance.aggregation,
      'alias': instance.alias,
      'filters': instance.filters,
    };

QueryDimension _$QueryDimensionFromJson(Map<String, dynamic> json) =>
    QueryDimension(
      fieldName: json['fieldName'] as String,
      alias: json['alias'] as String?,
      timeBucket: json['timeBucket'] as String?,
    );

Map<String, dynamic> _$QueryDimensionToJson(QueryDimension instance) =>
    <String, dynamic>{
      'fieldName': instance.fieldName,
      'alias': instance.alias,
      'timeBucket': instance.timeBucket,
    };

QueryFilter _$QueryFilterFromJson(Map<String, dynamic> json) => QueryFilter(
  fieldName: json['fieldName'] as String,
  operator: json['operator'] as String,
  value: json['value'],
  connector: json['connector'] as String?,
);

Map<String, dynamic> _$QueryFilterToJson(QueryFilter instance) =>
    <String, dynamic>{
      'fieldName': instance.fieldName,
      'operator': instance.operator,
      'value': instance.value,
      'connector': instance.connector,
    };

QueryTimeRange _$QueryTimeRangeFromJson(Map<String, dynamic> json) =>
    QueryTimeRange(
      start: DateTime.parse(json['start'] as String),
      end: DateTime.parse(json['end'] as String),
      timeZone: json['timeZone'] as String?,
    );

Map<String, dynamic> _$QueryTimeRangeToJson(QueryTimeRange instance) =>
    <String, dynamic>{
      'start': instance.start.toIso8601String(),
      'end': instance.end.toIso8601String(),
      'timeZone': instance.timeZone,
    };

QuerySort _$QuerySortFromJson(Map<String, dynamic> json) => QuerySort(
  fieldName: json['fieldName'] as String,
  direction: $enumDecode(_$SortDirectionEnumMap, json['direction']),
);

Map<String, dynamic> _$QuerySortToJson(QuerySort instance) => <String, dynamic>{
  'fieldName': instance.fieldName,
  'direction': _$SortDirectionEnumMap[instance.direction]!,
};

const _$SortDirectionEnumMap = {
  SortDirection.ascending: 'asc',
  SortDirection.descending: 'desc',
};
